package com.yiwise.calloutjob.bo;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.cache.*;
import com.yiwise.aicall.engine.engine.brain.entities.DialogFlow;
import com.yiwise.aicall.engine.engine.dialog.DialogFlowHandler;
import com.yiwise.aicall.engine.helper.PeersThreadExecutorHelper;
import com.yiwise.aicall.engine.robot.Robot;
import com.yiwise.aicall.engine.robot.RobotFactory;
import com.yiwise.aicall.engine.service.DialogFlowLoader;
import com.yiwise.aicall.engine.service.WechatTemplateMessageService;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.calloutjob.RobotCallJobTaskScheduler;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.dal.dao.RobotCallTaskPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.feignclient.rcs.RcsApiClient;
import com.yiwise.core.helper.*;
import com.yiwise.core.model.bo.algorithm.PredictRequest;
import com.yiwise.core.model.bo.calldetail.CallDetailReplaceBO;
import com.yiwise.core.model.bo.mq.FilteredTaskMessageBO;
import com.yiwise.core.model.bo.phonenumber.RobotCallPhoneNumberWithSipInfoBO;
import com.yiwise.core.model.bo.robot.FreeRobotBO;
import com.yiwise.core.model.bo.robot.JobRobotChangeBO;
import com.yiwise.core.model.bo.robotcalljob.*;
import com.yiwise.core.model.bo.robotcalljob.timeduration.ActiveTimeBO;
import com.yiwise.core.model.bo.robotcalltask.RunTimeRobotCallTaskBO;
import com.yiwise.core.model.bo.sms.SmsTemplateSendBO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.isv.ISVDataTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.JobRobotChangeTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.RobotCallJobTypeEnum;
import com.yiwise.core.model.enums.train.*;
import com.yiwise.core.service.asr.AsrApplicationService;
import com.yiwise.core.service.asr.AsrDialogFlowRelationService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.SmsTemplateService;
import com.yiwise.core.service.engine.asyncjob.AsyncJobService;
import com.yiwise.core.service.engine.calljob.*;
import com.yiwise.core.service.engine.calljob.impl.RobotCallJobChangeRobotServiceImpl;
import com.yiwise.core.service.engine.callstats.CallStatsService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonService;
import com.yiwise.core.service.engine.isv.IsvCallbackService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.openapi.platform.IsvInfoService;
import com.yiwise.core.service.platform.*;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.scrm.TenantScrmService;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.lcs.api.SmsPlatformChannelApi;
import com.yiwise.lcs.api.dto.SmsPlatformChannelDTO;
import com.yiwise.lcs.api.dto.SmsPlatformChannelRequestDTO;
import com.yiwise.lcs.api.enums.OwnerTypeEnum;
import com.yiwise.rcs.api.dto.RiskControlStrategyDTO;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.control.Try;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.*;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.*;
import static java.lang.Boolean.TRUE;

@Data
public class RobotCallJob {
    private static final Logger logger = LoggerFactory.getLogger(RobotCallJob.class);

    private static final String ASR_CONCURRENCY_REDIS_KEY = RedisKeyCenter.getCountYiwiseAsrConcurrencyKey();
    private static final String ASR_SERVER_CONCURRENCY_REDIS_KEY = RedisKeyCenter.getYiwiseAsrServerConcurrencyCountKey();
    private static UserService userService = AppContextUtils.getBean(UserService.class);
    private static UserSystemService userSystemService = AppContextUtils.getBean(UserSystemService.class);
    private static SmsTemplateService smsTemplateService = AppContextUtils.getBean(SmsTemplateService.class);
    private static RobotCallJobService robotCallJobService = AppContextUtils.getBean(RobotCallJobService.class);
    private static RobotCallTaskService robotCallTaskService = AppContextUtils.getBean(RobotCallTaskService.class);
    private static TenantService tenantService = AppContextUtils.getBean(TenantService.class);
    private static IsvInfoService isvInfoService = AppContextUtils.getBean(IsvInfoService.class);
    private static TenantPhoneNumberService tenantPhoneNumberService = AppContextUtils.getBean(TenantPhoneNumberService.class);
    private static RobotCallJobPhoneNumberService robotCallJobPhoneNumberService = AppContextUtils.getBean(RobotCallJobPhoneNumberService.class);
    private static CallStatsService callStatsService = AppContextUtils.getBean(CallStatsService.class);
    private static RedisOpsService redisOpsService = AppContextUtils.getBean(RedisOpsService.class);
    private static DialogFlowLoader dialogFlowLoader = AppContextUtils.getBean(DialogFlowLoader.class);
    private static SmsService smsService = AppContextUtils.getBean(SmsService.class);
    private static WechatTemplateMessageService wechatTemplateMessageService = AppContextUtils.getBean(WechatTemplateMessageService.class);
    private static IntentMessageService intentMessageService = AppContextUtils.getBean(IntentMessageService.class);
    private static SharePolicyTenantService sharePolicyTenantService = AppContextUtils.getBean(SharePolicyTenantService.class);
    private static CustomerWhiteListService customerWhiteListService = AppContextUtils.getBean(CustomerWhiteListService.class);
    private static SubRobotCallJobService subRobotCallJobService = AppContextUtils.getBean(SubRobotCallJobService.class);
    private static RobotCallTaskPOMapper robotCallTaskPOMapper = AppContextUtils.getBean(RobotCallTaskPOMapper.class);
    private static CustomerPersonService customerPersonService = AppContextUtils.getBean(CustomerPersonService.class);
    private static CallRecordInfoService callRecordInfoService = AppContextUtils.getBean(CallRecordInfoService.class);
    private static PhoneCardService phoneCardService = AppContextUtils.getBean(PhoneCardService.class);
    private static TenantAutoSetService tenantAutoSetService = AppContextUtils.getBean(TenantAutoSetService.class);
    private static RobotService robotService = AppContextUtils.getBean(RobotService.class);
    private static RobotChangeLogService robotChangeLogService = AppContextUtils.getBean(RobotChangeLogService.class);
    private static AsrApplicationService asrApplicationService = AppContextUtils.getBean(AsrApplicationService.class);
    private static AsrDialogFlowRelationService asrDialogFlowRelationService = AppContextUtils.getBean(AsrDialogFlowRelationService.class);
    private static FilteredRobotCallTaskService filteredRobotCallTaskService = AppContextUtils.getBean(FilteredRobotCallTaskService.class);
    private static PhoneNumberService phoneNumberService = AppContextUtils.getBean(PhoneNumberService.class);
    private static TenantCallInterceptService tenantCallInterceptService = AppContextUtils.getBean(TenantCallInterceptService.class);
    private static AsyncJobService asyncJobService = AppContextUtils.getBean(AsyncJobService.class);
    private static IsvCallbackService isvCallbackService = AppContextUtils.getBean(IsvCallbackService.class);
    private static DialogFlowService dialogFlowService = AppContextUtils.getBean(DialogFlowService.class);
    private static TenantScrmService tenantScrmService = AppContextUtils.getBean(TenantScrmService.class);
    private static FilterStrategyService filterStrategyService = AppContextUtils.getBean(FilterStrategyService.class);
    private static TenantEncryptConfigureService tenantEncryptConfigureService = AppContextUtils.getBean(TenantEncryptConfigureService.class);
    private static SmsPlatformChannelApi smsPlatformChannelApi = AppContextUtils.getBean(SmsPlatformChannelApi.class);
    private static CallDetailInfoService callDetailInfoService = AppContextUtils.getBean(CallDetailInfoService.class);
    private static PhoneLocationService phoneLocationService = AppContextUtils.getBean(PhoneLocationService.class);
    private static DistributorService distributorService = AppContextUtils.getBean(DistributorService.class);
    private static SmsChannelInfoService smsChannelInfoService = AppContextUtils.getBean(SmsChannelInfoService.class);
    private static RcsApiClient rcsApiClient = AppContextUtils.getBean(RcsApiClient.class);

    private static RobotFactory robotFactory = AppContextUtils.getBean(RobotFactory.class);
    private final RobotCallJobPO robotCallJobInfo;
    private final ConcurrentLinkedQueue<RunTimeRobotCallTaskBO> robotTaskInfoCache;
    private UserPO jobCreateUserInfo;
    private Long callJobId;
    private SmsTemplateSendBO jobSmsTemplateInfo;
    private SmsTemplateSendBO jobVirtualSmsTemplateInfo;
    private SmsTemplateSendBO flashSmsTemplateInfo;
    private Map<Integer, List<SmsTemplateSendBO>> smsTemplateIntentBOMap;
    private SmsTemplateSendBO wphCouponSmsTemplateInfo;
    private SmsTemplateSendBO wphProductSmsTemplateInfo;
    private List<WechatPushConditionRunTimeBO> wechatPushConditionRunTimeList = new ArrayList<>();
    private List<RobotTaskChannel> channelList = new LinkedList<>();
    private TenantPO tenantInfo;
	@Nullable
	private DistributorPO distributor;
    private List<UserPO> earlyWarningAlertUsers = Collections.emptyList();
    private IsvInfoPO isvInfo;
    // 一线多并发下剩余的可占用的asr计数
    private AtomicInteger remainAsrCount = new AtomicInteger(0);
    // 是否是一线多并发
    private boolean multipleConcurrent = false;

    private Integer robotCount;
    private Integer concurrencyQuota;

    private volatile Long lastConcurrencyQuotaFullCheckTime = -1L;

    private CallPolicyGroupDispatcher callPolicyGroupDispatcher;

    private DialogFlow dialogFlow;
    private String dialogFlowName;

    private Long subRobotCallJobId;

    private Map<Integer, RobotCallPhoneNumberWithSipInfoBO> callerMap;

    /**
     * 分机号主叫信息
     */
    private Map<Integer, RobotCallPhoneNumberWithSipInfoBO> extensionCallerMap;

    private String tag;

    private AtomicBoolean isProcessMQ = new AtomicBoolean(false);

    /**
     * 是否需要加入消费者
     */
    private boolean needConsumer;


    private Set<String> needProperties;

    private LocalDateTime lastUpdateTime;

    /**
     * <p>该tenant是否设置了拦截规则(不限于本任务)</p>
     * <p>如果该tenant设置了拦截规则,即使该任务未启用拦截规则也需要记录外呼数据</p>
     */
    private boolean enableIntercept;
    /**
     * 该任务启用的拦截规则
     */
    private List<TenantCallInterceptPO> intercepts = new ArrayList<>();

    private List<String> deadAreaCodes;

    private List<String> activeAreaCodes;

    private Set<Long> customerWhiteGroupIds;

    private LoadingCache<Long, Boolean> skipOrderBy = CacheBuilder.newBuilder().expireAfterWrite(2, TimeUnit.MINUTES).build(CacheLoader.from(this::loadSkip));

    /**
     * 启动完成标记
     * 未启动完成时不能处理mq消息（弹性坐席）
     */
    private volatile boolean started = false;

    /**
     * 新版计费客户检查余额时外呼需要检查的余额类型
     */
    private Set<TenantAccountEnum> tenantAccountsCallNeedCheck = new HashSet<>(4);
    /**
     * 新版计费客户检查余额时短信需要检查的通道归属者类型
     */
    private Set<OwnerTypeEnum> tenantAccountsSmsNeedCheck = new HashSet<>(4);

    /**
     * 关机时重置task状态
     */
    private AtomicBoolean resetTask = new AtomicBoolean(false);

    private AtomicBoolean tryCache = new AtomicBoolean(false);

    // 数据库无数据时不进行预查询
    private volatile boolean dbEmpty;

    private RiskControlStrategyDTO systemRcs;

    public RobotCallJob(RobotCallJobPO robotCallJobInfo, Long subRobotCallJobId, Map<Integer, RobotCallPhoneNumberWithSipInfoBO> callerMap, Map<Integer, RobotCallPhoneNumberWithSipInfoBO> extensionCallerMap) {
        this.callJobId = robotCallJobInfo.getRobotCallJobId();
        this.robotCount = robotCallJobInfo.getRobotCount();
        this.concurrencyQuota = robotCallJobInfo.getConcurrencyQuota();
        this.robotCallJobInfo = robotCallJobInfo;
        this.robotTaskInfoCache = new ConcurrentLinkedQueue<>();
        this.remainAsrCount.set(robotCount);
        this.subRobotCallJobId = subRobotCallJobId;
        this.callerMap = callerMap;
        this.extensionCallerMap = extensionCallerMap;
        this.smsTemplateIntentBOMap = new HashMap<>();


        if (!Objects.equals(robotCallJobInfo.getConcurrencyQuota(), robotCallJobInfo.getRobotCount())) {
            multipleConcurrent = true;
        }
        initTaskChannelList(callerMap);
        tag = getCallJobId() + "_TAG_" + getSubRobotCallJobId();
        // 分时任务或弹性任务需要消费者监听
        if (Boolean.TRUE.equals(robotCallJobInfo.getEnableElasticRobot()) || Boolean.TRUE.equals(robotCallJobInfo.getEnableTimeRobot())) {
            needConsumer = true;
            logger.info("JobId={}, 需要加入消费者", callJobId);
            RobotCallJobTaskScheduler.addTimeOrElasticJobMap(tag, this);
        }

        // 模型预热
        warmUp();
    }

    private void warmUp() {
        logger.info("模型预热...");
        if (Objects.isNull(dialogFlow)) {
            dialogFlow = dialogFlowLoader.loadFromRemote(robotCallJobInfo.getDialogFlowId());
        }
        DialogFlowInfoPO dialogFlowInfoPO = dialogFlow.getDialogFlowInfoPO();
        // 模型预热
        if (Objects.nonNull(dialogFlowInfoPO) && dialogFlowInfoPO.getEnableAskService() == 1) {
            Try.run(() -> {
                AlgorithmTrainTypeEnum trainType = AlgorithmTrainTypeEnum.INTENT;
                if (!ModelTypeEnum.CUSTOMIZED.name().equals(dialogFlowInfoPO.getModelType()) && Boolean.TRUE.equals(dialogFlowInfoPO.getMixedModel())) {
                    trainType = AlgorithmTrainTypeEnum.MIXED_MODEL;
                }
                PredictRequest predictRequest = PredictRequest.builder()
                        .tenantId(0L)
                        .robotId(dialogFlow.getId())
                        .userInput("模型预热")
                        .trainType(trainType)
                        .snapshotType(SnapshotTypeEnum.PUBLISHED)
                        .isVerbalTraining(false)
                        .modelType(dialogFlowInfoPO.getModelType())
                        .build();
                YiwiseAskHelper.predict(predictRequest);
            }).onFailure(e -> logger.error("模型预热请求报错", e));
        } else {
            logger.info("问法开关未开启，无需模型预热");
        }
    }

    /**
     * 任务停止时，没有下一个task时，可能会重复调用
     */
    public void doWhenJobStopped(boolean jobFinished) {
        rewriteWechatPushConditionIndex();
        // 任务未结束时的停止需要保存策略组状态
        if (!jobFinished) {
            storePolicyStatus();
        }

        resetTaskStatus();
    }

    public void resetTaskStatus() {
        if (isPartitionJob() && CollectionUtils.isNotEmpty(robotTaskInfoCache)) {
            try {
                if (resetTask.compareAndSet(false, true)) {
                    // 子任务停止 但是task没有打完 重置task状态
                    robotTaskInfoCache.forEach(item -> {
                        if (item.getLastStatus() != null) {
                            int success = robotCallTaskService.updateRobotCallTaskNewStatusById(item.getTenantId(), item.getRobotCallJobId(),
                                    item.getRobotCallTaskId(), item.getLastStatus(), RobotCallTaskStatusEnum.IN_CACHE);
                            logger.info("taskId={}, 状态重置success={}", item.getRobotCallTaskId(), success);
                        } else {
                            logger.info("LastStatus is null");
                        }
                    });
                }
            } catch (Exception e) {
                logger.error("reset error", e);
            } finally {
                // 标记重置 cache里重新获取task时可以再次重置
                resetTask.set(false);
            }
        }
    }

    public void closeMQ() {
        if (needConsumer) {
            logger.info("任务停止时关闭mq消费者");
            RobotCallJobTaskScheduler.removeTimeOrElasticJobMap(tag);
        }
    }

    public void startMQ() {
        if (needConsumer) {
            logger.info("重启mq消费者");
            RobotCallJobTaskScheduler.addTimeOrElasticJobMap(tag, this);
        }
    }

    public Boolean loadSkip(Long robotCallJobId) {
        Long totalTask = callStatsService.getTotalTask(tenantInfo.getTenantId(), callJobId);
        return totalTask != null && totalTask > 500_000;
    }

    public void rewriteWechatPushConditionIndex() {
        try {
            List<WechatPushConditionBO> wechatPushConditionList = robotCallJobInfo.getWechatPushConditionList();
            RobotCallJobPO robotCallJob = robotCallJobService.getRobotCallJobById(robotCallJobInfo.getTenantId(), robotCallJobInfo.getRobotCallJobId());
            List<WechatPushConditionBO> wechatPushConditionBOList = robotCallJob.getWechatPushConditionList();
            // 看日志这里集合为空 会导致推送标记没有回填
            logger.info("wechatPushConditionList={}", JsonUtils.object2PrettyString(wechatPushConditionList));
            if (CollectionUtils.isNotEmpty(wechatPushConditionRunTimeList)) {
                if (wechatPushConditionBOList.size() != wechatPushConditionRunTimeList.size()) {
                    logger.info("size 不同, size1={}, size2={}", wechatPushConditionBOList.size(), wechatPushConditionRunTimeList.size());
                    return;
                }
                for (int index = 0; index < wechatPushConditionList.size(); index++) {
                    WechatPushConditionBO condition = wechatPushConditionList.get(index);
                    WechatPushConditionBO conditionBO = wechatPushConditionBOList.get(index);
                    Set<Integer> conditionIntent = condition.getIntentLevelCodes();
                    Set<Long> conditionUser = condition.getUserIds();
                    Long durationThreshold = condition.getDurationThreshold();
                    Set<Integer> conditionBOIntent = conditionBO.getIntentLevelCodes();
                    Set<Long> conditionBOUser = conditionBO.getUserIds();
                    Long oldDurationThreshold = conditionBO.getDurationThreshold();
                    if (!(conditionIntent.containsAll(conditionBOIntent) && conditionBOIntent.containsAll(conditionIntent)
                            && conditionUser.containsAll(conditionBOUser) && conditionBOUser.containsAll(conditionUser)
                            && Objects.equals(durationThreshold, oldDurationThreshold))) {
                        logger.info("推送信息已被修改conditionIntent={}, conditionBOIntent={}, conditionUser={}, conditionBOUser={}", conditionIntent, conditionBOIntent, conditionUser, conditionBOUser);
                        return;
                    }
                }
                wechatPushConditionList.forEach(condition -> wechatPushConditionRunTimeList.forEach(run -> {
                    // 相同意向即为同一个
                    if (condition.getIntentLevelCodes().containsAll(run.getIntentLevelCodes())) {
                        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
                            int count = 0;
                            if (isPartitionJob()) {
                                int index = wechatPushConditionList.indexOf(condition);
                                String jobWechatPushKey = RedisKeyCenter.getJobWechatPushKey(callJobId, index);
                                String countStr = redisOpsService.get(jobWechatPushKey);
                                if (StringUtils.isNotEmpty(countStr)) {
                                    try {
                                        count = Integer.parseInt(countStr);
                                    } catch (Exception e) {
                                        logger.error("获取推送人计数失败, countStr={}", countStr, e);
                                    }
                                }
                            } else {
                                count = (int) run.getWechatUserPoint().get();
                            }
                            int idx = (count % condition.getUserIds().size());
                            condition.setPushIndex(idx);
                        }
                    }
                }));
                RobotCallJobPO robotCallJobPO = new RobotCallJobPO();
                robotCallJobPO.setRobotCallJobId(robotCallJobInfo.getRobotCallJobId());
                robotCallJobPO.setWechatPushConditionList(wechatPushConditionList);
                // 修改推送人的index
                logger.info("修改微信推送index,wechatPushConditionList={}", wechatPushConditionList);
                robotCallJobService.updateNotNull(robotCallJobPO);
            }
        } catch (Exception e) {
            logger.error("更新微信推送信息失败", e);
        }
    }

    /**
     * 保存策略组的计数状态
     */
    public void storePolicyStatus() {
        if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
            callPolicyGroupDispatcher.saveCallingTimesMap();
        }
    }

    private void initTaskChannelList(Map<Integer, RobotCallPhoneNumberWithSipInfoBO> callerMap) {
        if (callerMap.size() > 0 || PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
            initJobInfo();

            // 预先下载音频文件，防止并发获取到多个任务的时候出现网卡打满的情况
            // 缓存话术信息
            dialogFlow = dialogFlowLoader.loadFromRemote(robotCallJobInfo.getDialogFlowId());
            this.dialogFlowName = dialogFlow.getName();
            Long tenantId = robotCallJobInfo.getTenantId();
            DialogFlowHandler.downloadDialogFlowAudio(tenantId, dialogFlow, Collections.emptyMap(), true, new HashMap<>());
        }

        // 对于采用策略组调度和采用其他方式调度，生产channelList方式不一致，一般方式按照可以线路，每个线路一个channel，如果是策略组调度，则根据任务设置的并发数量设置channelList
        if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
            this.callPolicyGroupDispatcher = new CallPolicyGroupDispatcher(this);
            this.channelList.addAll(callPolicyGroupDispatcher.getChannelList());
        } else if (PhoneTypeEnum.MOBILE.equals(robotCallJobInfo.getPhoneType())) {
            callerMap.forEach((callerIndex, callerInfo) -> {
                logger.debug("JobId={}, 获取到机器人账号, 主叫号码={}, 主叫号码序号={}.", callJobId, callerInfo.getPhoneNumber(), callerIndex);
                RobotTaskChannel channel = new RobotTaskChannel(this, callerInfo, null, callerIndex);
                channelList.add(channel);
            });
        } else {
            Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> callPhoneNumberWithSipInfoEntry = CollectionUtils.get(callerMap, 0);
            RobotCallPhoneNumberWithSipInfoBO callPhoneNumberWithSipInfo = callPhoneNumberWithSipInfoEntry.getValue();

            RobotCallPhoneNumberWithSipInfoBO extensionPhoneNumber = null;
            if (MapUtils.isNotEmpty(extensionCallerMap)) {
                Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> extensionEntry = CollectionUtils.get(extensionCallerMap, 0);
                extensionPhoneNumber = extensionEntry.getValue();
            }

            for (int index = 0; index < concurrencyQuota; index++) {
                logger.debug("JobId={}, 获取到机器人账号, 主叫号码={}, 主叫号码序号={}.", callJobId, callPhoneNumberWithSipInfo.getPhoneNumber(), index);
                RobotTaskChannel channel = new RobotTaskChannel(this, callPhoneNumberWithSipInfo, extensionPhoneNumber, index);
                channelList.add(channel);
            }
        }

        logger.debug("新的Job初始化完成, JobId={}, RobotCount={}, 初始化channelListSize={}, 即将执行.", callJobId, robotCount, channelList.size());

        Set<Long> tenantPhoneNumberIdSet = callerMap.values().stream().map(RobotCallPhoneNumberWithSipInfoBO::getTenantPhoneNumberId).collect(Collectors.toSet());
        tenantPhoneNumberService.updateLastHeartBeatTime(tenantPhoneNumberIdSet);
    }

    private void initMoreTaskChannelList(Map<Integer, RobotCallPhoneNumberWithSipInfoBO> callerMap, int startIndex, int initNum) {

        List<RobotTaskChannel> newChannels = new ArrayList<>();
        // 对于采用策略组调度和采用其他方式调度，生产channelList方式不一致，一般方式按照可以线路，每个线路一个channel，如果是策略组调度，则根据任务设置的并发数量设置channelList
        if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
            this.callPolicyGroupDispatcher = new CallPolicyGroupDispatcher(this);
            newChannels = callPolicyGroupDispatcher.getChannelList(startIndex, initNum);
        } else if (PhoneTypeEnum.MOBILE.equals(robotCallJobInfo.getPhoneType())) {
            logger.error("手机号线路不支持增加channel");
            return;
        } else {
            Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> callPhoneNumberWithSipInfoEntry = CollectionUtils.get(callerMap, 0);
            RobotCallPhoneNumberWithSipInfoBO callPhoneNumberWithSipInfo = callPhoneNumberWithSipInfoEntry.getValue();

            RobotCallPhoneNumberWithSipInfoBO extensionPhoneNumber = null;
            if (MapUtils.isNotEmpty(extensionCallerMap)) {
                Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> extensionEntry = CollectionUtils.get(extensionCallerMap, 0);
                extensionPhoneNumber = extensionEntry.getValue();
            }

            for (int index = startIndex; index < startIndex + initNum; index++) {
                logger.debug("JobId={}, 获取到机器人账号, 主叫号码={}, 主叫号码序号={}.", callJobId, callPhoneNumberWithSipInfo.getPhoneNumber(), index);
                RobotTaskChannel channel = new RobotTaskChannel(this, callPhoneNumberWithSipInfo, extensionPhoneNumber, index);
                newChannels.add(channel);
            }
        }

        // 新增channel立即启动
        newChannels.forEach(taskChannel -> {
            Optional<RobotCallJobTask> nextTask = taskChannel.getNextTask();
            if (nextTask.isPresent()) {
                taskChannel.setCurrTask(nextTask.get());
                taskChannel.setChannelFinished(false);
                taskChannel.setWaiting(false);
                taskChannel.executeCurrTask(0);
                RobotCallJobTaskScheduler.addChannel(1);
                logger.debug("新增Channel启动成功， ChannelIndex={}", taskChannel.getChannelIndex());
            } else {
                // 此时未获取task channel关闭
                taskChannel.setChannelFinished(true);
                taskChannel.setWaiting(false);
                taskChannel.waitingForTask();
                logger.debug("新增Channel重新进入等待， ChannelIndex={}", taskChannel.getChannelIndex());
            }
        });

        this.channelList.addAll(newChannels);

        logger.debug("新增channel初始化完整, JobId={}, RobotCount={}, 初始化channelListSize={}, initNum={}, 即将执行.", callJobId, robotCount, channelList.size(), initNum);

        Set<Long> tenantPhoneNumberIdSet = callerMap.values().stream().map(RobotCallPhoneNumberWithSipInfoBO::getTenantPhoneNumberId).collect(Collectors.toSet());
        tenantPhoneNumberService.updateLastHeartBeatTime(tenantPhoneNumberIdSet);
    }

    /**
     * 每5分钟重新加载任务相关数据
     */
    private void initJobInfo() {
        this.lastUpdateTime = LocalDateTime.now();
        // 微信推送
        List<WechatPushConditionBO> wechatPushConditionList = robotCallJobInfo.getWechatPushConditionList();
        Comparator<? super UserPO> userSorter = (o1, o2) -> {
            if (o2 == null || o1.getUserId() == null || o2.getUserId() == null) {
                return 0;
            }
            return Long.compare(o1.getUserId(), o2.getUserId());
        };
        if (CollectionUtils.isNotEmpty(wechatPushConditionList)) {
            if (CollectionUtils.isNotEmpty(this.wechatPushConditionRunTimeList)) {
                // 已经初始化 只更新推送人
                wechatPushConditionRunTimeList.forEach(condition -> {
                    List<UserSystemPO> userSystemPOList = userSystemService.findByUserIds(new ArrayList<>(condition.getUserIds()));
                    userSystemPOList = userSystemPOList.stream().filter(item -> UserStatusEnum.ENABLED.equals(item.getStatus()) && SystemEnum.CRM.equals(item.getSystemType())).collect(Collectors.toList());
                    List<Long> userIdList = userSystemPOList.stream().map(UserSystemPO::getUserId).collect(Collectors.toList());
                    Map<? extends Serializable, UserPO> userMap = userService.selectMapByKeyCollect(userIdList);
                    List<UserPO> userPOS = userMap.values().stream()
                            .filter(item -> StringUtils.isNotEmpty(item.getWxPublicAccountOpenId())
                                    && EnabledStatusEnum.ENABLE.equals(item.getEnabledStatus())
                                    && Objects.equals(robotCallJobInfo.getTenantId(), item.getTenantId()))
                            .sorted(userSorter).collect(Collectors.toList());
                    condition.setUserSet(userPOS);
                    List<Long> userIds = userPOS.stream().map(UserPO::getUserId).collect(Collectors.toList());
                    logger.info("更新微信推送需要的用户 CallJobId={}, intentLevelCodes={}, userIds={}", getCallJobId(), condition.getIntentLevelCodes(), userIds);
                });
            } else {
                List<WechatPushConditionRunTimeBO> tmpRunTimeList = new ArrayList<>();
                wechatPushConditionList.forEach(condition -> {
                    WechatPushConditionRunTimeBO wechatPushConditionRunTimeBO = new WechatPushConditionRunTimeBO();
                    BeanUtils.copyProperties(condition, wechatPushConditionRunTimeBO);
                    // index为空或者大于用户数 重置为0
                    if (condition.getPushIndex() == null) {
                        condition.setPushIndex(0);
                    }
                    wechatPushConditionRunTimeBO.getWechatUserPoint().set(condition.getPushIndex());
                    List<UserSystemPO> userSystemPOList = userSystemService.findByUserIds(new ArrayList<>(condition.getUserIds()));
                    userSystemPOList = userSystemPOList.stream().filter(item -> UserStatusEnum.ENABLED.equals(item.getStatus()) && SystemEnum.CRM.equals(item.getSystemType())).collect(Collectors.toList());
                    List<Long> userIdList = userSystemPOList.stream().map(UserSystemPO::getUserId).collect(Collectors.toList());
                    Map<? extends Serializable, UserPO> userMap = userService.selectMapByKeyCollect(userIdList);
                    List<UserPO> userPOS = userMap.values().stream()
                            .filter(item -> StringUtils.isNotEmpty(item.getWxPublicAccountOpenId())
                                    && EnabledStatusEnum.ENABLE.equals(item.getEnabledStatus())
                                    && Objects.equals(robotCallJobInfo.getTenantId(), item.getTenantId()))
                            .sorted(userSorter).collect(Collectors.toList());
                    wechatPushConditionRunTimeBO.setUserSet(userPOS);
                    tmpRunTimeList.add(wechatPushConditionRunTimeBO);
                    List<Long> userIds = userPOS.stream().map(UserPO::getUserId).collect(Collectors.toList());
                    logger.info("初始化微信推送需要的用户 CallJobId={}, intentLevelCodes={}, userIds={}", getCallJobId(), condition.getIntentLevelCodes(), userIds);
                });
                this.wechatPushConditionRunTimeList = tmpRunTimeList;
            }
        }


        // initialize
        if (tenantInfo == null) {
            tenantInfo = tenantService.selectByKey(robotCallJobInfo.getTenantId());
        }

		if (tenantInfo.getDistributorId() != null && tenantInfo.getDistributorId() > 0) {
			distributor = distributorService.selectByKey(tenantInfo.getDistributorId());
		}

        // 自定义变量
        needProperties = RunTimePropertiesHandler.trimProperties(robotCallJobService.getPropertiesFromRobotCallJob(robotCallJobInfo.getRobotCallJobId(), robotCallJobInfo.getTenantId(), true));

        // 短信模版
        logger.debug("开始查询短信模板,robotCallJobInfo:{}", robotCallJobInfo);
        if (robotCallJobInfo.getSmsTemplateId() != null && robotCallJobInfo.getSmsTemplateId() > 0) {
            if (tenantInfo != null && QIYU_DISTRIBUTOR.contains(tenantInfo.getDistributorId())) {
                jobSmsTemplateInfo = new SmsTemplateSendBO();
                jobSmsTemplateInfo.setSmsTemplateId(robotCallJobInfo.getSmsTemplateId());
            } else {
                jobSmsTemplateInfo = smsTemplateService.getSmsTemplateSendBO(robotCallJobInfo.getTenantId(), robotCallJobInfo.getSmsTemplateId());
            }
        }

        //闪信模板
        if (robotCallJobInfo.getFlashMessageTemplateId() != null && robotCallJobInfo.getFlashMessageTemplateId() > 0) {
            flashSmsTemplateInfo = smsTemplateService.getSmsTemplateSendBO(robotCallJobInfo.getTenantId(), robotCallJobInfo.getFlashMessageTemplateId());
        }

        //虚拟号短信模板
        if (robotCallJobInfo.getVirtualSmsTemplateId() != null && robotCallJobInfo.getVirtualSmsTemplateId() > 0) {
            jobVirtualSmsTemplateInfo = smsTemplateService.getSmsTemplateSendBO(robotCallJobInfo.getTenantId(), robotCallJobInfo.getVirtualSmsTemplateId());
        }

        //唯品会推券短信模板
        if (robotCallJobInfo.getWphCouponSmsTemplateId() != null && robotCallJobInfo.getWphCouponSmsTemplateId() > 0) {
            wphCouponSmsTemplateInfo = smsTemplateService.getSmsTemplateSendBO(robotCallJobInfo.getTenantId(), robotCallJobInfo.getWphCouponSmsTemplateId());
        }

        //唯品会推商品短信模板
        if (robotCallJobInfo.getWphProductSmsTemplateId() != null && robotCallJobInfo.getWphProductSmsTemplateId() > 0) {
            wphProductSmsTemplateInfo = smsTemplateService.getSmsTemplateSendBO(robotCallJobInfo.getTenantId(), robotCallJobInfo.getWphProductSmsTemplateId());
        }

        logger.info("jobSmsTemplateInfo={}", jobSmsTemplateInfo);
        // 挂机短信高级模式
        List<SmsTemplateIntentBO> smsTemplateIntentBOList = robotCallJobInfo.getSmsTemplateIntentList();
        if (CollectionUtils.isNotEmpty(smsTemplateIntentBOList)) {
            smsTemplateIntentBOList.forEach(smsTemplateIntentBO -> {
                Integer key = smsTemplateIntentBO.getIntentLevel();
                List<SmsTemplateSendBO> value = new ArrayList<>();
                if (tenantInfo != null && QIYU_DISTRIBUTOR.contains(tenantInfo.getDistributorId())) {
                    // 七鱼短信模版只有id
                    List<SmsTemplateSendBO> finalValue = value;
                    smsTemplateIntentBO.getSmsTemplateIds().forEach(id -> {
                        SmsTemplateSendBO smsTemplateInfoBO = new SmsTemplateSendBO();
                        smsTemplateInfoBO.setSmsTemplateId(id);
                        finalValue.add(smsTemplateInfoBO);
                    });
                } else if (tenantInfo != null) {
                    value = smsTemplateService.getSmsTemplateSendBOList(tenantInfo.getTenantId(), smsTemplateIntentBO.getSmsTemplateIds());
                }
                smsTemplateIntentBOMap.put(key, value);
            });
        }

        // 任务创建人信息
        jobCreateUserInfo = userService.selectUserFromDB(robotCallJobInfo.getCreateUserId());


        // 查询isv信息
        isvInfo = isvInfoService.findByTenantId(robotCallJobInfo.getTenantId());

        // 找出行业预警信息推送人
        if (CollectionUtils.isNotEmpty(robotCallJobInfo.getEarlyWarningAlertUsers())) {
            List<UserSystemPO> userSystemPOList = userSystemService.findByUserIds(new ArrayList<>(robotCallJobInfo.getEarlyWarningAlertUsers()));
            userSystemPOList = userSystemPOList.stream().filter(item -> UserStatusEnum.ENABLED.equals(item.getStatus()) && SystemEnum.CRM.equals(item.getSystemType())).collect(Collectors.toList());
            List<Long> userIdList = userSystemPOList.stream().map(UserSystemPO::getUserId).collect(Collectors.toList());
            Map<? extends Serializable, UserPO> userMap = userService.selectMapByKeyCollect(userIdList);
            earlyWarningAlertUsers = userMap.values().stream()
                    .filter(item -> item.getTenantId().equals(tenantInfo.getTenantId()) &&
                            EnabledStatusEnum.ENABLE.equals(item.getEnabledStatus())).collect(Collectors.toList());
        } else {
            logger.info("任务 {} 没有配置行业提醒, robotCallJobId={}", robotCallJobInfo.getName(), robotCallJobInfo.getRobotCallJobId());
        }


        this.enableIntercept = tenantCallInterceptService.tenantEnableIntercept(tenantInfo.getTenantId());
        Set<Long> filterStrategyIds = robotCallJobInfo.getFilterStrategyIds();
        // 策略组优先
        if (CollectionUtils.isNotEmpty(filterStrategyIds)) {
            // 系统拦截组
            List<TenantCallInterceptPO> tempIntercept = new ArrayList<>();
            List<TenantCallInterceptPO> systemIntercepts = tenantCallInterceptService.selectSystemInterceptsByTenantId(robotCallJobInfo.getTenantId());
            if (CollectionUtils.isNotEmpty(systemIntercepts)) {
                tempIntercept.addAll(systemIntercepts);
            }

            List<FilterStrategyPO> filterStrategyPOS = filterStrategyService.selectListByKeyCollect(filterStrategyIds);
            Set<Long> allInterceptIds = new HashSet<>();
            Set<Long> allWhiteGroupIds = new HashSet<>();
            filterStrategyPOS.forEach(item -> {
                Set<Long> interceptIds = item.getInterceptIds();
                if (CollectionUtils.isNotEmpty(interceptIds)) {
                    allInterceptIds.addAll(interceptIds);
                }
                Set<Long> whiteListGroupIds = item.getWhiteListGroupIds();
                if (CollectionUtils.isNotEmpty(whiteListGroupIds)) {
                    allWhiteGroupIds.addAll(whiteListGroupIds);
                }
            });
            List<TenantCallInterceptPO> tenantCallInterceptPOS = tenantCallInterceptService.selectTenantCallInterceptListByTenant(allInterceptIds, robotCallJobInfo.getTenantId());

            // 策略组中的拦截组
            if (CollectionUtils.isNotEmpty(tenantCallInterceptPOS)) {
                tempIntercept.addAll(tenantCallInterceptPOS);
            }
            // 更新拦截组和白名单分组
            this.intercepts = tempIntercept;
            this.customerWhiteGroupIds = allWhiteGroupIds;
        } else {
            // 外呼拦截信息
            if (enableIntercept) {
                this.intercepts = tenantCallInterceptService.selectByRobotCallJob(robotCallJobInfo);
            }
            //白名单
            this.customerWhiteGroupIds = robotCallJobInfo.getCustomerWhiteGroupIds();
        }


        // 固话或无主叫
        if (PhoneTypeEnum.LANDLINE.equals(robotCallJobInfo.getPhoneType())) {
            Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> callPhoneNumberWithSipInfoEntry = CollectionUtils.get(callerMap, 0);
            RobotCallPhoneNumberWithSipInfoBO callPhoneNumberWithSipInfo = callPhoneNumberWithSipInfoEntry.getValue();
            List<AreaPO> deadArea = callPhoneNumberWithSipInfo.getDeadArea();
            List<AreaPO> activeArea = callPhoneNumberWithSipInfo.getAcctiveArea();
            if (CollectionUtils.isNotEmpty(deadArea)) {
                deadAreaCodes = new ArrayList<>();
                for (AreaPO areaPO : deadArea) {
                    List<String> strings = phoneNumberService.selectAreaCodesByProvinceAndCity(areaPO.getProv(), Objects.equals(areaPO.getCity(), "全省") ? null : areaPO.getCity());
                    deadAreaCodes.addAll(strings);
                }
            } else if (CollectionUtils.isNotEmpty(activeArea)) {
                activeAreaCodes = new ArrayList<>();
                for (AreaPO areaPO : activeArea) {
                    List<String> strings = phoneNumberService.selectAreaCodesByProvinceAndCity(areaPO.getProv(), Objects.equals(areaPO.getCity(), "全省") ? null : areaPO.getCity());
                    activeAreaCodes.addAll(strings);
                }
            }
        }

        // 计算新版计费客户使用的线路/短信应该扣哪个账户余额
        if (TRUE.equals(tenantInfo.getUsingNewBillingService())) {
            // 线路
            List<RobotCallPhoneNumberWithSipInfoBO> callers = new ArrayList<>(callerMap.values());
            callers.addAll(callerMap.values());
            tenantAccountsCallNeedCheck = callers.stream()
                    .map(caller -> TenantPayTypeEnum.isSubscribe(tenantInfo.getTenantPayType()) && caller.callerOwnedByDistributor(tenantInfo.getDistributorId()))
                    .map(callerOwnedByDistributor -> callerOwnedByDistributor ? TenantAccountEnum.DISTRIBUTOR_ACCOUNT_FARE : TenantAccountEnum.ACCOUNT_FARE)
                    .collect(Collectors.toSet());
            // 短信
            Set<Long> smsTemplateIds = robotCallJobService.getRobotCallJobSmsTemplateIdSet(robotCallJobInfo);
            if (dialogFlow != null && dialogFlow.getSmsTemplateIds() != null) {
                smsTemplateIds.addAll(dialogFlow.getSmsTemplateIds());
            }
            if (tenantInfo != null && !ApplicationConstant.QIYU_DISTRIBUTOR.contains(tenantInfo.getDistributorId())) {
                if (TenantPayTypeEnum.isSubscribe(tenantInfo.getTenantPayType())) {
                    List<SmsTemplatePO> smsTemplatePOS = smsTemplateService.selectListByKeyCollect(smsTemplateIds);
                    Set<Long> smsPlatformChannelIds = smsTemplatePOS.stream().filter(smsTemplatePO -> BooleanUtils.isNotTrue(smsTemplatePO.getNewSmsPlatform())).map(SmsTemplatePO::getSmsPlatformChannelId).collect(Collectors.toSet());
                    Set<Long> newChannelIdList = smsTemplatePOS.stream().filter(smsTemplatePO -> BooleanUtils.isTrue(smsTemplatePO.getNewSmsPlatform())).map(SmsTemplatePO::getSmsPlatformChannelId).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(smsPlatformChannelIds)) {
                        Set<OwnerTypeEnum> collect = smsPlatformChannelApi.getSmsPlatformChannelList(new SmsPlatformChannelRequestDTO(smsPlatformChannelIds)).stream().map(SmsPlatformChannelDTO::getOwnerType).collect(Collectors.toSet());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            tenantAccountsSmsNeedCheck.addAll(collect);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(newChannelIdList)) {
                        Set<OwnerTypeEnum> collect = smsChannelInfoService.getChannelByIdList(newChannelIdList).stream().map(SmsPlatformChannelDTO::getOwnerType).collect(Collectors.toSet());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            tenantAccountsSmsNeedCheck.addAll(collect);
                        }
                    }
                } else {
                    tenantAccountsSmsNeedCheck.add(OwnerTypeEnum.YIWISE);
                }
            }
            logger.debug("tenantAccountsCallNeedCheck={}, tenantAccountsSmsNeedCheck={}", tenantAccountsCallNeedCheck, tenantAccountsSmsNeedCheck);
        }
        // 风控信息
        try {
            systemRcs = rcsApiClient.getSystemRcs(robotCallJobInfo.getTenantId(), com.yiwise.rcs.api.enums.ModelTypeEnum.CALL);
        }catch (Exception e) {
            logger.error("[LogHub_Warn]外呼前初始化任务,获取系统风控信息失败, JobId={}", getCallJobId(), e);
        }
    }

    /**
     * 开启job执行
     */
    public int start() {
        AtomicInteger initChannelCount = new AtomicInteger(0);

        // 此时触发弹性坐席修改channelList会导致报错
        channelList.forEach(channel -> {
            try {
                // 获取一个可执行的Task作为channel的开端
                Optional<RobotCallJobTask> nextTaskOpt = channel.getNextTask();

                if (nextTaskOpt.isPresent()) {
                    channel.setCurrTask(nextTaskOpt.get());
                    channel.executeCurrTask(0);
                    initChannelCount.incrementAndGet();
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn]启动Channel失败, JobId={}, ChannelIndex={}", getCallJobId(), channel.getChannelIndex(), e);
            }
        });

        // 释放没有获取到task的channel资源
        List<RobotTaskChannel> noTaskChannelList = channelList.stream().filter(channel -> channel.getCurrTask() == null).collect(Collectors.toList());
        noTaskChannelList.forEach(channel -> {
            channel.setChannelFinished();
            doWhenChannelFinished(channel, false);
            // 任务完全结束前 等待获取task
            channel.waitingForTask();
        });

        if (initChannelCount.get() == 0 && !robotCallTaskService.isAllJobTaskRunFinished(robotCallJobInfo.getRobotCallJobId())) {
            logger.error("启动Job完成, JobId={}, 真实initChannelCount为{}.", getCallJobId(), initChannelCount.get());
        } else {
            logger.debug("启动Job完成, JobId={}, 真实initChannelCount为{}.", getCallJobId(), initChannelCount.get());
        }
        // 启动完成
        started = true;
        return initChannelCount.get();
    }

    Tuple2<RunTimeRobotCallTaskBO, AccountVO> getRobotCallTaskInfo(RobotTaskChannel robotTaskChannel) {
        Tuple2<RunTimeRobotCallTaskBO, AccountVO> robotTaskCallInfoTuple = Tuple.of(null, null);

        Long callJobId = getCallJobId();
        Integer channelIndex = robotTaskChannel.getChannelIndex();
        boolean filterSuccess = false;
        while (!filterSuccess) {
            boolean success = false;
            RunTimeRobotCallTaskBO robotTaskCallInfo = null;
            int tryTime = 0;
            int phoneNumberCapsTryTime = 0;
            while (!success) {
                logger.info("获取task");
                try {
                    if (WeiPinHuiHelper.isWeipinhuiTenantWithPO(tenantInfo) && !WeiPinHuiNewHelper.belongWphMarketing(tenantInfo.getTenantId())&& ApplicationConstant.WPH_API_QPS > 0) {
                        //拉取任务QPS限制
                        String taskQpsLimit = RedisKeyCenter.getTaskQpsLimit();
                        String hashKey = LocalTime.now().getSecond() + "";

                        Long count = redisOpsService.getRedisTemplate().opsForHash().increment(taskQpsLimit, hashKey, 1);
                        redisOpsService.expire(taskQpsLimit, 2, TimeUnit.MINUTES);
                        if (count.intValue() > ApplicationConstant.WPH_API_QPS) {
                            long currentTimeMillis = System.currentTimeMillis();
                            long timeToSleep = 1000 - (currentTimeMillis % 1000);
                            // 如果timeToSleep为负数，表示当前时间已经接近或超过整秒，因此直接睡眠到下一个整秒
                            if (timeToSleep < 0) {
                                timeToSleep = 1000;
                            }
                            int weight = 500 - ++tryTime * 20;
                            if (weight <= 0) {
                                weight = 10;
                            }
                            timeToSleep = timeToSleep + weight;
                            logger.info("getRobotCallTaskInfo exceed {} {} {} tryTime={} {} weight={}", taskQpsLimit, hashKey, count, tryTime, timeToSleep, weight);
                            MyThreadUtils.sleepMilliseconds(timeToSleep);
                            continue;
                        }
                    }
                } catch (Exception e) {
                    logger.error("getRobotCallTaskInfo qps error", e);
                }
                
                if (PullRobotCallTaskHelper.phoneNumberCapsLimit(robotTaskChannel.getCallCallerInfo(false), ++phoneNumberCapsTryTime)) {
                    continue;
                }
                robotTaskCallInfo = getRobotCallTaskInfoFromCache();
                if (robotTaskCallInfo == null) {
                    // 未获取task时，直接返回
                    logger.debug("未获取到更多Task, JobId={}, ChannelIndex={}.", callJobId, channelIndex);
                    return robotTaskCallInfoTuple;
                }

                int count = robotCallTaskService.updateRobotCallTaskNewStatusWithTimeById(robotTaskCallInfo.getTenantId(), callJobId, robotTaskCallInfo.getRobotCallTaskId(),
                        RobotCallTaskStatusEnum.IN_PROCESS, robotTaskCallInfo.getStatus(), robotTaskCallInfo.getLastCalledTime());
                success = count > 0;
                logger.info("修改 taskId={} 状态success={}", robotTaskCallInfo.getRobotCallTaskId(), success);
            }

            //判断是否是抖音外呼
            robotTaskCallInfo.setTicktokMode(TenantEncryptTypeEnum.DOU_YIN_ENCRYPT.equals(tenantInfo.getEncryptType()));

            Long robotCallTaskId = robotTaskCallInfo.getRobotCallTaskId();
            AccountVO accountVO = null;
            if (robotTaskCallInfo.getCustomerPersonId() > 0) {
                accountVO = customerPersonService.getCustomerPersonById(tenantInfo.getTenantId(), robotTaskCallInfo.getCustomerPersonId());
                if (Objects.isNull(accountVO)) {
                    accountVO = new AccountVO();
                    accountVO.setAccountId(robotTaskCallInfo.getCustomerPersonId());
                }
            }
            robotTaskCallInfo.setRunTimeProperties(RunTimePropertiesHandler.getProperties(accountVO, robotTaskCallInfo.getProperties()));

            useCustomerPersonNameInTask(robotTaskCallInfo);

            robotTaskCallInfoTuple = Tuple.of(robotTaskCallInfo, accountVO);

            boolean alternate = false;
            // 备用号码重拨 在拉起任务时获取次状态任务 需要重新计算被叫
            // 大任务时使用LastStatus
            if (RobotCallTaskStatusEnum.ALTERNATE.equals(robotTaskCallInfo.getLastStatus()) || RobotCallTaskStatusEnum.ALTERNATE.equals(robotTaskCallInfo.getStatus())) {
                if (accountVO != null) {
                    // 计算上次使用线路 检查是否可用 检查未拨
                    List<CallRecordPO> callRecordPOList = callRecordInfoService.getByRobotCallTaskId(robotTaskCallInfo.getTenantId(), robotTaskCallInfo.getRobotCallTaskId());
                    Long lastTenantPhoneNumberId = null;
                    if (CollectionUtils.isNotEmpty(callRecordPOList)) {
                        CallRecordPO callRecordPO = callRecordPOList.get(callRecordPOList.size() - 1);
                        // 上次拨打的线路
                        lastTenantPhoneNumberId = callRecordPO.getTenantPhoneNumberId();
                    } else {
                        List<FilteredRobotCallTaskPO> filteredRobotCallTaskPOS = filteredRobotCallTaskService.selectFilteredTaskByTaskId(robotTaskCallInfo.getTenantId(), robotTaskCallInfo.getRobotCallJobId(), robotTaskCallInfo.getRobotCallTaskId());
                        if (CollectionUtils.isNotEmpty(filteredRobotCallTaskPOS)) {
                            lastTenantPhoneNumberId = filteredRobotCallTaskPOS.get(0).getTenantPhoneNumberId();
                        }
                    }
                    // 检查线路是否可用
                    if (checkTenantPhoneNumberIdAvailable(lastTenantPhoneNumberId)) {
                        String alternatePhoneNumber = checkAlternatePhoneNumber(robotTaskCallInfo, accountVO, lastTenantPhoneNumberId, null);
                        if (StringUtils.isNotEmpty(alternatePhoneNumber)) {
                            // 替换被叫号码
                            logger.info("被叫替换为={}", alternatePhoneNumber);
                            robotTaskCallInfo.setCalledPhoneNumber(alternatePhoneNumber);
                            alternate = true;
                        }
                    }
                }
            }

            // 如果任务是采用外呼策略组调用的话，需要根据被叫的手机号码归属地调度主叫号码
            // 切换被叫的优先级高于外呼策略组 切换号码时不进行策略组的重新获取
            if (!alternate && PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
                Long usingTenantPhoneNumberId = robotTaskChannel.getCallCallerInfo(false).getTenantPhoneNumberId();
                RobotCallPhoneNumberWithSipInfoBO dispatchSipInfo = callPolicyGroupDispatcher.dispatchSipAccountInfoForJobUsePolicyGroup(robotTaskCallInfo, usingTenantPhoneNumberId);
                robotTaskChannel.setCallCallerInfo(dispatchSipInfo);
                filterSuccess = true;
            } else {
                boolean filterAlterTask = doFilterAlterTask(robotTaskCallInfo, robotTaskChannel, accountVO);
                if (filterAlterTask) {
                    // 此处重置task 避免下一个循环返回数据错误
                    robotTaskCallInfoTuple = Tuple.of(null, null);
                    if (!robotTaskChannel.checkAndUpdateJobHeartbeat()) {
                        // 外呼过滤成功 此时任务停止 需要结束当前循环
                        filterSuccess = true;
                        logger.info("过滤时任务心跳停止 直接返回空task 主动进行task状态回退");
                        // 此处回退task状态，因为返回为空时认为已无task任务已完成，不会执行回退
                        resetTaskStatus();
                    } else {
                        filterSuccess = false;
                    }
                } else {
                    filterSuccess = true;
                }
            }
            // 成功获取下一次task
            if (filterSuccess) {
                if (subRobotCallJobId != null && subRobotCallJobId > 0) {
                    logger.debug("[LogHub]获取到可执行Task, TaskId={}, JobId={}, subJobId={}, ChannelIndex={}, 被叫号码={}, cacheSize={}.", robotCallTaskId, callJobId, subRobotCallJobId, channelIndex, robotTaskCallInfo.getCalledPhoneNumber(), robotTaskInfoCache.size());
                } else {
                    logger.debug("[LogHub]获取到可执行Task, TaskId={}, JobId={}, ChannelIndex={}, 被叫号码={}, cacheSize={}.", robotCallTaskId, callJobId, channelIndex, robotTaskCallInfo.getCalledPhoneNumber(), robotTaskInfoCache.size());
                }
                Try.run(() -> {
                    // 萧山sip线路强制切换
                    String phoneNumber = robotTaskChannel.getCallCallerInfo(false).getPhoneNumber();
                    if (phoneNumber != null && phoneNumber.startsWith("萧山SIP自切换专线")) {
                        Map<String, String> variableMap = robotTaskChannel.getCallCallerInfo(false).getVariableSet();
                        String effectiveCallerIdNumber = variableMap.get("effective_caller_id_number");
                        String forceReplace = XiaoShanSipHelper.forceReplace(effectiveCallerIdNumber);
                        variableMap.put("effective_caller_id_number", forceReplace);
                    }
                });
            } else {
                logger.debug("[LogHub]外呼过滤Task, TaskId={}, JobId={}, ChannelIndex={}, 被叫号码={}, cacheSize={}.", robotCallTaskId, callJobId, channelIndex, robotTaskCallInfo.getCalledPhoneNumber(), robotTaskInfoCache.size());
            }
            robotTaskCallInfo.setFlashSmsTemplateInfo(flashSmsTemplateInfo);
        }
        return robotTaskCallInfoTuple;
    }

    private void useCustomerPersonNameInTask(RunTimeRobotCallTaskBO robotTaskCallInfo) {
        Map<String, String> properties = robotTaskCallInfo.getRunTimeProperties();
        if (StringUtils.isNotEmpty(robotTaskCallInfo.getCustomerPersonName())) {
            logger.debug("使用task表的客户名称");
            properties.put(DialogVariablesPlaceholder.CUSTOMER_NAME, robotTaskCallInfo.getCustomerPersonName());
        }
    }

    /**
     * 检查task是否被过滤
     */
    private boolean doFilterAlterTask(RunTimeRobotCallTaskBO robotTaskCallInfo, RobotTaskChannel robotTaskChannel, AccountVO accountVO) {
        Long robotCallTaskId = robotTaskCallInfo.getRobotCallTaskId();
        String preMdcStr = MDC.get("MDC_LOG_ID");
        MDC.put("MDC_LOG_ID", preMdcStr + "_F_TID" + robotCallTaskId);


        FilteredRobotCallTaskPO filteredRobotCallTaskPO = null;
        try {
            filteredRobotCallTaskPO = filteredRobotCallTaskService.rcsFilterTask(robotTaskCallInfo,
                    robotCallJobInfo,
                    accountVO,
                    needProperties,
                    tenantInfo,
                    systemRcs);
            if(Objects.isNull(filteredRobotCallTaskPO)){
                filteredRobotCallTaskPO = filteredRobotCallTaskService.rcsCallingAtSameTime(robotTaskCallInfo,
                        robotCallJobInfo,
                        accountVO,
                        needProperties,
                        tenantInfo
                );
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]外呼过滤报错", e);
        }
        if (filteredRobotCallTaskPO != null) {
            // 修改task状态 增加统计信息
            int count = robotCallTaskService.updateRobotCallTaskNewStatusById(robotTaskCallInfo.getTenantId(), robotTaskCallInfo.getRobotCallJobId(), robotCallTaskId, RobotCallTaskStatusEnum.COMPLETED,
                    RobotCallTaskStatusEnum.IN_PROCESS);
            logger.info("过滤taskId={} count={}", robotCallTaskId, count);
            // task已被运行但是未生成通话记录 设未0 用于和未运行task做区分 callRecordId为0的task只进行过外呼过滤没有被成功外呼过
            if (count == 1 && robotTaskCallInfo.getCallRecordId() == null) {
                robotCallTaskService.updateCallRecordId(robotCallTaskId, 0L);
            }
            if (count == 1) {
                asyncDoTaskAnalyzeMission(isvInfo, robotTaskCallInfo, filteredRobotCallTaskPO);
                MDC.put("MDC_LOG_ID", preMdcStr);
            } else {
                logger.error("task更新失败 删除filteredRobotCallTaskPO");
                filteredRobotCallTaskService.delete(filteredRobotCallTaskPO.getFilteredRobotCallTaskId());
            }
            return true;
        }
        MDC.put("MDC_LOG_ID", preMdcStr);
        return false;
    }

    private boolean checkTenantPhoneNumberIdAvailable(Long tenantPhoneNumberId) {
        if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
            RobotCallPhoneNumberWithSipInfoBO phoneNumber = callPolicyGroupDispatcher.getPhoneNumber(tenantPhoneNumberId);
            return phoneNumber != null;
        } else {
            for (Map.Entry<Integer, RobotCallPhoneNumberWithSipInfoBO> entry : callerMap.entrySet()) {
                RobotCallPhoneNumberWithSipInfoBO value = entry.getValue();
                if (Objects.equals(tenantPhoneNumberId, value.getTenantPhoneNumberId())) {
                    return true;
                }
            }
            return false;
        }
    }

    private void sendJobFinishedWechatInfo() {
        Try.run(() -> {
            if (dialogFlowName != null) {
                wechatTemplateMessageService.sendJobFinishedMsg(getRobotCallJobInfo(), getEarlyWarningAlertUsers(), dialogFlowName);
            } else {
                logger.error("callJob finished send wechatMsg no dialogFlow, callJobId={}", callJobId);
            }
        }).onFailure(e -> logger.error("[LogHub_Warn]执行发送任务结束微信推送消息失败 calljobId={}", getCallJobId(), e));
    }

    public void doWhenCallJobFinished() {
        logger.info("[LogHub]JobId={}任务完成", getCallJobId());
        // 发送微信通知，要在数据统计后做
        sendJobFinishedWechatInfo();
    }

    public boolean checkAndUpdateIsRobotTaskFinished() {
        Long callJobId = getCallJobId();
        RobotCallJobStatusEnum jobStatus = robotCallJobService.getRobotCallJobStatus(callJobId);
        // 任务状态已被修改 直接返回
        if (!RobotCallJobStatusEnum.isRunnable(jobStatus)) {
            return false;
        }
        // 检查并更新Job执行是否完成
        if (isLastTaskRunning()) {

            if (RobotCallJobStatusEnum.isRunnable(jobStatus)) {
                // 分片过的任务，只有当所有子任务都运行完成
                if (isPartitionJob() && robotCallTaskService.haveTaskInRunningOrCache(callJobId)) {
                    return false;
                }

                robotCallJobService.updateRobotCallJobStatusById(callJobId, RobotCallJobStatusEnum.COMPLETED, jobStatus);
                if(ApplicationConstant.SHUYAO_TENANT_ID.equals(getTenantInfo().getTenantId())) {
                    callStatsService.sendAntCalloutStatsMessage(getTenantInfo().getTenantId(), callJobId);
                }
                getRobotCallJobInfo().setStatus(RobotCallJobStatusEnum.COMPLETED);
                return true;
            }
        } else if (isPartitionJob() && robotCallTaskService.haveTaskInRunningOrCache(callJobId)) {
            // 还有task是运行中状态或者缓存中状态
            logger.debug("JobId={}还有task是运行中状态或者缓存中状态", callJobId);
            return false;
        } else if (leftTasksNotAvailable()) {
            // 剩下的 task 都没到可拨打时间(自动重拨的间隔)
            // 更新 job 状态
            LocalDateTime nextRedialTaskTime = robotCallTaskService.getMinNextCalledTimeByJobId(callJobId);
            if (nextRedialTaskTime == null) {
                logger.info("不是等待重播");
                return false;
            }
            Optional<ActiveTimeBO> activeTime = NewRobotCallJobActiveTimeHelper.getNearestActiveTime(robotCallJobInfo, nextRedialTaskTime);
            logger.info("未拨打的task均在等待自动重拨的间隔中, 更新job下次可拨打时间: inactiveTimeList: {}, dailyStartTime:{}, " +
                            "dailyEndTime: {}, daysOfWeek(): {}, endTime: {}, activeTime: {}",
                    robotCallJobInfo.getInactiveTimeList(), robotCallJobInfo.getDailyStartTime(), robotCallJobInfo.getDailyEndTime(),
                    JSON.toJSONString(robotCallJobInfo.getDaysOfWeek()), robotCallJobInfo.getEndTime(), JSON.toJSONString(activeTime));

            if (activeTime.isPresent()) {
                robotCallJobService.updateTimeDurationById(callJobId, activeTime.get());
                robotCallJobService.updateRobotCallJobStatusById(callJobId, RobotCallJobStatusEnum.WAITING_FOR_REDIAL, jobStatus);
            } else {
                robotCallJobService.updateTimeDurationById(callJobId, NewRobotCallJobActiveTimeHelper.DEFAULT_INVALID_DURATION);
                robotCallJobService.updateRobotCallJobStatusById(callJobId, RobotCallJobStatusEnum.EXPIRED, jobStatus);
            }
        } else if (checkCurrServerChannelAllRunningFinished()) {
            List<RobotCallPhoneNumberWithSipInfoBO> callJobCallerInfoList;
            boolean jobUseScrmAddWechat = RobotCallJobAddFriendStatusEnum.SEND.equals(robotCallJobInfo.getAddFriendStatus())
                    && (WechatCpAddFriendEnum.YIWISE_SCRM_AUTO.equals(robotCallJobInfo.getWechatCpAddFriend())
                    || WechatCpAddFriendEnum.YIWISE_SCRM_MANUAL.equals(robotCallJobInfo.getWechatCpAddFriend()));
            //短信模板校验
            String hangUpMsg = smsTemplateCheck();
            // 非进行中状态不做系统挂起检查，等下一次进行时再做检查
            if (RobotCallJobStatusEnum.IN_PROCESS.equals(jobStatus)) {
                if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
                    callJobCallerInfoList = robotCallJobPhoneNumberService.getCallJobCallerInfoListByJobUsePolicyGroup(callJobId);
                } else {
                    callJobCallerInfoList = robotCallJobPhoneNumberService.getCallJobCallerInfoList(callJobId);
                }
                if (CollectionUtils.isEmpty(callJobCallerInfoList)) {
                    // 所有线路都解绑
                    robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.PHONE_UNBIND, jobStatus);
                } else if (tenantService.lineCanNotOverdue(tenantInfo.getTenantId())
                        && robotCallJobService.isAllPhoneNumberDebt(callJobId, false, callJobCallerInfoList)) {
                    // 所有线路都欠费
                    robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.ACCOUNT_DEBT, jobStatus);
                } else if (robotCallJobService.totalAccountDebt(robotCallJobInfo.getTenantId(), tenantAccountsCallNeedCheck)) {
                    // 新版计费客户欠费/按量付费客户欠费
                    robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.TOTAL_ACCOUNT_DEBT, jobStatus);
                } else if (robotCallJobService.isAllPhoneNumberBreakDown(callJobId, callJobCallerInfoList)) {
                    // 所有线路都故障 策略组在此时停止 其他的在ope模块修改
                    if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
                        robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.LINE_BREAKDOWN, jobStatus);
                    }
                } else if (smsDebt()) {
                    robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.SMS_DEBT, jobStatus);
                } else if (jobUseScrmAddWechat) {
                    Optional<Long> scrmIdOptional = tenantScrmService.getScrmIdByTenantId(robotCallJobInfo.getTenantId());
                    // tenant的SCRM关联关系取消后tenant创建的SCRM加微任务挂起
                    if (!scrmIdOptional.isPresent()) {
                        robotCallJobService.updateRobotCallJobStatusToSystemHangUp(callJobId, RobotCallJobHangUpTypeEnum.SCRM_RELATION_INVALID, jobStatus);
                    }
                } else if (StringUtils.isNotBlank(hangUpMsg)) {
                    robotCallJobService.updateRobotCallJobStatusById(callJobId, RobotCallJobStatusEnum.SYSTEM_HANG_UP, hangUpMsg, RobotCallJobHangUpTypeEnum.SMS_TEMPLATE_DISABLE, null, jobStatus);
                }
            }
        }
        return false;
    }

    private synchronized boolean isLastTaskRunning() {
        return checkCurrServerChannelAllRunningFinished() && robotCallTaskService.isAllJobTaskRunFinished(callJobId);
    }

    private synchronized boolean leftTasksNotAvailable() {
        return checkCurrServerChannelAllRunningFinished()
                && robotCallTaskService.allLeftTasksWaiting4Redial(callJobId);
    }

    private boolean checkCurrServerChannelAllRunningFinished() {
        return channelList.stream().allMatch(item -> {
            if (!item.isChannelFinished()) {
                logger.info("channel index={}, 未停止", item.getChannelIndex());
            }
            return item.isChannelFinished();
        });
    }

    /**
     * 在Channel执行结束时调用，有可能是用户点击暂停的结束，也有可能是Channel真正的执行结束了
     *
     * @param channel 当前运行的channel
     */
    public boolean doWhenChannelFinished(RobotTaskChannel channel, boolean started) {
        if (started) {
            RobotCallJobTaskScheduler.releaseChannel();
        }

        Long jobId = getCallJobId();

        logger.debug("[LogHub]JobId={}, ChannelIndex={}, 主叫号码={}, 一个Channel运行停止", jobId, channel.getChannelIndex(), channel.getCallCallerInfo(false).getPhoneNumber());
        channel.setChannelFinished();
        channel.onChannelRunFinished();

        // 检查是否所有的任务都执行完毕了
        return checkAndUpdateIsRobotTaskFinished();
    }

    /**
     * 检查当前Job的状态，获取下一个task 并且查询出Job是否真正执行完成
     *
     * @param channel         当前channel
     * @param nextPhoneNumber 使用当前task拨打下一个被叫
     * @return (是否还有下一个任务 ， Job是否执行完成)
     */
    public Optional<RobotCallJobTask> checkJobStatusAndGetNextTask(RobotTaskChannel channel, String nextPhoneNumber) {
        try {
            Long callJobId = channel.getCallJob().getCallJobId();

            // 检查服务器是否发送了停止服务命令
            if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
                Long robotCallTaskId;
                if (channel.getCurrTask() == null) {
                    robotCallTaskId = -1L;
                } else {
                    robotCallTaskId = channel.getCurrTask().getRobotCallTaskId();
                }
                logger.info("JobId={}, 服务器已发送停止服务请求，不再执行新的Task, TaskId={}", callJobId, robotCallTaskId);
                // 更新任务为可执行状态
                resetRobotCallJobToRunnable(callJobId);
                return Optional.empty();
            }

            // 检查任务类型是否发生变化 普通任务和大任务``
            RobotCallJobTypeEnum robotCallJobTypeEnum = robotCallJobService.selectTypeByJobId(callJobId);
            if (!Objects.equals(robotCallJobTypeEnum, robotCallJobInfo.getRobotCallJobType())) {
                logger.info("任务类型变更 当前channel停止");
                return Optional.empty();
            }
            // 判断子任务version是否变化
            if (isPartitionJob()) {
                SubRobotCallJobPO subRobotCallJobPO = subRobotCallJobService.selectByKey(subRobotCallJobId);
                if (subRobotCallJobPO == null || !Objects.equals(subRobotCallJobPO.getVersionNumber(), robotCallJobInfo.getVersionNumber())) {
                    logger.info("子任务版本号变更 当前进程已过期");
                    return Optional.empty();
                }
            }

            // 线路计费，但是用户欠费了
            RobotCallPhoneNumberWithSipInfoBO callCallerInfo = channel.getCallCallerInfo(false);
            TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectByKey(callCallerInfo.getTenantPhoneNumberId());

            boolean lineCanNotOverdue = TenantPayTypeEnum.lineCanNotOverdue(tenantInfo.getTenantPayType()) && !Boolean.TRUE.equals(tenantInfo.getUsingNewBillingService());
            if (lineCanNotOverdue && callCallerInfo.needFeeCharging() && tenantPhoneNumberService.isAccountDebt(tenantPhoneNumberPO, true)) {
                logger.info("JobId={}, 线路计费，并且线路欠费了，关闭线路, TenantPhoneNumberId={}, TaskId={}", callJobId, callCallerInfo.getTenantPhoneNumberId(), Objects.nonNull(channel.getCurrTask()) ? channel.getCurrTask().getRobotCallTaskId() : "");
                // 如果任务是外呼策略组调度的，则需要更新一条新的线路，继续拨打
                if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
                    // 检查策略组所有的线路是否都欠费
                    callPolicyGroupDispatcher.setTenantPhoneNumberIsOverduce(callCallerInfo.getTenantPhoneNumberId());
                    if (callPolicyGroupDispatcher.checkPolicyGroupAllTenantPhoneNumberAccountDebt()) {
                        return Optional.empty();
                    }

                } else {
                    return Optional.empty();
                }
            }
            if (robotCallJobService.totalAccountDebt(robotCallJobInfo.getTenantId(), tenantAccountsCallNeedCheck)) {
                logger.info(RobotCallJobHangUpTypeEnum.TOTAL_ACCOUNT_DEBT.getDesc());
                return Optional.empty();
            }

            if (smsDebt()) {
                return Optional.empty();
            }

            //短信模板检查
            if (StringUtils.isNotBlank(smsTemplateCheck())) {
                return Optional.empty();
            }

            // 不可用 故障或停用
            if (!phoneCardService.checkPhoneNumberWithGatewayAvailable(callCallerInfo.getPhoneNumberId()) || tenantPhoneNumberService.checkTenantPhoneNumberStopped(tenantPhoneNumberPO)) {
                // 检查策略组是否全部故障
                if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(robotCallJobInfo.getPhoneType())) {
                    callPolicyGroupDispatcher.setForceRefresh(true);
                    if (callPolicyGroupDispatcher.checkPolicyGroupAllTenantPhoneNumberBreakDown()) {
                        return Optional.empty();
                    }
                }
                // 不可用时，只对策略组做特殊处理，其他线路按ope原来的逻辑处理
            }

            boolean jobUseScrmAddWechat = RobotCallJobAddFriendStatusEnum.SEND.equals(robotCallJobInfo.getAddFriendStatus())
                    && (WechatCpAddFriendEnum.YIWISE_SCRM_AUTO.equals(robotCallJobInfo.getWechatCpAddFriend())
                    || WechatCpAddFriendEnum.YIWISE_SCRM_MANUAL.equals(robotCallJobInfo.getWechatCpAddFriend()));
            if (jobUseScrmAddWechat) {
                Optional<Long> scrmIdOptional = tenantScrmService.getScrmIdByTenantId(robotCallJobInfo.getTenantId());
                // tenant的SCRM关联关系取消后tenant创建的SCRM加微任务挂起
                if (!scrmIdOptional.isPresent()) {
                    logger.info(RobotCallJobHangUpTypeEnum.SCRM_RELATION_INVALID.getMsg());
                    return Optional.empty();
                }
            }

            // 检查任务是否还有心跳
            if (channel.checkAndUpdateJobHeartbeat()) {
                Optional<RobotCallJobTask> taskOpt;
                //每五分钟更新一次数据
                Try.run(() -> {
                    LocalDateTime now = LocalDateTime.now();
                    if (Duration.between(lastUpdateTime, now).toMinutes() >= 5) {
                        initJobInfo();
                    }
                }).onFailure(e -> logger.error("更新任务数据失败,ChannelIndex={}", channel.getChannelIndex(), e));

                if (StringUtils.isNotEmpty(nextPhoneNumber) && channel.getCurrTask() != null) {
                    // TODO 外呼过滤检查
                    logger.info("使用当前task拨打号码={}", nextPhoneNumber);
                    RobotCallJobTask currTask = channel.getCurrTask();
                    currTask.getRobotTaskCallInfo().setCalledPhoneNumber(nextPhoneNumber);

                    boolean filterAlterTask = doFilterAlterTask(currTask.getRobotTaskCallInfo(), channel, currTask.getAccountInfoOpt().isPresent() ? currTask.getAccountInfoOpt().get() : null);
                    // 如果当前号码被过滤 直接获取下一个task
                    if (filterAlterTask) {
                        taskOpt = channel.getNextTask();
                        channel.setFilterAlterPhoneNumber(true);
                    } else {
                        logger.info("使用当前task进行副号码重播");
                        taskOpt = Optional.of(currTask);
                    }
                } else {
                    // 获取下一个task运行
                    taskOpt = channel.getNextTask();
                }
                if (taskOpt.isPresent()) {
                    logger.debug("JobId={}, 获取到下一个任务, TaskId={}, channelIndex={}.", callJobId, taskOpt.get().getRobotCallTaskId(), channel.getChannelIndex());
                    // 重置标记位
                    channel.setNoneTask(false);
                    return taskOpt;
                } else {
                    logger.debug("JobId={}, 一个Channel运行完全结束, channelIndex={}.", callJobId, channel.getChannelIndex());
                    // 没有剩余task导致未获取到
                    channel.setNoneTask(true);
                    return Optional.empty();
                }
            } else {
                logger.debug("JobId={}, 心跳已经停止", callJobId);
                return Optional.empty();
            }
        } catch (Exception e) {
            logger.error("获取下个task出错", e);
            return Optional.empty();
        }
    }

    /**
     * 短信欠费
     */
    private boolean smsDebt() {
        return robotCallJobService.smsDebt(robotCallJobInfo.getTenantId(), dialogFlow.getSmsTemplateIds(), robotCallJobInfo, tenantAccountsSmsNeedCheck);
    }

    /**
     * 短信模板校验
     */
    private String smsTemplateCheck() {
        logger.info("smsTemplateCheck jobSmsTemplateInfo={}", jobSmsTemplateInfo);
        if (Objects.nonNull(jobSmsTemplateInfo)) {
            if (!EnabledStatusEnum.ENABLE.equals(jobSmsTemplateInfo.getEnabledStatus())) {
                return "挂机短信模板" + jobSmsTemplateInfo.getSmsTemplateName() + "已删除";
            }
            if (BooleanUtils.isFalse(jobSmsTemplateInfo.getServingStatus())) {
                return "挂机短信模板" + jobSmsTemplateInfo.getSmsTemplateName() + "已停用";
            }
        }
        if (Objects.nonNull(jobVirtualSmsTemplateInfo)) {
            if (!EnabledStatusEnum.ENABLE.equals(jobVirtualSmsTemplateInfo.getEnabledStatus())) {
                return "虚拟号短信模板" + jobVirtualSmsTemplateInfo.getSmsTemplateName() + "已删除";
            }
            if (BooleanUtils.isFalse(jobVirtualSmsTemplateInfo.getServingStatus())) {
                return "虚拟号短信模板" + jobVirtualSmsTemplateInfo.getSmsTemplateName() + "已停用";
            }
        }
        if (Objects.nonNull(flashSmsTemplateInfo)) {
            if (!EnabledStatusEnum.ENABLE.equals(flashSmsTemplateInfo.getEnabledStatus())) {
                return "闪信模板" + flashSmsTemplateInfo.getSmsTemplateName() + "已删除";
            }
            if (BooleanUtils.isFalse(flashSmsTemplateInfo.getServingStatus())) {
                return "闪信模板" + flashSmsTemplateInfo.getSmsTemplateName() + "已停用";
            }
        }
        if (MapUtils.isNotEmpty(smsTemplateIntentBOMap)) {
            for (List<SmsTemplateSendBO> list : smsTemplateIntentBOMap.values()) {
                for (SmsTemplateSendBO smsTemplateInfoBO : list) {
                    if (!EnabledStatusEnum.ENABLE.equals(smsTemplateInfoBO.getEnabledStatus())) {
                        return "挂机短信模板" + smsTemplateInfoBO.getSmsTemplateName() + "已删除";
                    }
                    if (BooleanUtils.isFalse(smsTemplateInfoBO.getServingStatus())) {
                        return "挂机短信模板" + smsTemplateInfoBO.getSmsTemplateName() + "已停用";
                    }
                }
            }
        }
        return null;
    }

    // 最后一个task执行完之后 任务未结束 需要重启channel
    public void restartTaskChannelWhenNoneTask() {
        // 任务还在进行中 存在未执行task 但是所有channel已关闭
        if (!checkCurrServerChannelAllRunningTerminated() && robotCallJobService.isJobStillExecAbleAndUpdateJobLastHeartBeatTime(callJobId, getSubRobotCallJobId()) && hasTaskButAllChannelDone()) {
            logger.info("还有task未执行 重启channel");
            doRestartTaskChannel();
            logger.info("此处并发重启，消费者关闭时需重启");
            startMQ();
            if (isPartitionJob()) {
                boolean success = subRobotCallJobService.reStartSubRobotCallJobByVersion(subRobotCallJobId, robotCallJobInfo.getVersionNumber());
                logger.info("大任务需重启subjob success={}", success);
            }
        }
    }

    private void doRestartTaskChannel() {
        List<RobotTaskChannel> finishedRobotTaskChannelList = getFinishedRobotTaskChannel();

        finishedRobotTaskChannelList.forEach(taskChannel -> {
            Optional<RobotCallJobTask> nextTask = taskChannel.getNextTask();

            if (nextTask.isPresent()) {
                taskChannel.setCurrTask(nextTask.get());
                taskChannel.setChannelFinished(false);
                taskChannel.setWaiting(false);
                taskChannel.executeCurrTask(0);
                RobotCallJobTaskScheduler.addChannel(1);
                logger.debug("重启Channel， ChannelIndex={}", taskChannel.getChannelIndex());
            } else {
                // 此时未获取task channel关闭
                taskChannel.setChannelFinished(true);
                taskChannel.setWaiting(false);
                taskChannel.waitingForTask();
                logger.debug("重新进入等待， ChannelIndex={}", taskChannel.getChannelIndex());
            }
        });
    }

    @Transactional
    public void resetRobotCallJobToRunnable(Long callJobId) {
        RobotCallJobStatusEnum taskStatus = robotCallJobService.getRobotCallJobStatus(callJobId);
        if (RobotCallJobStatusEnum.isRunnable(taskStatus)) {
            RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(callJobId);
            robotCallJobService.setStatusForRunnableJob(robotCallJobPO);
        }
    }

    /**
     * 从缓存中获取一个Task对象
     *
     * @return 一个可执行的Task对象
     */
    private synchronized RunTimeRobotCallTaskBO getRobotCallTaskInfoFromCache() {
        RunTimeRobotCallTaskBO robotTaskCallInfo = null;
        try {
            if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
                logger.info("synchronized get 已关机");
                return null;
            }
            // 先从内存队列中获取，如果获取不到再从数据库中获取，并加入缓存队列中
            robotTaskCallInfo = robotTaskInfoCache.poll();
            if (robotTaskCallInfo == null) {
                synchronized (this) {
                    if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
                        logger.info("synchronized get1 已关机");
                        return null;
                    }
                    // 防止并发重入 重复获取redis锁
                    robotTaskCallInfo = robotTaskInfoCache.poll();
                    if (robotTaskCallInfo == null) {
                        robotTaskCallInfo = doGetRobotCallTaskInfoFromCache();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取task错误", e);
        }
        return robotTaskCallInfo;
    }

    private void checkTaskCache() {
        if (!dbEmpty && (robotTaskInfoCache.size() < robotCount)) {
            if (tryCache.compareAndSet(false, true)) {
                try {
                    logger.info("开始预检查");
                    doGetTaskFromDB();
                } catch (Exception e) {
                    logger.error("task预查询失败", e);
                } finally {
                    tryCache.set(false);
                }
            }
        }
    }

    private void doGetTaskFromDB() {
        // 使用队列缓存大小
        // 在普通任务中，缓存队列大小最大为100
        // 在分片的任务中，缓存队列大小最大为105，减小缓存队列的原因是，减小超时时间计算，避免误超时导致同一个电话打两次的情况发生
        // 系统默认一通电话超时时间是五分钟，这里设置在缓存队列中的超时时间为1小时
        int instanceQueueCacheSize = channelList.size() * 10;
        instanceQueueCacheSize = Math.min(instanceQueueCacheSize, COMMON_JOB_TASK_CACHE_SIZE);
        instanceQueueCacheSize = isPartitionJob() ? PARTITION_CHANNEL_TASK_CACHE_SIZE * channelList.size() : instanceQueueCacheSize;
        if (QiFuHelper.isQiFuTenant(tenantInfo.getTenantId())) {
            //奇富批量过滤优化，每次查询50个 改成50的倍数
            instanceQueueCacheSize = 100;
        }
        List<RunTimeRobotCallTaskBO> robotCallInstanceList = robotCallTaskService.getRunnableRobotCallTaskList(callJobId, subRobotCallJobId, instanceQueueCacheSize, skipOrderBy.getUnchecked(callJobId));
        //外呼前置批量过滤
        filteredRobotCallTaskService.preBatchFilterTask(tenantInfo.getTenantId(), robotCallInstanceList);
        if (isPartitionJob()) {
            Iterator<RunTimeRobotCallTaskBO> iterator = robotCallInstanceList.iterator();
            while (iterator.hasNext()) {
                // 这里耗时最多
                if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
                    logger.info("获取task过程中 已关机");
                    break;
                }
                RunTimeRobotCallTaskBO taskBO = iterator.next();
                int count = robotCallTaskService.updateRobotCallTaskNewStatusById(taskBO.getTenantId(), callJobId, taskBO.getRobotCallTaskId(), RobotCallTaskStatusEnum.IN_CACHE, taskBO.getStatus());
                // IN_CACHE状态过期之后，此时更新不是成功，所以直接返回成功，但是可能会导致多个并发获取此task，但是在task改为进行中时依然有行锁，不会出现多次外呼现象
                if (count > 0 || RobotCallTaskStatusEnum.IN_CACHE.equals(taskBO.getStatus())) {
                    taskBO.setLastStatus(taskBO.getStatus());
                    taskBO.setStatus(RobotCallTaskStatusEnum.IN_CACHE);
                } else {
                    // 移除item
                    logger.info("更新失败，移除taskId={}", taskBO.getRobotCallTaskId());
                    iterator.remove();
                }
            }
        } else {
            // 对普通任务也赋值
            robotCallInstanceList.forEach(item -> item.setLastStatus(item.getStatus()));
        }

        // 外呼计划先导入客户后修改话术会导致job和task的部分配置不一致, 需要在拉取任务时统一, 使用job的配置覆盖task的
        if (robotCallJobInfo.getCallOutPlanId() != null && robotCallJobInfo.getCallOutPlanId() > 0) {
            robotCallInstanceList.forEach(task -> {
                Long originId = task.getDialogFlowId();
                task.setDialogFlowId(robotCallJobInfo.getDialogFlowId());
                task.setIntentLevelTagId(dialogFlow.getIntentLevelTagId());
                if (!Objects.equals(originId, task.getDialogFlowId())) {
                    logger.info("任务属于外呼计划, 更新task的话术信息");
                    RobotCallTaskPO update = new RobotCallTaskPO();
                    update.setRobotCallTaskId(task.getRobotCallTaskId());
                    update.setDialogFlowId(task.getDialogFlowId());
                    update.setIntentLevelTagId(task.getIntentLevelTagId());
                    robotCallTaskService.updateNotNull(update);
                }
            });
        }
        dbEmpty = CollectionUtils.isEmpty(robotCallInstanceList);
        robotTaskInfoCache.addAll(robotCallInstanceList);
    }

    private RunTimeRobotCallTaskBO doGetRobotCallTaskInfoFromCache() {
        RunTimeRobotCallTaskBO robotTaskCallInfo;
        // 防止并发重入
        if (robotTaskInfoCache.size() > 0) {
            if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
                logger.info("已关机");
                resetTaskStatus();
                return null;
            }
            robotTaskCallInfo = robotTaskInfoCache.poll();
            return robotTaskCallInfo;
        }

        doGetTaskFromDB();
        if (RobotCallJobTaskScheduler.getHaveSendShutdownCmd().get()) {
            logger.info("已关机");
            resetTaskStatus();
            return null;
        }
        checkTaskCache();
        return robotTaskInfoCache.poll();
    }

    // 存在未完成task但是所有channel关闭且等待结束
    private boolean hasTaskButAllChannelDone() {
        Integer count = robotCallTaskService.countRunnableRobotCallTaskList(robotCallJobInfo.getRobotCallJobId());
        if (count != null && count > 0) {
            // 轮询需要2秒 等轮询完
            try {
                int waitTime = TASK_WAITING;
                try {
                    waitTime = RobotTaskChannel.getWAIT_TIME(getRobotCallJobInfo().getBaseRobotCount());
                } catch (Exception e) {
                    logger.error("转换失败", e);
                }
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                logger.error("等待2秒失败", e);
            }
            // 所有channel都关闭 且等待结束
            return channelList.stream().filter(item -> item.isChannelFinished() && !item.isWaiting()).count() == concurrencyQuota;
        }
        return false;
    }

    public AtomicInteger getRemainAsrCount() {
        return remainAsrCount;
    }

    public UserPO getJobCreateUserInfo() {
        return jobCreateUserInfo;
    }

    public RobotCallJobPO getRobotCallJobInfo() {
        return robotCallJobInfo;
    }

    public Long getCallJobId() {
        return callJobId;
    }

    public TenantPO getTenantInfo() {
        return tenantInfo;
    }

    public List<UserPO> getEarlyWarningAlertUsers() {
        return earlyWarningAlertUsers;
    }

    public IsvInfoPO getIsvInfo() {
        return isvInfo;
    }

    public Integer getRobotCount() {
        return robotCount;
    }

    public boolean isMultipleConcurrent() {
        return multipleConcurrent;
    }

    public Integer getConcurrencyQuota() {
        return concurrencyQuota;
    }

    public Long getSubRobotCallJobId() {
        return subRobotCallJobId;
    }

    /**
     * 这是一个分片切分后的任务
     */
    public boolean isPartitionJob() {
        return !ApplicationConstant.DEFAULT_CHILD_ROBOT_CALL_JOB_ID.equals(subRobotCallJobId);
    }

    public Robot getRobot() {
        return robotFactory.getRobot(robotCallJobInfo.getDialogFlowId(), RobotSnapshotUsageTargetEnum.CALL_OUT);
    }

    public Boolean isCompressAudio() {
        return robotCallJobInfo.getCompressAudio();
    }

    /**
     * 获取当前完成的Channel，避免多个线程同时调用
     */
    public synchronized List<RobotTaskChannel> getFinishedRobotTaskChannel() {
        return channelList.stream().filter(RobotTaskChannel::isChannelFinished).collect(Collectors.toList());
    }


    /**
     * 所有channel等待或者结束 即所有channel都未获取task 任务停止
     */
    public boolean checkCurrServerChannelAllWaitingOrFinished() {
        // channel结束 等待 或超过10分钟未更新心跳
        return channelList.stream().allMatch(item -> item.isChannelFinished() || item.isWaiting());
    }

    public void terminateChannel(JobRobotChangeBO jobRobotChangeBO) {
        Integer terminateNum = jobRobotChangeBO.getRobotCount();
        // TODO 检查数据是否正确
        logger.info("channelListSize={}, concurrencyQuota={}", channelList.size(), concurrencyQuota);
        if (terminateNum > channelList.size() || terminateNum > concurrencyQuota) {
            logger.error("减少坐席数量大于总数");
            return;
        }
        // 多并发任务减少后取消多并发
        boolean resetConcurrent = false;
        // 普通任务对于多并发取消的处理
        if (!Objects.equals(DEFAULT_CHILD_ROBOT_CALL_JOB_ID, subRobotCallJobId) && !Boolean.TRUE.equals(robotCallJobInfo.getEnableTimeRobot())) {
            if (multipleConcurrent && (concurrencyQuota - terminateNum) < robotCallJobInfo.getShouldUseRobotCount()) {
                multipleConcurrent = false;
                // 把多余的多并发channel也停止
                terminateNum += (robotCallJobInfo.getConcurrencyQuota() - robotCallJobInfo.getRobotCount());
                resetConcurrent = true;
                logger.info("任务从多并发状态变为非多并发状态");
            }
        }
        logger.info("当前channel数量 {} 需减少channel{}", concurrencyQuota, terminateNum);
        concurrencyQuota -= terminateNum;
        if (resetConcurrent) {
            this.robotCount = concurrencyQuota;
        } else {
            this.robotCount -= terminateNum;
        }
        if (this.robotCount <= 0) {
            // 当前任务坐席数全部停止时，所有channel停止
            terminateNum = channelList.size();
        }
        Map<Integer, RobotTaskChannel> integerRobotTaskChannelMap = MyCollectionUtils.listToMap(channelList, RobotTaskChannel::getChannelIndex);
        int max = channelList.size() - 1;
        int min = channelList.size() - terminateNum;
        for (int i = max; i >= min; i--) {
            RobotTaskChannel robotTaskChannel = integerRobotTaskChannelMap.get(i);
            robotTaskChannel.setTerminate(true);
            channelList.remove(robotTaskChannel);
            logger.info("remove channel index = {}", i);
        }
        // channel全部关闭时 关闭消费者
        if (CollectionUtils.isEmpty(channelList)) {
            closeMQ();
        }
        // 更新数据库中的坐席数和并发数
        updateRobotCount(null, jobRobotChangeBO);

    }

    public void initMoreChannel(JobRobotChangeBO jobRobotChangeBO) {
        Integer initNum = jobRobotChangeBO.getRobotCount();
        Integer initConcurrencyQuota = jobRobotChangeBO.getConcurrencyQuota();
        FreeRobotBO userNowFreeRobot = robotService.getUserNowFreeRobot(robotCallJobInfo.getTenantId(), robotCallJobInfo.getCreateUserId());
        // 大任务不进行坐席检查，因为job中已经更新
        // TODO 检查job和subjob的坐席数量是否正确
        if (Objects.equals(DEFAULT_CHILD_ROBOT_CALL_JOB_ID, subRobotCallJobId) && initNum > userNowFreeRobot.getMinFreeRobot()) {
            logger.error("增加并发数超过坐席限制 tenantId={},JobId={},subJobId={},increase={}", robotCallJobInfo.getTenantId(), robotCallJobInfo.getRobotCallJobId(), subRobotCallJobId, initNum);
        } else {
            // subjob不进行多并发计算
            // 多并发任务 增加坐席后满足多并发条件 需要重新计算多并发个数
            // 非分时任务启动多并发
            if (Objects.equals(DEFAULT_CHILD_ROBOT_CALL_JOB_ID, subRobotCallJobId) && !Boolean.TRUE.equals(robotCallJobInfo.getEnableTimeRobot()) && (robotCallJobInfo.getRobotCount() + initNum) >= robotCallJobInfo.getShouldUseRobotCount() && robotCallJobInfo.getBaseConcurrencyQuota() > robotCallJobInfo.getShouldUseRobotCount()) {
                int nowConcurrencyQuota = (int) Math.ceil(robotCallJobInfo.getBaseConcurrencyQuota() * 1.0 / robotCallJobInfo.getShouldUseRobotCount() * (robotCallJobInfo.getRobotCount() + initNum));
                logger.info("JobId={}, 普通任务 触发多并发 重新计算 nowConcurrencyQuota={}", callJobId, nowConcurrencyQuota);
                multipleConcurrent = true;
                robotCount = robotCallJobInfo.getRobotCount() + initNum;
                logger.info("robotCount={}", robotCount);
                // 需要增加的channel
                initNum = nowConcurrencyQuota - this.concurrencyQuota;
                logger.info("initNum={}", initNum);
                this.concurrencyQuota = nowConcurrencyQuota;
            } else {
                // 切片任务
                if (!Objects.equals(DEFAULT_CHILD_ROBOT_CALL_JOB_ID, subRobotCallJobId)) {
                    logger.info("JobId={},subJobId={} 切片任务 计算多并发", callJobId, subRobotCallJobId);
                    // 切片任务没有传并发数 则并发数与坐席数一致 取消多并发
                    if (initConcurrencyQuota == null) {
                        robotCount += initNum;
                        this.concurrencyQuota = robotCount;
                        multipleConcurrent = false;
                        initNum = robotCount - channelList.size();
                    } else {
                        // 切片任务传了并发数则增加相应并发
                        this.concurrencyQuota += initConcurrencyQuota;
                        robotCount += initNum;
                        // 此时需要增加的channel数应是增加的并发数
                        initNum = initConcurrencyQuota;
                        if (!this.concurrencyQuota.equals(robotCount)) {
                            multipleConcurrent = true;
                        }
                    }
                } else {
                    logger.info("JobId={}, 普通任务 不触发多并发", callJobId);
                    this.concurrencyQuota += initNum;
                    robotCount += initNum;
                }
            }
            int currentServerQuota = robotCallJobService.getCurrentServerQuota();
            int remainingQuota = SERVER_JOB_MAX_QUOTA - currentServerQuota;
            if (remainingQuota >= initNum) {
                Integer concurrencyQuota = robotCallJobInfo.getConcurrencyQuota();
                logger.info("channelListSize={}, concurrencyQuota={}", channelList.size(), concurrencyQuota);
                if (initNum > 0) {
                    initMoreTaskChannelList(callerMap, channelList.size(), initNum);
                } else {
                    logger.info("取消多并发后channel数应减少 当前channel数量 {} 需减少channel{}", this.concurrencyQuota, initNum);
                    if (-initNum > this.concurrencyQuota) {
                        logger.info("数据错误");
                        return;
                    }
                    for (int i = channelList.size() - 1; i >= (channelList.size() + initNum); i--) {
                        RobotTaskChannel robotTaskChannel = channelList.get(i);
                        robotTaskChannel.setTerminate(true);
                        channelList.remove(robotTaskChannel);
                    }
                }
                updateRobotCount(null, jobRobotChangeBO);
            } else {
                // 超过当前服务器并发数 状态改为可运行 重新调度
                logger.info("超过当前服务器并发数 reset to runnable robotCount={}, concurrencyQuota={}, callJobId={}", robotCount, this.concurrencyQuota, callJobId);
                updateRobotCount(RobotCallJobStatusEnum.RUNNABLE, jobRobotChangeBO);
            }
        }
    }

    private void updateRobotCount(RobotCallJobStatusEnum status, JobRobotChangeBO jobRobotChangeBO) {
        logger.info("更新缓存中的并发数和坐席数");
        robotCallJobInfo.setRobotCount(robotCount);
        robotCallJobInfo.setConcurrencyQuota(concurrencyQuota);
        // 切片任务变更坐席不加入变更记录
        if (!Objects.equals(DEFAULT_CHILD_ROBOT_CALL_JOB_ID, subRobotCallJobId)) {
            // 切片任务重新调度时需要改为未开始 父任务改为可运行
            if (RobotCallJobStatusEnum.RUNNABLE.equals(status)) {
                RobotCallJobPO robotCallJobPO = new RobotCallJobPO();
                robotCallJobPO.setRobotCallJobId(callJobId);
                robotCallJobPO.setStatus(RobotCallJobStatusEnum.RUNNABLE);
                robotCallJobService.updateNotNull(robotCallJobPO);
                subRobotCallJobService.updateRobotCount(subRobotCallJobId, robotCount, concurrencyQuota, RobotCallJobStatusEnum.NOT_STARTED);
            } else {
                if (robotCount <= 0 || concurrencyQuota <= 0) {
                    logger.info("切片任务坐席全部停止 robotCount={}, concurrencyQuota={}", robotCount, concurrencyQuota);
                    if (JobRobotChangeTypeEnum.DELETE.equals(jobRobotChangeBO.getType())) {
                        logger.info("删除切片任务");
                        subRobotCallJobService.delete(subRobotCallJobId);
                    }
                } else {
                    subRobotCallJobService.updateRobotCount(subRobotCallJobId, robotCount, concurrencyQuota, null);
                }
            }
        } else {
            // 分时变更 要变更数据库和内存
            if (RobotCallJobChangeRobotServiceImpl.TIME_ROBOT.equals(jobRobotChangeBO.getReason())) {
                Integer nowRobotCount = TimeRobotCountHelper.getNowRobotCount(robotCallJobInfo.getTimeRobotCountList(), robotCallJobInfo.getBaseRobotCount());
                // 其他字段已修改 ShouldUseRobotCount 只在分时任务时修改
                robotCallJobInfo.setShouldUseRobotCount(nowRobotCount);
                logger.info("分时任务修改数据库和本地内存 nowRobotCount={}", nowRobotCount);
                robotCallJobService.updateBaseRobotCountAndBaseConcurrencyQuota(nowRobotCount, nowRobotCount, robotCallJobInfo.getRobotCallJobId());
            } else {
                robotCallJobService.updateRobotCount(robotCount, concurrencyQuota, callJobId, null);
            }
            // 坐席变更
            int change = jobRobotChangeBO.getType().equals(JobRobotChangeTypeEnum.INCREASE) ? jobRobotChangeBO.getRobotCount() : -jobRobotChangeBO.getRobotCount();
            robotChangeLogService.addRobotChangeLog(tenantInfo.getTenantId(), callJobId, change, robotCount, jobRobotChangeBO.getReason());
            if (status != null) {
                robotCallJobService.updateRobotCallJobStatusById(callJobId, status, RobotCallJobStatusEnum.IN_PROCESS);
            }
        }
    }

    /**
     * 所有channel都被停止时 任务结束
     */
    private boolean checkCurrServerChannelAllRunningTerminated() {
        return channelList.stream().allMatch(item -> {
            logger.info("index={} terminate={}", item.getChannelIndex(), item.isTerminate());
            return item.isTerminate();
        });
    }


    public void updateRobotCount(JobRobotChangeBO jobRobotChangeBO) {
        while (!this.started) {
            // 会延长mq的消费速度 暂不考虑
            MyThreadUtils.sleepMilliseconds(500);
        }
        // 先比较快照
        RobotCallJobPO robotCallJobPO = jobRobotChangeBO.getRobotCallJobPO();
        RobotCallJobPO robotCallJobInfo = getRobotCallJobInfo();
        SubRobotCallJobPO subRobotCallJobPO = jobRobotChangeBO.getSubRobotCallJobPO();
        logger.info("快照比较 robotCallJobPO={}, robotCallJobInfo={}, subRobotCallJobPO={}", robotCallJobPO, robotCallJobInfo, subRobotCallJobPO);
        if (subRobotCallJobPO == null) {
            if (Objects.equals(robotCallJobInfo.getRobotCount(), robotCallJobPO.getRobotCount()) &&
                    (Objects.equals(robotCallJobInfo.getShouldUseRobotCount(), robotCallJobPO.getShouldUseRobotCount()))
                    &&
                    (Objects.equals(robotCallJobInfo.getVersionNumber(), robotCallJobPO.getVersionNumber()))
            ) {
                // 减少channel
                if (JobRobotChangeTypeEnum.DECREASE.equals(jobRobotChangeBO.getType())) {
                    // 修改robotCount 发送关闭channel信息 修改一线多并发信息
                    terminateChannel(jobRobotChangeBO);
                } else {
                    // 增加channel
                    // 检查是否超过坐席限制 检查是否超过当前机器并发 修改robotCount 新增channel 修改一线多并发信息
                    initMoreChannel(jobRobotChangeBO);
                }
            } else {
                // 任务重复启动 或者上一次结束任务时未关闭消费者
                if (robotCallJobInfo.getVersionNumber() != null && robotCallJobPO.getVersionNumber() != null && robotCallJobInfo.getVersionNumber() < robotCallJobPO.getVersionNumber()) {
                    logger.info("版本号不同 任务已重新启动 关闭当前消费者 thisVersion={}, MQVersion={}", robotCallJobInfo.getVersionNumber(), robotCallJobPO.getVersionNumber());
                    closeMQ();
                    // 停止当前任务
                    jobRobotChangeBO.setRobotCount(robotCallJobInfo.getRobotCount());
                    terminateChannel(jobRobotChangeBO);
                }
                logger.info("job快照不同 消息已过期 丢弃");
                logger.info("robotCount1={}, robotCount2={}, should1={}, should2={}", robotCallJobInfo.getRobotCount(), robotCallJobPO.getRobotCount(), robotCallJobInfo.getShouldUseRobotCount(), robotCallJobPO.getShouldUseRobotCount());
            }
        } else {
            if (Objects.equals(robotCallJobInfo.getRobotCount(), subRobotCallJobPO.getRobotCount())
                    &&
                    (Objects.equals(robotCallJobInfo.getVersionNumber(), subRobotCallJobPO.getVersionNumber()))
            ) {
                // 减少channel
                if (JobRobotChangeTypeEnum.DECREASE.equals(jobRobotChangeBO.getType())) {
                    // 修改robotCount 发送关闭channel信息 修改一线多并发信息
                    terminateChannel(jobRobotChangeBO);
                } else if (JobRobotChangeTypeEnum.INCREASE.equals(jobRobotChangeBO.getType())) {
                    // 增加channel
                    // 检查是否超过坐席限制 检查是否超过当前机器并发 修改robotCount 新增channel 修改一线多并发信息
                    initMoreChannel(jobRobotChangeBO);
                } else if (JobRobotChangeTypeEnum.DELETE.equals(jobRobotChangeBO.getType())) {
                    // TODO 删除当前子任务
                    jobRobotChangeBO.setRobotCount(robotCallJobInfo.getRobotCount());
                    terminateChannel(jobRobotChangeBO);
                }
            } else {
                if (robotCallJobInfo.getVersionNumber() != null && subRobotCallJobPO.getVersionNumber() != null && robotCallJobInfo.getVersionNumber() < subRobotCallJobPO.getVersionNumber()) {
                    logger.info("版本号不同 任务已重新启动 关闭当前消费者 thisVersion={}, MQVersion={}", robotCallJobInfo.getVersionNumber(), subRobotCallJobPO.getVersionNumber());
                    closeMQ();
                    // 停止当前任务
                    jobRobotChangeBO.setRobotCount(robotCallJobInfo.getRobotCount());
                    terminateChannel(jobRobotChangeBO);
                }
                logger.info("job快照不同 消息已过期 丢弃");
            }
        }
    }

    public void setSubJobFinished() {
        if (isPartitionJob()) {
            logger.info("切片任务结束");
            // todo VersionNumber 不对 导致更新失败
            boolean success = subRobotCallJobService.updateSubRobotCallJobToNotStartByVersion(subRobotCallJobId, robotCallJobInfo.getVersionNumber());
            logger.info("更新结果 success={}", success);
        }
    }

    /**
     * 当前job是否可以处理mq 不可多线程同时处理
     */
    public boolean canProcessMQ() {
        return isProcessMQ.compareAndSet(false, true);
    }

    /**
     * 重置标记位
     */
    public void finishMQ() {
        isProcessMQ.set(false);
        logger.info("结束MQ 重置标记位");
    }

    protected void asyncDoTaskAnalyzeMission(IsvInfoPO isvInfo, RobotCallTaskPO robotCallTaskPO, FilteredRobotCallTaskPO filteredRobotCallTaskPO) {
        PeersThreadExecutorHelper.execute("TaskId=" + robotCallTaskPO.getRobotCallTaskId() + ": Task被外呼过滤，执行相关异步任务",
                () -> Try.run(() -> doAsyncAfterFilterTask(isvInfo, robotCallTaskPO, filteredRobotCallTaskPO))
                        .onFailure(e -> logger.error("[LogHub_Warn]执行Task外呼过滤之后的异步处理逻辑出错，TaskId=" + robotCallTaskPO.getRobotCallTaskId(), e)));
    }

    public void doAsyncAfterFilterTask(IsvInfoPO isvInfo, RobotCallTaskPO robotCallTaskPO, FilteredRobotCallTaskPO filteredRobotCallTaskPO) {
        FilteredTaskMessageBO filteredTaskMessageBO = new FilteredTaskMessageBO();
        filteredTaskMessageBO.setIntentLevelTagId(robotCallTaskPO.getIntentLevelTagId());
        filteredTaskMessageBO.setFilteredRobotCallTaskPO(filteredRobotCallTaskPO);
        filteredTaskMessageBO.setCurrDateTime(LocalDateTime.now());
        if (MapUtils.isNotEmpty(robotCallTaskPO.getProperties())) {
            filteredTaskMessageBO.setCrowdCustomerId(robotCallTaskPO.getProperties().get("childCrowdId"));
        }
        if (filteredRobotCallTaskPO.getFilterType() == FilterTypeEnum.BLACK_LIST || filteredRobotCallTaskPO.getFilterType() == FilterTypeEnum.BLACK_LIST_SMS) {
            filteredRobotCallTaskPO.setFilterReason(null);
        }
        asyncJobService.sendFilteredTaskMessageToQueue(filteredTaskMessageBO);

        //滴滴回调
        if (DIDI_TENANT_IDS.contains(tenantInfo.getTenantId())) {
            List<CallRecordPO> callRecordPOList = callRecordInfoService.getByRobotCallTaskId(tenantInfo.getTenantId(), robotCallTaskPO.getRobotCallTaskId());
            CallRecordPO callBackRecord = null;
            if (CollectionUtils.isNotEmpty(callRecordPOList)) {
                for (CallRecordPO callRecordPO : callRecordPOList) {
                    if (hasCallBack(callRecordPO, robotCallTaskPO.getProperties())) {
                        callBackRecord = callRecordPO;
                        break;
                    }
                }
            }
            if (isvInfo != null && callBackRecord != null) {
                CallRecordPO finalCallBackRecord = callBackRecord;
                IsvInfoPO finalIsvInfo = isvInfo;
                Try.run(() -> {
                    List<CallDetailPO> callDetailList = new ArrayList<>();
                    List<CallDetailReplaceBO> callDetailReplaceList = callDetailInfoService.selectByCallRecordId(tenantInfo.getTenantId(), finalCallBackRecord.getCallRecordId());
                    if (CollectionUtils.isNotEmpty(callDetailReplaceList)) {
                        callDetailList = callDetailReplaceList.stream().map(CallDetailPO.class::cast).collect(Collectors.toList());
                    }
                    PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKey(finalCallBackRecord.getPhoneNumberId());
                    isvCallbackService.callBackCallRecord(finalIsvInfo, finalCallBackRecord, callDetailList, finalCallBackRecord.getDynamicVariables(), null, robotCallTaskPO.getRedialTimes(), null, robotCallJobInfo, null, dialogFlowName, null, null, null, Objects.nonNull(phoneNumberPO) ? phoneNumberPO.getPhoneNumber() : null, tenantInfo, null, null, null);
                }).onFailure(e -> logger.error("[LogHub_Warn]执行Job的Callback失败", e));
                return;
            }
        }

        // 回调
        // 七鱼 手动拼接isv
        if (ApplicationConstant.QIYU_DISTRIBUTOR.contains(tenantInfo.getDistributorId())) {
            isvInfo = new IsvInfoPO();
            isvInfo.setTenantId(tenantInfo.getTenantId());
            isvInfo.setDataTypes(Collections.singleton(ISVDataTypeEnum.FILER_TASK));
        }
        isvCallbackService.callbackFilteredTask(isvInfo, filteredRobotCallTaskPO, jobCreateUserInfo);
    }

    private boolean hasCallBack(CallRecordPO oldRecord, Map<String, String> properties) {
        return oldRecord.getProperties() != null && oldRecord.getProperties().containsKey("requestId")
                && properties != null && properties.containsKey("requestId")
                && properties.get("requestId").equals(oldRecord.getProperties().get("requestId"));
    }

    public String checkAlternatePhoneNumber(RobotCallTaskPO taskPO, AccountVO accountVO, Long tenantPhoneNumberId, String usedPhoneNumber) {
        logger.info("开始检查备用号码重拨");
        // 未接通且客户有多个号码
        // 除去asr已接听
        Set<String> untriedPhoneNumber = robotCallTaskService.getUntriedPhoneNumber(accountVO, taskPO.getTenantId(), taskPO.getRobotCallTaskId(), tenantPhoneNumberId);
        // 当前通话记录是异步生成的 需要除去当前被叫
        if (CollectionUtils.isNotEmpty(untriedPhoneNumber)) {
            if (StringUtils.isNotEmpty(usedPhoneNumber)) {
                untriedPhoneNumber.remove(usedPhoneNumber);
            }
        }
        // 还有剩余号码未被当前线路拨打
        if (CollectionUtils.isNotEmpty(untriedPhoneNumber)) {
            String next = untriedPhoneNumber.iterator().next();
            logger.info("需要重拨号码={}, untriedPhoneNumber={}", next, untriedPhoneNumber);
            return next;
        } else {
            logger.info("不需要换号码重拨");
            return null;
        }
    }
}
