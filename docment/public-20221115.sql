alter table call_out_plan_job
    add sms_template_import_name varchar(50) null comment '短信模板名';

alter table call_out_plan_job
    add sms_push_type tinyint default 0 null comment '推送模式-逐通/去重';


alter table call_out_plan_job
    add sms_alert_level varchar(100) default '[]' null comment '推送条件';

create table customer_navigation_setting
(
    customer_navigation_setting_id bigint auto_increment comment 'id
',
    user_id                        bigint  null comment '用户id',
    tenant_id                      bigint  null comment '租户id',
    sms_platform                   tinyint null comment '是否完成短信平台引导',
    call_out                       tinyint null comment '呼叫任务',
    call_stats                     tinyint null comment '通话统计',
    filter_customer                tinyint null comment '过滤客户',
    filter_strategy                tinyint null comment '过滤策略',
    black_list                     tinyint null comment '黑名单管理',
    white_list                     tinyint null comment '白名单管理',
    call_intercept                 int     null comment '外呼拦截',
    constraint customer_navigation_setting_pk
        primary key (customer_navigation_setting_id)
);
create index index_tenant_id_user_id
    on customer_navigation_setting (tenant_id, user_id);

create index index_user_id
    on customer_navigation_setting (user_id);
