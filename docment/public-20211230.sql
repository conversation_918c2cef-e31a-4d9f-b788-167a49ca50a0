alter table sms_template
    add serving_status boolean default 1 null comment '0:禁用，1:启用';
alter table privacy_number add transfer_number varchar(256) null comment '转接号码' after privacy_number_operator_id;

alter table tenant
    add column free_dialog_flow_count                 int default 0 comment '赠送话术套数',
    add column used_free_dialog_flow_count            int default 0 comment '赠送话术套数-已使用',
    add column dialog_flow_cost_without_record        int default 1000 comment '话术制作成本（不含录音）',
    add column dialog_flow_cost_with_record           int default 1500 comment '话术制作成本（含录音）',
    add column dialog_flow_cost_within_ten_sentence   int default 100 comment '话术制作成本（10句以内）',
    add column dialog_flow_cost_within_fifty_sentence int default 800 comment '话术制作成本（50句以内）',
    add column free_dialog_flow_sentence_count        int default 0 comment '赠送话术修改句',
    add column used_free_dialog_flow_sentence_count   int default 0 comment '赠送话术修改句-已使用',
    add column sentence_cost                          int default 10 comment '单句修改成本';

CREATE TABLE `dialog_flow_statement`
(
    `dialog_flow_statement_id`    bigint              NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id`                   bigint              NOT NULL COMMENT '租户ID',
    `dialog_flow_id`              bigint              NOT NULL COMMENT '话术ID',
    `number`                      int                          DEFAULT NULL COMMENT '数量',
    `unit_cost`                   int                          DEFAULT NULL COMMENT '成本单价',
    `transaction_unit_cost`       int                          DEFAULT NULL COMMENT '实际单价',
    `total_amount`                int                          DEFAULT NULL COMMENT '总金额',
    `dialog_flow_billing_type`    tinyint                      DEFAULT NULL COMMENT '扣费类型 1-完整话术 2-修改文案+录音 3-修改文案 4-修改录音',
    `dialog_flow_billing_account` tinyint                      DEFAULT NULL COMMENT '扣费路径 1-赠送话术 2-账户余额 3-不扣费',
    `create_time`                 datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                 datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_user_id`              bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `update_user_id`              bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新用户ID',
    PRIMARY KEY (`dialog_flow_statement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='话术消费流水';

alter table dialog_flow_tenant_relation_op
    add column `unit_cost`                   int     DEFAULT NULL COMMENT '成本单价',
    add column `transaction_unit_cost`       int     DEFAULT NULL COMMENT '实际单价',
    add column `total_amount`                int     DEFAULT NULL COMMENT '总金额',
    add column `dialog_flow_billing_account` tinyint DEFAULT NULL COMMENT '扣费路径 1-赠送话术 2-话术余额 3-不扣费',
    add column discount                      tinyint default 0 comment '是否打折',
    add column discount_price                 int null comment '折后单价';

alter table data_report add column step_count int NULL COMMENT '话术统计流程数';