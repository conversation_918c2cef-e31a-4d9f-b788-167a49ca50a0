# 计费改造1.0.1
insert into table_info (table_url, platform_type) VALUE ('/apiBoss/rechargeStream/exportOfflineRechargeList', 1);
insert into header_info (header_name, table_id, field_name, order_by) values
('时间', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'createTime', 1),
('账户', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'account', 2),
('类型', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'rechargeMethod', 3),
('金额（元）', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'cost', 4),
('操作人', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'createUserName', 5),
('备注', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportOfflineRechargeList'), 'remark', 6);

insert into table_info (table_url, platform_type) VALUE ('/apiBoss/rechargeStream/exportAccountRecharge', 1);
insert into header_info (header_name, table_id, field_name, order_by) values
('时间', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'createTime', 1),
('账户类型', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'tenantAccount', 2),
('客户名', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'companyName', 3),
('联系人', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'linkman', 4),
('联系电话', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'phone', 5),
('金额（元）', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'fare', 6),
('操作人', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'createUserName', 7),
('备注', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/rechargeStream/exportAccountRecharge'), 'remark', 8);

insert into table_info (table_url, platform_type) VALUE ('/apiBoss/stock/exportAllAccountRecharge', 1);
insert into header_info (header_name, table_id, field_name, order_by) values
('时间', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'createTime', 1),
('客户名', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'companyName', 2),
('金额（元）', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'fare', 3),
('余额（元）', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'accountFare', 4),
('操作人', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'createUserName', 5),
('备注', (SELECT table_id FROM table_info WHERE table_url = '/apiBoss/stock/exportAllAccountRecharge'), 'remark', 6);

# 隐私号
alter table privacy_number add area_code varchar(64) null comment '区号' after binding_count;