create table ai_call_engine.spring_batch_job_type
(
    spring_batch_job_type_id bigint auto_increment
        primary key,
    description              varchar(128) null,
    file_type                tinyint(2)   not null comment '0 输出错误文件 1 输出导出文件',
    job_count_step_name      varchar(64)  not null,
    name                     varchar(64)  not null,
    constraint spring_batch_job_type_name_uindex
        unique (name)
)
    comment '导入导出';

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (1, '未知类型', 0, 'unknownStep', 'UNKNOWN');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (2, '我的客户导入客户', 0, 'customerPersonImportStep', 'IMPORT_PRIVATE_CUSTOMERS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (3, '我的客户导出客户', 1, 'customerPersonExportStep', 'EXPORT_PRIVATE_CUSTOMERS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (4, '导入客户到任务', 0, 'customerPersonImportStep', 'IMPORT_CUSTOMERS_TO_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (5, '导出联系历史', 1, 'callRecordExportStep', 'EXPORT_CALL_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (6, '已呼客户列表导出通话记录', 1, 'callRecordExportStep', 'EXPORT_CALL_RECORDS_FROM_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (7, '导出话费详单', 1, 'callCostDetailExportStep', 'EXPORT_CALL_COST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (8, '导出通话统计信息', 1, 'callStatusInfoExportStep', 'EXPORT_CALL_STATUS_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (9, '导出通话费用信息（按日期汇总）', 1, 'callCostInfoExportStep', 'EXPORT_CALL_COST_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (10, '导出短信费用信息（按日期汇总）', 1, 'messageCostInfoExportStep', 'EXPORT_MESSAGE_COST_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (11, '导出通话费用信息（按月汇总）', 1, 'callCostMonthInfoExportStep', 'EXPORT_CALL_COST_MONTH_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (12, '导出通话统计信息(按任务)', 1, 'callStatusInfoByTaskExportStep', 'EXPORT_CALL_STATUS_INFO_BY_TASK');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (13, '导出短信费用信息（按月汇总）', 1, 'messageCostMonthInfoExportStep', 'EXPORT_MESSAGE_COST_MONTH_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (14, '已呼客户列表重新添加客户到任务', 0, 'customerPersonReAddImportStep', 'IMPORT_CUSTOMERS_RE_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (15, '我的客户导入到任务', 0, 'customerPersonAddImportStep', 'IMPORT_CUSTOMERS_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (16, '导出租户线路消费流水', 1, 'costListTenantLineExportStep', 'EXPORT_TENANT_COST_LIST_LINE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (17, '导出分销商客户', 1, 'distributorCustomerExportStep', 'EXPORT_DISTRIBUTOR_CUSTOMER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (18, '导出直销客户', 1, 'directCustomerExportStep', 'EXPORT_DIRECT_CUSTOMER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (19, '客户公海导入客户', 0, 'customerPersonImportStep', 'IMPORT_PUBLIC_CUSTOMERS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (20, '客户公海导出客户', 1, 'customerPersonExportStep', 'EXPORT_PUBLIC_CUSTOMERS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (21, '导入未呼客户列表到其他任务', 0, 'toBeCalledTaskImportStep', 'IMPORT_FROM_TASK_TO_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (22, '导出租户短信消费流水', 1, 'costListTenantMessageExportStep', 'EXPORT_TENANT_COST_LIST_MESSAGE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (23, '导出分销商', 1, 'distributorExportStep', 'EXPORT_DISTRIBUTOR');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (24, '导出客户统计分析', 1, 'tenantCallStatsExportStep', 'EXPORT_TENANT_CALL_STATUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (25, '导出客户统计分析详情', 1, 'detailCallStatsExportStep', 'EXPORT_DETAIL_CALL_STATUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (26, '导出充值记录', 1, 'allRechargeRecordExportStep', 'EXPORT_ALL_RECHARGE_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (27, '已呼客户列表重新添加客户到任务', 0, 'lastCallRecordReAddImportStep', 'IMPORT_LAST_RECORD_RE_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (28, '导出联系历史', 1, 'lastCallRecordExportStep', 'EXPORT_LAST_CALL_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (29, '导出呼叫任务联系历史通话记录', 1, 'callRecordExportFromJobStep', 'EXPORT_CALL_RECORDS_FROM_ROBOT_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (30, '导出话术训练联系历史通话记录', 1, 'callRecordExportFromTrainingStep', 'EXPORT_CALL_RECORDS_FROM_TRAINING');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (31, '导出快速拨打联系历史通话记录', 1, 'callRecordExportFromDirectCallStep', 'EXPORT_CALL_RECORDS_FROM_DIRECT_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (32, '导出充值记录', 1, 'rechargeStreamExportStep', 'EXPORT_RECHARGE_DISTRIBUTE_STREAM');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (33, '导出子账号线路计费', 1, 'subAccountCallCostExportStep', 'EXPORT_SUB_ACCOUNT_CALL_COST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (34, '导出子账号短信计费', 1, 'subAccountMessageCostExportStep', 'EXPORT_SUB_ACCOUNT_SMS_COST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (35, '导出子账号话单明细', 1, 'subAccountCallRecordExportStep', 'EXPORT_SUB_ACCOUNT_CALL_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (36, '呼叫任务联系历史重新添加客户到任务', 0, 'customerPersonReAddCallRecordFromJobImportStep', 'IMPORT_CALL_RECORD_FROM_JOB_RE_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (37, '导出未呼客户列表', 1, 'toBeCalledTaskExportStep', 'EXPORT_TO_BE_CALLED_TASK');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (38, '导出子账号人工外呼话单明细', 1, 'subAccountCsCallRecordExportStep', 'EXPORT_SUB_ACCOUNT_CS_CALL_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (39, '导出计费统计客户线路', 1, 'financeStatsExportStep', 'EXPORT_FINANCE_TENANT_LINE_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (40, '导出计费统计客户短信', 1, 'financeStatsExportStep', 'EXPORT_FINANCE_TENANT_SMS_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (41, '导出计费统计线路计费', 1, 'financeStatsExportStep', 'EXPORT_FINANCE_LINE_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (42, '导出计费统计线路测试', 1, 'financeStatsExportStep', 'EXPORT_FINANCE_LINE_TEST_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (43, '导入手机号码到短信任务', 0, 'smsJobImportStep', 'IMPORT_PHONE_NUMBER_TO_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (44, '导出短信统计', 1, 'smsStatsExportStep', 'EXPORT_SMS_STATS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (45, 'excel导入客户', 0, 'customerPersonImportStep', 'IMPORT_FROM_EXCEL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (46, 'excel导入客户', 0, 'customerPersonImportStep', 'QIYU_IMPORT_FROM_EXCEL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (47, '导出呼入通话费用信息（按日汇总）', 1, 'callInCostInfoExportStep', 'EXPORT_CALL_IN_COST_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (48, '导出呼入通话费用信息（按月汇总）', 1, 'callInCostMonthInfoExportStep', 'EXPORT_CALL_IN_COST_MONTH_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (49, '导入客户到人工外呼批量任务', 0, 'csBatchCustomerPersonImportStep', 'CS_BATCH_IMPORT_EXCEL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (50, '导出人工外呼通话费用信息（按天汇总）', 1, 'csCallCostInfoExportStep', 'EXPORT_CS_CALL_COST_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (51, '导出人工外呼通话费用信息（按月汇总）', 1, 'csCallCostMonthInfoExportStep', 'EXPORT_CS_CALL_COST_MONTH_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (52, '导出意向短信发送历史', 1, 'exportSmsIntentRecordStep', 'EXPORT_SMS_INTENT_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (53, '导出群发短信发送历史', 1, 'exportSmsJobMessageRecordStep', 'EXPORT_SMS_JOB_MESSAGE_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (54, '导出质检消费记录', 1, 'qcCostExportStep', 'EXPORT_QC_COST_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (55, '导出质检充值记录', 1, 'qcRechargeExportStep', 'EXPORT_QC_RECHARGE_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (56, '导出客户状态统计数据', 1, 'tenantStatsDetailExportStep', 'EXPORT_OPE_TENANT_STATS_DETAIL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (57, '导出客户状态统计数据', 1, 'tenantStatsDetailExportStep', 'EXPORT_OPE_TENANT_STATS_DETAIL_NEW');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (58, '导出OPE直销客户列表', 1, 'opeDirectCustomerExportStep', 'EXPORT_OPE_DIRECT_CUSTOMER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (59, '导出OPE的代理商列表', 1, 'opeDirectDistributorExportStep', 'EXPORT_OPE_DIRECT_DISTRIBUTOR');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (60, '导出OPE的代理商客户列表', 1, 'opeDistributorCustomerExportStep', 'EXPORT_OPE_DISTRIBUTOR_CUSTOMER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (61, '导出OPE的二级代理商列表', 1, 'opeSubDistributorExportStep', 'EXPORT_OPE_SUB_DISTRIBUTOR');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (62, '导出OPE的二级代理商客户列表', 1, 'opeSubDistributorCustomerExportStep', 'EXPORT_OPE_SUB_DISTRIBUTOR_CUSTOMER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (63, '导出OPE线路列表', 1, 'opePhoneLineExportStep', 'EXPORT_OPE_PHONE_LINE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (64, 'OPE导出训练历史', 1, 'crmDialogTrainRecordExportStep', 'OPE_EXPORT_DIALOG_TRAIN_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (65, 'CRM导出训练历史', 1, 'crmDialogTrainRecordExportStep', 'CRM_EXPORT_DIALOG_TRAIN_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (66, 'BOSS导出训练历史', 1, 'crmDialogTrainRecordExportStep', 'BOSS_EXPORT_DIALOG_TRAIN_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (67, '导出客户分析趋势', 1, 'opeTenantStatsExportStep', 'OPE_EXPORT_TENANT_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (68, '导出客户分析趋势', 1, 'opeTenantStatsExportStep', 'BOSS_EXPORT_TENANT_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (69, 'BOSS导出代理商AI坐席', 1, 'bossDistributorAiExportStep', 'BOSS_EXPORT_DISTRIBUTOR_AI_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (70, 'BOSS导出代理商AI话术', 1, 'bossDistributorDialogflowExportStep', 'BOSS_EXPORT_DISTRIBUTOR_DIALOG_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (71, 'BOSS导出代理商余额流水', 1, 'bossDistributorAccountExportStep', 'BOSS_EXPORT_DISTRIBUTOR_ACCOUNT_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (72, '导出短信充值记录', 1, 'rechargeStreamExportStep', 'BOSS_EXPORT_SMS_RECHARGE_STREAM');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (73, '导出线路充值记录', 1, 'rechargeStreamExportStep', 'BOSS_EXPORT_LINE_RECHARGE_STREAM');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (74, '导出意图学习', 1, 'unidentifiedCallDetailExportStep', 'EXPORT_UNIDENTIFIED_CALL_DETAIL_MAN');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (75, '导出意图学习', 1, 'unidentifiedCallDetailExportStep', 'EXPORT_UNIDENTIFIED_CALL_DETAIL_MACHINE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (76, '我的客户批量导入黑名单', 1, 'myCustomerBatchImportWhiteLineStep', 'CRM_EXPORT_BATCH_IMPORT_WHITE_LINE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (77, '导出试用申请', 1, 'huaweiFreeTryExportStep', 'EXPORT_HUAWEI_FREE_TRY');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (78, '导出接待统计信息', 1, 'callInReceptionExportStep', 'EXPORT_CALL_IN_RECEPTION_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (79, '导出呼入接待联系历史', 1, 'callInRecordExportStep', 'EXPORT_CALL_IN_RECORD_HISTORY');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (80, '导出语音坐席统计', 1, 'staffStatListExportStep', 'EXPORT_STAFF_STAT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (81, '导出语音坐席组统计', 1, 'groupStatListExportStep', 'EXPORT_GROUP_STAT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (82, '导出文本坐席统计', 1, 'staffTextStatExportStep', 'EXPORT_TEXT_STAFF_STAT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (83, '导出文本坐席组统计', 1, 'groupTextStatExportStep', 'EXPORT_TEXT_GROUP_STAT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (84, '导出单点短信发送历史', 1, 'exportCustomerSmsMessageRecordStep', 'EXPORT_CUSTOMER_SMS_MESSAGE_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (85, '导出语音客服联系历史', 1, 'exportCsCallRecordStep1', 'EXPORT_CS_CALL_RECORD1');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (86, '导出语音客服联系历史', 1, 'exportCsCallRecordStep2', 'EXPORT_CS_CALL_RECORD2');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (87, '导出语音客服联系历史', 1, 'exportCsCallRecordStep3', 'EXPORT_CS_CALL_RECORD3');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (88, '导出文本客服联系历史', 1, 'exportTextRecordStep', 'EXPORT_TEXT_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (89, '导出意图学习', 1, 'unidentifiedCallDetailExportStep', 'EXPORT_UNIDENTIFIED_CALL_DETAIL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (90, '导出已质检记录', 1, 'qcRecordExportStep', 'QC_EXPORT_RECORD_LIST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (91, '人工外呼', 1, 'exportCsRecordStep', 'EXPORT_CS_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (92, '导出总账户的流水记录', 1, 'tenantAccountRechargeStep', 'EXPORT_OPE_DIRECT_AICC_ACCOUNT_RECHARGE_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (93, '导出话费的流水记录', 1, 'tenantLineRechargeStep', 'EXPORT_OPE_DIRECT_AICC_LINE_RECHARGE_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (94, '导出短信费的流水记录', 1, 'tenantMessageRechargeStep', 'EXPORT_OPE_DIRECT_AICC_MESSAGE_RECHARGE_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (95, '导出质检的流水记录', 1, 'tenantQcRechargeStep', 'EXPORT_OPE_DIRECT_AICC_QC_RECHARGE_RECORDS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (96, '通话描述文件上传', 0, 'qcJobImportDescFileStep', 'QC_JOB_IMPORT_DESC_FILE_STEP');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (97, '导出抽检任务历史', 1, 'qcCheckRecordExportStep', 'EXPORT_QC_CHECK_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (98, '客户导入到预测式外呼任务', 0, 'customerPersonAddImportCsBatchStep', 'IMPORT_CUSTOMERS_ADD_CS_BATCH_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (99, '导出未呼客户', 1, 'csBatchNotStartExportStep', 'CS_BATCH_JOB_NOT_START_RECORD_EXPORT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (100, '导出已呼客户', 1, 'csBatchCompleteExportStep', 'CS_BATCH_JOB_COMPLETE_RECORD_EXPORT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (101, '未呼客户导入到预测式外呼任务', 0, 'csBatchNotStartImportStep', 'CS_BATCH_NOT_START_IMPORT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (102, '已呼客户列表重新添加到预测试外呼任务', 0, 'csBatchCompleteImportStep', 'CS_BATCH_JOB_COMPLETE_RECORD_IMPORT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (103, '从AI外呼任务联系历史导入客户到预测式外呼任务', 0, 'importCustomerFromCallRecordToCsBatchCallJobStep', 'IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_CS_BATCH_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (104, '从呼入历史导入客户到预测式外呼任务', 0, 'importCustomerFromCallInRecordToCsBatchCallJobStep', 'IMPORT_CUSTOMER_FROM_CALL_IN_RECORD_TO_CS_BATCH_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (105, '从呼入历史导入客户到AI外呼任务', 0, 'importCustomerFromCallInRecordToRobotCallJobStep', 'IMPORT_CUSTOMER_FROM_CALL_IN_RECORD_TO_ROBOT_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (106, '导入客户黑名单', 0, 'customerWhiteListImport', 'CUSTOMER_WHITE_LIST_IMPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (107, '导出客户黑名单', 1, 'customerWhiteListExport', 'CUSTOMER_WHITE_LIST_EXPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (108, '移动客户黑名单', 0, 'customerWhiteListMove', 'CUSTOMER_WHITE_LIST_MOVE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (109, '导出通话统计数据', 1, 'callStatsDetailExportStep', 'EXPORT_CALL_STATS_DETAIL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (110, '导出所有计费统计客户线路', 1, 'financeTenantAllLineStatsExportStep', 'EXPORT_FINANCE_TENANT_ALL_LINE_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (111, '导出质检考核反馈记录', 1, 'qcInspectionFeedbackExportStep', 'QC_EXPORT_INSPECTION_FEEDBACK_LIST_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (112, '导出复审记录', 1, 'qcAuditRecordExportStep', 'QC_AUDIT_RECORD_EXPORT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (113, '导入客户到坐席待呼任务', 0, 'csWaitingCallCustomerPersonImportStep', 'IMPORT_CUSTOMER_TO_WAITING_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (114, '待呼列表导入到预测式外呼任务', 0, 'callWaitingImportCsBatchStep', 'IMPORT_WAITING_CALL_TO_CS_BATCH_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (115, '为坐席分配待呼任务', 1, 'assignCallOutStep', 'ASSIGN_CALL_OUT_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (116, '导出潜在客户列表', 1, 'customerPersonPotentialStep', 'EXPORT_CUSTOMER_PERSON_POTENTIAL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (117, '批量修改客户标签', 0, 'batchUpdateCustomerPersonLevelTag', 'BATCH_UPDATE_CUSTOMER_PERSON_LEVEL_TAG');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (118, '批量修改客户分组', 0, 'batchUpdateCustomerPersonGroup', 'BATCH_UPDATE_CUSTOMER_PERSON_GROUP');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (119, '向质检范围为坐席外呼的质检任务导入记录', 0, 'importRecordIntoCallOutQcJobStep', 'IMPORT_RECORD_INTO_CALL_OUT_QC_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (120, '向质检范围为坐席接待的质检任务导入呼入记录', 0, 'importRecordIntoCallInQcJobStep', 'IMPORT_CALL_IN_RECORD_INTO_CALL_IN_QC_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (121, '向质检范围为坐席接待的质检任务导入预测式外呼任务记录', 0, 'importCsRecordIntoCallInQcJobStep', 'IMPORT_CS_RECORD_INTO_CALL_IN_QC_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (122, '导入问法语料', 0, 'intentBranchCorpusImportStep', 'IMPORT_INTENT_BRANCH_CORPUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (123, '导出问法语料', 1, 'intentBranchCorpusExportStep', 'EXPORT_INTENT_BRANCH_CORPUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (124, '从AI外呼任务联系历史导入客户到预测式外呼任务', 0, 'lastCallRecordToCsBatchCallJobStep', 'LAST_CALL_RECORD_TO_CS_BATCH_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (125, '待呼列表转移', 1, 'transferCallWaitingStep', 'TRANSFER_CS_WAITING_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (126, '导入问答知识', 0, 'knowledgeCorpusImportStep', 'IMPORT_KNOWLEDGE_CORPUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (127, '导出问答知识', 1, 'knowledgeCorpusExportStep', 'EXPORT_KNOWLEDGE_CORPUS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (128, '导出语音坐席', 1, 'csStaffInfoStep', 'EXPORT_CS_STAFF_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (129, 'AI外呼导入到待呼任务', 1, 'assignCallRecordStep', 'ASSIGN_CALL_RECORD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (130, 'AI外呼导入到待呼任务', 1, 'lastAssignCallRecordJob', 'LAST_ASSIGN_CALL_RECORD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (131, '批量重新发送微信加好友', 1, 'sendAddFriendMessageStep', 'SEND_ADD_FRIEND');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (132, '导出共享黑名单', 1, 'opeBossShareWhiteListExportStep', 'EXPORT_OPE_BOSS_SHARE_WHITE_LIST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (133, '导出按量计费统计', 1, 'tenantPayTypeExportStep', 'EXPORT_OPE_FINANCE_TENANT_PAY_TYPE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (134, '导出呼入短信发送历史', 1, 'exportCallInSmsStep', 'EXPORT_CALLIN_SMS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (135, '导出呼叫任务的联系历史', 1, 'callRecordESExportFromJobStep', 'EXPORT_CALL_RECORDS_ES_FROM_ROBOT_CALL_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (136, '导入文本质检记录', 0, 'importTextRecordIntoQcJobStep', 'IMPORT_TEXT_RECORD_INTO_QC_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (137, '导出客服标签', 1, 'exportCsQcRuleTagStep', 'EXPORT_CS_QC_RULE_TAG');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (138, '导入语句标签', 0, 'importQcRuleTagStep', 'IMPORT_QC_RULE_TAG');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (139, '导出组织架构用户', 1, 'organizationUserExportStep', 'EXPORT_ORGANIZATION_USER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (140, '导出客户标签', 1, 'exportCustomerQcRuleTagStep', 'EXPORT_CUSTOMER_QC_RULE_TAG');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (141, '组织架构导入客户', 0, 'platformCustomerPersonImportStep', 'IMPORT_PLATFORM_CUSTOMERS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (142, '导出代理商充值流水', 1, 'opeDistributorPrestoreStep', 'EXPORT_OPE_FINANCE_DISTRIBUTOR_PRESTORE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (143, '微信变量批量替换', 1, 'wechatValueReplaceStep', 'WECHAT_VALUE_REPLACE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (144, '外呼过滤导入到外呼任务', 0, 'filteredTaskImportStep', 'FILTERED_TASK_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (145, '外呼过滤导出', 1, 'filteredTaskExportStep', 'FILTERED_TASK_EXPORT_EXCEL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (146, '外呼联系历史导入到待呼列表', 1, 'callOutImportToWaitingCallStep', 'CALL_OUT_IMPORT_TO_WAITING_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (147, '呼入联系历史导入到待呼列表', 1, 'callInImportToWaitingCallStep', 'CALL_IN_IMPORT_TO_WAITING_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (148, '已质检记录分配人工抽检', 1, 'importToQcCheckStep', 'QC_RECORD_IMPORT_TO_CHECK_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (149, '已质检记录导入到质检任务', 1, 'importQcRecordToQcJobStep', 'QC_RECORD_IMPORT_TO_QC_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (150, '导出计费统计代理商客户线路', 1, 'financeStatsExportForQiyuStep', 'EXPORT_FINANCE_TENANT_LINE_STATS_FOR_QIYU');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (151, '导出OPE的代理商客户AI坐席', 1, 'opeDistributorCustomerExportForQiyuStep', 'EXPORT_OPE_DISTRIBUTOR_CUSTOMER_FOR_QIYU');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (152, '导出OPE代理商绑定话术列表', 1, 'opeDistributorDialogFlowExportStep', 'EXPORT_OPE_DISTRIBUTOR_DIALOG_FLOW');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (153, '将客户退回公海', 0, 'transferPrivateToPublic', 'TRANSFER_PRIVATE_TO_PUBLIC');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (154, '公海领取客户', 1, 'transferPublicToPrivate', 'TRANSFER_PUBLIC_TO_PRIVATE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (155, '分配客户', 1, 'distributeCustomerPerson', 'DISTRIBUTE_CUSTOMER_PERSON');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (156, '客户中心批量删除客户', 1, 'customerPersonDeleteStep', 'DELETE_PRIVATE_CUSTOMER_PERSON');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (157, '公海批量删除客户', 1, 'customerPersonDeleteStep', 'DELETE_PUBLIC_CUSTOMER_PERSON');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (158, '导出待呼列表', 1, 'csCallWaitingExportStep', 'EXPORT_CS_CALL_WAITING');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (159, '导出话费详单', 1, 'callCostCsRecordExportStep', 'EXPORT_CALL_COST_CS_RECORD');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (160, '我的客户导出客户', 1, 'customerPersonExportFromEsStep', 'EXPORT_PRIVATE_CUSTOMERS_ES');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (161, '导出质检计费统计', 1, 'opeTenantQcCostExportStep', 'OPE_EXPORT_QC_COST_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (162, '导出质检计费明细', 1, 'opeTenantQcCostDetailExportStep', 'OPE_EXPORT_QC_COST_DETAIL_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (163, '导出话术线路行业对应关系列表', 1, 'opeDialogPhoneRelationExportStep', 'EXPORT_OPE_DIALOG_PHONE_RELATION');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (164, '实体导出', 1, 'entityExportStep', 'ENTITY_EXPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (165, '实体导入', 0, 'entityImportStep', 'ENTITY_IMPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (166, '导出通话费用信息（按日期任务汇总）', 1, 'callCostInfoByTaskExportStep', 'EXPORT_CALL_COST_INFO_BY_TASK');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (167, '导出通话费用信息（按月按任务汇总）', 1, 'callCostMonthInfoByTaskExportStep', 'EXPORT_CALL_COST_MONTH_INFO_BY_TASK');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (168, '导出外呼巡检统计数据', 1, 'callRecordInspectsStatsExportStep', 'EXPORT_CALL_RECORD_INSPECTS_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (169, '导出外呼巡检数据', 1, 'callRecordInspectsStatsExportStep', 'EXPORT_CALL_RECORD_INSPECTS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (170, '导出业务网关落地分析表', 1, 'vosReportExportStep', 'EXPORT_VOS_REPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (171, '按产品导出OPE直销客户列表', 1, 'opeDirectCustomerByRobotExportStep', 'EXPORT_OPE_DIRECT_CUSTOMER_BY_ROBOT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (172, '导出代理商直销客户', 1, 'directCustomerByRobotExportStep', 'EXPORT_DIRECT_CUSTOMER_BY_ROBOT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (173, '导出OPE的代理商客户列表', 1, 'opeDistributorCustomerByRobotExportStep', 'EXPORT_OPE_DISTRIBUTOR_CUSTOMER_BY_ROBOT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (174, '按话术导出OPE直销客户列表', 1, 'opeDirectCustomerByDialogFlowExportStep', 'EXPORT_OPE_DIRECT_CUSTOMER_BY_DIALOG_FLOW');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (175, '批量修改客户的话费', 1, 'opeBatchUpdatePhoneBill', 'OPE_BATCH_UPDATE_PHONE_BILL_STEP');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (176, '短信模板数据统计', 1, 'opeDirectCustomerByDialogFlowExportStep', 'EXPORT_SMS_TEMPLATE_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (177, '批量导入知识问答测试', 0, 'knowledgeTestImportStep', 'KNOWLEDGE_TEST_IMPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (178, '阈值推荐结果导出', 1, 'recommendResultExportStep', 'RECOMMEND_RESULT_EXPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (179, '批量测试结果导出', 1, 'testResultExportStep', 'TEST_RESULT_EXPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (180, '导出隐私号码', 1, 'privacyNumberExportStep', 'EXPORT_PRIVACY_NUMBER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (181, '导出隐私号联系历史', 1, 'privacyNumberHistoryExportStep', 'EXPORT_PRIVACY_NUMBER_HISTORY');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (182, '导出日报', 1, 'dailyExportStep', 'EXPORT_DAILY');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (183, '导出数据对比分析表', 1, 'abTestExportStep', 'EXPORT_AB_TEST_INFO');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (184, '预测试外呼过滤导出', 1, 'filteredCsTaskExportStep', 'FILTERED_CS_TASK_EXPORT_EXCEL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (185, '预测试外呼过滤导入到预测试外呼任务', 0, 'filteredCsTaskImportStep', 'FILTERED_CS_TASK_ADD_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (186, '导出线路供应商消耗表', 1, 'phoneNumberSupplierStatsExportStep', 'EXPORT_PHONE_NUMBER_SUPPLIER');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (187, '导出投诉记录统计', 1, 'complaintHistoryExportStep', 'EXPORT_COMPLAINT_HISTORY');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (188, '导出投诉记录分析', 1, 'complaintStatsExportStep', 'EXPORT_COMPLAINT_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (189, '导出数据报表', 1, 'dataReportExportStep', 'EXPORT_DATA_REPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (190, 'OPE导出隐私号码', 1, 'privacyNumberOpeExportStep', 'EXPORT_PRIVACY_NUMBER_OPE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (191, '知识共享批量导入', 0, 'sharedKnowledgeImportStep', 'IMPORT_SHARED_KNOWLEDGE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (192, '商机分析导出客户关注点', 1, 'customerConcernExportStep', 'EXPORT_CUSTOMER_CONCERN');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (193, '商机分析导出问答知识触发', 1, 'customerKnowledgeActiveExportStep', 'EXPORT_KNOWLEDGE_ACTIVE');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (194, '话术费用导出', 1, 'dialogFlowBillingExportStep', 'EXPORT_DIALOG_FLOW_BILLING');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (195, 'OPE导出计费统计客户短信(新)', 1, 'exportFinanceSmsStatsStep', 'EXPORT_FINANCE_SMS_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (196, 'OPE导出计费统计直销客户计费统计(新)', 1, 'exportFinanceDirectCustomerStatsStep', 'EXPORT_FINANCE_DIRECT_CUSTOMER_STATS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (197, '呼叫任务已呼客户添加到短信任务--去重', 0, 'CustomerFromCallRecordToSmsJobImportStep', 'IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_SMS_JOB_LAST_CALL');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (198, '呼叫任务已呼客户添加到短信任务', 0, 'CustomerFromCallRecordToSmsJobImportStep', 'IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_SMS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (199, '呼叫任务最后一通已呼客户添加到短信任务', 0, 'LastCallFromCallRecordToSmsJobImportStep', 'IMPORT_LAST_CALL_FROM_CALL_RECORD_TO_SMS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (200, '呼叫任务未呼客户添加到短信任务', 0, 'CustomerFromCallRecordToSmsJobImportStep', 'IMPORT_TO_BE_CALLED_CUSTOMER_TO_SMS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (201, '呼叫任务过滤客户添加到短信任务', 0, 'CustomerFromCallRecordToSmsJobImportStep', 'IMPORT_FILTERED_CUSTOMER_TO_SMS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (202, '呼叫任务联系历史重新添加客户到短信任务', 0, 'customerPersonReAddCallRecordToSmsJobImportStep', 'IMPORT_CALL_RECORD_TO_SMS_JOB');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (203, '共享黑名单批量导入', 0, 'shareBlackListImportStep', 'IMPORT_SHARED_BLACK_LIST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (204, '导出外呼数据分析报表', 1, 'callOutDataReportExportStep', 'EXPORT_CALL_OUT_DATA_REPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (205, '导出外呼数据分析报表明细', 1, 'callOutDataReportDetailExportStep', 'EXPORT_CALL_OUT_DATA_DETAIL_REPORT');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (206, '导出网关关联线路列表', 1, 'phoneNumberByGwExportStep', 'EXPORT_PHONE_NUMBER_BY_GATEWAYS');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (207, '导出话单信息', 1, 'opensipsAccExportStep', 'EXPORT_OPENSIPS_ACC');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (208, '话术推荐导入', 0, 'dialogFlowRecommendImportStep', 'IMPORT_DIALOGFLOW_RECOMMEND');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (209, '话术推荐导出', 1, 'dialogFlowRecommendExportStep', 'EXPORT_DIALOGFLOW_RECOMMEND');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (210, '导出主品牌列表', 1, 'mainBrandExportStep', 'EXPORT_MAIN_BRAND_LIST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (211, '导出主品牌费用汇总', 1, 'mainBrandTenantCostExportStep', 'EXPORT_MAIN_BRAND_TENANT_COST');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (212, '批量删除未呼客户', 1, 'robotCallTaskDeleteStep', 'DELETE_ROBOT_CALL_TASK');

INSERT INTO ai_call_engine.spring_batch_job_type (spring_batch_job_type_id, description, file_type, job_count_step_name, name) VALUES (213, '导出计费统计短信测试', 1, 'exportSmsTestStatStep', 'EXPORT_SMS_TEST_STAT_JOB');

alter table spring_batch_job_log
    add job_type_id int null comment '微服务版本的任务类型';

alter table spring_batch_job_log alter column job_type set default -999999;

CREATE TABLE `call_record_action_log` (
   `call_record_action_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
   `call_record_id` bigint(20) DEFAULT NULL COMMENT '通话记录id',
   `dialog_flow_id` bigint(20) DEFAULT NULL COMMENT '话术id',
   `reason` varchar(2048) DEFAULT NULL COMMENT '执行原因',
   `rule_id` varchar(128) DEFAULT NULL COMMENT '规则id',
   `sms_template_id_list` varchar(2048) DEFAULT NULL COMMENT '短信模板id列表',
   `sms_template_name_list` varchar(2048) DEFAULT NULL COMMENT '短信模板名称列表',
   `white_group_id_list` varchar(2048) DEFAULT NULL COMMENT '黑名单id列表',
   `white_group_name_list` varchar(2048) DEFAULT NULL COMMENT '黑名单名称列表',
   `customer_tag_id_list` varchar(2048) DEFAULT NULL COMMENT '客户标签id列表',
   `customer_tag_name_list` varchar(2048) DEFAULT NULL COMMENT '客户标签名称列表',
   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
   PRIMARY KEY (`call_record_action_log_id`),
   KEY `idx_callrecordid` (`call_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=59160512 DEFAULT CHARSET=utf8 COMMENT='通话记录的动作匹配日志';
alter table spring_batch_job_log
    add service_name varchar(32) not null comment '微服务名称';

CREATE TABLE `call_record_action_log` (
   `call_record_action_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
   `call_record_id` bigint(20) DEFAULT NULL COMMENT '通话记录id',
   `dialog_flow_id` bigint(20) DEFAULT NULL COMMENT '话术id',
   `reason` varchar(2048) DEFAULT NULL COMMENT '执行原因',
   `rule_id` varchar(128) DEFAULT NULL COMMENT '规则id',
   `sms_template_id_list` varchar(2048) DEFAULT NULL COMMENT '短信模板id列表',
   `sms_template_name_list` varchar(2048) DEFAULT NULL COMMENT '短信模板名称列表',
   `white_group_id_list` varchar(2048) DEFAULT NULL COMMENT '黑名单id列表',
   `white_group_name_list` varchar(2048) DEFAULT NULL COMMENT '黑名单名称列表',
   `customer_tag_id_list` varchar(2048) DEFAULT NULL COMMENT '客户标签id列表',
   `customer_tag_name_list` varchar(2048) DEFAULT NULL COMMENT '客户标签名称列表',
   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
   PRIMARY KEY (`call_record_action_log_id`),
   KEY `idx_callrecordid` (`call_record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=59160512 DEFAULT CHARSET=utf8 COMMENT='通话记录的动作匹配日志';
