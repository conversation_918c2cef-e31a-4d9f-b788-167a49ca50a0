INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_index', NULL, NULL, 'view', 1791, '首页', NULL, 11, NULL, 1, 0, '2020-02-11 14:32:16', 0, '2020-02-21 14:48:43');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task', NULL, NULL, 'view', 1791, '呼叫任务', NULL, 11, NULL, 1, 0, '2020-02-11 14:33:57', 0, '2020-02-21 14:48:44');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage', NULL, NULL, 'view', 1791, '坐席管理', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:46');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design', NULL, NULL, 'view', 1791, '话术设计', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:48');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge', NULL, NULL, 'view', 1791, '知识共享', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:50');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2', NULL, NULL, 'view', 1791, '话术设计2.0', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:52');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intentionTag', NULL, NULL, 'view', 1791, '意向标签', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:56');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning', NULL, NULL, 'view', 1791, '意图学习', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:48:58');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory', NULL, NULL, 'view', 1791, '联系历史', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:00');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsDial', NULL, NULL, 'view', 1791, '通话统计', 'crm_out_call_platform_call_task', 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:01');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBusi', NULL, NULL, 'view', 1791, '商机分析', 'crm_out_call_platform_call_task', 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:02');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBill', NULL, NULL, 'view', 1791, '计费汇总', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:04');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_config', NULL, NULL, 'view', 1791, '设置', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:05');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_other', NULL, NULL, 'view', 1791, '其他', NULL, 11, NULL, 1, 0, '2020-02-11 15:03:38', 0, '2020-02-21 14:49:08');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_index_view', NULL, NULL, 'button', 792, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 15:05:43', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_data_permission_my', NULL, NULL, 'dataAuth', 792, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 15:08:37', 0, '2020-02-21 14:15:13');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_data_permission_organization', NULL, NULL, 'dataAuth', 792, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 15:09:35', 0, '2020-02-21 14:16:20');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_data_permission_company', NULL, NULL, 'dataAuth', 792, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 15:09:35', 0, '2020-02-21 14:15:35');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_view', NULL, NULL, 'button', 793, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_add_edit', NULL, NULL, 'button', 793, '新建/编辑', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_import_customer', NULL, NULL, 'button', 793, '导入客户', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_export_customer', NULL, NULL, 'button', 793, '导出客户', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_export_record', NULL, NULL, 'button', 793, '导出记录', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_down_record', NULL, NULL, 'button', 793, '下载通话录音', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_view_detail', NULL, NULL, 'button', 793, '查看通话详情', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_retotask', NULL, NULL, 'button', 793, '重新添加/导入到任务', NULL, 11, NULL, 1, 0, '2020-02-11 15:44:54', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_data_permission_my', NULL, NULL, 'dataAuth', 793, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 15:47:14', 0, '2020-02-21 14:15:14');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_data_permission_organization', NULL, NULL, 'dataAuth', 793, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 15:47:14', 0, '2020-02-21 14:16:21');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_call_task_data_permission_company', NULL, NULL, 'dataAuth', 793, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 15:47:14', 0, '2020-02-21 14:15:36');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_ai', NULL, NULL, 'view', 794, 'AI坐席', NULL, 11, NULL, 1, 0, '2020-02-11 15:50:52', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_cu', NULL, NULL, 'view', 794, '人工坐席', NULL, 11, NULL, 1, 0, '2020-02-11 15:50:52', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_seatGroup', NULL, NULL, 'view', 794, '坐席组', NULL, 11, NULL, 1, 0, '2020-02-11 15:50:52', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_ai_view', NULL, NULL, 'view', 823, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_cu_view', NULL, NULL, 'view', 824, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_cu_add_edit', NULL, NULL, 'view', 824, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_cu_enable_unable', NULL, NULL, 'view', 824, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_cu_delete', NULL, NULL, 'view', 824, '删除', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_seatGroup_view', NULL, NULL, 'view', 825, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_seatGroup_add_edit', NULL, NULL, 'view', 825, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_seatGroup_enable_unable', NULL, NULL, 'view', 825, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_seatGroup_delete', NULL, NULL, 'view', 825, '删除', NULL, 11, NULL, 1, 0, '2020-02-11 15:56:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_data_permission_my', NULL, NULL, 'dataAuth', 794, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 15:59:04', 0, '2020-02-21 14:15:14');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_data_permission_organization', NULL, NULL, 'dataAuth', 794, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 15:59:04', 0, '2020-02-21 14:16:22');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_seat_manage_data_permission_company', NULL, NULL, 'dataAuth', 794, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 15:59:04', 0, '2020-02-21 14:15:37');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_view', NULL, NULL, 'view', 795, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:05:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_add_edit_copy', NULL, NULL, 'button', 795, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-11 16:05:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_dialog_audio', NULL, NULL, 'button', 795, '话术录音', NULL, 11, NULL, 1, 0, '2020-02-11 16:05:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_delete', NULL, NULL, 'button', 795, '删除', NULL, 11, NULL, 1, 0, '2020-02-11 16:05:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_data_permission_my', NULL, NULL, 'dataAuth', 795, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 16:06:26', 0, '2020-02-21 14:15:16');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_data_permission_organization', NULL, NULL, 'dataAuth', 795, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 16:06:26', 0, '2020-02-21 14:16:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_data_permission_company', NULL, NULL, 'dataAuth', 795, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:06:26', 0, '2020-02-21 14:15:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_view', NULL, NULL, 'view', 796, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:11:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_add_edit', NULL, NULL, 'button', 796, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-11 16:11:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_import', NULL, NULL, 'button', 796, '导入话术知识', NULL, 11, NULL, 1, 0, '2020-02-11 16:11:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_synchronization', NULL, NULL, 'button', 796, '同步到话术', NULL, 11, NULL, 1, 0, '2020-02-11 16:11:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_delete', NULL, NULL, 'button', 796, '删除知识/分组', NULL, 11, NULL, 1, 0, '2020-02-11 16:11:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_share_knowledge_data_permission_company', NULL, NULL, 'dataAuth', 796, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:12:43', 0, '2020-02-21 14:15:40');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_view', NULL, NULL, 'view', 797, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:17:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_add_edit_copy', NULL, NULL, 'button', 797, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-11 16:17:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_dialog_audio', NULL, NULL, 'button', 797, '话术录音', NULL, 11, NULL, 1, 0, '2020-02-11 16:17:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_delete', NULL, NULL, 'button', 797, '删除', NULL, 11, NULL, 1, 0, '2020-02-11 16:17:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_data_permission_my', NULL, NULL, 'dataAuth', 797, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 16:18:14', 0, '2020-02-21 14:15:17');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_data_permission_organization', NULL, NULL, 'dataAuth', 797, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 16:18:14', 0, '2020-02-21 14:16:24');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_2_data_permission_company', NULL, NULL, 'dataAuth', 797, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:18:14', 0, '2020-02-21 14:15:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialogflow_design_data_permission_partDialog', NULL, NULL, 'dataAuth', 795, '部分话术', NULL, 11, NULL, 1, 0, '2020-02-11 16:19:05', 0, '2020-02-21 14:16:51');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intentionTag_view', NULL, NULL, 'button', 798, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:23:57', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intentionTag_add_edit_copy', NULL, NULL, 'button', 798, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-11 16:23:57', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intentionTag_delete', NULL, NULL, 'button', 798, '删除', NULL, 11, NULL, 1, 0, '2020-02-11 16:23:57', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intentionTag_data_permission_company', NULL, NULL, 'dataAuth', 798, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:24:56', 0, '2020-02-21 14:15:43');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_view', NULL, NULL, 'view', 800, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:44:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_export', NULL, NULL, 'button', 800, '导出通话记录', NULL, 11, NULL, 1, 0, '2020-02-11 16:44:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_download', NULL, NULL, 'button', 800, '下载通话录音', NULL, 11, NULL, 1, 0, '2020-02-11 16:44:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_data_permission_my', NULL, NULL, 'dataAuth', 800, '我自己', NULL, 11, NULL, 1, 0, '2020-02-11 16:45:21', 0, '2020-02-21 14:15:18');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_data_permission_organization', NULL, NULL, 'dataAuth', 800, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-11 16:45:21', 0, '2020-02-21 14:16:25');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_dialHistory_data_permission_company', NULL, NULL, 'dataAuth', 800, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:45:21', 0, '2020-02-21 14:15:45');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsDial_view', NULL, NULL, 'view', 801, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:48:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsDial_export', NULL, NULL, 'view', 801, '导出数据', NULL, 11, NULL, 1, 0, '2020-02-11 16:48:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBusi_view', NULL, NULL, 'view', 802, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:49:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_config_view', NULL, NULL, 'view', 804, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:53:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_config_dialog', NULL, NULL, 'button', 804, '通话设置', NULL, 11, NULL, 1, 0, '2020-02-11 16:53:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_config_warn', NULL, NULL, 'button', 804, '预警设置', NULL, 11, NULL, 1, 0, '2020-02-11 16:53:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_other_hide', NULL, NULL, 'button', 805, '隐藏客户号码', NULL, 11, NULL, 1, 0, '2020-02-11 16:54:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_display', NULL, NULL, 'view', 1895, '数据展示', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:19');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_reception_scene', NULL, NULL, 'view', 1895, '接待场景', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:21');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage', NULL, NULL, 'view', 1895, '坐席管理', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:22');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_voice_navigation', NULL, NULL, 'view', 1895, '语音导航', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design', NULL, NULL, 'view', 1895, '话术设计', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:24');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2', NULL, NULL, 'view', 1895, '话术设计2.0', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:26');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_intentionTag', NULL, NULL, 'view', 1895, '意向标签', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:27');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge', NULL, NULL, 'view', 1895, '知识共享', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:29');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory', NULL, NULL, 'view', 1895, '联系历史', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:31');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsDial', NULL, NULL, 'view', 1895, '通话统计', 'crm_out_call_platform_call_task', 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:32');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_statisticsBill', NULL, NULL, 'view', 1895, '计费汇总', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:35');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_other', NULL, NULL, 'view', 1895, '其他', NULL, 11, NULL, 1, 0, '2020-02-12 10:16:02', 0, '2020-02-21 14:49:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_display_view', NULL, NULL, 'view', 897, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:28:48', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_display_data_permission_my', NULL, NULL, 'dataAuth', 897, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 10:29:34', 0, '2020-02-21 14:15:19');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_display_data_permission_organization', NULL, NULL, 'dataAuth', 897, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 10:29:34', 0, '2020-02-21 14:16:26');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_data_display_data_permission_company', NULL, NULL, 'dataAuth', 897, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 10:29:34', 0, '2020-02-21 14:15:47');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_reception_scene_view', NULL, NULL, 'view', 898, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:32:50', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_reception_scene_add_edit', NULL, NULL, 'view', 898, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-12 10:32:50', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_reception_scene_enable_unable', NULL, NULL, 'view', 898, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-12 10:32:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_reception_scene_delete', NULL, NULL, 'view', 898, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 10:32:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_ai', NULL, NULL, 'view', 899, 'AI坐席', NULL, 11, NULL, 1, 0, '2020-02-12 10:35:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_cu', NULL, NULL, 'view', 899, '人工坐席', NULL, 11, NULL, 1, 0, '2020-02-12 10:35:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_seatGroup', NULL, NULL, 'view', 899, '坐席组', NULL, 11, NULL, 1, 0, '2020-02-12 10:35:47', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_ai_view', NULL, NULL, 'view', 918, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_cu_view', NULL, NULL, 'view', 919, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_cu_add_edit', NULL, NULL, 'view', 919, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_cu_enable_unable', NULL, NULL, 'view', 919, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_cu_delete', NULL, NULL, 'view', 919, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_seatGroup_view', NULL, NULL, 'view', 920, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_seatGroup_add_edit', NULL, NULL, 'view', 920, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_seatGroup_enable_unable', NULL, NULL, 'view', 920, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_seat_manage_seatGroup_delete', NULL, NULL, 'view', 920, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 10:39:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_voice_navigation_view', NULL, NULL, 'view', 900, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 10:42:44', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_voice_navigation_add_edit_copy', NULL, NULL, 'button', 900, '新增/修改/复制', NULL, 11, NULL, 1, 0, '2020-02-12 10:42:44', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_voice_navigation_delete', NULL, NULL, 'button', 900, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 10:42:44', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_view', NULL, NULL, 'view', 901, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_add_edit_copy', NULL, NULL, 'button', 901, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_dialog_audio', NULL, NULL, 'button', 901, '话术录音', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_delete', NULL, NULL, 'button', 901, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_data_permission_my', NULL, NULL, 'dataAuth', 901, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:34', 0, '2020-02-21 14:15:20');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_data_permission_organization', NULL, NULL, 'dataAuth', 901, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:34', 0, '2020-02-21 14:16:27');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_data_permission_company', NULL, NULL, 'dataAuth', 901, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 13:40:34', 0, '2020-02-21 14:15:48');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_data_permission_partDialog', NULL, NULL, 'dataAuth', 901, '部分话术', NULL, 11, NULL, 1, 0, '2020-02-12 13:42:50', 0, '2020-02-21 14:16:52');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_view', NULL, NULL, 'view', 902, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 13:44:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_add_edit_copy', NULL, NULL, 'button', 902, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-12 13:44:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_dialog_audio', NULL, NULL, 'button', 902, '话术录音', NULL, 11, NULL, 1, 0, '2020-02-12 13:44:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_delete', NULL, NULL, 'button', 902, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 13:44:28', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_intentionTag_view', NULL, NULL, 'button', 903, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 13:50:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_intentionTag_add_edit_copy', NULL, NULL, 'button', 903, '新增/编辑/复制', NULL, 11, NULL, 1, 0, '2020-02-12 13:50:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_intentionTag_delete', NULL, NULL, 'button', 903, '删除', NULL, 11, NULL, 1, 0, '2020-02-12 13:50:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_intentionTag_data_permission_company', NULL, NULL, 'dataAuth', 903, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 13:51:26', 0, '2020-02-21 14:15:49');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_view', NULL, NULL, 'view', 904, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_add_edit', NULL, NULL, 'button', 904, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_import', NULL, NULL, 'button', 904, '导入话术知识', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_synchronization', NULL, NULL, 'button', 904, '同步到话术', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_delete', NULL, NULL, 'button', 904, '删除知识/分组', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_share_knowledge_data_permission_company', NULL, NULL, 'dataAuth', 904, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 13:53:36', 0, '2020-02-21 14:15:51');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_view', NULL, NULL, 'view', 905, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:22', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_export', NULL, NULL, 'button', 905, '导出通话记录', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:22', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_download', NULL, NULL, 'button', 905, '下载通话录音', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:22', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_data_permission_my', NULL, NULL, 'dataAuth', 905, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:54', 0, '2020-02-21 14:15:21');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_data_permission_organization', NULL, NULL, 'dataAuth', 905, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:54', 0, '2020-02-21 14:16:28');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialHistory_data_permission_company', NULL, NULL, 'dataAuth', 905, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 13:56:54', 0, '2020-02-21 14:15:52');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsDial_view', NULL, NULL, 'view', 906, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 14:04:57', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsDial_export', NULL, NULL, 'view', 906, '导出数据', NULL, 11, NULL, 1, 0, '2020-02-12 14:04:57', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_other_hide', NULL, NULL, 'button', 908, '隐藏客户号码', NULL, 11, NULL, 1, 0, '2020-02-12 14:10:04', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_voice', NULL, NULL, 'view', 1977, '语音质检', NULL, 11, NULL, 1, 0, '2020-02-12 15:18:29', 0, '2020-02-21 14:49:47');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule', NULL, NULL, 'view', 1977, '质检规则', NULL, 11, NULL, 1, 0, '2020-02-12 15:18:29', 0, '2020-02-21 14:49:48');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_voice_view', NULL, NULL, 'view', 978, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_voice_add_edit', NULL, NULL, 'button', 978, '新建编辑', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_voice_upload_voice', NULL, NULL, 'button', 978, '上传录音', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_voice_export', NULL, NULL, 'button', 978, '导出质检记录', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:14', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_permission_my', NULL, NULL, 'dataAuth', 978, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:59', 0, '2020-02-21 14:15:22');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_permission_organization', NULL, NULL, 'dataAuth', 978, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:59', 0, '2020-02-21 14:16:29');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_permission_company', NULL, NULL, 'dataAuth', 978, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 15:22:59', 0, '2020-02-21 14:15:53');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template', NULL, NULL, 'view', 979, '评分模板', NULL, 11, NULL, 1, 0, '2020-02-12 15:26:52', 0, '2020-02-26 10:22:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin', NULL, NULL, 'view', 979, '规则引擎', NULL, 11, NULL, 1, 0, '2020-02-12 15:26:52', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_state_sign', NULL, NULL, 'view', 979, '语句标签', NULL, 11, NULL, 1, 0, '2020-02-12 15:26:52', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template_view', NULL, NULL, 'view', 991, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template_add', NULL, NULL, 'button', 991, '添加模板', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template_delete', NULL, NULL, 'button', 991, '删除模板', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template_enable_unable', NULL, NULL, 'button', 991, '启用/禁用', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_template_edit', NULL, NULL, 'button', 991, '编辑模板', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:35', 0, '2020-02-21 16:10:05');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin_view', NULL, NULL, 'view', 992, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:41', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin_add', NULL, NULL, 'button', 992, '添加规则', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:41', 0, '2020-03-02 16:24:47');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin_delete', NULL, NULL, 'button', 992, '删除规则', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:41', 0, '2020-03-02 16:24:53');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin_enable_unable', NULL, NULL, 'button', 992, '启用/禁用', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:41', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_engin_edit', NULL, NULL, 'button', 992, '编辑规则', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:41', 0, '2020-03-02 16:24:56');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_state_sign_view', NULL, NULL, 'view', 993, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:45', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_state_sign_add', NULL, NULL, 'button', 993, '添加语句标签', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:45', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_state_sign_delete', NULL, NULL, 'button', 993, '删除语句标签', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:45', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_rule_state_sign_edit', NULL, NULL, 'button', 993, '编辑语句标签', NULL, 11, NULL, 1, 0, '2020-02-12 15:32:45', 0, '2020-02-26 13:48:36');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_cu', NULL, NULL, 'view', 1977, '客服服务', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:10');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_violation', NULL, NULL, 'view', 1977, '违规统计', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:11');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_wordCount', NULL, NULL, 'view', 1977, '词频统计', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:12');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_person_robot', NULL, NULL, 'view', 1977, '人机对比', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:12');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_personal_report', NULL, NULL, 'view', 1977, '个性报表', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:13');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_statisticsBill', NULL, NULL, 'view', 1977, '计费汇总', NULL, 11, NULL, 1, 0, '2020-02-12 15:41:13', 0, '2020-02-24 11:15:14');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_cu_view', NULL, NULL, 'view', 1009, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:43:18', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_cu_data_permission_organization', NULL, NULL, 'dataAuth', 1009, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 15:44:36', 0, '2020-02-21 14:16:30');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_cu_data_permission_company', NULL, NULL, 'dataAuth', 1009, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 15:44:36', 0, '2020-02-21 14:15:55');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_violation_view', NULL, NULL, 'view', 1010, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:46:16', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_violation_data_permission_organization', NULL, NULL, 'dataAuth', 1010, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 15:46:37', 0, '2020-02-21 14:16:31');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_violation_data_permission_company', NULL, NULL, 'dataAuth', 1010, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 15:46:37', 0, '2020-02-21 14:15:56');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_wordCount_view', NULL, NULL, 'view', 1011, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:48:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_wordCount_data_permission_my', NULL, NULL, 'dataAuth', 1011, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 15:49:16', 0, '2020-02-21 17:40:25');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_person_robot_view', NULL, NULL, 'view', 1012, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:50:03', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_personal_report_view', NULL, NULL, 'view', 1013, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:50:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_statisticsBill_view', NULL, NULL, 'view', 1014, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 15:50:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_other_hide', NULL, NULL, 'view', 1981, '隐藏客户号码', NULL, 11, NULL, 1, 0, '2020-02-12 15:52:40', 0, '2020-02-25 12:20:56');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_template', NULL, NULL, 'view', 11032, '模板设置', NULL, 11, NULL, 1, 0, '2020-02-12 17:23:04', 0, '2020-02-21 14:50:06');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_center', NULL, NULL, 'view', 11032, '工单中心', NULL, 11, NULL, 1, 0, '2020-02-12 17:23:04', 0, '2020-02-21 14:50:07');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_data_statistic', NULL, NULL, 'view', 11032, '数据统计', NULL, 11, NULL, 1, 0, '2020-02-12 17:23:04', 0, '2020-02-21 14:50:09');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_template_view', NULL, NULL, 'view', 1033, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 17:26:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_template_add_edit_delete', NULL, NULL, 'button', 1033, '增/删/改', NULL, 11, NULL, 1, 0, '2020-02-12 17:26:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_template_data_permission_my', NULL, NULL, 'dataAuth', 1033, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 17:27:58', 0, '2020-02-21 14:15:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_template_data_permission_company', NULL, NULL, 'dataAuth', 1033, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 17:27:58', 0, '2020-02-21 14:15:59');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_data_statistic_view', NULL, NULL, 'view', 1035, '查看', NULL, 11, NULL, 1, 0, '2020-02-12 17:32:58', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'basic_setting', NULL, NULL, 'menu', 0, '基本设置', NULL, 11, NULL, 1, 0, '2020-02-13 15:04:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'import_export_list', NULL, NULL, 'tab', 1046, '导入导出列表', NULL, 11, NULL, 1, 0, '2020-02-13 15:05:30', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'import_export_list_view', NULL, NULL, 'view', 1047, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:07:32', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'import_export_list_myself', NULL, NULL, 'dataAuth', 1047, '我自己', NULL, 11, NULL, 1, 0, '2020-02-13 15:07:32', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'import_export_list_groups', NULL, NULL, 'dataAuth', 1047, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-13 15:07:32', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'import_export_list_company', NULL, NULL, 'dataAuth', 1047, '全公司', NULL, 11, NULL, 1, 0, '2020-02-13 15:07:32', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'message_center', NULL, NULL, 'tab', 1046, '消息中心', NULL, 11, NULL, 1, 0, '2020-02-13 15:08:00', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'message_center_view', NULL, NULL, 'view', 1052, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:09:14', 0, '2020-02-24 13:57:02');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'ticket_message_view', NULL, NULL, 'view', 1052, '工单消息', NULL, 11, NULL, 1, 0, '2020-02-13 15:09:14', 0, '2020-02-24 13:56:44');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'alarm_message_view', NULL, NULL, 'view', 1052, '预警消息', NULL, 11, NULL, 1, 0, '2020-02-13 15:09:14', 0, '2020-02-24 13:56:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'business_center', NULL, NULL, 'menu', 0, '企业中心', NULL, 11, NULL, 1, 0, '2020-02-13 15:11:40', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'account_info', NULL, NULL, 'tab', 1056, '账号信息', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'billing_summation', NULL, NULL, 'tab', 1056, '计费汇总', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_management', NULL, NULL, 'tab', 1056, '通信管理', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'interface_management', NULL, NULL, 'tab', 1056, '接口管理', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'team_management', NULL, NULL, 'tab', 1056, '团队管理', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'role_management', NULL, NULL, 'tab', 1056, '角色管理', NULL, 11, NULL, 1, 0, '2020-02-13 15:16:33', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'account_info_view', NULL, NULL, 'view', 1057, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'billing_summation_view', NULL, NULL, 'view', 1058, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_management_view', NULL, NULL, 'view', 1059, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'interface_management_view', NULL, NULL, 'view', 1060, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'team_management_view', NULL, NULL, 'view', 1061, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'add_edit_team_member', NULL, NULL, 'button', 1061, '新增/编辑成员', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'enable_disable_team_member', NULL, NULL, 'button', 1061, '启用/停用成员', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'reset_password', NULL, NULL, 'button', 1061, '重置密码', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'delete_team_member', NULL, NULL, 'button', 1061, '删除成员', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'role_management_view', NULL, NULL, 'view', 1062, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'add_edit_role', NULL, NULL, 'button', 1062, '新增/编辑角色', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'delete_role', NULL, NULL, 'button', 1062, '删除角色', NULL, 11, NULL, 1, 0, '2020-02-13 15:19:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_console', NULL, NULL, 'menu', 0, '客服工作台', NULL, 11, NULL, 1, 0, '2020-02-13 16:15:06', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_console', NULL, NULL, 'tab', 1075, '文本客服工作台', NULL, 11, NULL, 1, 0, '2020-02-13 16:17:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_console_view', NULL, NULL, 'view', 1076, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'current_session', NULL, NULL, 'view', 1076, '当前会话', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'past_session', NULL, NULL, 'view', 1076, '历史会话', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'comment_message', NULL, NULL, 'view', 1076, '留言记录', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_create', NULL, NULL, 'button', 1076, '新建短信', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_ticket_create', NULL, NULL, 'button', 1076, '新建工单', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'manage_public_fast_response', NULL, NULL, 'button', 1076, '管理公共快捷回复', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'personal_fast_response', NULL, NULL, 'button', 1076, '个人快捷回复', NULL, 11, NULL, 1, 0, '2020-02-13 16:22:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_console', NULL, NULL, 'tab', 1075, '语音客服工作台', NULL, 11, NULL, 1, 0, '2020-02-13 17:03:20', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_console_view', NULL, NULL, 'view', 1085, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'reception', NULL, NULL, 'button', 1085, '接待', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'send_sms', NULL, NULL, 'button', 1085, '发送短信', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'regular_call', NULL, NULL, 'button', 1085, '普通外呼', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_ticket_create', NULL, NULL, 'button', 1085, '新建工单', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'forcasting_call', NULL, NULL, 'button', 1085, '预测试外呼', NULL, 11, NULL, 1, 0, '2020-02-13 17:10:02', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'data_statistics', NULL, NULL, 'menu', 1075, '数据统计', NULL, 11, NULL, 1, 0, '2020-02-13 17:26:44', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_data', NULL, NULL, 'tab', 1110, '文本坐席数据', NULL, 11, NULL, 1, 0, '2020-02-13 17:28:07', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_data_view', NULL, NULL, 'view', 1111, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 17:28:30', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_data_download', NULL, NULL, 'button', 1111, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 17:28:30', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_group_data', NULL, NULL, 'tab', 1110, '文本坐席组数据', NULL, 11, NULL, 1, 0, '2020-02-13 17:44:38', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_group_data_view', NULL, NULL, 'view', 1114, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 17:45:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_cs_group_data_download', NULL, NULL, 'button', 1114, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 17:45:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_data', NULL, NULL, 'tab', 1110, '语音坐席数据', NULL, 11, NULL, 1, 0, '2020-02-13 17:46:27', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_data_view', NULL, NULL, 'view', 1117, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 17:46:58', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_data_download', NULL, NULL, 'button', 1117, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 17:46:58', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_group_data', NULL, NULL, 'tab', 1110, '语音坐席组数据', NULL, 11, NULL, 1, 0, '2020-02-13 17:47:26', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_group_data_view', NULL, NULL, 'view', 1120, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 17:47:48', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_cs_group_data_download', NULL, NULL, 'button', 1120, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 17:47:48', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'data_statistics_myself', NULL, NULL, 'dataAuth', 1110, '我自己', NULL, 11, NULL, 1, 0, '2020-02-13 18:17:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'data_statistics_groups', NULL, NULL, 'dataAuth', 1110, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-13 18:17:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'data_statistics_company', NULL, NULL, 'dataAuth', 1110, '全公司', NULL, 11, NULL, 1, 0, '2020-02-13 18:17:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_records', NULL, NULL, 'menu', 1075, '联系历史', NULL, 11, NULL, 1, 0, '2020-02-13 18:18:43', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_records_myself', NULL, NULL, 'dataAuth', 1126, '我自己', NULL, 11, NULL, 1, 0, '2020-02-13 18:19:38', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_records_groups', NULL, NULL, 'dataAuth', 1126, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-13 18:19:38', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'contact_records_company', NULL, NULL, 'dataAuth', 1126, '全公司', NULL, 11, NULL, 1, 0, '2020-02-13 18:19:38', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_records', NULL, NULL, 'tab', 1126, '文本历史', NULL, 11, NULL, 1, 0, '2020-02-13 18:20:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_records_view', NULL, NULL, 'view', 1130, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 18:21:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_records_download', NULL, NULL, 'button', 1130, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 18:21:51', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_records_export', NULL, NULL, 'button', 1130, '导出记录', NULL, 11, NULL, 1, 0, '2020-02-13 18:21:51', 0, '2020-03-03 14:42:43');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_records_view_detail', NULL, NULL, 'view', 1130, '查看通话详情', NULL, 11, NULL, 1, 0, '2020-03-03 14:45:33', 0, '2020-03-03 14:45:33');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_records', NULL, NULL, 'tab', 1126, '语音历史', NULL, 11, NULL, 1, 0, '2020-02-13 18:22:30', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_records_view', NULL, NULL, 'view', 1135, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 18:23:10', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_records_download', NULL, NULL, 'button', 1135, '下载报表', NULL, 11, NULL, 1, 0, '2020-02-13 18:23:10', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_records_export', NULL, NULL, 'button', 1135, '导出记录', NULL, 11, NULL, 1, 0, '2020-02-13 18:23:10', 0, '2020-03-03 14:46:14');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_records_view_detail', NULL, NULL, 'view', 1135, '查看通话详情', NULL, 11, NULL, 1, 0, '2020-03-03 14:46:44', 0, '2020-03-03 14:46:44');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_management', NULL, NULL, 'menu', 1075, '坐席管理', NULL, 11, NULL, 1, 0, '2020-02-13 18:24:25', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff', NULL, NULL, 'tab', 1140, '人工坐席', NULL, 11, NULL, 1, 0, '2020-02-13 18:25:20', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_view', NULL, NULL, 'view', 1141, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 18:27:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_create_edit', NULL, NULL, 'button', 1141, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-13 18:27:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_enable_disable', NULL, NULL, 'button', 1141, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-13 18:27:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_delete', NULL, NULL, 'button', 1141, '删除', NULL, 11, NULL, 1, 0, '2020-02-13 18:27:12', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_group', NULL, NULL, 'tab', 1140, '坐席组', NULL, 11, NULL, 1, 0, '2020-02-13 18:27:37', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_group_view', NULL, NULL, 'view', 1146, '查看', NULL, 11, NULL, 1, 0, '2020-02-13 18:28:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_group_create_edit', NULL, NULL, 'button', 1146, '新增/编辑', NULL, 11, NULL, 1, 0, '2020-02-13 18:28:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_group_enable_disable', NULL, NULL, 'button', 1146, '停用/启用', NULL, 11, NULL, 1, 0, '2020-02-13 18:28:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_staff_group_delete', NULL, NULL, 'button', 1146, '删除', NULL, 11, NULL, 1, 0, '2020-02-13 18:28:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'settings', NULL, NULL, 'menu', 1075, '设置', NULL, 11, NULL, 1, 0, '2020-02-13 18:29:08', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'voice_setting', NULL, NULL, 'tab', 1151, '语音设置', NULL, 11, NULL, 1, 0, '2020-02-13 18:37:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'basic_voice_setting', NULL, NULL, 'button', 1152, '基础设置', NULL, 11, NULL, 1, 0, '2020-02-13 18:38:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'detail_voice_setting', NULL, NULL, 'button', 1152, '语音项设置', NULL, 11, NULL, 1, 0, '2020-02-13 18:38:24', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'text_setting', NULL, NULL, 'tab', 1151, '文本设置', NULL, 11, NULL, 1, 0, '2020-02-13 18:39:00', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'message_feedback', NULL, NULL, 'button', 1155, '留言反馈', NULL, 11, NULL, 1, 0, '2020-02-13 18:41:48', 0, '2020-03-02 11:03:20');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'access_methods', NULL, NULL, 'tab', 1155, '渠道接入', NULL, 11, NULL, 0, 0, '2020-02-13 18:41:48', 0, '2020-03-03 14:24:01');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'web_wap_h5', NULL, NULL, 'button', 1155, 'web/wap/h5', NULL, 11, NULL, 1, 0, '2020-02-13 18:43:08', 0, '2020-03-03 14:22:16');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'wechat_public', NULL, NULL, 'button', 1155, '微信公众号', NULL, 11, NULL, 1, 0, '2020-02-13 18:43:08', 0, '2020-03-03 14:22:19');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'wechat_miniapp', NULL, NULL, 'button', 1155, '微信小程序', NULL, 11, NULL, 1, 0, '2020-02-13 18:43:08', 0, '2020-03-03 14:22:21');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'others', NULL, NULL, 'menu', 1075, '其他', NULL, 11, NULL, 1, 0, '2020-02-13 18:43:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'hide_customer_phone_number', NULL, NULL, 'button', 1161, '隐藏客户号码', NULL, 11, NULL, 1, 0, '2020-02-13 18:44:14', 0, '2020-02-24 16:53:26');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center', NULL, NULL, 'menu', 0, '客户中心', NULL, 11, NULL, 1, 0, '2020-02-14 13:51:05', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers', NULL, NULL, 'menu', 1163, '我的客户', NULL, 11, NULL, 1, 0, '2020-02-14 13:56:32', 0, '2020-02-24 11:21:17');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_myself', NULL, NULL, 'dataAuth', 1165, '我自己', NULL, 11, NULL, 1, 0, '2020-02-14 13:57:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_groups', NULL, NULL, 'dataAuth', 1165, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-14 13:57:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_company', NULL, NULL, 'dataAuth', 1165, '全公司', NULL, 11, NULL, 1, 0, '2020-02-14 13:57:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_view', NULL, NULL, 'view', 1165, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-03-03 14:40:11');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_create_edit', NULL, NULL, 'button', 1165, '新建/编辑客户', NULL, 11, NULL, 1, 0, '2020-03-03 14:39:59', 0, '2020-03-03 14:40:15');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_import', NULL, NULL, 'button', 1165, '导入', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'my_customers_export', NULL, NULL, 'button', 1165, '导出', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'add_customer_to_callout_task', NULL, NULL, 'button', 1165, '添加到外呼任务', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'transfer_delete', NULL, NULL, 'button', 1165, '转移/删除', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'add_to_white_list', NULL, NULL, 'button', 1165, '加入黑名单', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'transfer_to_public_sea', NULL, NULL, 'button', 1165, '退回公海', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'view_record_detail', NULL, NULL, 'view', 1165, '查看通话详情', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'ai_quick_dial', NULL, NULL, 'button', 1165, 'ai快速拨打', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'manual_dial', NULL, NULL, 'button', 1165, '人工拨打', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_send_sms', NULL, NULL, 'button', 1165, '发送短信', NULL, 11, NULL, 1, 0, '2020-02-14 14:03:21', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_public_sea', NULL, NULL, 'tab', 1163, '客户公海', NULL, 11, NULL, 1, 0, '2020-02-14 14:09:25', 0, '2020-02-24 11:21:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_public_sea_view', NULL, NULL, 'view', 1191, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'view_entire_phone_number', NULL, NULL, 'view', 1191, '查看完整号码', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_import', NULL, NULL, 'button', 1191, '导入客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_export', NULL, NULL, 'button', 1191, '导出客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'create_edit_customer', NULL, NULL, 'button', 1191, '新建/编辑客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'add_customer_to_white_list', NULL, NULL, 'button', 1191, '加入黑名单', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'pick_customer', NULL, NULL, 'button', 1191, '领取客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-03-03 14:30:16');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'distribute_customer', NULL, NULL, 'button', 1191, '分配客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-03-03 14:30:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'delete_customer', NULL, NULL, 'button', 1191, '删除客户', NULL, 11, NULL, 1, 0, '2020-02-14 14:12:55', 0, '2020-03-03 14:30:28');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'white_list_management', NULL, NULL, 'tab', 1163, '黑名单管理', NULL, 11, NULL, 1, 0, '2020-02-14 14:13:34', 0, '2020-02-24 11:21:20');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'white_list_management_view', NULL, NULL, 'view', 1201, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:16:26', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'white_list_batch_import', NULL, NULL, 'button', 1201, '批量导入', NULL, 11, NULL, 1, 0, '2020-02-14 14:16:26', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'white_exit', NULL, NULL, 'button', 1201, '退出黑名单', NULL, 11, NULL, 1, 0, '2020-02-14 14:16:26', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_contact_records_myself', NULL, NULL, 'dataAuth', 1260, '我自己', NULL, 11, NULL, 1, 0, '2020-02-14 14:24:10', 0, '2020-03-04 15:42:00');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_contact_records_groups', NULL, NULL, 'dataAuth', 1260, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-14 14:24:10', 0, '2020-03-04 15:42:01');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_contact_records_company', NULL, NULL, 'dataAuth', 1260, '全公司', NULL, 11, NULL, 1, 0, '2020-02-14 14:24:10', 0, '2020-03-04 15:42:03');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callout_records', NULL, NULL, 'tab', 1260, '外呼历史', NULL, 11, NULL, 1, 0, '2020-02-14 14:25:21', 0, '2020-03-04 15:42:02');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callout_record_view', NULL, NULL, 'view', 1209, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:26:19', 0, '2020-02-25 10:53:26');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callout_record_detail_export', NULL, NULL, 'button', 1209, '导出通话记录', NULL, 11, NULL, 1, 0, '2020-02-14 14:27:17', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callout_record_file_download', NULL, NULL, 'button', 1209, '下载通话录音', NULL, 11, NULL, 1, 0, '2020-02-14 14:27:17', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callin_records', NULL, NULL, 'tab', 1260, '呼入历史', NULL, 11, NULL, 1, 0, '2020-02-14 14:27:40', 0, '2020-03-04 15:42:06');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callin_record_view', NULL, NULL, 'view', 1213, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:28:41', 0, '2020-02-25 10:53:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callin_record_detail_export', NULL, NULL, 'button', 1213, '导出通话记录', NULL, 11, NULL, 1, 0, '2020-02-14 14:28:41', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'callin_record_file_download', NULL, NULL, 'button', 1213, '下载通话录音', NULL, 11, NULL, 1, 0, '2020-02-14 14:28:41', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'custom_properties', NULL, NULL, 'menu', 1163, '自定义属性', NULL, 11, NULL, 1, 0, '2020-02-14 14:30:40', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_custom_properties', NULL, NULL, 'view', 1217, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:32:46', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'create_edit_property', NULL, NULL, 'button', 1217, '新增/编辑字段', NULL, 11, NULL, 1, 0, '2020-02-14 14:32:46', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'delete_property', NULL, NULL, 'button', 1217, '删除字段', NULL, 11, NULL, 1, 0, '2020-02-14 14:32:46', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_center', NULL, NULL, 'menu', 1163, '短信中心', NULL, 11, NULL, 1, 0, '2020-02-14 14:52:35', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_platform', NULL, NULL, 'tab', 1224, '短信平台', NULL, 11, NULL, 1, 0, '2020-02-14 14:53:07', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_platform_view', NULL, NULL, 'view', 1225, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:53:39', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_template', NULL, NULL, 'tab', 1224, '短信模板', NULL, 11, NULL, 1, 0, '2020-02-14 14:55:23', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_template_view', NULL, NULL, 'view', 1227, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:55:36', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_signature', NULL, NULL, 'tab', 1224, '短信签名', NULL, 11, NULL, 1, 0, '2020-02-14 14:55:42', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_signature_view', NULL, NULL, 'view', 1229, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:55:55', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_records', NULL, NULL, 'tab', 1224, '发送历史', NULL, 11, NULL, 1, 0, '2020-02-14 14:56:03', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'sms_records_view', NULL, NULL, 'view', 1231, '查看', NULL, 11, NULL, 1, 0, '2020-02-14 14:56:13', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_others', NULL, NULL, 'menu', 1163, '其他', NULL, 11, NULL, 1, 0, '2020-02-14 14:56:44', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_hide_phone_number', NULL, NULL, 'button', 1233, '隐藏客户号码', NULL, 11, NULL, 1, 0, '2020-02-14 14:57:23', 0, '2020-02-16 13:50:54');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_system_status', NULL, NULL, 'button', 1791, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 14:54:31', 0, '2020-02-21 15:01:37');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_system_status', NULL, NULL, 'button', 1895, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 14:55:23', 0, '2020-02-21 15:01:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_system_status', NULL, NULL, 'button', 1977, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 14:55:54', 0, '2020-02-21 15:01:39');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_system_status', NULL, NULL, 'button', 11032, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 14:56:32', 0, '2020-02-21 15:01:23');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'business_center_system_status', NULL, NULL, 'button', 1056, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 15:03:41', 0, '2020-02-21 15:03:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'cs_console_system_status', NULL, NULL, 'button', 1075, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 15:03:41', 0, '2020-02-21 15:03:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_system_status', NULL, NULL, 'button', 1163, '系统状态', NULL, 11, NULL, 1, 0, '2020-02-21 15:03:41', 0, '2020-02-21 15:03:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'system_announcement_view', NULL, NULL, 'view', 1052, '系统公告', NULL, 11, NULL, 1, 0, '2020-02-21 16:41:04', 0, '2020-02-24 13:56:58');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBill_view', NULL, NULL, 'view', 803, '查看', NULL, 11, NULL, 1, 0, '2020-02-24 14:12:24', 0, '2020-02-24 14:12:29');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsBill_view', NULL, NULL, 'view', 907, '查看', NULL, 11, NULL, 1, 0, '2020-02-24 14:13:12', 0, '2020-02-24 14:13:12');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_center_view', NULL, NULL, 'view', 1034, '查看', NULL, 11, NULL, 1, 0, '2020-02-24 14:14:38', 0, '2020-02-24 14:14:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_data_permission_my', NULL, NULL, 'dataAuth', 902, '我自己', NULL, 11, NULL, 1, 0, '2020-02-26 10:58:13', 0, '2020-02-26 10:58:13');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_data_permission_organization', NULL, NULL, 'dataAuth', 902, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-26 10:58:44', 0, '2020-02-26 10:58:44');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_dialogflow_design_2_data_permission_company', NULL, NULL, 'dataAuth', 902, '全公司', NULL, 11, NULL, 1, 0, '2020-02-26 10:58:56', 0, '2020-02-26 10:59:09');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_robot', NULL, NULL, 'view', 799, '机器判定', NULL, 11, NULL, 1, 0, '2020-02-26 12:27:48', 0, '2020-02-26 12:27:56');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_cu', NULL, NULL, 'view', 799, '人工巡检', NULL, 11, NULL, 1, 0, '2020-02-26 12:28:16', 0, '2020-02-26 12:28:22');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'call_records', NULL, NULL, 'tab', 1163, '通话历史', NULL, 11, NULL, 1, 0, '2020-03-04 15:41:55', 0, '2020-03-04 16:09:22');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records', NULL, NULL, 'tab', 1163, '文本历史', NULL, 11, NULL, 1, 0, '2020-03-04 15:57:09', 0, '2020-03-04 16:09:25');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_view', NULL, NULL, 'button', 1262, '查看', NULL, 11, NULL, 1, 0, '2020-03-04 15:57:43', 0, '2020-03-04 15:57:46');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_export', NULL, NULL, 'button', 1262, '导出会话记录', NULL, 11, NULL, 1, 0, '2020-03-04 15:58:16', 0, '2020-03-04 15:59:07');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_down', NULL, NULL, 'button', 1262, '下载会话记录', NULL, 11, NULL, 1, 0, '2020-03-04 15:58:47', 0, '2020-03-04 15:59:19');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_detail', NULL, NULL, 'button', 1262, '查看会话详情', NULL, 11, NULL, 1, 0, '2020-03-04 15:59:24', 0, '2020-03-04 15:59:55');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_dataAuth_my', NULL, NULL, 'dataAuth', 1262, '我自己', NULL, 11, NULL, 1, 0, '2020-03-04 16:00:31', 0, '2020-03-04 16:00:31');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_dataAuth_groups', NULL, NULL, 'dataAuth', 1262, '所在组以及下级组', NULL, 11, NULL, 1, 0, '2020-03-04 16:00:41', 0, '2020-03-04 16:01:03');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'customer_center_text_records_dataAuth_company', NULL, NULL, 'dataAuth', 1262, '全公司', NULL, 11, NULL, 1, 0, '2020-03-04 16:01:27', 0, '2020-03-04 16:01:27');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform', NULL, NULL, 'view', 0, '智能外呼平台', NULL, 11, NULL, 1, 0, '2020-02-11 14:30:43', 0, '2020-02-21 14:47:38');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_view_robot', NULL, NULL, 'view', 1252, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:40:47', 0, '2020-02-26 12:28:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_view_cu', NULL, NULL, 'button', 1253, '查看', NULL, 11, NULL, 1, 0, '2020-02-11 16:40:47', 0, '2020-02-26 12:28:49');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_robot_export', NULL, NULL, 'button', 1252, '数据导出', NULL, 11, NULL, 1, 0, '2020-02-11 16:40:47', 0, '2020-02-26 12:29:19');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_cu_export', NULL, NULL, 'button', 1253, '数据导出', NULL, 11, NULL, 1, 0, '2020-02-11 16:40:47', 0, '2020-02-26 12:29:30');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_intent_learning_data_permission_company', NULL, NULL, 'dataAuth', 799, '全公司', NULL, 11, NULL, 1, 0, '2020-02-11 16:41:16', 0, '2020-02-24 14:10:09');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBill_view_bill', NULL, NULL, 'view', 803, '查看话单计费', NULL, 11, NULL, 1, 0, '2020-02-11 16:51:41', 0, '2020-02-24 14:12:34');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBill_view_detail', NULL, NULL, 'view', 803, '查看话单详情', NULL, 11, NULL, 1, 0, '2020-02-11 16:51:41', 0, '2020-02-24 14:12:35');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_out_call_platform_statisticsBill_view_sms', NULL, NULL, 'view', 803, '查看短信费用', NULL, 11, NULL, 1, 0, '2020-02-11 16:51:41', 0, '2020-02-24 14:12:36');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp', NULL, NULL, 'view', 0, '智能呼入接待', NULL, 11, NULL, 1, 0, '2020-02-12 10:00:30', 0, '2020-02-21 14:47:40');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsBill_view_bill', NULL, NULL, 'button', 907, '查看话单计费', NULL, 11, NULL, 1, 0, '2020-02-12 14:09:19', 0, '2020-02-24 14:13:15');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_call_in_recp_statisticsBill_view_detail', NULL, NULL, 'button', 907, '查看话单详情', NULL, 11, NULL, 1, 0, '2020-02-12 14:09:19', 0, '2020-02-24 14:13:25');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform', NULL, NULL, 'view', 0, '智能质检', NULL, 11, NULL, 1, 0, '2020-02-12 15:15:49', 0, '2020-02-21 14:47:42');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_other', NULL, NULL, 'view', 1977, '其他', NULL, 11, NULL, 1, 0, '2020-02-12 15:18:29', 0, '2020-02-25 11:41:24');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_wordCount_data_permission_organization', NULL, NULL, 'dataAuth', 1011, '所在组及下级组', NULL, 11, NULL, 1, 0, '2020-02-12 15:49:16', 0, '2020-02-24 16:35:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_qc_platform_data_analysis_wordCount_data_permission_company', NULL, NULL, 'dataAuth', 1011, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 15:49:16', 0, '2020-02-24 16:35:44');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform', NULL, NULL, 'view', 0, '工单系统', NULL, 11, NULL, 1, 0, '2020-02-12 17:20:22', 0, '2020-02-21 14:47:46');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_center_add', NULL, NULL, 'view', 1034, '新建工单', NULL, 11, NULL, 1, 0, '2020-02-12 17:30:49', 0, '2020-02-24 14:14:40');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_center_data_permission_my', NULL, NULL, 'dataAuth', 1034, '我自己', NULL, 11, NULL, 1, 0, '2020-02-12 17:31:38', 0, '2020-02-24 14:14:41');
INSERT INTO `auth_resource`(`resource_uri`, `page_url`, `resource_icon`, `resource_type`, `resource_parent_id`, `resource_name`, `same_as`, `system_type`, `auth_style`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES ( 'crm_work_platform_center_data_permission_company', NULL, NULL, 'dataAuth', 1034, '全公司', NULL, 11, NULL, 1, 0, '2020-02-12 17:31:38', 0, '2020-02-24 14:14:45');
