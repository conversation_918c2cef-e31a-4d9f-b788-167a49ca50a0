ALTER TABLE dialog_flow_info ADD COLUMN `model_type` varchar(20) NULL COMMENT '算法模型种类';
alter table privacy_number_binding add expire_time_b timestamp null after expire_time;
alter table privacy_number_binding add extend_info varchar(1024) null comment 'openapi扩展字段' after expire_time_b;

alter table privacy_number_contact_history add audio_status tinyint default 0 after call_recording;
alter table privacy_number_contact_history add internalRecordingUrl varchar(512) default 0 after call_recording;

update privacy_number_binding set number_b = '', expire_time_b = null where 1 = 1;
