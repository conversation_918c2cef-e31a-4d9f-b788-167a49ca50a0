# 区分 crm boss
alter table knowledge_base_info
    add distributor_id bigint default 0 null comment '代理商id';
alter table knowledge_base_info alter column tenant_id set default 0;

alter table knowledge_base_info
    add record_user_id bigint null comment '分配录音师id';

create index idx_record_user_id
    on knowledge_base_info (record_user_id);
# 通话记录 性别
# alter table call_record algorithm=inplace, lock=shared,
#                         add gender tinyint default 0 null comment '性别 0 未知 1 男 2 女' after emotion;
# alter table call_in_record
#     add gender tinyint default 0 null comment '性别 0 未识别 1 男 2 女' after emotion;

# detail 表旧数据枚举修改
# update call_detail set emotion = 0 where emotion = -1;
# update call_in_detail set emotion = 0 where emotion = -1;
# update cs_detail set emotion = 0 where emotion = -1;
# update qc_job_record_detail set emotion = 0 where emotion = -1;

# 备注修改，都要改
# alter table call_detail modify emotion tinyint null comment '0 未识别 1 开心 2 生气';
# alter table call_in_detail modify emotion tinyint null comment '0 未识别 1 开心 2 生气';
# alter table cs_detail modify emotion tinyint null comment '0 未识别 1 开心 2 生气';
# alter table qc_job_record_detail modify emotion tinyint null comment '0 未识别 1 开心 2 生气';