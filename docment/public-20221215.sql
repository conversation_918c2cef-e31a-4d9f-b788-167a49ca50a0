# 短信模板链接
alter table sms_template add grow_customer_apps varchar(256) null after serving_status;

# 任务被动加微数
update call_job_stats_user_set set passive_add_wechat_count = 0 where passive_add_wechat_count is null;
alter table call_job_stats_user_set modify passive_add_wechat_count bigint default 0 not null comment '被动加微数';
alter table call_job_stats_user_set add url_click_count bigint default 0 not null comment '链接点击数';
# 表头改造
INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (35, '/apiOpe/opensipsAcc/list', 1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('开始时间',35,'time',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('日志ID(FSX-cid)',35,'callid',2);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('Sip Call ID',35,'sipCode',3);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('错误码(Sip Code)',35,'sipCode',4);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('Sip Reason',35,'sipReason',5);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('准备时长(s)',35,'setuptime',6);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('话单通话时长(s)',35,'duration',7);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('主叫',35,'caller',8);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('被叫',35,'callee',9);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('真实被叫',35,'realCallee',10);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('是否匹配静态黑名单',35,'isSBlacklist',11);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('黑名单主叫',35,'blacklistCaller',12);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('是否匹配动态黑名单',35,'isDBlacklist',13);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('落地网关ID',35,'gwReqAddr',14);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('落地网关请求地址',35,'gwReqAddr',15);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('落地网关主叫',35,'gwCaller',16);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('计费状态',35,'billStatus',17);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('计费时长(s)',35,'billDuration',18);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('通话费用(元)',35,'billFee',19);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('供应商编号',35,'supplierId',20);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('计费周期',35,'billPeriod',21);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('计费费率(元)',35,'billRate',22);

INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (36, '/apiOpe/mainBrand/list', 1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('主公司编号',36,'mainBrandCode',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('主公司名称',36,'mainBrandName',2);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('联系人',36,'contactName',3);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('联系电话',36,'contactPhone',4);

INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (37, '/apiOpe/dialogFlow/exportIntentBranch/corpus', 1);
INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (38, '/apiOpe/dialogFlow/exportIntentBranch/property', 1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('分支名称',37,'name',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('问法',37,'question',2);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('关键词',37,'keyword',3);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('分支名称',38,'name',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('分支属性',38,'category',2);

INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (39, '/apiOpe/dialogFlowBilling/list', 1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('时间',39,'createTime',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('类型',39,'dialogFlowBillingType',2);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('话术名称',39,'dialogFlowName',3);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('话术id',39,'dialogFlowId',4);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('数量',39,'number',5);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('成本单价',39,'unitCost',6);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('实际单价',39,'transactionUnitCost',7);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('金额（元）',39,'totalAmount',8);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('扣费路径',39,'dialogFlowBillingAccount',9);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('客户名',39,'tenantName',10);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('操作人',39,'createUserName',11);

delete from header_info where table_id = 7 and header_name in('合同编号','累计回款(元)','合同总金额(元)');

delete from header_info where table_id = 10 and header_name in('合同编号','累计回款(元)','合同总金额(元)');

delete from header_info where table_id = 14 and header_name = '支持IVR导航';

INSERT INTO ai_call_engine.table_info (table_id, table_url, platform_type) VALUES (40, '/apiOpe/privacyNumber/query', 1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('隐私号码',40,'number',1);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('所属运营商',40,'privacyNumberOperationName',2);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('导入时间',40,'createTime',3);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('号码归属地',40,'location',4);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('绑定情况',40,'binding',5);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('转接号设置情况',40,'transfer',6);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('归属客户',40,'tenantNames',7);
insert into header_info (header_name, table_id, field_name, order_by) VALUES ('备注',40,'remark',8);
