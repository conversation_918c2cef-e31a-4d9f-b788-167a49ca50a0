insert into auth_resource (resource_uri, resource_type, resource_parent_id, resource_name, system_type) value ('call_floating_ball', 'view', 1085, '外呼悬浮球', 11);
alter table customer_person drop key uni_tenant_wechat_phone;
insert into role_authority(role_id, authority_id) select role_id, (select auth_resource_id from auth_resource where resource_uri = 'call_floating_ball')
from role where system_type = 2 and type != 255 and type in (0, 4, 7);
alter table customer_person drop key uni_tenant_wechat_phone;
alter table dialog_flow_info add column mixed_model tinyint(1) comment '是否开启自定义/领域混合模型';
CREATE TABLE `call_cost`
(
    `call_cost_id`        bigint(20)                                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `tenant_id`           bigint(20)                                                    NOT NULL COMMENT '租户id',
    `called_phone_number` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '被叫号码',
    `start_time`          datetime                                                      NOT NULL COMMENT '通话开始时间',
    `local`               tinyint(4)                                                    null comment '外呼本地or外地',
    `chat_duration`       bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '对话时间',
    `call_unit_cost`      integer(20)                                                    NOT NULL DEFAULT 0 COMMENT 'AI通话单价，单位厘',
    `call_cost`           bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '通话费用，单位厘',
    `sms_count`           tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '短信条数',
    `sms_cost`            bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '短信费用，单位厘',
    `mms_count`           tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '彩信条数',
    `mms_cost`            bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '彩信费用，单位厘',
    `fms_count`           tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '闪信条数',
    `fms_cost`            bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '闪信费用，单位厘',
    `free_sms_count`      tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '免费短信条数',
    `total_cost`          bigint(20)                                                    NOT NULL DEFAULT 0 COMMENT '总费用，单位厘',
    `call_cost_type`      tinyint(2)                                                    NULL     DEFAULT 0 COMMENT '计费类型 0-AI坐席  1-人工外呼',
    `call_record_id`      bigint(20)                                                    NOT NULL COMMENT '通话记录id',
    `create_time`         timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `update_time`         timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`call_cost_id`),
    KEY `idx_tenant` (`tenant_id`) USING BTREE,
    KEY `idx_call_record_tenant` (`call_record_id`, `tenant_id`) USING BTREE,
    KEY `idx_tenant_start` (`tenant_id`, `start_time`) USING BTREE,
    KEY `idx_tenant_phone` (`tenant_id`, `called_phone_number`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='流水信息表'
  ROW_FORMAT = DYNAMIC;
alter table `robot_call_job`
add column `add_wechat_type` int(11) DEFAULT NULL COMMENT '加微账号类型';