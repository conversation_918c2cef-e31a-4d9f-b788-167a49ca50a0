-- 修改编码并检查
ALTER TABLE robot_call_job MODIFY wechat_cp_add_friend_message varchar(256) CHARACTER SET utf8mb4;
ALTER TABLE robot_call_job MODIFY aike_wechat_list text CHARACTER SET utf8mb4;
ALTER TABLE robot_call_job MODIFY add_wechat_config text CHARACTER SET utf8mb4;

SELECT column_name, character_set_name
FROM information_schema.`COLUMNS`
WHERE TABLE_SCHEMA = 'ai_call_engine'
  and TABLE_NAME = 'robot_call_job'
  AND COLUMN_NAME in ('wechat_cp_add_friend_message', 'aike_wechat_list', 'add_wechat_config');

ALTER TABLE wechat_cp_add_friend CONVERT TO CHARACTER SET 'utf8mb4';

SELECT column_name, character_set_name
FROM information_schema.`COLUMNS`
WHERE TABLE_SCHEMA = 'ai_call_engine_sync'
  and TABLE_NAME = 'wechat_cp_add_friend'
  AND COLUMN_NAME in ('fromwx_name', 'hello_msg');

-- 新增字段和索引,线上建新表迁数据
alter table robot_call_task add `add_wechat_friend_account_name` varchar(128) character set utf8mb4 COLLATE utf8mb4_unicode_ci null comment '加微账号名称';
alter table robot_call_task add `add_wechat_friend_status` tinyint null comment '加微状态';
alter table robot_call_task add `add_wechat_friend_send_time` timestamp null comment '加微申请时间';
alter table robot_call_task add `add_wechat_friend_adopt_time` timestamp null comment '加微通过时间';

-- 新增字段,直接执行
alter table robot_call_job add function_version int default 0 null comment '功能版本';

CREATE TABLE `short_url` (
                             `id` bigint(20) NOT NULL AUTO_INCREMENT,
                             `long_url` varchar(255) DEFAULT NULL COMMENT '长地址',
                             `short_url` varchar(10) DEFAULT NULL COMMENT '短地址',
                             `collision_time` tinyint(4) DEFAULT '0' COMMENT '冲突次数',
                             `extra_info` json DEFAULT NULL COMMENT '业务信息',
                             `create_user_id` bigint(20) NOT NULL DEFAULT '0',
                             `update_user_id` bigint(20) NOT NULL DEFAULT '0',
                             `create_time` datetime NULL COMMENT '创建时间',
                             `update_time` datetime NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             UNIQUE INDEX `idx_short_url`(`short_url`) USING BTREE COMMENT '短链索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE sms_template ADD COLUMN short_url varchar(10) COMMENT '短地址';

ALTER TABLE intent_message ADD INDEX `idx_template_status`(sms_template_id, create_time, send_status, report_status);

ALTER TABLE sms_job_message ADD INDEX `idx_template_status`(sms_template_id, create_time, send_status, report_status);


