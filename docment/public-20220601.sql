create table call_stat_user_set
(
    call_stat_user_set_id bigint auto_increment comment '通话统计设置ID'
        primary key,
    user_id               bigint        null comment '用户id',
    user_intent_set       json          null comment '意向字段列表',
    stage_count           int default 1 null comment '流程数'
);

create index call_stat_user_set_user_index
    on call_stat_user_set (user_id);


alter table user_variable_audio
    add volume bigint null comment '音量';
db.getCollection("defaultRobotKnowledgeRepo").updateMany(
    {title:'听不清楚AI说话',type:'GENERAL'},
    {$set: {"type": 'DEFAULT'}}
);

db.getCollection("robotKnowledge").updateMany(
    {title:"听不清楚AI说话",type:"GENERAL"},
    {$set: {"type": "DEFAULT"}},
);
alter table tenant add extension_fare bigint default 0 not null comment '分机号外呼单价' after support_extension_mode;

alter table call_cost add ai_fare bigint default 0 not null comment '单价';
alter table call_cost add com_fare bigint default 0 not null comment '单价';
alter table call_cost add extension_fare bigint default 0 not null comment '振铃单价';
alter table call_cost add bill_ring_duration bigint default 0 not null comment '振铃计费时长';
alter table call_cost add ring_duration bigint default 0 not null comment '振铃时长';
alter table call_cost add ring_cost bigint default 0 not null comment '振铃费用';

alter table user_variable_audio
    add volume bigint null comment '音量';

create table call_stat_user_set
(
    call_stat_user_set_id bigint auto_increment comment '通话统计设置ID'
        primary key,
    user_id               bigint        null comment '用户id',
    user_intent_set       json          null comment '意向字段列表',
    stage_count           int default 1 null comment '流程数'
);

create index call_stat_user_set_user_index
    on call_stat_user_set (user_id);

alter table cost_list add bill_chat_duration bigint default 0 not null after `count`;
alter table cost_list add ring_duration bigint default 0 not null after fare;
alter table cost_list add bill_ring_duration bigint default 0 not null after ring_duration;
alter table cost_list add call_out_model bigint default 0 not null;
create index idx_tenant_model on cost_list (tenant_id, call_out_model);

alter table call_record add call_out_model tinyint default 0 not null comment '外呼模式,是否分机号外呼';
create index idx_tenant_type_model on call_record (tenant_id, call_record_type, call_out_model);
