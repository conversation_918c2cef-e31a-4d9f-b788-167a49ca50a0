alter table distributor add hide_jump_to_aicc_record tinyint default 0 not null comment '0不隐藏,1隐藏';
CREATE TABLE `callback_record_log` (
                                       `callback_record_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
                                       `callback_type` tinyint(4) DEFAULT NULL COMMENT '回调类型',
                                       `callback_time` datetime NOT NULL COMMENT '回调时间',
                                       `state` tinyint(4) DEFAULT NULL COMMENT '回调状态',
                                       `try_times` tinyint(4) NOT NULL DEFAULT '0' COMMENT '已重试次数',
                                       `response_detail` varchar(4096) DEFAULT NULL COMMENT '回调isv的返回内容',
                                       `request_time` int(10) NOT NULL DEFAULT '0' COMMENT '回调用时',
                                       `ref_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联Id',
                                       `post_body` text CHARACTER SET utf8mb4 COMMENT '请求明文',
                                       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                       `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`callback_record_log_id`),
                                       KEY `index_ref` (`ref_id`) USING HASH,
                                       KEY `index_tenant_callback_time` (`tenant_id`,`callback_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='回调记录历史表';


CREATE TABLE `isv_auto_callback` (
                                     `isv_auto_callback_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
                                     `next_callback_time` datetime NOT NULL COMMENT '下次回调时间',
                                     `try_times` tinyint(4) NOT NULL DEFAULT '0' COMMENT '已重试次数',
                                     `post_body` text CHARACTER SET utf8mb4,
                                     `url_param` varchar(1024) NOT NULL DEFAULT '{}' COMMENT 'url上的参数，map格式',
                                     `last_response` varchar(4096) DEFAULT NULL COMMENT '最后一次回调isv的返回内容',
                                     `ref_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联ID',
                                     `callback_type` tinyint(4) DEFAULT NULL COMMENT '回调类型',
                                     `callback_record_log_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '回调记录id',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`isv_auto_callback_id`),
                                     KEY `idx_try_times_next_callback_time` (`try_times`,`next_callback_time`),
                                     KEY `idx_callback_type_ref_id_next_time` (`callback_type`,`ref_id`,`next_callback_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='isv自动回调表';


alter table isv_info ADD COLUMN `token` varchar(200) NOT NULL COMMENT '加签的token';

alter table isv_info ADD COLUMN `encoding_aes_key` varchar(200) NOT NULL COMMENT 'AESKey消息加密';

alter table isv_info ADD COLUMN `event_subscription_url` varchar(1024) DEFAULT NULL COMMENT '事件订阅地址';

alter table isv_info ADD COLUMN `event_subscription_types` varchar(200) NOT NULL DEFAULT '[]' COMMENT '事件订阅列表';

INSERT INTO `ai_call_engine`.`table_info`(`table_url`, `platform_type`) VALUES ('/apiEngine/callbackRecord/export', 1 );

INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '回调ID', table_id , 'callbackRecordLogId', 1 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '回调类型', table_id , 'callbackType', 2 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '回调时间', table_id , 'callbackTime', 3 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '回调状态', table_id , 'state', 4 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '重试次数', table_id , 'tryTimes', 5 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '返回详情', table_id , 'responseDetail', 6 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '回调用时', table_id , 'requestTime', 7 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;
INSERT INTO `ai_call_engine`.`header_info`( `header_name`, `table_id`, `field_name`, `order_by`) select '关联Id', table_id , 'refId', 8 from table_info where table_url='/apiEngine/callbackRecord/export' and platform_type = 1;

INSERT INTO `auth_resource`( `resource_uri`, `resource_type`, `resource_parent_id`, `resource_name`, `system_type`, `enabled_status`, `create_user_id`, `create_time`, `update_user_id`, `update_time`)
select  'interface_management_edit','button',  auth_resource_id , '编辑', 11, 1, 0, NOW(), 0, NOW()
from auth_resource where resource_name like '开放平台' limit 1 ;

insert into role_authority (role_id, authority_id)
select (SELECT role_id FROM role where type != 255 and system_type = 2 and name like '管理员'), auth_resource_id
from auth_resource
where resource_uri in ('interface_management_edit');

insert into role_authority (role_id, authority_id)
select (SELECT role_id FROM role where type != 255 and system_type = 2 and name like '超级管理员'), auth_resource_id
from auth_resource
where resource_uri in ('interface_management_edit');
