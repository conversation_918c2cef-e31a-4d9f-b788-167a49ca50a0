## 孙赛
ALTER TABLE user_variable_item ADD COLUMN user_variable_content varchar(256) NULL DEFAULT NULL COMMENT '自定义变量话术内容';
## 孙赛
create table robot_call_job_folder
(
    robot_call_job_folder_id bigint auto_increment primary key,
    tenant_id bigint default 0 not null,
    name varchar(64) default '' not null,
    create_user_id bigint default 0 not null,
    update_user_id bigint default 0 not null,
    create_time timestamp default current_timestamp not null,
    update_time timestamp default current_timestamp on update current_timestamp not null
);
alter table robot_call_job add folder_id bigint null after description;


alter table sms_channel add sms_type tinyint default 0 not null comment '0短信,1彩信,2闪信' after enabled_status;