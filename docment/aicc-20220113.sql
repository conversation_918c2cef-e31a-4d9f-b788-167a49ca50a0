insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_filter_strategy', 'menu', (select a.auth_resource_id from auth_resource a where a.resource_name ='AI外呼'), '过滤策略', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_filter_strategy_view', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_filter_strategy'), '查看', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_filter_strategy_add_update', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_filter_strategy'), '新建/编辑', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_filter_strategy_del', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_filter_strategy'), '删除', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_system_filter', 'menu', (select a.auth_resource_id from auth_resource a where a.resource_name ='AI外呼'), '系统过滤', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_system_filter_view', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_system_filter'), '查看', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_blacklist', 'menu', (select a.auth_resource_id from auth_resource a where a.resource_name ='AI外呼'), '黑名单管理', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_blacklist_view', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_blacklist'), '查看', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_blacklist_batch_import', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_blacklist'), '批量导入', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_blacklist_del', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_blacklist'), '退出黑名单', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_intercept', 'menu', (select a.auth_resource_id from auth_resource a where a.resource_name ='AI外呼'), '外呼拦截', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_intercept_view', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_intercept'), '查看', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_intercept_add_update', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_intercept'), '新建/编辑', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_intercept_del', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_intercept'), '删除', 11);

insert into auth_resource(resource_uri,  resource_type, resource_parent_id, resource_name, system_type)
VALUES ('aicc_callout_intercept_default', 'view', (select a.auth_resource_id from auth_resource a where a.resource_uri ='aicc_callout_intercept'), '设为默认', 11);

update auth_resource set resource_name='黑名单管理' where resource_name='呼叫白名单';

update auth_resource set resource_name='退出黑名单' where resource_name='退出白名单';

select r.role_id, r.name from role r where r.type in (0,1,4,5,7,8,9,10,11) and system_type = 2;

insert into role_authority (role_id, authority_id)  select 1, ar.auth_resource_id from auth_resource ar
where (ar.resource_uri like 'aicc_filter_strategy%' or ar.resource_uri like 'aicc_system_filter%' or ar.resource_uri like 'aicc_callout_intercept%' or ar.resource_uri like 'aicc_callout_blacklist%');

alter table filtered_robot_call_task
    add last_filter tinyint default 1 null comment '是否此task最后一次过滤';

alter table filtered_robot_call_task
    add called tinyint default 0 null comment '此task是否外呼过';
>>>>>>>>> Temporary merge branch 2
