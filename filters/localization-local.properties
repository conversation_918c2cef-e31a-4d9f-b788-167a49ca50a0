#### \u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E ####
db.master.url=*****************************************************************************************************************************************************************************************************************************************
db.master.username=ai_call_engine
db.master.password=yiwise.tanyi
db.slave.one.url=*****************************************************************************************************************************************************************************************************************************************
db.slave.one.username=ai_call_engine
db.slave.one.password=yiwise.tanyi
db.driver=com.mysql.jdbc.Driver
db.initialSize=5
db.maxActive=20
db.minIdle=5

#### spring\u914D\u7F6E ####
spring.profiles.active=localization
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchqueue.port=8140
server.callin.port=8150
server.smsjob.port=8160
server.markapi.port=8170

apollo.bootstrap.enabled=false

#### localization params
network.enabled=true
sms.enabled=true
wx.enabled=true
freeswitch.url=training.yiwise.com
freeswitch.port=7443
import.template.url=https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/common/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx

#### redis\u76F8\u5173\u914D\u7F6E ####
redis.hostname=*************
redis.port=6379
redis.password=yiwise.tanyi
redis.dbIndex=0

#### storage type
object.storage.type=fastdfs

#### oss\u76F8\u5173\u914D\u7F6E ####
oss.basicUrl=oss-cn-hangzhou.aliyuncs.com
oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
oss.accessId=LTAICuX7MOmHW6Oy
oss.accessKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
oss.defaultBucket=ai-call-platform-daily
oss.useOssAuth=true

#### minio\u914D\u7F6E ####
minio.endpoint=http://127.0.0.1:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.defaultBucket=ai-call-platform

#### fastDFS ####
fastDFS.tracker_server=*************:22122

yiwise.tts.ip=*************:8888

#### LogHub\u76F8\u5173\u914D\u7F6E ####
logHub.endpoint=http://cn-hangzhou.log.aliyuncs.com
logHub.accessId=LTAICuX7MOmHW6Oy
logHub.accessKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
logHub.projectName=ai-call-platform-daily
logHub.logstore=ai-call-platform


#### \u6BCF\u6761\u77ED\u4FE1\u7684\u8D39\u7528(\u5355\u4F4D\u5398) ####
sms.single.cost=10

#### \u963F\u91CC\u4E91mq ####
aliyun.ons.rocketmq.accessKey=LTAICuX7MOmHW6Oy
aliyun.ons.rocketmq.secretKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.ons.rocketmq.topic=daily
aliyun.ons.rocketmq.producer.ONSAddr=http://onsaddr-internet.aliyun.com/rocketmq/nsaddr4client-internet
aliyun.ons.rocketmq.producerId=PID_DAILY
aliyun.ons.rocketmq.consumerId=CID_DAILY
aliyun.ons.rocketmq.callback.topic=daily_callback
aliyun.ons.rocketmq.callback.producerId=PID_DAILY_CALLBACK
aliyun.ons.rocketmq.callback.consumerId=CID_DAILY_CALLBACK
aliyun.ons.rocketmq.wechatJob.topic=daily_wechat
aliyun.ons.rocketmq.wechatJob.groupId=GID_DAILY_WECHAT_JOB
aliyun.ons.rocketmq.callEventLog.topic=daily_callEventLog
aliyun.ons.rocketmq.callEventLog.groupId=GID_DAILY_CALL_EVENT_LOG
aliyun.ons.rocketmq.callEventLog.interruptAnalysis.groupId=GID_DAILY_INTERRUPT_ANALYSIS
aliyun.ons.rocketmq.intentRuleMatchFlag.topic=daily_intentRuleMatchFlag
aliyun.ons.rocketmq.intentRuleMatchFlag.groupId=GID_DAILY_INTENT_RULE_MATCH
aliyun.ons.rocketmq.dialogStats.topic=daily_dialogStats
aliyun.ons.rocketmq.dialogStats.groupId=GID_DAILY_DIALOG_STATS
aliyun.ons.rocketmq.predictLog.topic=daily_predictLog
aliyun.ons.rocketmq.predictLog.groupId=GID_DAILY_PREDICT_LOG
aliyun.ons.rocketmq.returnVisit.topic=daily_return_visit
aliyun.ons.rocketmq.returnVisit.groupId=GID_DAILY_RETURN_VISIT

aliyun.ons.rocketmq.crowd.push.callback.topic=daily_customer_aicc_crowd_push_callback
aliyun.ons.rocketmq.crowd.push.callback.groupId=GID_DAILY_CUSTOMER_AICC_CROWD_PUSH_CALLBACK

aliyun.ons.rocketmq.robot.account.analysis.topic=daily_robot_account_analysis

#### \u963F\u91CC\u4E91nls ####
nls.server.type=local
aliyun.nls.defaultServerAddr=ws://*************:8101/ws/v1
aliyun.nls.accessKeyId=default
aliyun.nls.accessKeySecret=default
aliyun.nls.defaultDialogFlowAsrModelAppkey=default
aliyun.nls.tts.appkey=default
aliyun.nls.tts.voice=Siyue
aliyun.nls.defaultApiAddr=http://*************:8701

#### \u4E91\u7247\u77ED\u4FE1\u914D\u7F6E ####
yunpian.sms.apiKey=959553e285a9d2cf89c497be5b592c61
yunpian.sms.financeApiKey=d4dbc473bf623e512bc65ea11dadaa7f
yunpian.sms.marketingApiKey=48eed6536ad50b297d00c6f5d341690a
yunpian.sms.callbackHost=http://************:58080
yunpian.template.callback.uri=http://crm.yiwise.com/apiEngine/smsTemplate/yunpian/callback
yunpian.sms.finance.tenant=771

ai.call.platform.back.url=https://crm.yiwise.com/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d
ai.call.platform.back.check.url=https://crm.yiwise.com/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.exist.url=https://crm.yiwise.com/apiDialogFlow/dialogFlow/isPlaceholderExist
ai.call.platform.back.dialog.params=https://crm.yiwise.com/apiDialogFlow/dialogFlow/getDialogFlowExtInfoInner?dialogFlowId=%d

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=wxd63fa0c76392f30d
wechat.appsecret=bd8188bbea0b83ab3fbdced3d0d1b043
wechat.token=yiwise123
wechat.aeskey=bo3oEon3nQ7VabeJ3

wechat.miniapp.appid=wxdc1924a8ddfc37f3
wechat.miniapp.appsecret=352ac81b014ca1d273ff800c6d16d553

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=OAH9JljoS0hRSbmUcbdB0iWziiCJOWPgjnFSaFJyQR8
wechat.template.telLineAlertMsg=2tPZw1mQfJ9bdC6FG1McC58Pzp7DgmW656JCTCtF5UI
wechat.template.noRobotAvailableAccountDebtMsg=i5YqCzPrNvhCrUAa2yIatvBKFZyVV4kM_aPIUztdWqs
wechat.template.earlyWarningMsg=cZL319ryviomjprt__omCs8wmHg6PNSx0prxF6RzTm8
wechat.template.callJobFinishMsg=KGcW-f4OIT7YXS7IlPl6y9JeL8kooZKiCIskUBFmEbg
wechat.template.insufficientBalanceMsg=SMpDtgKvWR9myMMiLks9wET1hmV9VxEgg27VMHV4clg
wechat.template.callJobCompletionPercentageMsg=Mk4iTdYGf8f86v7r583OE3gZw_CLf3z95h3uluYmyLw

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=https://wechat.yiwise.com

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5BA2\u6237\u5BFC\u5165\u660E\u7EC6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.chunkSize=5
export.chunkSize=5

import.rearrange.url=http://localhost:8110/apiImport/rearrange/?ossKey=%s

#### mongo ****
mongodb.primary.host=*************
mongodb.primary.port=27017
mongodb.primary.name=ai_call_engine
mongodb.primary.user=ai_call_engine
mongodb.primary.password=yiwise.tanyi
mongodb.primary.authentification=true

mongodb.secondary.host=*************
mongodb.secondary.port=27017
mongodb.secondary.name=ai_call_engine
mongodb.secondary.user=ai_call_engine
mongodb.secondary.password=yiwise.tanyi
mongodb.secondary.authentification=true

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

#### callback url #####
crm.url=https://crm.yiwise.com

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000

###### \u8F6C\u63A5\u89E6\u53D1 *0\u534F\u5546\u8F6C *1\u76F2\u8F6C #####
transfer.trigger=*0
###### \u89E6\u53D1\u4F1A\u8BAE \u5355\u4E2A\u6570\u5B57 ####
transfer.conf=0

###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.outbound.port=8151
freeswitch.esl.ip=**********
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon


######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=144
free_user.dialog_flow_id.finance=144
free_user.dialog_flow_id.fitment=144
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=1
cs.seat.freeswitchGroupId=1
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=98

###### first encrypt salt ######
first.encrypt.salt=etjtv8snxucwk@5d7oj7m<6ftm<?y?du6;:jj@epqli260l
first.digest.times=1
