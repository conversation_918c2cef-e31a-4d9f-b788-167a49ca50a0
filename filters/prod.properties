#### spring\u914D\u7F6E ####
spring.profiles.active=prod
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchjob.port=8140
server.callin.port=8150
server.smsjob.port=8160
server.esl.port=8170
server.freeswitch.port=8190
server.qcjob.port=8200
server.csCallJob.port=8210
server.qcweb.port=8220
server.text.port=8230
server.wechatJob.port=8250
server.bigdataWeb.port=8280
server.asyncJob.port=8290
server.batchMq.port=8410

apollo.bootstrap.enabled=true

apollo.meta=http://***************:8080,http://***************:8080
apollo.app.id=ai-call-back

logHub.endpoint = http://cn-hangzhou-intranet.log.aliyuncs.com
logHub.accessKeyId = LTAI0IFtCuGa2XQO
logHub.accessKeySecret = Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
logHub.project = ai-call-platform
logHub.logStore = ai-call-platform
logHub.v3EngineProject=aicc-dialogflow
logHub.v3EngineLogStore=aicc-dialogflow

### mq router ###
mq.router.ip=**************,**************
mq.router.port=7080

#### localization params
network.enabled=true
sms.enabled=true
wx.enabled=true
freeswitch.url=training.tanyibot.com
freeswitch.port=7443
import.template.url=https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/common/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx
nlp.url=https://brain.yiwise.com

#### storage type
object.storage.type=oss

ObjectStorage.useAuth = false
ObjectStorage.clazz = com.yiwise.middleware.objectstorage.oss.OSSHelper
oss.textBucket=ai-call-platform-text-prod

#### \u963F\u91CC\u4E91oss\u914D\u7F6E ####
ObjectStorage.oss.basicUrlPrefix = https://%s.oss-cn-hangzhou.aliyuncs.com
ObjectStorage.oss.endpoint = http://oss-cn-hangzhou-internal.aliyuncs.com
ObjectStorage.oss.accessId = LTAI0IFtCuGa2XQO
ObjectStorage.oss.accessKey = Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
ObjectStorage.oss.defaultBucket = ai-call-platform

#### minio\u914D\u7F6E ####
minio.endpoint=http://127.0.0.1:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.defaultBucket=ai-call-platform

#### fastDFS ####
fastDFS.tracker_server=

yiwise.tts.ip=***************:6543
server.job.max.quota=300
common.job.max.quota=35


#### \u963F\u91CC\u4E91mq ####
aliyun.ons.rocketmq.accessKey=LTAICuX7MOmHW6Oy
aliyun.ons.rocketmq.secretKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.ons.rocketmq.namesrvAddr=http://onsaddr.cn-hangzhou.mq-internal.aliyuncs.com:8080
aliyun.ons.rocketmq.instanceId=MQ_INST_1765425409792635_BeRbeUxx
aliyun.ons.rocketmq.regionId=cn-hangzhou
aliyun.ons.rocketmq.topic=tanyi-prod-common
aliyun.ons.rocketmq.producerId=PID_TANYI_PROD
aliyun.ons.rocketmq.groupId=GID_TANYI_PROD_COMMON

aliyun.ons.rocketmq.callback.topic=tanyi-prod-callback
aliyun.ons.rocketmq.callback.producerId=PID_TANYI_PROD_CALLBACK
aliyun.ons.rocketmq.callback.groupId=GID_TANYI_PROD_CALLBACK

aliyun.ons.rocketmq.asyncjob.topic=tanyi-prod-async-job
aliyun.ons.rocketmq.asyncjob.producerId=PID_TANYI_PROD_ASYNC_JOB
aliyun.ons.rocketmq.asyncjob.groupId=GID_TANYI_PROD_ASYNC_JOB

aliyun.ons.rocketmq.ocs.topic=tanyi-prod-ocs

aliyun.ons.rocketmq.filteredTask.topic=tanyi-prod-callout-filter
aliyun.ons.rocketmq.filteredTask.producerId=PID_TANYI_PROD_CALLOUT_FILTER
aliyun.ons.rocketmq.filteredTask.groupId=GID_TANYI_PROD_CALLOUT_FILTER

aliyun.ons.rocketmq.filteredTaskCompute.topic=tanyi-prod-filter-compute
aliyun.ons.rocketmq.filteredTaskCompute.producerId=PID_TANYI_PROD_FILTER_COMPUTE
aliyun.ons.rocketmq.filteredTaskCompute.groupId=GID_TANYI_PROD_FILTER_COMPUTE


aliyun.ons.rocketmq.locationAnalysis.topic=tanyi-prod-location-analysis
aliyun.ons.rocketmq.locationAnalysis.producerId=PID_TANYI_PROD_LOCATION_ANALYSIS
aliyun.ons.rocketmq.locationAnalysis.groupId=GID_TANYI_PROD_LOCATION_ANALYSIS

aliyun.ons.rocketmq.callOutOther.topic=tanyi-prod-callout-other
aliyun.ons.rocketmq.callOutOther.producerId=PID_TANYI_PROD_CALLOUT_OTHER
aliyun.ons.rocketmq.callOutOther.groupId=GID_TANYI_PROD_CALLOUT_OTHER

aliyun.ons.rocketmq.smsCallBack.topic=tanyi-prod-sms-callback
aliyun.ons.rocketmq.smsCallBack.producerId=PID_TANYI_PROD_SMS_CALLBACK
aliyun.ons.rocketmq.smsCallBack.groupId=GID_TANYI_PROD_SMS_CALLBACK

aliyun.ons.rocketmq.call.record.topic=tanyi-prod-call-record

aliyun.ons.rocketmq.asrRecognition.topic=tanyi-daily_asr
aliyun.ons.rocketmq.asrRecognition.producerId=PID_TANYI_daily_ASR
aliyun.ons.rocketmq.asrRecognition.groupId=GID_TANYI_daily_ASR
aliyun.ons.rocketmq.qc.plan.topic=tanyi-prod-qcplan
aliyun.ons.rocketmq.qc.plan.producerId=PID_TANYI_PROD_QCPLAN
aliyun.ons.rocketmq.qc.plan.groupId=GID_TANYI_PROD_QCPLAN
aliyun.ons.rocketmq.peers.topic=tanyi-prod-peers
aliyun.ons.rocketmq.peers.producerId=PID_TANYI_PROD_PEERS
aliyun.ons.rocketmq.peers.groupId=GID_TANYI_PROD_PEERS
aliyun.ons.rocketmq.wechatJob.topic=tanyi_prod_wechat
aliyun.ons.rocketmq.wechatJob.groupId=GID_PROD_WECHAT_JOB
aliyun.ons.rocketmq.callEventLog.topic=tanyi_prod_callEventLog
aliyun.ons.rocketmq.callEventLog.groupId=GID_PROD_CALL_EVENT_LOG
aliyun.ons.rocketmq.callEventLog.interruptAnalysis.groupId=GID_PROD_INTERRUPT_ANALYSIS
aliyun.ons.rocketmq.intentRuleMatchFlag.topic=tanyi_prod_intentRuleMatchFlag
aliyun.ons.rocketmq.intentRuleMatchFlag.groupId=GID_PROD_INTENT_RULE_MATCH
aliyun.ons.rocketmq.dialogStats.topic=tanyi_prod_dialogStats
aliyun.ons.rocketmq.dialogStats.groupId=GID_PROD_DIALOG_STATS
aliyun.ons.rocketmq.predictLog.topic=tanyi_prod_predictLog
aliyun.ons.rocketmq.predictLog.groupId=GID_PROD_PREDICT_LOG
aliyun.ons.rocketmq.returnVisit.topic=tanyi_prod_return_visit
aliyun.ons.rocketmq.returnVisit.groupId=GID_PROD_RETURN_VISIT
aliyun.ons.rocketmq.socketio.message.topic=prod_aicc_socketio_message
aliyun.ons.rocketmq.socketio.message.groupId=GID_PROD_AICC_SOCKETIO_MESSAGE

aliyun.ons.rocketmq.callbacknew.topic=tanyi_prod_callbacknew
aliyun.ons.rocketmq.callbacknew.producerId=PID_TANYI_PROD_CALLBACKNEW
aliyun.ons.rocketmq.callbacknew.groupId=GID_TANYI_PROD_CALLBACKNEW

aliyun.ons.rocketmq.callbackGender.topic = tanyi_prod_callback_gender
aliyun.ons.rocketmq.callbackGender.producerId = PID_TANYI_PROD_CALLBACK_GENDER
aliyun.ons.rocketmq.callbackGender.groupId = GID_TANYI_PROD_CALLBACK_GENDER

aliyun.ons.rocketmq.crowd.push.callback.topic=prod_customer_aicc_crowd_push_callback
aliyun.ons.rocketmq.crowd.push.callback.groupId=GID_PROD_CUSTOMER_AICC_CROWD_PUSH_CALLBACK

aliyun.ons.rocketmq.robot.account.analysis.topic=prod_robot_account_analysis

#### \u963F\u91CC\u4E91nls ####
nls.server.type=aliyun
aliyun.nls.defaultServerAddr=wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1
aliyun.nls.accessKeyId=LTAI0IFtCuGa2XQO
aliyun.nls.accessKeySecret=Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
aliyun.nls.defaultDialogFlowAsrModelAppkey=4QQmua3uHGKGwsYD
aliyun.nls.tts.appkey=8yus5oYXl6bMKdES
aliyun.nls.tts.voice=yina
aliyun.nls.slp.url=
aliyun.nls.tts.addr=https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts

sms.code.chuanglan.channel.id=119

#### \u9886\u9053\u77ED\u4FE1 ####
lingdao.sms.url=http://118.178.105.224:8088/status.jsp?usr=zyswgj&pwd=zyswgj0511

ai.call.platform.back.type.variables.url=http://**************/apiDialogFlow/variable/getVariableListForType?dialogFlowId=%d&type=%d
ai.call.platform.back.url=http://**************/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d&collectionName=%s
ai.call.platform.back.check.url=http://**************/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.standardNotifyModel.predict.url=http://***************:9898/predict
ai.call.platform.back.exist.url=http://**************/apiDialogFlow/dialogFlow/isPlaceholderExist
ai.call.platform.back.analyze.phoneWithFile.url=http://***************:8120/apiAnalyzePhone/analyze/withMultipartFile
ai.call.platform.back.dialog.params=http://**************/apiDialogFlow/dialogFlow/getDialogFlowExtInfoInner?dialogFlowId=%d
ai.call.platform.back.analyze.dictionary.url=http://**************:2466
ai.call.platform.back.timeEntity.url=http://**************:2612/extract
ai.call.platform.back.addrEntity.url=http://**************:2602/
ai.call.platform.back.gender.url=http://***************:7688/gender
ai.call.platform.back.emotion.url=http://***************:9018/emotion_recognition
ai.call.platform.back.role.url=http://************:5101/
ai.call.platform.nlp.knowledge.search.url=https://brain.yiwise.com/aicc/knowledge/quickSearch?tenantId=%d&robotId=%d&search=%s
ai.call.platform.nlp.robotLog.url=http://brain.yiwise.com/aicc/robot/operationLog?tenantId=%d&robotId=%d
ai.call.platform.nlp.shareKnowledgeList.url=https://brain.yiwise.com/aicc/knowledge/getShareKnowledgeList
ai.call.platform.nlp.syncShareKnowledge.url=https://brain.yiwise.com/aicc/knowledge/syncShareKnowledge

algorithm.similarSentence.generate.url=http://121.36.172.209:8990/gen

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=wx26106ad72e9cd401
wechat.appsecret=f5a15f59defd6351cb4850a82f2d1cd2
wechat.token=123456
wechat.aeskey=bo3oEon3nQ7VabeJ3

wechat.miniapp.appid=wxdc1924a8ddfc37f3
wechat.miniapp.appsecret=352ac81b014ca1d273ff800c6d16d553

#### \u56DE\u8C03\u5806\u79EF\u9884\u8B66\u914D\u7F6E ####
feishu.warn.phoneNumberSet=18753115285,***********
feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/12ab7732-18d7-4e70-9b82-e06225b4e252

#### ope\u4F01\u4E1A\u5FAE\u4FE1\u767B\u9646 ####
enterprise.wx.corpid=ww8ac24407dcd79e05
enterprise.wx.agentid=1000016
enterprise.wx.corpsecret=aC789NeGEHTRT2kqKNeb6pphxAV2WR2jLVmoJ_jE_70

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=wwl3G3nH7cPlLzRdUgi1H73DFQ7FqAxGjwNXqB-8kgg
wechat.template.telLineAlertMsg=25aISlkwxI1fCV9udVxmJrPNj0Abqc9Nzg4N8M8H-CE
wechat.template.noRobotAvailableAccountDebtMsg=70YkNAN9MQEBm74uU90L0Mvh6w5SWG4OB0yMAgv-hN8
wechat.template.earlyWarningMsg=CBALLLgbJo5kIe6oHExOFDu29EpN0Qt-N_1WhDP-Dv8
wechat.template.callJobFinishMsg=KGks5uLqRAsITb7gnA6AAnceUt9MxpAXsniGyfyUuWg
wechat.template.insufficientBalanceMsg=U1CK90jpXTGCaX0VEl8CuZ6HnEnkY0xwafxiPsK-9UU
wechat.template.callJobCompletionPercentageMsg=i2JBuSM1Kr_6l00qtiaULjSxELX9YaTCOMB7rL2iWrU
wechat.template.tenantAccountAutoSet=U1CK90jpXTGCaX0VEl8CuZ6HnEnkY0xwafxiPsK-9UU

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=https://wechat.tanyibot.com
wechatcp.frontend.url=https://aicc.yiwise.com

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5BA2\u6237\u5BFC\u5165\u660E\u7EC6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.assignFile=\u5206\u914D\u4EBA\u5DE5\u5916\u547C\u4EFB\u52A1
import.transferFile=\u5BA2\u6237\u5BFC\u5165\u60C5\u51B5
import.chunkSize=100
export.chunkSize=100

import.rearrange.url=http://**************/apiImport/rearrange/?ossKey=%s

#### elasticsearch ****
es.hostname=es-cn-v641as47r000z0nnw.elasticsearch.aliyuncs.com
es.port=9200
es.username=elastic
es.password=oN6vbqaaKlYsjAin
es.enable=true

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### prodocut document upload config ####
oss.productdocument.webDirectUpload.max=1048576000
oss.productdocument.root=ProductDocument/

oss.ttsaudition.root=TtsAudition/

oss.welcome.msg.root=WelcomeMsg

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload
#### \u8D44\u8D28\u8BA4\u8BC1\u6750\u6599 ####
oss.upload.authentication=authentication
#### \u5E2E\u52A9\u4E2D\u5FC3 ####
oss.upload.helpcenter=helpcenter

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

oss.avatar.upload.root=AvatarFileUpload

#### callback url #####
crm.url=https://crm.tanyibot.com

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000

###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.outbound.port=8151
freeswitch.esl.ip=**************
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon


######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=152
free_user.dialog_flow_id.finance=129
free_user.dialog_flow_id.fitment=327
free_user.dialog_flow_id.back=7130
free_user.dialog_flow_id.debt=7110
free_user.dialog_flow_id.power=7123
free_user.dialog_flow_id.telecom=5359
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

peers.queuedPacketNum=10

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=5
cs.seat.freeswitchGroupId=6
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=98

###### first encrypt salt ######
first.encrypt.salt=geelysdafaqj23ou89ZXcj@#$@#$#@KJdjklj;D../dSF.,
first.digest.times=1

question.length=20
caitong.ask.dialogFlowId=5052,5432

weibao.tenant=3765
weibao.startcall.url=https://tms-prd.wesure100.com/api/v1/public/cop/callBothRequest
weibao.callrecord.callback.url=https://tms-prd.wesure100.com/api/v1/public/callinfo/intention/notify

tongdun.tenant=96372

# \u524D\u540Eusersaytext\u5408\u5E76\u7684\u6682\u505C\u65F6\u95F4
user.say.text.time=600

# local \u672C\u5730\u5316 aliyun \u7EBF\u4E0A
rocketmq.type=aliyun
rocketmq.name-server=*************:9876

# \u652F\u4ED8\u5B9D\u8FD4\u56DE\u548C\u56DE\u8C03
alipay.boss.returnUrl=https://boss.tanyibot.com/apiBoss/aliPay/payReturn
alipay.boss.notifyUrl=https://boss.tanyibot.com/apiBoss/aliPay/payNotify
alipay.boss.backUrl=https://boss.tanyibot.com/#/systemManagement/line
alipay.crm.returnUrl=https://crm.tanyibot.com/apiEngine/aliPay/payReturn
alipay.crm.notifyUrl=https://crm.tanyibot.com/apiEngine/aliPay/payNotify
alipay.crm.backUrl=https://crm.tanyibot.com/#/systemManagement/line
alipay.aicc.returnUrl=https://aicc.tanyibot.com/active-enterprise/account-manage/info
alipay.aicc.backUrl=https://aicc.tanyibot.com/active-enterprise/account-manage/info
alipay.doudian.aicc.returnUrl=https://dou-magicbot.tanyibot.com/home

robot.asr.type=yiwise

#### YiwiseAsrGateway\u5730\u5740 ####
yiwise.asr.gatewayUrl=http://asr-gateway.hangzhou-internal.yiwise.com
yiwise.asr.accessKeyId=
yiwise.asr.accessKeySecret=
yiwise.asr.AsrClientTokenGetterClazz=com.yiwise.core.service.yiwise.asr.AsrClientTokenGetterImpl

yiwise.asr.file.gatewayUrl=http://************:6060
yiwise.asr.file.accessKeyId=0peS8KMJ
yiwise.asr.file.accessSecret=vGpi8XgOUN7KV0gg2t1i

nlp.prefix=https://brain.yiwise.com
nlp.beforeChat=https://brain.yiwise.com/aicc/speech/beforeChat
nlp.afterChat=https://brain.yiwise.com/aicc/speech/afterChat
nlp.chat=https://brain.yiwise.com/aicc/speech/chat
nlp.publishCallBack=https://brain.yiwise.com/aicc/speech/createSnapshot
nlp.entityList=https://brain.yiwise.com/apiIntegration/tanyi/entityList
nlp.knowledgeList=https://brain.yiwise.com/aicc/knowledge/knowledgeList?tenantId=%s&robotId=%s&pageSize=%s&pageNum=%s&keyword=%s&knowledgeId=%s
nlp.login=https://brain.yiwise.com/apiPlatform/entry/singleLogin
nlp.getRobotInfo=https://brain.yiwise.com/aicc/robot/getRobotInfo
nlp.access.secret=p8HJ4IZFEfEirRWJaDwV45Gsv8zwobtc6MJyuSdnqSBjOcqi


# jd ai platform
jd.app.key=642A97CB88E064536C8767C5157F9798
jd.app.secret=3E13A9BEFDF047C08453EF6133380623
jd.new.user.url=https://crm.tanyibot.com
jd.redirect.uri=https://crm.tanyibot.com/apiPlatform/jd/auth
jd.follower.id=6882

# tencnet asr
tencent.asr.appId=1305570814
tencent.asr.secretId=AKIDsd9ON6XhbqsfBQO5WhbEagW4MN8bniNl
tencent.asr.secretKey=8zauylHXYLsBF2atwjHadRzIMAi03qNe

# Customer Service console config
consistent.dialog.beep.url=DefaultBeeps/consistentDialogBeep.wav
forcasting.callout.beep.url=DefaultBeeps/forecastingCalloutBeep.wav
uncommon.hangup.beep.url=DefaultBeeps/uncommonHangupBeep.wav
queue.beep.url=DefaultBeeps/queueBeep.wav

call.out.quickLogin.ak=XDPVMX4GHQM5TKOY2MK5
call.out.quickLogin.phoneNumber=***********
call.out.quickLogin.password=Zn0Yo3F7

#wechat
wechat.component.appId=wx09bae5d404449180
wechat.component.appSecret=652b978663e03138ee8d095f5a68dd00
wechat.component.aesKey=VDBUMHmdgTCCtGNoQgxvRIizAwwzZLVrQItjyIGMJzJ
wechat.component.token=LwtScWLvpacbdqTeYWGMopyxQjaatjjL
wechat.component.preAuthCallback.url=https://aicc.tanyibot.com/enterprise-center/account-manage/channel?type=%s
wechat.component.accessToken.url=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
wechat.component.userInfo.url=https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN
wechat.miniapp.scheme.h5.url=https://wechat.tanyibot.com/wxapp-h5/?link=%s
wechat.miniapp.tryDialogFlow.h5.url=https://wechat1.tanyibot.com/experience/#/callout?qrCodeId=%s
wechat.miniapp.qrCodeImport.h5.url=https://wechat1.tanyibot.com/robot-call-job-import-qr?qrCodeId=%s

#aicc
nlp.robot.chat.url=https://brain.yiwise.com/aicc/text/chat?tenantId=%d&robotId=%d&text=%s&sessionId=%s
# qc algorithm interfaces
qc.word.frequency.update=http://**************:2604/upload
qc.word.frequency.query=http://**************:2604/query
qc.rule.tag.trigger=http://************:2605/predict

changhe.tenant.id=4895

# \u6817\u5B50\u96C6\u6210
lizi.ak=6cd88362121047f2a0b6dc1613a41232
lizi.as=2bbb99e4b80f44579f71e1a7895b0143


## simple \u7B80\u6613\u7248\u672C \u6709\u4E9B\u529F\u80FD\u6A21\u5757\u4E0D\u62C6\u5206 advanced \u7EBF\u4E0A\u7248\u672C \u529F\u80FD\u6A21\u5757\u62C6\u5206
publish.module.type=advanced

# \u4EA7\u54C1\u6A21\u5F0F online \u7EBF\u4E0A \u5305\u542B\u4E00\u77E5\u7684\u4E13\u6709\u54C1\u529F\u80FD other \u4E0D\u5305\u542B\u4E00\u77E5\u4E13\u6709\u529F\u80FD \u4E3A\u5176\u4ED6\u5BA2\u6237\u90E8\u7F72
product.type=online

server.call.in.count=100

# yiwise tts
tts.yiwise.url=http://tts.hangzhou-internal.yiwise.com/v1/tts/single
tts.yiwise.auth.url=http://tts.hangzhou-internal.yiwise.com/v1/auth/token
tts.yiwise.user.name=yiwise
tts.yiwise.user.password=_yiwise

#### Huawei callin demo ####
huawei.callin.dialogFlowId=16078

training.dialogFlow.nodeArriveStats.enabled=false
#### qiyu ####
qiyu.back.url=https://qiyukf.com/openapi/crm/thirdParty/syncCrmInfo?appKey=%s&time=%s&checksum=%s
qiyu.menu.url=https://qiyukf.com/openapi/icoutcall/syncthirdparty/appinfo?appKey=%s&time=%s&checksum=%s
qiyu.template.get.url=https://qiyukf.com/openapi/icoutcall/get/sms/templates?appKey=%s&time=%s&checksum=%s
qiyu.template.send.url=http://qiyukf.com/openapi/icoutcall/smstask/create?appKey=%s&time=%s&checksum=%s
qiyu.template.result.url=https://qiyukf.com/openapi/icoutcall/smstask/status?appKey=%s&time=%s&checksum=%s
qiyu.callrecord.callback.url=https://qiyukf.com/openapi/icoutcall/sync/callhistory?appKey=%s&time=%s&checksum=%s
qiyu.staffgroup.get.url=https://qiyukf.com/openapi/icoutcall/get/staffGroups?appKey=%s&time=%s&checksum=%s
qiyu.staffgroup.upgrade.get.url=https://qyhjcs.qiyukf.com/openapi/kefu/icoutcall/staffgroup/list?appKey=%s&time=%s&checksum=%s
qiyu.staff.upgrade.get.url=https://qyhjcs.qiyukf.com/openapi/kefu/icoutcall/staff/list?appKey=%s&time=%s&checksum=%s
qiyu.push.mail.url=https://qiyukf.com/openapi/icoutcall/pushMail?appKey=%s&time=%s&checksum=%s
qiyu.cc.transfer.url=cc-trunk.qiyukf.com:6280
qiyu.appkey=7dsa8i3f047270d789d1d4c0d4a118c9@icoutcall@
qiyu.secret=disd341bac2fa48d0ffe8fc779d02480
qiyu.distributor=215
qiyu.dialogflow.id=14967
#### e\u7B7E\u5B9D ####
esign.openapi.url=https://openapi.esign.cn
esign.openapi.appId=**********
esign.openapi.secret=9ee912ab3c0fc09f25017542823ece7a
esign.openapi.agentAccountId=69541a70adba40bda9377cd3041dc125
esign.openapi.callback=https://aicc.tanyibot.com//apiEngine/authentication/eCallBack
#### \u97F3\u8272\u514B\u9686\u5C0F\u7A0B\u5E8F ####
timbre.clone.miniapp.push.app=wx774e47f56821c847,35c21264d55a8430487a28d34e0a988b
timbre.clone.miniapp.nopush.app=wx32b692f9f50afc52,e9fb7afbca56be754a6516b1af69c276

sunfeng.tenant=9887
sunfeng.apikey=e77621511efc4c689cc19669a48b5034
sunfeng.virtualnumber.get.url=https://crs-k8.sf-express.com/api/recruitcenter/out/ty_virtual
sunfeng.login.user=131910

sunfeng.tenant.dev=17735
sunfeng.apikey.dev=d9cf325f97d84f43ae5aa251cba04d81
sunfeng.virtualnumber.get.url.dev=http://prs-new.sit.sf-express.com:23222/api/recruitcenter/out/ty_virtual

global_sms_send=true
global_message_center_customer_interact=true
global_message_center_work_order=true
global_io_history_clue=true
global_user_center_contact_manager=true
global_user_center_back_old=true
main_sms_platform=true
main_smart_clue=true
main_seat_help=true
main_work_order=true
main_code_login=true
main_wechat_login=true
ope_customer_whitelist_share=true
ope_customer_contract_num=true
ope_customer_back_money=true
ope_customer_callout=true
ope_customer_callin=true
ope_customer_qc=true
ope_customer_voice=true
ope_customer_text=true
ope_customer_yibrain_voice=true
ope_customer_yibrain_text=true
ope_customer_zhineng_xiansuo=true
ope_customer_sms_center=true
ope_customer_aiassistant=true
ope_customer_ivr=true
ope_customer_login_crm=true
ope_customer_login_aicc=true
ope_customer_forbid_account=true
ope_customer_view_login_log=true
ope_whitelist_share=true
ope_utils=true
ope_concurency=true
ope_cur_concurency=true
ope_operation_log=true
ope_clue_collect=true
ope_huawei_form=true
customerServiceStage=true
qc=true
engine=true
callout_phoneTaskV2_BOTV2=true
callout_phoneTask_genderMood=true
callout_billing=true
callin_IVR=true
enterprise_account_manage_info=true
enterprise_account_manage_order=true
enterprise_account_manage_billing=true
enterprise_account_manage_communication=true
enterprise_account_manage_wechatCp=true
workorder=true
customerCenter=true
ope_dialog_template=true
ope_dialog_audit=true
global_voice_seat=true
callout_phoneTaskV2_intent_wechat_push=true
callout_phoneTaskV2_sms_push=true
callout_setting_stats_range=true
#default BackgroundSound url
bot.backgroundSound.url=http://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/common/audio/office_talk.wav

# scrm
scrm.queryJuziBot.url=https://scrm.yiwise.com/scrm/juzi/getJuziBotListForAICC
scrm.queryJuziCode.url=https://scrm.yiwise.com/scrm/api/juzi/getJuziCode
scrm.getJuziToken.url=https://scrm.yiwise.com/scrm/api/juzi/getJuziToken
scrm.getJuziUser.url=https://scrm.yiwise.com/scrm/api/juzi/getJuziUserInfo
scrm.queryTenantList.url=https://scrm.yiwise.com/scrm/tenant/listForAICC
scrm.queryTenantInfo.url=https://scrm.yiwise.com/scrm/tenant/infoForAicc
scrm.queryWechatList.url=https://scrm.yiwise.com/scrm/wxwork/getWxworkUserListForAICC
scrm.queryOrgList.url=https://scrm.yiwise.com/scrm/wxwork/getWxworkOrgListForAicc
scrm.queryWechatByIds.url=https://scrm.yiwise.com/scrm/wxwork/getWxworkUserListByIdsForAICC
scrm.queryOrgByIds.url=https://scrm.yiwise.com/scrm/wxwork/getOrgListByIdsForAicc
scrm.addJuziWechatFriend.url=https://scrm.yiwise.com/scrm/juzi/applyJuziAddFriend
scrm.addWechatFriend.url=https://scrm.yiwise.com/scrm/wxwork/autoSendAddApply
scrm.queryManualWechatList.url=https://scrm.yiwise.com/scrm/user/listAllForAicc
scrm.addManualWechatFriend.url=https://scrm.yiwise.com/scrm/tobeadd/aiccAddToBeAddCustomer
scrm.queryWechatTag.url=https://scrm.yiwise.com/scrm/customerTag/selectWechatGroupAICC
scrm.saveWelcomeMsg.url=https://scrm.yiwise.com/scrm/tobeadd/aiccSaveWelcomeMsg
scrm.getWelcomeMsg.url=https://scrm.yiwise.com/scrm/tobeadd/aiccGetWelcomeMsg?robotCallJobId=%s
scrm.batchGetWelcomeMsg.url=https://scrm.yiwise.com/scrm/tobeadd/aiccBatchGetWelcomeMsg
scrm.deleteWelcomeMsg.url=https://scrm.yiwise.com/scrm/tobeadd/aiccDeleteWelcomeMsg?robotCallJobId=%s
scrm.getEnterpriseArticle.url=https://scrm.yiwise.com/scrm/enterpriseArticle/getEnterpriseArticleByTenantId
scrm.getEnterpriseFile.url=https://scrm.yiwise.com/scrm/enterpriseFile/getEnterpriseFileByTenantId
scrm.addWechatAlert.url=https://scrm.yiwise.com/scrm/wxwork/sendJobTerminateMsg
scrm.getGrowCustomerAppList.url=https://scrm.yiwise.com/scrm/growCustomerApp/aiccGetList
scrm.directCallback.url=https://scrm.yiwise.com/scrm/ma/aiccOpeation/directlyCallBack
scrm.get.template.url=https://scrm.yiwise.com/scrm/exclusiveLink/getQcCode
scrm.getLinkUrlTemplate.url=https://scrm.yiwise.com/scrm/linkUrlTemplate/aiccGetLinkUrlTemplates
scrm.getStatisticalData.url=https://scrm.yiwise.com/scrm/getStatisticalData
scrm.updateScrmInfo=https://scrm.yiwise.com/api-web/tenantManage/updateScrmInfo
scrm.getScrmRpaRobotCount=https://scrm.yiwise.com/api-web/tenantManage/getScrmRpaRobotCount?aiccTenantId=%d&scrmTenantId=%d
scrm.testGetQRCode=https://scrm.yiwise.com/api-web/tenantManage/testGetQRCode?ip=%s
scrm.rpaServerList=https://scrm.yiwise.com/api-web/tenantManage/rpaServerList?aiccTenantId=%d&scrmTenantId=%d
scrm.rpaServerHistoryList=https://scrm.yiwise.com/api-web/tenantManage/rpaServerHistoryList?aiccTenantId=%d&scrmTenantId=%d
scrm.robotList=https://scrm.yiwise.com/api-web/tenantManage/robotList?aiccTenantId=%d&scrmTenantId=%d
scrm.robotHistoryList=https://scrm.yiwise.com/api-web/tenantManage/robotHistoryList?aiccTenantId=%d&scrmTenantId=%d
scrm.severUnbindList=https://scrm.yiwise.com/api-web/serverResource/unbindList
scrm.addServerResource=https://scrm.yiwise.com/api-web/serverResource/addServerResource
scrm.serverList=https://scrm.yiwise.com/api-web/serverResource/list?pageNum=%d&pageSize=%d
scrm.updateServerResource=https://scrm.yiwise.com/api-web/serverResource/update
scrm.getAiccTenantIds=https://scrm.yiwise.com/api-web/tenantManage/getAiccTenantIds
scrm.addRpaServer=https://scrm.yiwise.com/api-web/tenantManage/addRpaServer
scrm.editRpaServerAccountCount=https://scrm.yiwise.com/api-web/tenantManage/editRpaServerAccountCount?scrmTenantRelationId=%d&count=%d&userId=%d
scrm.delRpaServer=https://scrm.yiwise.com/api-web/tenantManage/delRpaServer?scrmTenantRelationId=%d&userId=%d
scrm.postponeRpaServer=https://scrm.yiwise.com/api-web/tenantManage/postponeRpaServer
scrm.updateRobotCount=https://scrm.yiwise.com/api-web/tenantManage/updateRobotCount
scrm.selectByOrganizationIdAuth=https://scrm.yiwise.com/api-web/tenantManage/selectByOrganizationIdAuth?scrmTenantId=%d
scrm.selectByOrganizationId=https://scrm.yiwise.com/api-web/tenantManage/selectByOrganizationId?organizationId=%d
scrm.selectByTenantAndOrganizationIdList=https://scrm.yiwise.com/api-web/tenantManage/selectByTenantAndOrganizationIdList
scrm.getScrmTenantInfo=https://scrm.yiwise.com/api-web/tenantManage/getScrmTenantInfo?scrmTenantId=%d
scrm.getOrgUserTree=https://scrm.yiwise.com/api-web/tenantManage/getOrgUserTree
scrm.scrmUserListAll=https://scrm.yiwise.com/api-web/tenantManage/scrmUserListAll
scrm.getOrgAndUserNams=https://scrm.yiwise.com/api-web/tenantManage/getOrgAndUserNams
scrm.cancelBatchJob.url=https://scrm.yiwise.com/scrm/batch/cancelBatchJob?jobInstanceId=%s
scrm.setUser2Redis.url=https://scrm.yiwise.com/scrm/user/setUser2redis/%s
scrm.request.url=https://scrm.yiwise.com


xiaoke.default.deployment.userId=DEFAULT,18217237005

sms.callback.chuanglan.mms=https://aicc.tanyibot.com/apiEngine/chuanglanmms/callback

implement.role=57,170
dialog.role=152,361,362,5

wechat.cp.group.url.ysld=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fe8dcbc9-7dcc-4be2-8f89-afe70121db66
wechat.cp.group.url.weibao=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fe8dcbc9-7dcc-4be2-8f89-afe70121db66
wechat.cp.group.url.tf=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6465a270-4066-4338-a2bd-be29d43e39d0
wechat.cp.group.url.cl=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4064a00f-c653-49c2-aacb-a47b5478e97b
wechat.cp.group.url.origins=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9e850330-5de6-46f5-b93b-776f73bf54f4

# \u7B97\u6CD5\u914D\u7F6E
algorithm.register.url=http://***************:9008/register
algorithm.register.env=AICC-PROD
algorithm.redisKey.url=https://aicc.yiwise.com/apiDialogFlow/algorithm/redisKeyList
algorithm.data.url=https://aicc.yiwise.com/apiDialogFlow/algorithm/list
algorithm.callback.url=https://aicc.yiwise.com/apiDialogFlow/algorithm/callback
algorithm.predict.url=http://***************:9898/predict
algorithm.verbal.training.url=http://***************:9899/predict
algorithm.domain.list.url=http://***************:9898/get_domain_names
algorithm.patch.list.url=http://***************:9898/get_patch_keys

privacyNumber.fengyin.url=http://192.168.101.2:30005

#\u521B\u84DD\u95EA\u6D4B\u914D\u7F6E
sms.chuanglan.appId=n4vjLXHN
sms.chuanglan.appKey=2q1q4Fxk
sms.chuanglan.flash.verify.url=https://api.253.com/open/flashVerify

#HBase
hbase.zookeeper.quorum=hb-bp1sse22j7l8l68hf-master2-001.hbase.rds.aliyuncs.com,hb-bp1sse22j7l8l68hf-master1-001.hbase.rds.aliyuncs.com,hb-bp1sse22j7l8l68hf-master3-001.hbase.rds.aliyuncs.com
hbase.zookeeper.property.clientPort=2181

qiweibao.addFriend.url=https://oapi-qyb.wxb.com/yizhi/addRecord
quanliang.addFriend.url=https://api.aquanliang.com/gateway/qopen/AddExtUserByRule
quanliang.getAccessToken.url=https://api.aquanliang.com/gateway/qopen/GetAccessToken
quanliang.appkey=co2280d729654041f5
quanliang.appSecret=qowG3yiK6qDdnZM0NHhtCpTPD2IKXQNGHZQD5wsO53Ua33BeRhRt
quanliang.encodingAESKey=ceb6d87c731f477f95494620988efd03

v3.dialogFlow.url.prefix=https://aicc.tanyibot.com/

extension.judge.answer.server=ws://***************:9091/audio_predict

service.name=ai-call-service

jxs.server.url=https://baize-api-yizhi.jingxiansuo.com
jxs.push.url=https://openapi.tanyibot.com/apiOpen/v1/job/importCustomerJxs

zhejiao.tenant=9852
zhejiao.call.in.url=https://zsdl.zj.sgcc.com.cn/zj_eqa/open/bcpiss/rest/msg01
zhejiao.private.key.name=MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIhIs/3wz/nod7Ff/0UMzyK4gRCjPLqSfYkxxtlLn8GEn5Tg9kgKEl+CBiVad3w1afgFivaTHHI7xCC9zyulFkKQ3Q5IuouBkaY2+hKUPDzRRer3RmxUcNM4e5IUfDwG//8Hh69Q0kEHyD22lXGvo/kQnoUyhH+RjZ1UVAJAzj7lAgMBAAECgYAVh26vsggY0Yl/Asw/qztZn837w93HF3cvYiaokxLErl/LVBJz5OtsHQ09f2IaxBFedfmy5CB9R0W/aly851JxrI8WAkx2W2FNllzhha01fmlNlOSumoiRF++JcbsAjDcrcIiR8eSVNuB6ymBCrx/FqhdX3+t/VUbSAFXYT9tsgQJBALsHurnovZS1qjCTl6pkNS0V5qio88SzYP7lzgq0eYGlvfupdlLX8/MrSdi4DherMTcutUcaTzgQU20uAI0EMyECQQC6il1Kdkw8Peeb0JZMHbs+cMCsbGATiAt4pfo1b/i9/BO0QnRgDqYcjt3J9Ux22dPYbDpDtMjMRNrAKFb4BJdFAkBMrdWTZOVc88IL2mcC98SJcII5wdL3YSeyOZto7icmzUH/zLFzM5CTsLq8/HDiqVArNJ4jwZia/q6Fg6e8KO2hAkB0EK1VLF/ox7e5GkK533Hmuu8XGWN6I5bHnbYd06qYQyTbbtHMBrFSaY4UH91Qwd3u9gAWqoCZoGnfT/o03V5lAkBqq8jZd2lHifey+9cf1hsHD5WQbjJKPPIb57CK08hn7vUlX5ePJ02Q8AhdZKETaW+EsqJWpNgsu5wPqsy2UynO
zhejiao.sm4.key=21A81BD0A039227B

aliyun.ons.rocketmq.batchmq.accessKey=LTAICuX7MOmHW6Oy
aliyun.ons.rocketmq.batchmq.secretKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.ons.rocketmq.batchmq.namesrvAddr=http://MQ_INST_1765425409792635_BYgyU5O6.cn-hangzhou.mq-vpc.aliyuncs.com:8080
aliyun.ons.rocketmq.batchmq.namespace=
aliyun.ons.rocketmq.batch.process.default.topic=batch_process
aliyun.ons.rocketmq.batch.writer.default.topic=batch_writer
process.topic.key=ProcessTopicKey
writer.topic.key=WriterTopicKey

ysld.mainBrandId=29

sfl.feishu.warn.phoneNumberSet=15868136161
sfl.feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/2b651fcc-0d5c-4b1f-bece-5a41398cbd9e
sfl.feishu.warn.tenantSet=

ysld.black.list.feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/595b40fe-8259-45f7-8efb-3a1fa42095db

isv.aliyun.ons.rocketmq.accessKey=LTAICuX7MOmHW6Oy
isv.aliyun.ons.rocketmq.secretKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
isv.aliyun.ons.rocketmq.namesrvAddr=http://onsaddr.cn-hangzhou.mq-internal.aliyuncs.com:8080
isv.aliyun.ons.rocketmq.producerId=PID_TANYI_PROD
isv.aliyun.ons.rocketmq.callbacknew.topic=tanyi_prod_callbacknew
isv.aliyun.ons.rocketmq.callbackGender.topic=tanyi_prod_callback_gender

aliyun.ons.rocketmq.importAsync.topic = prod_import_async_process

bingjian.daily.feishu.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/2243a24d-5a4e-4a4b-a7fe-5385c5893877
bingjian.daily.feishu.tenantId=95971
recording.channel.switching.tenants=96356,96280

yi.che.add.sip.accounts=1003524,1003475,L118834
privacy.number.feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/3dde89f2-0cd9-4d59-8276-29fa1827c04f

