#### spring port ####
spring.profiles.active=prod-huawei
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchjob.port=8140
server.callin.port=8150
server.smsjob.port=8160
server.esl.port=8170
server.freeswitch.port=8190
server.qcjob.port=8200
server.csCallJob.port=8210
server.qcweb.port=8220
server.text.port=8230
server.wechatJob.port=8250
server.bigdataWeb.port=8280
server.asyncJob.port=8290
server.batchMq.port=8410

apollo.bootstrap.enabled=true
apollo.meta=http://***************:8080,http://***************:8080
apollo.app.id=ai-call-back-finance-huawei

### mq router ###
mq.router.ip=************
mq.router.port=7080

#### localization params
network.enabled=true
sms.enabled=true
wx.enabled=true
freeswitch.url=hwc-training.tanyibot.com
freeswitch.port=7443
import.template.url=https://ai-call-platform.obs.cn-east-3.myhuaweicloud.com/common/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx
nlp.url=http://***********:8860
yiwise.tts.ip=*************:6543
server.job.max.quota=300
common.job.max.quota=35


#### \u963F\u91CC\u4E91nls ####
nls.server.type=aliyun
aliyun.nls.defaultServerAddr=wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1
aliyun.nls.accessKeyId=LTAICuX7MOmHW6Oy
aliyun.nls.accessKeySecret=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.nls.defaultDialogFlowAsrModelAppkey=4QQmua3uHGKGwsYD
aliyun.nls.tts.appkey=8yus5oYXl6bMKdES
aliyun.nls.tts.voice=yina
aliyun.nls.slp.url=
aliyun.nls.tts.addr=https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts

sms.code.chuanglan.channel.id=119

ai.call.platform.back.type.variables.url=http://***********/apiDialogFlow/variable/getVariableListForType?dialogFlowId=%d&type=%d
ai.call.platform.back.url=http://***********/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d&collectionName=%s
ai.call.platform.back.check.url=http://***********/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.standardNotifyModel.predict.url=http://**********:9898/predict
ai.call.platform.back.exist.url=http://***********/apiDialogFlow/dialogFlow/isPlaceholderExist
ai.call.platform.back.analyze.phoneWithFile.url=http://***********:8120/apiAnalyzePhone/analyze/withMultipartFile
ai.call.platform.back.dialog.params=http://***********/apiDialogFlow/dialogFlow/getDialogFlowExtInfoInner?dialogFlowId=%d
ai.call.platform.back.analyze.dictionary.url=http://**************:2466
ai.call.platform.back.timeEntity.url=http://**************:2612/extract
ai.call.platform.back.addrEntity.url=http://**************:2602/
ai.call.platform.back.gender.url=http://***********:6869/gender
ai.call.platform.back.emotion.url=http://***************:9018/emotion_recognition
ai.call.platform.back.role.url=http://************:5101/
ai.call.platform.nlp.knowledge.search.url=http://***********:8860/aicc/knowledge/quickSearch
ai.call.platform.nlp.robotLog.url=http://***********:8860/aicc/robot/operationLog?tenantId=%d&robotId=%d
ai.call.platform.nlp.shareKnowledgeList.url=http://***********:8860/aicc/knowledge/getShareKnowledgeList
ai.call.platform.nlp.syncShareKnowledge.url=http://***********:8860/aicc/knowledge/syncShareKnowledge

algorithm.similarSentence.generate.url=http://121.36.172.209:8990/gen

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=wxd63fa0c76392f30d
wechat.appsecret=bd8188bbea0b83ab3fbdced3d0d1b043
wechat.token=123456
wechat.aeskey=bo3oEon3nQ7VabeJ3

wechat.miniapp.appid=wxdc1924a8ddfc37f3
wechat.miniapp.appsecret=352ac81b014ca1d273ff800c6d16d553

#### ope\u4F01\u4E1A\u5FAE\u4FE1\u767B\u9646 ####
enterprise.wx.corpid=ww8ac24407dcd79e05
enterprise.wx.agentid=1000016
enterprise.wx.corpsecret=aC789NeGEHTRT2kqKNeb6pphxAV2WR2jLVmoJ_jE_70

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=wwl3G3nH7cPlLzRdUgi1H73DFQ7FqAxGjwNXqB-8kgg
wechat.template.telLineAlertMsg=2tPZw1mQfJ9bdC6FG1McC58Pzp7DgmW656JCTCtF5UI
wechat.template.noRobotAvailableAccountDebtMsg=i5YqCzPrNvhCrUAa2yIatvBKFZyVV4kM_aPIUztdWqs
wechat.template.earlyWarningMsg=cZL319ryviomjprt__omCs8wmHg6PNSx0prxF6RzTm8
wechat.template.callJobFinishMsg=KGcW-f4OIT7YXS7IlPl6y9JeL8kooZKiCIskUBFmEbg
wechat.template.insufficientBalanceMsg=SMpDtgKvWR9myMMiLks9wET1hmV9VxEgg27VMHV4clg
wechat.template.callJobCompletionPercentageMsg=Mk4iTdYGf8f86v7r583OE3gZw_CLf3z95h3uluYmyLw
wechat.template.tenantAccountAutoSet=CNWrrsOsH8egGca-3QVbkUhOBY6fnpXLpREUJa9HQS0

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=https://crm.yiwise.com
wechatcp.frontend.url=https://aicc-daily.yiwise.com

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5BA2\u6237\u5BFC\u5165\u660E\u7EC6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.assignFile=\u5206\u914D\u4EBA\u5DE5\u5916\u547C\u4EFB\u52A1
import.transferFile=\u5BA2\u6237\u5BFC\u5165\u60C5\u51B5
import.chunkSize=100
export.chunkSize=100

import.rearrange.url=http://aicc-daily.yiwise.com/apiImport/rearrange/?ossKey=%s

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### prodocut document upload config ####
oss.productdocument.webDirectUpload.max=**********
oss.productdocument.root=ProductDocument/

oss.ttsaudition.root=TtsAudition/

oss.welcome.msg.root=WelcomeMsg

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload
#### \u8D44\u8D28\u8BA4\u8BC1\u6750\u6599 ####
oss.upload.authentication=authentication
#### \u5E2E\u52A9\u4E2D\u5FC3 ####
oss.upload.helpcenter=helpcenter

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

oss.avatar.upload.root=AvatarFileUpload

#### callback url #####
crm.url=https://crm.yiwise.com

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000

###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.outbound.port=8151
freeswitch.esl.ip=**********
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon


######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=144
free_user.dialog_flow_id.finance=144
free_user.dialog_flow_id.fitment=144
free_user.dialog_flow_id.back=144
free_user.dialog_flow_id.debt=144
free_user.dialog_flow_id.power=144
free_user.dialog_flow_id.telecom=144
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

peers.queuedPacketNum=15

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=1
cs.seat.freeswitchGroupId=1
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=98

###### first encrypt salt ######
first.encrypt.salt=geelysdafaqj23ou89ZXcj@#$@#$#@KJdjklj;D../dSF.,
first.digest.times=1

# bert
bert.url=http://**********:8125
yiwise.ask.use=1
yiwise.ask.url=https://crm.yiwise.com/apiAsk/ask?dialogFlowId=%d&text=%s
liepin.ask.use=0
liepin.ask.url=https://crm.yiwise.com/apiAsk/liepinAsk?dialogFlowId=%d&text=%s
liepin.ask.dialogFlowId=0

question.length=10
caitong.ask.dialogFlowId=5052

weibao.tenant=-1381
weibao.startcall.url=https://tms-sit.wesure100.com/api/v1/public/cop/callBothRequest
weibao.callrecord.callback.url=https://tms-sit.wesure100.com/api/v1/public/callinfo/intention/notify

tongdun.tenant=916

# \u524D\u540Eusersaytext\u5408\u5E76\u7684\u6682\u505C\u65F6\u95F4
user.say.text.time=600

# local \u672C\u5730\u5316 aliyun \u7EBF\u4E0A
rocketmq.type=aliyun
rocketmq.name-server=*************:9876

# \u652F\u4ED8\u5B9D\u8FD4\u56DE\u548C\u56DE\u8C03
alipay.boss.returnUrl=https://boss.yiwise.com/apiBoss/aliPay/payReturn
alipay.boss.notifyUrl=https://boss.yiwise.com/apiBoss/aliPay/payNotify
alipay.boss.backUrl=https://boss.yiwise.com/#/accountInventory/
alipay.crm.returnUrl=https://crm.yiwise.com/apiEngine/aliPay/payReturn
alipay.crm.notifyUrl=https://crm.yiwise.com/apiEngine/aliPay/payNotify
alipay.crm.backUrl=https://crm.yiwise.com/#/systemManagement/line
alipay.aicc.returnUrl=https://aicc.yiwise.com/active-enterprise/account-manage/info
alipay.aicc.backUrl=https://aicc.yiwise.com/enterprise-center/account-manage/info
alipay.doudian.aicc.returnUrl=https://dou-magicbot.tanyibot.com/home

robot.asr.type=yiwise

#### YiwiseAsrGateway\u5730\u5740 ####
yiwise.asr.gatewayUrl=http://*************:6060
yiwise.asr.accessKeyId=
yiwise.asr.accessKeySecret=
yiwise.asr.AsrClientTokenGetterClazz=com.yiwise.core.service.yiwise.asr.AsrClientTokenGetterImpl

yiwise.asr.file.gatewayUrl=http://************:6060
yiwise.asr.file.accessKeyId=0peS8KMJ
yiwise.asr.file.accessSecret=vGpi8XgOUN7KV0gg2t1i

nlp.prefix=http://***********:8860
nlp.beforeChat=http://***********:8860/aicc/speech/beforeChat
nlp.afterChat=http://***********:8860/aicc/speech/afterChat
nlp.chat=http://***********:8860/aicc/speech/chat
nlp.publishCallBack=http://***********:8860/aicc/speech/createSnapshot
nlp.entityList=http://***********:8860/apiIntegration/tanyi/entityList
nlp.knowledgeList=http://***********:8860/aicc/knowledge/knowledgeList?tenantId=%s&robotId=%s&pageSize=%s&pageNum=%s&keyword=%s&knowledgeId=%s
nlp.login=http://***********:8860/apiPlatform/entry/singleLogin
nlp.getRobotInfo=http://***********:8860/aicc/robot/getRobotInfo
nlp.createRobot=
nlp.access.secret=1VmaIyTrvkJiEHAFjArqp9Z3lD5ufj8aIIgmf0W9OiZR1GI7



# jd ai platform
jd.app.key=9344BBF819A36B7B6E92635F7D7455DB
jd.app.secret=2F4E3CEAB5FB58A0C6E75B41D57ABC06
jd.new.user.url=https://crm.yiwise.com
jd.redirect.uri=https://crm.yiwise.com/apiPlatform/jd/auth
jd.follower.id=1753

# tencnet asr
tencent.asr.appId=1305570814
tencent.asr.secretId=AKIDsd9ON6XhbqsfBQO5WhbEagW4MN8bniNl
tencent.asr.secretKey=8zauylHXYLsBF2atwjHadRzIMAi03qNe


# Customer Service console config
consistent.dialog.beep.url=DefaultBeeps/consistentDialogBeep.wav
forcasting.callout.beep.url=DefaultBeeps/forecastingCalloutBeep.wav
uncommon.hangup.beep.url=DefaultBeeps/uncommonHangupBeep.wav
queue.beep.url=DefaultBeeps/queueBeep.wav

#aicc
nlp.robot.chat.url=http://***********:8860/aicc/text/chat?tenantId=%d&robotId=%d&text=%s&sessionId=%s

wechat.component.appId=wx4e22167f4b372558
wechat.component.appSecret=4c4133eab8541e403b00e73c3140286c
wechat.component.aesKey=DG79bsLYzBkRmNShgZviHUXmp81mVQCL7fFDTjo6QAC
wechat.component.token=nefljCILR1exxlPGznajHvgjaYA7WNrQ
wechat.component.preAuthCallback.url=https://aicc.yiwise.com/customerServiceStage/moreConfig/all?type=%s
wechat.component.accessToken.url=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
wechat.component.userInfo.url=https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN
wechat.miniapp.scheme.h5.url=https://wechat.yiwise.com/wxapp-h5/?link=%s
wechat.miniapp.tryDialogFlow.h5.url=https://hwc-miniapp.tanyibot.com/experience/#/callout?qrCodeId=%s
wechat.miniapp.qrCodeImport.h5.url=https://wechat.yiwise.com/robot-call-job-import-qr?qrCodeId=%s
# qc algorithm interfaces
qc.word.frequency.update=http://**************:2603/upload
qc.word.frequency.query=http://**************:2603/query
qc.rule.tag.trigger=http://**********:2606/predict

changhe.tenant.id=397

# \u6817\u5B50\u96C6\u6210
lizi.ak=28c89eb6d96d4b97ac7e950d9e858688
lizi.as=0eee06561c3443eea06d5cb781409d50


## simple \u7B80\u6613\u7248\u672C \u6709\u4E9B\u529F\u80FD\u6A21\u5757\u4E0D\u62C6\u5206 advanced \u7EBF\u4E0A\u7248\u672C \u529F\u80FD\u6A21\u5757\u62C6\u5206
publish.module.type=advanced

# \u4EA7\u54C1\u6A21\u5F0F online \u7EBF\u4E0A \u5305\u542B\u4E00\u77E5\u7684\u4E13\u6709\u54C1\u529F\u80FD other \u4E0D\u5305\u542B\u4E00\u77E5\u4E13\u6709\u529F\u80FD \u4E3A\u5176\u4ED6\u5BA2\u6237\u90E8\u7F72
product.type=online

server.call.in.count=100

# yiwise tts
tts.yiwise.url=http://tts-test.yiwise.com:8010/v1/tts/single
tts.yiwise.auth.url=http://tts-test.yiwise.com:8010/v1/auth/token
tts.yiwise.user.name=yiwise
tts.yiwise.user.password=_yiwise

#### Huawei callin demo ####
huawei.callin.dialogFlowId=6346

training.dialogFlow.nodeArriveStats.enabled=true
#### qiyu ####
qiyu.back.url=https://qytest.netease.com/openapi/crm/thirdParty/syncCrmInfo?appKey=%s&time=%s&checksum=%s
qiyu.menu.url=https://qytest.netease.com/openapi/icoutcall/syncthirdparty/appinfo?appKey=%s&time=%s&checksum=%s
qiyu.template.get.url=https://qytest.netease.com/openapi/icoutcall/get/sms/templates?appKey=%s&time=%s&checksum=%s
qiyu.template.send.url=https://qytest.netease.com/openapi/icoutcall/smstask/create?appKey=%s&time=%s&checksum=%s
qiyu.template.result.url=https://qytest.netease.com/openapi/icoutcall/smstask/status?appKey=%s&time=%s&checksum=%s
qiyu.callrecord.callback.url=https://qytest.netease.com/openapi/icoutcall/sync/callhistory?appKey=%s&time=%s&checksum=%s
qiyu.staffgroup.get.url=https://qytest.netease.com/openapi/icoutcall/get/staffGroups?appKey=%s&time=%s&checksum=%s
qiyu.staffgroup.upgrade.get.url=https://qytest.netease.com/openapi/kefu/icoutcall/staffgroup/list?appKey=%s&time=%s&checksum=%s
qiyu.staff.upgrade.get.url=https://qytest.netease.com/openapi/kefu/icoutcall/staff/list?appKey=%s&time=%s&checksum=%s
qiyu.push.mail.url=https://qytest.netease.com/openapi/icoutcall/pushMail?appKey=%s&time=%s&checksum=%s
qiyu.cc.transfer.url=59.111.96.125:6280
qiyu.appkey=a9e6ed3f047270d789d1d4c0d4a118c9@icoutcall@
qiyu.secret=8f9390fbac2fa48d0ffe8fc779d02480
qiyu.distributor=5025
qiyu.dialogflow.id=14967
#### e\u7B7E\u5B9D ####
esign.openapi.url=https://openapi.esign.cn
esign.openapi.appId=**********
esign.openapi.secret=a7eb25304a4a636d0c23c235f45477ac
esign.openapi.agentAccountId=b635c048022440b680a25270aa151108
esign.openapi.callback=https://hwc-aicc.tanyibot.com/apiEngine/authentication/eCallBack
#### \u97F3\u8272\u514B\u9686\u5C0F\u7A0B\u5E8F ####
timbre.clone.miniapp.push.app=wx774e47f56821c847,35c21264d55a8430487a28d34e0a988b
timbre.clone.miniapp.nopush.app=wx32b692f9f50afc52,e9fb7afbca56be754a6516b1af69c276

sunfeng.tenant=1692
sunfeng.apikey=d9cf325f97d84f43ae5aa251cba04d81
sunfeng.virtualnumber.get.url=http://prs-new.sit.sf-express.com:23222/api/recruitcenter/out/ty_virtual
sunfeng.login.user=2850

sunfeng.tenant.dev=1692
sunfeng.apikey.dev=d9cf325f97d84f43ae5aa251cba04d81
sunfeng.virtualnumber.get.url.dev=http://prs-new.sit.sf-express.com:23222/api/recruitcenter/out/ty_virtual

global_sms_send=true
global_message_center_customer_interact=true
global_message_center_work_order=true
global_io_history_clue=true
global_user_center_contact_manager=true
global_user_center_back_old=true
main_sms_platform=true
main_smart_clue=true
main_seat_help=true
main_work_order=true
main_code_login=true
main_wechat_login=true
ope_customer_whitelist_share=true
ope_customer_contract_num=true
ope_customer_back_money=true
ope_customer_callout=true
ope_customer_callin=true
ope_customer_qc=true
ope_customer_voice=true
ope_customer_text=true
ope_customer_yibrain_voice=true
ope_customer_yibrain_text=true
ope_customer_zhineng_xiansuo=true
ope_customer_sms_center=true
ope_customer_aiassistant=true
ope_customer_ivr=true
ope_customer_login_crm=true
ope_customer_login_aicc=true
ope_customer_forbid_account=true
ope_customer_view_login_log=true
ope_whitelist_share=true
ope_utils=true
ope_concurency=true
ope_cur_concurency=true
ope_operation_log=true
ope_clue_collect=true
ope_huawei_form=true
customerServiceStage=true
qc=true
engine=true
callout_phoneTaskV2_BOTV2=true
callout_phoneTask_genderMood=true
callout_billing=true
callin_IVR=true
enterprise_account_manage_info=true
enterprise_account_manage_order=true
enterprise_account_manage_billing=true
enterprise_account_manage_communication=true
enterprise_account_manage_wechatCp=true
workorder=true
customerCenter=true
ope_dialog_template=true
ope_dialog_audit=true
global_voice_seat=true
callout_phoneTaskV2_intent_wechat_push=true
callout_phoneTaskV2_sms_push=true
callout_setting_stats_range=true
#default BackgroundSound url
bot.backgroundSound.url=https://ai-call-platform.obs.cn-east-3.myhuaweicloud.com/common/audio/office_talk.wav

# scrm
scrm.queryJuziBot.url=https://scrm-test.yiwise.com/scrm/juzi/getJuziBotListForAICC
scrm.queryJuziCode.url=https://scrm-test.yiwise.com/scrm/api/juzi/getJuziCode
scrm.getJuziToken.url=https://scrm-test.yiwise.com/scrm/api/juzi/getJuziToken
scrm.getJuziUser.url=https://scrm-test.yiwise.com/scrm/api/juzi/getJuziUserInfo
scrm.queryTenantList.url=https://scrm-test.yiwise.com/scrm/tenant/listForAICC
scrm.queryTenantInfo.url=https://scrm-test.yiwise.com/scrm/tenant/infoForAicc
scrm.queryWechatList.url=https://scrm-test.yiwise.com/scrm/wxwork/getWxworkUserListForAICC
scrm.queryOrgList.url=https://scrm-test.yiwise.com/scrm/wxwork/getWxworkOrgListForAicc
scrm.queryWechatByIds.url=https://scrm-test.yiwise.com/scrm/wxwork/getWxworkUserListByIdsForAICC
scrm.queryOrgByIds.url=https://scrm-test.yiwise.com/scrm/wxwork/getOrgListByIdsForAicc
scrm.addJuziWechatFriend.url=https://scrm-test.yiwise.com/scrm/juzi/applyJuziAddFriend
scrm.addWechatFriend.url=https://scrm-test.yiwise.com/scrm/wxwork/autoSendAddApply
scrm.queryManualWechatList.url=https://scrm-test.yiwise.com/scrm/user/listAllForAicc
scrm.addManualWechatFriend.url=https://scrm-test.yiwise.com/scrm/tobeadd/aiccAddToBeAddCustomer
scrm.queryWechatTag.url=https://scrm-test.yiwise.com/scrm/customerTag/selectWechatGroupAICC
scrm.saveWelcomeMsg.url=https://scrm-test.yiwise.com/scrm/tobeadd/aiccSaveWelcomeMsg
scrm.getWelcomeMsg.url=https://scrm-test.yiwise.com/scrm/tobeadd/aiccGetWelcomeMsg?robotCallJobId=%s
scrm.batchGetWelcomeMsg.url=https://scrm-test.yiwise.com/scrm/tobeadd/aiccBatchGetWelcomeMsg
scrm.deleteWelcomeMsg.url=https://scrm-test.yiwise.com/scrm/tobeadd/aiccDeleteWelcomeMsg?robotCallJobId=%s
scrm.getEnterpriseArticle.url=https://scrm-test.yiwise.com/scrm/enterpriseArticle/getEnterpriseArticleByTenantId
scrm.getEnterpriseFile.url=https://scrm-test.yiwise.com/scrm/enterpriseFile/getEnterpriseFileByTenantId
scrm.addWechatAlert.url=https://scrm-test.yiwise.com/scrm/wxwork/sendJobTerminateMsg
scrm.getGrowCustomerAppList.url=https://scrm-test.yiwise.com/scrm/growCustomerApp/aiccGetList
scrm.directCallback.url=https://scrm-test.yiwise.com/scrm/ma/aiccOpeation/directlyCallBack
scrm.get.template.url=https://scrm-ope-test.yiwise.com/scrm/exclusiveLink/getQcCode
scrm.getLinkUrlTemplate.url=https://scrm-test.yiwise.com/scrm/linkUrlTemplate/aiccGetLinkUrlTemplates
scrm.getStatisticalData.url=https://scrm-test.yiwise.com/scrm/getStatisticalData
scrm.updateScrmInfo=https://scrm-test.yiwise.com/api-web/tenantManage/updateScrmInfo
scrm.getScrmRpaRobotCount=https://scrm-test.yiwise.com/api-web/tenantManage/getScrmRpaRobotCount?aiccTenantId=%d&scrmTenantId=%d
scrm.testGetQRCode=https://scrm-test.yiwise.com/api-web/tenantManage/testGetQRCode?ip=%s
scrm.rpaServerList=https://scrm-test.yiwise.com/api-web/tenantManage/rpaServerList?aiccTenantId=%d&scrmTenantId=%d
scrm.rpaServerHistoryList=https://scrm-test.yiwise.com/api-web/tenantManage/rpaServerHistoryList?aiccTenantId=%d&scrmTenantId=%d
scrm.robotList=https://scrm-test.yiwise.com/api-web/tenantManage/robotList?aiccTenantId=%d&scrmTenantId=%d
scrm.robotHistoryList=https://scrm-test.yiwise.com/api-web/tenantManage/robotHistoryList?aiccTenantId=%d&scrmTenantId=%d
scrm.severUnbindList=https://scrm-test.yiwise.com/api-web/serverResource/unbindList
scrm.addServerResource=https://scrm-test.yiwise.com/api-web/serverResource/addServerResource
scrm.serverList=https://scrm-test.yiwise.com/api-web/serverResource/list?pageNum=%d&pageSize=%d
scrm.updateServerResource=https://scrm-test.yiwise.com/api-web/serverResource/update
scrm.getAiccTenantIds=https://scrm-test.yiwise.com/api-web/tenantManage/getAiccTenantIds
scrm.addRpaServer=https://scrm-test.yiwise.com/api-web/tenantManage/addRpaServer
scrm.editRpaServerAccountCount=https://scrm-test.yiwise.com/api-web/tenantManage/editRpaServerAccountCount?scrmTenantRelationId=%d&count=%d&userId=%d
scrm.delRpaServer=https://scrm-test.yiwise.com/api-web/tenantManage/delRpaServer?scrmTenantRelationId=%d&userId=%d
scrm.postponeRpaServer=https://scrm-test.yiwise.com/api-web/tenantManage/postponeRpaServer
scrm.updateRobotCount=https://scrm-test.yiwise.com/api-web/tenantManage/updateRobotCount
scrm.selectByOrganizationIdAuth=https://scrm-test.yiwise.com/api-web/tenantManage/selectByOrganizationIdAuth?scrmTenantId=%d
scrm.selectByOrganizationId=https://scrm-test.yiwise.com/api-web/tenantManage/selectByOrganizationId?organizationId=%d
scrm.selectByTenantAndOrganizationIdList=https://scrm-test.yiwise.com/api-web/tenantManage/selectByTenantAndOrganizationIdList
scrm.getScrmTenantInfo=https://scrm-test.yiwise.com/api-web/tenantManage/getScrmTenantInfo?scrmTenantId=%d
scrm.getOrgUserTree=https://scrm-test.yiwise.com/api-web/tenantManage/getOrgUserTree
scrm.scrmUserListAll=https://scrm-test.yiwise.com/api-web/tenantManage/scrmUserListAll
scrm.getOrgAndUserNams=https://scrm-test.yiwise.com/api-web/tenantManage/getOrgAndUserNams
scrm.cancelBatchJob.url=https://scrm-test.yiwise.com/scrm/batch/cancelBatchJob?jobInstanceId=%s
scrm.setUser2Redis.url=https://scrm-test.yiwise.com/scrm/user/setUser2redis/%s
scrm.request.url=https://scrm-test.yiwise.com

xiaoke.default.deployment.userId=DEFAULT,15062222222

sms.callback.chuanglan.mms=https://aicc-daily.yiwise.com/apiEngine/chuanglanmms/callback

implement.role=217,220,270
dialog.role=4,86,135,136

wechat.cp.group.url.ysld=
wechat.cp.group.url.weibao=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fe8dcbc9-7dcc-4be2-8f89-afe70121db66
wechat.cp.group.url.tf=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6465a270-4066-4338-a2bd-be29d43e39d0
wechat.cp.group.url.cl=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4064a00f-c653-49c2-aacb-a47b5478e97b
wechat.cp.group.url.origins=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9e850330-5de6-46f5-b93b-776f73bf54f4

# ç®æ³éç½®
algorithm.register.url=http://***********/register
algorithm.register.env=AICC-FINANCE-HUAWEI-PROD
algorithm.redisKey.url=https://hwc-aicc.tanyibot.com/apiDialogFlow/algorithm/redisKeyList
algorithm.data.url=https://hwc-aicc.tanyibot.com/apiDialogFlow/algorithm/list
algorithm.callback.url=https://hwc-aicc.tanyibot.com/apiDialogFlow/algorithm/callback
algorithm.predict.url=http://***********/predict
algorithm.verbal.training.url=http://***********/predict
algorithm.domain.list.url=http://***********/get_domain_names
algorithm.patch.list.url=http://***********/get_patch_keys

privacyNumber.fengyin.url=http://testapi.fyprivacy.com

#åèéªæµéç½®
sms.chuanglan.appId=n4vjLXHN
sms.chuanglan.appKey=2q1q4Fxk
sms.chuanglan.flash.verify.url=https://api.253.com/open/flashVerify


#HBase
hbase.zookeeper.quorum=hb-bp1jc7159v4xsw10e-001.hbase.rds.aliyuncs.com
hbase.zookeeper.property.clientPort=2181

qiweibao.addFriend.url=https://oapi-qyb.beta.wxb.com/yizhi/addRecord
quanliang.addFriend.url=https://api.aquanliang.com/gateway/qopen/AddExtUserByRule
quanliang.getAccessToken.url=https://api.aquanliang.com/gateway/qopen/GetAccessToken
quanliang.appkey=co2280d729654041f5
quanliang.appSecret=qowG3yiK6qDdnZM0NHhtCpTPD2IKXQNGHZQD5wsO53Ua33BeRhRt
quanliang.encodingAESKey=ceb6d87c731f477f95494620988efd03

v3.dialogFlow.url.prefix=https://aicc-daily.yiwise.com/

extension.judge.answer.server=ws://***********:9091/audio_predict

service.name=ai-call-service

jxs.server.url=https://yizhi-baize-api.staging.ukuaiqi.com
jxs.push.url=https://openapi.yiwise.com/apiOpen/v1/job/importCustomerJxs
rtp.miss.packets=80

#### \u56de\u8c03\u5806\u79ef\u9884\u8b66\u914d\u7f6e ####
feishu.warn.phoneNumberSet=18753115285,17769911620
feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/12ab7732-18d7-4e70-9b82-e06225b4e252

zhejiao.tenant=916
zhejiao.call.in.url=https://zsdltst.zj.sgcc.com.cn/zj_eqa/open/bcpiss/rest/msg01
zhejiao.private.key.name=MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIhIs/3wz/nod7Ff/0UMzyK4gRCjPLqSfYkxxtlLn8GEn5Tg9kgKEl+CBiVad3w1afgFivaTHHI7xCC9zyulFkKQ3Q5IuouBkaY2+hKUPDzRRer3RmxUcNM4e5IUfDwG//8Hh69Q0kEHyD22lXGvo/kQnoUyhH+RjZ1UVAJAzj7lAgMBAAECgYAVh26vsggY0Yl/Asw/qztZn837w93HF3cvYiaokxLErl/LVBJz5OtsHQ09f2IaxBFedfmy5CB9R0W/aly851JxrI8WAkx2W2FNllzhha01fmlNlOSumoiRF++JcbsAjDcrcIiR8eSVNuB6ymBCrx/FqhdX3+t/VUbSAFXYT9tsgQJBALsHurnovZS1qjCTl6pkNS0V5qio88SzYP7lzgq0eYGlvfupdlLX8/MrSdi4DherMTcutUcaTzgQU20uAI0EMyECQQC6il1Kdkw8Peeb0JZMHbs+cMCsbGATiAt4pfo1b/i9/BO0QnRgDqYcjt3J9Ux22dPYbDpDtMjMRNrAKFb4BJdFAkBMrdWTZOVc88IL2mcC98SJcII5wdL3YSeyOZto7icmzUH/zLFzM5CTsLq8/HDiqVArNJ4jwZia/q6Fg6e8KO2hAkB0EK1VLF/ox7e5GkK533Hmuu8XGWN6I5bHnbYd06qYQyTbbtHMBrFSaY4UH91Qwd3u9gAWqoCZoGnfT/o03V5lAkBqq8jZd2lHifey+9cf1hsHD5WQbjJKPPIb57CK08hn7vUlX5ePJ02Q8AhdZKETaW+EsqJWpNgsu5wPqsy2UynO
zhejiao.sm4.key=21A81BD0A039227B


process.topic.key=ProcessTopicKey
writer.topic.key=WriterTopicKey

ysld.mainBrandId=12

sfl.feishu.warn.phoneNumberSet=15868136161
sfl.feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/2b651fcc-0d5c-4b1f-bece-5a41398cbd9e
sfl.feishu.warn.tenantSet=2269

ysld.black.list.feishu.warn.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/3dde89f2-0cd9-4d59-8276-29fa1827c04f

bingjian.daily.feishu.webHook=https://open.feishu.cn/open-apis/bot/v2/hook/3dde89f2-0cd9-4d59-8276-29fa1827c04f
bingjian.daily.feishu.tenantId=916
recording.channel.switching.tenants=916

ObjectStorage.useAuth = true
ObjectStorage.clazz = com.yiwise.middleware.objectstorage.obs.OBSHelper
ObjectStorage.obs.basicUrlPrefix = https://ai-call-platform.obs.cn-east-3.myhuaweicloud.com
ObjectStorage.obs.defaultBucket = ai-call-platform
ObjectStorage.obs.endpoint = obs.cn-east-3.myhuaweicloud.com
ObjectStorage.obs.ak = RE9W8GQ1EASQOOL9FAO7
ObjectStorage.obs.sk = VttsjgbakehR0li3KF4IbvYaaEF1bjUs9Hq2HeMu

MessageQueue.clazz = com.yiwise.middleware.mq.rocketmq.RocketMqFactory
rocket.mq.instance.config = [{"mqInstance": "DEFAULT","accessKey": "FUM0LWRZFK4Z9QZL3YBY","secretKey": "wLVuTHpu38ELds4McQ5vQ6i0i0HYBnLmce9K1CUC","namesrvAddr": "192.168.8.169:8100;192.168.8.229:8100"},{"mqInstance": "BATCHMQ","accessKey": "FUM0LWRZFK4Z9QZL3YBY","secretKey": "wLVuTHpu38ELds4McQ5vQ6i0i0HYBnLmce9K1CUC","namesrvAddr": "192.168.8.169:8100;192.168.8.229:8100"},{"mqInstance": "BILLING","accessKey": "FUM0LWRZFK4Z9QZL3YBY","secretKey": "wLVuTHpu38ELds4McQ5vQ6i0i0HYBnLmce9K1CUC","namesrvAddr": "192.168.8.169:8100;192.168.8.229:8100"}]

