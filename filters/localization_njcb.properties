#### \u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E ####
db.master.url=**********************************************************************************************************************************************************************************************************************************************************
db.master.username=root
db.master.password=123456
db.polardb.url=**********************************************************************************************************************************************************************************************************************************************************
db.polardb.username=root
db.polardb.password=123456
db.slave.one.url=******************************************************************************************************************************************************************************************************************************************************************
db.slave.one.username=root
db.slave.one.password=123456
db.driver=com.mysql.jdbc.Driver
db.initialSize=5
db.maxActive=20
db.minIdle=5

#### spring\u914D\u7F6E ####
spring.profiles.active=localization
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchqueue.port=8140

apollo.bootstrap.enabled=false

### mq router ###
mq.router.ip=
mq.router.port=7080

#### localization params
network.enabled=false
sms.enabled=false
wx.enabled=false
freeswitch.url=traininglocalization.tanyibot.com
freeswitch.port=7443
import.template.url=

#### redis\u76F8\u5173\u914D\u7F6E ####
redis.hostname=redislocalization.tanyibot.com
redis.port=6379
redis.password=654321
redis.dbIndex=1

#### storage type
object.storage.type=fastdfs

#### oss\u76F8\u5173\u914D\u7F6E ####
oss.basicUrl=
oss.endpoint=
oss.accessId=
oss.accessKey=
oss.defaultBucket=
oss.useOssAuth=false

#### minioéç½® ####
minio.endpoint=http://127.0.0.1:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.defaultBucket=ai-call-platform

#### fastDFS ####
fastDFS.tracker_server=fastdfslocalization.tanyibot.com:22122


#### LogHub\u76F8\u5173\u914D\u7F6E ####
logHub.endpoint=
logHub.accessId=
logHub.accessKey=
logHub.projectName=
logHub.logstore=


#### \u963F\u91CC\u4E91mq ####
aliyun.ons.rocketmq.accessKey=
aliyun.ons.rocketmq.secretKey=
aliyun.ons.rocketmq.topic=
aliyun.ons.rocketmq.namesrvAddr=
aliyun.ons.rocketmq.producerId=
aliyun.ons.rocketmq.groupId=
aliyun.ons.rocketmq.callback.topic=
aliyun.ons.rocketmq.callback.producerId=
aliyun.ons.rocketmq.callback.groupId=
aliyun.ons.rocketmq.asrRecognition.topic=
aliyun.ons.rocketmq.asrRecognition.producerId=
aliyun.ons.rocketmq.asrRecognition.groupId=

#### \u963F\u91CC\u4E91nls ####
nls.server.type=local
aliyun.nls.defaultServerAddr=ws://asrlocalization.tanyibot.com:8101/ws/v1
aliyun.nls.accessKeyId=default
aliyun.nls.accessKeySecret=default
aliyun.nls.defaultDialogFlowAsrModelAppkey=default
aliyun.nls.tts.appkey=default
aliyun.nls.tts.voice=xiaoyun

#### \u4E91\u7247\u77ED\u4FE1\u914D\u7F6E ####
yunpian.sms.apiKey=
yunpian.sms.financeApiKey=
yunpian.sms.marketingApiKey=
yunpian.sms.callbackHost=
yunpian.template.callback.uri=
yunpian.sms.finance.tenant=

ai.call.platform.back.url=https://crmlocalization.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d
ai.call.platform.back.check.url=https://crmlocalization.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.exist.url=https://crmlocalization.tanyibot.com/apiDialogFlow/dialogFlow/isPlaceholderExist

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=
wechat.appsecret=
wechat.token=
wechat.aeskey=

wechat.miniapp.appid=
wechat.miniapp.appsecret=

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=
wechat.template.telLineAlertMsg=
wechat.template.noRobotAvailableAccountDebtMsg=
wechat.template.earlyWarningMsg=
wechat.template.callJobFinishMsg=
wechat.template.insufficientBalanceMsg=
wechat.template.callJobCompletionPercentageMsg=

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5ba2\u6237\u5bfc\u5165\u660e\u7ec6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.chunkSize=100
export.chunkSize=100

import.rearrange.url=https://crmlocalization.tanyibot.com/apiImport/rearrange/?ossKey=%s

#### mongo ****
mongodb.primary.host=crmlocalization.tanyibot.com
mongodb.primary.port=27017
mongodb.primary.name=ai_call_engine
mongodb.primary.user=dba_localization
mongodb.primary.password=pwd_localization
mongodb.primary.authentification=true

mongodb.secondary.host=crmlocalization.tanyibot.com
mongodb.secondary.port=27017
mongodb.secondary.name=ai_call_engine
mongodb.secondary.user=dba_localization
mongodb.secondary.password=pwd_localization
mongodb.secondary.authentification=true

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

#### callback url #####
crm.url=

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000


###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon

######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=0
free_user.dialog_flow_id.finance=0
free_user.dialog_flow_id.fitment=0
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

peers.queuedPacketNum=15

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=1
cs.seat.freeswitchGroupId=1
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=

###### first encrypt salt ######
first.encrypt.salt=etjtv8snxucwk@5d7oj7m<6ftm<?y?du6;:jj@epqli260l
first.digest.times=1
