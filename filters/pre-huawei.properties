#### \u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E ####
db.master.url=*****************************************************************************************************************************************************************************************************************************************
db.master.username=root
db.master.password=OTwPq2qv53GY^q
db.polardb.url=*****************************************************************************************************************************************************************************************************************************************
db.polardb.username=root
db.polardb.password=OTwPq2qv53GY^q
db.polardb.master.url=*****************************************************************************************************************************************************************************************************************************************
db.polardb.master.username=root
db.polardb.master.password=OTwPq2qv53GY^q
db.slave.one.url=*****************************************************************************************************************************************************************************************************************************************
db.slave.one.username=root
db.slave.one.password=OTwPq2qv53GY^q
db.driver=com.mysql.jdbc.Driver
db.new.polardb.url=*****************************************************************************************************************************************************************************************************************************************
db.new.polardb.username=root
db.new.polardb.password=OTwPq2qv53GY^q
db.new.polardb.master.url=*****************************************************************************************************************************************************************************************************************************************
db.new.polardb.master.username=root
db.new.polardb.master.password=OTwPq2qv53GY^q
db.initialSize=5
db.maxActive=20
db.minIdle=5

#### spring\u914D\u7F6E ####
spring.profiles.active=pre-huawei
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchjob.port=8140
server.callin.port=8150
server.smsjob.port=8160
server.esl.port=8170
server.freeswitch.port=8190
server.qcjob.port=8200
server.csCallJob.port=8210
server.qcweb.port=8220
server.text.port=8230
server.wechatJob.port=8250

apollo.bootstrap.enabled=false

### mq router ###
mq.router.ip=**********
mq.router.port=7080

#### localization params
network.enabled=true
sms.enabled=true
wx.enabled=true
cs.enabled=true
freeswitch.url=training.huaweipre.tanyibot.com
freeswitch.port=7443
import.template.url=https://obs-yizhi-waihu.obs.cn-north-4.myhuaweicloud.com:443/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx
nlp.url=https://aipre.huawei.tanyibot.com

#### redis\u76F8\u5173\u914D\u7F6E ####
redis.hostname=*************
redis.port=6379
redis.password=OTwPq2qv53GY^q$L
redis.dbIndex=0
redis.statDbIndex=20

#### storage type
object.storage.type=obs

#### oss\u76F8\u5173\u914D\u7F6E ####
oss.basicUrl=oss-cn-hangzhou.aliyuncs.com
oss.endpoint=http://oss-cn-hangzhou-internal.aliyuncs.com
oss.accessId=LTAI0IFtCuGa2XQO
oss.accessKey=Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
oss.defaultBucket=ai-call-platform
oss.textBucket=ai-call-platform-text
oss.useOssAuth=false

#### minio\u914D\u7F6E ####
minio.endpoint=http://127.0.0.1:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.defaultBucket=ai-call-platform

#### huawei OBS ####
obs.endpoint=obs.cn-north-4.myhuaweicloud.com
obs.defaultBucket=obs-yizhi-waihu
obs.prefix=https://obs-yizhi-waihu.obs.cn-north-4.myhuaweicloud.com:443/

#### huawei cloud common
huawei.cloud.ak=AXMHA3VKV3WR8ZKNR5ZF
huawei.cloud.sk=RmKj7Cywv7Uut0WDgUBODgcpgctQwlHHSxv8D4jq
huawei.cloud.serviceRegion=cn-north-4
huawei.cloud.projectId=0737664fe00026a92f59c0017ab7e20a

#### fastDFS ####
fastDFS.tracker_server=

yiwise.tts.ip=***************:6543
server.job.max.quota=60

#### LogHub\u76F8\u5173\u914D\u7F6E ####
logHub.endpoint=http://cn-hangzhou-intranet.log.aliyuncs.com
logHub.accessId=
logHub.accessKey=
logHub.projectName=ai-call-platform
logHub.logstore=ai-call-platform


#### \u963F\u91CC\u4E91mq ####
aliyun.ons.rocketmq.accessKey=
aliyun.ons.rocketmq.secretKey=
aliyun.ons.rocketmq.namesrvAddr=http://onsaddr.cn-hangzhou.mq-internal.aliyuncs.com:8080
aliyun.ons.rocketmq.topic=pre-common
aliyun.ons.rocketmq.producerId=PID_TANYI_PRE
aliyun.ons.rocketmq.groupId=GID_TANYI_PRE_COMMON
aliyun.ons.rocketmq.callback.topic=pre-callback
aliyun.ons.rocketmq.callback.producerId=PID_TANYI_PRE_CALLBACK
aliyun.ons.rocketmq.callback.groupId=GID_TANYI_PRE_CALLBACK
aliyun.ons.rocketmq.asrRecognition.topic=tanyi-prod-asr
aliyun.ons.rocketmq.asrRecognition.producerId=PID_TANYI_PROD_ASR
aliyun.ons.rocketmq.asrRecognition.groupId=GID_TANYI_PROD_ASR
aliyun.ons.rocketmq.qc.plan.topic=daily_qcplan
aliyun.ons.rocketmq.qc.plan.producerId=PID_DAILY_QCPLAN
aliyun.ons.rocketmq.qc.plan.groupId=GID_DAILY_QCPLAN
aliyun.ons.rocketmq.instanceId=MQ_INST_1765425409792635_BC0bxQxx
aliyun.ons.rocketmq.regionId=mq-internet-access
aliyun.ons.rocketmq.peers.topic=daily_peers
aliyun.ons.rocketmq.peers.producerId=PID_DAILY_PEERS
aliyun.ons.rocketmq.peers.groupId=GID_DAILY_PEERS
aliyun.ons.rocketmq.wechatJob.topic=pre_wechat
aliyun.ons.rocketmq.wechatJob.groupId=GID_PRE_WECHAT_JOB
aliyun.ons.rocketmq.callEventLog.topic=pre_callEventLog
aliyun.ons.rocketmq.callEventLog.groupId=GID_PRE_CALL_EVENT_LOG
aliyun.ons.rocketmq.callEventLog.interruptAnalysis.groupId=GID_PRE_INTERRUPT_ANALYSIS
aliyun.ons.rocketmq.intentRuleMatchFlag.topic=pre_intentRuleMatchFlag
aliyun.ons.rocketmq.intentRuleMatchFlag.groupId=GID_PRE_INTENT_RULE_MATCH
aliyun.ons.rocketmq.dialogStats.topic=pre_dialogStats
aliyun.ons.rocketmq.dialogStats.groupId=GID_PRE_DIALOG_STATS
aliyun.ons.rocketmq.predictLog.topic=pre_predictLog
aliyun.ons.rocketmq.predictLog.groupId=GID_PRE_PREDICT_LOG
aliyun.ons.rocketmq.returnVisit.topic=tanyi_pre_return_visit
aliyun.ons.rocketmq.returnVisit.groupId=GID_PRE_RETURN_VISIT

aliyun.ons.rocketmq.crowd.push.callback.topic=pre_customer_aicc_crowd_push_callback
aliyun.ons.rocketmq.crowd.push.callback.groupId=GID_PRE_CUSTOMER_AICC_CROWD_PUSH_CALLBACK

aliyun.ons.rocketmq.robot.account.analysis.topic=pre_robot_account_analysis

#### \u963F\u91CC\u4E91nls ####
nls.server.type=aliyun
aliyun.nls.defaultServerAddr=wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1
aliyun.nls.accessKeyId=LTAICuX7MOmHW6Oy
aliyun.nls.accessKeySecret=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.nls.defaultDialogFlowAsrModelAppkey=4QQmua3uHGKGwsYD
aliyun.nls.tts.appkey=8yus5oYXl6bMKdES
aliyun.nls.tts.voice=yina
aliyun.nls.slp.url=
aliyun.nls.tts.addr=https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts

#### \u4E91\u7247\u77ED\u4FE1\u914D\u7F6E ####
yunpian.sms.verificationApiKey=abca65e763d31a31edab6df1e60effba
yunpian.sms.apiKey=959553e285a9d2cf89c497be5b592c61
yunpian.sms.financeApiKey=d4dbc473bf623e512bc65ea11dadaa7f
yunpian.sms.marketingApiKey=48eed6536ad50b297d00c6f5d341690a
yunpian.sms.gameApiKey=7531b8a9e7e2f3ef1b345cda4c231c8c
yunpian.sms.socialApiKey=f5068a3de387a02543de782766951d11
yunpian.sms.creditCardApiKey=f71bc23eca5d9c3ccd81fb1b0fc1962e
yunpian.sms.collectionApiKey=912741fbacfffb967c7eb0c4ca67830d
yunpian.sms.callbackHost=https://crmkunpeng.tanyibot.com
yunpian.template.callback.uri=https://crmkunpeng.tanyibot.com/apiEngine/smsTemplate/yunpian/callback
yunpian.sms.finance.tenant=771
sms.general.channel.id=1
sms.code.channel.id=2

ai.call.platform.back.type.variables.url=https://crmpre.huawei.tanyibot.com/apiDialogFlow/variable/getVariableListForType?dialogFlowId=%d&type=%d
ai.call.platform.back.url=https://crmpre.huawei.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d&collectionName=%s
ai.call.platform.back.check.url=http://crmpre.huawei.yiwise.com/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.standardNotifyModel.predict.url=http://**************:10012/predict
ai.call.platform.back.exist.url=https://crmpre.huawei.tanyibot.com/apiDialogFlow/dialogFlow/isPlaceholderExist
ai.call.platform.back.analyze.phoneWithFile.url=https://crmpre.huawei.tanyibot.com/apiAnalyzePhone/analyze/withMultipartFile
ai.call.platform.back.correction.url=http://************:55001/parse
ai.call.platform.back.dialog.params=https://crmpre.huawei.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowExtInfoInner?dialogFlowId=%d
ai.call.platform.back.analyze.dictionary.url=http://**************:2466
ai.call.platform.back.timeEntity.url=http://**************:2612/extract
ai.call.platform.back.addrEntity.url=http://**************:2602/
ai.call.platform.back.gender.url=http://**************:5010/algorithm?url=%s&startOffset=%d&endOffset=%d
ai.call.platform.back.emotion.url=http://***************:5012?s=%s
ai.call.platform.back.role.url=http://************:5101/
ai.call.platform.nlp.knowledge.search.url=http://aipre.huawei.tanyibot.com/aicc/knowledge/quickSearch?tenantId=%d&robotId=%d&search=%s
algorithm.similarSentence.generate.url=http://**************:8990/gen

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=wxd63fa0c76392f30d
wechat.appsecret=bd8188bbea0b83ab3fbdced3d0d1b043
wechat.token=yiwise123
wechat.aeskey=bo3oEon3nQ7VabeJ3

wechat.miniapp.appid=wxdc1924a8ddfc37f3
wechat.miniapp.appsecret=352ac81b014ca1d273ff800c6d16d553

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=o4OCt7ty6wava84kXUnbYNiqPGv5PtNH2RQET_ZdFo0
wechat.template.telLineAlertMsg=25aISlkwxI1fCV9udVxmJrPNj0Abqc9Nzg4N8M8H-CE
wechat.template.noRobotAvailableAccountDebtMsg=70YkNAN9MQEBm74uU90L0Mvh6w5SWG4OB0yMAgv-hN8
wechat.template.earlyWarningMsg=CBALLLgbJo5kIe6oHExOFDu29EpN0Qt-N_1WhDP-Dv8
wechat.template.callJobFinishMsg=KGks5uLqRAsITb7gnA6AAnceUt9MxpAXsniGyfyUuWg
wechat.template.insufficientBalanceMsg=CNWrrsOsH8egGca-3QVbkUhOBY6fnpXLpREUJa9HQS0
wechat.template.callJobCompletionPercentageMsg=i2JBuSM1Kr_6l00qtiaULjSxELX9YaTCOMB7rL2iWrU
wechat.template.tenantAccountAutoSet=CNWrrsOsH8egGca-3QVbkUhOBY6fnpXLpREUJa9HQS0

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=https://crm.huawei.tanyibot.com

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5BA2\u6237\u5BFC\u5165\u660E\u7EC6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.chunkSize=100
export.chunkSize=100

import.rearrange.url=http://*************:8110/apiImport/rearrange/?ossKey=%s

#### mongo ****
mongodb.primary.host=*************
mongodb.secondary.host=*************
mongodb.port=8635
mongodb.dbname=ai_call_engine
mongodb.user=ai_call_engine
mongodb.password=RootMongo2019!
mongodb.authentification=true
mongodb.nodeType=SINGLE
mongodb.replicaSet=mgset-********

#### elasticsearch ****
es.hostname=es-cn-v641as47r000z0nnw.elasticsearch.aliyuncs.com
es.port=9200
es.username=K6TMN8s9XScpuJPcsQ6uAA==
es.password=Aarq-DuEmRWDCLUTaRj_3dniCQez3a6CIFE6HhAJOpk=
es.enable=false

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### prodocut document upload config ####
oss.productdocument.webDirectUpload.max=1048576000
oss.productdocument.root=ProductDocument/

oss.ttsaudition.root=TtsAudition/

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

oss.avatar.upload.root=AvatarFileUpload

#### callback url #####
crm.url=https://crmpre.huawei.tanyibot.com

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000

###### \u8F6C\u63A5\u89E6\u53D1 *0\u534F\u5546\u8F6C *1\u76F2\u8F6C #####
transfer.trigger=*0
###### \u89E6\u53D1\u4F1A\u8BAE \u5355\u4E2A\u6570\u5B57 ####
transfer.conf=0

###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.outbound.port=8151
freeswitch.esl.ip=**************
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon


######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=144
free_user.dialog_flow_id.finance=144
free_user.dialog_flow_id.fitment=144
free_user.dialog_flow_id.back=144
free_user.dialog_flow_id.debt=144
free_user.dialog_flow_id.power=144
free_user.dialog_flow_id.telecom=144
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=5
cs.seat.freeswitchGroupId=6
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=98

###### first encrypt salt ######
first.encrypt.salt=geelysdafaqj23ou89ZXcj@#$@#$#@KJdjklj;D../dSF.,
first.digest.times=1

# bert
bert.url=http://**********:8125
yiwise.ask.use=1
yiwise.ask.url=https://crmpre.huawei.tanyibot.com/apiAsk/ask?dialogFlowId=%d&text=%s
liepin.ask.use=0
liepin.ask.url=https://crmpre.huawei.tanyibot.com/apiAsk/liepinAsk?dialogFlowId=%d&text=%s
liepin.ask.dialogFlowId=0

question.length=20
caitong.ask.dialogFlowId=5052,5432

# \u524D\u540Eusersaytext\u5408\u5E76\u7684\u6682\u505C\u65F6\u95F4
user.say.text.time=600

# local \u672C\u5730\u5316 aliyun \u7EBF\u4E0A
rocketmq.type=local
rocketmq.name-server=**********:9876

# \u652F\u4ED8\u5B9D\u8FD4\u56DE\u548C\u56DE\u8C03
alipay.boss.returnUrl=https://bosspre.huawei.tanyibot.com/apiBoss/aliPay/payReturn
alipay.boss.notifyUrl=https://bosspre.huawei.tanyibot.com/apiBoss/aliPay/payNotify
alipay.boss.backUrl=https://bosspre.huawei.tanyibot.com/#/accountInventory/
alipay.crm.returnUrl=https://crmpre.huawei.tanyibot.com/apiEngine/aliPay/payReturn
alipay.crm.notifyUrl=https://crmpre.huawei.tanyibot.com/apiEngine/aliPay/payNotify
alipay.crm.backUrl=https://crmpre.huawei.tanyibot.com/#/systemManagement/line
alipay.aicc.returnUrl=https://aiccpre.huawei.tanyibot.com/apiEngine/aliPay/aiccPayReturn
alipay.aicc.backUrl=https://aiccpre.huawei.tanyibot.com/enterprise-center/account-manage/info
alipay.doudian.aicc.returnUrl=https://dou-magicbot.tanyibot.com/home

robot.asr.type=

#### YiwiseAsrGateway\u5730\u5740 ####
yiwise.asr.gatewayUrl=http://***************:6060
yiwise.asr.accessKeyId=SKJFHS2u1LKDKFS
yiwise.asr.accessKeySecret=IUYOHKJFSHDKJH12
yiwise.asr.AsrClientTokenGetterClazz=com.yiwise.asr.DefaultTokenGenerator

nlp.beforeChat=https://aipre.huawei.yiwise.com/aicc/speech/beforeChat
nlp.afterChat=https://aipre.huawei.yiwise.com/aicc/speech/afterChat
nlp.chat=https://aipre.huawei.yiwise.com/aicc/speech/chat
nlp.publishCallBack=https://aipre.huawei.yiwise.com/aicc/speech/createSnapshot
nlp.entityList=https://aipre.huawei.yiwise.com/apiIntegration/tanyi/entityList
nlp.knowledgeList=https://aipre.huawei.yiwise.com/aicc/knowledge/knowledgeList?tenantId=%s&robotId=%s&pageSize=%s&pageNum=%s&keyword=%s&knowledgeId=%s
nlp.login=https://aipre.huawei.yiwise.com/apiPlatform/entry/singleLogin
nlp.getRobotInfo=https://aipre.huawei.yiwise.com/aicc/robot/getRobotInfo
nlp.access.secret=1VmaIyTrvkJiEHAFjArqp9Z3lD5ufj8aIIgmf0W9OiZR1GI7


# jd ai platform
jd.app.key=642A97CB88E064536C8767C5157F9798
jd.app.secret=3E13A9BEFDF047C08453EF6133380623
jd.new.user.url=https://crm.tanyibot.com
jd.redirect.uri=https://crm.tanyibot.com/apiPlatform/jd/auth
jd.follower.id=6882


# Customer Service console config
consistent.dialog.beep.url=DefaultBeeps/consistentDialogBeep.wav
forcasting.callout.beep.url=DefaultBeeps/forecastingCalloutBeep.wav
uncommon.hangup.beep.url=DefaultBeeps/uncommonHangupBeep.wav
queue.beep.url=DefaultBeeps/queueBeep.wav

#aicc
nlp.robot.chat.url=http://aipre.huawei.tanyibot.com/aicc/text/chat?tenantId=%d&robotId=%d&text=%s&sessionId=%s

wechat.component.appId=wx4e22167f4b372558
wechat.component.appSecret=4c4133eab8541e403b00e73c3140286c
wechat.component.aesKey=DG79bsLYzBkRmNShgZviHUXmp81mVQCL7fFDTjo6QAC
wechat.component.token=nefljCILR1exxlPGznajHvgjaYA7WNrQ
wechat.component.preAuthCallback.url=https://aiccpre.huawei.tanyibot.com/customerServiceStage/moreConfig/all?type=%s
wechat.component.accessToken.url=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s
wechat.component.userInfo.url=https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN
wechat.miniapp.scheme.h5.url=https://wechat.huawei.tanyibot.com/wxapp-h5/?link=%s
# qc algorithm interfaces
qc.word.frequency.update=http://**************:2603/upload
qc.word.frequency.query=http://**************:2603/query
qc.rule.tag.trigger=http://**************:2606/predict

changhe.tenant.id=-1

# \u6817\u5B50\u96C6\u6210
lizi.ak=28c89eb6d96d4b97ac7e950d9e858688
lizi.as=0eee06561c3443eea06d5cb781409d50


## simple \u7B80\u6613\u7248\u672C \u6709\u4E9B\u529F\u80FD\u6A21\u5757\u4E0D\u62C6\u5206 advanced \u7EBF\u4E0A\u7248\u672C \u529F\u80FD\u6A21\u5757\u62C6\u5206
publish.module.type=advanced

# \u4EA7\u54C1\u6A21\u5F0F online \u7EBF\u4E0A \u5305\u542B\u4E00\u77E5\u7684\u4E13\u6709\u54C1\u529F\u80FD other \u4E0D\u5305\u542B\u4E00\u77E5\u4E13\u6709\u529F\u80FD \u4E3A\u5176\u4ED6\u5BA2\u6237\u90E8\u7F72
product.type=online

server.call.in.count=100

# yiwise tts
tts.yiwise.url=http://***************:8010/v1/tts/single
tts.yiwise.auth.url=http://***************:8010/v1/auth/token
tts.yiwise.user.name=yiwise
tts.yiwise.user.password=_yiwise

#### Huawei callin demo ####
huawei.callin.dialogFlowId=6346

# Huawei sms parameters
huawei.sms.appSecret=F2436QRa7s3oXyRl90F7Zlh5B3Ne
huawei.sms.appKey=9Myg7Ng5TEP7sDzH9C5X1Q8Ij9a4

peers.queuedPacketNum=15

common.job.max.quota=10

weibao.tenant=-1
weibao.startcall.url=https://tms-sit.wesure100.com/api/v1/public/cop/callBothRequest
weibao.callrecord.callback.url=https://tms-sit.wesure100.com/api/v1/public/callinfo/intention/notify
tongdun.tenant=-2

# tencnet asr
tencent.asr.appId=**********
tencent.asr.secretId=AKIDA8qIXWgt74nGTQrk8G8k6C1e2MRHnJUR
tencent.asr.secretKey=wwkJcgV3LN9rfEcjbEeojOoX1LPEnBNJ

qiyu.distributor=5025

#### e\u7B7E\u5B9D ####
esign.openapi.url=https://openapi.esign.cn
esign.openapi.appId=
esign.openapi.secret=
esign.openapi.agentAccountId=
esign.openapi.callback=

sunfeng.tenant=-1
sunfeng.apikey=d9cf325f97d84f43ae5aa251cba04d81
sunfeng.virtualnumber.get.url=http://crs-pub.sit.sf-express.com:45444/api/recruitcenter/out/ty_virtual

#default BackgroundSound url
bot.backgroundSound.url=https://obs-tanyi-prod.obs.cn-east-2.myhuaweicloud.com/common/audio/\u529E\u516C\u5BA4\u8C08\u8BDD\u58F0.wav

global_sms_send=true
global_message_center_customer_interact=true
global_message_center_work_order=false
global_io_history_clue=true
global_user_center_contact_manager=true
global_user_center_back_old=true
main_sms_platform=true
main_smart_clue=true
main_seat_help=true
main_work_order=false
main_code_login=true
main_wechat_login=true
ope_customer_whitelist_share=true
ope_customer_contract_num=true
ope_customer_back_money=true
ope_customer_callout=true
ope_customer_callin=true
ope_customer_qc=true
ope_customer_voice=true
ope_customer_text=true
ope_customer_yibrain_voice=true
ope_customer_yibrain_text=true
ope_customer_zhineng_xiansuo=true
ope_customer_sms_center=true
ope_customer_aiassistant=true
ope_customer_ivr=true
ope_customer_login_crm=true
ope_customer_login_aicc=true
ope_customer_forbid_account=true
ope_customer_view_login_log=true
ope_whitelist_share=true
ope_utils=true
ope_concurency=true
ope_cur_concurency=true
ope_operation_log=true
ope_clue_collect=true
ope_huawei_form=true
customerServiceStage=true
qc=true
engine=true
callout_phoneTaskV2_BOTV2=true
callout_phoneTask_genderMood=true
callout_billing=true
callin_IVR=true
enterprise_account_manage_info=true
enterprise_account_manage_order=true
enterprise_account_manage_billing=true
enterprise_account_manage_communication=true
enterprise_account_manage_wechatCp=false
workorder=false
customerCenter=true
ope_dialog_template=true
ope_dialog_audit=true
global_voice_seat=true
callout_phoneTaskV2_intent_wechat_push=true
callout_phoneTaskV2_sms_push=true
callout_setting_stats_range=true

zhejiao.tenant=916
zhejiao.call.in.url=https://zsdltst.zj.sgcc.com.cn/zj_eqa/open/bcpiss/rest/msg01
zhejiao.private.key.name=MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIhIs/3wz/nod7Ff/0UMzyK4gRCjPLqSfYkxxtlLn8GEn5Tg9kgKEl+CBiVad3w1afgFivaTHHI7xCC9zyulFkKQ3Q5IuouBkaY2+hKUPDzRRer3RmxUcNM4e5IUfDwG//8Hh69Q0kEHyD22lXGvo/kQnoUyhH+RjZ1UVAJAzj7lAgMBAAECgYAVh26vsggY0Yl/Asw/qztZn837w93HF3cvYiaokxLErl/LVBJz5OtsHQ09f2IaxBFedfmy5CB9R0W/aly851JxrI8WAkx2W2FNllzhha01fmlNlOSumoiRF++JcbsAjDcrcIiR8eSVNuB6ymBCrx/FqhdX3+t/VUbSAFXYT9tsgQJBALsHurnovZS1qjCTl6pkNS0V5qio88SzYP7lzgq0eYGlvfupdlLX8/MrSdi4DherMTcutUcaTzgQU20uAI0EMyECQQC6il1Kdkw8Peeb0JZMHbs+cMCsbGATiAt4pfo1b/i9/BO0QnRgDqYcjt3J9Ux22dPYbDpDtMjMRNrAKFb4BJdFAkBMrdWTZOVc88IL2mcC98SJcII5wdL3YSeyOZto7icmzUH/zLFzM5CTsLq8/HDiqVArNJ4jwZia/q6Fg6e8KO2hAkB0EK1VLF/ox7e5GkK533Hmuu8XGWN6I5bHnbYd06qYQyTbbtHMBrFSaY4UH91Qwd3u9gAWqoCZoGnfT/o03V5lAkBqq8jZd2lHifey+9cf1hsHD5WQbjJKPPIb57CK08hn7vUlX5ePJ02Q8AhdZKETaW+EsqJWpNgsu5wPqsy2UynO
zhejiao.sm4.key=21A81BD0A039227B
