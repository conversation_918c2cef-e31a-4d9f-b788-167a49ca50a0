#### \u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E ####
db.master.url=*************************************************************************************************************************************************************************************************************************************************************************
db.master.username=ai_call_engine
db.master.password=#Daily_ai_call_engine
db.polardb.url=*************************************************************************************************************************************************************************************************************************************************************************
db.polardb.username=ai_call_engine
db.polardb.password=#Daily_ai_call_engine
db.slave.one.url=*************************************************************************************************************************************************************************************************************************************************************************
db.slave.one.username=ai_call_engine
db.slave.one.password=#Daily_ai_call_engine
db.driver=com.mysql.jdbc.Driver
db.initialSize=5
db.maxActive=20
db.minIdle=5

#### spring\u914D\u7F6E ####
spring.profiles.active=prod-local
server.engine.port=8010
server.ope.port=8020
server.miniapp.port=8030
server.dialogFlow.port=8040
server.platform.port=8050
server.openapi.port=8060
server.bossapi.port=8070
server.peers.port=8090
server.quartz.port=8100
server.rearrange.port=8110
server.isvcallback.port=8130
server.batchjob.port=8140
server.callin.port=8150
server.smsjob.port=8160
server.esl.port=8170
server.freeswitch.port=8190
server.qcjob.port=8200

apollo.bootstrap.enabled=false

### mq router ###
mq.router.ip=***************
mq.router.port=7080

#### localization params
network.enabled=true
sms.enabled=true
wx.enabled=true
freeswitch.url=training.tanyibot.com
freeswitch.port=7443
import.template.url=https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/common/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx

#### redis\u76F8\u5173\u914D\u7F6E ####
redis.hostname=**********
redis.port=6379
redis.password=!Yiwise0915=
redis.dbIndex=0

#### storage type
object.storage.type=oss

#### oss\u76F8\u5173\u914D\u7F6E ####
oss.basicUrl=oss-cn-hangzhou.aliyuncs.com
oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
oss.accessId=LTAI0IFtCuGa2XQO
oss.accessKey=Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
oss.defaultBucket=ai-call-platform
oss.useOssAuth=false

#### minio\u914D\u7F6E ####
minio.endpoint=http://127.0.0.1:9000
minio.accessKey=minioadmin
minio.secretKey=minioadmin
minio.defaultBucket=ai-call-platform

#### fastDFS ####
fastDFS.tracker_server=

yiwise.tts.ip=***************:6543
server.job.max.quota=120

#### LogHub\u76F8\u5173\u914D\u7F6E ####
logHub.endpoint=http://cn-hangzhou.log.aliyuncs.com
logHub.accessId=LTAICuX7MOmHW6Oy
logHub.accessKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
logHub.projectName=ai-call-platform-daily
logHub.logstore=ai-call-platform


#### \u963F\u91CC\u4E91mq ####
aliyun.ons.rocketmq.accessKey=LTAICuX7MOmHW6Oy
aliyun.ons.rocketmq.secretKey=Anm7NGPiYNKRe15MMg4oQA5Lqdt9xG
aliyun.ons.rocketmq.namesrvAddr=http://onsaddr.cn-hangzhou.mq-internal.aliyuncs.com:8080
aliyun.ons.rocketmq.topic=tanyi-prod-common
aliyun.ons.rocketmq.producerId=PID_TANYI_PROD
aliyun.ons.rocketmq.groupId=GID_TANYI_PROD_COMMON
aliyun.ons.rocketmq.callback.topic=tanyi-prod-callback
aliyun.ons.rocketmq.callback.producerId=PID_TANYI_PROD_CALLBACK
aliyun.ons.rocketmq.callback.groupId=GID_TANYI_PROD_CALLBACK
aliyun.ons.rocketmq.asrRecognition.topic=tanyi-prod_asr
aliyun.ons.rocketmq.asrRecognition.producerId=PID_TANYI_PROD_ASR
aliyun.ons.rocketmq.asrRecognition.groupId=GID_TANYI_PROD_ASR
aliyun.ons.rocketmq.wechatJob.topic=tanyi_prod_wechat
aliyun.ons.rocketmq.wechatJob.groupId=GID_PROD_WECHAT_JOB
aliyun.ons.rocketmq.callEventLog.topic=tanyi_prod_callEventLog
aliyun.ons.rocketmq.callEventLog.groupId=GID_PROD_CALL_EVENT_LOG
aliyun.ons.rocketmq.callEventLog.interruptAnalysis.groupId=GID_PROD_INTERRUPT_ANALYSIS
aliyun.ons.rocketmq.intentRuleMatchFlag.topic=tanyi_prod_intentRuleMatchFlag
aliyun.ons.rocketmq.intentRuleMatchFlag.groupId=GID_PROD_INTENT_RULE_MATCH
aliyun.ons.rocketmq.dialogStats.topic=tanyi_prod_dialogStats
aliyun.ons.rocketmq.dialogStats.groupId=GID_PROD_DIALOG_STATS
aliyun.ons.rocketmq.predictLog.topic=tanyi_prod_predictLog
aliyun.ons.rocketmq.predictLog.groupId=GID_PROD_PREDICT_LOG
aliyun.ons.rocketmq.returnVisit.topic=tanyi_prod_return_visit
aliyun.ons.rocketmq.returnVisit.groupId=GID_PROD_RETURN_VISIT

aliyun.ons.rocketmq.crowd.push.callback.topic=prod_customer_aicc_crowd_push_callback
aliyun.ons.rocketmq.crowd.push.callback.groupId=GID_PROD_CUSTOMER_AICC_CROWD_PUSH_CALLBACK

#### \u963F\u91CC\u4E91nls ####
nls.server.type=aliyun
aliyun.nls.defaultServerAddr=wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1
aliyun.nls.accessKeyId=LTAI0IFtCuGa2XQO
aliyun.nls.accessKeySecret=Z8BWgD1eCp8TzyPhh1iiLCf9JxoRzM
aliyun.nls.defaultDialogFlowAsrModelAppkey=4QQmua3uHGKGwsYD
aliyun.nls.tts.appkey=8yus5oYXl6bMKdES
aliyun.nls.tts.voice=yina

#### \u4E91\u7247\u77ED\u4FE1\u914D\u7F6E ####
yunpian.sms.verificationApiKey=abca65e763d31a31edab6df1e60effba
yunpian.sms.apiKey=959553e285a9d2cf89c497be5b592c61
yunpian.sms.financeApiKey=d4dbc473bf623e512bc65ea11dadaa7f
yunpian.sms.marketingApiKey=48eed6536ad50b297d00c6f5d341690a
yunpian.sms.callbackHost=https://crm.tanyibot.com
yunpian.template.callback.uri=https://crm.tanyibot.com/apiEngine/smsTemplate/yunpian/callback
yunpian.sms.finance.tenant=1072

ai.call.platform.back.url=https://crm.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowTotalInfo?dialogFlowId=%d
ai.call.platform.back.check.url=https://crm.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowUpdateTime?dialogFlowId=%d
ai.call.platform.back.exist.url=https://crm.tanyibot.com/apiDialogFlow/dialogFlow/isPlaceholderExist
ai.call.platform.back.analyze.phoneWithFile.url=https://crm.tanyibot.com/apiAnalyzePhone/analyze/withMultipartFile
ai.call.platform.back.dialog.params=https://crm.tanyibot.com/apiDialogFlow/dialogFlow/getDialogFlowExtInfoInner?dialogFlowId=%d

##### \u610F\u5411\u77ED\u4FE1\u53D1\u9001\u5931\u8D25\u62A5\u8B66\u914D\u7F6E #####
intent.message.fail.alert.number=30
intent.message.fail.alert.interval.time=86400

#### \u5FAE\u4FE1\u76F8\u5173\u914D\u7F6E ####
wechat.appid=wx26106ad72e9cd401
wechat.appsecret=f5a15f59defd6351cb4850a82f2d1cd2
wechat.token=123456
wechat.aeskey=bo3oEon3nQ7VabeJ3

wechat.miniapp.appid=wxdc1924a8ddfc37f3
wechat.miniapp.appsecret=352ac81b014ca1d273ff800c6d16d553

#### \u56de\u8c03\u5806\u79ef\u9884\u8b66\u914d\u7f6e ####
feishu.warn.phoneNumberSet=***********,***********
feishu.warn.webHook="https://open.feishu.cn/open-apis/bot/v2/hook/12ab7732-18d7-4e70-9b82-e06225b4e252"

#### \u5FAE\u4FE1\u6A21\u677F\u6D88\u606F ####
wechat.template.intentionCustomerFindMsg=o4OCt7ty6wava84kXUnbYNiqPGv5PtNH2RQET_ZdFo0
wechat.template.telLineAlertMsg=25aISlkwxI1fCV9udVxmJrPNj0Abqc9Nzg4N8M8H-CE
wechat.template.noRobotAvailableAccountDebtMsg=70YkNAN9MQEBm74uU90L0Mvh6w5SWG4OB0yMAgv-hN8
wechat.template.earlyWarningMsg=CBALLLgbJo5kIe6oHExOFDu29EpN0Qt-N_1WhDP-Dv8
wechat.template.callJobFinishMsg=KGks5uLqRAsITb7gnA6AAnceUt9MxpAXsniGyfyUuWg
wechat.template.insufficientBalanceMsg=U1CK90jpXTGCaX0VEl8CuZ6HnEnkY0xwafxiPsK-9UU
wechat.template.callJobCompletionPercentageMsg=i2JBuSM1Kr_6l00qtiaULjSxELX9YaTCOMB7rL2iWrU

#### \u5FAE\u4FE1OAuth ####
wechat.frontend.url=https://wechat.tanyibot.com

#### excel \u5BFC\u5165\u5BFC\u51FA\u914D\u7F6E ####
oss.excel.root=excel/
export.filePrefix=\u5BA2\u6237\u5BFC\u51FA
import.errorFilePrefix=\u5ba2\u6237\u5bfc\u5165\u660e\u7ec6
import.filePrefix=\u5BA2\u6237\u5BFC\u5165
import.reAddFile=\u91CD\u65B0\u6DFB\u52A0\u5BFC\u5165
import.addFile=\u5BA2\u6237\u5BFC\u5165\u4EFB\u52A1
import.chunkSize=100
export.chunkSize=100

import.rearrange.url=http://**************/apiImport/rearrange/?ossKey=%s

#### mongo ****
mongodb.primary.host=dds-bp1b12f4078bc134-pub.mongodb.rds.aliyuncs.com
mongodb.primary.port=3717
mongodb.primary.name=ai_call_engine
mongodb.primary.user=ai_call_engine
mongodb.primary.password=YxFfMCX39SY
mongodb.primary.authentification=true

mongodb.secondary.host=dds-bp1b12f4078bc134-pub.mongodb.rds.aliyuncs.com
mongodb.secondary.port=3717
mongodb.secondary.name=ai_call_engine
mongodb.secondary.user=ai_call_engine
mongodb.secondary.password=YxFfMCX39SY
mongodb.secondary.authentification=true

#### \u767E\u5BB6\u59D3\u4E0A\u4F20\u914D\u7F6E ####
oss.familyname.root=AudioRecord/familyName/

#### \u81EA\u5B9A\u4E49\u53D8\u91CF\u5F55\u97F3\u914D\u7F6E  ####
oss.customvariable.root=AudioRecord/customVariable/

### \u8BDD\u672F\u5F55\u97F3\u4E0A\u4F20\u914D\u7F6E ###
oss.dialogrecord.root=AudioRecord/dialogFlow/

#### prodocut document upload config ####
oss.productdocument.webDirectUpload.max=1048576000
oss.productdocument.root=ProductDocument/

oss.ttsaudition.root=TtsAudition/

#### \u7528\u6237\u4E0A\u4F20excel ####
oss.upload.root=FileUpload

####  ai\u7528\u6237\u5BF9\u8BDD\u8BB0\u5F55\u4E0A\u4F20  ####
oss.upload.dialogue.record.root=DialogueRecording

#### callback url #####
crm.url=https://crm.tanyibot.com

#### tenant \u6B20\u8D39\u63D0\u9192\u9608\u503C#####
tenant.fare.alert_threshold=50000

###### freeswitch esl \u914D\u7F6E #######
freeswitch.esl.outbound.port=8151
freeswitch.esl.ip=
freeswitch.esl.port=8021
freeswitch.esl.password=ClueCon


######## \u514D\u8D39\u8BD5\u7528\u8BDD\u672Fid ########
free_user.dialog_flow_id.house=152
free_user.dialog_flow_id.finance=129
free_user.dialog_flow_id.fitment=327
######### \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u677F ########
verification.code.template=\u3010\u4E00\u77E5\u667A\u80FD\u3011\u9A8C\u8BC1\u7801\uFF1A${code}\u3002\u6709\u6548\u671F3\u5206\u949F\uFF0C\u8BF7\u52FF\u6CC4\u9732\u7ED9\u4ED6\u4EBA\u4F7F\u7528\u3002

peers.queuedPacketNum=15

###### cs seat freeswitch and phoneNumber config ####
cs.seat.freeswitchId=5
cs.seat.freeswitchGroupId=1
cs.seat.useDialplanType=1
cs.seat.lineIp=
cs.seat.linePort=
cs.seat.variableSet=
cs.seat.linePrefix=
cs.seat.defaultPrefix=98

###### first encrypt salt ######
first.encrypt.salt=geelysdafaqj23ou89ZXcj@#$@#$#@KJdjklj;D../dSF.,
first.digest.times=1

robot.asr.type=yiwise

#### YiwiseAsrGateway\u5730\u5740 ####
yiwise.asr.gatewayUrl=https://asr-gateway.yiwise.com
yiwise.asr.accessKeyId=SKJFHS2u1LKDKFS
yiwise.asr.accessKeySecret=IUYOHKJFSHDKJH12
yiwise.asr.AsrClientTokenGetterClazz=com.yiwise.asr.DefaultTokenGenerator
ope_dialog_template=true
ope_dialog_audit=true
global_voice_seat=true
callout_phoneTaskV2_intent_wechat_push=true
callout_phoneTaskV2_sms_push=true
callout_setting_stats_range=true

zhejiao.tenant=916
zhejiao.call.in.url=https://zsdl.zj.sgcc.com.cn/zj_eqa/open/bcpiss/rest/msg01
zhejiao.private.key.name=MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIhIs/3wz/nod7Ff/0UMzyK4gRCjPLqSfYkxxtlLn8GEn5Tg9kgKEl+CBiVad3w1afgFivaTHHI7xCC9zyulFkKQ3Q5IuouBkaY2+hKUPDzRRer3RmxUcNM4e5IUfDwG//8Hh69Q0kEHyD22lXGvo/kQnoUyhH+RjZ1UVAJAzj7lAgMBAAECgYAVh26vsggY0Yl/Asw/qztZn837w93HF3cvYiaokxLErl/LVBJz5OtsHQ09f2IaxBFedfmy5CB9R0W/aly851JxrI8WAkx2W2FNllzhha01fmlNlOSumoiRF++JcbsAjDcrcIiR8eSVNuB6ymBCrx/FqhdX3+t/VUbSAFXYT9tsgQJBALsHurnovZS1qjCTl6pkNS0V5qio88SzYP7lzgq0eYGlvfupdlLX8/MrSdi4DherMTcutUcaTzgQU20uAI0EMyECQQC6il1Kdkw8Peeb0JZMHbs+cMCsbGATiAt4pfo1b/i9/BO0QnRgDqYcjt3J9Ux22dPYbDpDtMjMRNrAKFb4BJdFAkBMrdWTZOVc88IL2mcC98SJcII5wdL3YSeyOZto7icmzUH/zLFzM5CTsLq8/HDiqVArNJ4jwZia/q6Fg6e8KO2hAkB0EK1VLF/ox7e5GkK533Hmuu8XGWN6I5bHnbYd06qYQyTbbtHMBrFSaY4UH91Qwd3u9gAWqoCZoGnfT/o03V5lAkBqq8jZd2lHifey+9cf1hsHD5WQbjJKPPIb57CK08hn7vUlX5ePJ02Q8AhdZKETaW+EsqJWpNgsu5wPqsy2UynO
zhejiao.sm4.key=21A81BD0A039227B

