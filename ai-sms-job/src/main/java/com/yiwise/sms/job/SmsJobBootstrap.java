package com.yiwise.sms.job;

import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.core.config.AppConfig;
import com.yiwise.core.thread.DynamicDataSourceApplicationExecutorHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import static com.yiwise.base.model.exception.ChildThreadExceptionHandler.DEFAULT_EXCEPTION_HANDLER;

/**
 * @Author: wangguomin
 * @Date: 2019-02-14 10:25
 */
@SpringBootApplication(
        scanBasePackageClasses = {
                AppConfig.class
        }
)
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.yiwise.core.client", "com.yiwise.core.feignclient"})
public class SmsJobBootstrap {
    private static final Logger logger = LoggerFactory.getLogger(SmsJobBootstrap.class);

    public static void main(String[] args) throws Exception {
        Thread.setDefaultUncaughtExceptionHandler(DEFAULT_EXCEPTION_HANDLER);

        new SpringApplicationBuilder(SmsJobBootstrap.class)
                .run(args);

        // 开启获取Job轮询
        SmsJobScheduler executor = AppContextUtils.getBean(SmsJobScheduler.class);
        executor.startJobScheduler();

        // 简单任务执行线程监控
        DynamicDataSourceApplicationExecutorHolder.startThreadPoolStatusMonitor();

        logger.info("=============================spring boot start successful !=============================");
        Thread.currentThread().join();
    }

}
