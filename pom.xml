<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yiwise</groupId>
    <artifactId>ai-call-back</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <modules>
        <!--  CRM  -->
        <module>ai-call-engine-web</module>

        <!--  DAO, service 公共层  -->
        <module>ai-call-service</module>
        <module>ai-call-service-api</module>

        <!--  AI 对话  -->
        <module>ai-call-engine</module>
        <!--  AI 外呼任务  -->
        <module>ai-call-callout-job</module>
        <!--  OPE  -->
        <module>ai-call-ope-web</module>
        <!--  话术编辑和查询  -->
        <module>ai-call-dialog-flow-web</module>
        <!--  小程序  -->
        <module>ai-call-miniapp-web</module>
        <!--  定时任务  -->
        <module>ai-call-quartz</module>
        <!--  登录，角色管理，权限管理  -->
        <module>ai-call-platform-web</module>
        <!--  springboot web 配置 -->
        <module>ai-call-springboot-config</module>
        <!--  openAPI接口  -->
        <module>ai-call-open-api</module>
        <!--  boss  -->
        <module>ai-call-boss-web</module>
        <!--  导入客户电话打乱，比较消耗内存，单独开了一个工程  -->
        <module>ai-call-import-rearrange-web</module>

        <!--  接受消息，向isv回调  -->
        <module>ai-call-isv-callback</module>

        <!--  导入导出 -->
        <module>ai-call-batch-job</module>

        <!--  呼入 -->
        <module>ai-call-callin-job</module>
        <module>ai-call-esl</module>

        <module>ai-call-freeswitch-curl</module>

        <module>ai-sms-job</module>

        <!-- 人工批量外呼 -->
        <module>ai-call-cscall-job</module>

        <module>ai-call-message-router</module>
        <module>ai-text-service-web</module>

        <module>ai-qc-job</module>
        <module>ai-qc-web</module>

        <!--加微-->
        <module>ai-call-wechat-job</module>

        <!--  任务一步处理回调  -->
        <module>ai-call-async-job</module>
        <!--  批处理任务  -->
        <module>ai-call-batch-mq</module>
    </modules>

    <properties>
        <yiwise-isv-callback>2.1.0-SNAPSHOT</yiwise-isv-callback>
        <yiwise-rcs-api>2.1.3-SNAPSHOT</yiwise-rcs-api>
        <customer-data-platform-rpc-api>2.2.2-SNAPSHOT</customer-data-platform-rpc-api>
        <ma-center-api>0.0.1-SNAPSHOT</ma-center-api>
        <java.version>1.8</java.version>
        <argLine>-Dfile.encoding=UTF-8</argLine>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.deploy.skip>false</maven.deploy.skip>

        <yiwise-common.version>5.0.0-RELEASE</yiwise-common.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-text.version>1.4</commons-text.version>
        <commons-lang3.version>3.6</commons-lang3.version>
        <commons-io.version>2.0.1</commons-io.version>
        <commons-collections4.version>4.2</commons-collections4.version>
        <commons-codec.version>1.10</commons-codec.version>
        <commons-fileupload.version>1.3.3</commons-fileupload.version>
        <commons-cli.version>1.4</commons-cli.version>
        <logback-classic.version>1.2.3</logback-classic.version>
        <log4j.version>2.21.1</log4j.version>
        <aliyun-log-logback-appender.version>0.1.15</aliyun-log-logback-appender.version>
        <jackson.version>2.13.5</jackson.version>
        <junit.version>4.12</junit.version>
        <spring-boot-dependencies.version>2.0.3.RELEASE</spring-boot-dependencies.version>
        <mockito-core.version>1.10.19</mockito-core.version>
        <powermock-module-junit4.version>1.7.4</powermock-module-junit4.version>
        <mybatis-typehandlers-jsr310.version>1.0.2</mybatis-typehandlers-jsr310.version>
        <mysql-connector-java.version>8.0.14</mysql-connector-java.version>
        <mybatis.version>3.4.1</mybatis.version>
        <tk.mybatis.mapper.version>4.0.3</tk.mybatis.mapper.version>
        <mybatis-generator-core.version>1.3.7</mybatis-generator-core.version>
        <pagehelper.version>4.1.6</pagehelper.version>
        <mybatis-spring-boot-starter.version>1.3.2</mybatis-spring-boot-starter.version>
        <hibernate-validator.version>6.1.1.Final</hibernate-validator.version>
        <jaudiotagger.version>2.0.3</jaudiotagger.version>
        <druid.version>1.2.22</druid.version>
        <druid-spring-boot-starter.version>1.1.10</druid-spring-boot-starter.version>
        <httpcomponents.version>4.5.6</httpcomponents.version>
        <spring-shell-starter.version>2.0.1.RELEASE</spring-shell-starter.version>
        <spring-boot-dependencies.version>2.0.3.RELEASE</spring-boot-dependencies.version>
        <javaslang.version>2.0.5</javaslang.version>
        <quartz.version>2.2.3</quartz.version>
        <javax.el.version>3.0.0</javax.el.version>
        <google.guava.version>23.5-jre</google.guava.version>
        <codehaus.janino.version>3.0.7</codehaus.janino.version>
        <bcprov-jdk15on.version>1.55</bcprov-jdk15on.version>
        <spring-boot-starter-webflux.version>2.0.3.RELEASE</spring-boot-starter-webflux.version>
        <spring-cloud-starter-alibaba-nacos-discovery.version>2.0.4.RELEASE</spring-cloud-starter-alibaba-nacos-discovery.version>

        <peers-lib.version>3.0.3-RELEASE</peers-lib.version>
        <ai-call-engine.version>1.0.0-SNAPSHOT</ai-call-engine.version>
        <nls-sdk.version>2.0.3</nls-sdk.version>
        <aliyun-core.version>3.5.1</aliyun-core.version>
        <netty-all.version>4.1.36.Final</netty-all.version>
        <fastjson.version>1.2.83</fastjson.version>
        <aliyun-sdk-oss.version>3.14.0</aliyun-sdk-oss.version>
        <aliyun-java-sdk-dysmsapi.version>1.1.0</aliyun-java-sdk-dysmsapi.version>
        <dyplsapi20170525.version>3.3.0</dyplsapi20170525.version>
        <com.aliyun.openservices.ons.client.version>1.9.1.Final</com.aliyun.openservices.ons.client.version>
        <baidu.aip.java-sdk.version>4.16.16</baidu.aip.java-sdk.version>
        <spring.data.version>2.0.2.RELEASE</spring.data.version>
        <xlsx.streamer.version>2.1.0</xlsx.streamer.version>
        <bouncycastle.version>140</bouncycastle.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <fastdfs-client-java.version>1.27-RELEASE</fastdfs-client-java.version>
        <proguard-maven-plugin.version>2.0.14</proguard-maven-plugin.version>
        <org.apache.rocketmq.version>4.4.0</org.apache.rocketmq.version>
        <rocketmq-spring-boot-starter-version>2.0.2</rocketmq-spring-boot-starter-version>
        <alipay-sdk.version>3.7.26.ALL</alipay-sdk.version>
        <elasticsearch.version>6.7.0</elasticsearch.version>

        <skywalking.version>7.0.0</skywalking.version>
        <com.github.binarywang.version>3.7.0</com.github.binarywang.version>
        <jsoup.version>1.7.2</jsoup.version>
        <aliyun-java-sdk-ons.version>3.1.4</aliyun-java-sdk-ons.version>
        <hbase.client.version>2.4.6</hbase.client.version>
        <account-service-api>2.2.3-SNAPSHOT</account-service-api>
        <yiwise-billing-service-api>2.0.3-SNAPSHOT</yiwise-billing-service-api>
        <callout-job-api.version>2.4.7-SNAPSHOT</callout-job-api.version>
        <v3dialogFlow.version>daily-2.1.0-SNAPSHOT</v3dialogFlow.version>
        <call-activity-api.version>2.2.2-SNAPSHOT</call-activity-api.version>
        <ma-dispatch-center-api>2.0.2-SNAPSHOT</ma-dispatch-center-api>
        <ai-call-reception-service-api.version>2.1.4-SNAPSHOT</ai-call-reception-service-api.version>
        <video-callout-job-api.version>2.0.5-RELEASE</video-callout-job-api.version>
        <jd-sdk-oss.version>1.11.490</jd-sdk-oss.version>
        <middleware-tts.version>5.2.5-SNAPSHOT</middleware-tts.version>
        <middleware-ObjectStorage.version>5.0.0-RELEASE</middleware-ObjectStorage.version>
        <yiwise-agent-service-api.version>1.1.5-RELEASE</yiwise-agent-service-api.version>
        <emoji-java.version>5.1.1</emoji-java.version>
        <aicc-common.version>1.0.7-SNAPSHOT</aicc-common.version>
        <yiwise-bridge-service-api.verion>1.0.0-SNAPSHOT</yiwise-bridge-service-api.verion>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-bridge-service-api</artifactId>
                <version>${yiwise-bridge-service-api.verion}</version>
            </dependency>
            <dependency>
                <groupId>com.vdurmont</groupId>
                <artifactId>emoji-java</artifactId>
                <version>${emoji-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.netflix.archaius</groupId>
                <artifactId>archaius-core</artifactId>
                <version>0.7.6</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-service</artifactId>
                <version>${yiwise-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-service-api</artifactId>
                <version>${yiwise-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.reception</groupId>
                <artifactId>ai-call-reception-service-api</artifactId>
                <version>${ai-call-reception-service-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.agent</groupId>
                <artifactId>yiwise-agent-service-api</artifactId>
                <version>${yiwise-agent-service-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-api</artifactId>
                <version>${v3dialogFlow.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.yiwise.common</groupId>
                        <artifactId>base-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.skywalking</groupId>
                        <artifactId>apm-toolkit-logback-1.x</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${jd-sdk-oss.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>dialogflow-chat-client</artifactId>
                <version>${v3dialogFlow.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>call-activity-api</artifactId>
                <version>${call-activity-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-model</artifactId>
                <version>${yiwise-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>rocketmq-connector</artifactId>
                <version>2.0.0-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.corundumstudio.socketio</groupId>
                <artifactId>netty-socketio</artifactId>
                <version>1.7.19</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>video-callout-job-api</artifactId>
                <version>${video-callout-job-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>asr-client-sdk</artifactId>
                <version>1.2.0-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-lcs-api</artifactId>
                <version>2.2.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ai-call-service-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ai-call-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ai-call-springboot-config</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-peers-lib</artifactId>
                <version>${peers-lib.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ai-call-engine</artifactId>
                <version>${ai-call-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-common</artifactId>
                <version>${yiwise-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>base-monitoring</artifactId>
                <version>${yiwise-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-ObjectStorage</artifactId>
                <version>${middleware-ObjectStorage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.middleware</groupId>
                <artifactId>Middleware-Tts</artifactId>
                <version>${middleware-tts.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-spring-batch</artifactId>
                <version>1.0.1-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${com.github.binarywang.version}</version>
            </dependency>

            <!-- jdk工具补充包 -->
            <dependency>
                <groupId>bouncycastle</groupId>
                <artifactId>bcprov-jdk16</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ecs</artifactId>
                <version>4.16.9</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-common</artifactId>
                <version>2.2.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-long-asr</artifactId>
                <version>${nls-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-tts</artifactId>
                <version>${nls-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nls</groupId>
                <artifactId>nls-sdk-recognizer</artifactId>
                <version>2.1.6</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.14.9</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>3.7.1</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>ons-client</artifactId>
                <version>${com.aliyun.openservices.ons.client.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>${javax.el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>${javax.el.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>${mybatis-typehandlers-jsr310.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>${tk.mybatis.mapper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis-generator-core.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>io.javaslang</groupId>
                <artifactId>javaslang</artifactId>
                <version>${javaslang.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-cli</groupId>
                <artifactId>commons-cli</artifactId>
                <version>${commons-cli.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-classic.version}</version>
            </dependency>

            <!--不使用, 覆盖其他依赖引入的有漏洞的旧版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>commons-compiler</artifactId>
                <version>${codehaus.janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${codehaus.janino.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>aliyun-log-logback-appender</artifactId>
                <version>${aliyun-log-logback-appender.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock-module-junit4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito</artifactId>
                <version>${powermock-module-junit4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org</groupId>
                <artifactId>jaudiotagger</artifactId>
                <version>${jaudiotagger.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpcomponents.version}</version>
            </dependency>

            <!--quartz start-->
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <!--quartz end-->



            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>${aliyun-java-sdk-dysmsapi.version}</version>
            </dependency>

            <!--https://next.api.aliyun.com/api-tools/sdk/Dyplsapi?language=java-tea-->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dyplsapi20170525</artifactId>
                <version>${dyplsapi20170525.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>tea</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${baidu.aip.java-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.monitorjbl</groupId>
                <artifactId>xlsx-streamer</artifactId>
                <version>${xlsx.streamer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise.common</groupId>
                <artifactId>yiwise-esl-client</artifactId>
                <version>1.0.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.csource</groupId>
                <artifactId>fastdfs-client-java</artifactId>
                <version>${fastdfs-client-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.9.11</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.23</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot-starter-version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.19</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>${spring-boot-starter-webflux.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client-sniffer</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!--huawei-->
            <dependency>
                <groupId>com.huawei.sis</groupId>
                <artifactId>huaweicloud-java-sdk-sis</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.sis</groupId>
                <artifactId>java-websocket</artifactId>
                <version>1.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.sis</groupId>
                <artifactId>huaweicloud-java-sdk-custom_tts</artifactId>
                <version>1.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.sis</groupId>
                <artifactId>java-sdk-core</artifactId>
                <version>1.0-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java</artifactId>
                <version>3.19.7</version>
            </dependency>
            <dependency>
                <groupId>com.huawei.apigateway</groupId>
                <artifactId>java-sdk-core</artifactId>
                <version>3.0.12</version>
            </dependency>

            <dependency>
                <groupId>com.github.whvcse</groupId>
                <artifactId>easy-captcha</artifactId>
                <version>1.6.2</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
                <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询最新版本 -->
                <version>3.0.97</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${com.github.binarywang.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>4.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>4.0.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ons</artifactId>
                <version>${aliyun-java-sdk-ons.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${com.github.binarywang.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${com.github.binarywang.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>1.9.1</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>7.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.alicom</groupId>
                <artifactId>alicom-mns-receive-sdk</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.mns</groupId>
                <artifactId>aliyun-sdk-mns</artifactId>
                <version>1.1.8</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dybaseapi</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.github.eljah</groupId>
                <artifactId>xmindjbehaveplugin</artifactId>
                <version>0.8</version>
            </dependency>
            <!--二维码生成工具 https://github.com/zxing/zxing-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.hbase</groupId>
                <artifactId>alihbase-client</artifactId>
                <version>2.8.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>triton-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- 腾讯云ASR -->
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-speech-sdk-java</artifactId>
                <version>1.0.18</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Finchley.SR4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-starter-alibaba-nacos-discovery.version}</version>
            </dependency>

            <!-- SkyWalking 日志追踪 -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>8.10.0</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-rcs-api</artifactId>
                <version>${yiwise-rcs-api}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-blacklist-api</artifactId>
                <version>1.0.4-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ma-center-api</artifactId>
                <version>${ma-center-api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baomidou</groupId>
                        <artifactId>mybatis-plus-annotation</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>customer-data-platform-rpc-api</artifactId>
                <version>${customer-data-platform-rpc-api}</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>1.7.1</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>cloud-feign-helper</artifactId>
                <version>2.0.0-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-isv-callback-api</artifactId>
                <version>${yiwise-isv-callback}</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>account-service-api</artifactId>
                <version>${account-service-api}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-billing-service-api</artifactId>
                <version>${yiwise-billing-service-api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>bouncycastle</groupId>
                        <artifactId>bcprov-jdk16</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-billing-service-client</artifactId>
                <version>${yiwise-billing-service-api}</version>
            </dependency>
            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>yiwise-bill-statistic-service-api</artifactId>
                <version>2.0.0-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>ma-dispatch-center-api</artifactId>
                <version>${ma-dispatch-center-api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.yiwise</groupId>
                        <artifactId>customer-data-platform-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yiwise.dialogflow</groupId>
                <artifactId>asr-result-filter-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise.aicc</groupId>
                <artifactId>callin-service-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.yiwise.common</groupId>
                        <artifactId>base-model</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ti-net</groupId>
                <artifactId>clink-serversdk</artifactId>
                <version>3.0.9.9</version>
            </dependency>

            <!--    飞书SDK        -->
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>2.0.30</version>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>callout-job-api</artifactId>
                <version>${callout-job-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>2.16.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yiwise</groupId>
                <artifactId>aicc-common</artifactId>
                <version>${aicc-common.version}</version>
            </dependency>

            <!--   唯品会SDK         -->
            <dependency>
                <groupId>com.vip</groupId>
                <artifactId>osp-sdk</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.vip</groupId>
                <artifactId>vop-sdk-idlc</artifactId>
                <version>1.0.0</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>com.github.wvengen</groupId>
                    <artifactId>proguard-maven-plugin</artifactId>
                    <version>${proguard-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.1.2.RELEASE</version>
                </plugin>
                <plugin>
                    <groupId>com.yiwise</groupId>
                    <artifactId>json-variable-filter</artifactId>
                    <version>1.1-RELEASE</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.4</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 使properties扩展到整个maven bulid 周期
                    Ref: https://github.com/ktoso/maven-git-commit-id-plugin/issues/280 -->
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <dateFormat>yyyy.MM.dd HH:mm:ss</dateFormat>
                    <verbose>true</verbose>
                    <!-- 是否生 git.properties 属性文件 -->
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--指定"git.properties"文件的存放路径(相对于${project.basedir}的一个路径);-->
                    <!--<generateGitPropertiesFilename>git.properties</generateGitPropertiesFilename>-->
                    <!--git描述配置,可选;由JGit提供实现;-->
                    <gitDescribe>
                        <!--是否生成描述属性-->
                        <skip>false</skip>
                        <!--提交操作未发现tag时,仅打印提交操作ID,-->
                        <always>false</always>
                        <!--提交操作ID显式字符长度,最大值为:40;默认值:7; 0代表特殊意义;后面有解释; -->
                        <abbrev>7</abbrev>
                        <!--构建触发时,代码有修改时(即"dirty state"),添加指定后缀;默认值:"";-->
                        <dirty>-dirty</dirty>
                        <!--always print using the "tag-commits_from_tag-g_commit_id-maybe_dirty" format, even if "on" a tag. The distance will always be 0 if you're "on" the tag. -->
                        <forceLongFormat>false</forceLongFormat>
                    </gitDescribe>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>code_source</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>code_proguard</id>
        </profile>

        <profile>
            <id>local</id>
            <properties>
                <!-- env用于子pom中指定filter文件 -->
                <env>local</env>
                <!-- buildEnv用于build_parameter.json中获取当前build的环境 -->
                <buildEnv>daily</buildEnv>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>local-huoshan</id>
            <properties>
                <!-- env用于子pom中指定filter文件 -->
                <env>local-huoshan</env>
                <!-- buildEnv用于build_parameter.json中获取当前build的环境 -->
                <buildEnv>daily</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>local-huawei</id>
            <properties>
                <env>local-huawei</env>
                <buildEnv>local-huawei</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>daily</id>
            <properties>
                <env>daily</env>
                <buildEnv>daily</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
                <buildEnv>daily</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
                <buildEnv>pre</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
                <buildEnv>prod</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-huoshan</id>
            <properties>
                <env>prod-huoshan</env>
                <buildEnv>prod-huoshan</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-finance</id>
            <properties>
                <env>prod-finance</env>
                <buildEnv>prod-finance</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-local</id>
            <properties>
                <env>prod-local</env>
                <buildEnv>daily</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>localization</id>
            <properties>
                <env>localization</env>
                <buildEnv>localization</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>daily-huawei</id>
            <properties>
                <env>daily-huawei</env>
                <buildEnv>daily-huawei</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>daily-huoshan</id>
            <properties>
                <env>daily-huoshan</env>
                <buildEnv>daily-huoshan</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>pre-finance</id>
            <properties>
                <env>pre-finance</env>
                <buildEnv>pre-finance</buildEnv>
            </properties>
        </profile>
        <profile>
            <id>prod-huawei</id>
            <properties>
                <env>prod-huawei</env>
                <buildEnv>prod-huawei</buildEnv>
            </properties>
        </profile>

        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>Windows</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.bat</script.extension>
            </properties>
        </profile>
        <profile>
            <id>unix</id>
            <activation>
                <os>
                    <family>unix</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.sh</script.extension>
            </properties>
        </profile>
        <profile>
            <id>mac</id>
            <activation>
                <os>
                    <family>mac</family>
                </os>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <script.extension>.sh</script.extension>
            </properties>
        </profile>

    </profiles>

    <repositories>
        <repository>
            <id>nexus-yiwise</id>
            <url>http://nexus.yiwise.net/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>nexus-yiwise</id>
            <url>http://nexus.yiwise.net/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
