package com.yiwise.boss.api.controller.distributor;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.entity.InitiateContractRecordPO;
import com.yiwise.core.model.enums.AuthResourceUriEnum;
import com.yiwise.core.model.enums.OperationLogLogTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.boss.*;
import com.yiwise.core.model.vo.ope.DistributorInitiateContractVO;
import com.yiwise.core.model.vo.ope.InitiateContractRecordQueryVO;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.core.service.ope.platform.InitiateContractRecordService;
import com.yiwise.core.service.ope.platform.RechargePrestoreStreamService;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.validate.boss.distributor.AddBossDistributorValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @ClassName DistributorController
 * <AUTHOR>
 * @Date 19:40
 * @Version 1.0
 **/
@Validated
@RestController
@RequestMapping("apiBoss/distributor")
public class DistributorController {

    @Resource
    private DistributorService distributorService;
    @Resource
    private UserService userService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private InitiateContractRecordService initiateContractRecordService;

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/getAllSecondDistributor           新增、编辑经销商客户时获取所有的二级经销商
     * @apiName getAllSecondDistributor
     * @apiGroup distributor
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "id": 2,                           # 经销商的id
     *             "name": "test"                     # 经销商的名称
     *         },
     *         {
     *             "id": 1,
     *             "name": "test"
     *         },
     *         {
     *             "id": 3,
     *             "name": "33"
     *         }
     *     ],
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/getAllSecondDistributor")
    public ResultObject getAllSecondDistributor() {
        //获取所有的二级经销商
        Long distributorId = SecurityUtils.getDistributorId();
        Set<DistributorPO> distributorPOSet = distributorService.getSecondDistributors(distributorId);
        return ResultObject.success(distributorPOSet);
    }

// @formatter:off
    /**
     * @api {post} /apiBoss/distributor/create    boss系统新建代理商
     * @apiName createDistributor
     * @apiGroup distributor
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"name" : "深圳二级代理商",                # 经销商名字,必填
     * 	"contactPerson" : "李总",                 # 经销商联系人，必填
     * 	"contactPhone":"17700000000",             # 经销商联系电话，必填
     * 	"address":"深圳市",                       # 经销商地址，必填
     * 	"customerManager" : "客户经理",           # 客户经理
     * 	"managerPhone":"1400000000",              # 客户经理电话
     * 	"startDate" :"2018-08-02",                # 服务开启时间，必填
     * 	"endDate":"2019-08-02",                   # 服务结束时间，必填
     * 	"totalDialogFlow" : 66,                   # AI话术总量，必填
     * 	"status" : "ENABLED",                     # 是否启用,ENABLED(1, "启用"),CLOSED(2, "关闭");必填
     * 	"comment":"备注",                         # 备注
     * 	"billingMethod":"MONTHLY"                 # 计费方式,MONTHLY(0,"按月计费"),YEARLY(1,"按年计费") ，必填
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "YZSFDIJZ",
     *     "resultMsg": "新建经销商成功!",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/create")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.DISTRIBUTOR,
            contentExpression = "'用户' + #currentUser.name + '添加了新的代理商' + #distributorVO.name "
    )
    public ResultObject createDistributor(@Validated(value = AddBossDistributorValidate.class) @RequestBody DistributorVO distributorVO) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long createdUser = SecurityUtils.getUserId();
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        Long parentId = distributorPO.getParentId();
        //parentId不为0的经销商不能新建经销商以及经销商客户
        if (parentId != 0 || !distributorPO.getRightToSplit()) {
            return ResultObject.fail(ComErrorCode.FORBIDDEN, "您没有权限新建经销商");
        }
        //设置parentId
        distributorVO.setParentId(distributorId);
        distributorVO.setUserId(createdUser);

        //设置经销商的id，用来减去库存
        distributorVO.setDistributorId(distributorId);
        String defaultPassword = distributorService.addBossDistributor(distributorVO);
        return ResultObject.success(defaultPassword);
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/getAIRobotAndDialogFlow   获取当前代理商的AI坐席以及话术数据供前端显示
     * @apiName getAIRobotAndDialogFlow
     * @apiGroup distributor
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "totalRobot": 100,                      # 机器坐席总量
     *         "soldRobot": 32,                        # 机器坐席已售
     *         "remainingRobot": 68,                   # 机器坐席剩余
     *         "totalDialogFlow": 100,                 # 话术总量
     *         "soldDialogFlow": 10,                   # 话术消耗
     *         "remainingDialogFlow": 90,              # 话术剩余
     *         "customerManager": "客户经理",          # 客户经理
     *         "managerPhone": "13699999999"           # 经理电话
     *     },
     *     "requestId": "UYXJNUGZ",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    //boss系统获取AI坐席总量（剩余）以及话术总量（剩余）供前端显示
    @PostMapping("/getAIRobotAndDialogFlow")
    public ResultObject getAIRobotAndDialogFlow() {
        Long distributorId = SecurityUtils.getDistributorId();
        DistributorAIVO totalAndRemaining = distributorService.getAIRobotAndDialogFlow(distributorId);
        return ResultObject.success(totalAndRemaining);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/getAIRobotAndDialogFlowByDistributor   获取 （目标代理商）的AI坐席以及话术数据供前端显示
     * @apiName getAIRobotAndDialogFlowByDistributor
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId                                    # 目标代理商d，必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "totalRobot": 100,                      # 机器坐席总量
     *         "soldRobot": 32,                        # 机器坐席已售
     *         "remainingRobot": 68,                   # 机器坐席剩余
     *         "totalDialogFlow": 100,                 # 话术总量
     *         "soldDialogFlow": 10,                   # 话术消耗
     *         "remainingDialogFlow": 90,              # 话术剩余
     *         "customerManager": "客户经理",          # 客户经理
     *         "managerPhone": "13699999999"           # 经理电话
     *     },
     *     "requestId": "UYXJNUGZ",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    //boss系统获取AI坐席总量（剩余）以及话术总量（剩余）供前端显示
    @PostMapping("/getAIRobotAndDialogFlowByDistributor")
    public ResultObject getAIRobotAndDialogFlowByDistributor(@RequestParam Long distributorId) {
        DistributorAIVO totalAndRemaining = distributorService.getAIRobotAndDialogFlow(distributorId);
        return ResultObject.success(totalAndRemaining);
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/updateDistributor   更新经销商信息
     * @apiName updateDistributor
     * @apiGroup distributor
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"distributorId":15,                            # 要更新的代理商的id
     * 	"name" : "深圳二级代理商",                     # 代理商的名字
     * 	"contactPerson" : "李总",                      # 代理商的联系人
     * 	"contactPhone":"17700000000",                  # 代理商的联系方式
     * 	"address":"深圳市",                            # 代理商的地址
     * 	"customerManager" : "bianji客户经理",          # 客户经理
     * 	"managerPhone" : "13455566666",                # 客户经理电话
     * 	"comment":"编辑备注",                          # 备注
     * 	"billingMethod":"MONTHLY"                      # 计费方式
     * }
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "更新经销商成功！",
     *     "requestId": "FDEXGUMT",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateDistributor")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorVO.distributorId",
            contentExpression = "'用户' + #currentUser.name + '更新了代理商' + #distributorVO.name + '的信息' "
    )
    public ResultObject updateDistributor(@RequestBody DistributorVO distributorVO) {
        distributorVO.calculate(distributorVO);
        distributorService.updateBossDistributor(distributorVO, SecurityUtils.getUserId());
        return ResultObject.success(null, "更新经销商成功！");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/dilatation     扩容
     * @apiName dilatation
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId                                    # 要扩容的经销商的id，必填
     * @apiParam {Integer} robotCount                                    # 要增加的AI坐席数量
     * @apiParam {Integer} dialogFlowCount                               # 要增加的AI话术数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "扩容成功！",
     *     "requestId": "RROXLYBG",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on

    //扩容涉及给目标代理商增加坐席及话术；还涉及减库存的操作;扩容只更改本身的AI坐席数，不会更改自身的话术数；
    @PostMapping("/dilatation")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = "'用户' + #currentUser.name + '对代理商[distributorId=' + #distributorId + ']进行了扩容操作' "
    )
    public ResultObject dilatation(@RequestParam Long distributorId,
                                  @RequestParam(defaultValue = "0", required = false) BigDecimal robotCount,
                                  @RequestParam(defaultValue = "0", required = false) Integer dialogFlowCount) {
        Long firstDistributorId = SecurityUtils.getDistributorId();
        Long updateUser = SecurityUtils.getUserId();
        distributorService.dilatation(distributorId, updateUser, firstDistributorId, robotCount, dialogFlowCount);
        return ResultObject.success(null, "扩容成功！");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/list     列出所有的经销商
     * @apiName getAllDistributor
     * @apiGroup distributor
     *
     * @apiParam {Integer} pageNum                          # 第几页
     * @apiParam {Integer} pageSize                         # 页面大小
     * @apiParam {String} name                              #搜索渠道商
     * @apiParam {String} contactPerson                     #搜索联系人
     * @apiParam {String} contactPhone                      #搜索联系电话
     * @apiParam {String} customerManager                   #搜索客户经理
     * @apiParam {UserStatusEnum} status                    # 账号状态的枚举。ENABLED(1, "启用"),CLOSED(2, "关闭");
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 1,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "id": 9,                                     # 经销商的id
     *                 "parentId": 8,                               # 经销商的父节点id
     *                 "name": "杭州二级代理商",                    # 经销商名字
     *                 "contactPerson": "3",                        # 经销商联系人
     *                 "contactPhone": "4",                         # 经销商联系电话
     *                 "address": "2",                              # 地址
     *                 "robotLimit": null,                          # ai并发数量
     *                 "dialogLimit": null,                         # 话术
     *                 "customerManager": "5",                      # 客户管理员
     *                 "comment": "345",                            # 备注
     *                 "chargingMethod": null,                      # 充值手段
     *                 "marketScreen": null,                        # 市场屏幕
     *                 "startDate": "2018-10-19",                   # 开始服务时间
     *                 "endDate": "2018-11-14",                     # 结束服务时间
     *                 "totalRobotDisplay": 9,                      # AI坐席总量
     *                 "soldRobotDisplay": 8,                       # AI坐席已售
     *                 "remainingRobotDisplay": 1,                  # AI坐席剩余
     *                 "totalDialogFlow": 7,                        # AI话术总量
     *                 "soldDialogFlow": 6,                         # AI话术已售
     *                 "remainingDialogFlow": 1,                    # AI话术剩余
     *                 "billingMethod": "MONTHLY",                  # 计费方式  MONTHLY(0,"按月计费"),YEARLY(1,"按年计费");
     *                 "managerPhone": null,                        # 经理电话
     *                 "status": "ENABLED"                          # 账号状态
     *             }
     *         ]
     *     },
     *     "requestId": "BWBCEAXA",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping(value = "/list")
    public ResultObject list(DistributorQueryVO distributorQuery) {
        Long distributorId = SecurityUtils.getDistributorId();
        distributorQuery.setDistributorId(distributorId);
        distributorQuery.setSystemEnum(SystemEnum.BOSS);
        if(!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)) {
            distributorQuery.setCreateUserId(SecurityUtils.getUserId());
        }
        PageResultObject distributors = distributorService.listBossDistributor(distributorQuery);
        return ResultObject.success(distributors);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/updateStatus     经销商更改账号的状态（启用、禁用）
     * @apiName updateStatus
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId              # 代理商的id.必填
     * @apiParam {boolean} flag                    # 更改状态的标志 ，true: 启用，false:禁用；默认启用true
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateStatus")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = "'用户' + #currentUser.name + '对代理商[distributorId=' + #distributorId + ']进行了状态变更操作, 状态变更为' + #flag "
    )
    public ResultObject updateStatus(@RequestParam @NotNull(message = "经销商的id不能为空") Long distributorId,
                                    @RequestParam(required = false, defaultValue = "true") boolean flag) {
        Long updateUser = SecurityUtils.getUserId();
        distributorService.updateStatus(updateUser, distributorId, flag);
        return ResultObject.success(null, "启用/禁用成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributor/resetPassword     boss系统代理商重置密码
     * @apiName resetPassword
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId                                    # 经销商的id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "重置密码成功",
     *     "requestId": "DVMEQOMU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/resetPassword")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = "'用户' + #currentUser.name + '对代理商[distributorId=' + #distributorId + ']进行了重置密码操作'"
    )
    public ResultObject resetPassword(@RequestParam Long distributorId) {
        Long updateUser = SecurityUtils.getUserId();
        String password = userService.resetDistributorPassword(distributorId, updateUser);
        return ResultObject.success(password);
    }

// @formatter:off
    /**
     * @api {get} /apiBoss/distributor/getDistributorInfo    编辑代理商时带出的原有信息
     * @apiName getDistributorInfo
     * @apiGroup distributor
     * @apiParam {Long} distributor                      #代理商ID
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *          {
     *            "id": 60,
     *            "parentId": 0,
     *            "name": "杭州一知智能科技有限公司",
     *            "contactPerson": "范范",
     *            "contactPhone": "13812345691",
     *            "address": "浙江省杭州市",
     *            "robotLimit": null,
     *            "dialogLimit": null,
     *            "customerManager": "封君",
     *            "comment": "伟大的共产主义接班人",
     *            "chargingMethod": null,
     *            "marketScreen": null,
     *            "startDate": "2018-06-05",
     *            "endDate": "2021-07-01",
     *            "totalRobotDisplay": 36,
     *            "soldRobotDisplay": 0,
     *            "remainingRobotDisplay": 36,
     *            "totalDialogFlow": 999,
     *            "soldDialogFlow": 0,
     *            "remainingDialogFlow": 0,
     *            "billingMethod": "YEARLY",
     *            "managerPhone": null
     *        }
     *     ],
     *    "requestId": "AXGAPZJS",
     *    "resultMsg": "执行成功",
     *    "errorStackTrace": null
     *}
     */
    // @formatter:on
    @GetMapping("/getDistributorInfo")
    public ResultObject getDistributorInfo(@RequestParam(value = "distributorId") Long distributorId) {
        DistributorListVO distributorListVO = distributorService.getDistributorInfo(distributorId);
        return ResultObject.success(distributorListVO);
    }

    /**
     * 设置代理商合同模板id
     */
    @PostMapping("/setContractId")
    public ResultObject setContractId(@RequestParam("contractId") String contractId) {
        distributorService.setContractId(SecurityUtils.getDistributorId(), contractId);
        return ResultObject.success(null, "配置成功");
    }

    /**
     * 获取代理商合同模板id
     */
    @PostMapping("/getContractId")
    public ResultObject getContractId() {
        String contractId = distributorService.getContractIdByDistributor(SecurityUtils.getDistributorId());
        return ResultObject.success(contractId);
    }

    /**
     * 合同发起表单
     */
    @PostMapping("/initiateContract")
    public ResultObject initiateContract(@RequestBody DistributorInitiateContractVO req) {
        req.setDistributorId(SecurityUtils.getDistributorId());
        req.setUserId(SecurityUtils.getUserId());
        distributorService.initiateContract(req);
        return ResultObject.success();
    }

    /**
     * 合同发起记录
     */
    @PostMapping("/getContractList")
    public ResultObject<PageResultObject<InitiateContractRecordPO>> getContractList(@RequestBody InitiateContractRecordQueryVO req) {
        req.setDistributorId(SecurityUtils.getDistributorId());
        PageResultObject<InitiateContractRecordPO> pageInfo = initiateContractRecordService.selectPageInfo(req);
        return ResultObject.success(pageInfo);
    }
}
