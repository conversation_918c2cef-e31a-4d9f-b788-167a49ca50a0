package com.yiwise.boss.api.controller.customer;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.RobotStasPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.dto.TenantAiccPartDTO;
import com.yiwise.core.model.dto.TradeTypeInfoDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.request.ControlDataEntryRequest;
import com.yiwise.core.model.request.TenantSmsSendRequest;
import com.yiwise.core.model.vo.boss.DirectCustomerBatchQueryVO;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.tenant.CallOutVersionVO;
import com.yiwise.core.model.vo.tenant.TenantVersionVO;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.validate.boss.customer.AddBossDirectCustomerValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR> yangdehong
 * @date : 2018/10/26 10:41 AM
 * @description: 代理商客户管理
 */
@Validated
@RestController
@RequestMapping("apiBoss/directCustomer")
public class DirectCustomerController {

    @Resource
    private DirectCustomerService directCustomerService;
    @Resource
    private UserService userService;
    @Resource
    private DistributorCustomerService distributorCustomerService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private TenantService tenantService;


    // @formatter:off
    /**
     * @api {get} /apiBoss/directCustomer/getIndustryList   获取行业列表
     * @apiName getIndustryList
     * @apiGroup customer
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "name": "OTHER",
     *             "desc": "其他产品推广",
     *             "subTypes": [
     *                 {
     *                     "name": "OTHER",
     *                     "desc": "其他产品推广"
     *                 }
     *             ]
     *         },
     *     ],
     *     "requestId": null,
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getIndustryList")
    public ResultObject getIndustryList() {
        List<TradeTypeInfoDTO> page = directCustomerService.getAllIndustryOptions();
        return ResultObject.success(page, "获取成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/directCustomer/list       直销客户列表
     * @apiName listAllDirectCustomer
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {Enum} accountType                                # 账号类型的枚举  FORMAL(0,"正式") ON_TRIAL(1,"试用");
     * @apiParam {String} companyName                              # 搜索 客户名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {String} accountManager                           # 搜索 客户经理
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false
     * @apiParam {TenantStatusEnum} status                         # 是否启用 ENABLED(1, "启用"),CLOSED(2, "关闭");
     * @apiParam {Long} dialogFlowId                               # 话术id
     * @apiParam {String} subAccountPhone                          # 根据子账号电话进行搜索客户
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     * @apiParam {String} enableConcurrency                       # 过滤是否启用一线多并发，true： 启用， false： 禁用
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 5,
     *         "totalElements": 5,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 5,                                            # 客户id
     *                 "tenantName": "杭州一知智能科技有限公司",                # 客户名称
     *                 "linkman": "达康书记",                                    # 联系人
     *                 "phoneNumber": "***********",                             # 联系方式
     *                 "accountManager": null,                                   # 客户经理
     *                 "accountType": "FORMAL",                                  # 账户类型
     *                 "comment": null,                                          # 备注
     *                 "dialogFlowId": 1,                                        # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10,                                 # AI 并发数
     *                 "bindingId": null,                                        # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                    # 账户余额
     *                 "startTime": "2018-08-02",                                # 服务开始时间
     *                 "endTime": "2018-08-17",                                  # 服务结束时间
     *                 "distributorId": 0,                                       # 经销商的id
     *                 "accountManagePhone" : "***********",                     # 客户经理的电话
     *                 "status": ENABLED,                                        # 是否启用 ENABLED(1, "启用"),CLOSED(2, "关闭");
     *                 "accountFareView": "206.00" ,                             # 账户余额显示
     *                 "forbiddenCount" : 1                                      # 禁用的总次数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "robotList": [                                            # 机器坐席的集合
     *                     {
     *                         "robotId": 2,                                     # 机器坐席的ｉｄ
     *                         "count": 10, 　                                   # 机器坐席的数量
     *                         "tenantId": 5,                                    # 对应的客户的id
     *                         "comment": null,                                  # 备注
     *                         "startTime": "2018-07-01",                        # 开始服务时间
     *                         "endTime": "2019-08-31",                          # 结束服务时间
     *                         "expirationTime": 60                              # 该客户机器坐席剩余服务时间
     *                     }
     *                 ]
     *             }
     *         ]
     *     },
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/list")
    public ResultObject listAllDirectCustomer(@RequestBody DirectCustomerQueryVO query) {
        //从securityUtil中去获取distributorId
        Long distributorId = SecurityUtils.getDistributorId();
        query.setDistributorId(distributorId);
        query.setSystemEnum(SystemEnum.BOSS);
        if(!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)) {
            query.setCreateUserId(SecurityUtils.getUserId());
        }
        PageResultObject page = directCustomerService.listAllCustomer(query, true);
        return ResultObject.success(page);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/directCustomer/allTenantIdAndNamePair  获取所有分销商客户的id和名字键值对列表
     * @apiName allTenantIdAndNamePair
     * @apiGroup customer-directCustomer
     *
     * @apiParam {String} searchName 搜索客户名称
     * @apiSuccessExample Response 200 Example
     *
     *{
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 416,                 # 客户id
     *             "name": "13100002224"            # 客户名称
     *         }
     *     ],
     *     "requestId": "MXQCYHZP",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/allTenantIdAndNamePair")
    public ResultObject allTenantIdAndNamePair(@RequestParam(required = false, name = "searchName") String searchName) {
        Long distributorId = SecurityUtils.getDistributorId();
        return ResultObject.success(directCustomerService.getTenantIdAndNamePairList(distributorId, searchName));
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/addDirectCustomer    添加直销客户
     * @apiName addDirectCustomer
     * @apiGroup customer-directCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantName" : "万达集团",                      # 客户名，必填
     *     "linkman" : "易建联",                           # 联系人 ，必填
     *     "phoneNumber" : "***********",                  # 联系方式, 必填
     *     "password":"password",                          # 密码
     *     "aiConcurrencyLevel" : 500,                     # AI并发数,必填
     *     "accountManager" : "lisi",                      # 客户经理
     *     "comment" : "备注" ,                            # 备注
     *     "startTime" : "2018-10-11",                     # 服务开始时间,必填
     *     "endTime" : "2019-10-11",                       # 服务结束时间,必填
     *     "accountType":"FORMAL"     ,                    # 账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *     "accountManagePhone" : "***********",           # 客户经理的电话
     *     "status" : "ENABLED",                           # 分配賬號的状态， ENABLED(1, "启用"),CLOSED(2, "关闭");
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": "添加直销客户成功！",
     *     "requestId": "RHATBESM",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addDirectCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']添加了客户 ' + #customerInsertVO.companyName"
    )
    public ResultObject addDirectCustomer(@RequestBody @Validated(value = AddBossDirectCustomerValidate.class) CustomerInsertVO customerInsertVO) {
        //从securityUtil中获取
        Long distributorId = SecurityUtils.getDistributorId();
        Long currentUserId = SecurityUtils.getUserId();
        customerInsertVO.setCreateUserId(currentUserId);
        customerInsertVO.setUpdateUserId(currentUserId);
        customerInsertVO.setDistributorId(distributorId);
        String defaultPassword = directCustomerService.addBossCustomer(customerInsertVO);
        return ResultObject.success(defaultPassword);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/updateDirectCustomer  更新直销客户信息
     * @apiName updateDirectCustomer
     * @apiGroup customer-directCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantId":1,                                        # 客户id，必填
     *     "tenantName" : "杭州一知智能科技有限公司",           # 客户名称
     *     "linkman" : "达康书记",                              # 联系人
     *     "accountManager" : "祁同伟",                         # 客户经理
     *     "phoneNumber" : "110",                               # 联系方式
     *     "comment" : "意向客户，重点关注！",                  # 评论
     *     "accountManagePhone": "***********",                 # 客户经理的电话
     *     "accountType" : "FORMAL"                             # 账号类型 FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "更新直销客户成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateDirectCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户' + #tenantPO.companyName + '的信息'",
            refIdExpression = "#tenantPO.tenantId"
    )
    public ResultObject updateDirectCustomer(@RequestBody CustomerUpdateVO tenantPO) {
        Long currentUserId = SecurityUtils.getUserId();
        tenantPO.setUpdateUserId(currentUserId);
        directCustomerService.updateDirectCustomer(tenantPO);
        return ResultObject.success(null, "更新直销客户成功");
    }

    /**
     * 修改名称与手机号
     *
     * @param modifyCustomerInfoVO 参数
     * @return 结果
     */
    @PostMapping("/modifyCustomerNameAndPhone")
    public ResultObject modifyCustomerNameAndPhone(@RequestBody @Validated ModifyCustomerInfoVO modifyCustomerInfoVO) {
        Long updateUserId = SecurityUtils.getUserId();
        modifyCustomerInfoVO.setUpdateUserId(updateUserId);
        boolean result = directCustomerService.modifyCustomerNameAndPhone(modifyCustomerInfoVO.getTenantId(), modifyCustomerInfoVO.getUpdateUserId(), modifyCustomerInfoVO.getLinkman(), modifyCustomerInfoVO.getPhoneNumber());
        return ResultObject.success(result, "修改成功");
    }

    /**
     * 修改账号类型
     *
     * @param modifyCustomerTypeVO 参数
     * @return 结果
     */
    @PostMapping("/modifyCustomerAccountType")
    public ResultObject modifyCustomerType(@RequestBody @Validated ModifyCustomerTypeVO modifyCustomerTypeVO) {
        Long updateUserId = SecurityUtils.getUserId();
        modifyCustomerTypeVO.setUpdateUserId(updateUserId);
        boolean result = directCustomerService.modifyCustomerType(modifyCustomerTypeVO.getTenantId(), modifyCustomerTypeVO.getUpdateUserId(), modifyCustomerTypeVO.getAccountType());
        return ResultObject.success(result, "修改成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/directCustomerDelay    直销客户延期
     * @apiName directCustomerDelay
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newEndTime                        # 要延期的时间,必填
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {string} tenantAiccPartType                               # 类型 crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本") FUN_MULTI_CONCURRENCY(8, "一线多并发"),
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/directCustomerDelay")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '进行了延期操作'",
            refIdExpression = "#tenantId"
    )
    public ResultObject directDelay(@RequestParam Long id, @RequestParam LocalDate newEndTime, @RequestParam Long tenantId,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        distributorCustomerService.opeAndBossDistributorCustomerDelay(id,newEndTime,tenantId,SecurityUtils.getUserId(),tenantAiccPartType);
        return ResultObject.success(null, "延期成功!");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/directCustomerDilatation    直销客户扩容
     * @apiName directCustomerDilatation
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {string} tenantAiccPartType                           # 类型，必填crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本")
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/directCustomerDilatation")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '进行了扩容操作'",
            refIdExpression = "#tenantId"
    )
    public ResultObject directDilatation(@RequestParam Long tenantId,
                                        @RequestParam Integer count,
                                        @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                        @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime
                                        ,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long userId = SecurityUtils.getUserId();
        directCustomerService.bossDilatation(tenantId, count, startTime, endTime, distributorId, userId,tenantAiccPartType);
        return ResultObject.success(null, "扩容成功");
    }
// @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/calculateDelayCost 代理商客户延期费用 结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateDelayCost
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Long} ObjectCount                                     # 开通数量，比如开通多少 AI 并发数 非必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {LocalDate} newEndTime                               # 要延期的时间,必填
     * @apiParam {Long} robotId                               # 机器人的id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "代理商客户功能开通消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDelayCost")
    public ResultObject calculateDelayCost(
            @RequestParam(required = false) Double ObjectCount,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "新的服务结束日期不能为空") @RequestParam LocalDate newEndTime,
            @NotNull(message = "机器人id不能为空") @RequestParam Long robotId) {
        Double result=distributorCustomerService.calculateDelayCost(ObjectCount,distributorId,tenantId,newEndTime,robotId);
        return ResultObject.success(result, "代理商客户延期消费计算成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/calculateAgentAndAiAssistantCost 计算 开通人工坐席/AI 助理的费用 改动之前接口比较费事所以重新写个
     * @apiName calculateAgentAndAiAssistantCost
     * @apiGroup distributorCustomer
     *
     * @apiParam {Double} count                                     # 新增数量 必填
     * @apiParam {LocalDate} startTime                      # 服务开始时间必填
     * @apiParam {LocalDate} endTime                        # 服务结束时间必填
     * @apiParam {Long} distributorId                               # 代理商id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "开通功能消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAgentAndAiAssistantCost")
    public ResultObject calculateAiCost(
            @NotNull(message = "新增数量不能为空")@RequestParam Double count,
            @NotNull(message = "服务开始时间不能为空")@RequestParam LocalDate startTime,
            @NotNull(message = "服务结束时间不能为空")@RequestParam LocalDate endTime,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        Double result=distributorCustomerService.calculateAgentAndAiAssistantCost(count,distributorId,startTime, endTime,rechargePrestoreStreamFunEnum);
        return ResultObject.success(result, "开通功能+"+rechargePrestoreStreamFunEnum.getDesc()+"消耗费用计算成功");
    }
// @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/calculateAdjustingServiceCycleCost    经销商客户调整服务周期消费   结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateAdjustingServiceCycleCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 扣除坐席并发的数量
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAdjustingServiceCycleCost")
    @Deprecated
    public ResultObject calculateAdjustingServiceCycleCost(@RequestParam Long robotId,
                                                          @RequestParam(required = false) LocalDate newStartTime,
                                                          @RequestParam(required = false) LocalDate newEndTime,
                                                          @RequestParam Long tenantId,
                                                          @RequestParam Long distributorId,
                                                          @RequestParam(required = false) Long reduceAiAmount) {
        Double cost= directCustomerService.calculateAdjustingServiceCycleCost(robotId,tenantId,distributorId,SecurityUtils.getUserId(), newStartTime, newEndTime, reduceAiAmount);
        return ResultObject.success(cost, "调整服务周期消费计算成功!");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/calculateDilatationCost 代理商客户扩容消费   返回正数表示退回  负数表示消耗
     * @apiName calculateDilatationCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDilatationCost")
    public ResultObject calculateDilatationCost(@RequestParam Long tenantId,
                                               @RequestParam Integer count,
                                               @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                               @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime,
                                               @RequestParam Long distributorId,
                                               @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartEnum) {
        Double cost=directCustomerService.calculateDilatationCost(tenantId, count, startTime, endTime, distributorId,tenantAiccPartEnum);
        return ResultObject.success(cost, "扩容成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/resetPassword     直销客户与经销商客户重置密码
     * @apiName resetPassword
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Long} tenantId                                    # 客户id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "xu763521",
     *     "requestId": "DVMEQOMU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/resetPassword")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '密码进行了重置'",
            refIdExpression = "#tenantId"
    )
    public ResultObject resetPassword(@RequestParam Long tenantId) {
        Long updateUser = SecurityUtils.getUserId();
        String password = userService.resetTenantPassword(tenantId, updateUser);
        return ResultObject.success(password);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/updateStatus     直销客户与经销商客户更改账号的状态（启用、禁用）
     * @apiName updateStatus
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Long} tenantId                                    # 客户id，必填
     * @apiParam {TenantStatusEnum} status                          # 状态，ENABLED(1, "启用"),CLOSED(2, "关闭");
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateStatus")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT_STATUS,
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '状态进行了更新='+#status",
            refIdExpression = "#tenantId"
    )
    public ResultObject updateStatus(@RequestParam @NotNull(message = "客户id不能为空") Long tenantId,
                                    @RequestParam(required = false) TenantStatusEnum status) {
        Long currentUserId = SecurityUtils.getUserId();
        directCustomerService.updateStatus(tenantId, status, currentUserId);
        return ResultObject.success(null, "操作成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/batchUpdateStatus     直销客户与经销商客户更改账号的状态（批量启用、禁用）
     * @apiName batchUpdateStatus
     * @apiGroup customer-directCustomer
     *
     * @apiParam {list} tenantIds                                    # 客户id，必填
     * @apiParam {TenantStatusEnum} status                          # 状态，ENABLED(1, "启用"),CLOSED(2, "关闭");
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/batchUpdateStatus")
    public ResultObject updateStatus(@RequestBody DirectCustomerBatchQueryVO directCustomerBatchQueryVO) {
        Long currentUserId = SecurityUtils.getUserId();
        //从securityUtil中去获取distributorId
        Long distributorId = SecurityUtils.getDistributorId();
        directCustomerBatchQueryVO.setDistributorId(distributorId);
        if(!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)) {
            directCustomerBatchQueryVO.setCreateUserId(SecurityUtils.getUserId());
        }
        directCustomerService.batchUpdateStatus(directCustomerBatchQueryVO.getTenantIds(), directCustomerBatchQueryVO.getOpStatus(), currentUserId,directCustomerBatchQueryVO,directCustomerBatchQueryVO.getSelectAll());
        return ResultObject.success(null, "操作成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/adjustingServiceCycle    直销客户调整服务周期
     * @apiName adjustingServiceCycle
     * @apiGroup customer-directCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 要扣除的坐席的并发数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/adjustingServiceCycle")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '状态进行了服务周期调整'",
            refIdExpression = "#tenantId"
    )
    @Deprecated
    public ResultObject adjustingServiceCycle(@RequestParam Long id,
                                             @RequestParam(required = false) LocalDate newStartTime,
                                             @RequestParam(required = false) LocalDate newEndTime,
                                             @RequestParam Long tenantId,
                                             @RequestParam(required = true) Long reduceAiAmount,
                                             @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        //boss的直销客户应该给自己重新计算库存
        Long distributorId = SecurityUtils.getDistributorId();
        directCustomerService.adjustingServiceCycleByBossDirectCustomer(id, newStartTime, newEndTime, tenantId, SecurityUtils.getUserId(), distributorId, reduceAiAmount, tenantAiccPartType);
        return ResultObject.success(null, "调整服务周期成功!");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/openAiccPartFunction    AICC系统开通子系统功能
     * @apiName openAiccPartFunction
     * @apiGroup directCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *      	"tenantId": 1
     *      	"enableConcurrency": true  #外呼一线多并发
     *      	"compressAudio": true  #外呼音频压缩
     *      	"useYiwiseAsr": true   # 外呼使用一知ASR
     *      	"enableSpeechCorrection": true   # 开启语音学习
     *      	"compressAudioCallin": true  #音频压缩(呼入)
     *      	"useYiwiseAsrCallin": true   #使用一只sar（呼入）
     *           "qcCostUnit":3              #qc 质检费用
     *      	"enableStatsMsgPush": true  # 外呼推送统计信息
     *      	"enableMessageInvoke": true #  外呼短信回掉
     *      	"redialLimit": 1  #重播次数, -1表示不限制
     *      	"dialIntervalStart": '00:00:00'  #外呼可选时段开始
     *      	"dialIntervalEnd": "00:00:00" #外呼可选时段结束
     *          "enableAiAssistant" ： true    # 智能辅助
     *      	"tenantAiccPartType": "crm_out_call_platform"  # 类型 crm_out_call_platform(0,"智能外呼"), crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本")
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/openAiccPartFunction")
    public ResultObject openAiccPartFunction(@RequestBody TenantAiccPartDTO tenantAiccPartDTO) {
	    tenantAiccPartDTO.processPriceUnit();
        tenantAiccPartDTO.setUpdateUserId(SecurityUtils.getUserId());
        directCustomerService.openAiccPartFunction(tenantAiccPartDTO);
        return ResultObject.success(null, "配置成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/directCustomer/queryAiccPartRobots    查询AICC子系统robots
     * @apiName queryAiccPartRobots
     * @apiGroup directCustomer
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/queryAiccPartRobots")
    public ResultObject queryAiccPartRobots(@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType,@RequestParam(required = false) Long tenantId) {
        Map<TenantAiccPartEnum, RobotStasPO> tenantAiccPartList=directCustomerService.queryAiccPartRobots(tenantId);
        Map<TenantAiccPartEnum,List<RobotExpirationTimeVO>> result=new HashMap<>();
        RobotStasPO robotStasPO=tenantAiccPartList.get(tenantAiccPartType);
        if(Objects.nonNull(robotStasPO)&&!CollectionUtils.isEmpty(robotStasPO.getRobotExpirationTimeVOList())){
            result.put(tenantAiccPartType,robotStasPO.getRobotExpirationTimeVOList());
        }
        return ResultObject.success(result, "配置成功");
    }
    @GetMapping("/getTenantByTenantId")
    public ResultObject getTenantByTenantId(@RequestParam Long tenantId) {
        TenantPO tenantPO= tenantService.getTenantByTenantId( tenantId);
        return ResultObject.success(tenantPO, "获取成功");
    }

    /**
     * 自动化营销/会员中心入口开关
     */
    @PostMapping("/controlDataEntry")
    public ResultObject<Boolean> controlDataEntry(@RequestBody ControlDataEntryRequest controlDataEntryRequest) {
        controlDataEntryRequest.setUserId(SecurityUtils.getUserId());
        tenantService.controlDataEntry(controlDataEntryRequest);

        if(!controlDataEntryRequest.getEnableStatus()){
            controlDataEntryRequest.setTaoCreateCustomer(YesOrNoEnum.NO.getCode());
            controlDataEntryRequest.setYzCreateCustomer(YesOrNoEnum.NO.getCode());
            controlDataEntryRequest.setMinappCreateCustomer(YesOrNoEnum.NO.getCode());
            controlDataEntryRequest.setPlatformCreateCustomer(YesOrNoEnum.NO.getCode());
            controlDataEntryRequest.setClearOrderTime(true);
        }
        if(null!=controlDataEntryRequest.getTaoCreateCustomer()||
                null!=controlDataEntryRequest.getYzCreateCustomer()||
                null!=controlDataEntryRequest.getPlatformCreateCustomer()||
                null!=controlDataEntryRequest.getMinappCreateCustomer()){
            tenantService.controlOrderMaRequest(controlDataEntryRequest);
        }
        return ResultObject.success(true);
    }


    /**
     * 智能外呼版本配置 /apiBoss/directCustomer/updateAiccVersion
     */
    @PostMapping("/updateAiccVersion")
    public ResultObject updateAiccVersion(@RequestBody TenantVersionVO tenantVersionVO) {
        tenantVersionVO.setUpdateUserId(SecurityUtils.getUserId());
        directCustomerService.updateAiccVersion(tenantVersionVO);
        return ResultObject.success(null, "配置成功");
    }

    @PostMapping("/setSmsConfig")
    public ResultObject<String> setSmsConfig(@RequestBody TenantSmsSendRequest request){
        return ResultObject.success(tenantService.updateSmsConfig(request));
    }

    /**
     * 客户外呼版本控制
     * @return
     */
    @PostMapping("updateEnableTenantCallOutVersion")
    public ResultObject<Void> updateEnableTenantCallOutVersion(@RequestBody CallOutVersionVO callOutVersionVO){
        tenantService.updateEnableTenantCallOutVersion(callOutVersionVO.getTenantId(),callOutVersionVO.getNewCallOut(),callOutVersionVO.getEnable());
        return ResultObject.success(null,"更新成功");
    }
}
