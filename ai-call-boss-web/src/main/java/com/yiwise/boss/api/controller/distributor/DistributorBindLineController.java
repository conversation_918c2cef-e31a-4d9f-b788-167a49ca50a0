package com.yiwise.boss.api.controller.distributor;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.model.dto.BindLineDTO;
import com.yiwise.core.model.enums.OperationLogLogTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.phonenumber.DeleteDistribtorBindOnePhoneNumberVO;
import com.yiwise.core.model.vo.phonenumber.DistributorBindOnePhoneNumberVO;
import com.yiwise.core.model.vo.phonenumber.UpdateDistributorBindOnePhoneNumberVO;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.ope.platform.DistributorPhoneNumberService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 代理商线路操作
* <AUTHOR> yangdehong
* @date : 2018/10/24 9:38 AM
*/
@RestController
@RequestMapping("apiBoss/distributorBindLine")
public class DistributorBindLineController {

    @Autowired
    private PhoneNumberService phoneNumberService;
    @Autowired
    private DistributorPhoneNumberService distributorPhoneNumberService;

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorBindLine/getDistributorCanBindLine 列出所有可以绑定的线路的线路
     * @apiName getDistributorCanBindLine
     * @apiGroup beBindDistributorId
     *
     * @apiParam {Long} distributorId
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "phoneNumberId": 3,
     *             "phoneNumber": "**********",
     *             "phoneName": "**********",
     *             "phoneType":  LANDLINE,    #  LANDLINE："固话", UNFIXED_CALL："无主叫", MOBILE："手机号码")
     *             "localBillRate": null,
     *             "distributorId": 0,
     *             "tenantPhoneNumberId": 0,
     *             "distributorPhoneNumberId": 0,
     *             "ownerName": "一知智能",
     *             "selfPhoneNumber": true,
     *             "ownerDistributorId": 0,
     *             "ownerTenantId": 0
     *         }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getDistributorCanBindLine")
    public ResultObject getDistributorCanBindLine(@RequestParam Long beBindDistributorId) {
        Long distributorId = SecurityUtils.getDistributorId();
        List<BindLineDTO> PhoneNumberPOS = phoneNumberService.newGetBossDistributorCanBindLine(distributorId, beBindDistributorId);
        return ResultObject.success(PhoneNumberPOS);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorBindLine/lineUpdateQuery 列出所有可以绑定的线路的线路
     * @apiName lineUpdateQuery
     * @apiGroup beBindDistributorId
     *
     * @apiParam {Long} distributorPhoneNumberId
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "totalConcurrence": 3,
     *             "usedConcurrence": 4,
     *             "concurrenceLimit": 5,
     *             "phoneType":  LANDLINE,    #  LANDLINE："固话", UNFIXED_CALL："无主叫", MOBILE："手机号码")
     *             "localBillRate": null,
     *             "distributorId": 0,
     *             "tenantPhoneNumberId": 0,
     *             "distributorPhoneNumberId": 0,
     *             "ownerName": "一知智能",
     *             "selfPhoneNumber": true,
     *             "ownerDistributorId": 0,
     *             "ownerTenantId": 0
     *         }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/lineUpdateQuery")
    public ResultObject lineUpdateQuery(@RequestParam Long distributorPhoneNumberId) {
        Long upDistributorId = SecurityUtils.getDistributorId();
        BindLineDTO result = distributorPhoneNumberService.queryUpdateBindInfo(distributorPhoneNumberId, upDistributorId);
        return ResultObject.success(result, "获取成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorBindLine/bindingOneLine 给代理商绑定一条线路
     * @apiName bindingOneLine
     * @apiGroup distributorBindLine
     *
     *
     * @apiParamExample {json} Request Example
     * {
     *      	"distributorId": 1,
     *      	"phoneNumberId": 22,
     *      	"localBillRate": 23,
     *          "monthlyBillRate":8
     *      }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "绑定线路成功！",
     *     "requestId": "TLTMSHPY",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @PostMapping("/bindingOneLine")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.PHONE_NUMBER, refIdExpression = "#distributorBindOnePhoneNumberVO.phoneNumberId",
            contentExpression = "'用户' + #currentUser.name + '为代理商' + #distributorBindOnePhoneNumberVO.distributorId + '绑定了线路' + #distributorBindOnePhoneNumberVO.phoneNumberId"
    )
    public ResultObject bindingOneLine(@RequestBody DistributorBindOnePhoneNumberVO distributorBindOnePhoneNumberVO) {
        Long userId = SecurityUtils.getUserId();
        Long distributorId = SecurityUtils.getDistributorId();
        distributorPhoneNumberService.newAddBossDistributorPhoneNumber(distributorBindOnePhoneNumberVO, userId, distributorId);
        return ResultObject.success();
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorBindLine/updateBindOneLine 给代理商修改一条线路
     * @apiName updateBindOneLine
     * @apiGroup distributorBindLine
     *
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"distributorId": 1,
     * 	"distributorPhoneNumberId": 4,
     *  "localBillRate": 23,
     *  "monthlyBillRate":8
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "绑定线路成功！",
     *     "requestId": "TLTMSHPY",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateBindOneLine")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.PHONE_NUMBER, refIdExpression = "#updateBindOnePhoneNumberVO.distributorPhoneNumberId",
            contentExpression = "'用户' + #currentUser.name + '更新了绑定线路' "
    )
    public ResultObject updateBindOneLine(@RequestBody UpdateDistributorBindOnePhoneNumberVO updateBindOnePhoneNumberVO) {
        Long userId = SecurityUtils.getUserId();
        Long distributorId = SecurityUtils.getDistributorId();
        distributorPhoneNumberService.newUpdateDistributorPhoneNumberById(updateBindOnePhoneNumberVO, userId, distributorId);
        return ResultObject.success();
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorBindLine/deleteBindOneLine 给代理商删除一条线路
     * @apiName deleteBindOneLine
     * @apiGroup distributorBindLine
     *
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"distributorPhoneNumberId": 531
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "绑定线路成功！",
     *     "requestId": "TLTMSHPY",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/deleteBindOneLine")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.PHONE_NUMBER, refIdExpression = "#deleteDistribtorBindOnePhoneNumberVO.distributorPhoneNumberId",
            contentExpression = "'用户' + #currentUser.name + '解绑了代理商线路' "
    )
    public ResultObject deleteBindOneLine(@RequestBody DeleteDistribtorBindOnePhoneNumberVO deleteDistribtorBindOnePhoneNumberVO) {
        Long distributorId = SecurityUtils.getDistributorId();
        distributorPhoneNumberService.deleteDistributorPhoneNumberById(deleteDistribtorBindOnePhoneNumberVO.getDistributorPhoneNumberId(), distributorId);
        return ResultObject.success();
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorBindLine/seeLineByDistributor    根据代理商id获取指定代理商所有绑定的线路
     * @apiName seeLineByDistributor
     * @apiGroup beBindDistributorId
     *
     * @apiParam {Long} distributorId                                    # 客户id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "phoneNumberId": 3,
     *             "phoneNumber": "**********",
     *             "localBillRate": null,
     *             "distributorId": 0,
     *             "tenantPhoneNumberId": 0,
     *             "distributorPhoneNumberId": 0,
     *             "ownerDistributorId": 0,
     *             "ownerTenantId": 0,
     *             "accountFare":123
     *         }
     *     ],
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/seeLineByDistributor")
    public ResultObject seeLineByDistributor(@RequestParam Long beBindDistributorId) {
        Long upDistributorId = SecurityUtils.getDistributorId();
        List<BindLineDTO> phoneNumberList = phoneNumberService.seeNewLineByDistributor(beBindDistributorId, upDistributorId, null);
        return ResultObject.success(phoneNumberList);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorBindLine/getRechargeLineByDistributor    获取代理商充值线路信息
     * @apiName getRechargeLineByDistributor
     * @apiGroup distributorBindLine
     *
     * @apiParam {Long} distributorId                                    # 客户id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "phoneNumberId": 3,
     *             "phoneNumber": "**********",
     *             "phoneName": "**********",
     *             "phoneType":  LANDLINE,    #  LANDLINE："固话", UNFIXED_CALL："无主叫", MOBILE："手机号码")
     *             "localBillRate": null,
     *             "distributorId": 0,
     *             "tenantPhoneNumberId": 0,
     *             "distributorPhoneNumberId": 0,
     *             "ownerName": "一知智能",
     *             "selfPhoneNumber": true,
     *             "ownerDistributorId": 0,
     *             "ownerTenantId": 0,
     *             "accountFare":123
     *         }
     *     ],
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getRechargeLineByDistributor")
    public ResultObject getRechargeLineByDistributor(@RequestParam Long distributorId) {
        Long ownerDistributorId = SecurityUtils.getDistributorId();
        List<BindLineDTO> phoneNumberList = phoneNumberService.getRechargeLineByDistributor(ownerDistributorId, distributorId);
        return ResultObject.success(phoneNumberList);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorBindLine/seeRechargeLineByDistributor    获取代理商能充值的线路，业务逻辑上已经弃用
     * @apiName seeRechargeLineByDistributor
     * @apiGroup distributorBindLine
     *
     * @apiParam {Long} beBindDistributorId                                    # 客户id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "phoneNumberId": 3,
     *             "phoneNumber": "**********",
     *             "phoneName": "**********",
     *             "phoneType":  LANDLINE,    #  LANDLINE："固话", UNFIXED_CALL："无主叫", MOBILE："手机号码")
     *             "localBillRate": null,
     *             "distributorId": 0,
     *             "tenantPhoneNumberId": 0,
     *             "distributorPhoneNumberId": 0,
     *             "ownerName": "一知智能",
     *             "selfPhoneNumber": true,
     *             "ownerDistributorId": 0,
     *             "ownerTenantId": 0,
     *             "accountFare": 0
     *         }
     *     ],
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/seeRechargeLineByDistributor")
    public ResultObject seeRechargeLineByDistributor(@RequestParam @NotNull Long beBindDistributorId) {
        Long distributorId = SecurityUtils.getDistributorId();
        List<BindLineDTO> phoneNumberList = phoneNumberService.seeRechargeLineByDistributor(distributorId, beBindDistributorId);
        return ResultObject.success(phoneNumberList);
    }

}
