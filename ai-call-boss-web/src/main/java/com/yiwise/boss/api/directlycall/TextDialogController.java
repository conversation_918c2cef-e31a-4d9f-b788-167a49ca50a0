package com.yiwise.boss.api.directlycall;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yiwise.aicall.engine.engine.brain.SemanticManager;
import com.yiwise.aicall.engine.engine.brain.SemanticManagerFactory;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.bo.websocket.UserIdPrincipal;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.dialogflow.BotTypeEnum;
import com.yiwise.core.model.nlp.bo.NlpBeforeChatBO;
import com.yiwise.core.model.nlp.bo.NlpBeforeChatParamsBO;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.TextDialogSessionManager;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.aicall.engine.engine.NlpTextDialogManager;
import com.yiwise.aicall.engine.engine.TextDialogManager;
import com.yiwise.aicall.engine.engine.brain.NlpSemanticManager;
import com.yiwise.aicall.engine.engine.brain.entities.DialogFlow;
import com.yiwise.aicall.engine.model.TextDialogVO;
import com.yiwise.aicall.engine.nlp.NlpAudioManager;
import com.yiwise.aicall.engine.service.DialogFlowLoader;
import com.yiwise.aicall.engine.utils.NlpHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.yiwise.core.config.ApplicationConstant.WEB_SOCKET_TEXT_DIALOG_MSG;


/**
 * <AUTHOR>
 * @date 15/08/2018
 */
@RestController
public class TextDialogController implements TextDialogSessionManager {

    private static final Logger logger = LoggerFactory.getLogger(TextDialogController.class);

    private Cache<String, TextDialogManager> nluTestSoundManagerMap = CacheBuilder.newBuilder().expireAfterAccess(15, TimeUnit.MINUTES).build();
    private Cache<String, NlpTextDialogManager> nluNlpTestSoundManagerMap = CacheBuilder.newBuilder().expireAfterAccess(15, TimeUnit.MINUTES).build();
    private Cache<String, DialogFlowInfoPO> dialogFlowInfoPOMap = CacheBuilder.newBuilder().expireAfterAccess(15, TimeUnit.MINUTES).build();

    @Resource
    private DialogFlowLoader dialogFlowLoader;
    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private TenantService tenantService;
    @Resource
    private UserService userService;

    // @formatter:off
    /**
     * @api {post}  /user/queue/callTaskInfoMsg                 拨打电话webSocket异常处理
     * @apiName handleException
     * @apiGroup webSocket
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code":500,
     *     "data": null,
     *     "requestId":null,
     *     "resultMsg":"发起失败",
     *     "errorStackTrace":null
     * }
     */
    // @formatter:on
    @MessageMapping("/textDialog")
    @SendToUser(WEB_SOCKET_TEXT_DIALOG_MSG)
    public void textDialog(UserIdPrincipal principal, TextDialogVO textDialogVO) {
        logger.debug("cache size = [{}]", nluTestSoundManagerMap.size());
        logger.debug("nlp cache size = [{}]", nluNlpTestSoundManagerMap.size());
        Long dialogFlowId = textDialogVO.getDialogFlowId();
        Boolean testOneNode = textDialogVO.getTestOneNode();
        if (Objects.nonNull(testOneNode) && testOneNode) {
            logger.debug("进入单节点测试");
            if (dialogFlowId!=null) {
                logger.debug("初始化对话系统");
                DialogFlow dialogFlow = null;
                try {
                    dialogFlow = dialogFlowLoader.loadFromRemoteVerbalTrickTraining(dialogFlowId);
                } catch (Exception e) {
                    logger.error("节点测试获取话术失败",e);
                    TextDialogManager nluTestSoundManager = new TextDialogManager(principal, SystemEnum.OPE, dialogFlow, textDialogVO);
                    nluTestSoundManager.whenError("话术未发布");
                    return;
                }
                TextDialogManager nluTestSoundManager = new TextDialogManager(principal, SystemEnum.OPE, dialogFlow, textDialogVO);
                nluTestSoundManagerMap.put(principal.getName(), nluTestSoundManager);
                SemanticManager semanticManager = SemanticManagerFactory.getInstance(nluTestSoundManager, dialogFlow, nluTestSoundManager.getProperties(), null);
                nluTestSoundManager.setSemanticManager(semanticManager);
                semanticManager.enter(textDialogVO.getStepId(), textDialogVO.getNodeId());
                // 单个节点测试才有
                nluTestSoundManager.afterEnter();
            } else {
                logger.debug("文字对话系统获取的参数对象={}", textDialogVO);
                TextDialogManager textDialogManager = nluTestSoundManagerMap.getIfPresent(principal.getName());
                if (textDialogManager == null) {
                    logger.error("通话异常, 未找到对应的对话管理器");
                    return;
                }
                textDialogVO.setAiProgress(textDialogVO.getAiProgress()==100?0:textDialogVO.getAiProgress());
                textDialogManager.oneRound(textDialogVO);

            }

            return;
        }
        if (dialogFlowId!=null) {
            logger.debug("初始化对话系统");
            DialogFlowInfoPO dialogFlowInfoPO = dialogFlowService.selectByKey(dialogFlowId);
            dialogFlowInfoPOMap.put(principal.getName(), dialogFlowInfoPO);
            Long nlpRobotId = dialogFlowInfoPO.getNlpRobotId();
            if (BotTypeEnum.PROFESSIONAL.equals(dialogFlowInfoPO.getBotType())) {
                Long tenantId = dialogFlowInfoPO.getTenantId();
                TenantPO tenantPO = null;
                try {
                    Long userId = principal.getUserId();
                    tenantPO = tenantService.selectByKey(tenantId);
                } catch (Exception e) {
                    logger.error("[LogHub_Warn]文本测试权限出现问题", e);
                }
                //获取话术机器人信息
                NlpBeforeChatParamsBO nlpBeforeChatParamsBO = new NlpBeforeChatParamsBO(tenantId, nlpRobotId, textDialogVO.getProperties(), false, tenantPO == null ? "" : tenantPO.getPhoneNumber());
                NlpBeforeChatBO nlpBeforeChatBO = NlpHelper.beforeChat(nlpBeforeChatParamsBO);
                NlpAudioManager nlpAudioManager = new NlpAudioManager(dialogFlowInfoPO, dialogFlowInfoPO.getNlpRobotId(), nlpBeforeChatBO.getPreAudioTextList());
                nlpAudioManager.preDialog();
                DialogFlow dialogFlow = dialogFlowLoader.loadFromRemoteTextTraining(dialogFlowId);
                NlpTextDialogManager nluTestSoundManager = new NlpTextDialogManager(dialogFlow, principal, SystemEnum.BOSS, nlpBeforeChatBO, dialogFlowInfoPO);
                nluNlpTestSoundManagerMap.put(principal.getName(), nluTestSoundManager);
                nlpBeforeChatBO.setDialogFlowId(dialogFlowInfoPO.getId());
                NlpSemanticManager semanticManager = new NlpSemanticManager(dialogFlow, nlpBeforeChatBO, nluTestSoundManager, null, nlpAudioManager, tenantPO);
                nluTestSoundManager.setSemanticManager(semanticManager);
                semanticManager.enter();
            } else {
                DialogFlow dialogFlow = dialogFlowLoader.loadFromRemoteTextTraining(dialogFlowId);
                TextDialogManager nluTestSoundManager = new TextDialogManager(principal, SystemEnum.BOSS, dialogFlow, textDialogVO);
                nluTestSoundManagerMap.put(principal.getName(), nluTestSoundManager);
                SemanticManager semanticManager = SemanticManagerFactory.getInstance(nluTestSoundManager, dialogFlow, nluTestSoundManager.getProperties(), null);
                nluTestSoundManager.setSemanticManager(semanticManager);
                nluTestSoundManager.enter();
            }
        } else {
            logger.debug("文字对话系统获取的参数对象={}", textDialogVO);

            DialogFlowInfoPO dialogFlowInfoPO = dialogFlowInfoPOMap.getIfPresent(principal.getName());
            Long nlpRobotId = dialogFlowInfoPO.getNlpRobotId();
            if (BotTypeEnum.PROFESSIONAL.equals(dialogFlowInfoPO.getBotType())) {
                NlpTextDialogManager nluTestSoundManager = nluNlpTestSoundManagerMap.getIfPresent(principal.getName());
                nluTestSoundManager.oneRound(textDialogVO);
            } else {
                TextDialogManager textDialogManager = nluTestSoundManagerMap.getIfPresent(principal.getName());
                if (textDialogManager == null) {
                    logger.error("通话异常, 未找到对应的对话管理器");
                    return;
                }
                textDialogVO.setAiProgress(textDialogVO.getAiProgress()==100?0:textDialogVO.getAiProgress());
                textDialogManager.oneRound(textDialogVO);
            }
        }

    }

    @Override
    public void onTextDialogDisconnect(String principalName) {
        if (StringUtils.isBlank(principalName)) {
            return;
        }
        logger.info("文本训练客户挂机");
        TextDialogManager textDialogManager = nluTestSoundManagerMap.getIfPresent(principalName);

        if (textDialogManager != null) {
            textDialogManager.updateCallRecordOnFinish(true);
        }

    }
}
