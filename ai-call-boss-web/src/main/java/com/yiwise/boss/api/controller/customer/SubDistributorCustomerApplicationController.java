package com.yiwise.boss.api.controller.customer;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.AuditStatusEnum;
import com.yiwise.core.service.ope.platform.DistributorCustomerService;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.validate.boss.customer.AddBossDistributorCustomerApplyValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Validated
@RestController
@RequestMapping("apiBoss/subDistributorCustomer")
public class SubDistributorCustomerApplicationController {


    @Resource
    private DistributorCustomerService distributorCustomerService;
    @Resource
    private DataAccessControlService dataAccessControlService;

    // @formatter:off
    /**
     * @api {post} /apiBoss/subDistributorCustomer/subDistributorAddDistributorCustomer    分销商添加客户
     * @apiName subDistributorAddDistributorCustomer
     * @apiGroup customer-subDistributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "companyName" : "阿里巴巴集团",                 # 客户名，必填
     *     "linkman" : "马云",                             # 联系人 ，必填
     *     "phoneNumber" : "***********",                  # 联系方式 ，必填
     *     "aiConcurrencyLevel" : 10,                      # AI并发数,必填
     *     "startTime" : "2018-08-20",                     # 服务开始时间,必填
     *     "endTime" : "2022-08-17",                       # 服务结束时间,必填
     *     "accountType":"FORMAL" ,                        # 账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *     "accountManager":"王思聪",                      # 客户经理,
     *     "accountManagePhone": "***********",            # 客户经理电话
     *     "comment" : "此客户有极大的意向长期合作！"，    # 备注
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *    "code": 200,
     *    "data": "添加分销商客户成功",
     *    "requestId": "LXOAPCID",
     *    "resultMsg": "执行成功",
     *    "errorStackTrace": null
     *}
     */
    // @formatter:on
    @PostMapping("/subDistributorAddDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']添加新客户' + #tenantPO.companyName "
    )
    public ResultObject subDistributorAddDistributorCustomer(@Validated(value = AddBossDistributorCustomerApplyValidate.class) @RequestBody TenantPO tenantPO) {
        tenantPO.setDistributorId(SecurityUtils.getDistributorId());
        Long userId = SecurityUtils.getUserId();
        tenantPO.setCreateUserId(userId);
        tenantPO.setUpdateUserId(userId);
        distributorCustomerService.addSubBossDistributorCustomer(tenantPO);
        return ResultObject.success(null, "添加分销商客户成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/subDistributorCustomer/auditSubDistributorCustomer    一级代理商审核
     * @apiName subDistributorAddDistributorCustomer
     * @apiGroup customer-subDistributorCustomer
     * @apiParam {Boolean} result                                 # 通过/不通过
     * @apiParam {String} auditNote                                # 批语
     * @apiParam {Long} tenantId                                   # 需要审核的客户ID
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *    "code": 200,
     *    "data": "添加分销商客户成功",
     *    "requestId": "LXOAPCID",
     *    "resultMsg": "执行成功",
     *    "errorStackTrace": null
     *}
     */
    // @formatter:on
    @PostMapping("/auditSubDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + ']审批客户申请, 结果为:' + #result + ', 备注信息为：' + #auditNote"
    )
    public ResultObject auditSubDistributorCustomer(@RequestParam(value = "result") Boolean result,
                                                   @RequestParam(value = "auditNote", required = false) String auditNote,
                                                   @RequestParam(value = "tenantId") Long tenantId) {
        String defaultPassword = distributorCustomerService.auditSubDistributorCustomer(result, auditNote, tenantId, SecurityUtils.getUserId());
        return ResultObject.success(defaultPassword);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/subDistributorCustomer/queryForAuditing  一级代理商看的需要审核的分销商客户列表
     * @apiName queryForAuditing
     * @apiGroup customer-subDistributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客户名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {String} accountManager                           # 搜索 客户经理
     * @apiParam {AccountTypeEnum} accountType                     # 账号状态 FORMAL(0,"正式"), ON_TRIAL(1,"试用");
     * @apiParam {AuditStatusEnum} auditStatus                     # 审核状态 PENDING(0, "审核中"), PASS(1, "审核通过"), FAIL(2, "审核不通过")
     *
     * @apiSuccessExample Response 200 Example
     * {
     *                 "tenantId": 197,                             #客户ID
     *                 "companyName": "方方土直销",                 #客户名称
     *                 "linkman": "联系人",                         #联系人
     *                 "phoneNumber": "***********",                #联系电话
     *                 "accountManager": "客户经理",                #客户经理
     *                 "accountManagePhone": "***********",         # 客户经理电话
     *                 "accountType": "FORMAL",                     #账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *                 "comment": "此客户有极大的意向长期合作！",    # 备注
     *                 "dialogFlowId": null,                        #话术ID
     *                 "dialogFlowName": null,                      #话术名称
     *                 "aiConcurrencyLevel": 1,                     #AI并发数量
     *                 "bindingId": null,                           #绑定线路id
     *                 "bindingName": null,                         #绑定线路名称
     *                 "accountFare": 0,                            #账户余额（单位是厘）
     *                 "startTime": "2018-08-20",                   #开始日期
     *                 "endTime": "2019-08-17",                     #结束日期
     *                 "distributorId": 214,                        #代理商ID
     *                 "status": null,                              # 1-启用 2-关闭
     *                 "auditNote": null,                           #审核注释
     *                 "auditStatus": "PENDING"                     #审核状态
     *                 "forbiddenCount" : 1                         # 禁用的总次数
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/queryForAuditing")
    public ResultObject queryForAuditing(@RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                        @RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize,
                                        @RequestParam(value = "accountType", required = false) AccountTypeEnum accountType,
                                        @RequestParam(value = "auditStatus", required = false) AuditStatusEnum auditStatus,
                                        @RequestParam(required = false) String companyName,
                                        @RequestParam(required = false) String linkMan,
                                        @RequestParam(required = false) String phoneNumber,
                                        @RequestParam(required = false) String accountManager
    ) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long createUserId = null;
        if(!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)){
            createUserId = SecurityUtils.getUserId();
        }
        PageResultObject pageResultObject = distributorCustomerService.queryCustomerWaitingForAuditing(pageNum, pageSize, distributorId, accountType, auditStatus, companyName, linkMan, phoneNumber, accountManager, createUserId);

        return ResultObject.success(pageResultObject, "获取成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/subDistributorCustomer/queryForBeingAudited  二级代理商看的自己审核的分销商客户列表
     * @apiName queryForBeingAudited
     * @apiGroup customer-subDistributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客户名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {String} accountManager                           # 搜索 客户经理
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     *
     * @apiSuccessExample Response 200 Example
     * {
     *                 "tenantId": 197,                             #客户ID
     *                 "companyName": "方方土直销",                 #客户名称
     *                 "linkman": "联系人",                         #联系人
     *                 "phoneNumber": "***********",                #联系电话
     *                 "accountManager": "客户经理",                #客户经理
     *                 "accountManagePhone": "***********",            # 客户经理电话
     *                 "accountType": "FORMAL",                     #账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *                 "comment": "此客户有极大的意向长期合作！",    # 备注
     *                 "dialogFlowId": null,                        #话术ID
     *                 "dialogFlowName": null,                      #话术名称
     *                 "aiConcurrencyLevel": 1,                     #AI并发数量
     *                 "bindingId": null,                           #绑定线路id
     *                 "bindingName": null,                         #绑定线路名称
     *                 "accountFare": 0,                            #账户余额（单位是厘）
     *                 "startTime": "2018-08-20",                   #开始日期
     *                 "endTime": "2019-08-17",                     #结束日期
     *                 "distributorId": 214,                        #代理商ID
     *                 "status": null,                              # 1-启用 2-关闭
     *                 "auditNote": null,                           #审核注释
     *                 "auditStatus": "PENDING"                     #审核状态
     *                 "forbiddenCount" : 1                         #禁用的总次数
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/queryForBeingAudited")
    public ResultObject queryForBeingAudited(@RequestParam(value = "pageNum", defaultValue = "1", required = false) Integer pageNum,
                                            @RequestParam(value = "pageSize", defaultValue = "20", required = false) Integer pageSize,
                                            @RequestParam(value = "accountType", required = false) AccountTypeEnum accountType,
                                            @RequestParam(value = "auditStatus", required = false) AuditStatusEnum auditStatus,
                                            @RequestParam(required = false) String companyName,
                                            @RequestParam(required = false) String linkMan,
                                            @RequestParam(required = false) String phoneNumber,
                                            @RequestParam(required = false) String accountManager) {
        Long distributorId = SecurityUtils.getDistributorId();
        PageResultObject pageResultObject = distributorCustomerService.queryForBeingAudited(pageNum, pageSize, distributorId, accountType, auditStatus, companyName, linkMan, phoneNumber, accountManager);
        return ResultObject.success(pageResultObject, "获取成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/subDistributorCustomer/subDistributorUpdateDistributorCustomer    分销商编辑客户
     * @apiName subDistributorUpdateDistributorCustomer
     * @apiGroup customer-subDistributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "companyName" : "阿里巴巴集团",                 # 客户名，必填
     *     "linkman" : "马云",                             # 联系人 ，必填
     *     "phoneNumber" : "***********",                  # 联系方式 ，必填
     *     "aiConcurrencyLevel" : 10,                      # AI并发数,必填
     *     "startTime" : "2018-08-20",                     # 服务开始时间,必填
     *     "endTime" : "2022-08-17",                       # 服务结束时间,必填
     *     "accountType":"FORMAL" ,                        # 账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *     "accountManager":"王思聪",                      # 客户经理
     *     "accountManagePhone": "***********",            # 客户经理电话
     *     "comment" : "此客户有极大的意向长期合作！"      # 备注
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *    "code": 200,
     *    "data": "添加分销商客户成功",
     *    "requestId": "LXOAPCID",
     *    "resultMsg": "执行成功",
     *    "errorStackTrace": null
     *}
     */
    // @formatter:on
    @PostMapping("/subDistributorUpdateDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantPO.tenantId",
            contentExpression = "'用户[' + #currentUser.name + ']编辑了客户' + #tenantPO.companyName + '的信息'"
    )
    public ResultObject subDistributorUpdateDistributorCustomer(@RequestBody TenantPO tenantPO) {
        Long userId = SecurityUtils.getUserId();
        tenantPO.setUpdateUserId(userId);
        distributorCustomerService.updateSubBossDistributorCustomer(tenantPO);
        return ResultObject.success(null, "编辑分销商客户成功");
    }

}
