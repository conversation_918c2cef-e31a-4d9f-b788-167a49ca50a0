package com.yiwise.boss.api.controller.sharewhitelist;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.entity.SharePolicyPO;
import com.yiwise.core.model.enums.CreateTypeEnum;
import com.yiwise.core.model.enums.PolicyStatusEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.WhiteListJoinStatusEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sharewhitelist.BatchAddShareTenantCustomerVO;
import com.yiwise.core.model.vo.sharewhitelist.ShareWhiteListVO;
import com.yiwise.core.service.engine.SharePolicyService;
import com.yiwise.core.service.engine.SharePolicyTenantService;
import com.yiwise.core.service.engine.ShareWhiteListService;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @ClassName ShareWhiteListController
 * <AUTHOR>
 * @Date 2019/3/28
 **/
@RestController
@Validated
@RequestMapping("/apiBoss/shareWhiteList")
public class ShareWhiteListController {

    @Resource
    private SharePolicyService sharePolicyService;
    @Resource
    private SharePolicyTenantService sharePolicyTenantService;
    @Resource
    private ShareWhiteListService shareWhiteListService;
    @Resource
    private DistributorService distributorService;


    // @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/getPolicyList  获取共享组列表
     * @apiName getPolicyList
     * @apiGroup shareWhiteList
     *
     * @apiParam {Integer} pageNum                               # 第几页，非必填，默认1
     * @apiParam {Integer} pageSize                              # 每页数量，非必填，默认20
     * @apiParam {String} sharePolicyName                        # 共享组名，非必填，支持模糊
     * @apiParam {String} tenantName                             # 客户名，非必填。支持模糊
     * @apiParam {CreateTypeEnum} createType                     # 创建类型，非必填:ONE_DISTRIBUTOR(1, "一级代理商"),WO_DISTRIBUTOR(2, "二级分销商");与ope进行区别这里只有两种枚举
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 2,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "createUserId": 0,
     *                 "updateUserId": 0,
     *                 "createTime": "2019-03-27 10:16:54",      #创建时间
     *                 "updateTime": "2019-03-27 10:17:08",
     *                 "sharePolicyId": 2,                       # 共享组id
     *                 "name": "共享组2",                        # 共享组名
     *                 "comment": "拒绝骚然2！",                 # 备注
     *                 "threshold": 10,                          # 阈值，
     *                 "sharingTime": 20,                        # 共享时长
     *                 "createType": "OPE",                      # 创建类型 ONE_DISTRIBUTOR(1, "一级代理商"),WO_DISTRIBUTOR(2, "二级分销商");
     *                 "status": "ENABLED",                      # 启用状态 DISENABLED(0, "禁用"),ENABLED(1, "启用");
     *                 "enabledStatus": "ENABLE",
     *                 "tenantCount": 0,                         # 共享组客户数量
     *                 "whiteListCount": 1                       # 共享黑名单数量
     *             }
     *         ]
     *     },
     *     "requestId": "FZEIBVSM",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getPolicyList")
    public ResultObject getPolicyList(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                     @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                     @RequestParam(required = false) String sharePolicyName,
                                     @RequestParam(required = false) Long tenantId,
                                     @RequestParam(required = false) CreateTypeEnum createType) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long createUserId = SecurityUtils.getUserId();
        PageResultObject page = sharePolicyService.getPolicyList(pageNum, pageSize, sharePolicyName, tenantId, createType, distributorId, createUserId);
        return ResultObject.success(page);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/getSharePolicyTenant  获取共享组成员
     * @apiName getSharePolicyTenant
     * @apiGroup shareWhiteList
     *
     * @apiParam {Integer} pageNum                               # 第几页，默认1
     * @apiParam {Integer} pageSize                              # 每页数量,默认20
     * @apiParam {Long} policyId                                 # 共享id，必填
     * @apiParam {String} tenantName                             # 客户名，支持模糊
     * @apiParam {String} customerType                           # 客户类型，必填。 ONE_DISTRIBUTOR_CUSTOMER(1, "一级代理商直销客户"),TWO_DISTRIBUTOR_CUSTOMER(2, "二级代理商直销客户");一级代理商显示以上两个枚举，默认必传ONE_DISTRIBUTOR_CUSTOMER ; 二级代理商只显示TWO_DISTRIBUTOR_CUSTOMER，默认必传
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 1,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 1,                     #客户id
     *                 "tenantName": "邹静在找的大boss",  #客户名
     *                 "customerType": "ONE_DISTRIBUTOR_CUSTOMER", #客户类型
     *                 "joinTime": "2019-03-27",          #加入时间
     *                 "distributorId": 0
     *             }
     *         ]
     *     },
     *     "requestId": "XEQNHTJM",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getSharePolicyTenant")
    public ResultObject getSharePolicyTenant(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                            @RequestParam Long policyId,
                                            @RequestParam(required = false) String tenantName,
                                            @RequestParam(required = false) String customerType) {
        PageResultObject page = sharePolicyTenantService.getSharePolicyTenant(pageNum, pageSize, policyId, tenantName, customerType);
        return ResultObject.success(page);
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/deleteShareTenantCustomer  删除共享组成员与退出共享组同用此接口
     * @apiName deleteShareTenantCustomer
     * @apiGroup shareWhiteList
     *
     * @apiParam {Long} tenantId                               # 客户id,必填
     * @apiParam {Long} policyId                               # 共享组的id,必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "EBLPGQIN",
     *     "resultMsg": "删除成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/deleteShareTenantCustomer")
    public ResultObject deleteShareTenantCustomer(@RequestParam Long policyId,
                                                 @RequestParam Long tenantId) {
        sharePolicyTenantService.deleteShareTenantCustomer(policyId, tenantId);
        return ResultObject.success(null, "删除/退出共享组成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/addShareTenantCustomer  加入共享组
     * @apiName addShareTenantCustomer
     * @apiGroup shareWhiteList
     *
     * @apiParam {Long} tenantId                               # 客户id,必填
     * @apiParam {Long} policyId                               # 共享组id，必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "SXLEOXJY",
     *     "resultMsg": "加入共享组成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addShareTenantCustomer")
    public ResultObject addShareTenantCustomer(@RequestParam Long tenantId,
                                              @RequestParam Long policyId) {
        sharePolicyTenantService.addShareTenantCustomer(tenantId, policyId);
        return ResultObject.success(null, "加入共享组成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/batchAddShareTenantCustomer  批量加入共享组
     * @apiName batchAddShareTenantCustomer
     * @apiGroup shareWhiteList
     *
     * @apiParam {List} tenantIds                               # 客户id,必填
     * @apiParam {Long} policyId                               # 共享组id，必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "SXLEOXJY",
     *     "resultMsg": "加入共享组成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/batchAddShareTenantCustomer")
    public ResultObject batchAddShareTenantCustomer(@RequestBody BatchAddShareTenantCustomerVO batchAddShareTenantCustomerVO) {
        Long userId=SecurityUtils.getUserId();
        sharePolicyTenantService.batchAddShareTenantCustomer(batchAddShareTenantCustomerVO.getTenantIds(), batchAddShareTenantCustomerVO.getPolicyId(),userId);
        return ResultObject.success(null, "批量加入共享组成功");
    }


    // @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/getShareWhiteList  查看共享黑名单
     * @apiName getShareWhiteList
     * @apiGroup shareWhiteList
     *
     * @apiParam {Integer} pageNum                               # 第几页，默认1
     * @apiParam {Integer} pageSize                              # 每页数量,默认20
     * @apiParam {Long} policyId                                 # 共享id，必填
     * @apiParam {String} phoneNumber                            # 手机号，精确匹配
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 1,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "createUserId": null,
     *                 "updateUserId": null,
     *                 "createTime": null,
     *                 "updateTime": null,
     *                 "shareWhiteListId": 1,               # 黑名单id
     *                 "degree": null,
     *                 "phoneNumber": "13273888079",        # 手机号
     *                 "policyId": null,
     *                 "deadline": "2019-03-28 10:13:00",   # 过期时间
     *                 "joinTime": "2019-03-27 16:37:16",   # 加入时间
     *                 "enabledStatus": null
     *             }
     *         ]
     *     },
     *     "requestId": "QTXLWBTQ",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/getShareWhiteList")
    public ResultObject getShareWhiteList(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                         @RequestParam Long policyId,
                                         @RequestParam(required = false) String phoneNumber) {
        PageResultObject page = shareWhiteListService.getShareWhiteList(pageNum, pageSize, policyId, phoneNumber);
        return ResultObject.success(page);
    }
// @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/exportShareWhiteList  导出共享黑名单
     * @apiName exportShareWhiteList
     * @apiGroup shareWhiteList
     *
     * @apiParam {Long} policyId                                 # 共享id，必填
     * @apiParam {String} phoneNumber                            # 手机号，精确匹配
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 1,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "createUserId": null,
     *                 "updateUserId": null,
     *                 "createTime": null,
     *                 "updateTime": null,
     *                 "shareWhiteListId": 1,               # 黑名单id
     *                 "degree": null,
     *                 "phoneNumber": "13273888079",        # 手机号
     *                 "policyId": null,
     *                 "deadline": "2019-03-28 10:13:00",   # 过期时间
     *                 "joinTime": "2019-03-27 16:37:16",   # 加入时间
     *                 "enabledStatus": null
     *             }
     *         ]
     *     },
     *     "requestId": "QTXLWBTQ",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/exportShareWhiteList")
    public ResultObject exportShareWhiteList(
            @RequestParam Long policyId,
            @RequestParam(required = false) String phoneNumber) {

        JobStartResultVO result =  shareWhiteListService.exportShareWhiteList(policyId, phoneNumber, SystemEnum.BOSS,SecurityUtils.getDistributorId(),SecurityUtils.getTenantId(),SecurityUtils.getUserId());
        return ResultObject.success(result);
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/deleteShareWhiteList  删除共享黑名单
     * @apiName deleteShareWhiteList
     * @apiGroup shareWhiteList
     *
     * @apiParam {Long} shareWhiteListId                               # 黑名单id,必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "IDHJEWVY",
     *     "resultMsg": "删除成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/deleteShareWhiteList")
    public ResultObject deleteShareWhiteList(@RequestParam Long shareWhiteListId) {
        shareWhiteListService.deleteShareWhiteList(shareWhiteListId);
        return ResultObject.success(null, "删除成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/addShareWhiteList  批量添加共享黑名单
     * @apiName addShareWhiteList
     * @apiGroup shareWhiteList
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"policyId" : 1,       # 共享组id,必填
     * 	"phoneList":[
     * 		"13273888077",
     *     	"13273888076"	  # 手机号，不能为空
     * 		]
     * }
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2,    有效添加数
     *     "requestId": "ZHBJOLZS",
     *     "resultMsg": "批量添加黑名单成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addShareWhiteList")
    public ResultObject addShareWhiteList(@RequestBody ShareWhiteListVO listVO) {
        Integer effectiveCount = shareWhiteListService.addShareWhiteList(listVO);
        return ResultObject.success(effectiveCount, "批量添加黑名单成功");
    }


    // @formatter:off
    /**
     * @api {get} /apiBoss/shareWhiteList/selectPolicyList  客户管理选择共享组列表
     * @apiName selectPolicyList
     * @apiGroup shareWhiteList
     *
     * @apiParam {Integer} pageNum                               # 第几页，默认1
     * @apiParam {Integer} pageSize                              # 每页数量,默认20
     * @apiParam {String} sharePolicyName                        # 共享组名
     * @apiParam {CreateTypeEnum} createType                     # 创建者类型 ONE_DISTRIBUTOR(1, "一级代理商"),TWO_DISTRIBUTOR(2, "二级分销商");
     * @apiParam {WhiteListJoinStatusEnum} joinStatus            # 加入状态，必填，前端需要默认传 已加入      JOIN(0, "已加入"),NOT_JOIN(1, "未加入");
     * @apiParam {Long} tenantId                                 # 客户id,  必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 1,
     *         "pages": 1,0
     *         "content": [
     *             {
     *                 "createUserId": null,
     *                 "updateUserId": null,
     *                 "createTime": null,               # 创建时间
     *                 "updateTime": null,
     *                 "sharePolicyId": null,            # 共享组di
     *                 "name": "共享组2",                # 共享组名
     *                 "comment": "拒绝骚然2！",         # 备注
     *                 "threshold": 10,                  # 阈值
     *                 "sharingTime": null,              # 共享时长
     *                 "createType": null,               # 创建者类型  ONE_DISTRIBUTOR(1, "一级代理商"),TWO_DISTRIBUTOR(2, "二级分销商");，与OPE有趣被，这里只传两种枚举
     *                 "status": "ENABLED",              # 启用禁用状态
     *                 "enabledStatus": null,
     *                 "joinStatus": "NOT_JOIN"          # 加入状态，，默认为已加入      JOIN(0, "已加入"),NOT_JOIN(1, "未加入");
     *             }
     *         ]
     *     },
     *     "requestId": "KOSWLRZD",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/selectPolicyList")
    public ResultObject selectPolicyList(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                        @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                        @RequestParam(required = false) String sharePolicyName,
                                        @RequestParam(required = false) CreateTypeEnum createType,
                                        @RequestParam(required = false) WhiteListJoinStatusEnum joinStatus,
                                        @RequestParam Long tenantId) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long createUserId = SecurityUtils.getUserId();
        PageResultObject page = sharePolicyService.selectPolicyList(pageNum, pageSize, sharePolicyName, createType, joinStatus, tenantId, distributorId, createUserId);
        return ResultObject.success(page);
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/addSharePolicy  新建共享组
     * @apiName addSharePolicy
     * @apiGroup shareWhiteList
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"name":"共享组6",             # 共享组名，必填
     * 	"comment":"我是备注666",      # 备注
     * 	"threshold":9,                # 阈值
     * 	"sharingTime":999             # 共享天数
     * }
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "ZHQOYLUN",
     *     "resultMsg": "新建共享组成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addSharePolicy")
    public ResultObject addSharePolicy(@RequestBody @Validated SharePolicyPO sharePolicyPO) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long createUserId = SecurityUtils.getUserId();
        sharePolicyPO.setDistributorId(distributorId);
        if (Objects.isNull(sharePolicyPO.getThreshold())) {
            sharePolicyPO.setThreshold(2);
        } else if (sharePolicyPO.getThreshold() < 1 || sharePolicyPO.getThreshold() > 9) {
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR, "阈值取值范围为1-9");
        }
        if (Objects.isNull(sharePolicyPO.getSharingTime())) {
            sharePolicyPO.setSharingTime(30);
        } else if (sharePolicyPO.getSharingTime() < 1 || sharePolicyPO.getSharingTime() > 999) {
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR, "共享天数取值范围为1-999");
        }
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        Long parentId = distributorPO.getParentId();
        if (parentId == 0) {
            sharePolicyService.addSharePolicy(sharePolicyPO, CreateTypeEnum.ONE_DISTRIBUTOR, distributorId, createUserId);
        } else {
            sharePolicyService.addSharePolicy(sharePolicyPO, CreateTypeEnum.TWO_DISTRIBUTOR, distributorId, createUserId);
        }
        return ResultObject.success(null, "新建共享组成功！");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/updateSharePolicy  编辑共享组
     * @apiName updateSharePolicy
     * @apiGroup shareWhiteList
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"sharePolicyId":6,       # 共享组id,必填
     * 	"name":"共享组6",        # 共享组名
     * 	"comment":"我是备注666", # 备注
     * 	"threshold":8,           # 阈值
     * 	"sharingTime":88         # 共享时长
     * }
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "BVJUVTPB",
     *     "resultMsg": "编辑共享组成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    // 更新共享天数：根据老的共享天数计算出的退出时间需要做增加，减少的改动；新加入黑名单的数据按照最新的共享天计算推出时间
    // 更新阈值，以最新的阈值为判断标准计算是否加入黑名单,查询出所有的客户下的手机号加入次数与当前阈值做判断，判断是否需要加入黑名单
    @PostMapping("updateSharePolicy")
    public ResultObject updateSharePolicy(@RequestBody @Validated SharePolicyPO sharePolicyPO) {
        if (Objects.isNull(sharePolicyPO.getSharePolicyId())) {
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR, "共享组id不能为空");
        }
        if (Objects.isNull(sharePolicyPO.getThreshold())) {
            sharePolicyPO.setThreshold(2);
        } else if (sharePolicyPO.getThreshold() < 1 || sharePolicyPO.getThreshold() > 9) {
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR, "阈值取值范围为1-9");
        }
        if (Objects.isNull(sharePolicyPO.getSharingTime())) {
            sharePolicyPO.setSharingTime(30);
        } else if (sharePolicyPO.getSharingTime() < 1 || sharePolicyPO.getSharingTime() > 999) {
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR, "共享天数取值范围为1-999");
        }
        sharePolicyService.updateSharePolicy(sharePolicyPO);
        return ResultObject.success(null, "编辑共享组成功！");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/startOrBlockSharePolicy  启用、停用共享组状态
     * @apiName startOrBlockSharePolicy
     * @apiGroup shareWhiteList
     *
     * @apiParam {PolicyStatusEnum} policyStatus                    # 要更改的状态，必填 DISENABLED(0, "禁用"),ENABLED(1, "启用");
     * @apiParam {Long} sharePolicyId                               # 共享组的id,必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "LZYFPTXS",
     *     "resultMsg": "启用/禁用成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("startOrBlockSharePolicy")
    public ResultObject startOrBlockSharePolicy(@RequestParam PolicyStatusEnum policyStatus,
                                               @RequestParam Long sharePolicyId) {
        sharePolicyService.startOrBlock(policyStatus, sharePolicyId);
        return ResultObject.success(null, "启用/禁用成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/shareWhiteList/deleteSharePolicy  删除共享组
     * @apiName deleteSharePolicy
     * @apiGroup shareWhiteList
     *
     * @apiParam {Long} sharePolicyId                               # 共享组的id,必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "KCVGZEAI",
     *     "resultMsg": "删除成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("deleteSharePolicy")
    public ResultObject deleteSharePolicy(@RequestParam Long sharePolicyId) {
        sharePolicyService.deleteSharePolicy(sharePolicyId);
        return ResultObject.success(null, "删除成功！");
    }

}
