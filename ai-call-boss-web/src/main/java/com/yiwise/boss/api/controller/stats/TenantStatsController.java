package com.yiwise.boss.api.controller.stats;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.model.bo.boss.TenantStatsBO;
import com.yiwise.core.model.dto.stats.TenantStatsDTO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.stats.TenantStatsVO;
import com.yiwise.core.service.ope.stats.TenantStatsService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
* 客户统计
* <AUTHOR> yangdehong
* @date : 2019/1/28 11:50
*/
@RestController
@RequestMapping("/apiBoss/tenantStats")
public class TenantStatsController {

    @Resource
    private TenantStatsService tenantStatsService;

    /**
     * @api {get} /apiBoss/tenantStats/getTenantStateStatus  客户状态统计
     * @apiName getTenantStateStatus
     * @apiGroup tenantStats
     *
     * @apiParam {LocalDate} startDate     开始日期
     * @apiParam {LocalDate} endDate       结束日期
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "createTime": "2019-01-29 18:49:34",
     *             "updateTime": "2019-01-29 18:49:34",
     *             "distributorId": 0,                   # 代理商id
     *             "statsDate": "2019-01-28",
     *             "totalTenantCount": 91,               # 全部客户
     *             "newTenantCount": 0,                  # 新签客户
     *             "activeTenantCount": 0,               # 活跃客户
     *             "willAdjectiveTenantCount": 1,        # 沉寂风险客户
     *             "adjectiveTenantCount": 7,            # 沉寂客户
     *             "willOutDateTenantCount": 12,         # 即将过期客户
     *             "outDateTenantCount": 71              # 过期客户
     *         }
     *     ],
     *     "requestId": "UNXEBDOZ",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    @GetMapping("/getTenantStateStatus")
    public ResultObject getTenantStateStatus(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Long distributorId = SecurityUtils.getDistributorId();
        List<TenantStatsBO> tenantStatsList = tenantStatsService.getTenantStats(distributorId, startDate, endDate);
        return ResultObject.success(tenantStatsList);

    }

    /**
     * @api {get} /apiBoss/tenantStats/getTenantDetail  客户状态详细列表
     * @apiName getTenantDetail
     * @apiGroup tenantStats
     *
     * @apiParam {Long} tenantName     客户名称
     * @apiParam {Long} accountManager 客户经理名称
     * @apiParam {Long} tenantType     客户类型 LESS_T3(0, "微型客户"),F4_T10(1, "小型客户"),F11_T30(2, "中型客户"),F30_MORE(3, "大型客户")
     * @apiParam {Long} joinTime       客户加入时长 LESS_T1MONTH(0, "在网<1个月"),F1MONTH_T3MONTH(1, "在网1-3个月"),F3MONTH_MORE(2, "在网>3个月")
     * @apiParam {Long} accountType    账户状态 FORMAL(0,"正式"),ON_TRIAL(1,"试用")
     * @apiParam {Long} tenantStatus   客户状态 NOT_ENABLED(0, "未启用"),ENABLED(1, "启用"),CLOSED(2, "关闭")
     * @apiParam {Long} tenantLostStatus   "OUT_DATE_TENANT",       # NEW_TENANT(0, "新签客户"),ACTIVE_TENANT(1, "活跃客户"),WILL_ADJECTIVE_TENANT(2, "沉寂风险客户"),ADJECTIVE_TENANT(3, "沉寂客户"),WILL_OUT_DATE_TENANT(4, "即将过期客户"),OUT_DATE_TENANT(5, "过期客户")
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 102,
     *         "pages": 6,
     *         "content": [
     *             {
     *                 "createUserId": 0,
     *                 "updateUserId": 548,
     *                 "createTime": "2018-09-05 17:03:09",
     *                 "updateTime": "2019-01-29 19:48:02",
     *                 "tenantId": 1,
     *                 "companyName": "邹静在找的大boss",
     *                 "linkman": "邹静在找的大BOSS",
     *                 "phoneNumber": "***********",
     *                 "accountManager": "邹大静",
     *                 "accountType": "FORMAL",
     *                 "comment": "邹静专属的直销客户 见者绕行",
     *                 "dialogFlowId": null,
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10,
     *                 "bindingId": null,
     *                 "bindingName": null,
     *                 "accountFare": 1622451,
     *                 "startTime": "2018-08-02",
     *                 "endTime": "2018-08-17",
     *                 "distributorId": 0,
     *                 "status": null,
     *                 "auditNote": null,
     *                 "auditStatus": "PASS",
     *                 "accountManagePhone": null,
     *                 "enableConcurrency": null,
     *                 "enabledStatus": null,
     *                 "enableCsSeat": true,
     *                 "csSeatCount": 6,
     *                 "forbiddenTime": null,
     *                 "deploymentUserId": 0,
     *                 "enableAiAssistant": true,
     *                 "enableCsTransfer": true,
     *                 "enableCallIn": null,
     *                 "forbiddenCount": 2,
     *                 "tenantStatsStatus": "OUT_DATE_TENANT",       # NEW_TENANT(0, "新签客户"),ACTIVE_TENANT(1, "活跃客户"),WILL_ADJECTIVE_TENANT(2, "沉寂风险客户"),ADJECTIVE_TENANT(3, "沉寂客户"),WILL_OUT_DATE_TENANT(4, "即将过期客户"),OUT_DATE_TENANT(5, "过期客户")
     *                 "robotCount": 25,
     *                 "tenantStateStats": {
     *                     "tenantStateStatsDetail": {
     *                         "tenantId": 1,
     *                         "year": 2019,
     *                         "month": 1,
     *                         "day": 28,
     *                         "callTaskCompleted": 106,
     *                         "taskTotalCompleted": 81,
     *                         "answeredCall": 25,
     *                         "intentLevel0": 1,
     *                         "intentLevel1": 43,
     *                         "intentLevelRate": 176,
     *                         "answeredRate": 30.86
     *                     },
     *                     "yesterdayTenantStateStatsDetail": {
     *                         "tenantId": null,
     *                         "year": null,
     *                         "month": null,
     *                         "day": null,
     *                         "callTaskCompleted": null,
     *                         "taskTotalCompleted": null,
     *                         "answeredCall": null,
     *                         "intentLevel0": null,
     *                         "intentLevel1": null,
     *                         "intentLevelRate": null,
     *                         "answeredRate": null
     *                     },
     *                     "lastWeekTenantStateStatsDetail": {
     *                         "tenantId": null,
     *                         "year": null,
     *                         "month": null,
     *                         "day": null,
     *                         "callTaskCompleted": null,
     *                         "taskTotalCompleted": null,
     *                         "answeredCall": null,
     *                         "intentLevel0": null,
     *                         "intentLevel1": null,
     *                         "intentLevelRate": null,
     *                         "answeredRate": null
     *                     }
     *                 }
     *             }
     *         ]
     *     },
     *     "requestId": "EHJZARGV",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    @PostMapping("/getTenantDetail")
    public ResultObject getTenantDetail(@RequestBody TenantStatsVO tenantStatsVO) {
        Long distributorId = SecurityUtils.getDistributorId();
        tenantStatsVO.setDistributorId(distributorId);
        PageResultObject<TenantStatsDTO> tenantStatsList = tenantStatsService.getTenantDetail(tenantStatsVO);
        return ResultObject.success(tenantStatsList);
    }

    /**
     * @api {get} /apiBoss/tenantStats/exportTenantStats  导出客户分析趋势图
     * @apiName exportTenantStats
     * @apiGroup tenantStats
     *
     * @apiParam {LocalDate} startDate     开始日期
     * @apiParam {LocalDate} endDate       结束日期
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "springBatchJobLogId": 6601,
     *         "jobInstanceId": 7118
     *     },
     *     "requestId": "EHJZARGV",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    @GetMapping("/exportTenantStats")
    public ResultObject exportTenantStats(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Long distributorId = SecurityUtils.getDistributorId();
        Long userId = SecurityUtils.getUserId();
        JobStartResultVO result = tenantStatsService.exportTenantStats(userId, distributorId, startDate, endDate, SystemEnum.BOSS);
        return ResultObject.success(result);
    }

    /**
     * @api {post} /apiBoss/tenantStats/exportTenantDetail  导出客户状态详细列表
     * @apiName exportTenantDetail
     * @apiGroup tenantStats
     *
     * @apiParam {Long} tenantName     客户名称
     * @apiParam {Long} accountManager 客户经理名称
     * @apiParam {Long} tenantType     客户类型 LESS_T3(0, "微型客户"),F4_T10(1, "小型客户"),F11_T30(2, "中型客户"),F30_MORE(3, "大型客户")
     * @apiParam {Long} joinTime       客户加入时长 LESS_T1MONTH(0, "在网<1个月"),F1MONTH_T3MONTH(1, "在网1-3个月"),F3MONTH_MORE(2, "在网>3个月")
     * @apiParam {Long} accountType    账户状态 FORMAL(0,"正式"),ON_TRIAL(1,"试用")
     * @apiParam {Long} tenantStatus   客户状态 NOT_ENABLED(0, "未启用"),ENABLED(1, "启用"),CLOSED(2, "关闭")
     * @apiParam {Long} tenantLostStatus   "OUT_DATE_TENANT",       # NEW_TENANT(0, "新签客户"),ACTIVE_TENANT(1, "活跃客户"),WILL_ADJECTIVE_TENANT(2, "沉寂风险客户"),ADJECTIVE_TENANT(3, "沉寂客户"),WILL_OUT_DATE_TENANT(4, "即将过期客户"),OUT_DATE_TENANT(5, "过期客户")
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "springBatchJobLogId": 6601,
     *         "jobInstanceId": 7118
     *     },
     *     "requestId": "EHJZARGV",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    @PostMapping("/exportTenantDetail")
    public ResultObject exportTenantDetail(@RequestBody TenantStatsVO tenantStatsVO) {
        Long distributorId = SecurityUtils.getDistributorId();
        tenantStatsVO.setDistributorId(distributorId);
        Long userId = SecurityUtils.getUserId();
        JobStartResultVO result = tenantStatsService.exportTenantState(tenantStatsVO, userId, SystemEnum.BOSS);
        return ResultObject.success(result);
    }
}
