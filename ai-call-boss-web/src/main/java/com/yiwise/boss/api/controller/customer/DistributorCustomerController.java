package com.yiwise.boss.api.controller.customer;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.RobotStasPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.dto.TenantAiccPartDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.boss.DirectCustomerBatchQueryVO;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerFunctionInfo;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerOpenFunctionQueryVO;
import com.yiwise.core.model.vo.ope.CustomerInsertVO;
import com.yiwise.core.model.vo.ope.RobotExpirationTimeVO;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerInsertValidate;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerOpenFunctionValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;

/**
 * 分销商客户
 *
 * <AUTHOR> yangdehong
 * @date : 2018/10/26 10:52 AM
 */
@Validated
@RestController
@RequestMapping("apiBoss/distributorCustomer")
public class DistributorCustomerController {

    @Resource
    private DistributorCustomerService distributorCustomerService;
    @Resource
    private TenantService tenantService;
    @Resource
    private DirectCustomerService directCustomerService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private DistributorService distributorService;

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorCustomer/listAllDistributorCustomer  分销商客户列表
     * @apiName list
     * @apiGroup distributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客戶名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {String} accountManager                           # 搜索 客户经理
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     * @apiParam {TenantStatusEnum} status                         # 是否启用 ENABLED(1, "启用"),CLOSED(2, "关闭");默认启用
     * @apiParam {Long} subDistributorId                           # 次级代理商的id
     * @apiParam {AccountTypeEnum} accountType                     # 账号类型FORMAL(0,"正式"),ON_TRIAL(1,"试用");
     * @apiParam {String}  subAccountPhone                         # 子账户的手机号
     * @apiParam {String}  startDate                               # 开始服务时间
     * @apiParam {String}  endDate                                 # 结束服务时间
     * @apiParam {String} enableConcurrency                       # 过滤是否启用一线多并发， true： 启用， false：禁用
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                           # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountManagePhone": "***********",                    # 客户经理电话
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "robotList": [                                          # 机器坐席的集合
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的ｉｄ
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ]
     *             }
     *         ]
     *     },
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/listAllDistributorCustomer")
    public ResultObject list(@RequestBody DirectCustomerQueryVO distributorCustomerQueryVO) {

        if (!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)) {
            distributorCustomerQueryVO.setCreateUserId(SecurityUtils.getUserId());
        }

        //获取该代理商下的所有次级代理商
        List<Long> distributorIds = distributorService.selectByParentId(SecurityUtils.getDistributorId());
        if (CollectionUtils.isEmpty(distributorIds)) {
            return ResultObject.success(Collections.emptyList());
        }
        distributorCustomerQueryVO.setDistributorIds(distributorIds);
        distributorCustomerQueryVO.setSystemEnum(SystemEnum.BOSS);
        PageResultObject page = directCustomerService.listAllCustomer(distributorCustomerQueryVO, true);

        return ResultObject.success(page);
    }

    @PostMapping("/batchUpdateStatus")
    public ResultObject updateStatus(@RequestBody DirectCustomerBatchQueryVO directCustomerBatchQueryVO) {
        Long currentUserId = SecurityUtils.getUserId();
        Long distributorId = SecurityUtils.getDistributorId();
        directCustomerBatchQueryVO.setDistributorId(distributorId);

        //仅可以查看自己的权限
        if(!dataAccessControlService.hasBossAllDataAuth(SecurityUtils.getUserInfo(), AuthResourceUriEnum.boss_customer_company)) {
            directCustomerBatchQueryVO.setCreateUserId(currentUserId);
        }

        //获取该代理商下的所有次级代理商
        List<Long> distributorIds = distributorService.selectByParentId(distributorId);
        if (CollectionUtils.isEmpty(distributorIds)) {
            return ResultObject.success(Collections.emptyList());
        }
        directCustomerBatchQueryVO.setDistributorIds(distributorIds);

        directCustomerService.batchUpdateStatus(directCustomerBatchQueryVO.getTenantIds(), directCustomerBatchQueryVO.getOpStatus(), currentUserId,directCustomerBatchQueryVO,directCustomerBatchQueryVO.getSelectAll());
        return ResultObject.success(null, "操作成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorCustomer/allTenantIdAndNamePair 获取所有分销商客户的id和名字键值对列表
     * @apiName allTenantIdAndNamePair
     * @apiGroup distributorCustomer
     *
     * @apiParam {String} searchName  搜索客户名称
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 416,                     # 客户id
     *             "name": "13100002224"                # 客户名称
     *         }
     *     ],
     *     "requestId": "MXQCYHZP",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     *
     *
     */
    // @formatter:on
    @GetMapping("/allTenantIdAndNamePair")
    public ResultObject allTenantIdAndNamePair(@RequestParam(required = false, name = "searchName") String searchName) {
        Long distributorId = SecurityUtils.getDistributorId();
        return ResultObject.success(distributorCustomerService.getTenantIdAndNamePairList(distributorId, searchName));
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/addDistributorCustomer    添加分销商客户
     * @apiName addBossDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantName" : "阿里巴巴集团",                  # 客户名，必填
     *     "linkman" : "马云",                             # 联系人 ，必填
     *     "phoneNumber" : "***********",                  # 联系方式 ，必填
     *     "password":"123333",                            # 密码
     *     "aiConcurrencyLevel" : 10,                      # AI并发数,必填
     *     "startTime" : "2018-08-20",                     # 服务开始时间,必填
     *     "endTime" : "2022-08-17",                       # 服务结束时间,必填
     *     "accountType":"FORMAL" ,                        # 账号类型，必填  FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *     "distributorId":8,                              # 经销商的id,必填
     *     "accountManager":"王思聪",                      # 客户经理
     *     "accountManagePhone" :  "***********",          # 客户经理电话
     *     "comment" : "此客户有极大的意向长期合作！"   ,  # 备注
     *     "status" : "ENABLED",                           # 是否启用账户， ENABLED(1, "启用"),CLOSED(2, "关闭");
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "添加经销商客户成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + ']添加了客户 ' + #customerInsertVO.companyName"
    )
    public ResultObject addDistributorCustomer(@Validated(value = DistributorCustomerInsertValidate.class) @RequestBody CustomerInsertVO customerInsertVO) {
        //新建经销商客户的distributor_id为前端传
        Long distributorId = SecurityUtils.getDistributorId();
        Long userId = SecurityUtils.getUserId();
        customerInsertVO.setCreateUserId(userId);
        customerInsertVO.setUpdateUserId(userId);
        String defaultPassword = distributorCustomerService.addBossDistributorCustomer(customerInsertVO, distributorId);
        return ResultObject.success(defaultPassword);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/updateDistributorCustomer   更新分销商客户信息
     * @apiName updateDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantId":9,                                        # 客户id,必填
     *     "tenantName" : "杭州一知智能科技有限公司",           # 客户名
     *     "linkman" : "达康书记",                              # 联系人
     *     "phoneNumber" : "***********",                       # 联系方式
     *     "distributorId": 2 ,                                 # 经销商的id
     *     "accountType","FORMAL",                              # 账号类型 FORMAL(0,"正式"),ON_TRIAL(1,"试用"),
     *     "accountManagePhone" : "***********",                # 客户经理电话
     *     "comment":"备注"                                     # 备注
     * }
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "更新代理商客户成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantPO.tenantId",
            contentExpression = "'用户[' + #currentUser.name + ']更新了客户' + #tenantPO.companyName + '的信息'"
    )
    public ResultObject updateDistributorCustomer(@RequestBody TenantPO tenantPO) {
        Long userId = SecurityUtils.getUserId();
        tenantPO.setUpdateUserId(userId);
        distributorCustomerService.updateDistributorCustomer(tenantPO);
        return ResultObject.success(null, "更新分销商客户成功！");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/distributorCustomerDelay    经销商客户延期
     * @apiName distributorCustomerDelay
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newEndTime                        # 要延期的时间,必填
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {string} tenantAiccPartType                               # 类型 crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本") FUN_MULTI_CONCURRENCY(8, "一线多并发"),
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/distributorCustomerDelay")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + ']对客户' + #tenantId + '进行了延期操作'"
    )
    public ResultObject distributorCustomerDelay(@RequestParam Long id, @RequestParam LocalDate newEndTime, @RequestParam Long tenantId,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        distributorCustomerService.opeAndBossDistributorCustomerDelay(id, newEndTime, tenantId, SecurityUtils.getUserId(),tenantAiccPartType);
        return ResultObject.success(null, "延期成功!");
    }


    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/adjustingServiceCycle    分销商客户调整服务周期
     * @apiName adjustingServiceCycle
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 扣除坐席并发的数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/adjustingServiceCycle")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + ']调整了客户' + #tenantId + '的服务周期'"
    )
    public ResultObject adjustingServiceCycle(@RequestParam Long id,
                                             @RequestParam(required = false) LocalDate newStartTime,
                                             @RequestParam(required = false) LocalDate newEndTime,
                                             @RequestParam Long tenantId,
                                             @RequestParam(required = true) Long reduceAiAmount,
                                             @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartEnum) {
        //boss的经销商客户应该给自己的经销商重新计算库存，所以与ope的一致
        if(tenantAiccPartEnum == null){
            tenantAiccPartEnum = TenantAiccPartEnum.crm_out_call_platform;
        }
        directCustomerService.adjustingServiceCycle(id, newStartTime, newEndTime, tenantId, SecurityUtils.getUserId(), reduceAiAmount,tenantAiccPartEnum);
        return ResultObject.success(null, "调整服务周期成功!");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/dilatation 代理商客户扩容
     * @apiName dilatation
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {string} tenantAiccPartType                           # 类型，必填crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本")
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/dilatation")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 对经销商客户[tenantId='+ #tenantId +']进行了扩容' "
    )
    public ResultObject dilatation(@RequestParam Long tenantId,
                                  @RequestParam Integer count,
                                  @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                  @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime
                                ,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        directCustomerService.dilatation(tenantId, count, startTime, endTime, SecurityUtils.getUserId(),tenantAiccPartType);
        return ResultObject.success(null, "扩容成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateAgentAndAiAssistantCost 计算 开通人工坐席/AI 助理的费用 改动之前接口比较费事所以重新写个
     * @apiName calculateAgentAndAiAssistantCost
     * @apiGroup distributorCustomer
     *
     * @apiParam {Double} count                                     # 新增数量 必填
     * @apiParam {LocalDate} startTime                      # 服务开始时间必填
     * @apiParam {LocalDate} endTime                        # 服务结束时间必填
     * @apiParam {Long} distributorId                               # 代理商id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "开通功能消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAgentAndAiAssistantCost")
    public ResultObject calculateAiCost(
            @NotNull(message = "新增数量不能为空")@RequestParam Double count,
            @NotNull(message = "服务开始时间不能为空")@RequestParam LocalDate startTime,
            @NotNull(message = "服务结束时间不能为空")@RequestParam LocalDate endTime,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        Double result=distributorCustomerService.calculateAgentAndAiAssistantCost(count,distributorId,startTime, endTime,rechargePrestoreStreamFunEnum);
        return ResultObject.success(result, "开通功能+"+rechargePrestoreStreamFunEnum.getDesc()+"消耗费用计算成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateDelayCost 代理商客户延期费用 结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateDelayCost
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Double} ObjectCount                                     # 开通数量，比如开通多少 AI 并发数 非必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {LocalDate} newEndTime                               # 要延期的时间,必填
     * @apiParam {Long} robotId                               # 机器人的id,必填
     * @apiParam {RechargePrestoreStreamFunEnum} rechargePrestoreStreamFunEnum                      # FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),FUN_DELAY(9,"延期消费"),FUN_CHANGE_CYCLE(10,"调整周期"),FUN_DALATATION(11,"扩容")
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "代理商客户功能开通消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDelayCost")
    public ResultObject calculateDelayCost(
            @RequestParam(required = false) Double ObjectCount,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "新的服务结束日期不能为空") @RequestParam LocalDate newEndTime,
            @NotNull(message = "机器人id不能为空") @RequestParam Long robotId) {
        Double result=distributorCustomerService.calculateDelayCost(ObjectCount,distributorId,tenantId,newEndTime,robotId);
        return ResultObject.success(result, "代理商客户延期消费计算成功");
    }
// @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateAdjustingServiceCycleCost    经销商客户调整服务周期消费   结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateAdjustingServiceCycleCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 扣除坐席并发的数量
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {RechargePrestoreStreamFunEnum} rechargePrestoreStreamFunEnum                      # FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),FUN_DELAY(9,"延期消费"),FUN_CHANGE_CYCLE(10,"调整周期"),FUN_DALATATION(11,"扩容")
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAdjustingServiceCycleCost")
    public ResultObject calculateAdjustingServiceCycleCost(@RequestParam Long robotId,
                                                          @RequestParam(required = false) LocalDate newStartTime,
                                                          @RequestParam(required = false) LocalDate newEndTime,
                                                          @RequestParam Long tenantId,
                                                          @RequestParam Long distributorId,
                                                          @RequestParam(required = false) Long reduceAiAmount) {
        Double cost= directCustomerService.calculateAdjustingServiceCycleCost(robotId,tenantId,distributorId,SecurityUtils.getUserId(), newStartTime, newEndTime, reduceAiAmount);
        return ResultObject.success(cost, "调整服务周期消费计算成功!");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateDilatationCost 代理商客户扩容消费   返回正数表示退回  负数表示消耗
     * @apiName calculateDilatationCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDilatationCost")
    public ResultObject calculateDilatationCost(@RequestParam Long tenantId,
                                               @RequestParam Integer count,
                                               @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                               @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime,
                                               @RequestParam Long distributorId,
                                               @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartEnum) {
        Double cost=directCustomerService.calculateDilatationCost(tenantId, count, startTime, endTime, distributorId,tenantAiccPartEnum);
        return ResultObject.success(cost, "扩容成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateAiCost 新增代理商客户 开通AI坐席消耗费用
     * @apiName calculateAiCost
     * @apiGroup distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "robotCount" : 55,                  # AI 坐席数必填
     *     "startTime" : "2018-08-20",                     # 服务开始时间必填
     *     "endTime" : "2022-08-17",                       # 服务结束时间必填
     *     "distributorId" : 435,                       # 代理商id,必填
     *
     *
     *
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "开通功能消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAiCost")
    public ResultObject calculateAiCost(
            @NotNull(message = "AI 座席数不能为空")@RequestParam Double robotCount,
            @NotNull(message = "服务开始时间不能为空")@RequestParam LocalDate startTime,
            @NotNull(message = "服务结束时间不能为空")@RequestParam LocalDate endTime) {
        Long distributorId = SecurityUtils.getDistributorId();
        Double result=distributorCustomerService.calculateAiCost(robotCount,distributorId,startTime, endTime,true);
        return ResultObject.success(result, "开通功能消耗费用计算成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/calculateFunctionCost 代理商客户功能开通消耗费用
     * @apiName calculateFunctionCost
     * @apiGroup distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "ObjectCount" : 55,                  # 开通数量，比如开通多少 AI 并发数 非必填
     *     "distributorId" : 435,                       # 代理商id,必填
     *     "tenantId" : 438,                       # 租户id,必填
     *     "RechargePrestoreStreamFunEnum" : FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     *
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "代理商客户功能开通消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateFunctionCost")
    public ResultObject calculateFunctionCost(
            @RequestParam(required = false) Double ObjectCount,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum,
            @RequestParam(required = false) LocalDate startTime,
            @RequestParam(required = false) LocalDate endTime) {
        Double result=distributorCustomerService.calculateFunctionCost(ObjectCount,distributorId,tenantId, rechargePrestoreStreamFunEnum,true,startTime,endTime);
        return ResultObject.success(result, "代理商客户功能开通消耗费用计算成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorCustomer/getFunctionInfo 代理商客户开通功能 窗口信息查询
     * @apiName getFunctionInfo
     * @apiGroup distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantId" : 438,                       # 租户id,必填
     *     "RechargePrestoreStreamFunEnum" : FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     *
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *
     *     },
     *     "requestId": null,
     *     "resultMsg": "代理商客户开通功能 窗口信息查询成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/getFunctionInfo")
    public ResultObject getFunctionInfo(
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        List<DistributorCustomerFunctionInfo> result=distributorCustomerService.getFunctionInfo(tenantId, rechargePrestoreStreamFunEnum);
        return ResultObject.success(result, "代理商客户开通功能 窗口信息查询成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiBoss/distributorCustomer/openFunction 代理商客户开通功能
     * @apiName openFunction
     * @apiGroup distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "ObjectCount" : 55,                  # 开通数量，比如开通多少 AI 并发数 非必填
     *     "distributorId" : 435,                       # 代理商id,必填
     *     "tenantId" : 438,                       # 租户id,必填
     *     "startTime" : "2020-10-28",                       #功能开始时间
     *     "endTime" : "2020-11-28",                       # 功能结束时间
     *     "RechargePrestoreStreamFunEnum" : FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     *
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *
     *     },
     *     "requestId": null,
     *     "resultMsg": "",
     *     "errorStackTrace": null
     * }
     */
    @PostMapping("/openFunction")
    public ResultObject openFunction(@Validated(value = DistributorCustomerOpenFunctionValidate.class) @RequestBody DistributorCustomerOpenFunctionQueryVO distributorCustomerOpenFunctionQueryVO) {
        Long userId=SecurityUtils.getUserId();
        distributorCustomerService.openFunction(distributorCustomerOpenFunctionQueryVO.getDistributorId(),distributorCustomerOpenFunctionQueryVO.getTenantId(), distributorCustomerOpenFunctionQueryVO.getObjectCount(), distributorCustomerOpenFunctionQueryVO.getRechargePrestoreStreamFunEnum(),userId,distributorCustomerOpenFunctionQueryVO.getStartTime(),distributorCustomerOpenFunctionQueryVO.getEndTime());
        return ResultObject.success("代理商客户功能开通成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/openAiccPartFunction    AICC系统开通子系统功能
     * @apiName openAiccPartFunction
     * @apiGroup directCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *      	"tenantId": 1
     *      	"enableConcurrency": true  #外呼一线多并发
     *      	"compressAudio": true  #外呼音频压缩
     *      	"useYiwiseAsr": true   # 外呼使用一知ASR
     *      	"enableSpeechCorrection": true   # 开启语音学习
     *      	"compressAudioCallin": true  #音频压缩(呼入)
     *      	"useYiwiseAsrCallin": true   #使用一只sar（呼入）
     *           "qcCostUnit":3              #qc 质检费用
     *      	"enableStatsMsgPush": true  # 外呼推送统计信息
     *      	"enableMessageInvoke": true #  外呼短信回掉
     *      	"redialLimit": 1  #重播次数, -1表示不限制
     *      	"dialIntervalStart": '00:00:00'  #外呼可选时段开始
     *      	"dialIntervalEnd": "00:00:00" #外呼可选时段结束
     *          "enableAiAssistant" ： true    # 智能辅助
     *      	"tenantAiccPartType": "crm_out_call_platform"  # 类型 crm_out_call_platform(0,"智能外呼"), crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本")
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/openAiccPartFunction")
    public ResultObject openAiccPartFunction(@RequestBody TenantAiccPartDTO tenantAiccPartDTO) {
	    tenantAiccPartDTO.processPriceUnit();
        tenantAiccPartDTO.setUpdateUserId(SecurityUtils.getUserId());
        directCustomerService.openAiccPartFunction(tenantAiccPartDTO);
        return ResultObject.success(null, "配置成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiBoss/distributorCustomer/queryAiccPartRobots    查询AICC子系统robots
     * @apiName queryAiccPartRobots
     * @apiGroup directCustomer
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/queryAiccPartRobots")
    public ResultObject queryAiccPartRobots(@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType,@RequestParam(required = false) Long tenantId) {
        Map<TenantAiccPartEnum, RobotStasPO> tenantAiccPartList=directCustomerService.queryAiccPartRobots(tenantId);
        Map<TenantAiccPartEnum,List<RobotExpirationTimeVO>> result=new HashMap<>();
        RobotStasPO robotStasPO=tenantAiccPartList.get(tenantAiccPartType);
        if(Objects.nonNull(robotStasPO)&&!CollectionUtils.isEmpty(robotStasPO.getRobotExpirationTimeVOList())){
            result.put(tenantAiccPartType,robotStasPO.getRobotExpirationTimeVOList());
        }
        return ResultObject.success(result, "配置成功");
    }
    @GetMapping("/getTenantByTenantId")
    public ResultObject getTenantByTenantId(@RequestParam Long tenantId) {
        TenantPO tenantPO= tenantService.getTenantByTenantId( tenantId);
        return ResultObject.success(tenantPO, "获取成功");
    }

}
