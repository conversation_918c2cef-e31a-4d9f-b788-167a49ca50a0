package com.yiwise.boss.api.controller.authority;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.dal.entity.AuthResourcePO;
import com.yiwise.core.dal.entity.RolePO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.authority.AuthResourceTreeVO;
import com.yiwise.core.model.vo.authority.AuthorityVO;
import com.yiwise.core.model.vo.RoleVO;
import com.yiwise.core.service.ope.platform.AuthService;
import com.yiwise.core.service.platform.RoleAuthorityService;
import com.yiwise.core.service.platform.RoleService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wangguomin
 * @Date: 2019-01-25 11:08
 */

@RestController
@RequestMapping("/apiBoss/auth")
public class AuthController {


    @Resource
    private AuthService authService;

    @Resource
    private RoleAuthorityService roleAuthorityService;

    @Resource
    private RoleService roleService;

    /**
     * @api {get} /apiBoss/auth/getSystemResourceList 获取系统的resource 列表，从数据库中拿出资源，然后进行树状输出
     * @apiName getSystemResourceList
     * @apiParam {String} systemEnum  # 系统 CRM OPE  BOSS
     * @apiGroup authResource
     *
     *
     */
    @GetMapping(value = "/getSystemResourceList")
    public ResultObject getSystemResourceList(){
        List<AuthResourceTreeVO> list = authService.getSystemResourceList(SecurityUtils.getTenantId(), SystemEnum.BOSS.getCode(), SystemEnum.BOSS.getCode());
        return ResultObject.success(list);
    }
    // @formatter:off
    /**
     * @api {get} /apiBoss/auth/authorityList  根据角色的id获取权限code的集合
     * @apiName listAuthority
     * @apiGroup /apiBoss/auth
     *
     * @apiParam {Long} roleId                                 # 角色id，必填
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data":  null,
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/authorityList")
    public ResultObject listAuthority(@RequestParam @NotNull(message = "角色id不能为空") Long roleId) {
        RolePO rolePO = roleService.selectByKey(roleId);
        List<AuthResourcePO> authResourceList = roleAuthorityService.selectAuthResourcePOByRoleId(roleId);
        RoleVO roleVO = new RoleVO();
        BeanUtils.copyProperties(rolePO, roleVO);
        if(authResourceList != null && !authResourceList.isEmpty()) {
            roleVO.setAuthList(authResourceList.stream().map(AuthResourcePO::getResourceUri).collect(Collectors.toList()));
        }
        return ResultObject.success(roleVO);
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/auth/insertOrUpdateAuthority  添加或者更新权限
     * @apiName updateAndGetAuthority
     * @apiGroup /apiBoss/auth
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"roleId" : 1,                                                                                 # 角色的id
     * 	"authorityCodes" : ["ADMIN_PUBLIC_CUSTOMER_ADOPT","ADMIN_PRIVATE_CUSTOMER_TRANSFER_DELETE"]   # 要添加或者更新的权限code的集合
     * }
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": [                                        # 权限code的集合
     *         "ADMIN_PRIVATE_CUSTOMER_TRANSFER_DELETE",
     *         "ADMIN_PUBLIC_CUSTOMER_ADOPT"
     *     ],
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/insertOrUpdateAuthority")
    public ResultObject updateAndGetAuthority(@Validated @RequestBody AuthorityVO authorityVO) {
        List<String> codeList = roleAuthorityService.updateRoleAuth(authorityVO, SecurityUtils.getUserId(),null);
        return ResultObject.success(codeList);
    }
}
