package com.yiwise.boss.api.controller.isv;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.entity.IsvInfoPO;
import com.yiwise.core.model.enums.OperationLogLogTypeEnum;
import com.yiwise.core.model.enums.OperationLogOperationTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.ope.IsvInfoVO;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.core.service.openapi.platform.IsvInfoService;
import com.yiwise.core.validate.isv.IsvStatusValidate;
import com.yiwise.core.validate.isv.IsvValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
* isv
* <AUTHOR> yangdehong
* @date : 2018/10/11 3:45 PM
*/
@RestController
@RequestMapping("apiBoss/isvInfo")
public class IsvInfoController {

    @Resource
    private IsvInfoService isvInfoService;

    @Resource
    private DistributorService distributorService;

    // @formatter:off
    /**
     * @api {post} /apiBoss/isvInfo/createIsv    新增isv
     * @apiName post isv
     * @apiGroup ope_isv
     *
     * @apiParam {Integer} tenantId 客户ID
     * @apiParam {String} tenantSign 公司签名
     * @apiParam {String} callbackUrl 回调函数
     * @apiParam {String} smsCallbackUrl 短信回调地址
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": "VQVQKKBK",
     *     "resultMsg": "获取成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("createIsv")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.ISV, refIdExpression = "#isvInfoVO.tenantId",
            operationType = OperationLogOperationTypeEnum.ISV_UPDATE,
            contentExpression = " '用户[' + #currentUser.name + '] 添加了新的isv账号[tenantId='+ #isvInfoVO.tenantId +']tenantSign='+#isvInfoVO.tenantSign "
    )
    public ResultObject createIsv(@RequestBody @Validated({IsvValidate.class}) IsvInfoVO isvInfoVO){
        Long userId = SecurityUtils.getUserId();
        Long distributorId = SecurityUtils.getDistributorId();
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        if(!distributorPO.getSupportIsvConfig()) {
            return ResultObject.fail(ComErrorCode.FORBIDDEN, "无权配置isv账号");
        }
        isvInfoService.addIsvInfo(userId, isvInfoVO.getTenantId(), null, isvInfoVO.getTenantSign(), isvInfoVO.getCallbackUrl(), isvInfoVO.getSmsCallbackUrl());
        return ResultObject.success("操作成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiBoss/isvInfo/updateIsvStatus   修改了isv账号的状态
     * @apiName updateIsvStatus
     * @apiGroup isv
     *
     * @apiParamExample {json}
     * {
     * 	"tenantId": 1,
     * 	"enabledStatus": "ENABLE"
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": "VQVQKKBK",
     *     "resultMsg": "操作成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("updateIsvStatus")
    @SimpleOperationLog(
            module = SystemEnum.BOSS, logType = OperationLogLogTypeEnum.ISV, refIdExpression = "#isvInfoVO.tenantId",
            contentExpression = " '用户[' + #currentUser.name + '] 修改了isv账号[tenantId='+ #isvInfoVO.tenantId +']'的状态 "
    )
    public ResultObject updateIsvStatus(@RequestBody @Validated({IsvStatusValidate.class}) IsvInfoVO isvInfoVO) {
        Long distributorId = SecurityUtils.getDistributorId();
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        if(!distributorPO.getSupportIsvConfig()) {
            return ResultObject.fail(ComErrorCode.FORBIDDEN, "无权配置isv账号");
        }
        Long userId = SecurityUtils.getUserId();
        isvInfoVO.setUpdateUserId(userId);
        isvInfoVO.setSystemEnum(SystemEnum.BOSS);
        isvInfoService.updateEnabledStatus(isvInfoVO);
        return ResultObject.success("操作成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiBoss/isvInfo/getIsv    根据tenantid获取isv
     * @apiName get isv
     * @apiGroup ope_isv
     *
     * @apiParam {Integer} tenantId 客户ID
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": "VQVQKKBK",
     *     "resultMsg": "获取成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("getIsv")
    public ResultObject getIsv(@RequestParam(value = "tenantId", required = false) Long tenantId) {

        Long distributorId = SecurityUtils.getDistributorId();

        //查看当前代理商的isv账号
        if (Objects.isNull(tenantId)) {
            return ResultObject.success(isvInfoService.findByDistributorId(distributorId), "操作成功");
        }

        //查看代理商客户的isv账号
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        if(!distributorPO.getSupportIsvConfig()) {
            return ResultObject.fail(ComErrorCode.FORBIDDEN, "无权查看isv账号");
        }
        IsvInfoPO isvInfo = isvInfoService.findByTenantId(tenantId);
        return ResultObject.success(isvInfo, "操作成功");
    }

}
