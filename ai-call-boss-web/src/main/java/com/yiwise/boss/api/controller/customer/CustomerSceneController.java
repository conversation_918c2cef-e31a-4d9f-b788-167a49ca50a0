package com.yiwise.boss.api.controller.customer;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.dal.entity.CustomerScenePO;
import com.yiwise.core.service.ope.platform.CustomerSceneService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/3/29
 * @class <code>CustomerSceneController</code>
 * @see
 * @since JDK1.8
 */
@Validated
@RestController
@RequestMapping("apiBoss/customerScene")
public class CustomerSceneController {

    @Resource
    private CustomerSceneService customerSceneService;

    @GetMapping("/getSceneList")
    public ResultObject<List<CustomerScenePO>> getSceneList(@RequestParam(required = false) Integer customerTrackType, @RequestParam(defaultValue = "false") Boolean isAll) {
        List<CustomerScenePO> sceneList = customerSceneService.getSceneList(customerTrackType, isAll);
        return ResultObject.success(sceneList);
    }
}
