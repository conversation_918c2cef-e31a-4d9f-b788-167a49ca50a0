package com.yiwise.batch.mq.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.core.batch.entity.dto.BatchMqRobotCalJobMessageDTO;
import com.yiwise.core.batch.mq.BatchMqRobotCallJobWriter;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @desc 批处理写入
 */
@Slf4j
public class BatchMqWriterMsgListener extends MsgIdIdempotentBaseListener<BatchMqRobotCalJobMessageDTO> {

    private static final BatchMqRobotCallJobWriter batchMqRobotCallJobWriter = AppContextUtils.getBean(BatchMqRobotCallJobWriter.class);

    public BatchMqWriterMsgListener() {
        super(new TypeReference<BatchMqRobotCalJobMessageDTO>() {});
    }

    @Override
    protected boolean myConsume(BatchMqRobotCalJobMessageDTO messageDTO) {
        batchMqRobotCallJobWriter.importToRobotCallJob(messageDTO.getCommonInfo(), messageDTO.getRecords());
        return true;
    }

}
