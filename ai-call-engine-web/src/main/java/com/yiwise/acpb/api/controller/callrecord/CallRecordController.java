package com.yiwise.acpb.api.controller.callrecord;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.helper.WeiPinHuiHelper;
import com.yiwise.core.helper.WeiPinHuiNewHelper;
import com.yiwise.core.model.dto.CallRecordExportDTO;
import com.yiwise.core.model.enums.AuthResourceUriEnum;
import com.yiwise.core.model.enums.OperationLogLogTypeEnum;
import com.yiwise.core.model.enums.OperationLogOperationTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.callrecord.CallRecordBossAndOpeQueryVO;
import com.yiwise.core.model.vo.callrecord.CallRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.ReturnVisitVO;
import com.yiwise.core.model.vo.callrecord.TrainRecordOpeQueryVO;
import com.yiwise.core.model.vo.customer.CallRecordDetailVO;
import com.yiwise.core.model.vo.ope.TrainRecordVO;
import com.yiwise.core.model.vo.robotcalljob.RobotCallJobListInfoVO;
import com.yiwise.core.model.vo.robotcalljob.RobotCallJobQueryVO;
import com.yiwise.core.model.vo.robotcalljob.RobotCallJobScrmDTO;
import com.yiwise.core.service.callin.CallInRecordService;
import com.yiwise.core.service.dialogflow.DialogFlowTrainingHistoryService;
import com.yiwise.core.service.engine.CallRecordInfoService;
import com.yiwise.core.service.engine.OperationLogService;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.csseat.CsRecordService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonService;
import com.yiwise.core.service.platform.RoleService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.validate.callrecord.QueryCallRecordFromJobValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2018/7/27
 **/
@Validated
@RestController
@RequestMapping("/apiEngine/callRecord")
public class CallRecordController {

    @Resource
    private CallRecordInfoService callRecordInfoService;

    @Resource
    private CustomerPersonService customerPersonService;

    @Resource
    private CsRecordService csRecordService;

    @Resource
    private CallInRecordService callInRecordService;

    @Resource
    private DialogFlowTrainingHistoryService dialogFlowTrainingHistoryService;


    @Resource
    RobotCallJobService robotCallJobService;

    @Resource
    UserService userService;
    @Resource
    OperationLogService operationLogService;
    @Resource
    private RoleService roleService;

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/getInfoList 任务已呼客户列表
     *
     * @apiName getCallRecordInfoList
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "lastCallRecord": true,              # 是否过滤重复通话记录只显示最近的一条 如此处为true 条件只有 robotCallJobId resultStatuses intentLevels chatDurationMin chatDurationMax 有效
     *     "redialTimes": 2,                    # 已经自动重播次数 仅在lastCallRecord=true有效
     *      "realIntent": true,              # 是否人工意向分类
     *     "robotCallJobId": 1,                 # 必填
     *     "calledPhoneNumber":"1458745212",    # 电话号码
     *     "callRecordId": 145213,              # 通话记录id
     *     "customerPersonName": "张三",        # 客户名称 模糊查询
     *     "customerGroupId": 1,                # 分组id
     *     "resultStatuses": ["ANSWERED", "REFUSED"],  # 本次通话记录的通话状态，多选，可以为空。ANSWERED，已接听；NO_ANSWER，未接；BUSY，占线； CALL_LOSS,多并发呼损; TRANSFER_ARTIFICIAL,"转人工呼损"
     *     "intentLevelTagId": 0,                 # 意向标签分组id
     *     "intentLevelCodes": [                  # 意向等级
     *          0
     *     ],
     *     "customerConcern":["公司位置","交通"],     # 客户关注点 此条件只在robotCallJobId存在的情况下有效
     *     "followStatus": "AI_INITIAL_VISIT",  # 跟进状态
     *     "dialogFlowId": 2,                   # 话术ID,
     *     "earliestCreationTime" :  "2018-07-25" ,          # 最早创建时间，日期标准格式，请不要包含时间。可以为空
     *     "latestCreationTime" :  "2018-07-30" ,            # 最晚创建时间，日期标准格式，请不要包含时间。可以为空
     *     "getTrainTaskList": true,            # 为true表示获取话术训练列表
     *     "readStatus": "NOT_READ",               # 已读未读状态 NOT_READ未读 HAS_READ 已读
     *     "chatDurationMin" : 1,                                   # 通话持续时间最小值
     *     "chatDurationMax" : 3,                                   # 通话持续时间最大值
     *     "chatDuration": "LESS_10S",          # 通话时长 LESS_10S 小于10s F10S_T1MIN 10s-60s F1MIN_T2MIN 60s-120s F2MIN_T3MIN 120s-180s F3MIN_TMORE 大于180s 区间左闭右开
     *     "hangupBy": "INITIAL_HANGUP"  # REMOTE_HANGUP 客户挂断 INITIAL_HANGUP ai挂断 字条件不为空时后端会将resultStatuses设置为已接听
     *     "pageNum": 1,
     *     "pageSize": 2
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 4,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "callRecordId": 47,
     *                 "tenantId": 1,
     *                 "robotCallJobId": 50,
     *                 "dialogFlowId": 4,
     *                 "robotCallTaskId": 4,
     *                 "customerPersonId": 5,
     *                 "calledPhoneNumber": "18817501728",
     *                 "phoneNumberId": 0,
     *                 "resultStatus": "ANSWERED",
     *                 "realIntentLevelName": "A类"                  # 意向名称
     *                 "aiIntentLevelName": "B类"                    # ai意向名称
     *                 "totalIntentLevelTagSize": 6,
     *                 "realIntentLevel": null,
     *                 "customerConcern": [
     *                 ],
     *                 "fullAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/ai_user.wav",
     *                 "customerAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/user.wav",
     *                 "analysisBasis": "",
     *                 "startTime": "2018-08-04 11:57:33",
     *                 "chatDuration": 93,
     *                 "chatRound": 16,
     *                 "manualMarked": false,
     *                 "read": "NOT_READ",
     *                 "customerGroupName": null,
     *                 "robotCallJobName": "曹文浩123",
     *                 "dialogFlowName": "张三",
     *                 "customerPersonInfo": null,
     *                 "callDetailList": null,
     *                 "transferType": "NO_TRANSFER",            # 转接状态 NO_TRANSFER 无转人工 TRANSFERED 已转人工
     *                 "transferPhoneNumber":"1585247856",        # 转人工号码
     *                 "tenantPhoneNumber":"23432",               # 主叫号码
     *                 "inWhiteList": false
     *                 "hangupBy": "INITIAL_HANGUP"  # REMOTE_HANGUP 客户挂断 INITIAL_HANGUP ai挂断
     *             }
     *         ]
     *     },
     *     "requestId": "YXDXFDSC",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    @NoLogin
    // @formatter:on
    @PostMapping(value = "/getInfoList")
    public ResultObject getCallRecordInfoList(@RequestBody @Validated(value = QueryCallRecordFromJobValidate.class) CallRecordQueryVO callRecordQuery) {
        Long tenantId = SecurityUtils.getTenantId();
        if (StringUtils.isNotBlank(callRecordQuery.getUid())) {
            String uid = WeiPinHuiHelper.getHashUid(callRecordQuery.getUid());
            if (StringUtils.isEmpty(uid)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "查询uid失败");
            }
            callRecordQuery.setUid(uid);
        }
        callRecordQuery.setTenantId(tenantId);
        callRecordQuery.setUser(SecurityUtils.getUserInfo());
        callRecordQuery.setCallRecordType(CallRecordTypeEnum.NORMAL);
        PageResultObject<CallRecordExportDTO> pageInfo = callRecordInfoService.getCallRecordInfoList(callRecordQuery);
        return ResultObject.success(pageInfo, "获取成功");
    }

    @PostMapping(value = "/getInfoListCount")
    public ResultObject getCallRecordInfoListCount(@RequestBody @Validated(value = QueryCallRecordFromJobValidate.class) CallRecordQueryVO callRecordQuery) {
        Long tenantId = SecurityUtils.getTenantId();
        callRecordQuery.setTenantId(tenantId);
        callRecordQuery.setUser(SecurityUtils.getUserInfo());
        callRecordQuery.setCallRecordType(CallRecordTypeEnum.NORMAL);
        Long count = callRecordInfoService.getCallRecordInfoListCount(callRecordQuery);
        return ResultObject.success(count, "获取成功");
    }



    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/getInfoListFromJob 来自任务的通话记录列表 含数据权限
     *
     * @apiName getInfoListFromJob
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "robotCallJobId": 1,
     *     "wechatPushIds": [1],                # 微信推送人的id。当条件不为空的时候，其他所有的条件都失效
     *     "realIntent": true,              # 是否人工意向分类
     *     "calledPhoneNumber":"1458745212",    # 电话号码
     *     "callRecordId": 145213,              # 通话记录id
     *     "customerPersonName": "张三",        # 客户名称 模糊查询
     *     "customerGroupId": 1,                # 分组id
     *     "resultStatuses": ["ANSWERED", "REFUSED"],  # 本次通话记录的通话状态，多选，可以为空。ANSWERED，已接听；NO_ANSWER，未接；BUSY，占线；CALL_LOSS(多并发呼损); TRANSFER_ARTIFICIAL,"转人工呼损"
     *     "intentLevelTagId": 0,                 # 意向标签分组id
     *     "intentLevelCodes": [                  # 意向等级
     *          0
     *     ],
     *     "followStatus": "AI_INITIAL_VISIT",  # 跟进状态
     *     "dialogFlowId": 2,                   # 话术ID,
     *     "earliestCreationTime" :  "2018-07-25" ,          # 最早创建时间，日期标准格式，请不要包含时间。可以为空
     *     "latestCreationTime" :  "2018-07-30" ,            # 最晚创建时间，日期标准格式，请不要包含时间。可以为空
     *     "getTrainTaskList": true,            # 为true表示获取话术训练列表
     *     "readStatus": "NOT_READ",               # 已读未读状态 NOT_READ未读 HAS_READ 已读
     *     "chatDurationMin" : 1,                                   # 通话持续时间最小值
     *     "chatDurationMax" : 3,                                   # 通话持续时间最大值
     *     "chatDuration": "LESS_10S",          # 通话时长 LESS_10S 小于10s F10S_T1MIN 10s-60s F1MIN_T2MIN 60s-120s F2MIN_T3MIN 120s-180s F3MIN_TMORE 大于180s 区间左闭右开
     *     "pageNum": 1,
     *     "pageSize": 2,
     *     "callRecordOrder": "REAL_INTENT_LEVEL",                             # 排序字段 与排序方向要同时使用 REAL_INTENT_LEVEL 意向等级
     *     "orderDirection": "ascending"                                # 排序方向 与排序字段要同时使用 descending 倒序 ascending 正序 排序字段和排序方向同时不为空时进行排序 首页用ascending
     *     "staffGroupId" : 1,                                        #坐席组ID
     *     "staffId" : 1,                                             #坐席ID
     *     "csTransferNotify" : "NOTIFY_SUCCESS" ,                       #  NOTIFY_NOT_EXIST(1, "未通知"),
     *                                                                      NOTIFY_SUCCESS(2, "通知成功"),
     *                                                                      NOTIFY_FAIL(3, "通知失败"),
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 4,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "callRecordId": 47,
     *                 "tenantId": 1,
     *                 "robotCallJobId": 50,
     *                 "dialogFlowId": 4,
     *                 "robotCallTaskId": 4,
     *                 "customerPersonId": 5,
     *                 "calledPhoneNumber": "18817501728",
     *                 "phoneNumberId": 0,
     *                 "resultStatus": "ANSWERED",
     *                 "realIntentLevelName": "A类"                  # 意向名称
     *                 "aiIntentLevelName": "B类"                    # ai意向名称
     *                 "totalIntentLevelTagSize": 6,
     *                 "realIntentLevel": null,
     *                 "customerConcern": [
     *                 ],
     *                 "fullAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/ai_user.wav",
     *                 "customerAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/user.wav",
     *                 "analysisBasis": "",
     *                 "startTime": "2018-08-04 11:57:33",
     *                 "chatDuration": 93,
     *                 "chatRound": 16,
     *                 "manualMarked": false,
     *                 "read": "NOT_READ",
     *                 "customerGroupName": null,
     *                 "robotCallJobName": "曹文浩123",
     *                 "dialogFlowName": "张三",
     *                 "customerPersonInfo": null,
     *                 "callDetailList": null,
     *                 "transferType": "NO_TRANSFER",            # 转接状态 NO_TRANSFER 无转人工 TRANSFERED 已转人工
     *                 "transferPhoneNumber":"1585247856",        # 转人工号码
     *                 "tenantPhoneNumber":"23432",               # 主叫号码
     *                 "inWhiteList": false
     *                 "wechatPushUserName": "测试，吃的撒"
     *                 "wechatPushTime": null 或者 2019-01-04 14:43:27,
     *                 "followUserName":"tom"
     *             }
     *         ]
     *     },
     *     "requestId": "YXDXFDSC",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/getInfoListFromJob")
    public ResultObject getInfoListFromJob(@RequestBody CallRecordQueryVO callRecordQuery) {
        callRecordQuery.setTenantId(SecurityUtils.getTenantId());
        if (StringUtils.isNotBlank(callRecordQuery.getUid())) {
            String uid = WeiPinHuiHelper.getHashUid(callRecordQuery.getUid());
            callRecordQuery.setUid(uid);
        }
        UserPO user = SecurityUtils.getUserInfo();
        callRecordQuery.setCallRecordType(CallRecordTypeEnum.NORMAL);
        PageResultObject<CallRecordExportDTO> list = callRecordInfoService.getCallRecordInfoListFromJobList(callRecordQuery, user);
        return ResultObject.success(list, "获取成功");
    }
    @PostMapping(value = "/getInfoListFromJobCount")
    public ResultObject getInfoListFromJobCount(@RequestBody CallRecordQueryVO callRecordQuery) {
        callRecordQuery.setTenantId(SecurityUtils.getTenantId());
        if (StringUtils.isNotBlank(callRecordQuery.getUid())) {
            String uid = WeiPinHuiHelper.getHashUid(callRecordQuery.getUid());
            if (StringUtils.isEmpty(uid)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "查询uid失败");
            }
            callRecordQuery.setUid(uid);
        }
        UserPO user = SecurityUtils.getUserInfo();
        callRecordQuery.setCallRecordType(CallRecordTypeEnum.NORMAL);
        Long count = callRecordInfoService.getCallRecordInfoListFromJobCount(callRecordQuery, user);
        return ResultObject.success(count, "获取成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/getInfoListFromTraining 来自话术训练的通话记录列表 含数据权限
     *
     * @apiName getInfoListFromTraining
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "robotCallJobId": 1,
     *     "calledPhoneNumber":"1458745212",    # 电话号码
     *     "callRecordId": 145213,              # 通话记录id
     *     "customerPersonName": "张三",        # 客户名称 模糊查询
     *     "customerGroupId": 1,                # 分组id
     *     "resultStatuses": ["ANSWERED", "REFUSED"],  # 本次通话记录的通话状态，多选，可以为空。ANSWERED，已接听；NO_ANSWER，未接；BUSY，占线；
     *     "intentLevelTagId": 0,                 # 意向标签分组id
     *     "intentLevelCodes": [                  # 意向等级
     *          0
     *     ],
     *     "followStatus": "AI_INITIAL_VISIT",  # 跟进状态
     *     "dialogFlowId": 2,                   # 话术ID,
     *     "earliestCreationTime" :  "2018-07-25" ,          # 最早创建时间，日期标准格式，请不要包含时间。可以为空
     *     "latestCreationTime" :  "2018-07-30" ,            # 最晚创建时间，日期标准格式，请不要包含时间。可以为空
     *     "getTrainTaskList": true,            # 为true表示获取话术训练列表
     *     "readStatus": "NOT_READ",               # 已读未读状态 NOT_READ未读 HAS_READ 已读
     *     "chatDurationMin" : 1,                                   # 通话持续时间最小值
     *     "chatDurationMax" : 3,                                   # 通话持续时间最大值
     *     "chatDuration": "LESS_10S",          # 通话时长 LESS_10S 小于10s F10S_T1MIN 10s-60s F1MIN_T2MIN 60s-120s F2MIN_T3MIN 120s-180s F3MIN_TMORE 大于180s 区间左闭右开
     *     "pageNum": 1,
     *     "pageSize": 2
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 4,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "callRecordId": 47,
     *                 "tenantId": 1,
     *                 "robotCallJobId": 50,
     *                 "dialogFlowId": 4,
     *                 "robotCallTaskId": 4,
     *                 "customerPersonId": 5,
     *                 "calledPhoneNumber": "18817501728",
     *                 "phoneNumberId": 0,
     *                 "resultStatus": "ANSWERED",
     *                 "realIntentLevelName": "A类"                  # 意向名称
     *                 "aiIntentLevelName": "B类"                    # ai意向名称
     *                 "totalIntentLevelTagSize": 6,
     *                 "realIntentLevel": null,
     *                 "customerConcern": [
     *                 ],
     *                 "fullAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/ai_user.wav",
     *                 "customerAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/user.wav",
     *                 "analysisBasis": "",
     *                 "startTime": "2018-08-04 11:57:33",
     *                 "chatDuration": 93,
     *                 "chatRound": 16,
     *                 "manualMarked": false,
     *                 "read": "NOT_READ",
     *                 "customerGroupName": null,
     *                 "robotCallJobName": "曹文浩123",
     *                 "dialogFlowName": "张三",
     *                 "customerPersonInfo": null,
     *                 "callDetailList": null,
     *                 "transferType": "NO_TRANSFER",            # 转接状态 NO_TRANSFER 无转人工 TRANSFERED 已转人工
     *                 "transferPhoneNumber":"1585247856",        # 转人工号码
     *                 "tenantPhoneNumber":"23432",               # 主叫号码
     *                 "inWhiteList": false
     *             }
     *         ]
     *     },
     *     "requestId": "YXDXFDSC",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/getInfoListFromTraining")
    public ResultObject getInfoListFromTraining(@RequestBody CallRecordQueryVO callRecordQuery) {
        callRecordQuery.setTenantId(SecurityUtils.getTenantId());
        UserPO user = SecurityUtils.getUserInfo();
        if (Objects.isNull(callRecordQuery.getCallRecordType())) {
            callRecordQuery.setCallRecordType(CallRecordTypeEnum.VERBAL_TRICK_TRAINING);
        }
        PageResultObject<CallRecordExportDTO> pageInfo = callRecordInfoService.getCallRecordInfoListFromTraining(callRecordQuery, user);
        return ResultObject.success(pageInfo, "获取成功");
    }

    @PostMapping(value = "/getInfoListFromTrainingCount")
    public ResultObject getInfoListFromTrainingCount(@RequestBody CallRecordQueryVO callRecordQuery) {
        callRecordQuery.setTenantId(SecurityUtils.getTenantId());
        UserPO user = SecurityUtils.getUserInfo();
        if (Objects.isNull(callRecordQuery.getCallRecordType())) {
            callRecordQuery.setCallRecordType(CallRecordTypeEnum.VERBAL_TRICK_TRAINING);
        }
        Integer totalCount = callRecordInfoService.getCallRecordInfoListFromTrainingCount(callRecordQuery, user);
        return ResultObject.success(totalCount, "获取成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/getInfoListFromDirectCall 来自快速拨打的通话记录列表 含数据权限
     *
     * @apiName getInfoListFromDirectCall
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "robotCallJobId": 1,
     *     "calledPhoneNumber":"1458745212",    # 电话号码
     *     "callRecordId": 145213,              # 通话记录id
     *     "customerPersonName": "张三",        # 客户名称 模糊查询
     *     "customerGroupId": 1,                # 分组id
     *     "resultStatuses": ["ANSWERED", "REFUSED"],  # 本次通话记录的通话状态，多选，可以为空。ANSWERED，已接听；NO_ANSWER，未接；BUSY，占线；
     *     "intentLevelTagId": 0,                 # 意向标签分组id
     *     "intentLevelCodes": [                  # 意向等级
     *          0
     *     ],
     *     "followStatus": "AI_INITIAL_VISIT",  # 跟进状态
     *     "dialogFlowId": 2,                   # 话术ID,
     *     "earliestCreationTime" :  "2018-07-25" ,          # 最早创建时间，日期标准格式，请不要包含时间。可以为空
     *     "latestCreationTime" :  "2018-07-30" ,            # 最晚创建时间，日期标准格式，请不要包含时间。可以为空
     *     "getTrainTaskList": true,            # 为true表示获取话术训练列表
     *     "readStatus": "NOT_READ",               # 已读未读状态 NOT_READ未读 HAS_READ 已读
     *     "chatDurationMin" : 1,                                   # 通话持续时间最小值
     *     "chatDurationMax" : 3,                                   # 通话持续时间最大值
     *     "chatDuration": "LESS_10S",          # 通话时长 LESS_10S 小于10s F10S_T1MIN 10s-60s F1MIN_T2MIN 60s-120s F2MIN_T3MIN 120s-180s F3MIN_TMORE 大于180s 区间左闭右开
     *     "pageNum": 1,
     *     "pageSize": 2,
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 4,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "callRecordId": 47,
     *                 "tenantId": 1,
     *                 "robotCallJobId": 50,
     *                 "dialogFlowId": 4,
     *                 "robotCallTaskId": 4,
     *                 "customerPersonId": 5,
     *                 "calledPhoneNumber": "18817501728",
     *                 "phoneNumberId": 0,
     *                 "resultStatus": "ANSWERED",
     *                 "intentLevel": "A",
     *                 "realIntentLevelName": "A类"                  # 意向名称
     *                 "aiIntentLevelName": "B类"                    # ai意向名称
     *                 "totalIntentLevelTagSize": 6,
     *                 "customerConcern": [
     *                 ],
     *                 "fullAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/ai_user.wav",
     *                 "customerAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId50/OUZAXBCB__TaskId_4/user.wav",
     *                 "analysisBasis": "",
     *                 "startTime": "2018-08-04 11:57:33",
     *                 "chatDuration": 93,
     *                 "chatRound": 16,
     *                 "manualMarked": false,
     *                 "read": "NOT_READ",
     *                 "customerGroupName": null,
     *                 "robotCallJobName": "曹文浩123",
     *                 "dialogFlowName": "张三",
     *                 "customerPersonInfo": null,
     *                 "callDetailList": null,
     *                 "transferType": "NO_TRANSFER",            # 转接状态 NO_TRANSFER 无转人工 TRANSFERED 已转人工
     *                 "transferPhoneNumber":"1585247856",        # 转人工号码
     *                 "tenantPhoneNumber":"23432",               # 主叫号码
     *                 "inWhiteList": false,
     *             }
     *         ]
     *     },
     *     "requestId": "YXDXFDSC",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/getInfoListFromDirectCall")
    public ResultObject getInfoListFromDirectCall(@RequestBody CallRecordBossAndOpeQueryVO callRecordQuery) {
        callRecordQuery.setSystem(Objects.isNull(callRecordQuery.getSystemType())?SystemEnum.CRM:callRecordQuery.getSystemType());
        callRecordQuery.setTenantId(SecurityUtils.getTenantId());
        UserPO user = SecurityUtils.getUserInfo();
        callRecordQuery.setCallRecordType(CallRecordTypeEnum.DIRECT_CALL);
        PageResultObject<CallRecordExportDTO> pageInfo = callRecordInfoService.getCallRecordInfoListFromDirectCall(callRecordQuery, user);
        return ResultObject.success(pageInfo, "获取成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/callDetailList 查询一个客户的一个通话记录的详情
     * @apiName callDetailList
     * @apiGroup callRecord
     *
     * @apiParam {Long} callRecordId                           #通话记录ID   必填
     * @apiParam {Integer} type                                 #0-ai 1-人工外呼
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *   "code": 200,
     *   "data": {
     *     "callRecordId": 58,
     *     "customerAudioUrl": null,
     *     "startTime": "2018-08-07T00:51:05",
     *     "chatRound": 0,
     *     "chatDuration": 0,
     *     "customerPerson": {
     *       "customerPersonId": 45,
     *       "creationTime": "2018-08-06T23:01:02",
     *       "tenantId": 1,
     *       "customerGroupId": 32,
     *       "name": "曹文浩",
     *       "phoneNumber": "17751279857",
     *       "wechatNumber": null,
     *       "email": null,
     *       "source": "IMPORTED_FROM_INTERNATIONAL_WATERS",
     *       "lastEmotion": null,
     *       "customerLevel": null,
     *       "gender": null,
     *       "followUserId": 0,
     *       "followStatus": "AI_INITIAL_VISIT",
     *       "properties": {},
     *       "lastChatDuration": 0,
     *       "lastStartTime": "2018-08-30T14:50:58",
     *       "attributes": [],
     *       "lastIntentLevel": "C",
     *       "lastDialStatus": "ANSWERED",
     *       "inWhiteList": false           # 是否在黑名单中
     *     },
     *     "customerGroup": {
     *       "customerGroupId": 32,
     *       "tenantId": 1,
     *       "name": "研发部"
     *     },
     *     "resultStatus": "NO_ANSWER",
     *     "intentLevel": "E",
     *     "realIntentLevel": null,
     *     "customerConcern": [],
     *     "analysisBasis": "意向分析基础依据",
     *     "fullAudioUrl": "https://ai-call-platform.oss-cn-hangzhou.aliyuncs.com/DialogueRecording/TenantId1/CallJobId52/EADVVYJL_TaskId_19/early_media.wav",
     *     "callDetailList": [
     *     {
     *         "callDetailId": 2364,
     *         "callRecordId": 3259,
     *         "tenantId": 1,
     *         "text": "喂，您好, （停顿2s），您好，想问下您位于钱塘江边、奥体中心附近，均价只要20000的现房楼盘，有兴趣了解一下吗？",
     *         "startOffset": null,
     *         "endOffset": null,
     *         "type": "ROBOT",
     *         "debugLog": null,
     *         "robotKnowledgeId": null,
     *         "dialogFlowNodeId": null,
     *         "intentBranchId": null,
     *         "emotion": null,
     *         "correctionStatus": "NO_CORRECTION",
     *         "feedbackStatus": "NO_FEED_BACK",
     *         "replaceText": null
     *       },
     *       {
     *         "callDetailId": 2365,
     *         "callRecordId": 3259,
     *         "tenantId": 1,
     *         "text": "喂，你好听得见吗？",
     *         "startOffset": 1110,
     *         "endOffset": 4295,
     *         "type": "PERSON",
     *         "debugLog": "默认分支",
     *         "robotKnowledgeId": null,
     *         "dialogFlowNodeId": null,
     *         "intentBranchId": null,
     *         "emotion": null,
     *         "correctionStatus": "NO_CORRECTION",                     # 纠错状态 NO_CORRECTION 未纠错 CORRECTION 已纠错 NO_ERROR 无错误
     *         "feedbackStatus": "NO_FEED_BACK",                        # 反馈状态 NO_FEED_BACK 无反馈  HELPFUL 有效  UN_HELPFUL 无效
     *         "replaceText": {
     *              "origin": "雅",
     *              "replace": "的",
     *              "createDocument": 9
     *          }
     *       }]
     *     "realIntentLevelName": "D类",
     *     "totalIntentLevelTagSize": 6,
     *   },
     *   "requestId": "FMPYCIUW",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/callDetailList")
    public ResultObject callDetailList(@RequestParam @NotNull(message = "通话记录id不能为空") Long callRecordId,
                                      @RequestParam(required = false, defaultValue = "0") Integer type,
                                      @RequestParam(required = false, defaultValue = "false") Boolean simpleDebug) {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        boolean encryptPhoneNumber = true;
        if (roleService.hasAuthResource(userId, SystemEnum.AICC, AuthResourceUriEnum.basic_setting_other_phone_show)) {
            encryptPhoneNumber = false;
        }
        CallRecordDetailVO callRecordDetailVOList;
        if (type == 0) {
            callRecordDetailVOList = customerPersonService.getCallDetailList(callRecordId, tenantId, encryptPhoneNumber, simpleDebug);
        } else {
            callRecordDetailVOList = customerPersonService.getCsCallDetailList(callRecordId, tenantId);
        }
        return ResultObject.success(callRecordDetailVOList);
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/markIntentLevel 标记通话记录的意向等级
     * @apiName markIntentLevel
     * @apiGroup callRecord
     *
     * @apiParam {Long} callRecordId                           # 通话记录ID   必填
     * @apiParam {Enum} intentLevel                            # 重新标记通话记录的意向等级
     * @apiParam {Integer} type    # 0-ai  1-人工外呼
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *   "code": 200,
     *   "data": null,
     *   "requestId": "KXPKFJKY",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/markIntentLevel")
    @SimpleOperationLog(
            module = SystemEnum.AICC, subModule = SystemEnum.CALL_OUT, logType = OperationLogLogTypeEnum.ROBOTCALLJOB,
            contentExpression = " '标记通话记录[' + #callRecordId + ']的人工意向等级' "
    )
    public ResultObject markIntentLevel(@RequestParam @NotNull(message = "通话记录id不能为空") Long callRecordId,
                                       @RequestParam @NotNull(message = "intentLevel不能为空") Integer intentLevel,
                                       @RequestParam(required = false, defaultValue = "0") Integer type,
                                       @RequestParam(required = false) Long intentLevelTagId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (type == 0) {
            callRecordInfoService.updateCallRecordIntentLevel(tenantId, callRecordId, intentLevel);
        } else if (type == 1) {
            csRecordService.updateCallRecordIntentLevel(tenantId, callRecordId, intentLevel,intentLevelTagId);
        } else {
            callInRecordService.updateCallRecordIntentLevel(tenantId, callRecordId, intentLevel,intentLevelTagId);
        }
        return ResultObject.success();
    }

    @PostMapping("/markIntentLevelRuntime")
    @SimpleOperationLog(
            module = SystemEnum.AICC, logType = OperationLogLogTypeEnum.CALL_RECORD,
            contentExpression = " '用户[' + #currentUser.name + '] 在通话结束前标记通话记录[callRecordId='+ #callRecordId +']的意向等级' "
    )
    public ResultObject markIntentLevelRuntime(@RequestParam @NotNull(message = "通话记录id不能为空") Long callRecordId,
                                              @RequestParam @NotNull(message = "intentLevel不能为空") Integer intentLevel) {
        callRecordInfoService.updateCallRecordIntentLevel(SecurityUtils.getTenantId(), callRecordId, intentLevel, false);
        return ResultObject.success();
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/readCallRecord 读取请求
     * @apiName readCallRecord
     * @apiGroup callRecord
     *
     * @apiParam {Long} callRecordId                           # 通话记录ID   必填
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *   "code": 200,
     *   "data": null,
     *   "requestId": "READFJKY",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/readCallRecord")
    public ResultObject readCallRecord(@RequestParam @NotNull(message = "callRecordId不能为空") Long callRecordId) {
        Long tenantId = SecurityUtils.getTenantId();
        callRecordInfoService.setCallRecordStatus(callRecordId, tenantId, SystemEnum.CRM);
        return ResultObject.success();
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/getTrainHistory  获取话术设置训练历史
     * @apiName getTrainHistory
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "callRecordId":3,
     *     "dialogFlowId":3,
     *     "chatDuration": "LESS_10S" #  LESS_10S, 10S_1MIN, F1MIN_T2MIN, F2MIN_T3MIN, F3MIN_TMORE
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *          "callRecordId":1  #记录ID
     *          "calledPhoneNumber":"4343243" #外呼号码
     *          "startTime":"2019-02-02 00:11:33"  #外呼时间
     *          "chatDuration":33   #外呼时长
     *          "dialogFlowName":"fdsadsa" #话术名称
     *          "properties": {
     *              "df":"fd",
     *              "fds":"wewr"
     *          }  #自定义变量列表
     *
     *     },
     *     "requestId": "YZJVVMLT",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/getTrainHistory")
    public ResultObject getTrainHistory(@RequestBody TrainRecordOpeQueryVO condition) {
        condition.setTenantId(SecurityUtils.getTenantId());
        PageResultObject<TrainRecordVO> result = dialogFlowTrainingHistoryService.getCrmDialogFlowTrainRecord(condition);
        return ResultObject.success(result, "获取成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/callRecord/exportTrainHistory  获取话术设置
     * @apiName exportTrainHistory
     * @apiGroup callRecord
     *
     * @apiParamExample {json} Request Example
     * {
     *     "callRecordId":3,
     *     "dialogFlowId":3,
     *     "chatDuration": "LESS_10S" #  LESS_10S, 10S_1MIN, F1MIN_T2MIN, F2MIN_T3MIN, F3MIN_TMORE
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *          "callRecordId":1  #记录ID
     *          "calledPhoneNumber":"4343243" #外呼号码
     *          "startTime":"2019-02-02 00:11:33"  #外呼时间
     *          "chatDuration":33   #外呼时长
     *          "dialogFlowName":"fdsadsa" #话术名称
     *          "properties": {
     *              "df":"fd",
     *              "fds":"wewr"
     *          }  #自定义变量列表
     *
     *     },
     *     "requestId": "YZJVVMLT",
     *     "resultMsg": "获取成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/exportTrainHistory")
    public ResultObject exportTrainHistory(@RequestBody TrainRecordOpeQueryVO condition) {
        condition.setTenantId(SecurityUtils.getTenantId());
        JobStartResultVO result = dialogFlowTrainingHistoryService.exportCrmDialogTrain(condition, SecurityUtils.getUserId());
        return ResultObject.success(result, "获取成功");
    }


    // @formatter:off
    /**
     * @api {get} /apiEngine/callRecord/hasReturnVisitCrm  查询当前通话记录是否有回访记录
     * @apiName hasReturnVisitCrm
     * @apiGroup callRecord
     * @apiParam {Integer} callRecordId 通话记录id
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": true,
     *     "requestId": "YZJVVMLT",
     *     "resultMsg": "查询成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("hasReturnVisit")
    public ResultObject hasReturnVisit(@RequestParam @NotNull(message = "通话记录id不能为空") Long callRecordId) {
        Long tenantId = SecurityUtils.getTenantId();
        Boolean hasReturnVisit = callRecordInfoService.hasReturnVisitCrm(callRecordId, tenantId);
        return ResultObject.success(hasReturnVisit, "查询成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiEngine/callRecord/returnVisit  查询回访记录
     * @apiName returnVisit
     * @apiGroup callRecord
     * @apiParam {Integer} callRecordId 通话记录id
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "customerPersonName": "客户姓名",
     *         "calledPhoneNumber": "13000000000",
     *         "customVariables": {
     *             "自定义属性1": "自定义属性值1",
     *             "自定义属性2": "自定义属性值2"
     *         },
     *         "intentLevel": 1, # "ai意向等级(数字)"
     *         "realIntentLevel": 2, # "人工意向等级(数字)"
     *         "intentLevelTagId": 1, # 意向标签组id
     *         "stepList": [
     *             {
     *                 "text": "每个主流程中第一个普通节点的话术文本",
     *                 "branchFlow": [
     *                     {
     *                         "name": "节点名",
     *                         "type": "IntentBranch" # IntentBranch 意向分支, ChatNode 普通节点,
     *                         "intentBranchCategory": DEFINITE 肯定, NEGATIVE 否定, DECLINE 拒绝,
     *                                                 NEURAL 中性, COLLECT_DATA 收集信息, NOANSWER 客户无应答
     *                     }
     *                 ],
     *                 "attributesCollection": {
     *                     "收集属性1": "收集属性值1",
     *                     "收集属性2": "收集属性值2"
     *                 }
     *             }
     *         ]
     *     },
     *     "requestId": "YZJVVMLT",
     *     "resultMsg": "查询成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("returnVisit")
    public ResultObject returnVisit(@RequestParam @NotNull(message = "通话记录id不能为空") Long callRecordId) {
        Long tenantId = SecurityUtils.getTenantId();
        ReturnVisitVO result = callRecordInfoService.getReturnVisitCrm(callRecordId, tenantId);
        return ResultObject.success(result, "查询成功");
    }

    /**
     * /apiEngine/callRecord/getJobs scrm搜索AI外呼任务接口
     */
    @NoLogin
    @PostMapping(value = "/getJobs")
    public ResultObject getJobs(@RequestBody RobotCallJobQueryVO robotCallJobQueryVO) {
        PageResultObject<RobotCallJobListInfoVO> pageInfo = robotCallJobService.getSimpleRobotCallJobListInfo(robotCallJobQueryVO);
        return ResultObject.success(pageInfo.getContent(), "获取成功");
    }

    @GetMapping("addDownloadRecord")
    @SimpleOperationLog(
            module = SystemEnum.AICC, subModule = SystemEnum.CALL_OUT, logType = OperationLogLogTypeEnum.AI_CONTACT_HISTORY,
            contentExpression = " 下载录音' ", operationType = OperationLogOperationTypeEnum.CALL_RECORD_DOWNLOAD
    )
    public ResultObject download() {
        return ResultObject.success( "查询成功");
    }

    /**
     * /apiEngine/callRecord/getAllProperties scrm搜索AI外呼任务接口
     */
    @NoLogin
    @PostMapping(value = "/getAllProperties")
    public ResultObject getAllProperties(@RequestBody RobotCallJobQueryVO robotCallJobQueryVO) {
        RobotCallJobScrmDTO robotCallJobScrmDTO = new RobotCallJobScrmDTO();
        Set<String> properties = new HashSet<>();
        if(Objects.nonNull(robotCallJobQueryVO.getTenantId())&& Objects.nonNull(robotCallJobQueryVO.getRobotCallJobId())){
            properties = robotCallJobService.getPropertiesFromRobotCallJob(robotCallJobQueryVO.getRobotCallJobId(), robotCallJobQueryVO.getTenantId());
            robotCallJobScrmDTO.setProperties(properties);
            RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobQueryVO.getRobotCallJobId());
            if(Objects.nonNull(robotCallJobPO)){
                robotCallJobScrmDTO.setRobotCallJobId(robotCallJobPO.getRobotCallJobId());
                robotCallJobScrmDTO.setStatus(robotCallJobPO.getStatus());
            }
        }
        return ResultObject.success(robotCallJobScrmDTO, "获取自定义属性成功");
    }
}