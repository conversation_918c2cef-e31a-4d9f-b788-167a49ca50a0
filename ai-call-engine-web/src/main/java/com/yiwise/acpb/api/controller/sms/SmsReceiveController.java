package com.yiwise.acpb.api.controller.sms;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.batch.mq.BatchMqSmsReceiveReader;
import com.yiwise.core.dal.entity.SmsReceiveRecordPO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sms.SmsReceiveQueryVO;
import com.yiwise.core.model.vo.sms.SmsReceiveRecordVO;
import com.yiwise.core.service.engine.SmsReceiveRecordService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/3/7
 * @class <code>SmsReceviceController</code>
 * @description 回复短信管理
 * @see
 * @since JDK1.8
 */
@RestController
@RequestMapping("/apiEngine/smsReceiveRecord")
public class SmsReceiveController {

    @Resource
    private SmsReceiveRecordService smsReceiveRecordService;

    @Resource
    private BatchMqSmsReceiveReader batchMqSmsReceiveReader;

    /**
     * 短信回复列表
     *
     * @param smsReceiveQueryVO
     * @return
     */
    @GetMapping("/list")
    public ResultObject<PageResultObject<SmsReceiveRecordPO>> getList(SmsReceiveQueryVO smsReceiveQueryVO) {
        Long tenantId = SecurityUtils.getTenantId();
        smsReceiveQueryVO.setTenantId(tenantId);
        PageResultObject<SmsReceiveRecordPO> smsReceiveRecordPageInfo = smsReceiveRecordService.getSmsReceivePageInfo(smsReceiveQueryVO);
        return ResultObject.success(smsReceiveRecordPageInfo);
    }

    /**
     * 短信回复列表-limit
     *
     * @param smsReceiveQueryVO
     * @return
     */
    @GetMapping("/listLimit")
    public ResultObject<PageResultObject<SmsReceiveRecordPO>> getListLimit(SmsReceiveQueryVO smsReceiveQueryVO) {
        Long tenantId = SecurityUtils.getTenantId();
        smsReceiveQueryVO.setTenantId(tenantId);
        PageResultObject<SmsReceiveRecordPO> smsReceiveRecordPageInfo = smsReceiveRecordService.getSmsReceivePageInfoLimit(smsReceiveQueryVO);
        return ResultObject.success(smsReceiveRecordPageInfo);
    }

    /**
     * 短信回复列表总数-limit
     *
     * @param smsReceiveQueryVO
     * @return
     */
    @GetMapping("/listCount")
    public ResultObject<Long> getListCount(SmsReceiveQueryVO smsReceiveQueryVO) {
        Long tenantId = SecurityUtils.getTenantId();
        smsReceiveQueryVO.setTenantId(tenantId);
        Long count = smsReceiveRecordService.getSmsReceivePageInfoCount(smsReceiveQueryVO);
        return ResultObject.success(count);
    }

    /**
     * 回复记录导出
     *
     * @param exportVO
     * @return
     */
    @PostMapping("/export")
    public ResultObject<JobStartResultVO> exportFile(@RequestBody SmsReceiveRecordVO exportVO) {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        exportVO.setTenantId(tenantId);
        JobStartResultVO resultVO = smsReceiveRecordService.export(exportVO, tenantId, userId);
        return ResultObject.success(resultVO);
    }

    /**
     * 导入到短信任务
     *
     * @param smsReceiveRecordVO
     * @return
     */
    @NoLogin
    @PostMapping("/importToSmsJob")
    public ResultObject<JobStartResultVO> importToSmsJob(@RequestBody SmsReceiveRecordVO smsReceiveRecordVO) {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        smsReceiveRecordVO.setTenantId(tenantId);
        JobStartResultVO resultVO = smsReceiveRecordService.importToSmsJob(smsReceiveRecordVO, tenantId, userId);
        return ResultObject.success(resultVO);
    }

    /**
     * 导入到外呼任务
     *
     * @param smsReceiveRecordVO
     * @return
     */
    @PostMapping("/importToCalloutJob")
    public ResultObject<JobStartResultVO> importToCalloutJob(@RequestBody SmsReceiveRecordVO smsReceiveRecordVO) {
        smsReceiveRecordVO.setTenantId(SecurityUtils.getTenantId());
        smsReceiveRecordVO.setCurrentUserId(SecurityUtils.getUserId());
        return ResultObject.success(batchMqSmsReceiveReader.importToRobotCallJob(smsReceiveRecordVO));
    }

    /**
     * 导入到黑名单
     *
     * @param smsReceiveRecordVO
     * @return
     */
    @NoLogin
    @PostMapping("/importToBlackList")
    public ResultObject<JobStartResultVO> importToBlackList(@RequestBody SmsReceiveRecordVO smsReceiveRecordVO) {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        smsReceiveRecordVO.setTenantId(tenantId);
        JobStartResultVO resultVO = smsReceiveRecordService.importToBlackList(smsReceiveRecordVO, tenantId, userId);
        return ResultObject.success(resultVO);
    }

    @PostMapping("/add")
    public ResultObject addSmsReceive(@RequestBody SmsReceiveRecordPO param) {
        Long tenantId = SecurityUtils.getTenantId();
        param.setTenantId(tenantId);
        smsReceiveRecordService.addSmsReceive(param);
        return ResultObject.success();
    }

}
