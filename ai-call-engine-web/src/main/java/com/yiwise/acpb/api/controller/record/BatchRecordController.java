package com.yiwise.acpb.api.controller.record;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.model.vo.batchrecord.BatchRecordUpdateRequestVO;
import com.yiwise.core.service.dialogflow.DialogFlowAccessControlService;
import com.yiwise.core.service.dialogflow.DialogRecordBatchService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量录音
 * @create 2019/09/30
 **/
@RestController
@RequestMapping("/apiEngine/batchRecord")
public class BatchRecordController {

    @Resource
    private DialogRecordBatchService dialogRecordBatchService;
    @Resource
    private DialogFlowAccessControlService dialogFlowAccessControlService;

    // @formatter:off
    /**
     * @api {get} /apiEngine/batchRecord/download 批量下载录音
     * @apiName download
     * @apiGroup /apiEngine/batchRecord
     *
     * @apiParam {Long} dialogFlowId            # 话术id
     *
     * @apiSuccessExample Response 200 Example
     * {
     *
     * }
     */
    // @formatter:on
    @GetMapping("download")
    public ResultObject download(@RequestParam("dialogFlowId") Long dialogFlowId) {
        dialogFlowAccessControlService.checkDialogFlowAccessibleByTenant(SecurityUtils.getTenantId(), dialogFlowId);
        String url = dialogRecordBatchService.batchDownloadRecord(dialogFlowId);
        return ResultObject.success(url);
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/batchRecord/upload 批量上传录音到oss
     * @apiName upload
     * @apiGroup /apiEngine/batchRecord
     *
     * @apiParam {Long} dialogFlowId            # 话术id
     * @apiParam {File} file  # 文件
     *
     * @apiSuccessExample Response 200 Example
     * {
     *
     * }
     */
    // @formatter:on
    @PostMapping("upload")
    public ResultObject upload(@RequestParam("dialogFlowId") Long dialogFlowId,
                              @RequestParam("file") MultipartFile multipartFile) {
        dialogFlowAccessControlService.checkCrmDialogFlowModifiable(dialogFlowId, SecurityUtils.getTenantId());
        Pair<Boolean, List<String>> result = dialogRecordBatchService.batchUploadRecord(dialogFlowId, multipartFile, SecurityUtils.getUserId());
        String successMsg = "上传成功";
        String failedMsg = "存在上传失败音频，请检查音频格式或音频文件名是否符合标准";
        String msg = result.getFirst() ? failedMsg : successMsg;
        return ResultObject.success(result.getSecond(), msg);
    }

    // @formatter:off
    /**
     * @api {post} /apiEngine/batchRecord/update 批量更新录音话术录音
     * @apiName update
     * @apiGroup /apiEngine/batchRecord
     *
     * @apiParamExample {json} request example
     * {
     *     "dialogFlowId": 1,
     *     "urls": ["https://xxx", "https://yyy"]
     * }
     *
     * @apiSuccessExample Response 200 Example
     * {
     *
     * }
     */
    // @formatter:on
    @PostMapping("update")
    public ResultObject upload(@RequestBody BatchRecordUpdateRequestVO request) {
        dialogFlowAccessControlService.checkCrmDialogFlowModifiable(request.getDialogFlowId(), SecurityUtils.getTenantId());
        String info = dialogRecordBatchService.batchUpdateRecord(request.getDialogFlowId(), request.getUrls(), SecurityUtils.getUserId());
        return ResultObject.success(null, info);
    }
}
