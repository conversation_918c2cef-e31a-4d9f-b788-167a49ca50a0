#!/bin/bash

TIME_NOW=`date "+%Y-%m-%d%H%M%S"`

# deploy server
function deploy_server(){
  if [ -d "/home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT/" ];then

        if [ ! -d "/tmp/backup/ai-call-back/$1-1.0.0-SNAPSHOT/" ];then
            mkdir -p /tmp/backup/ai-call-back/$1-1.0.0-SNAPSHOT/
        fi

        mkdir -p /tmp/backup/ai-call-back/$1-1.0.0-SNAPSHOT/${TIME_NOW}
        /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT/bin/startup.sh stop
        mv  /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT  /tmp/backup/ai-call-back/$1-1.0.0-SNAPSHOT/${TIME_NOW}
  fi

   tar -xzvf /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT.tar.gz -C /home/<USER>/ai-call-back/
   mv /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT.tar.gz /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT
   chmod a+x /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT/bin/startup.sh
   /home/<USER>/ai-call-back/$1-1.0.0-SNAPSHOT/bin/startup.sh start
   echo "==========deploy $1 success ============="
}

(deploy_server $1)