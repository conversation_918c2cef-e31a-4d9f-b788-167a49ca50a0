spring:
  profiles:
    active: @spring.profiles.active@
  application:
    name: ai-call-dialogFlow-web
  batch:
    job:
      enabled: false
  cloud:
    nacos:
      discovery:
        server-addr: mse-8ac2d1d0-p.nacos-ans.mse.aliyuncs.com:8848
        namespace: 4f9cf083-8b62-4990-bdb3-8c2a57e34a12
server:
  port: ${server.dialogFlow.port}

feign:
  hystrix:
    enabled: true

# 关闭hystrix的超时, 使用feign的超时设置
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: false

app:
  # 使用的 Apollo 的项目（应用）编号
  id: ai-call-back-huoshan
apollo:
  # Apollo Meta Server 地址
  meta: http://47.110.234.45:8080
  cache-dir: /tmp/apollo-cache-dir
  bootstrap:
    # 是否开启 Apollo 配置预加载功能。默认为 false。
    enabled: "true"
    # 是否开启 Apollo 支持日志级别的加载时机。默认为 false。
    eagerLoad:
      enabled: "true"
    # 使用的 Apollo 的命名空间，默认为 application。
    namespaces: application

