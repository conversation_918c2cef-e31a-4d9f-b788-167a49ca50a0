{"description": ["Xmx", "Xms", "Xmn", "jmxremotePort", "remoteDebugAddress", "DOCKER_BASE_IMAGE", "armsOptions"], "defaultValue": {"Xmx": "700m", "Xms": "700m", "Xmn": "200m", "remoteDebugAddress": "", "DOCKER_BASE_IMAGE": "docker.yiwise.net/base/java-base", "armsOptions": ""}, "ai-call-callout-job": {"daily": ["700m", "700m", "200m", "10011", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["700m", "700m", "200m", "10011", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["6g", "4g", "2g", "10011", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["6g", "4g", "2g", "10011", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["12g", "8g", "4g", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["12g", "8g", "4g", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["12g", "8g", "4g", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["12g", "8g", "4g", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["6g", "4g", "2g", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-engine-web": {"daily": ["2g", "2g", "700m", "10021", "-Xdebug -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-daily -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=daily -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "daily-huoshan": ["700m", "700m", "200m", "10021", "-Xdebug -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-daily -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=daily-huoshan -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre": ["700m", "700m", "200m", "10021", "-Xdebug -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-pre  -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=pre -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre-finance": ["700m", "700m", "200m", "10021", "-Xdebug -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-pre-finance  -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=pre-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod": ["3g", "2g", "1g", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-prod  -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=prod -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huoshan": ["3g", "2g", "1g", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base", ""], "prod-finance": ["3g", "2g", "1g", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-engine-web-prod-finance  -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-engine-web -Dmse.namespace=prod-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huawei": ["3g", "2g", "1g", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base", ""], "localization": ["3g", "2g", "1g", "", "", "docker.yiwise.net/base/java-ffmpeg-base", ""]}, "ai-call-quartz": {"daily": ["700m", "700m", "200m", "10031", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "daily-huoshan": ["700m", "700m", "200m", "10031", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre": ["700m", "700m", "200m", "10031", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre-finance": ["700m", "700m", "200m", "10031", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "prod": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["1g", "500m", "300m", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["1g", "500m", "300m", "", "-Dspring.profiles.active=prod-huawei", null], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-base"]}, "ai-call-ope-web": {"daily": ["1000m", "1000m", "300m", "10041", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-daily -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=daily -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "daily-huoshan": ["700m", "700m", "200m", "10041", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-daily-huoshan -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=daily-huoshan -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre": ["700m", "700m", "200m", "10041", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-pre -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=pre -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre-finance": ["2000m", "2000m", "700m", "10041", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-pre-finance -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=pre-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-prod -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=prod -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huoshan": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base", "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-ope-web-prod-finance -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-ope-web -Dmse.namespace=prod-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huawei": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-miniapp-web": {"daily": ["700m", "700m", "200m", "10051", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["700m", "700m", "200m", "10051", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["600m", "600m", "200m", "10051", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["600m", "600m", "200m", "10051", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["1536m", "1536m", "512m", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["1536m", "1536m", "512m", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["1536m", "1536m", "512m", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["1536m", "1536m", "512m", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-dialog-flow-web": {"daily": ["700m", "700m", "200m", "10061", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["700m", "700m", "200m", "10061", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["2100m", "2100m", "700m", "10061", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["2100m", "2100m", "700m", "10061", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["6600m", "6600m", "2200m", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["6600m", "6600m", "2200m", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["6600m", "6600m", "2200m", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["6600m", "6600m", "2200m", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-platform-web": {"daily": ["2g", "2g", "700m", "10071", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "daily-huoshan": ["2g", "2g", "700m", "10071", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre": ["700m", "700m", "200m", "10071", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre-finance": ["700m", "700m", "200m", "10071", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "prod": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["2000m", "2000m", "700m", "", "-Dspring.profiles.active=prod-huawei", null], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-base"]}, "ai-call-open-api": {"daily": ["600m", "600m", "200m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-open-api-daily"], "daily-huoshan": ["600m", "600m", "200m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, ""], "pre": ["600m", "600m", "200m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-open-api-pre"], "pre-finance": ["600m", "600m", "200m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-open-api-pre-finance"], "prod": ["2g", "2g", "700m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=prod -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-open-api-prod"], "prod-huoshan": ["2g", "2g", "700m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=prod-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, ""], "prod-finance": ["2g", "2g", "700m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=prod-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-open-api-prod-finance"], "prod-huawei": ["2g", "2g", "700m", "10081", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=prod-huawei -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, ""], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-base", ""]}, "ai-call-boss-web": {"daily": ["600m", "600m", "200m", "10091", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-daily -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=daily -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "daily-huoshan": ["600m", "600m", "200m", "10091", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-daily-huoshan -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=daily-huoshan -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre": ["600m", "600m", "200m", "10091", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-pre -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=pre -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "pre-finance": ["600m", "600m", "200m", "10091", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=,", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-pre-finance -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=pre-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-prod -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=prod -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huoshan": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod-finance", null, "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=hdsh8j2263@09bba141ff19f16 -Darms.appName=ai-call-boss-web-prod-finance -Dmse.licenseKey=hdsh8j2263@61bf742a4281a7a -Dmse.appName=ai-call-boss-web -Dmse.namespace=prod-finance -Dmse.enable=true -Dprofiler.micro.service.mse.version=ent"], "prod-huawei": ["2g", "2g", "700m", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-import-rearrange-web": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["3g", "3g", "1g", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-isv-callback": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-batch-job": {"daily": ["700m", "700m", "200m", "10997", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "daily-huoshan": ["700m", "700m", "200m", "10997", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre": ["700m", "700m", "200m", "10997", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre-finance": ["700m", "700m", "200m", "10997", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "prod": ["4000m", "4000m", "1300m", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["4000m", "4000m", "1300m", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["4000m", "4000m", "1300m", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["4000m", "4000m", "1300m", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-callin-job": {"daily": ["600m", "600m", "200m", "10111", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["600m", "600m", "200m", "10111", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["700m", "700m", "200m", "10111", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["700m", "700m", "200m", "10111", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-sms-job": {"daily": ["600m", "600m", "200m", "10113", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "daily-huoshan": ["600m", "600m", "200m", "10113", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre": ["700m", "700m", "200m", "10113", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "pre-finance": ["700m", "700m", "200m", "10113", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", null], "prod": ["700m", "700m", "200m", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["700m", "700m", "200m", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["700m", "700m", "200m", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["700m", "700m", "200m", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-freeswitch-curl": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-qc-job": {"daily, daily-huoshan": ["600m", "600m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "pre, pre-finance": ["600m", "600m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "prod, prod-huawei, prod-huoshan, prod-finance": ["2g", "2g", "1g", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-cscall-job": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-esl": {"daily": ["700m", "700m", "200m", "", "-Dspring.profiles.active=daily", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["700m", "700m", "200m", "", "-Dspring.profiles.active=daily-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["700m", "700m", "200m", "", "-Dspring.profiles.active=pre", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["700m", "700m", "200m", "", "-Dspring.profiles.active=pre-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["700m", "700m", "200m", "-Dspring.profiles.active=prod", "", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["700m", "700m", "200m", "-Dspring.profiles.active=prod-huoshan", "", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["700m", "700m", "200m", "-Dspring.profiles.active=prod-finance", "", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["700m", "700m", "200m", "-Dspring.profiles.active=prod-huawei", "", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-message-router": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-text-service-web": {"daily": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["3g", "3g", "1g", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-qc-web": {"daily, daily-huoshan": ["600m", "600m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "pre, pre-finance": ["600m", "600m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "prod, prod-huawei, prod-huoshan, prod-finance": ["2g", "2g", "660m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-wechat-job": {"daily": ["600m", "600m", "200m", "10101", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "daily-huoshan": ["600m", "600m", "200m", "10101", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=daily-huoshan -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre": ["600m", "600m", "200m", "10101", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "pre-finance": ["600m", "600m", "200m", "10101", "-Xdebug -Xnoagent -Djava.compiler=NONE -Dspring.profiles.active=pre-finance -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=", "docker.yiwise.net/base/java-ffmpeg-base"], "prod": ["2g", "2g", "660m", "", "-Dspring.profiles.active=prod", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huoshan": ["2g", "2g", "660m", "", "-Dspring.profiles.active=prod-huoshan", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-finance": ["2g", "2g", "660m", "", "-Dspring.profiles.active=prod-finance", "docker.yiwise.net/base/java-ffmpeg-base"], "prod-huawei": ["2g", "2g", "660m", "", "-Dspring.profiles.active=prod-huawei", "docker.yiwise.net/base/java-ffmpeg-base"], "localization": ["700m", "700m", "200m", "", "", "docker.yiwise.net/base/java-ffmpeg-base"]}, "ai-call-async-job": {"daily": ["2g", "2g", "700m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["2g", "2g", "700m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["2g", "2g", "700m", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["600m", "600m", "200m", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huawei", null]}, "ai-call-batch-mq": {"daily": ["2g", "2g", "660m", "", "-Dspring.profiles.active=daily", null], "daily-huoshan": ["600m", "600m", "200m", "", "-Dspring.profiles.active=daily-huoshan", null], "pre": ["2g", "2g", "660m", "", "-Dspring.profiles.active=pre", null], "pre-finance": ["2g", "2g", "660m", "", "-Dspring.profiles.active=pre-finance", null], "prod": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod", null], "prod-huoshan": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huoshan", null], "prod-finance": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-finance", null], "prod-huawei": ["6g", "6g", "2g", "", "-Dspring.profiles.active=prod-huawei", null]}}