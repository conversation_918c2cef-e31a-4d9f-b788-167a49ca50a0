package com.yiwise.aicall.engine.aliyun;

import com.google.common.collect.Lists;
import com.yiwise.aicall.engine.CallInRobot;
import com.yiwise.aicall.engine.asr.AsrContext;
import com.yiwise.aicall.engine.asr.AsrContextFactory;
import com.yiwise.aicall.engine.bo.CallInDetailBO;
import com.yiwise.aicall.engine.bo.RuntimeTextAudioContent;
import com.yiwise.aicall.engine.config.ApplicationConfig;
import com.yiwise.aicall.engine.engine.brain.NlpSemanticManager;
import com.yiwise.aicall.engine.engine.brain.SemanticManager;
import com.yiwise.aicall.engine.engine.brain.entities.DialogFlow;
import com.yiwise.aicall.engine.engine.dialog.DialogFlowHandler;
import com.yiwise.aicall.engine.helper.AsyncAsrInitExecutorHelper;
import com.yiwise.aicall.engine.helper.SpeachSynthesizerHelper;
import com.yiwise.aicall.engine.model.AiSayBO;
import com.yiwise.aicall.engine.model.AnalyzeDetail;
import com.yiwise.aicall.engine.model.RobotCallTask;
import com.yiwise.aicall.engine.model.UserSayBO;
import com.yiwise.aicall.engine.nlp.NlpAudioManager;
import com.yiwise.aicall.engine.utils.AsrHelper;
import com.yiwise.aicall.engine.utils.NlpHelper;
import com.yiwise.aicall.engine.yiwise.CallInYiwiseRobot;
import com.yiwise.aicall.engine.yiwise.YiwiseRobot;
import com.yiwise.aicc.callin.api.model.dto.CallInDetailDTO;
import com.yiwise.aicc.callin.api.model.dto.CallInRecordDTO;
import com.yiwise.aicc.callin.api.model.enums.CsProcessEnum;
import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.audio.vad.VadProcessor;
import com.yiwise.base.common.audio.vad.VadProcessorCallback;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.LogHubWarnException;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.EslConstant;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.asr.AsrApplicationPO;
import com.yiwise.core.dal.mongo.asr.AsrDialogFlowRelationPO;
import com.yiwise.core.dal.mongo.asr.AsrHotWordPO;
import com.yiwise.core.helper.ZheJiaoCallbackHelper;
import com.yiwise.core.model.bo.IntegerIntegerBO;
import com.yiwise.core.model.bo.asr.YiwiseAsrParam;
import com.yiwise.core.model.bo.cs.CallInStatusBO;
import com.yiwise.core.model.bo.phonenumber.PhoneHomeLocationBO;
import com.yiwise.core.model.bo.robotcalljob.TaskCallResultBO;
import com.yiwise.core.model.bo.robotcalltask.RunTimeRobotCallTaskBO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.entity.KeyWordDetectionPO;
import com.yiwise.core.model.dialogflow.entity.TextAudioContentPO;
import com.yiwise.core.model.dto.CsSeatQueueDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.asr.AsrProviderEnum;
import com.yiwise.core.model.enums.callin.CallInResultStatusEnum;
import com.yiwise.core.model.enums.callin.CallInstanceTypeEnum;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.callin.StaffGroupTypeEnum;
import com.yiwise.core.model.enums.dialogflow.BotTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.nlp.bo.*;
import com.yiwise.core.model.vo.callrecord.TransferCallVO;
import com.yiwise.core.service.asr.AsrApplicationService;
import com.yiwise.core.service.asr.AsrDialogFlowRelationService;
import com.yiwise.core.service.asr.AsrHotWordService;
import com.yiwise.core.service.assistant.AssistantRecordService;
import com.yiwise.core.service.callin.CallInDetailService;
import com.yiwise.core.service.callin.CallInRecordService;
import com.yiwise.core.service.customerextrainfo.CustomerPersonExtraFieldService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.dialogflow.KeyWordDetectionService;
import com.yiwise.core.service.engine.IntentLevelTagDetailService;
import com.yiwise.core.service.engine.PhoneLocationService;
import com.yiwise.core.service.engine.csseat.CsStaffInfoService;
import com.yiwise.core.service.engine.csseat.CsStaffStatService;
import com.yiwise.core.service.engine.csseat.CsStaffUserService;
import com.yiwise.core.service.engine.isv.IsvCallbackService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.openapi.platform.IsvInfoService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.websocket.WebSocketOverMQService;
import com.yiwise.core.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.core.util.CustomerInfoRecognitionUtil;
import com.yiwise.customer.data.platform.rpc.api.service.dto.CustomerAttributeMetaDataDTO;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import javaslang.Tuple2;
import javaslang.control.Try;
import lombok.Getter;
import lombok.Setter;
import net.sourceforge.peers.Constants;
import net.sourceforge.peers.sip.Utils;
import net.sourceforge.peers.sip.core.useragent.UserAgent;
import net.sourceforge.peers.sip.syntaxencoding.SipHeaderFieldName;
import net.sourceforge.peers.sip.syntaxencoding.SipHeaderFieldValue;
import net.sourceforge.peers.sip.syntaxencoding.SipHeaders;
import net.sourceforge.peers.sip.transactionuser.Dialog;
import net.sourceforge.peers.sip.transport.SipRequest;
import net.sourceforge.peers.sip.transport.SipResponse;
import net.sourceforge.peers.soundmanager.CallInSoundManagerEventListener;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.WAV_TO_PCM_HEAD_LEN;
import static com.yiwise.core.config.ApplicationConstant.ZHEJIAO_TEANET;
import static com.yiwise.core.config.CommonApplicationConstant.CURR_ENV;

/**
 * <AUTHOR>
 */
public abstract class CallInAliyunAsrSoundManager extends CallInSoundManagerEventListener implements NluCallListener {
    private static final Logger logger = LoggerFactory.getLogger(CallInAliyunAsrSoundManager.class);

    private static final CallInRecordService callInRecordService = AppContextUtils.getBean(CallInRecordService.class);
    private static final PhoneLocationService phoneLocationService = AppContextUtils.getBean(PhoneLocationService.class);
    private static final CallInDetailService callInDetailService = AppContextUtils.getBean(CallInDetailService.class);
    private static final KeyWordDetectionService keyWordDetectionService = AppContextUtils.getBean(KeyWordDetectionService.class);
    private final static DialogFlowService dialogFlowService = AppContextUtils.getBean(DialogFlowService.class);
    private static final IntentLevelTagDetailService intentLevelTagDetailService = AppContextUtils.getBean(IntentLevelTagDetailService.class);
    private static final CustomerPersonExtraFieldService customerPersonExtraFieldService = AppContextUtils.getBean(CustomerPersonExtraFieldService.class);
    private static final TenantService tenantService = AppContextUtils.getBean(TenantService.class);
    private static final CallStatsMongoService callStatsMongoService = AppContextUtils.getBean(CallStatsMongoService.class);
    private static final RedisOpsService redisOpsService = AppContextUtils.getBean(RedisOpsService.class);
    private static final CsStaffStatService csStaffStatService = AppContextUtils.getBean(CsStaffStatService.class);
    private static final CsStaffInfoService csStaffInfoService = AppContextUtils.getBean(CsStaffInfoService.class);
    private static final CsStaffUserService csStaffUserService = AppContextUtils.getBean(CsStaffUserService.class);
    private static final AssistantRecordService assistantRecordService = AppContextUtils.getBean(AssistantRecordService.class);
    private static final AsrApplicationService asrApplicationService = AppContextUtils.getBean(AsrApplicationService.class);
    private static final AsrHotWordService asrHotWordService = AppContextUtils.getBean(AsrHotWordService.class);
    private static final AsrDialogFlowRelationService asrDialogFlowRelationService = AppContextUtils.getBean(AsrDialogFlowRelationService.class);
    private static final WebSocketOverMQService webSocketOverMQService = AppContextUtils.getBean(WebSocketOverMQService.class);
    private static final File waitingFile = Try.of(() -> MyFileUtils.getFileFromJar("waitingSound/SundialDreams.wav")).getOrElseThrow((e) -> new LogHubWarnException(ComErrorCode.NOT_EXIST, "没有找到等待坐席录音的文件", e));
    private final static byte[] emptyBuffer = new byte[Constants.PACKET_BUFFER_SIZE];

    private static final IsvCallbackService isvCallbackService = AppContextUtils.getBean(IsvCallbackService.class);
    private static final IsvInfoService isvInfoService = AppContextUtils.getBean(IsvInfoService.class);


    @Setter
    @Getter
    private FreeswitchInfoPO freeswitchInfo;

    private volatile long lastAiPlayBeginTime = -1L;
    private volatile long lastAiPlayEndTime = 0L;
    private volatile long lastUserSayEndTime = System.currentTimeMillis();
    private volatile FileInputStream currPlayInputStream = null;
    private volatile String currPlayFileName = null;
    private volatile boolean pauseCurrPlayAudio = false;
    @Getter
    protected volatile RobotCallTask robotCallTask;
    private volatile CallInRobot robot;
    private AtomicInteger asrWaitingSoundBytesOffset = new AtomicInteger(0);                     // 开启语音识别前用户录音的说话字节数（目的是为了和asr返回的时长能吻合上）
    private List<AnalyzeDetail> callDetailInfoList = new LinkedList<>();
    private FilePlayProgressRecorder filePlayProgressRecorder = new FilePlayProgressRecorder();
    @Getter
    @Setter
    protected SemanticManager semanticManager;
    @Getter
    @Setter
    protected NlpSemanticManager nlpSemanticManager;

    @Getter
    private AsrProviderEnum asrProvider = AsrProviderEnum.ALI;
    @Getter
    private String dialogFlowAsrModelAppkey;
    @Getter
    private YiwiseAsrParam yiwiseAsrParam;

    private InputStream waitingSoundInputStream;

    @Setter
    @Getter
    protected DialogFlow dialogFlow;

    /**
     * VAD
     */
    private volatile VadProcessor vadProcessor;
    private volatile boolean vadProceeding = false;
    private volatile boolean vadPlayBlank = false;

    /**
     * 记录ai录音播放和结束的时间
     */
    private long perAiPlayStartTime = -1L;
    private long perAiPlayEndTime = 0L;
    /**
     * asr 处理是否结束
     */
    private volatile boolean asrEnd = true;

    protected CallInRecordPO callInRecordPO = new CallInRecordPO();
    @Getter
    @Setter
    private TaskCallResultBO taskCallResultInfo;
    @Getter
    @Setter
    private Long duration = 0L;
    /**
     * 反应灵敏度
     */
    private int maxSentenceSilence = 1000;

    /**
     * 转第三方和手机记录的唯一id
     */
    protected String xEid;
    /**
     * 是否是nlp
     */
    @Getter
    protected boolean isNlp = false;
    /**
     * 超时时间 秒
     */
    private Integer timeoutSeconds;

    /**
     * 开场白播放录音前，设置迟滞时间；
     * 原因是SIP媒体传输需要过程，直接播放开场白，容易丢失最前面的1到2个字；
     * 设置迟滞时间可以实现SIP媒体传输的初始化，这段时间内用户侧会听到短暂的等待音乐；
     * 从而使得开场白声音不出现吞字现象；后续可以做成apollo可配置的
     */
    private long INCOMING_CALL_WAIT_MS = 800L;

    /**
     * ai 等待时间，单位秒
     */
    private Integer waitTime;

    protected NlpAfterChatBO nlpAfterChatBO;
    protected TenantPO tenantPO;
    /**
     * 转人工随机地址
     */
    @Getter
    private String callInRecordingString;
    private LinkedBlockingQueue<AudioByteBuffer> audioInputStreamList = null;
    private AtomicInteger stopTts = new AtomicInteger(0);
    // incoming call 处理是否完成
    private boolean incomingInited = false;
    //接待id
    protected Long callInReceptionId;
    protected Long lineId;
    protected Long customerPersonId;

    protected Long csStaffGroupId = 0L;
    protected Long csStaffId = 0L;
    protected LocalDateTime transferStartTime;
    protected List<TransferCallVO> transferToCsList = new ArrayList<>();
    protected boolean transferFlag = false;
    protected CsSeatQueueDTO csSeatQueue = new CsSeatQueueDTO();
    /**
     * 人工介入消息推送
     */
    protected CsTransferNotifyEnum csTransferNotify = CsTransferNotifyEnum.NOTIFY_NONE;

    protected Long lastTransferGroupId;
    protected String dialogFlowIdStr;
    protected String customerPhoneNumber;

    protected boolean isHuaweiCallInDemo = false;

    /**
     * 按键相关
     */
    @Setter
    @Getter
    private volatile Long firstPressMillis;
    @Setter
    private volatile Long dtmfTimeoutMillis;

    /**
     * 呼入asrSM
     *
     * @param robotCallTask
     */
    public CallInAliyunAsrSoundManager(RobotCallTask robotCallTask) {
        super(robotCallTask.getRobotTaskCallInfo().getRobotCallJobId(), robotCallTask.getRobotCallTaskId());

        this.robotCallTask = robotCallTask;
        //初始化vad信息
        vadInitialize();
    }

    /**
     * 初始化vad相关信息
     */
    private void vadInitialize() {
        // 初始化VAD相关信息
        this.vadProcessor = new VadProcessor(new VadProcessorCallback() {
            @Override
            public void onVoiceStart() {
                // 检测到在说话，播放等待音
                vadProceeding = true;
                setLastUserSayEndTime(System.currentTimeMillis());
                logger.info("TaskId={} VAD 检测到用户正在说话...", getTaskId());
            }

            @Override
            public void onVoiceEnd(long frameCount) {
                // 检测到用户说话已经结束，重置标志
                vadProceeding = false;
                setLastUserSayEndTime(System.currentTimeMillis());
                logger.info("TaskId={} VAD 检测到用户说话完毕", getTaskId());
            }
        });
        setVadDetector();
    }

    public void setVadDetector() {
        if (dialogFlow != null) {
            if (null == this.dialogFlow.getVadGateMute() || this.dialogFlow.getVadGateMute() < 0) {
                vadProcessor.setMutePointValueGate(2450);
            } else {
//            // 存储的是差值，setMuteGateValue，最低值是1500，避免别人知道真实数值
                vadProcessor.setMutePointValueGate(1500 + this.dialogFlow.getVadGateMute());
            }
        } else {
            vadProcessor.setMutePointValueGate(2450);
        }
        vadProcessor.setMuteGateFrameCount(37);
        vadProcessor.setMaxStallTime(1000);
    }

    @Override
    public void reset() {
        super.reset();
        // 重置200状态
        setSip200Success(false);
        incomingInited = false;
        lastUserSayEndTime = System.currentTimeMillis();
        this.lastTransferGroupId = null;

        // asr的robot重置
        robot.releaseRobot(this.isCalleePickup(), this.robotCallTask.getRobotCallTaskId());
        AtomicReference<String> intentLevelDetail = new AtomicReference<>("");

        try {
            if (waitingSoundInputStream != null) {
                waitingSoundInputStream.close();
            }
        } catch (IOException e) {
        } finally {
            waitingSoundInputStream = null;
        }

        try {
            if (callInReceptionId != null) {
                //话后处理
                if (this.csStaffId != null && this.csStaffId > 0) {
                    //设置呼入化后处理
                    if (Objects.nonNull(this.callInRecordPO.getTenantId())) {
                        csStaffStatService.setCallInDealTime(this.callInRecordPO.getTenantId(), this.csStaffId);
                    }
                }

                long count = redisOpsService.incrementKey(RedisKeyCenter.ReceptionCallCount(callInReceptionId), -1);
                if (count < 0) {
                    redisOpsService.delete(RedisKeyCenter.ReceptionCallCount(callInReceptionId));
                }
            }

            callInRecordPO.setSeatType(StaffGroupTypeEnum.AI);
            callInRecordPO.setTransferStartTime(this.transferStartTime);
            callInRecordPO.setCsProcess(this.csSeatQueue.getCsProcess());

            //先设置好对话时长
            callInRecordPO.setChatDuration(taskCallResultInfo.getChatDuration());
            callInRecordPO.setCsProcess(this.csSeatQueue.getCsProcess());
            if (CsTransferNotifyEnum.NOTIFY_SUCCESS.equals(this.csTransferNotify) && callInRecordPO.getTransferStartTime() != null) {
                //说明切入成功且接听了
                callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
                callInRecordPO.setSeatStatus(CallInResultStatusEnum.ANSWERED);
            } else if (CsTransferNotifyEnum.NOTIFY_SUCCESS.equals(this.csTransferNotify) && callInRecordPO.getTransferStartTime() == null) {
                //说明切入成功没接听
                callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
                callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_ANSWER);
            } else {
                CallInStatusBO callInStatusBO = csStaffUserService.getCallInStatus(callInRecordPO, this.csSeatQueue, getCallInRecordingString());
                callInRecordPO.setResultStatus(callInStatusBO.getCallInStatus());
                callInRecordPO.setSeatStatus(callInStatusBO.getSeatStatus());
            }
            // 部署信息
            try {
                CallRecordPO.DeploymentInformation deploymentInformation = new CallRecordPO.DeploymentInformation();
                deploymentInformation.setServerName(ServerInfoConstants.SERVER_HOSTNAME);
                deploymentInformation.setIpAddress(ServerInfoConstants.SERVER_IP_ADDRESS);
                deploymentInformation.setLogId(this.getIdentifyId());
                deploymentInformation.setFreeswitchInfoId(this.getFreeswitchInfo().getFreeswitchInfoId());
                if (AsrProviderEnum.ALI.equals(getAsrProvider())) {
                    deploymentInformation.setAppkey(getDialogFlowAsrModelAppkey());
                }
                callInRecordPO.setDeploymentInformation(deploymentInformation);
            } catch (Exception e) {
                logger.error("部署信息错误", e);
            }

            List<AnalyzeDetail> callInDetailInfoBOList = getCallInDetailInfoBOList();
            if (DialStatusEnum.isAnswered(taskCallResultInfo.getResultStatus())) {
                List<AnalyzeDetail> userSayList = callInDetailInfoBOList.stream().filter(item -> CharacterEnum.PERSON.equals(item.getType())).collect(Collectors.toList());
                List<IntegerIntegerBO> userSayOffsetList = userSayList.stream().map(item -> new IntegerIntegerBO(item.getStartOffset(), item.getEndOffset())).collect(Collectors.toList());
                List<String> userSayTextList = userSayList.stream().map(AnalyzeDetail::getText).collect(Collectors.toList());
                GenderEnum gender = CustomerInfoRecognitionUtil.getGender(callInRecordPO.getTenantId(), taskCallResultInfo.getCustomerAudioUrl(), userSayOffsetList, tenantPO.getEnableGenderRecognition());
                callInRecordPO.setRecognizeGender(gender);
                CustomerEmotionEnum emotion = CustomerInfoRecognitionUtil.getEmotion(userSayTextList);
                callInRecordPO.setEmotion(emotion);
            }

            NlpAfterKnowledgeBO afterKnowledgeBO = NlpHelper.getIntentLevel(nlpAfterChatBO);
            if (afterKnowledgeBO != null) {
                logger.info("获取nlp意向 obj={}", afterKnowledgeBO);
                Integer intent = afterKnowledgeBO.getKnowledgeIntentLevelCode() == null ? afterKnowledgeBO.getDialogIntentLevelCode() : afterKnowledgeBO.getKnowledgeIntentLevelCode();
                taskCallResultInfo.setIntentLevel(intent);
                Try.run(() -> {
                    if (Objects.nonNull(afterKnowledgeBO.getKnowledgeIntentLevelCode())) {
                        intentLevelDetail.set(intentLevelTagDetailService.getIntentLevelDetailByCode(dialogFlow.getIntentLevelTagId(), afterKnowledgeBO.getKnowledgeIntentLevelCode(), false).getName());
                        taskCallResultInfo.setAnalysisBasis(String.format("命中业务知识：%s，该知识点设置了意向等级为：%s", afterKnowledgeBO.getKnowledgeTitle(), intentLevelDetail.get()));
                    }
                }).onFailure(e -> logger.error("获取意向名称失败", e));
            }
            callInRecordPO.setUseYiwiseAsr(robot instanceof CallInYiwiseRobot);

            //判断csCallCategory, 呼入接待AI外呼转人工，呼入接待直接转语音（这里不存在，逻辑在esl），呼入接待-人工介入
            if (callInRecordPO.getCsStaffId() != null || callInRecordPO.getStaffId() != null) {
                if (CallRecordTransferTypeEnum.TRANSFERED.equals(this.taskCallResultInfo.getTransferType())) {
                    callInRecordPO.setCsCallCategory(CsCallCategoryEnum.CALL_IN_CS);
                }
                if (CsTransferNotifyEnum.NOTIFY_SUCCESS.equals(callInRecordPO.getCsTransferNotify())) {
                    callInRecordPO.setCsCallCategory(CsCallCategoryEnum.CALL_IN_INT);
                }
            }
            if (callInRecordPO.getCustomerPersonId() == null) {
                logger.error("customerPersonId不存在");
                return;
            }
            if (nlpAfterChatBO != null && MapUtils.isNotEmpty(nlpAfterChatBO.getDataCollectInfo())) {
                callInRecordPO.setDataCollectInfo(nlpAfterChatBO.getDataCollectInfo());
            }
            callInRecordPO.setChatDuration(taskCallResultInfo.getChatDuration());
            callInRecordPO.setChatRound(taskCallResultInfo.getChatRound());
            callInRecordPO.setIntentLevel(taskCallResultInfo.getIntentLevel());
            callInRecordPO.setCustomerConcern(taskCallResultInfo.getCustomerConcern());
            callInRecordPO.setAttributes(taskCallResultInfo.getCustomerAttribute());
            callInRecordPO.setFullAudioUrl(taskCallResultInfo.getFullAudioUrl());
            callInRecordPO.setAnalysisBasis(taskCallResultInfo.getAnalysisBasis());
            callInRecordPO.setCustomerAudioUrl(taskCallResultInfo.getCustomerAudioUrl());
            callInRecordPO.setTransferType(taskCallResultInfo.getTransferType());
            callInRecordPO.setPhoneNumberId(lineId);
            if (callInRecordPO.getTenantId() != null) {
                TenantPO tenantPO = tenantService.getTenantByTenantId(callInRecordPO.getTenantId());
                callInRecordPO.setDistributorId(Objects.isNull(tenantPO) ? 0 : tenantPO.getDistributorId());
            }
            callInRecordService.insertCallInRecordByAiSuccess(callInRecordPO, taskCallResultInfo);
            logger.info("callInRecordPO:" + callInRecordPO.toString());
            List<CallInDetailPO> callInDetailList = getCallInDetailInfoList();
            // callDetailList入库
            callInDetailList.forEach(item -> {
                item.setTenantId(callInRecordPO.getTenantId());
                item.setCallInRecordId(callInRecordPO.getCallInRecordId());
                if (StringUtils.isNotEmpty(item.getText()) && item.getText().length() > 500) {
                    item.setText(item.getText().substring(0, 500));
                }
            });
            callInDetailService.addCallInDetailList(callInDetailList);

            try {
                assistantRecordService.relevantRecord(callInRecordPO.getCallInRecordId(), CallInstanceTypeEnum.CALL_IN_RECORD, callInRecordPO.getTenantId(), callInRecordPO.getCsStaffId() != null ? callInRecordPO.getCsStaffId() : callInRecordPO.getStaffId(), this.callInRecordingString);

                callInRecordService.addTransferToCsRecordListFromAiCallIn(callInRecordPO.getCallInRecordId(), callInRecordPO.getCustomerPhoneNumber(),
                        CsCallCategoryEnum.CALL_IN_TRANSFER, callInRecordPO, transferToCsList);

            } catch (Exception e) {
                logger.error("设置转接记录", e);
            }
            Map<String, Object> remarkMap = new HashMap<>();
            try {
                remarkMap.put("BOT名", dialogFlow == null ? "" : dialogFlow.getName());
                List<KeyWordDetectionPO> keyWordDetectionPOList;
                if (this.isNlp) {
                    keyWordDetectionPOList = new ArrayList<>();
                } else {
                    if (this.getSemanticManager().getDialogManagerNew() != null) {
                        keyWordDetectionPOList = this.getSemanticManager().getDialogManagerNew().getKeyWordDetectionPOList();
                    } else {
                        keyWordDetectionPOList = null;
                        logger.warn("获取关键字列表错误");
                    }
                }
                if (CollectionUtils.isNotEmpty(keyWordDetectionPOList)) {
                    keyWordDetectionPOList.forEach(keyWordDetectionPO -> {
                        keyWordDetectionPO.setCallRecordId(callInRecordPO.getCallInRecordId());
                    });
                    keyWordDetectionService.insertKeyWordDetectionList(keyWordDetectionPOList);
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn]呼入对话详细匹配过程错误", e);
            }

            // 获得音频时长
            remarkMap.put("通话时长", duration + " 秒");
            remarkMap.put("通话ID", callInRecordPO.getCallInRecordId());
            remarkMap.put("用户意向", intentLevelDetail.get());
            remarkMap.put("号码状态", callInRecordPO.getResultStatus().getDesc());
            remarkMap.put("通话情绪", callInRecordPO.getEmotion());

            //接待统计
            try {
                    logger.info("插入mongoDb--------开始");
                    Query query = callInRecordService.getCallInReceptionStatsQuery(callInRecordPO);
                    Update update = callInRecordService.getCallInReceptionUpdate(callInRecordPO);

                    callStatsMongoService.updateMongoData(MongoCollectionNameCenter.CALL_IN_RECEPTION_STATS, query, update);
                    logger.info("插入mongoDb--------结束");

                    if (this.csStaffId != null && this.csStaffId > 0) {
                        CsStaffInfoPO csStaffInfoPO = csStaffInfoService.selectByKey(this.csStaffId);
                        if (csStaffInfoPO != null) {
                            Long answeredReceptionCount = Objects.equals(callInRecordPO.getSeatStatus(), CallInResultStatusEnum.ANSWERED) ? 1L : 0L;
                            Long effectiveReceptionDuration = Objects.equals(callInRecordPO.getSeatStatus(), CallInResultStatusEnum.ANSWERED) ? this.callInRecordPO.getChatDuration() : 0L;
                            Integer xSecondCount = csStaffStatService.getCallInXSecondCount(this.callInRecordPO.getTenantId(), this.callInRecordPO.getRingDuration());
                            csStaffStatService.staffStatCallIn(csStaffInfoPO.getUserId(), this.callInRecordPO.getTenantId(), csStaffId, answeredReceptionCount, effectiveReceptionDuration, xSecondCount, this.callInRecordPO.getRingDuration());
                        }
                    }
                //删除redis里的排队量
                redisOpsService.delete(RedisKeyCenter.ReceptionQueueCount(callInRecordPO.getCallInReceptionId()));
            } catch (Exception e) {
                logger.error("统计出错", e);
            }

                //执行回调
                Try.run(() -> {
                    IsvInfoPO isvInfo = isvInfoService.findByTenantId(callInRecordPO.getTenantId());
                    if (ZHEJIAO_TEANET.equals(callInRecordPO.getTenantId())) {
                        Map<String, String> properties = new HashMap<>();
                        if (isNlp) {
                            Map<Long, String> syncAttributeMap = nlpAfterChatBO.getSyncAttributeMap();
                            if (syncAttributeMap != null) {
                                Map<Long, CustomerAttributeMetaDataDTO> attributeMap = customerPersonExtraFieldService.getMapByTenantId(callInRecordPO.getTenantId());
                                if (MapUtils.isNotEmpty(syncAttributeMap) && MapUtils.isNotEmpty(attributeMap)) {
                                    // 将id作为key 替换成name作为key
                                    Map<String, String> extraNameInfo = new HashMap<>();
                                    syncAttributeMap.forEach((key, value) -> {
                                        CustomerAttributeMetaDataDTO attributeMetaDataDTO = attributeMap.get(key);
                                        if (attributeMetaDataDTO != null) {
                                            extraNameInfo.put(attributeMetaDataDTO.getAttributeName(), value);
                                        }
                                    });
                                    properties = extraNameInfo;
                                }
                            }
                        } else if (semanticManager != null) {
                            properties = semanticManager.getDialogManagerNew().getAssignmentManager().getVarName2ValueMap();
                        }
                        logger.info("话术收集自定义信息,properties={}", properties);
                        ZheJiaoCallbackHelper.callback(callInRecordPO, getCallInDetailInfoList(), properties);
                    } else {
                        isvCallbackService.callBackCallInRecord(isvInfo, callInRecordPO, getCallInDetailInfoList());
                    }
                }).onFailure(e -> logger.error("[LogHub_Warn]执行呼入的Callback失败", e));

            onCallInRecordInsert();
        } catch (Exception e) {
            logger.error("reset呼入记录失败.", e);
        } finally {
            resetAudioPlayState();

            // 重置callInRecordPO的所有参数重置为null
            callInRecordPO = new CallInRecordPO();
            // 通话详情需要重置null
            callDetailInfoList = Lists.newArrayList();
            // 任务信息重置null
            taskCallResultInfo = new TaskCallResultBO();
            transferToCsList = new ArrayList<>();
            try {
                Long jobId = new Random().nextLong();
                Long taskId = System.currentTimeMillis();
                setJobId(jobId);
                setTaskId(taskId);
                RobotCallTaskPO robotCallTaskInfo = robotCallTask.getRobotTaskCallInfo();
                robotCallTaskInfo.setRobotCallJobId(jobId);
                robotCallTaskInfo.setRobotCallTaskId(taskId);
                this.setIdentifyId(MyRandomStringUtils.getRandomStringByLength(4) + "_JId_" + jobId + "_TId_" + taskId);
                // 本次通话的唯一key，主要用于日志打印，信令中Call-ID的识别
                String mdcKey = "MDC_LOG_ID";
                String preMdcStr = MDC.get(mdcKey);
                if (StringUtils.isNotEmpty(preMdcStr) && !preMdcStr.contains("_TId_")) {
                    MDC.put(mdcKey, preMdcStr + "-" + this.getIdentifyId());
                } else {
                    MDC.put(mdcKey, this.getIdentifyId());
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn]呼入重置部署信息错误", e);
            }
        }

    }

    @Override
    public void init(boolean sip200Success) {
        super.init(sip200Success);
    }

    /**
     * 接受用户的语音
     *
     * @param buffer
     * @param offset
     * @param length
     * @return
     */
    @Override
    public int writeData(byte[] buffer, int offset, int length) {
        // 如果还没建立200链接，不进行语音识别
        if (!isSip200Success() || getStartAsrTimestamp() <= 0 || !incomingInited) {
            if (isSip200Success()) {
                asrWaitingSoundBytesOffset.addAndGet(length);
            }
            return length;
        }

        try {
            robot.send(this, buffer, offset, length, maxSentenceSilence);
            //第二步：发送给本地vadProcessor
            short[] shorts = AudioHandleUtils.getShorts(buffer, offset, length);
            vadProcessor.update(shorts);
        } catch (Exception e) {
            logger.error("TaskId=" + getTaskId() + " : writeDate error", e);
        }
        return length;
    }

    @Override
    public void overTimeHangup(String reason) {
        super.overTimeHangup(reason);
        if (this.isNlp) {
            nlpSemanticManager.setFinish(true);
        }
    }

    /**
     * 获取用户过来的ivr信息
     *
     * @param b 语音buffer
     */
    @Override
    public void writeIvrData(byte b) {
        logger.info("用户按键输入信号为：{}", b);
        if (nlpSemanticManager != null) {
            nlpSemanticManager.whenUserClick(DTMFbyte2char(b));
        }
    }

    /**
     * DecodeDTMF信息转换成键盘按键
     *
     * @param b
     * @return
     */
    private char DTMFbyte2char(byte b) {
        char ret;
        if (b == 10) {
            ret = '*';
        } else if (b == 11) {
            ret = '#';
        } else if (b >= 0 && b <= 9) {
            ret = (char) (b + 48);
        } else {
            ret = (char) (b + 53);
        }
        return ret;
    }

    /**
     * 向用户播放录音
     *
     * @return
     */
    @Override
    public byte[] readData() {
        byte[] readData = readDataInner();
        return readData;
    }

    protected byte[] readDataInner() {
        // 向用户播放录音
        byte[] buffer = new byte[Constants.PACKET_BUFFER_SIZE];
        try {
            if (!incomingInited) {
                // 还没有坐席接入， 播放等待音
                playWaitingSound(buffer);
                return buffer;
            }

            boolean inerruptable;
            boolean needVad = false;
            if (this.isNlp) {
                inerruptable = nlpSemanticManager.isInterrupt();
            } else {
                Double aiProcess = getCurrentFilePlayProcess();
                inerruptable = semanticManager.getCurrentChatEventInterrupt(aiProcess);
            }
            // VAD 检测到用户说话，并且当前对话事件允许用户打断，播放空白录音
            if (vadProceeding && inerruptable && perAiPlaying()) {
                this.vadPlayBlank = true;
                return emptyBuffer;
            } else if (!this.asrEnd && this.vadPlayBlank) {
                // VAD已经结束, asr尚未结束播放空白录音
                return emptyBuffer;
            }
            // 如果是earlyMedia阶段，或者user breaking中，播放空白声音
            // 此处也支持直接播放打断的话术
            if (!isSip200Success() || pauseCurrPlayAudio || isFinished() && needVad) {
                return emptyBuffer;
            }

            if (CollectionUtils.isNotEmpty(audioInputStreamList)) {
                //实时合成,返回
                int length = calculateAudioBufferSize(audioInputStreamList);
                length = length < Constants.PACKET_BUFFER_SIZE ? length : Constants.PACKET_BUFFER_SIZE;
                //需要严格保证读出来的个数是2的整数倍，采样位数是16bit
                //出现奇数的原因就是读的过程中，刚好读到奇数个时，audioInputStreamList 被重置为空。应该只有这个原因
                int index = 0;
                for (; index < length; index+=2) {
                    Byte b = readByteFromQueue(audioInputStreamList);
                    Byte b1 = readByteFromQueue(audioInputStreamList);
                    if (b != null && b1 != null && index + 1 < length) {
                        buffer[index] = b;
                        buffer[index + 1] = b1;
                    } else {
                        break;
                    }
                }
                if (CollectionUtils.isEmpty(audioInputStreamList)) {
                    currPlayFileName = null;
                    currPlayInputStream = null;
                    audioInputStreamList = null;
                    semanticManager.aiSayFinish();
                    perAiPlayEndTime = System.currentTimeMillis();
                } else {
                    if (currPlayFileName != null) {
                        filePlayProgressRecorder.addOrUpdateFilePlayProgress(currPlayFileName, buffer.length);
                    } else {
                        logger.warn("currPlayFileName is null");
                    }
                    // 更新AI最后一次的说话事件
                    lastAiPlayEndTime = System.currentTimeMillis();
                }
                return buffer;
            }

            if (currPlayInputStream == null) {
                // 首次校验说话超时一定是在AI第一句话说完的基础上，并且用户没有挂机
                boolean isLastAiPlayEndTime = lastAiPlayEndTime != 0;
                boolean callIsRunning = !isFinished();
                boolean aiPlayNotFinished = !isAiPlaying();
                boolean isUserSayTimeout = isUserSayTimeout();

                if (!vadProceeding && isLastAiPlayEndTime && callIsRunning && aiPlayNotFinished && isUserSayTimeout) {
                    if (this.isNlp) {
                        boolean aiWait = nlpSemanticManager.isAiWait();
                        boolean aiWaitConfig = nlpSemanticManager.isAiWaitConfig();
                        if (aiWait) {
                            nlpSemanticManager.isWaitBack();
                        } else if (aiWaitConfig) {
                            nlpSemanticManager.aiWaitConfigBack();
                        } else {
                            nlpSemanticManager.userSilence();
                        }
                    } else {
                        semanticManager.userSilence();
                    }
//                    }
                } else {
                    boolean isDtmfTimeout = isDtmfTimeout();
                    if (!vadProceeding && isLastAiPlayEndTime && callIsRunning && aiPlayNotFinished && isDtmfTimeout) {
                        if (semanticManager != null) {
                            semanticManager.userDtmfSilence();
                        } else if (nlpSemanticManager != null) {
                            nlpSemanticManager.userDtmfSilence();
                        }
                    }
                }

                return buffer;
            }

            // 第一次播放文件音频
            long position = currPlayInputStream.getChannel().position();
            if (position > 0 && position <= WAV_TO_PCM_HEAD_LEN) {
                logger.debug("first play audio, currPlayFileName={}", currPlayFileName);
            }

            if (currPlayInputStream.read(buffer) < 0) {
                currPlayInputStream.close();
                currPlayFileName = null;
                currPlayInputStream = null;
                if (this.isNlp) {
                    nlpSemanticManager.aiSayFinish();
                } else {
                    semanticManager.aiSayFinish();
                }
//                }
                perAiPlayEndTime = System.currentTimeMillis();
            } else {
                filePlayProgressRecorder.addOrUpdateFilePlayProgress(currPlayFileName, buffer.length);
                // 更新AI最后一次的说话事件
                lastAiPlayEndTime = System.currentTimeMillis();
            }
        } catch (Exception e) {
            logger.error("catch io exception : " + e.getMessage(), e);
        }
        return buffer;
    }

    private boolean isDtmfTimeout() {
        if (dtmfTimeoutMillis != null) {
            // 当前正在进行按键收集
            if (firstPressMillis == null) {
                // 用户还没有按键, 从ai上一句话说完开始等待 语音超时时间
                return lastAiPlayEndTime + getUserSayTimeOut(0L) < System.currentTimeMillis();
            } else {
                // 用户已按键, 从第一次按键开始等待 按键超时时间
                return firstPressMillis + dtmfTimeoutMillis < System.currentTimeMillis();
            }
        } else {
            return false;
        }
    }

    public Double getCurrentFilePlayProcess() {
        return getFilePlayProgressRecorder().getFilePlayProgress(getCurrPlayFileName());
    }


    private int getUserSayTimeOut(Long tenantId) {
        Integer userSilenceMillisecond = 7000;
        if (this.isNlp) {
            boolean aiWait = nlpSemanticManager.isAiWait();
            boolean aiWaitConfig = nlpSemanticManager.isAiWaitConfig();
            if (aiWait) {
                userSilenceMillisecond = this.waitTime;
            } else {
                userSilenceMillisecond = nlpSemanticManager.getUserSilenceMillisecond();
            }
        } else {
            userSilenceMillisecond = semanticManager.getUserSilenceMillisecond();
            boolean used = semanticManager.getDialogFlowInfoBO().getAiWaitKnownKnowledge().isUsed();
            boolean aiWait = semanticManager.getDialogManagerNew().isAiWait();
            boolean aiWait2 = semanticManager.getDialogManagerNew().isAiWait2();
            if (used && aiWait && aiWait2) {
                userSilenceMillisecond = semanticManager.getDialogFlowInfoBO().getAiWaitKnownKnowledge().getAiWaitPreKnownKnowledge().getAiWaitTime() * 1000;
            } else if (CURR_ENV.isAliyun() && 4402L == tenantId) {
                userSilenceMillisecond = ApplicationConfig.USER_SAY_TIMEOUT_MILLISECOND_SPECIAL;
            } else if (CURR_ENV.isAliyun() && 3055L == tenantId) {
                userSilenceMillisecond = ApplicationConfig.USER_SAY_TIMEOUT_MILLISECOND_CAITONG;
            }
        }
        return userSilenceMillisecond;
    }

    private void playWaitingSound(byte[] buffer) throws Exception {
        int headLen = 44 + 320;
        if (waitingSoundInputStream == null) {
            waitingSoundInputStream = new FileInputStream(waitingFile);
            waitingSoundInputStream.read(new byte[headLen], 0, headLen);
        }
        if (waitingSoundInputStream.read(buffer) < 0) {
            waitingSoundInputStream.close();
            waitingSoundInputStream = new FileInputStream(waitingFile);
            waitingSoundInputStream.read(new byte[headLen], 0, headLen);
            waitingSoundInputStream.read(buffer);
        }
    }

    @Override
    public void onUserSay(UserSayBO userSayBO) {
        CallInDetailBO callDetail = new CallInDetailBO(userSayBO);
        if (StringUtils.isNotEmpty(callDetail.getText()) && callDetail.getText().length() > 250) {
            // 大于250个字截断
            callDetail.setText(callDetail.getText().substring(0, 250));
        }
        callDetailInfoList.add(callDetail);
        RunTimeRobotCallTaskBO taskCallInfo = robotCallTask.getRobotTaskCallInfo();

        try {
            if (transferFlag) {
                String key = RedisKeyCenter.getCallJobCallDetailRedisKey(getCallInRecordingString());
                redisOpsService.getRedisTemplate().opsForList().rightPush(key, callDetail);
                redisOpsService.getRedisTemplate().expire(key, 10, TimeUnit.MINUTES);
            }
        } catch (Exception e) {

        }
    }

    @Override
    public void onAiSay(AiSayBO aiSayBO) {
        CallInDetailBO callDetail = new CallInDetailBO(aiSayBO);
        callDetailInfoList.add(callDetail);
        RunTimeRobotCallTaskBO taskCallInfo = robotCallTask.getRobotTaskCallInfo();
        try {
            if (transferFlag) {
                String key = RedisKeyCenter.getCallJobCallDetailRedisKey(getCallInRecordingString());
                redisOpsService.getRedisTemplate().opsForList().rightPush(key, callDetail);
                redisOpsService.getRedisTemplate().expire(key, 10, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("onAiSay error", e);
        }
    }

    @Override
    public void calleePickup(SipResponse sipResponse) {
        super.calleePickup(sipResponse);

        if (robot != null) {
            Try.run(() -> {
                if (this.isNlp) {
                    nlpSemanticManager.enter();
                } else {
                    semanticManager.enter();
                }
            }).onFailure((e) -> logger.error("[LogHub_Warn]调用enter错误", e));
        }
    }

    @Override
    public void onUserAgentFinish() {
        super.onUserAgentFinish();

        // 同步更新任务状态，防止Job中查询所有Task是否执行完毕出问题
        syncDoAfterTaskFinished();

        // Task执行完成后需要执行的异步任务
        asyncDoTaskAnalyzeMission();
    }

    /**
     * 同步更新任务结果状态，防止Job中查询所有Task是否执行完毕出问题
     */
    public void syncDoAfterTaskFinished() {
        try {
            RobotCallTaskPO robotTaskCallInfo = robotCallTask.getRobotTaskCallInfo();

            // 更新电话状态一定要在同步逻辑里面，因为任务中回去判断是否所有的task都执行完毕了
            robotTaskCallInfo.setStatus(RobotCallTaskStatusEnum.COMPLETED);

            // 如果是网关手机号类型，统一休眠5秒钟，等待网关资源释放
            if (PhoneTypeEnum.MOBILE.equals(robotCallTask.getRobotCallPhoneNumberWithSipInfo().getPhoneType())) {
                int sleep = 5000 - getHangUpWaitTime();
                if (sleep > 0) {
                    MyThreadUtils.sleepMilliseconds(sleep);
                }
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]同步执行Task更新任务失败, TaskId=" + getTaskId(), e);
        }
    }

    /**
     * Task执行完成后需要执行的异步任务
     */
    protected void asyncDoTaskAnalyzeMission() {
        // 在有人工介入的时候，异步会导致人工介入的声音不能+上，而没有录音
        doAfterTaskAnalyzeMission();
    }

    /**
     * 在任务的异步任务完成之后再执行的方法
     */
    protected void doAfterTaskAnalyzeMission() {
    }

    private boolean isAiPlaying() {
        return (lastAiPlayBeginTime >= lastAiPlayEndTime);
    }

    /**
     * 每次播放的音频文件是否正在播放中
     *
     * @return boolean
     */
    private boolean perAiPlaying() {
        return (perAiPlayStartTime >= perAiPlayEndTime);
    }

    private boolean isUserSayTimeout() {
        int ustm;
        if (this.isNlp) {
            boolean aiWait = nlpSemanticManager.isAiWait();
            if (aiWait) {
                ustm = this.waitTime;
            } else {
                ustm = nlpSemanticManager.getUserSilenceMillisecond();
            }
        } else {
            ustm = semanticManager.getUserSilenceMillisecond();
            boolean used = semanticManager.getDialogFlowInfoBO().getAiWaitKnownKnowledge().isUsed();
            boolean aiWait = semanticManager.getDialogManagerNew().isAiWait();
            boolean aiWait2 = semanticManager.getDialogManagerNew().isAiWait2();
            if (used && aiWait && aiWait2) {
                ustm = semanticManager.getDialogFlowInfoBO().getAiWaitKnownKnowledge().getAiWaitPreKnownKnowledge().getAiWaitTime() * 1000;
            }
        }
        return (System.currentTimeMillis() - Math.max(lastAiPlayEndTime, lastUserSayEndTime)) > ustm;
    }

    private NlpBeforeChatBO buildRobotConfig(Long tenantId, Long nlpRobotId, Map<String, String> variableMap) {
        NlpBeforeChatParamsBO paramsBO = new NlpBeforeChatParamsBO(tenantId, nlpRobotId, variableMap, true, tenantPO.getPhoneNumber());
        return NlpHelper.beforeChat(paramsBO);
    }

    /**
     * 呼入详细实现
     *
     * @param sipRequest
     * @param provResponse
     */
    @Override
    public void incomingCall(SipRequest sipRequest, SipResponse provResponse) {
        super.incomingCall(sipRequest, provResponse);

        UserAgent userAgent = getUserAgent();
        net.sourceforge.peers.sip.transactionuser.DialogManager peersDialogManager = userAgent.getDialogManager();
        String callId = Utils.getMessageCallId(sipRequest);
        Dialog dialog = peersDialogManager.getDialog(callId);
        userAgent.acceptCall(sipRequest, dialog);

        AsyncAsrInitExecutorHelper.execute("incomingCallInitOnSip200", () -> {
            long methodStartTime = System.currentTimeMillis();
            String dialogFlowId = null;
            String tenantIdStr = null;
            String phoneNumberId = null;
            String account = null;
            String customerPersonId = null;
            String staffGroupId = null;
            String staffId = null;
            String transferCsStaffGroupId = null;
            String intentLevelTag = null;
            String seatTypeName = null;
            String userId = null;
            Long queueDuration = null;
            this.csStaffGroupId = 0L;
            this.csStaffId = 0L;
            this.transferStartTime = null;
            this.transferToCsList = new ArrayList<>();
            this.transferFlag = false;
            this.csSeatQueue = new CsSeatQueueDTO();
            this.lastTransferGroupId = null;
            this.setLastUserSayEndTime(System.currentTimeMillis());
            setStartAsrTimestamp(System.currentTimeMillis());

            SipHeaders sipHeaders = sipRequest.getSipHeaders();
            SipHeaderFieldValue callInRecordingFiled = sipHeaders.get(new SipHeaderFieldName(EslConstant.CALL_IN_RECORDING));
            callInRecordingString = Objects.nonNull(callInRecordingFiled) ? callInRecordingFiled.getValue() : null;
            try {
                // 判断是否是ivr导航
                SipHeaderFieldValue cipidValue = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_CALL_IN_RECEPTION_ID));
                callInReceptionId = Objects.isNull(cipidValue) ? null : Long.valueOf(cipidValue.getValue());
                String crdValue = getSipHeader(callInRecordingString, EslConstant.X_CALL_QUEUE_DURATION);
                queueDuration = Objects.isNull(crdValue) ? null : Long.valueOf(crdValue);
                String tenantIdField = getSipHeader(callInRecordingString, EslConstant.X_TENANT_ID);
                tenantIdStr = Objects.isNull(tenantIdField) ? null : tenantIdField;
                SipHeaderFieldValue lineIdValue = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_AI_CALL_IN_LINE_ID));
                lineId = Objects.isNull(lineIdValue) ? null : Long.valueOf(lineIdValue.getValue());
                SipHeaderFieldValue customerPhoneNumberField = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_CUSTOMER_PHONE_NUMBER));
                customerPhoneNumber = Objects.isNull(customerPhoneNumberField) ? null : customerPhoneNumberField.getValue();
                SipHeaderFieldValue accountField = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_REAL_DST_NUMBER));
                account = Objects.isNull(accountField) ? null : accountField.getValue();
                String fsIp = getSipHeader(callInRecordingString, EslConstant.X_AI_FS_IP);
                String fsHost = getSipHeader(callInRecordingString, EslConstant.X_AI_FS_HOST);
                if(this.freeswitchInfo == null) {
                    this.freeswitchInfo = new FreeswitchInfoPO();
                    this.freeswitchInfo.setName(fsHost);
                    this.freeswitchInfo.setPort(5070);
                    this.freeswitchInfo.setAudioDownloadPort(80);
                    this.freeswitchInfo.setHost(fsIp);
                    this.freeswitchInfo.setFreeswitchInfoId(-1L); // TODO
                }
                String customerPersonIdField = getSipHeader(callInRecordingString, EslConstant.X_CUSTOMER_PERSONER_ID);
                customerPersonId = Objects.isNull(customerPersonIdField) ? "-1" : customerPersonIdField;
                String staffGroupIdField = getSipHeader(callInRecordingString, EslConstant.X_STAFF_GROUP_ID);
                staffGroupId = Objects.isNull(staffGroupIdField) ? null : staffGroupIdField;
                SipHeaderFieldValue staffIdFeild = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_STAFF_ID));
                staffId = Objects.isNull(staffIdFeild) ? null : staffIdFeild.getValue();
                this.csStaffId = Objects.isNull(staffIdFeild) ? null : Long.valueOf(staffId);
                SipHeaderFieldValue xEidField = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_EID));
                this.xEid = Objects.isNull(xEidField) ? null : xEidField.getValue();
                SipHeaderFieldValue seatTypeNameField = sipHeaders.get(new SipHeaderFieldName(EslConstant.X_SEAT_TYPE_NAME));
                seatTypeName = Objects.isNull(seatTypeNameField) ? null : seatTypeNameField.getValue();
                String userIdField = getSipHeader(callInRecordingString, EslConstant.X_CALL_IN_USER_ID);
                userId = Objects.isNull(userIdField) ? null : userIdField;
                String intentLevelTagFiled = getSipHeader(callInRecordingString, EslConstant.X_INTENT_LEVEL_TAG_ID);
                intentLevelTag = Objects.isNull(intentLevelTagFiled) ? null : intentLevelTagFiled;
                // 人工介入坐席组id
                String transferCsStaffGroupIdField = getSipHeader(callInRecordingString, EslConstant.X_TRANSFER_CS_STAFF_GROUP_ID);
                transferCsStaffGroupId = Objects.isNull(transferCsStaffGroupIdField) ? null : transferCsStaffGroupIdField;
            } catch (Exception e) {
                logger.error("呼入incomingCall获取的esl参数处理错误", e);
            }
            logger.debug("tenantIdStr=[{}]", tenantIdStr);
            Long tenantId = tenantIdStr == null ? null : Long.valueOf(tenantIdStr);
            if (tenantId != null) {
                tenantPO = tenantService.selectByKey(tenantId);
            }
            try {
                logger.info("呼入为普通呼入接待");
                String dialogFlowIdFiled = getSipHeader(callInRecordingString, EslConstant.X_DIALOGFLOW_ID);
                dialogFlowId = Objects.isNull(dialogFlowIdFiled) ? null : dialogFlowIdFiled;
                robotCallTask.getRobotTaskCallInfo().setDialogFlowId(Long.valueOf(dialogFlowId));
                logger.info("当前户数的反应灵敏度={}", maxSentenceSilence);
                // 必须在robot.startAsr之前设置，不然在robot中会的appkey会null
                Long robotCallTaskId = robotCallTask.getRobotCallTaskId();
                DialogFlowInfoPO dialogFlowInfoPO = dialogFlowService.getDialogFlow(Long.valueOf(dialogFlowId));
                this.isNlp = BotTypeEnum.PROFESSIONAL.equals(dialogFlowInfoPO.getBotType());
                Tuple2<DialogFlow, Map<TextAudioContentPO, RuntimeTextAudioContent>> tuple = DialogFlowHandler.resolveDialogFlow(robotCallTask, false);
                this.dialogFlow = tuple._1();

                // 处理 ASR 配置
                // 为了兼容原有的逻辑，先在此进行处理，在下面通过判断值是否为空，来走原有的逻辑
                try {
                    AsrDialogFlowRelationPO relationPO = asrDialogFlowRelationService.getByTenantId(tenantId, dialogFlow.getId());
                    if (relationPO != null) {
                        AsrApplicationPO asrApplicationPO = asrApplicationService.get(relationPO.getAsrApplicationId());
                        this.asrProvider = asrApplicationPO.getAsrProvider();
                        this.yiwiseAsrParam = new YiwiseAsrParam();
                        this.yiwiseAsrParam.setEnablePunctuation(true);
                        this.yiwiseAsrParam.setEnableIntermediateResult(false);
                        if (StringUtils.isNotEmpty(asrApplicationPO.getAsrHotWordId())) {
                            AsrHotWordPO hotWordPO = asrHotWordService.get(asrApplicationPO.getAsrHotWordId());
                            this.yiwiseAsrParam.setHotWordId(hotWordPO.getLongHotWordId());
                        }
                        if (AsrProviderEnum.ALI.equals(asrApplicationPO.getAsrProvider())) {
                            this.dialogFlowAsrModelAppkey = asrApplicationPO.getAppKey();
                        }
                    }
                } catch (Exception e) {
                    logger.error("获取ASR配置错误", e);
                }

                if (this.isNlp) {
                    NlpBeforeChatBO nlpBeforeChatBO = null;
                    NlpAudioManager nlpAudioManager = null;

                    try {
                        //获取话术机器人信息
                        Map<String, String> properties = new HashMap<>();
                        properties.put("phoneNumber", customerPhoneNumber);
                        nlpBeforeChatBO = buildRobotConfig(tenantId, dialogFlowInfoPO.getNlpRobotId(), properties);
                        // 默认值 避免报错太多
                        if (nlpBeforeChatBO == null) {
                            logger.error("[LogHub_Warn] nlpBeforeChatBO 获取失败");
                            nlpBeforeChatBO = new NlpBeforeChatBO();
                            NlpRobotConfigBO configBO = new NlpRobotConfigBO();
                            configBO.setRobotId(-1L);
                            configBO.setTimeoutSeconds(0);
                            configBO.setWaitTime(0);
                            nlpBeforeChatBO.setConfig(configBO);
                        }
                        this.timeoutSeconds = nlpBeforeChatBO.getConfig().getTimeoutSeconds() * 1000;
                        this.waitTime = nlpBeforeChatBO.getConfig().getWaitTime() * 1000;
                        //获取话术机器人信息
                        nlpAudioManager = new NlpAudioManager(dialogFlowInfoPO, dialogFlowInfoPO.getNlpRobotId(), nlpBeforeChatBO.getPreAudioTextList());
                        nlpAudioManager.preDialog();
                    } catch (Exception e) {
                        logger.error("[LogHub_Warn] 呼入前置数据获取出现错误", e);
                    }

                    this.dialogFlow.setVadGateMute(dialogFlowInfoPO.getVadGateMute());

                    // 当配置的ASR为空时，才使用指定的appKey
                    if (CURR_ENV.isAliyun() && tenantId == 1159L) {
                        this.dialogFlowAsrModelAppkey = "UK9XZFm9d008xaP2";
                    } else if (Objects.isNull(dialogFlowAsrModelAppkey)) {
                        this.dialogFlowAsrModelAppkey = "GFxbNCyRzA59OZNq";
                        // 如果通过数据库为nlp的话术设置的asr模型, 则设置使用指定的asr模型
                        if (Objects.nonNull(dialogFlow.getDialogFlowAsrModelId())
                                && StringUtils.isNotBlank(dialogFlow.getDialogFlowAsrModelAppkey())) {
                            this.dialogFlowAsrModelAppkey = dialogFlow.getDialogFlowAsrModelAppkey();
                        }
                    }
                    logger.debug("当前使用的asrkey={}", this.dialogFlowAsrModelAppkey);
                    if (this.yiwiseAsrParam == null) {
                        this.yiwiseAsrParam = new YiwiseAsrParam();
                        this.yiwiseAsrParam.setHotWordId(dialogFlowInfoPO.getDialogFlowAsrModelId());
                        this.yiwiseAsrParam.setEnableIntermediateResult(false);
                        this.yiwiseAsrParam.setEnablePunctuation(true);
                    }
                    this.maxSentenceSilence = dialogFlowInfoPO.getMaxSentenceSilence() == null ? 500 : dialogFlowInfoPO.getMaxSentenceSilence();

                    nlpBeforeChatBO.setDialogFlowId(Long.valueOf(dialogFlowId));
                    nlpBeforeChatBO.setCallerPhoneNumber(customerPhoneNumber);

                    this.nlpSemanticManager = new NlpLogDecorateDialogManager(this.dialogFlow, nlpBeforeChatBO, this, robotCallTaskId, nlpAudioManager, this.tenantPO, true);
                } else {
                    this.maxSentenceSilence = dialogFlow.getMaxSentenceSilence();
                    // 必须在robot.startAsr之前设置，不然在robot中会的appkey会null
                    if (this.dialogFlowAsrModelAppkey == null) {
                        this.dialogFlowAsrModelAppkey = dialogFlow.getDialogFlowAsrModelAppkey();
                    }
                    AsrContext asrContext = AsrContextFactory.createContext(dialogFlow.getId(), RobotSnapshotUsageTargetEnum.CALL_OUT);
                    this.semanticManager = new LogDecorateDialogManager(this, dialogFlow, robotCallTask.getRobotTaskCallInfo().getRunTimeProperties(), null, asrContext);
                    if (this.dialogFlowAsrModelAppkey == null) {
                        this.dialogFlowAsrModelAppkey = asrContext.getAsrAppkey();
                    }
                }
                if (null != this.dialogFlow.getVadGateMute() && this.dialogFlow.getVadGateMute() >= 0) {
                    // 存储的是差值，是与最低值的差值，最低值是1500，避免别人知道真实数值
                    vadProcessor.setMutePointValueGate(1500 + this.dialogFlow.getVadGateMute());
                }
    //            }

                this.dialogFlowIdStr = dialogFlowId;
                logger.debug("dialogFlowId = [{}], customerPhoneNumber = [{}]", dialogFlowId, customerPhoneNumber);
                isHuaweiCallInDemo = Objects.equals(dialogFlowIdStr, Long.toString(ApplicationConstant.HUAWEI_CALL_IN_DIALOGFLOW_ID));
                if (isHuaweiCallInDemo) {
                    logger.info("展示话术, 通知web端推送");
                    final String finalPhone = customerPhoneNumber;
                    DynamicDataSourceApplicationExecutorHolder.execute("通知web端推送", () -> {
                        try {
                            webSocketOverMQService.sendCallInBeginNotice(finalPhone);
                        } catch (Exception e) {
                            logger.error("通知web端推送失败", e);
                        }
                    });
                }
                robot = AsrHelper.getCallInRobot(tenantId);
                robot.startAsr(this, -1L, maxSentenceSilence);
                logger.info("asr 启动完成，TaskId={}.", getTaskId());
            } catch (Exception e) {
                logger.error("呼入incomingCall初始化错误", e);
            }

            // 获取被叫
            AccountVO accountVO = new AccountVO();
            try {
                this.customerPersonId = customerPersonId == null ? -1L : Long.valueOf(customerPersonId);
            } catch (Exception e) {
                logger.error("数字转换错误", e);
                this.customerPersonId = -1L;
            }
            accountVO.setMainPhoneNumber(customerPhoneNumber);
            robotCallTask.getRobotCallPhoneNumberWithSipInfo().setSipAccount(customerPhoneNumber);
            robotCallTask.getRobotTaskCallInfo().setCalledPhoneNumber(account);
            robotCallTask.getRobotTaskCallInfo().setTenantId(tenantId);
            robotCallTask.setAccountInfoOpt(Optional.of(accountVO));
            robotCallTask.setCsStaffGroupId(transferCsStaffGroupId == null ? null : Long.valueOf(transferCsStaffGroupId));
            callInRecordPO.setCallInReceptionId(this.callInReceptionId);
            callInRecordPO.setTenantId(tenantId);
            if (tenantPO != null) {
                tenantPO = tenantService.selectByKey(tenantId);
                callInRecordPO.setDistributorId(tenantPO.getDistributorId());
            }
            callInRecordPO.setDialogFlowId(dialogFlowId == null ? null : Long.valueOf(dialogFlowId));
            callInRecordPO.setPhoneNumberId(phoneNumberId == null ? null : Long.valueOf(phoneNumberId));
            callInRecordPO.setCustomerPhoneNumber(customerPhoneNumber);
            callInRecordPO.setCustomerPersonId(this.customerPersonId);
            callInRecordPO.setStaffGroupId(staffGroupId == null ? null : Long.valueOf(staffGroupId));
            callInRecordPO.setStaffId(staffId == null ? null : Long.valueOf(staffId));
            callInRecordPO.setIntentLevelTagId(intentLevelTag == null ? null : Long.valueOf(intentLevelTag));
            callInRecordPO.setSeatType(StaffGroupTypeEnum.valueOf(seatTypeName));
            callInRecordPO.setStartTime(LocalDateTime.now());
            callInRecordPO.setCreateUserId(userId == null ? null : Long.valueOf(userId));
            callInRecordPO.setUpdateUserId(userId == null ? null : Long.valueOf(userId));
            PhoneHomeLocationBO locationOrNewByPhoneNumber = phoneLocationService.getLocationOrNewByPhoneNumber(customerPhoneNumber);
            callInRecordPO.setPhoneLocationId(Objects.isNull(locationOrNewByPhoneNumber) ? null : locationOrNewByPhoneNumber.getPhoneLocationId());
            callInRecordPO.setHangupBy(isCustomerHangup() ? CallJobHangupEnum.REMOTE_HANGUP : CallJobHangupEnum.INITIAL_HANGUP);
            callInRecordPO.setQueueDuration(queueDuration);

            try {
                if (callInReceptionId != null) {
                    redisOpsService.incrementKey(RedisKeyCenter.ReceptionCallCount(callInReceptionId), 1);
                }
                if (this.isNlp) {
                    setLastUserSayEndTime(System.currentTimeMillis());
                    nlpSemanticManager.enter();
                } else {
                    semanticManager.enter();
                }
            } catch (Exception e) {
                logger.error("调用enter错误", e);
            }
            // 此处使用异步方法，由于媒体传输需要时间，先回200OK，然后再启动相关初始化，可以改善开场白丢字问题
            long methodDuration = System.currentTimeMillis() - methodStartTime;
            if(methodDuration < INCOMING_CALL_WAIT_MS) {
                long sleepTime = INCOMING_CALL_WAIT_MS - methodDuration;
                logger.info("incoming call wait {} ms.", sleepTime);
                MyThreadUtils.sleepMilliseconds(sleepTime);
            }
            incomingInited = true;
        });
    }

    public String getSipHeader(String identifyId, String key) {
        Object res = redisOpsService.getRedisTemplate().opsForHash().get(RedisKeyCenter.getCallInHeaderEslKey(identifyId), key);
        return res == null ? null : String.valueOf(res);
    }

    /**
     * 设置新的播放语音流
     *
     * @param fileName 重置新的播放文件
     */
    private void tryNewFile(String fileName) {
        try {
            currPlayFileName = fileName;

            if (StringUtils.isNotBlank(fileName)) {
                if (currPlayInputStream != null) {
                    currPlayInputStream.close();
                    IOUtils.closeQuietly(currPlayInputStream);
                }
                pauseCurrPlayAudio = false;
                currPlayInputStream = new FileInputStream(CommonApplicationConstant.LOCAL_TMP_DIR + currPlayFileName);
                filePlayProgressRecorder.setFileSize(fileName, currPlayInputStream.available());

                lastAiPlayEndTime = 0L;
                lastAiPlayBeginTime = System.currentTimeMillis();
                perAiPlayStartTime = lastAiPlayBeginTime;
                perAiPlayEndTime = 0L;
                if (StringUtils.endsWith(fileName, ".wav")) {
                    currPlayInputStream.read(new byte[WAV_TO_PCM_HEAD_LEN], 0, WAV_TO_PCM_HEAD_LEN);
                }
            } else {
                currPlayInputStream = null;
                lastAiPlayEndTime = System.currentTimeMillis();
            }

            logger.debug("CurrPlayFileName============================{}", currPlayFileName);

            filePlayProgressRecorder.resetFilePlay(currPlayFileName);
        } catch (IOException e) {
            logger.error("[LogHub_Warn]获取语音流出错, TaskId={}, 文件地址={}.", getTaskId(), fileName, e);
        }
    }

    public CallInRobot getRobot() {
        return robot;
    }

    @Override
    public void pauseAudio() {
        logger.debug("nlu pauseAudio");
        pauseCurrPlayAudio = true;
    }

    @Override
    public void playNewAudio(String audioUrl) {
        tryNewFile(audioUrl);
    }

    @Override
    public void playAudioWithTts(String text, String originAnswerTemplate, List<String> textOssUrl, Map<String, String> customVariablesProperties, Map<String, Object> ttsParam) {
        try {
            currPlayFileName = text;

            if (StringUtils.isNotBlank(text)) {
                if (currPlayInputStream != null) {
                    currPlayInputStream.close();
                    IOUtils.closeQuietly(currPlayInputStream);
                }
                Integer synthesizerIndex = stopTts.incrementAndGet();

                pauseCurrPlayAudio = false;
                currPlayInputStream = null;
                audioInputStreamList = new LinkedBlockingQueue<>();
                SpeachSynthesizerHelper helper = new SpeachSynthesizerHelper(synthesizerIndex);
                helper.doSynthesizer(0L, filePlayProgressRecorder, audioInputStreamList, dialogFlow.getId(), dialogFlow.getName(), text, text, text, textOssUrl, customVariablesProperties, ttsParam, dialogFlow.getVoiceType(), stopTts);
                lastAiPlayEndTime = 0L;
                lastAiPlayBeginTime = System.currentTimeMillis();
                perAiPlayStartTime = lastAiPlayBeginTime;
                perAiPlayEndTime = 0L;
            } else {
                currPlayInputStream = null;
                lastAiPlayEndTime = System.currentTimeMillis();
            }

            logger.debug("CurrPlayText============================{}", currPlayFileName);

            filePlayProgressRecorder.resetFilePlay(currPlayFileName);
        } catch (IOException e) {
            logger.error("[LogHub_Warn]获取语音流出错, TaskId={}, 文本={}.", getTaskId(), text, e);
        }
    }


    private Byte readByteFromQueue(LinkedBlockingQueue<AudioByteBuffer> queue) {
        AudioByteBuffer audioStreamBuffer = queue.peek();
        if (audioStreamBuffer == null) {
            return null;
        }
        Byte b = audioStreamBuffer.buffer[audioStreamBuffer.nextReadOffset++];
        if (audioStreamBuffer.nextReadOffset >= audioStreamBuffer.buffer.length) {
            queue.poll();
        }
        return b;
    }

    private int calculateAudioBufferSize(LinkedBlockingQueue<AudioByteBuffer> queue) {
        if (CollectionUtils.isEmpty(queue)) {
            return 0;
        }
        int size = 0;
        for (AudioByteBuffer audioStreamBuffer : queue) {
            size += (audioStreamBuffer.size - audioStreamBuffer.nextReadOffset);
        }
        return size;
    }


    // 重置录音播放相关状态
    // 呼入接待时, 这个类是复用的, 所以在每次进线的时候, 都需要重置录音播放相关的状态
    // 目前是看到在一通通话开始前, 会短暂的播放上上一轮通话中未播放完的录音内容
    private void resetAudioPlayState() {
        logger.info("通话结束重置之前的录音播放状态");
        currPlayFileName = null;
        currPlayInputStream = null;
        audioInputStreamList = null;
    }

    @Override
    public void waitForUserSay(Integer timeoutMillisecond) {
        logger.debug("nlu waitForUserSay");
    }

    @Override
    public void resume() {
        logger.debug("nlu resume");
        pauseCurrPlayAudio = false;
    }

    @Override
    public void hangup() {
        logger.debug("nlu hangup");
        initiativeHangup();
    }

    @Override
    public void hangup(long delay) {
        // 呼入暂时不支持延迟挂机
        hangup();
    }


    @Override
    public void remoteHangup(SipRequest sipRequest) {
        super.remoteHangup(sipRequest);
        if (this.isNlp) {
            nlpSemanticManager.setFinish(true);
        }
    }

    @Override
    public void initiativeHangup() {
        super.initiativeHangup();
        if (this.isNlp) {
            nlpSemanticManager.setFinish(true);
        }
    }

    public List<CallInDetailPO> getCallInDetailInfoList() {
        return callDetailInfoList.stream().map(item -> (CallInDetailPO) item).collect(Collectors.toList());
    }

    public List<AnalyzeDetail> getCallInDetailInfoBOList() {
        return callDetailInfoList;
    }

    public void setLastUserSayEndTime (long lastUserSayEndTime) {
        this.lastUserSayEndTime = lastUserSayEndTime;
    }

    public String getCurrPlayFileName() {
        return currPlayFileName;
    }

    public FilePlayProgressRecorder getFilePlayProgressRecorder() {
        return filePlayProgressRecorder;
    }


    public int getAsrWaitingSoundOffsetTime() {
        return asrWaitingSoundBytesOffset.get() / ApplicationConfig.MONO_FILE_LENGTH_PER_MILLISECOND;
    }

    public void setAsrEnd(boolean asrEnd) {
        //只要asr结束, 本次vad就标志结束
        this.asrEnd = asrEnd;
        logger.info("setAsrEnd的asrEnd={}", asrEnd);
        if (robot != null && robot instanceof YiwiseRobot) {
            logger.info("使用的是一知的asr，忽略vad信息");
        } else {
            if (asrEnd) {
                this.vadPlayBlank = false;
                this.vadProceeding = false;
            } else {
                this.vadProceeding = true;
            }
        }
    }

    @Override
    public void setLastTransferGroupId(Long csStaffGroupId) {
        this.lastTransferGroupId = csStaffGroupId;
    }

    /**
     * 呼入通话记录保存后执行
     */
    protected void onCallInRecordInsert() {
    }

    @Override
    public void registerFailed(SipResponse sipResponse) {
        super.registerFailed(sipResponse);

        // 在注册失败后添加日志发送
        logger.error("[LogHub_Warn][CallIn_Warn] 呼入服务sip注册失败:" + sipResponse.getReasonPhrase());
    }
}
