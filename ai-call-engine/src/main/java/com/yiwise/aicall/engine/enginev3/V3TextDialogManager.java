package com.yiwise.aicall.engine.enginev3;

import com.yiwise.aicall.engine.aliyun.NluCallListener;
import com.yiwise.aicall.engine.engine.dialog.DialogFlowHandler;
import com.yiwise.aicall.engine.helper.AsyncAsrProcessExecutorHelper;
import com.yiwise.aicall.engine.model.*;
import com.yiwise.aicall.engine.model.websocket.*;
import com.yiwise.aicall.engine.service.DialogFlowLoader;
import com.yiwise.aicall.engine.service.WebSocketSendMsgService;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.helper.*;
import com.yiwise.core.model.bo.callrecord.CallRecordWebSocketBO;
import com.yiwise.core.model.bo.websocket.*;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.callin.CallTypeEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.dialogflow.client.engine.*;
import com.yiwise.dialogflow.client.service.BotChatService;
import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.action.WaitAction;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.dialogflow.engine.share.response.SessionInfo;
import javaslang.control.Try;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.ANONYMOUS_JOB_ID;
import static com.yiwise.core.config.ApplicationConstant.VERBAL_TRICK_TRAINING_CUSTOMER_PERSON_ID;

public class V3TextDialogManager implements NluCallListener {

    private static final Logger logger = LoggerFactory.getLogger(V3TextDialogManager.class);

    private static final WebSocketSendMsgService webSocketSendMsgService = AppContextUtils.getBean(WebSocketSendMsgService.class);
    private static final CallRecordInfoService callRecordInfoService = AppContextUtils.getBean(CallRecordInfoService.class);
    private static final CallDetailInfoService callDetailInfoService = AppContextUtils.getBean(CallDetailInfoService.class);
    private static final UserService userService = AppContextUtils.getBean(UserService.class);
    private static final AliMessageQueueHelper aliMessageQueueHelper = AppContextUtils.getBean(AliMessageQueueHelper.class);
    private static final DialogFlowLoader dialogFlowLoader = AppContextUtils.getBean(DialogFlowLoader.class);
    private static final BotChatService botChatService = AppContextUtils.getBean(BotChatService.class);
    private static final CallRecordActionLogService callRecordActionLogService = AppContextUtils.getBean(CallRecordActionLogService.class);

    private static final Pattern BUTTON_PATTERN = Pattern.compile("\\{([0-9*# ])}");

    private final long dialogFlowId;

    private final DialogFlowInfoPO dialogFlowInfo;

    private TextChatController chatController;

    private BotMetaData botMetaData;

    private UserIdPrincipal principal;

    private SystemEnum clientType;

    CallRecordPO callRecordInfo;

    private LocalDateTime startTime = LocalDateTime.now();

    private Integer durationSeconds;
    /**
     * 自定义变量
     */
    private final Map<String, String> properties;
    private final Map<String, String> originProperties;
    /**
     * 是否挂机
     */
    private boolean isHangup = false;

    /**
     * 用户过滤之后说的话，在第二次进入oneRound的时候，记录的是上一轮的
     */
    private String text;

    private final String logId;

    private List<CallDetailBO> callDetailList;

    private long startTimestamp;

    private LocalDateTime endTime;

    private SessionInfo sessionInfo;

    private volatile boolean executedAiSayFinish;

    private volatile boolean enableSendNoAnswerNotify;

    private volatile boolean analysisFinished;

    public V3TextDialogManager(UserIdPrincipal principal, SystemEnum clientType, DialogFlowInfoPO dialogFlowInfo, Map<String, String> properties) {
        this.logId = String.format("%s_DID_%s_UID_%s", MyRandomStringUtils.getRandomStringByLength(4), principal.getUserId(), dialogFlowInfo.getId());
        resetLogId();
        this.dialogFlowId = dialogFlowInfo.getId();
        this.dialogFlowInfo = dialogFlowInfo;
        this.startTimestamp = System.currentTimeMillis();
        this.principal = principal;
        this.clientType = clientType;

        this.callDetailList = new ArrayList<>();
        UserPO userInfo = userService.selectByKey(principal.getUserId());
        this.properties = RunTimePropertiesHandler.getProperties(userInfo, properties);
        this.originProperties = new HashMap<>(properties);

        parseProperties(properties);

        calleePickup();

        this.botMetaData = DialogFlowHandlerV3.resolveTextTrainDialogFlow(dialogFlowInfo.getId());

        initCallRecordInfo(principal, userInfo, dialogFlowInfo.getId());

        initEngine();
    }

    private void resetLogId() {
        MDC.put("MDC_LOG_ID", this.logId);
    }

    private void initEngine() {
        this.chatController = new TextChatController(botMetaData, callRecordInfo.getCallRecordId(), properties, this);
    }

    private void parseProperties(Map<String, String> properties) {
        if (StringUtils.isNotEmpty(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_GENDER))) {
            GenderEnum fromDesc = GenderEnum.UNKNOWN;
            try {
                fromDesc = GenderEnum.valueOf(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_GENDER));
            } catch (Exception e) {
                logger.info("未传入正确性别", e);
            }
            // 设置传入的性别信息
            this.properties.put(DialogVariablesPlaceholder.CUSTOMER_GENDER, GenderEnum.getDialogText(fromDesc));
        }
        // 姓名转姓名总
        if (StringUtils.isNotEmpty(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_NAME))) {
            String customerName = properties.get(DialogVariablesPlaceholder.CUSTOMER_NAME);
            String bossName = Try.of(() -> customerName.substring(0, 1) + "总").getOrElseGet((e) -> "");
            if (StringUtils.isEmpty(bossName)) {
                properties.put(DialogVariablesPlaceholder.CUSTOMER_NAME_FAMILY_NAME_BOSS, DialogFlowHandler.FAMILY_NAME_NOT_FOUND_REPLACER);
            } else {
                properties.put(DialogVariablesPlaceholder.CUSTOMER_NAME_FAMILY_NAME_BOSS, bossName);
            }
        }
    }

    public void enter() {
        chatController.enter();
    }

    public void onMessage(TextDialogVO textDialogVO) {
        MDC.put("MDC_LOG_ID", this.logId);
        logger.info("oneRound, param={}", JsonUtils.object2String(textDialogVO));
        doOneRound(textDialogVO);
    }

    private void sendNoAnswerMsg() {
        if (enableSendNoAnswerNotify) {
            sendTipMsg("机器人无后续主动操作，请先模拟客户输入", false);
        }
    }

    public void doOneRound(TextDialogVO textDialogVO) {
        String userSayText = textDialogVO.getUserSayText();
        double aiProgress = textDialogVO.getAiProgress() == null ? 100.0 : textDialogVO.getAiProgress();
        if (aiProgress == 0) {
            aiProgress = 100.0;
        }

        updateCallDuration(textDialogVO);
        // 挂机的话，直接返回
        if (isHangup) {
            logger.debug("挂机");
            sendTipMsg("已经挂机了, 请重新开始新的测试", true);
            return;
        }

        if (StringUtils.isEmpty(userSayText)) {
            sendMsg("输入不能为空");
            return;
        }
        if (isAiSayFinish(userSayText)) {
            aiSayFinish();
        } else if (isUserSilence(userSayText)) {
            userSilence();
        } else if (isHangup(userSayText)) {
            userHangup();
        } else if (isKeyCapture(userSayText)) {
            keyCapture(userSayText);
        } else {
            boolean merge =  BooleanUtils.isTrue(textDialogVO.getNeedMerge());
            processUserSay(userSayText, true, aiProgress, merge);
        }
    }

    private void updateCallDuration(TextDialogVO textDialogVO) {
        if (Objects.nonNull(textDialogVO.getDurationSeconds())) {
            durationSeconds = textDialogVO.getDurationSeconds();
            logger.info("update durationSeconds={}", durationSeconds);
        }
    }

    private boolean isUserSilence(String userSayText) {
        return "userSilence".equals(userSayText) || (StringUtils.isWhitespace(userSayText));
    }

    private boolean isKeyCapture(String userSayText) {
        return userSayText.startsWith("#");
    }

    private boolean isAiSayFinish(String userSayText) {
        return "aiSayFinish".equals(userSayText);
    }

    private boolean isHangup(String userSayText) {
        return "hangup".equals(userSayText);
    }

    private void userHangup() {
        chatController.onRemoteHangup();
        isHangup = true;
        updateCallRecordOnFinish(true);
    }

    private void processUserSay(String userSayText, boolean userSayFinish, double progress, boolean merge) {
        logger.info("processUserSay, userSayText={}, userSayFinish={}, progress={}, merge={}", userSayText, userSayFinish, progress, merge);
        int aiSayTime = (int) progress * 100;
        int beginTime = 0;
        int endTime = 0;
        if (merge && BooleanUtils.isNotFalse(botMetaData.getEnableInputMerge())) {
            chatController.textDialogEngine.setMergeFlag();
        }
        try {
            if (progress <= 0 || progress >= 100) {
                // 需要记录下, 是否已经执行过aiSayFinish了
                innerAiSayFinish();
            }

            if (isHangup) {
                logger.debug("挂机");
                sendTipMsg("已经挂机了, 请重新开始新的测试", true);
                return;
            }

            chatController.textDialogEngine.setAiProgress((int) progress);
            chatController.processUserSay(userSayText, progress, aiSayTime, userSayFinish, beginTime, endTime);
        } finally {
            chatController.textDialogEngine.clearMergeFlag();
        }
    }

    private String trim(String text) {
        return StringUtils.trimToEmpty(text);
    }

    private void userSilence() {
        innerAiSayFinish();
        if (isHangup) {
            logger.debug("挂机");
            sendTipMsg("已经挂机了, 请重新开始新的测试", true);
            return;
        }
        chatController.textDialogEngine.userSilence();
    }

    private void keyCapture(String userSayText) {
        userSayText = userSayText.substring(1);
        if ("timeout".equals(userSayText) || "t".equals(userSayText) || "T".equals(userSayText)) {
            chatController.dtmfManager.timout();
        }
        for (int i = 0; i < userSayText.length(); i++) {
            char c = userSayText.charAt(i);
            chatController.whenUserClick(c);
        }
    }

    // 在通话结束后分析意向等级
    public void analysisIntentLevel(boolean isCustomerHangup) {
        endTime = LocalDateTime.now();
        DialStatusEnum dialStatus = DialStatusEnum.ANSWERED;
        long now = System.currentTimeMillis();
        long duration = Objects.nonNull (durationSeconds) ? durationSeconds : (now - startTimestamp) / 1000;

        CallDataInfo callDataInfo = new CallDataInfo();
        callDataInfo.setStartTime(startTimestamp);
        callDataInfo.setDialStatus(dialStatus.getCode());
        callDataInfo.setChatDuration(duration);
        callDataInfo.setEndTime(now);
        callDataInfo.setSessionInfo(sessionInfo);
        callDataInfo.setFastHangup(false);
        callDataInfo.setUserHangup(isCustomerHangup);
        callDataInfo.setTenantId(callRecordInfo.getTenantId());
        callDataInfo.setCallJobId(callRecordInfo.getRobotCallJobId());
        callDataInfo.setCallRecordId(callRecordInfo.getCallRecordId());

        IntentLevelAnalysisResult intentLevelAnalysisResult = chatController.analysis(callRecordInfo.getCallRecordId(), callDataInfo);
        if (Objects.nonNull(intentLevelAnalysisResult) && BooleanUtils.isNotTrue(analysisFinished)) {
            analysisFinished = true;
            logger.info("意向等级分析结果={}", JsonUtils.object2String(intentLevelAnalysisResult));
            updateCallRecordInfo(intentLevelAnalysisResult, duration);

            // v3需要记录特殊动作执行结果
            try {
                if (CollectionUtils.isNotEmpty(intentLevelAnalysisResult.getIntentRuleActionResultList())) {
                    List<CallRecordActionLogPO> list = intentLevelAnalysisResult.getIntentRuleActionResultList().stream()
                            .map(item -> {
                                CallRecordActionLogPO log = new CallRecordActionLogPO();
                                log.setCallRecordId(callRecordInfo.getCallRecordId());
                                log.setDialogFlowId(callRecordInfo.getDialogFlowId());
                                log.setReason(item.getReason());
                                log.setRuleId(item.getRuleId());
                                log.setCustomerTagIdList(item.getCustomerTagIdList());
                                log.setSmsTemplateIdList(item.getSmsTemplateIdList());
                                log.setWhiteGroupIdList(item.getWhiteGroupIdList());
                                log.setAddWechat(item.getAddWechat());
                                return log;
                            }).collect(Collectors.toList());
                    callRecordActionLogService.batchInsert(callRecordInfo.getCallRecordId(), list);
                }
            } catch (Exception e) {
                logger.warn("记录特殊动作执行结果失败", e);
            }
        }
    }

    protected void updateCallRecordInfo(IntentLevelAnalysisResult analysisResult, long duration) {
        if (analysisResult == null) {
            return;
        }
        callRecordInfo.setWechatPushUsers(Collections.emptySet());
        callRecordInfo.setEndTime(getEndTime());
        callRecordInfo.setStartTime(getStartTime());
        callRecordInfo.setChatRound(analysisResult.getAiRounds());

        logger.info("获取探意分析的意向 intent={}", analysisResult.getIntentLevel());
        callRecordInfo.setIntentLevel(analysisResult.getIntentLevel());
        String analysisBasis = analysisResult.getBasis();
        if (analysisBasis.length() > 1000) {
            logger.info("analysisBasis超过1000字 截取前1000字");
            analysisBasis = analysisBasis.substring(0, 1000);
        }
        callRecordInfo.setAnalysisBasis(analysisBasis);
        callRecordInfo.setChatDuration(duration);

        callRecordInfo.setIntentLevelTagId(dialogFlowInfo.getIntentLevelTagId());
        // 通话过程中可能人工标记意向,通话结束后不再设值避免覆盖真实值
        // 本次使用的变量
        callRecordInfo.setProperties(originProperties);
        // 客户属性
        callRecordInfo.setAttributes(analysisResult.getCustomerAttribute());
        callRecordInfo.setCallType(CallTypeEnum.LOCAL);
        callRecordInfo.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);

        callRecordInfo.setHangupBy(CallJobHangupEnum.INITIAL_HANGUP);
        //关注点
        callRecordInfo.setCustomerConcern(analysisResult.getCustomerFocus());
        // 动态变量
        callRecordInfo.setDynamicVariables(analysisResult.getDynamicVariableValueMap());

        callRecordInfoService.updateNotNull(callRecordInfo);
    }

    private LocalDateTime getEndTime() {
        return endTime == null ? LocalDateTime.now() : endTime;
    }

    /**
     * 执行aiSayFinish
     */
    private void aiSayFinish() {
        logger.debug("执行aiSayFinish方法");
        enableSendNoAnswerNotify = true;
        doAiSayFinish();
    }

    private void innerAiSayFinish() {
        logger.debug("执行AiSayFinish方法");
        enableSendNoAnswerNotify = false;
        doAiSayFinish();
    }

    private void doAiSayFinish() {
        logger.debug("执行doAiSayFinish方法");
        if (executedAiSayFinish) {
            logger.info("当前话术执行过aiSayFinish, 无需再执行");
            return;
        }
        try {
            executedAiSayFinish = true;
            chatController.textDialogEngine.onAiPlayEnd(0);
        } catch (Exception e) {
            logger.error("执行onAiPlayEnd方法异常", e);
        }
    }



    /**
     * 发送错误信息
     */
    public void whenError(String message) {
        isHangup = true;
        logger.error("[LogHub_Warn]文本测试出现错误={}", message);
        CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(message, null);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
    }

    @Override
    public void pauseAudio() {
        logger.info("【NewAction】pauseAudio");
    }

    @Override
    public void playNewAudio(String audioUrl) {
        logger.info("【NewAction】playNewAudio, audioUrl=" + audioUrl);
    }

    @Override
    public void playAudioWithTts(String text, String originTemplate, List<String> textOssUrl, Map<String, String> customVariablesProperties, Map<String, Object> ttsParam) {
        logger.info("【NewAction】playAudioWithTts");
    }

    @Override
    public void waitForUserSay(Integer timeoutMillisecond) {
        logger.info("【NewAction】waitForUserSay, timeoutMillisecond=" + timeoutMillisecond);
    }

    @Override
    public void resume() {
        logger.info("【NewAction】resume");
    }

    @Override
    public void hangup() {
        isHangup = true;
        logger.info("【NewAction】hangup，挂机了，需要重新执行reset");
        updateCallRecordOnFinish(false);

        sendTipMsg("机器人执行了挂机操作", true);
    }

    public void updateCallRecordOnFinish(boolean isCustomerHangup) {
        try {
            MDC.put("MDC_LOG_ID", this.logId);
            logger.info("文本训练结束, 执行意向分析");
            analysisIntentLevel(isCustomerHangup);
//            persistenceKeywordDetectionList();
        } catch (Exception e) {
            logger.info("文本训练意向分析异常", e);
        }
    }

    @Override
    public void onUserSay(UserSayBO userSayBO) {
        logger.debug("【onUserSay】{}", userSayBO.getText());
        CallDetailMsg callDetailMsg = CallDetailMsg.toPerson(userSayBO);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
        // 保存通话记录
        CallDetailBO callDetail = new CallDetailBO(userSayBO);
        callDetailList.add(callDetail);
        persistenceCallDetail(callDetail);
    }

    @Override
    public void onAiSay(AiSayBO aiSayBO) {
        MDC.put("MDC_LOG_ID", this.logId);
        String text = aiSayBO.getText();
        sendMsg(text, aiSayBO.getDebugLog());
        // 持久化通话记录详情
        CallDetailBO callDetail = new CallDetailBO(aiSayBO);
        callDetail.setDebugLog(aiSayBO.getDebugLog());
        callDetailList.add(callDetail);
        persistenceCallDetail(callDetail);
    }

    private void sendMsg(String msg) {
        sendMsg(msg, null);
    }
    private void sendMsg(String msg, String debugLog) {
        CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(msg, debugLog, null);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
    }

    private void sendTipMsg(String msg, boolean hangup) {
        TextTrainTipMsg tipMsg = new TextTrainTipMsg(msg, hangup);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, tipMsg, clientType);
    }

    protected void persistenceCallDetail(CallDetailPO callDetail) {
        try {
            executedAiSayFinish = false;
            callDetail.setTenantId(callRecordInfo.getTenantId());
            callDetail.setCallRecordId(callRecordInfo.getCallRecordId());
            //任务，话术，通话开始时间冗余到call_detail表
            callDetail.setRobotCallJobId(callRecordInfo.getRobotCallJobId());
            callDetail.setDialogFlowId(callRecordInfo.getDialogFlowId());
            callDetail.setStartTime(getStartTime());
            callDetailInfoService.addCallDetailList(Collections.singletonList(callDetail));
        } catch (Exception e) {
            logger.error("文本训练callDetail序列化失败", e);
        }
    }

    private LocalDateTime getStartTime() {
        return startTime;
    }

    private void initCallRecordInfo(UserIdPrincipal userIdPrincipal, UserPO user, Long dialogFlowId) {
        long tenantId = user.getTenantId();
        long distributorId = user.getDistributorId();

        if (userIdPrincipal instanceof TenantUserIdPrincipal) {
            TenantUserIdPrincipal tenantUserIdPrincipal = (TenantUserIdPrincipal) userIdPrincipal;
            tenantId = tenantUserIdPrincipal.getTenantId();
        }

        if (userIdPrincipal instanceof DistributorUserIdPrincipal) {
            DistributorUserIdPrincipal principal = (DistributorUserIdPrincipal) userIdPrincipal;
            distributorId = principal.getDistributorId();
        }

        // callRecord入库
        callRecordInfo = new CallRecordPO();
        callRecordInfo.setTenantId(tenantId);
        callRecordInfo.setRobotCallTaskId(0L);
        callRecordInfo.setCallRecordType(CallRecordTypeEnum.TEXT_TRAINING);
        callRecordInfo.setDistributorId(distributorId);
        callRecordInfo.setRobotCallJobId(ANONYMOUS_JOB_ID);
        callRecordInfo.setCustomerPersonId(VERBAL_TRICK_TRAINING_CUSTOMER_PERSON_ID);
        callRecordInfo.setResultStatus(DialStatusEnum.NO_ANSWER);
        callRecordInfo.setCalledPhoneNumber(user.getPhoneNumber());
        callRecordInfo.setCreateUserId(user.getUserId());
        callRecordInfo.setStartTime(getStartTime());
        CallRecordPO.DeploymentInformation deploymentInformation = new CallRecordPO.DeploymentInformation();
        deploymentInformation.setServerName(ServerInfoConstants.SERVER_HOSTNAME);
        deploymentInformation.setIpAddress(ServerInfoConstants.SERVER_IP_ADDRESS);
        deploymentInformation.setLogId(this.logId);
        deploymentInformation.setNlpModelVersion(botMetaData.getNlpModelVersion());
        callRecordInfo.setDeploymentInformation(deploymentInformation);
		callRecordInfo.setLogId(this.logId);
        callRecordInfo.setDialogFlowId(dialogFlowId);
        callRecordInfo.setResultStatus(DialStatusEnum.ANSWERED);
        callRecordInfoService.saveNotNull(callRecordInfo);

        // 发送callRecord到前端
        sendCallRecordMsg(callRecordInfo);
    }

    private void sendCallRecordMsg(CallRecordPO callRecordInfo) {
        CallRecordWebSocketBO callRecordWebSocketBO = MyBeanUtils.copy(callRecordInfo, CallRecordWebSocketBO.class);
        CallRecordInfoMsg callRecordMsg = new CallRecordInfoMsg(callRecordWebSocketBO);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callRecordMsg, clientType);
    }

    public void calleePickup() {
        MDC.put("MDC_LOG_ID", this.logId);
        SystemMsg systemMsg = new SystemMsg("用户已接听", SystemMsgType.CALLEE_PICKUP);
        webSocketSendMsgService.sendCallTaskInfoMsgToUser(principal, systemMsg, clientType);
    }

    @Override
    public void transferToThirdParty(Long humanCsStaffGroupId) {

    }

    @Override
    public void transferToHuman() {
        logger.info("文本测试不支持转人工");
    }

    @Override
    public void transferToCs() {

    }

    private class TextChatController extends V3ChatController {

        private TextDialogEngine textDialogEngine;
        private TextDTMFManager dtmfManager;
        public TextChatController(BotMetaData botMetaData,
                                  Object taskId,
                                  Map<String, String> properties,
                                  NluCallListener nluCallListener) {
            super(botMetaData, taskId, RobotSnapshotUsageTargetEnum.TEXT_TEST, properties, nluCallListener);
        }

        @Override
        protected AudioManager createAudioManager(Long tenantId, BotMetaData botMetaData) {
            return new TextTestAudioManager();
        }

        @Override
        protected AudioPlayManager createAudioPlayManager(AudioManager audioManager) {
            return new TextTestAudioPlayManager();
        }

        @Override
        protected DTMFManager createDTMFManager() {
            if (this.dtmfManager == null) {
                this.dtmfManager = new TextDTMFManager();
            }
            return this.dtmfManager;
        }

        @Override
        protected SpeechDialogEngine createSpeechDialogEngine(Long tenantId, BotMetaData botMetaData, SessionInfo sessionInfo, AudioPlayManager audioPlayManager, DTMFManager dtmfManager) {
            textDialogEngine = new TextDialogEngine(sessionInfo, botMetaData, (TextTestAudioPlayManager) audioPlayManager, dtmfManager);
            return textDialogEngine;
        }
    }

    private class TextDialogEngine extends DefaultSpeechDialogEngine {

        boolean merge;

        private int aiProgress;

        private final TextTestAudioPlayManager textAudioPlayManager;

        public TextDialogEngine(SessionInfo sessionInfo, BotMetaData botMetaData, TextTestAudioPlayManager audioPlayManager, DTMFManager dtmfManager) {
            super(botMetaData, sessionInfo, audioPlayManager, AsyncAsrProcessExecutorHelper.getApplicationExecutor(), dtmfManager);
            this.textAudioPlayManager = audioPlayManager;
        }

        @Override
        protected boolean isMergeInput(UserSayInfo last, UserSayInfo current) {
            return merge;
        }

        public void setMergeFlag() {
            merge = true;
        }

        public void clearMergeFlag() {
            merge = false;
        }

        public void setAiProgress(int aiProgress) {
            this.aiProgress = aiProgress;
        }

        @Override
        protected void doWaitAction(WaitAction waitAction) {
            super.doWaitAction(waitAction);
            sendNoAnswerMsg();
        }

        @Override
        public void processUserSay(String userSayText, boolean userSayFinish, int beginTime, int endTime) {
            // 文本训练测试需要手动设计录音播放进度
            textAudioPlayManager.setCurrentAnswerPlayPercent(aiProgress);
            int aiSayTime = textAudioPlayManager.getCurrentAnswerPlayTime();
            String text = textAudioPlayManager.getCurrentAnswerPlayedContent();
            String subText = text;
            if (aiProgress < 100d) {
                int index = text.length() * aiProgress / 100;
                subText = text.substring(0, index);
            }
            this.innerProcessUserSay(userSayText, aiProgress, aiSayTime, userSayFinish, beginTime, endTime, subText);
        }
    }

}
