package com.yiwise.aicall.engine.engine.brain;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yiwise.aicall.engine.aliyun.AliyunAsrSoundManager;
import com.yiwise.aicall.engine.aliyun.NluCallListener;
import com.yiwise.aicall.engine.aliyun.WebSocketRobotSoundManager;
import com.yiwise.aicall.engine.asr.AsrContext;
import com.yiwise.aicall.engine.bo.DialogFlowInfoBO;
import com.yiwise.aicall.engine.bo.KnowledgeManagerInfo;
import com.yiwise.aicall.engine.bo.RuntimeTextAudioContent;
import com.yiwise.aicall.engine.bo.SimpleStepInfoBO;
import com.yiwise.aicall.engine.bo.algorithm.PredictResultWrapper;
import com.yiwise.aicall.engine.engine.TextDialogManager;
import com.yiwise.aicall.engine.engine.brain.entities.DialogFlow;
import com.yiwise.aicall.engine.engine.brain.entities.PredictResult;
import com.yiwise.aicall.engine.engine.brain.enums.AlgorithmTypeEnum;
import com.yiwise.aicall.engine.engine.brain.enums.PredictTypeEnum;
import com.yiwise.aicall.engine.engine.brain.events.Event;
import com.yiwise.aicall.engine.engine.dialog.DTMFManager;
import com.yiwise.aicall.engine.engine.listener.AnalyzableDetailCreatedListener;
import com.yiwise.aicall.engine.engine.listener.AsrResultEventListener;
import com.yiwise.aicall.engine.enginev3.V3TextDialogManager;
import com.yiwise.aicall.engine.helper.AsyncAsrProcessExecutorHelper;
import com.yiwise.aicall.engine.helper.DebugLogHelper;
import com.yiwise.aicall.engine.helper.ShareAnswerHelper;
import com.yiwise.aicall.engine.interruption.*;
import com.yiwise.aicall.engine.model.AnalyzeDetail;
import com.yiwise.aicall.engine.model.CallDetailBO;
import com.yiwise.aicall.engine.model.TestOneNodeVO;
import com.yiwise.aicall.engine.model.UserSayBO;
import com.yiwise.aicall.engine.service.PropertyCollectionService;
import com.yiwise.aicall.engine.utils.RuntimeTextAudioContentUtils;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.text.PinyinUtils;
import com.yiwise.base.common.text.TextPlaceholderSplitter;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.math.DecimalFormatUtils;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.config.ApplicationDialogConstant;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.dal.mongo.asr.AsrApplicationPO;
import com.yiwise.core.dal.mongo.asr.SystemAsrHotWordReplacePairPO;
import com.yiwise.core.helper.YiwiseAskHelper;
import com.yiwise.core.model.bo.algorithm.IntentBO;
import com.yiwise.core.model.bo.algorithm.PredictRequest;
import com.yiwise.core.model.bo.algorithm.PredictResponse;
import com.yiwise.core.model.bo.calldetail.WeipinhuiAudioPlayDetail;
import com.yiwise.core.model.bo.calldetail.WeipinhuiCallDetailExtendInfoBO;
import com.yiwise.core.model.bo.calleventlog.WeipinhuiCallEventLog;
import com.yiwise.core.model.bo.callrecord.WeipinhuiCallRecordExtendInfoBO;
import com.yiwise.core.model.bo.dialogflownodestats.DialogFlowNodeStatsDetailKey;
import com.yiwise.core.model.bo.robotcalljob.CollectExtraInfoBO;
import com.yiwise.core.service.dialogflow.ShareAnswerSceneService;
import com.yiwise.middleware.tts.TtsConfig;
import com.yiwise.middleware.tts.helper.TtsConfigConvertHelper;
import com.yiwise.middleware.tts.enums.TtsVoiceEnum;
import com.yiwise.middleware.tts.enums.TtsProviderEnum;
import com.yiwise.core.model.dialogflow.dto.*;
import com.yiwise.core.model.dialogflow.entity.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.dialogflow.*;
import com.yiwise.core.model.enums.train.AlgorithmTrainTypeEnum;
import com.yiwise.core.model.enums.train.ModelTypeEnum;
import com.yiwise.core.model.enums.train.SnapshotTypeEnum;
import com.yiwise.core.model.po.CallEventLogBatchPO;
import com.yiwise.core.model.po.CallEventLogExtraPO;
import com.yiwise.core.model.po.CallEventLogPO;
import com.yiwise.core.model.vo.dialogflowinfo.VariableVO;
import com.yiwise.core.service.asr.AsrApplicationService;
import com.yiwise.core.service.asr.SystemAsrHotWordReplacePairService;
import com.yiwise.core.service.dialogflow.AsrErrCorrectionService;
import javaslang.Tuple;
import javaslang.Tuple2;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.WEIPINHUI_SHARE_ANSWER_DIALOGFLOW_IDS;

/**
 * 语义处理类
 *
 * <AUTHOR> yangdehong
 * @date : 2019/3/7 09:58
 */
public class SemanticManager implements AsrResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(SemanticManager.class);

    private static final PropertyCollectionService propertyCollectionService = AppContextUtils.getBean(PropertyCollectionService.class);
    private static final SystemAsrHotWordReplacePairService systemAsrHotWordReplacePairService = AppContextUtils.getBean(SystemAsrHotWordReplacePairService.class);
    private static final AsrErrCorrectionService asrErrCorrectionService = AppContextUtils.getBean(AsrErrCorrectionService.class);
    private static final ShareAnswerSceneService shareAnswerSceneService = AppContextUtils.getBean(ShareAnswerSceneService.class);

    private static final double ALI_MIN_VOLUME = 0;
    private static final double ALI_MAX_VOLUME = 100;
    private static final double YIWISE2_MIN_VOLUME = 0;
    private static final double YIWISE2_MAX_VOLUME = 100;

    private static final double MIN_VOLUME = 1;
    private static final double MAX_VOLUME = 9;
    /**
     * 对话处理类
     */
    @Getter
    private final DialogReturnVisitManager dialogManagerNew;
    /**
     * 解析后的话术数据
     */
    @Getter
    private final DialogFlowInfoBO dialogFlowInfoBO;

    /**
     * 传入的变量
     */
    @Getter
    private final Map<String, String> runtimeProperties;

    /**
     * 用户无应答时间
     */
    private Integer userSilenceMillisecond;

    /**
     * 多次业务问题是否同意业务问题-同一问题的次数
     */
    private final Map<String, Integer> businessQuestionCountMap;

    private final Map<String, Integer> preBusinessQuestionCountMap;

    /**
     * 匹配到的流程信息
     */
    private final MatchingStepInfoManager matchingStepInfoManager;
    /**
     * 进入问答知识中的判断信息
     */
    @Getter
    private final KnowledgeManagerInfo knowledgeManagerInfo;

    // 记录断句补齐上一轮问答知识的匹配情况
    private final KnowledgeManagerInfo preKnowledgeManagerInfo;
    /**
     * 业务问题次数和否定次数
     */
    private int businessQuestionCount;

    // 断句补齐前业务问题命中次数
    private int preBusinessQuestionCount;

    // 命中否定次数
    private int negativeCount;

    // 断句补齐前命中否定次数
    private int preNegativeCount;
    /**
     * 重复的流程id
     */

    @Getter
    private final Set<String> preRepeatDialogFlowIdSet;

    @Getter
    private final Set<String> repeatDialogFlowIdSet;
    /**
     * 最近一次语境id
     */
    private String lastLanguageEnvironmentId;
    /**
     * 是否需要合并上一句重新判断
     */
    boolean isNeedMerge;
    /**
     * 当前节点信息采集次数
     */
    private int collectCount;
    /**
     * 信息采集节点采集到的信息
     */
    @Getter
    Map<Long, Set<String>> collectMap;
    @Getter
    LocalDateTime redialTime;
    /**
     * dtmf信号管理
     */
    @Getter
    private final DTMFManager dtmfManager;
    /**
     * 每次说话的语音内容
     */
    @Getter
    private boolean isNeedVad;
    /**
     * 是否继续AI等待
     */
    @Getter
    @Setter
    private boolean isAiContinueSay;
    /**
     * 节点测试
     */
    @Getter
    @Setter
    private TestOneNodeVO testOneNodeVO;

    /**
     * 关键词匹配详情
     */
    @Getter
    private KeyWordDetectionPO keyWordDetectionPO;

    @Getter
    private final Map<TextAudioContentPO, RuntimeTextAudioContent> runtimeTextAudioContentMap;

    @Getter
    @Setter
    private Integer waitUserSayFinishTimeout;

    private final List<WaitUserSayFinishBO> tmpUserInputList;

    private boolean enableToneInterrupt;

    // 语气词打断百分比
    private int toneInterruptPercent;
    // 语气词列表
    private Set<String> toneWordSet;

    // 是否启用通话最长时间限制
    private Boolean enableCallTimeout;

    // 通话最长时间， 单位毫秒
    private Integer callTimeoutMillis;

    private TextAudioContentPO callTimeoutHangupAnswer;

    private final long startCallTime;

    // 是否正在播放挂机前话术
    private boolean playingPreHangupAnswer;

    private boolean enableContinuousMuteTimeout;

    // 连续静音时长限制， 单位毫秒
    private long continuousMuteTimeoutMillis;

    private TextAudioContentPO continuousMuteHangupAnswer;


    // ai最后一次说完时间
    private long lastAiSayFinishTimestamp = 0;

    // 用户最后一次说完时间
    private long lastUserSayFinishTimestamp = 1;

    // 最近一次用户活跃时间戳， 活跃时间定义为，用户说完后的下一次aiSayFinish事件时
    private long lastUserActiveTimestamp = Long.MAX_VALUE;

    // 是否开启延时挂机
    @Getter
    private boolean enableDelayHangup = false;

    // 延时挂机毫秒
    @Getter
    private int delayHangupMillisecond = 0;

    private final IntentBranchPredictDataContext intentBranchPredictDataContext;

    // 全局采集信息列表
    private final List<GlobalAssignInfo> globalAssignList;

    private final Set<Tuple2<String, String>> repeatAssignSet;

    private Map<String, String> systemHotWordReplaceMap;

    private Map<String, String> customHotWordReplaceMap;

    @Getter
    private final List<PredictLogPO> algorithmPredictLogList = new ArrayList<>();

    @Getter
    private Float branchConfidence = null;

    private AliyunAsrSoundManager aliyunAsrSoundManager;

    private final CallEventLogProcessor eventLogProcessor;

    private boolean ignoreBranchPredictResult;

    private boolean preIgnoreBranchPredictResult;

    /**
     * 进入听不清并重复答案模式
     */
    @Getter
    @Setter
    private boolean inaudibleAndRepeatMode;

    private boolean preInaudibleAndRepeatMode;

    /**
     * 是否语音测试
     */
    private final boolean isVerbalTraining;

    /**
     * 是否文本测试
     */
    private final boolean isTextDialog;

    private final BusiMetricsDetail metrics = new BusiMetricsDetail();

    private final BusiMetricsDetail preMetrics = new BusiMetricsDetail();
    @Getter
    private final List<AnalyzeDetail> analyzeDetailList = new CopyOnWriteArrayList<>();

    private final List<AnalyzeDetail> preAnalyzeDetailList = new ArrayList<>();

    private final List<AnalyzeDetail> noRollbackAnalyzeDetailList = new ArrayList<>();

    /**
     * ASR上下文
     */
    private final AsrContext asrContext;

    @Getter
    private final AtomicInteger eventIndex = new AtomicInteger();

    private long enterTimestamp = System.currentTimeMillis();


    public SemanticManager(DialogFlow dialogFlow, NluCallListener nluCallListener, Map<String, String> originRuntimeProperties,
                           Map<TextAudioContentPO, RuntimeTextAudioContent> runtimeTextAudioContentMap,
                           AsrContext asrContext) {
        logger.debug("开始初始化SemanticManager语义对象类");
        this.asrContext = asrContext;
        this.dialogFlowInfoBO = dialogFlow.getDialogFlowInfoBO();
        this.runtimeProperties = new HashMap<>();
        // 对于变量过滤掉头尾和中间的空格
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(originRuntimeProperties)) {
            originRuntimeProperties.forEach((k, v) -> {
                this.runtimeProperties.put(org.springframework.util.StringUtils.trimAllWhitespace(k), v);
            });
        }
        this.runtimeTextAudioContentMap = runtimeTextAudioContentMap;
        this.userSilenceMillisecond = this.dialogFlowInfoBO.getDefaultUserSilenceMillisecond();

        this.businessQuestionCountMap = Maps.newHashMap();
        this.preBusinessQuestionCountMap = Maps.newHashMap();

        this.eventLogProcessor = new CallEventLogProcessor();

        if (nluCallListener instanceof AliyunAsrSoundManager) {
            aliyunAsrSoundManager = (AliyunAsrSoundManager) nluCallListener;
            aliyunAsrSoundManager.registerCreateAnalyzableDetailListener(new AnalyzableDetailListener());
        }
        this.isVerbalTraining = nluCallListener instanceof WebSocketRobotSoundManager;
        this.isTextDialog = nluCallListener instanceof TextDialogManager || nluCallListener instanceof V3TextDialogManager;

        this.matchingStepInfoManager = new MatchingStepInfoManager(new StateRollbackEventProcessor() {
            @Override
            public void recordState() {
                // 记录重复流程id
                SemanticManager.this.preRepeatDialogFlowIdSet.clear();
                SemanticManager.this.preRepeatDialogFlowIdSet.addAll(SemanticManager.this.repeatDialogFlowIdSet);
                // 记录业务问答知识命中次数
                SemanticManager.this.preBusinessQuestionCount = SemanticManager.this.businessQuestionCount;
                SemanticManager.this.preBusinessQuestionCountMap.clear();
                SemanticManager.this.preBusinessQuestionCountMap.putAll(SemanticManager.this.businessQuestionCountMap);
                // 记录命中拒绝分支次数
                SemanticManager.this.preNegativeCount = SemanticManager.this.negativeCount;

                eventLogProcessor.commitLog();
                // 暂存问答知识命中情况
                recordKnowledgeManagerInfo();
                preIgnoreBranchPredictResult = ignoreBranchPredictResult;
                preInaudibleAndRepeatMode = inaudibleAndRepeatMode;
                recordDialogStats();
                recordBusiMetrics();
                recordAnalyzeDetail();
            }

            @Override
            public void rollbackState() {
                logger.info("断句补齐开始回退状态");
                // 回退重复分支id列表
                SemanticManager.this.repeatDialogFlowIdSet.clear();
                SemanticManager.this.repeatDialogFlowIdSet.addAll(SemanticManager.this.preRepeatDialogFlowIdSet);
                // 回退命中业务问题次数统计
                SemanticManager.this.businessQuestionCount = SemanticManager.this.preBusinessQuestionCount;
                SemanticManager.this.businessQuestionCountMap.clear();
                SemanticManager.this.businessQuestionCountMap.putAll(SemanticManager.this.preBusinessQuestionCountMap);
                // 回退命中拒绝分支统计次数
                SemanticManager.this.negativeCount = SemanticManager.this.preNegativeCount;
                dialogManagerNew.rollbackFilePlayProgress();
                eventLogProcessor.rollbackLog();
                // 回退问答知识匹配的内容
                rollbackKnowledgeManagerInfo();
                ignoreBranchPredictResult = preIgnoreBranchPredictResult;
                inaudibleAndRepeatMode = preInaudibleAndRepeatMode;
                rollbackDialogStats();
                rollbackBusiMetrics();
                rollbackAnalyzeDetail();
            }
        });
        this.knowledgeManagerInfo = new KnowledgeManagerInfo();
        this.preKnowledgeManagerInfo = new KnowledgeManagerInfo();
        this.repeatDialogFlowIdSet = Sets.newHashSet();
        this.preRepeatDialogFlowIdSet = Sets.newHashSet();
        this.collectMap = Maps.newHashMap();

        this.isNeedMerge = false;
        this.isNeedVad = false;
        this.isAiContinueSay = false;
        this.negativeCount = 0;

        if (ApplicationConstant.POC_DIALOGFLOW_ID.contains(String.valueOf(dialogFlow.getDialogFlowInfoPO().getId()))) {
            // poc 测试话术, 按键即提交, 另外如果仅按#, 则获取到的按键采集信息为空
            logger.info("poc测试话术, 按键即提交");
            this.dtmfManager = new DTMFManager(nluCallListener, 1);
        } else {
            this.dtmfManager = new DTMFManager(nluCallListener);
        }
        this.dialogManagerNew = returnVisitManager(dialogFlow, nluCallListener, this.runtimeProperties);

        this.tmpUserInputList = new ArrayList<>();
        this.startCallTime = System.currentTimeMillis();

        this.intentBranchPredictDataContext = new IntentBranchPredictDataContext();
        globalAssignList = new ArrayList<>();
        repeatAssignSet = new HashSet<>();
        initConfiguration(dialogFlow.getConfiguration());
        initAsrHotWordReplacePair();
    }

    private void rollbackBusiMetrics() {
        preMetrics.copyTo(metrics);
    }

    private void recordBusiMetrics() {
        metrics.copyTo(preMetrics);
    }
    private void rollbackAnalyzeDetail() {
        analyzeDetailList.clear();
        analyzeDetailList.addAll(preAnalyzeDetailList);
    }

    private void recordAnalyzeDetail() {
        preAnalyzeDetailList.clear();
        preAnalyzeDetailList.addAll(analyzeDetailList);
    }

    private void rollbackDialogStats() {
        dialogManagerNew.rollbackDialogStats();
    }

    private void recordDialogStats() {
        dialogManagerNew.recordDialogStats();
    }

    public DialogBusiMetricsPO getBusiMetrics() {
        DialogBusiMetricsPO busiMetrics = new DialogBusiMetricsPO();
        logger.info("originMetrics:{}", JsonUtils.object2String(this.metrics));

        busiMetrics.setDialogFlowId(this.dialogFlowInfoBO.getDialogFlowId());
        // 有些需要计算的
        busiMetrics.setAlgorithmMatchCount(metrics.getAlgorithmMatchCount().longValue());
        busiMetrics.setRegexMatchCount(metrics.getRegexMatchCount().longValue());
        long noneIntent = metrics.userSayFinishCount.get() - metrics.algorithmMatchCount.get() - metrics.regexMatchCount.get();
        busiMetrics.setNoneIntentCount(noneIntent);

        busiMetrics.setRobotKnowledgeCount(metrics.getRobotKnowledgeCount().get() > 0 ? 1L : 0L);
        busiMetrics.setAbuseKnowledgeCount(metrics.getAbuseKnowledgeCount().get() > 0 ? 1L : 0L);
        boolean isInaudibleMatch = metrics.totalInaudibleKnowledgeCount.get() >= 3 || metrics.maxContinuousInaudibleKnowledgeCount.get() >= 2;
        busiMetrics.setInaudibleKnowledgeCount(isInaudibleMatch ? 1L : 0L);
        boolean isWholeMute = metrics.getUserSayFinishCount().get() < 1 && metrics.getUserSilenceCount().get() > 0;
        // 这里后面可能还需要判断是ai挂断的才累加
        busiMetrics.setUserWholeMuteCount(isWholeMute ? 1L : 0L);

        logger.info("businMetrics:{}", JsonUtils.object2String(busiMetrics));
        return busiMetrics;
    }

    private void recordKnowledgeManagerInfo() {
        logger.info("记录当前问答知识匹配情况, preInfo={}, currentInfo={}", printKnowledgeManagerInfo(preKnowledgeManagerInfo), printKnowledgeManagerInfo(knowledgeManagerInfo));
        preKnowledgeManagerInfo.setKnowledge(knowledgeManagerInfo.getKnowledge());
        preKnowledgeManagerInfo.setInKnowledge(knowledgeManagerInfo.isInKnowledge());
        preKnowledgeManagerInfo.setInterrupt(knowledgeManagerInfo.isInterrupt());
        preKnowledgeManagerInfo.setRepeat(knowledgeManagerInfo.isRepeat());
        preKnowledgeManagerInfo.setInConfig(knowledgeManagerInfo.isInConfig());
        this.getDialogManagerNew().getRobotKnowledgeManager().recordState();
    }

    private void rollbackKnowledgeManagerInfo() {
        logger.info("回退问答知识匹配情况, preInfo={}, currentInfo={}", printKnowledgeManagerInfo(preKnowledgeManagerInfo), printKnowledgeManagerInfo(knowledgeManagerInfo));
        knowledgeManagerInfo.setKnowledge(preKnowledgeManagerInfo.getKnowledge());
        knowledgeManagerInfo.setInKnowledge(preKnowledgeManagerInfo.isInKnowledge());
        knowledgeManagerInfo.setInterrupt(preKnowledgeManagerInfo.isInterrupt());
        knowledgeManagerInfo.setRepeat(preKnowledgeManagerInfo.isRepeat());
        knowledgeManagerInfo.setInConfig(preKnowledgeManagerInfo.isInConfig());
        this.getDialogManagerNew().getRobotKnowledgeManager().rollbackState();
    }

    private String printKnowledgeManagerInfo(KnowledgeManagerInfo info) {
        if (info == null) {
            return "null";
        }

        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(info.getKnowledge())) {
            map.put("knowledge", info.getKnowledge().getTitle());
        } else {
            map.put("knowledge", "null");
        }
        map.put("interrupt", info.isInterrupt());
        map.put("isInterruptByThreshold", info.isInterruptByThreshold());
        map.put("interruptThreshold", info.getInterruptThreshold());
        map.put("repeat", info.isRepeat());
        map.put("inKnowledge", info.isInKnowledge());
        map.put("inConfig", info.isInConfig());

        return JsonUtils.object2String(map);
    }


    private void initAsrHotWordReplacePair() {
        String appkey = asrContext.getAsrAppkey();
        AsrApplicationPO asrApplicationPO = asrContext.getAsrApplication();
        if (StringUtils.isBlank(appkey)) {
            logger.info("asrAppKey is null, 跳过初始化");
            return;
        }
        systemHotWordReplaceMap = new HashMap<>();
        customHotWordReplaceMap = new HashMap<>();

        try {
            // 加载系统配置
            SystemAsrHotWordReplacePairPO replacePair = systemAsrHotWordReplacePairService.queryByAsrAppKeyUseCache(appkey);
            if (Objects.nonNull(replacePair) && CollectionUtils.isNotEmpty(replacePair.getReplaceMapping())) {
                for (SystemAsrHotWordReplacePairPO.ReplaceMappingItem item : replacePair.getReplaceMapping()) {
                    String replacement = item.getCorrect();
                    List<String> originWordList = item.getReplace();
                    if (StringUtils.isNotBlank(replacement) && CollectionUtils.isNotEmpty(originWordList)) {
                        originWordList.forEach(originWord -> {
                            if (StringUtils.isNotBlank(originWord)) {
                                systemHotWordReplaceMap.put(originWord.trim(), replacement.trim());
                            }
                        });
                    }
                }
            }
        } catch (Exception e) {
            logger.error("初始化系统asr 热词替换配置异常", e);
        }

        try {
            // 加载用户配置
            if (Objects.nonNull(asrApplicationPO) && CollectionUtils.isNotEmpty(asrApplicationPO.getReplaceWord())) {
                asrApplicationPO.getReplaceWord().forEach(pair -> {
                    if (StringUtils.isNoneBlank(pair.getValue(), pair.getName())) {
                        customHotWordReplaceMap.put(pair.getName().trim(), pair.getValue().trim());
                    }
                });
            }
        } catch (Exception e) {
            logger.error("初始化系统asr 热词替换配置异常", e);
        }

        logger.info("初始化热词替换完成, systemHotWordReplaceMap={}, customHotWordReplaceMap={}", JsonUtils.object2String(systemHotWordReplaceMap), JsonUtils.object2String(customHotWordReplaceMap));
    }

    private String doHotWordReplace(String originUserInput) {
        if (MapUtils.isEmpty(customHotWordReplaceMap) && MapUtils.isEmpty(systemHotWordReplaceMap)) {
            return originUserInput;
        }

        if (StringUtils.isBlank(originUserInput)) {
            return originUserInput;
        }

        try {
            Map<String, String> replaceMap = MapUtils.isNotEmpty(customHotWordReplaceMap) ? customHotWordReplaceMap : systemHotWordReplaceMap;
            if (MapUtils.isEmpty(replaceMap)) {
                return originUserInput;
            }

            String result = originUserInput;
            for (Map.Entry<String, String> entry : replaceMap.entrySet()) {
                String search = entry.getKey();
                String replacement = entry.getValue();
                result = StringUtils.replace(result, search, replacement);
            }

            if (!originUserInput.equals(result)) {
                logger.info("热词替换命中, 原始输入={}, 替换后={}", originUserInput, result);
            }
            return result;
        } catch (Exception e) {
            logger.error("执行热词替换异常", e);
            return originUserInput;
        }
    }

    private void initConfiguration(DialogFlowConfigurationPO configuration) {
        // 语气词打断
        this.enableToneInterrupt = false;
        this.toneInterruptPercent = 100;
        this.toneWordSet = Collections.emptySet();

        // 通话超时
        this.enableCallTimeout = false;
        this.callTimeoutMillis = Integer.MAX_VALUE;

        // 用户连续静音超时
        this.enableContinuousMuteTimeout = false;

        if (Objects.isNull(configuration)) {
            return;
        }

        this.enableDelayHangup = BooleanUtils.isTrue(configuration.getEnableDelayHangup());
        if (this.enableDelayHangup && Objects.nonNull(configuration.getDelayHangupSeconds())) {
            this.delayHangupMillisecond = (int) (configuration.getDelayHangupSeconds() * 1000);
        }

        // 语气词打断
        enableToneInterrupt = BooleanUtils.isTrue(configuration.getEnableToneInterrupt()) && Objects.nonNull(configuration.getToneInterruptPercent());
        if (enableToneInterrupt) {
            this.toneInterruptPercent = configuration.getToneInterruptPercent();
            if (CollectionUtils.isNotEmpty(configuration.getToneWordList())) {
                this.toneWordSet = new HashSet<>(configuration.getToneWordList());
            }
        }

        // 通话超时
        this.enableCallTimeout = BooleanUtils.isTrue(configuration.getEnableCallTimeout())
                && Objects.nonNull(configuration.getCallTimeoutSeconds())
                && Objects.nonNull(configuration.getCallTimeoutHangupAnswer());
        if (enableCallTimeout) {
            this.callTimeoutMillis = configuration.getCallTimeoutSeconds() * 1000;
            this.callTimeoutHangupAnswer = configuration.getCallTimeoutHangupAnswer();
            logger.info("开启通话时长限制，callTimeoutMillis={}", this.callTimeoutMillis);
        }

        // 用户连续静音超时
        this.enableContinuousMuteTimeout = BooleanUtils.isTrue(configuration.getEnableContinuousMuteTimeout())
                && Objects.nonNull(configuration.getContinuousMuteHangupAnswer())
                && Objects.nonNull(configuration.getContinuousMuteTimeoutSeconds());

        if (this.enableContinuousMuteTimeout) {
            this.continuousMuteHangupAnswer = configuration.getContinuousMuteHangupAnswer();
            this.continuousMuteTimeoutMillis = configuration.getContinuousMuteTimeoutSeconds() * 1000;
            logger.info("开启连续静音超时限制，continuousMuteTimeoutMillis={}", continuousMuteTimeoutMillis);
        }
    }

    private DialogReturnVisitManager returnVisitManager(DialogFlow dialogFlow, NluCallListener nluCallListener, Map<String, String> customVariables){
        if(dynamicTts(dialogFlow)) {
            logger.info("话术id={}, 动态合成", dialogFlow.getId());
            TtsConfig ttsConfig = new TtsConfig();
            ttsConfig.setVoiceType(dialogFlow.getVoiceType());
            ttsConfig.setVolume(TtsConfigConvertHelper.computeRealParamValue(Double.valueOf(dialogFlow.getTtsVolume()), MIN_VOLUME, MAX_VOLUME, 0d, 100d).floatValue());
            ttsConfig.setSpeed(dialogFlow.getTtsSpeech());
            ttsConfig.setVoice(dialogFlow.getTtsVoice());
            Map<String, Object> map = new HashMap<>();
            if (ttsProvider(TtsProviderEnum.ALI, dialogFlow.getTtsVoice())) {
                map = TtsConfigConvertHelper.aliTtsRestConfig(ttsConfig);
                map.put("provider", TtsProviderEnum.ALI.name());
            }

            if (ttsProvider(TtsProviderEnum.BIAOBEI, dialogFlow.getTtsVoice()) || ttsProvider(TtsProviderEnum.YIWISE2, dialogFlow.getTtsVoice())) {
                map = TtsConfigConvertHelper.yiwiseTtsConfig2(ttsConfig);
                map.put("provider", TtsProviderEnum.YIWISE2.name());
            }
            return new DialogReturnVisitManager(nluCallListener, this.dialogFlowInfoBO, this, customVariables, map, matchingStepInfoManager);
        } else {
            return new DialogReturnVisitManager(nluCallListener, this.dialogFlowInfoBO, this, customVariables, matchingStepInfoManager);
        }
    }

    /**
     * 话术是否需要动态TTS(通话中TTS)
     */
    private static boolean dynamicTts(DialogFlow dialogFlow) {
        if (dialogFlow.isRealtimeTtsByDialogFlowName()) {
            if (dialogFlow.isRealtimeTtsByVoice()) {
                return true;
            }
            List<VariableVO> variables = dialogFlow.getVariableList();
            if (CollectionUtils.isNotEmpty(variables)) {
                return variables.stream().anyMatch(variable -> VariableTypeEnum.DYNAMIC.getCode().equals(variable.getType()));
            }
        }
        return false;
    }

    /**
     * voice音色是否属于provider提供者
     */
    private static boolean ttsProvider(TtsProviderEnum provider, TtsVoiceEnum voice) {
        return provider.equals(voice.getPartTtsProvider()) || provider.equals(voice.getPureTtsProvider());
    }

    /**
     * 开始进入对话过程
     */
    public void enter() {
        // 话术开始时的是好，stepIndex=0，nodeIndex=0
        enterTimestamp = System.currentTimeMillis();
        eventIndex.getAndIncrement();
        eventLogProcessor.createWeipinhuiEventLog(0, WeipinhuiCallEventLog.TYPE_START);

        DialogFlowStepListDTO dialogFlowStep = this.dialogFlowInfoBO.getDialogFlowSteps().get(0);
        matchingStepInfoManager.init(dialogFlowStep, 0, 0);
        dialogManagerNew.enter();
        eventLogProcessor.createEventLog(CallEventTypeEnums.START)
                .ifPresent(eventLogProcessor::updateStepNodeState);
    }

    /**
     * 单节点测试时候走的enter
     */
    public void enter(String stepId, Long nodeId) {
        this.testOneNodeVO = new TestOneNodeVO();
        this.testOneNodeVO.setSuccess(true);
        DialogFlowStepListDTO dialogFlowStepListDTO = this.dialogFlowInfoBO.getMainStepMap().get(stepId);
        Integer stepIndex = this.dialogFlowInfoBO.getDialogFlowSteps().indexOf(dialogFlowStepListDTO);
        if (Objects.isNull(dialogFlowStepListDTO)) {
            dialogFlowStepListDTO = this.dialogFlowInfoBO.getMultiRoundStepMap().get(stepId);
        }

        int nodeIndex = 0;
        List<DialogFlowNodeDTO> flattenTreeNodeList = dialogFlowStepListDTO.getFlattenTreeNodeList();
        for (int i = 0; i < flattenTreeNodeList.size(); i++) {
            DialogFlowNodeDTO dialogFlowNodeDTO = flattenTreeNodeList.get(i);
            Long id = dialogFlowNodeDTO.getId();
            if (Objects.equals(nodeId, id)) {
                nodeIndex = i;
            }
        }
        matchingStepInfoManager.init(dialogFlowStepListDTO, stepIndex, nodeIndex);
        dialogManagerNew.enter(dialogFlowStepListDTO, nodeIndex);
    }

    /**
     * AI 放音完成
     */
    public void aiSayFinish() {
        aiSayFinish(0);
    }

    /**
     * AI 放音完成
     */
    public void aiSayFinish(int audioPlayIndex) {
        eventIndex.incrementAndGet();
        eventLogProcessor.createWeipinhuiEventLog(audioPlayIndex, WeipinhuiCallEventLog.TYPE_AI_SAY_END);
        if (isCallTimeout()) {
            hangupByCallTimeout("通话时间超时");
            return;
        }
        if (isContinuousMuteTimeout()) {
            hangupByContinuousMuteTimeout("连续静音时长超过限制");
            return;
        }
        updateAiSayFinishTimestamp();
        dialogManagerNew.aiSayFinish();
        eventLogProcessor.createEventLog(CallEventTypeEnums.AI_SAY_FINISH)
                .ifPresent(eventLogProcessor::updateStepNodeState);
    }

    public void aiSayBegin(int audioPlayIndex) {
        eventLogProcessor.createEventLog(CallEventTypeEnums.AI_SAY_BEGIN);
        eventLogProcessor.createWeipinhuiEventLog(audioPlayIndex, WeipinhuiCallEventLog.TYPE_AI_SAY_BEGIN);
    }

    public void aiSayPause(int audioPlayIndex) {
        eventLogProcessor.createEventLog(CallEventTypeEnums.AI_SAY_PAUSE);
        eventLogProcessor.createWeipinhuiEventLog(audioPlayIndex, WeipinhuiCallEventLog.TYPE_AI_SAY_PAUSE);
    }

    public void aiSayResume(int audioPlayIndex) {
        eventLogProcessor.createEventLog(CallEventTypeEnums.AI_SAY_RESUME);
        eventLogProcessor.createWeipinhuiEventLog(audioPlayIndex, WeipinhuiCallEventLog.TYPE_AI_SAY_RESUME);
    }

    public List<CallEventLogPO> getEventLogList() {
        return eventLogProcessor.eventLogList;
    }

    /**
     * ai等待客户时间到了，询问是否可以继续
     */
    public void aiWaitTimeBack() {
        String userSayText = "ai等待客户时间到了";
        String debugLog = "ai等待客户时间到了，询问是否可以继续";
        keyWordDetectionToKnowledge(this.matchingStepInfoManager.getStep(), this.dialogFlowInfoBO.getAiWaitKnownKnowledge().getAiWaitBackKnownKnowledge(), userSayText, 0.0, debugLog);
    }

    /**
     * 用户设置的用户等待时长
     */
    public Integer getUserSilenceMillisecond() {
        // 这里需要根据是否开启了等待用户输入来进行一些动态调整， 以保证可以通过用户输入超时来正确触发用户输入完成
        if (isWaitUserSayFinish()) {
            int waitUserSayFinishMillisecond = waitUserSayFinishTimeout * 1000;
            if (CollectionUtils.isEmpty(tmpUserInputList)) {
                return userSilenceMillisecond;
            }
            return waitUserSayFinishMillisecond;
        }

        return userSilenceMillisecond;
    }


    /**
     * 用户不说话
     */
    public void userSilence() {
        eventIndex.incrementAndGet();
        Optional<CallEventLogPO> log = eventLogProcessor.createEventLog(CallEventTypeEnums.USER_SILENCE);
        log.ifPresent(eventLogProcessor::updatePlayProgressState);
        doUserSilence();
        log.ifPresent(eventLogProcessor::updateStepNodeState);
    }

    private void doUserSilence() {
        if (isCallTimeout()) {
            hangupByCallTimeout("通话时间超时");
            return;
        }

        if (isContinuousMuteTimeout()) {
            hangupByContinuousMuteTimeout("连续静音时长超过限制");
            return;
        }

        // 对于等待用户说完的逻辑， 在userSilence里面处理，这样不用开轮训线程， 避免因为线程切换带来问题
        if (isWaitUserSayFinish() && CollectionUtils.isNotEmpty(tmpUserInputList)) {
            // 用户说完了
            // 把之前说的所有的话拼成一句话，然后进行匹配
            logger.info("等待用户说完, 用户输入数据合并前为：{}", JsonUtils.object2String(tmpUserInputList));

            // 对用户输入内容进行合并, 按开始
            TreeMap<Integer, String> sortedUserInputMap = new TreeMap<>();
            tmpUserInputList.forEach(item -> {
                sortedUserInputMap.put(item.getBeginTime(), item.getText());
            });
            logger.info("等待用户说完, tmpUserInputList={}", JsonUtils.object2String(sortedUserInputMap));
            List<String> tmpTextList = new ArrayList<>();
            sortedUserInputMap.forEach((k, v) -> {
                tmpTextList.add(v);
            });
            String allUserSay = String.join("", tmpTextList);
            logger.debug("等待用户说完，现在已经说完了，用户输入={}", allUserSay);
            Integer startOffset = tmpUserInputList.stream()
                    .map(WaitUserSayFinishBO::getBeginTime)
                    .min(Comparator.naturalOrder())
                    .orElse(0);

            Integer endOffset = tmpUserInputList.stream()
                    .map(WaitUserSayFinishBO::getEndTime)
                    .max(Comparator.naturalOrder())
                    .orElse(0);

            tmpUserInputList.clear();
            logger.info("startOffset={}, endOffset={}", startOffset, endOffset);
            dialogManagerNew.setBeginTime(startOffset);
            dialogManagerNew.setEndTime(endOffset);
            allUserSay = doAsrErrCorrection(allUserSay);
            processUserSayFinished(allUserSay, allUserSay, 0.0);
            return;
        }

        if (dtmfManager.isWorking()) {
            logger.debug("当前正在进行按键收集, 忽略语音超时回复");
            return;
        }
        String silenceWords = ApplicationConstant.USER_SILENCE_WORDS;

        metrics.userSilenceCount.incrementAndGet();
        try {
            DialogFlowNodeDTO dialogFlowNodeDTO = this.matchingStepInfoManager.getStep().getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());

            if (DialogFlowNodeTypeEnum.INFO.equals(dialogFlowNodeDTO.getType())) {
                logger.info("信息采集节点用户无应答直接走问答知识");
            } else if (this.dialogFlowInfoBO.getNoAnswerBranchUidSet().size() > 0) {
                // 匹配到默认分支
                if (!dialogFlowNodeDTO.canLink()) {
                    DialogFlowChatNodeDTO chatNode = (DialogFlowChatNodeDTO) dialogFlowNodeDTO;
                    Map<String, Integer> intentBranchToChildrenNode = chatNode.getIntentBranchToChildrenNode();
                    for (String item : intentBranchToChildrenNode.keySet()) {
                        boolean isFlag = this.dialogFlowInfoBO.getNoAnswerBranchUidSet().contains(item);
                        if (isFlag) {
                            logger.info("用户无应答，走无应答分支");
                            IntentBranchPO intentBranch = this.dialogFlowInfoBO.getIntentBranchMap().get(item);
                            Integer nextNodeIndex = chatNode.getIntentBranchToChildrenNode().get(item);
                            this.matchingStepInfoManager.setNodeIndex(nextNodeIndex);
                            String debugLog = DebugLogHelper.noAnswer(this.isNeedMerge, intentBranch);
                            logger.info(debugLog);
                            if (!accumulateNegative(intentBranch, silenceWords, 0.0)) {
                                this.dialogManagerNew.keyWordDetectionToBranch(intentBranch, silenceWords, debugLog, this.isNeedMerge, 0.0);
                            }
                            return;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]用户无应答分支处理错误，将进入无应答知识库", e);
        }
        logger.info("用户无应答，走无应答问答知识流程");
        if (this.inaudibleAndRepeatMode) {
            logger.info("命中用户无应答, 取消听不清并重复上一句状态");
            this.inaudibleAndRepeatMode = false;
        }
        // 记录语境
        recordLastLanguageEnvironment(this.dialogFlowInfoBO.getSilenceKnowledge().getLanguageEnvironmentId());
        this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
        boolean interrupt = this.dialogManagerNew.userSilence();
        setKnowledgeManagerInfo(false, false, interrupt, true, this.dialogFlowInfoBO.getSilenceKnowledge());
    }

    /**
     * 用户说话
     *
     * @param userSayText   语音识别返回结果
     * @param aiProgress    为0则表示AI放音完成，否则正在放音
     * @param userSayFinish 用户是否说完
     */
    @Override
    public String processUserSay(String userSayText,
                                 double aiProgress,
                                 Integer aiSayTime,
                                 boolean userSayFinish,
                                 Integer beginTime,
                                 Integer endTime) {
        eventIndex.incrementAndGet();
        if (userSayFinish && Objects.nonNull(beginTime) && Objects.nonNull(endTime)) {
            createUserSayFinishEvent(userSayText, beginTime, endTime);
        }
        if ("*".equals(ApplicationConstant.ASR_RESULT_ASYNC_PROCESS_DIALOG_NAME_SUFFIX)
                || dialogFlowInfoBO.getDialogFlowName().endsWith(ApplicationConstant.ASR_RESULT_ASYNC_PROCESS_DIALOG_NAME_SUFFIX)) {
            logger.info("当前对话异步处理asr识别结果");
            String mdcLogId = MDC.get("MDC_LOG_ID");
            AsyncAsrProcessExecutorHelper.execute("异步处理asr识别结果", () -> {
                MDC.put("MDC_LOG_ID", mdcLogId);
                innerProcessUserSay(userSayText, aiProgress, aiSayTime, userSayFinish, beginTime, endTime);
            });
            // 这里后面应该返回 void, 并没有使用到该返回结果的地方和必要
            return userSayText;
        } else {
            return innerProcessUserSay(userSayText, aiProgress, aiSayTime, userSayFinish, beginTime, endTime);
        }
    }

    private synchronized String innerProcessUserSay(String userSayText,
                                 double aiProgress,
                                 Integer aiSayTime,
                                 boolean userSayFinish,
                                 Integer beginTime,
                                 Integer endTime) {
        final String originUserInput = userSayText;
        eventLogProcessor.createEventLog(CallEventTypeEnums.USER_INPUT)
                .ifPresent(log -> {
                    log.setText(originUserInput);
                });

        branchConfidence = null;

        // 赋值ASR的开始时间和结束时间
        dialogManagerNew.setBeginTime(beginTime);
        dialogManagerNew.setEndTime(endTime);

        // 只要说话了就记录
        this.matchingStepInfoManager.setPreUserSayMatchingStepInfoBO();
        updateUserSayFinishTimestamp();
        if (isCallTimeout()) {
            hangupByCallTimeout(userSayText);
            return null;
        }

        // 去掉结尾的句号，问好，感叹号
        if (userSayText.endsWith("。")||userSayText.endsWith("？")||userSayText.endsWith("?")||userSayText.endsWith("！")
                ||userSayText.endsWith("!")||userSayText.endsWith("，")||userSayText.endsWith(",")) {
            userSayText = userSayText.substring(0, userSayText.length()-1);
        }

        this.isNeedVad = userSayText.length()>=ApplicationDialogConstant.NEED_VAD_NUMBER;
        if (dtmfManager.isWorking()) {
            logger.debug("当前正在进行按键收集, 忽略用户说话");
            return null;
        }

        // 如果设置了等待用户把话说完，这直接暂存用户说的话，等用户说完之后在进行下面的判断
        if (isWaitUserSayFinish()) {
            WaitUserSayFinishBO waitUserSayFinishBO = new WaitUserSayFinishBO(beginTime, endTime, userSayText);
            logger.debug("等待用户说完，先暂存用户输入：{}", JsonUtils.object2String(waitUserSayFinishBO));
            tmpUserInputList.add(waitUserSayFinishBO);
            return null;
        }


        if (userSayFinish) {
            userSayText = doAsrErrCorrection(userSayText);
        }

        // 真实用来判断的文本
        String realMatchingText = userSayText;

        // 是否用户说话太长
        if (this.dialogFlowInfoBO.isUserTooLongOpt()) {
            Integer intervalTime = this.matchingStepInfoManager.getLastStepInfoBO().getIntervalTime();
            Integer lastEndTime = this.matchingStepInfoManager.getLastStepInfoBO().getLastEndTime();

            boolean pauseMerge =lastEndTime>0 && intervalTime>=beginTime-lastEndTime;
            // 判断用户是否说话太长了
            Integer userSayBeginTime = pauseMerge?this.matchingStepInfoManager.getLastStepInfoBO().getBeginTime():beginTime;
            logger.info("用户说话太长判断的endTime={}，beginTime={}", endTime, userSayBeginTime);
            realMatchingText = pauseMerge?this.matchingStepInfoManager.getLastStepInfoBO().getLastUserSayText()+"，"+userSayText:realMatchingText;
            boolean b = accumulateUserSay(endTime==null? 0:(endTime - userSayBeginTime), realMatchingText, aiProgress);
            if (b) {
                return realMatchingText;
            }
        }

        // 判断是否需要直接挂断
        if (this.dialogManagerNew.isUserSayDirectHangup(realMatchingText, userSayFinish, beginTime, endTime, this.dialogFlowInfoBO.getDialogFlowName())) {
            return realMatchingText;
        }

        // 判断是否需要打断（即噪音过滤判断）
        // 噪音：不停止播放录音，不往下走
        // 不可打断：不停止播放录音，不往下走
        // 正常用户输入：停止录音，往下走
        logger.info("打断判断开始......userInput={}, aiProgress={}", realMatchingText, aiProgress);
        InterruptionConfigBO interruptionConfigBO = InterruptionConfigBO.builder()
                .enableToneInterrupt(enableToneInterrupt)
                .toneInterruptPercent(toneInterruptPercent)
                .toneWordSet(toneWordSet)
                .build();
        InterruptionContext context = InterruptionContext.builder()
                .userInput(realMatchingText)
                .aiProgress(aiProgress)
                .beginTime(beginTime)
                .endTime(endTime)
                .isNeedMerge(isNeedMerge)
                .interruptionConfigBO(interruptionConfigBO)
                .ignoreNoiseMatchContext(dialogManagerNew.getIgnoreNoiseMatchContext())
                .dialogFlowInfoBO(dialogFlowInfoBO)
                .matchingStepInfoManager(matchingStepInfoManager)
                .knowledgeManagerInfo(knowledgeManagerInfo)
                .asrResultEventListener(this)
                .build();

        InterruptionFilterChain filterChain = new DefaultFilterChain();
        filterChain.setContext(context);
        // 这里调整了一下顺序，由简到繁：
        // 单字噪音过滤器->自定义语气词过滤->节点打断判断
        filterChain.next(new NoiseFilterChain())
                .next(new CustomModalParticleFilterChain())
                .next(new NodeInterruptableFilterChain());
        boolean interruptable = filterChain.filterAll();
        if (interruptable) {
            logger.info("打断判断结束，是否打断：是，userInput={}", realMatchingText);
            // 兼容文本测试
            if (Objects.nonNull(aliyunAsrSoundManager)) {
                aliyunAsrSoundManager.pauseAudio();
            }
        } else {
            logger.info("打断判断结束，是否打断：否，userInput={}", realMatchingText);
            // 如果是AI放音过程中的ASR最终结果，且判断为不可打断，则需要恢复播放，以防止中间结果判断为可打断导致的暂停播放
            if (userSayFinish && Objects.nonNull(aliyunAsrSoundManager)) {
                aliyunAsrSoundManager.resume();
            }
            onUserSay(new UserSayBO(context.getUserInput(), context.getBeginTime(), context.getEndTime(), context.getDebugLog(),
                    null, context.getMatchingStepInfoManager().getStep(), context.getMatchingStepInfoManager().getNodeIndex(),
                    null, true));
            return realMatchingText;
        }

//        realMatchingText = dialogManagerNew.processUserSay(realMatchingText, aiProgress, userSayFinish, beginTime, endTime, this.isNeedMerge);

        // 是否需要合并
        if (userSayFinish && StringUtils.isNotBlank(realMatchingText)) {
            // 断句补齐
            String backText = this.matchingStepInfoManager.linkingUp(realMatchingText, beginTime, endTime);
            if (!realMatchingText.equals(backText)) {
                realMatchingText = backText;
                this.isNeedMerge = true;
            } else {
                this.isNeedMerge = false;
            }
        } else {
            this.isNeedMerge = false;
        }
        if (userSayFinish && realMatchingText != null) {
            this.dialogManagerNew.setSilenceCount(0);
            // 获取当前节点的信息，加入语句
            processUserSayFinished(realMatchingText, userSayText, aiProgress);
        }

        return realMatchingText;
    }

    public void onUserSay(UserSayBO userSay) {
        userSay.setEventIndex(eventIndex.get());
        dialogManagerNew.onUserSay(userSay);
    }

    private boolean aiPlayFinish(Double aiProcess) {
        return aiProcess == ApplicationDialogConstant.DEFAULT_AI_PROGRESS;
    }

    /**
     * 对问答知识或意向分支进行预测, 返回命中的问答知识/意向分支, 以及命中的具体问法/关键词
     * 当开启了问法时，先走问法出置信度，根据置信度判断：
     * - 置信度高于阈值上限，问法优先
     * - 置信度高于阈值下限，同时走算法和关键词，有重叠的取交集内置信度最高的，没有则关键词优先
     * - 置信度低于阈值下限，走关键词
     */
    public PredictResult processPredict(String realMatchingText, AlgorithmTypeEnum algorithmType, Set<String> excludeKnowledgeIdSet, Optional<CallEventLogPO> eventLog) {

        logger.info("开始" + algorithmType.getDesc() + "预测");
        logger.info("excludeKnowledgeIdSet={}", excludeKnowledgeIdSet);

        PredictResult predictResult = null;

        // 优先进行语境匹配，语境只有问答知识才有
        if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType) && StringUtils.isNotBlank(lastLanguageEnvironmentId)) {
            logger.debug("成功匹配语境id={}", lastLanguageEnvironmentId);
            Map<Pattern, RobotKnowledgePO> environmentRegexMap = this.dialogFlowInfoBO.getKnowledgeLanguageEnvironmentMap().get(lastLanguageEnvironmentId);
            Map<Pattern, RobotKnowledgePO> environmentPinyinRegexMap = this.dialogFlowInfoBO.getKnowledgePinyinEnvironmentMap().get(lastLanguageEnvironmentId);
            Optional<PredictResultWrapper> predictResultOptional = predictKnowledgeByRegex(realMatchingText, environmentRegexMap, environmentPinyinRegexMap, excludeKnowledgeIdSet);
            if (predictResultOptional.isPresent()) {
                predictResult = predictResultOptional.get().getPredictResult();
                logger.info("通过语境关键词, 命中问答知识:{}", predictResult.getRobotKnowledgePO().getTitle());
            }
        }

        // 解决算法和正则的冲突
        DialogFlowStepListDTO currStep = this.matchingStepInfoManager.getStep();
        DialogFlowNodeDTO dialogFlowNode = currStep.getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());

        if (Objects.isNull(predictResult)) {
            // 问法知识库匹配
            Map<String, RobotKnowledgePO> robotKnowledgeMap = dialogFlowInfoBO.getRobotKnowledgeMap();
            Map<String, IntentBranchPO> intentBranchMap = dialogFlowInfoBO.getNameBranchMap();
            Optional<PredictResultWrapper> algorithmKnowledgeOptional;
            Optional<PredictResultWrapper> regexKnowledgeOptional;
            if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                algorithmKnowledgeOptional = predictKnowledgeByAlgorithm(realMatchingText, excludeKnowledgeIdSet);
                regexKnowledgeOptional = predictKnowledgeByRegex(realMatchingText, dialogFlowInfoBO.getKnowledgeRegexMap(), this.dialogFlowInfoBO.getKnowledgePinyinRegexMap(), excludeKnowledgeIdSet);
                // 记录到事件日志中
                eventLogProcessor.updateOriginPredictResult(eventLog,true, false, algorithmKnowledgeOptional);
                eventLogProcessor.updateOriginPredictResult(eventLog,true, true, regexKnowledgeOptional);
            } else {
                algorithmKnowledgeOptional = predictIntentBranchByAlgorithm(realMatchingText);
                regexKnowledgeOptional = predictIntentBranchByRegex(realMatchingText, intentBranchPredictDataContext.getKeywordsIntentList(), intentBranchPredictDataContext.getIntentBranchKeywordPatternMap(), intentBranchPredictDataContext.getKeywordsBranchMap());
                // 记录到事件日志中
                eventLogProcessor.updateOriginPredictResult(eventLog,false, false, algorithmKnowledgeOptional);
                eventLogProcessor.updateOriginPredictResult(eventLog,false, true, regexKnowledgeOptional);
            }
            if (algorithmKnowledgeOptional.isPresent() && regexKnowledgeOptional.isPresent()) {
                PredictResultWrapper algorithmResultWrapper = algorithmKnowledgeOptional.get();
                PredictResultWrapper regexResultWrapper = regexKnowledgeOptional.get();
                PredictResult algorithmResult = algorithmResultWrapper.getPredictResult();
                PredictResult regexPredictResult = regexResultWrapper.getPredictResult();

                Double confidence = algorithmResult.getConfidence();

                Double upperThreshold = dialogFlowInfoBO.getKnowledgeUpperThreshold();
                Double lowerThreshold = dialogFlowInfoBO.getKnowledgeLowerThreshold();

                if (BigDecimal.valueOf(confidence).compareTo(BigDecimal.valueOf(upperThreshold)) >= 0) {
                    // 置信度高于阈值上限，问法优先
                    predictResult = algorithmResult;
                    if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                        logger.info("置信度高于阈值上限，通过问法命中问答知识={}，置信度={}，阈值上限={}", predictResult.getRobotKnowledgePO().getTitle(), confidence, upperThreshold);
                    } else {
                        logger.info("置信度高于阈值上限，通过问法命中意向分支={}，置信度={}，阈值上限={}", predictResult.getIntentBranchPO().getName(), confidence, upperThreshold);
                    }
                } else {
                    if (BigDecimal.valueOf(confidence).compareTo(BigDecimal.valueOf(lowerThreshold)) >= 0) {
                        // 置信度高于阈值下限，有重叠，问法优先
                        List<IntentBO> predictIntentBOList = algorithmResultWrapper.getIntentBOList();
                        List<IntentBO> regexIntentBOList = regexResultWrapper.getIntentBOList();
                        List<IntentBO> intersection = intersection(predictIntentBOList, regexIntentBOList);
                        // 有重叠，取交集内置信度最高的
                        if (CollectionUtils.isNotEmpty(intersection)) {
                            Optional<IntentBO> optional = intersection.stream().max(Comparator.comparingDouble(e -> Double.parseDouble(e.getConfidence())));
                            if (optional.isPresent()) {
                                IntentBO intentBO = optional.get();
                                predictResult = new PredictResult();
                                predictResult.setPredictType(PredictTypeEnum.ALGORITHM);
                                predictResult.setConfidence(Double.valueOf(intentBO.getConfidence()));
                                predictResult.setKeyword(intentBO.getName());
                                predictResult.setMatchText(intentBO.getSim_sentence());
                                if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                                    RobotKnowledgePO knowledge = robotKnowledgeMap.get(intentBO.getName());
                                    predictResult.setAlgorithmType(AlgorithmTypeEnum.KNOWLEDGE);
                                    predictResult.setRobotKnowledgePO(knowledge);
                                    logger.info("置信度高于阈值下限，通过问法命中问答知识={}，置信度={}，阈值下限={}", intentBO.getName(), confidence, lowerThreshold);
                                } else {
                                    IntentBranchPO intentBranchPO = intentBranchMap.get(intentBO.getName());
                                    predictResult.setAlgorithmType(AlgorithmTypeEnum.INTENT_BRANCH);
                                    predictResult.setIntentBranchPO(intentBranchPO);
                                    logger.info("置信度高于阈值下限，通过问法命中意向分支={}，置信度={}，阈值下限={}", intentBO.getName(), confidence, lowerThreshold);
                                }
                            }
                        } else {
                            // 没有重叠或置信度低于阈值下限，走关键词
                            predictResult = regexPredictResult;
                            if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                                logger.info("置信度高于阈值下限，没有重叠，通过关键词, 命中问答知识:{}", predictResult.getRobotKnowledgePO().getTitle());
                            } else {
                                logger.info("置信度高于阈值下限，没有重叠，通过关键词, 命中意向分支:{}", predictResult.getIntentBranchPO().getName());
                            }
                        }
                    } else {
                        // 没有重叠或置信度低于阈值下限，走关键词
                        predictResult = regexPredictResult;
                        if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                            logger.info("置信度低于阈值下限，通过关键词, 命中问答知识:{}", predictResult.getRobotKnowledgePO().getTitle());
                        } else {
                            logger.info("置信度低于阈值下限，通过关键词, 命中意向分支:{}", predictResult.getIntentBranchPO().getName());
                        }
                    }
                }
            } else if (algorithmKnowledgeOptional.isPresent()) {
                // 只命中了问法
                PredictResultWrapper robotKnowledgeConfidenceWrapper = algorithmKnowledgeOptional.get();
                PredictResult algorithmResult = robotKnowledgeConfidenceWrapper.getPredictResult();

                Double confidence = algorithmResult.getConfidence();
                Double upperThreshold = dialogFlowInfoBO.getKnowledgeUpperThreshold();
                if (BigDecimal.valueOf(confidence).compareTo(BigDecimal.valueOf(upperThreshold)) >= 0) {
                    predictResult = algorithmResult;
                    if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                        logger.info("仅命中问法，通过问法命中问答知识={}，置信度={}，阈值上限={}", predictResult.getRobotKnowledgePO().getTitle(), confidence, upperThreshold);
                    } else {
                        logger.info("仅命中问法，通过问法命中意向分支={}，置信度={}，阈值上限={}", predictResult.getIntentBranchPO().getName(), confidence, upperThreshold);
                    }
                }
            } else if (regexKnowledgeOptional.isPresent()) {
                // 只命中了关键词
                predictResult = regexKnowledgeOptional.get().getPredictResult();
                if (AlgorithmTypeEnum.KNOWLEDGE.equals(algorithmType)) {
                    logger.info("仅命中关键词，通过关键词, 命中问答知识:{}", predictResult.getRobotKnowledgePO().getTitle());
                } else {
                    logger.info("仅命中关键词，通过关键词, 命中意向分支:{}", predictResult.getIntentBranchPO().getName());
                }
            }
        }

        if (Objects.isNull(predictResult)) {
            logger.info(algorithmType.getDesc() + "预测无结果");
        }

        return predictResult;
    }

    /**
     * 获取算法结果和正则结果的交集
     */
    private List<IntentBO> intersection(List<IntentBO> predictIntentList, List<IntentBO> regexIntentList) {
        List<IntentBO> collection = Lists.newArrayList();
        if (CollectionUtils.isEmpty(predictIntentList) || CollectionUtils.isEmpty(regexIntentList)) {
            return collection;
        }

        for (IntentBO regexIntent : regexIntentList) {
            for (IntentBO predictIntent : predictIntentList) {
                if (Objects.equals(regexIntent.getName(), predictIntent.getName())) {
                    collection.add(predictIntent);
                }
            }
        }

        return collection;
    }

    /**
     * 意向分支关键词匹配
     * - 命中多个关键词时取最长的
     * - 所有匹配命中的结果放到intentBOList里，用于与算法比较
     * - 关键词优先，未匹配到关键词则进行拼音匹配
     */
    private Optional<PredictResultWrapper> predictIntentBranchByRegex(String realMatchingText,
                                                                      List<String> keywordsIntentList,
                                                                      Map<String, Pattern> intentBranchKeywordPatternMap,
                                                                      Map<String, IntentBranchPO> keywordsBranchMap) {

        // 正则匹配的Pattern
        Pattern targetKnowledgePattern = null;
        // 匹配到的关键词（没有正则的）
        String targetBranchKeyword = null;
        // 匹配到的内容
        String targetBranchKeywordText = null;
        // 匹配命中的问答知识对象
        IntentBranchPO intentBranchPO = null;
        // 匹配命中的对象列表，用于与算法比较
        List<IntentBO> intentBOList = Lists.newArrayList();

        String pinyin = "<" + PinyinUtils.toPinyin(realMatchingText) + ">";

        if (CollectionUtils.isNotEmpty(keywordsIntentList)) {
            // 判断是否命中当前流程分支
            for (String keyword : keywordsIntentList) {
                Pattern pattern = intentBranchKeywordPatternMap.get(keyword);
                if (isPinyinPattern(pattern)) {
                    continue;
                }
                Matcher matcher = pattern.matcher(realMatchingText);
                if (matcher.matches()) {
                    String intentBranchName = "";
                    IntentBranchPO tempIntentBranch = keywordsBranchMap.get(keyword);
                    if (Objects.nonNull(tempIntentBranch)) {
                        intentBranchName = tempIntentBranch.getName();
                    }

                    // 因为 pattern 本身已按长度排序，取第一个匹配到的作为结果
                    if (Objects.isNull(targetKnowledgePattern)) {
                        intentBranchPO = tempIntentBranch;
                        targetKnowledgePattern = pattern;
                        targetBranchKeyword = keyword;
                        targetBranchKeywordText = matcher.group(1);
                    }

                    // 将所有匹配到的结果放入备选列表，用于和算法结果比较
                    IntentBO intentBO = new IntentBO();
                    intentBO.setName(intentBranchName);
                    intentBO.setConfidence("1.0");
                    intentBO.setSim_sentence(matcher.group(1));
                    intentBOList.add(intentBO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(keywordsIntentList) && Objects.isNull(targetKnowledgePattern)) {
            for (String keyword : keywordsIntentList) {
                Pattern pattern = intentBranchKeywordPatternMap.get(keyword);
                if (isPinyinPattern(pattern)) {
                    Matcher matcher = pattern.matcher(pinyin);
                    if (matcher.matches()) {
                        String intentBranchName = "";
                        IntentBranchPO tempIntentBranch = keywordsBranchMap.get(keyword);
                        if (Objects.nonNull(tempIntentBranch)) {
                            intentBranchName = tempIntentBranch.getName();
                        }

                        if (Objects.isNull(targetKnowledgePattern)) {
                            intentBranchPO = tempIntentBranch;
                            targetKnowledgePattern = pattern;
                            targetBranchKeyword = keyword;
                            targetBranchKeywordText = matcher.group(1);
                        }

                        // 将所有匹配到的结果放入备选列表，用于和算法结果比较
                        IntentBO intentBO = new IntentBO();
                        intentBO.setName(intentBranchName);
                        intentBO.setConfidence("1.0");
                        intentBO.setSim_sentence(matcher.group(1));
                        intentBOList.add(intentBO);
                    }
                }
            }
        }
        if (Objects.isNull(intentBranchPO)) {
            logger.info("意向分支正则匹配，无结果");
            return Optional.empty();
        }

        logger.info("意向分支正则匹配，关键词={}，正则表达式={}，匹配文本={}，备选列表={}", targetBranchKeyword, targetKnowledgePattern.pattern(), targetBranchKeywordText, JsonUtils.object2String(intentBOList));

        PredictResult result = new PredictResult();
        result.setAlgorithmType(AlgorithmTypeEnum.INTENT_BRANCH);
        result.setPredictType(PredictTypeEnum.REGEX);
        result.setIntentBranchPO(intentBranchPO);
        result.setKeyword(targetBranchKeyword);
        result.setPattern(targetKnowledgePattern);
        result.setMatchText(targetBranchKeywordText);

        PredictResultWrapper predictResultWrapper = new PredictResultWrapper();
        predictResultWrapper.setPredictResult(result);
        predictResultWrapper.setIntentBOList(intentBOList);
        return Optional.of(predictResultWrapper);
    }

    /**
     * 问答知识关键词匹配
     * - 命中多个关键词时取最长的
     * - 所有匹配命中的结果放到intentBOList里，用于与算法比较
     * - 关键词优先，未匹配到关键词则进行拼音匹配
     */
    private Optional<PredictResultWrapper> predictKnowledgeByRegex(String realMatchingText,
                                                                   Map<Pattern, RobotKnowledgePO> keyWord2KnowledgeMap,
                                                                   Map<Pattern, RobotKnowledgePO> pinyinKeyWord2KnowledgeMap,
                                                                   Set<String> excludeKnowledgeIdSet) {

        // 正则匹配的Pattern
        Pattern targetKnowledgePattern = null;
        // 匹配到的关键词（没有正则的）
        String targetKnowledgeKeyword = null;
        // 匹配到的内容
        String targetKnowledgeKeywordText = null;
        // 匹配命中的问答知识对象
        RobotKnowledgePO predictKnowledge = null;
        // 匹配命中的对象列表，用于与算法比较
        List<IntentBO> intentBOList = Lists.newArrayList();

        String pinyin = "<" + PinyinUtils.toPinyin(realMatchingText) + ">";

        if (MapUtils.isNotEmpty(keyWord2KnowledgeMap)) {
            // 判断是否命中问答知识
            for (Pattern pattern : keyWord2KnowledgeMap.keySet()) {
                if (isPinyinPattern(pattern)) {
                    continue;
                }
                RobotKnowledgePO knowledge = keyWord2KnowledgeMap.get(pattern);
                if (excludeKnowledgeIdSet.contains(knowledge.getId())) {
                    continue;
                }
                Matcher matcher = pattern.matcher(realMatchingText);
                if (matcher.matches()) {
                    String knowledgeTitle = "";
                    RobotKnowledgePO tempKnowledge = keyWord2KnowledgeMap.get(pattern);
                    if (Objects.nonNull(tempKnowledge)) {
                        knowledgeTitle = tempKnowledge.getTitle();
                    }

                    // 因为 pattern 本身已按长度排序，取第一个匹配到的作为结果
                    if (Objects.isNull(targetKnowledgePattern)) {
                        predictKnowledge = tempKnowledge;
                        targetKnowledgePattern = pattern;
                        targetKnowledgeKeyword = pattern.pattern();
                        targetKnowledgeKeyword = targetKnowledgeKeyword.substring(3, targetKnowledgeKeyword.length() - 3);
                        targetKnowledgeKeywordText = matcher.group(1);
                    }

                    // 将所有匹配到的结果放入备选列表，用于和算法结果比较
                    IntentBO intentBO = new IntentBO();
                    intentBO.setName(knowledgeTitle);
                    intentBO.setConfidence("1.0");
                    intentBO.setSim_sentence(matcher.group(1));
                    intentBOList.add(intentBO);
                }
            }
        }

        if (MapUtils.isNotEmpty(pinyinKeyWord2KnowledgeMap) && Objects.isNull(targetKnowledgePattern)) {
            for (Pattern pattern : this.dialogFlowInfoBO.getKnowledgePinyinRegexMap().keySet()) {
                if (isPinyinPattern(pattern)) {
                    RobotKnowledgePO knowledge = this.dialogFlowInfoBO.getKnowledgePinyinRegexMap().get(pattern);
                    if (excludeKnowledgeIdSet.contains(knowledge.getId())) {
                        continue;
                    }
                    Matcher matcher = pattern.matcher(pinyin);
                    if (matcher.matches()) {
                        String knowledgeTitle = "";
                        RobotKnowledgePO tempKnowledge = pinyinKeyWord2KnowledgeMap.get(pattern);
                        if (Objects.nonNull(tempKnowledge)) {
                            knowledgeTitle = tempKnowledge.getTitle();
                        }

                        if (Objects.isNull(targetKnowledgePattern)) {
                            predictKnowledge = tempKnowledge;
                            targetKnowledgePattern = pattern;
                            targetKnowledgeKeyword = pattern.pattern();
                            targetKnowledgeKeyword = targetKnowledgeKeyword.substring(3, targetKnowledgeKeyword.length() - 3);
                            targetKnowledgeKeywordText = matcher.group(1);
                        }

                        // 将所有匹配到的结果放入备选列表，用于和算法结果比较
                        IntentBO intentBO = new IntentBO();
                        intentBO.setName(knowledgeTitle);
                        intentBO.setConfidence("1.0");
                        intentBO.setSim_sentence(matcher.group(1));
                        intentBOList.add(intentBO);
                    }
                }
            }
        }
        if (Objects.isNull(predictKnowledge)) {
            logger.info("问答知识正则匹配，无结果");
            return Optional.empty();
        }

        logger.info("问答知识正则匹配，关键词={}，正则表达式={}，匹配文本={}，备选列表={}", targetKnowledgeKeyword, targetKnowledgePattern.pattern(), targetKnowledgeKeywordText, JsonUtils.object2String(intentBOList));

        PredictResult result = new PredictResult();
        result.setAlgorithmType(AlgorithmTypeEnum.KNOWLEDGE);
        result.setPredictType(PredictTypeEnum.REGEX);
        result.setRobotKnowledgePO(predictKnowledge);
        result.setKeyword(targetKnowledgeKeyword);
        result.setPattern(targetKnowledgePattern);
        result.setMatchText(targetKnowledgeKeywordText);

        PredictResultWrapper predictResultWrapper = new PredictResultWrapper();
        predictResultWrapper.setPredictResult(result);
        predictResultWrapper.setIntentBOList(intentBOList);
        return Optional.of(predictResultWrapper);
    }



    private boolean isWaitUserSayFinish() {
        return Objects.nonNull(waitUserSayFinishTimeout);
    }

    /**
     * 用户说完话处理
     *
     * @param userSayText 语音识别返回结果xxx
     */
    private void processUserSayFinished(String realMatchingText, String userSayText, double aiProgress) {
        metrics.userSayFinishCount.incrementAndGet();
        // 执行asr 模型纠错
        // 用户语义判断
        Optional<CallEventLogPO> log = eventLogProcessor.createEventLog(CallEventTypeEnums.USER_INPUT_FINISH);
        log.ifPresent(eventLogProcessor::updatePlayProgressState);
        userSayText = doHotWordReplace(userSayText);
        realMatchingText = doHotWordReplace(realMatchingText);
        keyWordDetection(realMatchingText, userSayText, aiProgress, log);
    }

    private String doAsrErrCorrection(String realMatchingText) {
        try {
            if (CollectionUtils.isNotEmpty(asrContext.getAsrCorrectionWhiteList())) {
                for (String text : asrContext.getAsrCorrectionWhiteList()) {
                    if (realMatchingText.contains(text)) {
                        return realMatchingText;
                    }
                }
            }

            String result = asrErrCorrectionService.requestAsrErrorCorrection(dialogFlowInfoBO.getDialogFlowId(), realMatchingText, asrContext.getAsrCorrectionThreshold());
            if (!realMatchingText.equals(result)) {
                logger.info("执行asr纠错模型进行文本纠正, 纠错前=[{}], 纠错后=[{}]", realMatchingText, result);
            }
            return result;
        } catch (Exception e) {
            logger.error("执行ASR纠错异常", e);
            return realMatchingText;
        }
    }

    /**
     * 关键词检测
     *
     * @param userSayText 用户说话的文本
     */
    public void keyWordDetection(String realMatchingText, String userSayText, Double aiProgress, Optional<CallEventLogPO> eventLog) {
        eventLog.ifPresent(log -> {
            log.setText(userSayText);
        });
        // 匹配到的分支关键词
        String targetBranchKeyword = "";
        // 匹配到的分支的关键词（没有正则的）
        String targetBranchKeywordText = "";

        PredictResult intentBranchPredictResult = null;
        PredictResult knowledgePredictResult = null;

        String pinyin = "<" + PinyinUtils.toPinyin(realMatchingText) + ">";
        logger.debug("用户文本转换出的拼音 = [{}]", pinyin);

        // 当前流程和节点
        DialogFlowStepListDTO currStep = this.matchingStepInfoManager.getStep();
        DialogFlowNodeDTO dialogFlowNode = currStep.getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());
        logger.debug("当前节点信息，stepId={}, nodeId={}, nodeName={}", dialogFlowNode.getDialogFlowStepId(), dialogFlowNode.getId(), dialogFlowNode.getName());
        // 关键词和分支的反射关系
        Map<String, IntentBranchPO> keywordsBranchMap = Maps.newHashMap();
        // 情况当前节点的所有分支名字
        if (!this.dialogManagerNew.isAiWait() || !this.dialogManagerNew.isAiWait2()) {
            if (DialogFlowNodeTypeEnum.INFO.equals(dialogFlowNode.getType())) {
                // 采集信息
                DialogFlowInfoNodeDTO infoNodeDTO = (DialogFlowInfoNodeDTO) dialogFlowNode;
                // 采集成功分支
                IntentBranchPO successBranch = null;
                for (String key : infoNodeDTO.getIntentBranchToChildrenNode().keySet()) {
                    if (!"default".equals(key)) {
                        successBranch = dialogFlowInfoBO.getIntentBranchMap().get(key);
                    }
                }
                if (CollectInfoTypeEnum.VOICE.equals(infoNodeDTO.getCollectType())) {
                    // 语音收集, 调用算法接口
                    Optional<Set<String>> valueOp = propertyCollectionService.collectFromTextRuntime(realMatchingText, infoNodeDTO, dialogFlowInfoBO.getEntityMap(), dialogFlowInfoBO.getGlobalEntityIgnoreRegexList(), dialogFlowInfoBO.getSystemEntityExtraConfigMap());
                    // 采集成功
                    if (valueOp.isPresent()) {
                        Set<String> value = valueOp.get();
                        String debugLog = DebugLogHelper.collectInfoSuccess(infoNodeDTO.getFieldId(), value, this.isNeedMerge);
                        logger.info(debugLog);
                        targetBranchKeyword = realMatchingText;
                        targetBranchKeywordText = realMatchingText;
                        keywordsBranchMap.put(targetBranchKeyword, successBranch);

                        if (successBranch != null) {
                            successBranch.setName("收集成功");
                            successBranch.setCategory(IntentBranchCategoryEnum.COLLECT_DATA);
                            intentBranchPredictResult = new PredictResult();
                            intentBranchPredictResult.setIntentBranchPO(successBranch);
                            intentBranchPredictResult.setConfidence(1.0);
                            intentBranchPredictResult.setKeyword(targetBranchKeyword);
                            intentBranchPredictResult.setPredictType(PredictTypeEnum.REGEX);
                            intentBranchPredictResult.setIsRegexMatch(true);
                            intentBranchPredictResult.setMatchText(realMatchingText);
                            intentBranchPredictResult.setAlgorithmType(AlgorithmTypeEnum.INTENT_BRANCH);
                        }

                        collectInfo(infoNodeDTO, value);
                    }
                }
                if (CollectInfoTypeEnum.BUTTON.equals(infoNodeDTO.getCollectType()) && !dtmfManager.isWorking()) {
                    // 按键收集: 从问答知识跳转回来时dtmfManager未启动, 此时应该开启dtmfManager
                    // 语音收集节点会将用户问答知识的回复作为收集节点的回复, 不需要特殊处理
                    if (collectCount > infoNodeDTO.getRepeatCollectTimes()) {
                        collectFailed(infoNodeDTO, realMatchingText, aiProgress, infoNodeDTO.getPressTime(), false);
                    } else {
                        dtmfManager.start(infoNodeDTO.getPressTime().longValue());
                        dialogManagerNew.repeatCurrentNode(userSayText, "返回按键收集节点", isNeedMerge);
                    }
                    return;
                }
            } else if (!dialogFlowNode.canLink()) {
                // 主动节点关键词Map
                // 获取当前节点的分支跳转Map, key 为关键词，value为nextNodeIndex
                Map<String, Integer> definiteKeywordsIntentMap = Maps.newHashMap();
                Map<String, Integer> negativeKeywordsIntentMap = Maps.newHashMap();
                Map<String, Integer> declineKeywordsIntentMap = Maps.newHashMap();
                Map<String, Integer> keywordsIntentMap = Maps.newHashMap();
                Map<String, Pattern> intentBranchKeywordPatternMap = Maps.newHashMap();
                DialogFlowChatNodeDTO chatNode = (DialogFlowChatNodeDTO) dialogFlowNode;

                // 关键词和分支的UID对应的Map
                Map<String, Integer> branches = chatNode.getIntentBranchToChildrenNode();
                logger.debug("当前节点分支映射信息, branches={}", branches);
                // 分支属性迭代
                for (Map.Entry<String, Integer> entry : branches.entrySet()) {
                    if (entry.getKey().equals("default") || Integer.valueOf(entry.getKey())<=0) {
                        continue;
                    }
                    IntentBranchPO branch = this.dialogFlowInfoBO.getIntentBranchMap().get(entry.getKey());
                    // 把当前节点几个分支的keyword Pattern的Map合并成当前节点的大的Map
                    intentBranchKeywordPatternMap.putAll(branch.getIntentBranchKeywordPatternMap());
                    // 条件属性
                    IntentBranchCategoryEnum category = branch.getCategory();
                    switch (category) {
                        case DEFINITE:
                            // 肯定
                            for (Map.Entry<String, Pattern> e : branch.getIntentBranchKeywordPatternMap().entrySet()) {
                                // key为keyword，value是当前命中关键词后跳到的节点的index
                                definiteKeywordsIntentMap.put(e.getKey(), entry.getValue());
                                keywordsBranchMap.put(e.getKey(), branch);
                            }
                            break;
                        case DECLINE:
                            // 拒绝
                            for (Map.Entry<String, Pattern> e : branch.getIntentBranchKeywordPatternMap().entrySet()) {
                                // key为keyword，value是当前命中关键词后跳到的节点的index
                                negativeKeywordsIntentMap.put(e.getKey(), entry.getValue());
                                keywordsBranchMap.put(e.getKey(), branch);
                            }
                            break;
                        case NEURAL:
                            // 中性
                            for (Map.Entry<String, Pattern> e : branch.getIntentBranchKeywordPatternMap().entrySet()) {
                                // key为keyword，value是当前命中关键词后跳到的节点的index
                                declineKeywordsIntentMap.put(e.getKey(), entry.getValue());
                                keywordsBranchMap.put(e.getKey(), branch);
                            }
                            break;
                        case NEGATIVE:
                            // 否定
                            for (Map.Entry<String, Pattern> e : branch.getIntentBranchKeywordPatternMap().entrySet()) {
                                // key为keyword，value是当前命中关键词后跳到的节点的index
                                declineKeywordsIntentMap.put(e.getKey(), entry.getValue());
                                keywordsBranchMap.put(e.getKey(), branch);
                            }
                            break;
                    }

                }
                //
                TreeSet<String> currKeywordSet = new TreeSet<>(StrLenComparator.getInstance());
                List<String> keywordsIntentList = Lists.newArrayList();
                // 默认回答分支优先级
                if (Objects.nonNull(this.dialogFlowInfoBO.getBranchLevel()) && this.dialogFlowInfoBO.getBranchLevel().size() >= 5) {
                    for (int i = 0; i < 5; i++) {
                        String val = this.dialogFlowInfoBO.getBranchLevel().get(i);
                        if (i%2==1) {
                            if ("&gt".equals(val)) {
                                currKeywordSet.addAll(keywordsIntentMap.keySet());
                                keywordsIntentList.addAll(currKeywordSet);
                                keywordsIntentMap = Maps.newHashMap();
                                currKeywordSet = new TreeSet<>(StrLenComparator.getInstance());
                            }
                        } else {
                            switch (val) {
                                case "DEFINITE":
                                    keywordsIntentMap.putAll(definiteKeywordsIntentMap);
                                    break;
                                case "DECLINE":
                                    keywordsIntentMap.putAll(negativeKeywordsIntentMap);
                                    break;
                                case "NEGATIVE":
                                    keywordsIntentMap.putAll(declineKeywordsIntentMap);
                                    break;
                            }
                        }
                    }
                    // 最后执行一次
                    currKeywordSet.addAll(keywordsIntentMap.keySet());
                    keywordsIntentList.addAll(currKeywordSet);
                } else {
                    keywordsIntentMap.putAll(definiteKeywordsIntentMap);
                    keywordsIntentMap.putAll(negativeKeywordsIntentMap);
                    keywordsIntentMap.putAll(declineKeywordsIntentMap);
                    currKeywordSet.addAll(keywordsIntentMap.keySet());
                    keywordsIntentList.addAll(currKeywordSet);
                }

                intentBranchPredictDataContext.setKeywordsIntentList(keywordsIntentList);
                intentBranchPredictDataContext.setIntentBranchKeywordPatternMap(intentBranchKeywordPatternMap);
                intentBranchPredictDataContext.setKeywordsBranchMap(keywordsBranchMap);
                GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
                if (globalAssignInfo != null) {
                    logger.info("当前节点需要重复采集, 不再进行分支预测, globalAssignInfo={}", globalAssignInfo);
                } else {
                    // Fix tb bug VM-4904 - 当节点不允许打断，但允许跳转到指定知识时，如果存在同名的分支和知识，会在后面的解冲突中优先选择分支
                    // 当节点不可打断，但允许跳转问答知识，只需要进行问答知识的预测逻辑，不需要进行分支的预测逻辑，直接返回预测结果为空
                    boolean predictIntentBranch = true;
                    // 仅限节点生效
                    if (Objects.isNull(knowledgeManagerInfo.getKnowledge()) && aiProgress > 0 && aiProgress < 100) {
                        Boolean nodeInterruptable = dialogFlowNode.getInteruptable();
                        if (Objects.isNull(nodeInterruptable)) {
                            nodeInterruptable = !dialogFlowNode.canLink();
                        }
                        Integer interruptableThreshold = dialogFlowNode.getInterruptableThreshold();
                        boolean progressThresholdMet = false;
                        if (BooleanUtils.isFalse(nodeInterruptable)
                                    && BooleanUtils.isTrue(dialogFlowNode.getEnableInterruptableThreshold())
                                    && Objects.nonNull(interruptableThreshold)) {
                            progressThresholdMet = aiProgress >= interruptableThreshold;
                        }
                        if (BooleanUtils.isNotTrue(progressThresholdMet) && CollectionUtils.isNotEmpty(dialogFlowNode.getInterruptableKnowledgeIdList())) {
                            predictIntentBranch = false;
                            logger.info("不可打断节点判断允许跳转问答知识，不再进行分支预测");
                        }
                    }
                    if (predictIntentBranch) {
                        intentBranchPredictResult = processPredict(realMatchingText, AlgorithmTypeEnum.INTENT_BRANCH, Collections.emptySet(), eventLog);
                    }
                }
            }
        }

        // 是否去匹配知识库，如果节点配置了不关联知识库那么就不需要做关联
        if (BooleanUtils.isNotTrue(dialogFlowNode.getMismatchKnowledge()) || CollectionUtils.isNotEmpty(dialogFlowNode.getMismatchKnowledgeIdList())) {
            logger.info("需要进行知识库匹配");
            Set<String> excludeKnowledgeSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(dialogFlowNode.getMismatchKnowledgeIdList())) {
                excludeKnowledgeSet.addAll(dialogFlowNode.getMismatchKnowledgeIdList());
            }
            knowledgePredictResult = processPredict(realMatchingText, AlgorithmTypeEnum.KNOWLEDGE, excludeKnowledgeSet, eventLog);
        } else {
            logger.info("不需要进行知识库匹配");
        }

        logger.info("分支/问答检测结果：userSay={}，匹配到分支={}，匹配到知识库={}",
                userSayText,
                Objects.nonNull(intentBranchPredictResult) ? JsonUtils.object2String(intentBranchPredictResult) : null,
                Objects.nonNull(knowledgePredictResult) ? JsonUtils.object2String(knowledgePredictResult) : null);

        eventLogProcessor.updatePredictResult(eventLog, false, intentBranchPredictResult);
        eventLogProcessor.updatePredictResult(eventLog, true, knowledgePredictResult);

        // 分析关键词匹配的分支、知识库
        // 这里插入一个逻辑, 就是是在问答知识的等待用户回答时, 且原节点开启了自动继续播放跳转前的录音, 且进度满足条件, 则置空分支的匹配结果
        if (ignoreBranchPredictResult) {
            logger.info("当前轮次用户输入忽略分支匹配结果");
            intentBranchPredictResult = null;
        }
        if (inaudibleAndRepeatMode) {
            logger.info("当前处于听不清重复上一句状态中, 忽略分支匹配结果");
            processInaudibleAndRepeat(realMatchingText, userSayText, knowledgePredictResult, aiProgress, currStep, eventLog);
            eventLog.ifPresent(eventLogProcessor::updateStepNodeState);
        } else {
            analyzeKeyWordDetection(realMatchingText, userSayText, intentBranchPredictResult, knowledgePredictResult, aiProgress, currStep, eventLog);
            eventLog.ifPresent(eventLogProcessor::updateStepNodeState);
        }
    }

    private GlobalAssignInfo checkCurrentIsWaitGlobalAssign(DialogFlowStepListDTO currStep) {
        try {
            DialogFlowNodeDTO dialogFlowNodeDTO = currStep.getFlattenTreeNodeList().get(matchingStepInfoManager.getNodeIndex());
            if (CollectionUtils.isNotEmpty(globalAssignList)) {
                for (GlobalAssignInfo globalAssignInfo : globalAssignList) {
                    if (globalAssignInfo.getStepId().equals(currStep.getId())
                            && globalAssignInfo.getNodeId().equals(dialogFlowNodeDTO.getId())
                            && globalAssignInfo.getNodeLabel().equals(dialogFlowNodeDTO.getLabel())) {
                        return globalAssignInfo;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("检查全局赋值状态异常", e);
        }

        return null;
    }

    private GlobalAssignInfo checkCurrentIsRepeatAssign(DialogFlowStepListDTO currStep) {
        GlobalAssignInfo globalAssignInfo = checkCurrentIsWaitGlobalAssign(currStep);
        if (globalAssignInfo != null && repeatAssignSet.contains(Tuple.of(globalAssignInfo.getStepId(), globalAssignInfo.getNodeLabel()))) {
            return globalAssignInfo;
        }
        return null;
    }

    private void removeRepeatAssign(GlobalAssignInfo globalAssignInfo) {
        if (globalAssignInfo == null) {
            return ;
        }
        logger.info("删除重复执行动态赋值动作:{}", globalAssignInfo);
        repeatAssignSet.remove(Tuple.of(globalAssignInfo.getStepId(), globalAssignInfo.getNodeLabel()));
    }

    public void removeRepeatAssign(String stepId, String nodeLab) {
        logger.info("删除重复执行动态赋值动作:{}:{}", stepId, nodeLab);
        repeatAssignSet.remove(Tuple.of(stepId, nodeLab));
    }


    private void addRepeatAssignAsk(GlobalAssignInfo globalAssignInfo) {
        logger.info("添加重复赋值反问标志");
        repeatAssignSet.add(Tuple.of(globalAssignInfo.getStepId(), globalAssignInfo.getNodeLabel()));
    }

    /**
     * 命中听不清并重复上一句后, 后续流程
     */
    private void processInaudibleAndRepeat(String realMatchingText, String userSayText,
                                         PredictResult knowledgePredictResult, Double aiProgress, DialogFlowStepListDTO currStep, Optional<CallEventLogPO> eventLog) {
        logger.info("在状态听不清且重复上一句状态下, 判断是否命中了新的问答知识");

        RobotKnowledgePO predictKnowledge = null;
        String predictKnowledgeName = null;
        if (Objects.nonNull(knowledgePredictResult)) {
            predictKnowledge = knowledgePredictResult.getRobotKnowledgePO();
            predictKnowledgeName = predictKnowledge.getTitle();
        }

        if (Objects.nonNull(predictKnowledge)) {
            recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
            keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
            if (!predictKnowledge.getTitle().equals(dialogFlowInfoBO.getInaudibleAndRepeatKnowledge().getTitle())) {
                logger.info("命中问答知识: {}, 非听不清重复上一句逻辑, 播放问答知识答案, 并退出当前状态", predictKnowledge.getTitle());
                this.inaudibleAndRepeatMode = false;
            }
        } else {
            logger.info("未命中问答知识, 执行重复上一句逻辑, 并退出当前状态");
            this.inaudibleAndRepeatMode = false;
            this.dialogManagerNew.repeatInaudibleAndRepeatEvent(userSayText);
        }
        this.matchingStepInfoManager.setPostUserSayMatchingStepInfoBO();
        dialogManagerNew.clearIsBeforeJumpWaitUserAnswer();
        logger.info("执行听不清重复上一句逻辑结束");
    }

    /**
     * 解决意向分支/问答知识的冲突
     */
    private void analyzeKeyWordDetection(String realMatchingText, String userSayText, PredictResult intentBranchPredictResult,
                                         PredictResult knowledgePredictResult, Double aiProgress, DialogFlowStepListDTO currStep, Optional<CallEventLogPO> eventLog) {
        logger.info("开始解分支/问答冲突");
        // 当前节点
        DialogFlowNodeDTO dialogFlowNode = currStep.getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());
        logger.info("当前分支信息, stepId={}, nodeId={}, nodeName={}", dialogFlowNode.getDialogFlowStepId(), dialogFlowNode.getId(), dialogFlowNode.getName());
        // 动态变量赋值
        boolean knowledgeAssignment = this.dialogManagerNew.knowledgeShouldAssignment(this.knowledgeManagerInfo.getKnowledge());
        RobotKnowledgePO knowledge = this.knowledgeManagerInfo.getKnowledge();
        if (Objects.nonNull(knowledge) && knowledgeAssignment) {
            // 问答知识采集
            this.dialogManagerNew.detectionSpeechAssignment(realMatchingText, knowledge);
        } else {
            // 在流程中采集
            boolean assignSuccess = this.dialogManagerNew.detectionSpeechAssignment(realMatchingText, dialogFlowNode);
            GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
            if (!assignSuccess) {
                if (globalAssignInfo != null) {
                    assignSuccess = dialogManagerNew.checkAssignSucceeded(globalAssignInfo.getDynamicAssignmentPO());
                    logger.info("检查全局赋值状态={}, 全局赋值信息={}", assignSuccess, globalAssignInfo);
                    if (assignSuccess) {
                        // 成功了, 就不需要重复了
                        removeRepeatAssign(globalAssignInfo);
                    }
                }
            }

            if (assignSuccess) {
                String debugLog = DebugLogHelper.detectionToCollectionBranch(this.isNeedMerge);
                DialogFlowChatNodeDTO chatNodeDTO = (DialogFlowChatNodeDTO) dialogFlowNode;
                IntentBranchPO collectionIntentBranch = this.dialogFlowInfoBO.getCollectionIntentBranch();
                if (Objects.nonNull(collectionIntentBranch)){
                    Integer nextNodeIndex = chatNodeDTO.getIntentBranchToChildrenNode().get(collectionIntentBranch.getUid());
                    if (Objects.nonNull(nextNodeIndex)) {
                        this.matchingStepInfoManager.setNodeIndex(nextNodeIndex);
                        this.dialogManagerNew.keyWordDetectionToBranch(this.dialogFlowInfoBO.getCollectionIntentBranch(), userSayText, debugLog, this.isNeedMerge, aiProgress);
                        return;
                    }
                }
            }

            // 执行全局赋值操作
            if (CollectionUtils.isNotEmpty(globalAssignList))  {
                logger.info("开始执行全局实体提取操作");
                doGlobalAssign(userSayText);
                logger.info("全局实体提取操作结束");
            }

            // 当前节点是否开启了全局采集信息
            if (this.dialogManagerNew.isEnableGlobalAssign(dialogFlowNode)) {
                globalAssignInfo = new GlobalAssignInfo();
                globalAssignInfo.setDynamicAssignmentPO(dialogFlowNode.getDynamicAssignment());
                globalAssignInfo.setNodeLabel(dialogFlowNode.getLabel());
                globalAssignInfo.setNodeId(dialogFlowNode.getId());
                globalAssignInfo.setStepId(currStep.getId());
                globalAssignInfo.setStepName(currStep.getName());
                globalAssignList.add(globalAssignInfo);
                logger.info("创建新的全局采集赋值节点:{}", globalAssignInfo);
            }
        }

        // 匹配到的分支和知识库
        IntentBranchPO intentBranch = null;
        RobotKnowledgePO predictKnowledge = null;
        String intentBranchName = null;
        String predictKnowledgeName = null;
        if (Objects.nonNull(intentBranchPredictResult)) {
            intentBranch = intentBranchPredictResult.getIntentBranchPO();
            intentBranchName = intentBranch.getName();
        }
        if (Objects.nonNull(knowledgePredictResult)) {
            predictKnowledge = knowledgePredictResult.getRobotKnowledgePO();
            predictKnowledgeName = predictKnowledge.getTitle();
        }

        // 记录详细的匹配过程
        try {
            StringBuilder detection = new StringBuilder(String.format("%s&当前AI进度=%s，流程类型=%s，流程名字=%s。", userSayText, DecimalFormatUtils.format(aiProgress),
                    DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW.equals(currStep.getType())?"主流程":"问答知识流程", currStep.getName()));
            // 算法的请求ID
            String requestId = null;
            if (Objects.nonNull(intentBranchPredictResult)) {
                if (Objects.nonNull(this.testOneNodeVO)) {
                    this.testOneNodeVO.setKeyWordForKnowledgeName(intentBranchName);
                    this.testOneNodeVO.setKeyWordForKnowledgeKeyWord(intentBranchPredictResult.getKeyword());
                }
                detection.append(DebugLogHelper.predictDetail(intentBranchPredictResult));
                requestId = intentBranchPredictResult.getRequestId();
            }
            if (Objects.nonNull(knowledgePredictResult)) {
                if (Objects.nonNull(this.testOneNodeVO)) {
                    this.testOneNodeVO.setKeyWordForKnowledgeName(predictKnowledgeName);
                    this.testOneNodeVO.setKeyWordForKnowledgeKeyWord(knowledgePredictResult.getKeyword());
                }
                detection.append(DebugLogHelper.predictDetail(knowledgePredictResult));
                if (StringUtils.isEmpty(requestId)) {
                    requestId = knowledgePredictResult.getRequestId();
                }
            }
            if (StringUtils.isNotEmpty(requestId)) {
                detection.append("requestId: ").append(requestId);
            }
            keyWordDetectionPO = new KeyWordDetectionPO(KeyWordDetectionEnum.USER, detection.toString());
            dialogManagerNew.recordKeyWordDetection(keyWordDetectionPO);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]记录详细的匹配过程失败", e);
        }

        // 如果命中的是问答知识，当前流程是否存在答案
        Integer answerIndex = hasAnswer(predictKnowledge);
        if (Objects.isNull(answerIndex)) {
            logger.info("匹配到问答知识={}，当前流程无答案", JsonUtils.object2String(predictKnowledge));
        }

        // 当置信度高于阈值上限时，取置信度最高的那个
        boolean resolved = true;
        Double upperThreshold = dialogFlowInfoBO.getKnowledgeUpperThreshold();
        if (Objects.nonNull(intentBranch)
                    && PredictTypeEnum.ALGORITHM.equals(intentBranchPredictResult.getPredictType())
                    && intentBranchPredictResult.getConfidence() > upperThreshold
                    && Objects.nonNull(predictKnowledge)
                    && PredictTypeEnum.ALGORITHM.equals(knowledgePredictResult.getPredictType())
                    && knowledgePredictResult.getConfidence() > upperThreshold) {
            if (intentBranchPredictResult.getConfidence() > knowledgePredictResult.getConfidence()) {
                // 走分支
                logger.info("置信度高于阈值上限，意向分支置信度={}，问答知识置信度={}，走意向分支", intentBranchPredictResult.getConfidence(), knowledgePredictResult.getConfidence());
                recordPredictLog(realMatchingText, intentBranchPredictResult, eventLog);
                keyWordDetectionToBranch(userSayText, intentBranchPredictResult, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
            } else if (intentBranchPredictResult.getConfidence() < knowledgePredictResult.getConfidence()) {
                // 走问答知识
                logger.info("置信度高于阈值上限，意向分支置信度={}，问答知识置信度={}，走问答知识", intentBranchPredictResult.getConfidence(), knowledgePredictResult.getConfidence());
                recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
                keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
            } else {
                if (Objects.equals(intentBranchName, predictKnowledgeName)) {
                    logger.info("置信度高于阈值上限，意向分支置信度={}，问答知识置信度={}，置信度相等，名称相同，走意向分支", intentBranchPredictResult.getConfidence(), knowledgePredictResult.getConfidence());
                    recordPredictLog(realMatchingText, intentBranchPredictResult, eventLog);
                    keyWordDetectionToBranch(userSayText, intentBranchPredictResult, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
                } else {
                    logger.info("置信度高于阈值上限，意向分支置信度={}，问答知识置信度={}，置信度相等，名称不同，走问答知识", intentBranchPredictResult.getConfidence(), knowledgePredictResult.getConfidence());
                    recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
                    keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
                }
            }
        } else if (Objects.nonNull(intentBranch)
                           && PredictTypeEnum.ALGORITHM.equals(intentBranchPredictResult.getPredictType())
                           && intentBranchPredictResult.getConfidence() > upperThreshold) {
            // 走分支
            logger.info("置信度高于阈值上限，意向分支置信度={}，走意向分支", intentBranchPredictResult.getConfidence());
            recordPredictLog(realMatchingText, intentBranchPredictResult, eventLog);
            keyWordDetectionToBranch(userSayText, intentBranchPredictResult, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
        } else if (Objects.nonNull(predictKnowledge)
                           && PredictTypeEnum.ALGORITHM.equals(knowledgePredictResult.getPredictType())
                           && knowledgePredictResult.getConfidence() > upperThreshold) {
            // 走问答知识
            logger.info("置信度高于阈值上限，问答知识置信度={}，走问答知识", knowledgePredictResult.getConfidence());
            recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
            keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
        } else {
            resolved = false;
        }

        if (resolved) {
            logger.info("算法置信度高于阈值上限时，取置信度最高项");
            this.matchingStepInfoManager.setPostUserSayMatchingStepInfoBO();
            dialogManagerNew.clearIsBeforeJumpWaitUserAnswer();
            logger.info("解分支/问答冲突结束");
            return;
        }

        // 当置信度低于阈值上限
        if (Objects.nonNull(intentBranch) && answerIndex != null) {
            // 通知回访做一个特殊逻辑，不进行强制第一个作为兜底
            // 同时命中分支和知识库
            if (answerIndex < 0 || Objects.equals(intentBranchName, predictKnowledgeName) || StringUtils.equals(intentBranchPredictResult.getKeyword(), knowledgePredictResult.getKeyword())) {
                // 在问答知识流程中，命中分支或者关键词相同，走分支；
                recordPredictLog(realMatchingText, intentBranchPredictResult, eventLog);
                keyWordDetectionToBranch(userSayText, intentBranchPredictResult, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
            } else {
                // 关键词不相同，走问答知识（流程）
                recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
                keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
            }
        } else if (Objects.nonNull(intentBranch)) {
            // 只命中分支，走分支
            recordPredictLog(realMatchingText, intentBranchPredictResult, eventLog);
            keyWordDetectionToBranch(userSayText, intentBranchPredictResult, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
        } else if (answerIndex != null && answerIndex >= 0) {
            // 只命中知识库，走知识库
            recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
            keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
        } else {
            // 如果是在等待用户回，而且没有命中问答知识的时候
            if (this.dialogManagerNew.isAiWait()) {
                // 在AI等待中：意图不明，继续等待
                keyWordDetectionToAiSay(userSayText, DebugLogHelper.unclearMeaningAndContinueWait(this.isNeedMerge));
                return;
            }
            if (this.dialogManagerNew.isAiWait2()) {
                // 回到原主动流程
                knowledgeBackRepeat(userSayText);
                return;
            }

            if (DialogFlowNodeTypeEnum.INFO.equals(dialogFlowNode.getType()) && CollectInfoTypeEnum.VOICE.equals(((DialogFlowInfoNodeDTO) dialogFlowNode).getCollectType())) {
                // 信息采集节点采集失败
                collectFailed((DialogFlowInfoNodeDTO) dialogFlowNode, userSayText, aiProgress, null, isNeedMerge);
                return;
            } else {
                if (Objects.nonNull(intentBranch) && Objects.nonNull(knowledge)) {
                    // 同时命中分支和知识库
                    if (Objects.equals(intentBranchName, predictKnowledgeName)) {
                        // 在问答知识流程中，命中分支或者关键词相同，走分支；
                        intentDetectionToBranch(userSayText, intentBranch, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress, intentBranchPredictResult);
                    } else {
                        // 关键词不相同，走问答知识（流程）
                        keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
                    }
                    return;
                } else if (Objects.nonNull(knowledge)) {
                    if (StringUtils.isNotBlank(predictKnowledgeName)) {
                        // 问法识别命中
                        recordPredictLog(realMatchingText, knowledgePredictResult, eventLog);
                        keyWordDetectionToKnowledgeLib(currStep, knowledgePredictResult, userSayText, aiProgress);
                        dialogManagerNew.clearIsBeforeJumpWaitUserAnswer();
                        return;
                    }
                } else if (Objects.nonNull(intentBranch)) {
                    if (StringUtils.isNotBlank(intentBranchName)) {
                        // 走分支
                        intentDetectionToBranch(userSayText, intentBranch, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress, intentBranchPredictResult);
                        return;
                    }
                }

                // 从普通问答知识的等待返回，且之前主流程的语音播放了<30的，需要再说一遍主流程的语音
                if (aiProgress == 0 && knowledgeManagerInfo.isRepeat()) {
                    knowledgeBackRepeat(userSayText);
                    return;
                }

                // 问法也没命中
                if (aiProgress != ApplicationDialogConstant.DEFAULT_AI_PROGRESS && !this.dialogManagerNew.isAiWait()) {
                    knowledge = this.knowledgeManagerInfo.getKnowledge();
                    if (Objects.nonNull(knowledge) && knowledge.getTitle().equals(this.dialogFlowInfoBO.getSilenceKnowledge().getTitle())) {
                        /**** 都没命中 *****/
                        if (dialogFlowNode instanceof DialogFlowChatNodeDTO
                                && ((DialogFlowChatNodeDTO) dialogFlowNode).getIntentBranchToChildrenNode().containsKey("default")
                                && !dialogManagerNew.isBeforeJumpWaitUserAnswer()) {
                            GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
                            if (globalAssignInfo != null) {
                                doRepeatAssign(currStep,globalAssignInfo, userSayText);
                            } else {
                                // 没有之前一个流程，有default走default分支
                                String debugLog = DebugLogHelper.unclearMeaningAndDefaultBranch(this.isNeedMerge, answerIndex, this.dialogFlowInfoBO.getDialogFlowId(), predictKnowledgeName);
                                keyWordDetectionToDefaultBranch(userSayText, debugLog, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
                            }
                        } else if (dialogFlowNode instanceof DialogFlowLinkNodeDTO) {
                            // 如果是转跳节点
                            backToLinkNode(dialogFlowNode, userSayText);
                        } else {
                            GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
                            if (globalAssignInfo != null) {
                                doRepeatAssign(currStep, globalAssignInfo, userSayText);
                            } else {
                                // 没有之前一个流程，无default走AI无法应答的知识库
                                keyWordDetectionToAiUnknown(userSayText);
                            }
                        }
                    } else {
                        /**** 都没命中，暂停下来是否匹配成功，失败的话继续播放录音，AI还没说完 *****/
                        keyWordDetectionToAiSay(userSayText, DebugLogHelper.unclearMeaningAndContinue(this.isNeedMerge, this.dialogFlowInfoBO.getDialogFlowId()));
                    }
                } else {
                    /**** 都没命中 *****/
                    if (dialogFlowNode instanceof DialogFlowChatNodeDTO
                            && ((DialogFlowChatNodeDTO) dialogFlowNode).getIntentBranchToChildrenNode().containsKey("default")
                            && !dialogManagerNew.isBeforeJumpWaitUserAnswer()) {
                        // 没有之前一个流程，有default走default分支
                        GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
                        if (globalAssignInfo != null) {
                            doRepeatAssign(currStep,globalAssignInfo, userSayText);
                        } else {
                            String debugLog = DebugLogHelper.unclearMeaningAndDefaultBranch(this.isNeedMerge, answerIndex, this.dialogFlowInfoBO.getDialogFlowId(), predictKnowledgeName);
                            keyWordDetectionToDefaultBranch(userSayText, debugLog, (DialogFlowChatNodeDTO) dialogFlowNode, aiProgress);
                        }
                    } else if (dialogFlowNode instanceof DialogFlowLinkNodeDTO) {
                        // 如果是转跳节点
                        backToLinkNode(dialogFlowNode, userSayText);
                    } else {
                        GlobalAssignInfo globalAssignInfo = checkCurrentIsRepeatAssign(currStep);
                        if (globalAssignInfo != null) {
                            doRepeatAssign(currStep, globalAssignInfo, userSayText);
                        } else {
                            // 没有之前一个流程，无default走AI无法应答的知识库
                            keyWordDetectionToAiUnknown(userSayText);
                        }
                    }
                }
            }
        }
        this.matchingStepInfoManager.setPostUserSayMatchingStepInfoBO();
        dialogManagerNew.clearIsBeforeJumpWaitUserAnswer();
        logger.info("解分支/问答冲突结束");
    }

    // 记录关键词/问法预测日志
    private void doRecordPredictLog(String userInput, PredictResult predictResult, Optional<CallEventLogPO> eventLog) {
        Long dialogFlowId = this.dialogFlowInfoBO.getDialogFlowId();
        if (predictResult == null) {
            return;
        }
        if (CommonApplicationConstant.CURR_ENV.isTest() || CommonApplicationConstant.CURR_ENV.isPre()) {
            logger.info("predictResult={}", JsonUtils.object2String(predictResult));
        }

        eventLogProcessor.updateFinalPredictResult(eventLog, predictResult);

        if (!PredictTypeEnum.ALGORITHM.equals(predictResult.getPredictType())) {
            return;
        }
        PredictLogPO predictLog = new PredictLogPO();
        predictLog.setDialogFlowId(dialogFlowId);
        predictLog.setCreateDateTime(LocalDateTime.now());
        predictLog.setUserInput(userInput);
        if (Objects.nonNull(predictResult.getIntentBranchPO())) {
            predictLog.setPredictBranchName(predictResult.getIntentBranchPO().getName());
        }
        if (Objects.nonNull(predictResult.getRobotKnowledgePO())) {
            predictLog.setPredictKnowledgeName(predictResult.getRobotKnowledgePO().getTitle());
        }
        predictLog.setConfidence(predictResult.getConfidence());
        predictLog.setMatchText(predictResult.getMatchText());
        if (Objects.nonNull(predictResult.getAlgorithmType())) {
            predictLog.setAlgorithmType(predictResult.getAlgorithmType().name());
        }
        if (Objects.nonNull(predictResult.getPredictType())) {
            predictLog.setPredictType(predictResult.getPredictType().name());
        }
        algorithmPredictLogList.add(predictLog);
    }

    private void recordPredictLog(String userInput, PredictResult predictResult, Optional<CallEventLogPO> eventLog) {

        // 统计命中次数
        if (Objects.nonNull(predictResult)) {
            if (PredictTypeEnum.ALGORITHM.equals(predictResult.getPredictType())) {
                metrics.algorithmMatchCount.incrementAndGet();
            } else {
                metrics.regexMatchCount.incrementAndGet();
            }
        }

        try {
            doRecordPredictLog(userInput, predictResult, eventLog);
        } catch (Exception e) {
            logger.error("记录预测日志异常", e);
        }
    }

    private void doRepeatAssign(DialogFlowStepListDTO currStep, GlobalAssignInfo globalAssignInfo, String userInput) {
        logger.info("全局动态赋值重复执行一次");
        removeRepeatAssign(globalAssignInfo);
        String debuglog = DebugLogHelper.repeatDoAssign(this.isNeedMerge);
        this.matchingStepInfoManager.setMatchingStepInfoBO(this.matchingStepInfoManager.getWhenFromMainToKnowledgeStepInfoBO());
        this.dialogManagerNew.knowledgeBackRepeat(userInput, debuglog);
    }

    private void doGlobalAssign(String userInput) {
        if (CollectionUtils.isEmpty(globalAssignList)) {
            return;
        }
        try {
            Set<String> executedSet = new HashSet<>();
            globalAssignList.forEach(item -> {
                String key = String.format("%s_%s", item.getDynamicAssignmentPO().getEntityId(), item.getDynamicAssignmentPO().getVariableId());
                if (executedSet.contains(key)) {
                    logger.info("doGlobalAssign:{}-{}, key={}节点动态赋值重复了, 取消本轮次执行", item.getStepName(), item.getNodeLabel(), key);
                    return;
                }
                executedSet.add(key);
                logger.info("doGlobalAssign:开始执行{}-{}节点动态赋值", item.getStepName(), item.getNodeLabel());
                this.dialogManagerNew.detectionSpeechAssignment(userInput, item.getDynamicAssignmentPO());
            });
        } catch (Exception e) {
            logger.error("执行全局变量赋值异常", e);
        }
    }

    /**
     * 问答知识等待用户应答返回重复主流程
     */
    private void knowledgeBackRepeat(String userSayText) {
        setKnowledgeManagerInfo(false, false, true, false, null);
        String debuglog = DebugLogHelper.unclearMeaningAndWaiting(this.isNeedMerge);
        this.matchingStepInfoManager.setMatchingStepInfoBO(this.matchingStepInfoManager.getWhenFromMainToKnowledgeStepInfoBO());
        this.dialogManagerNew.knowledgeBackRepeat(userSayText, debuglog);
    }

    /**
     * 跳转节点打断之后返回
     */
    private void backToLinkNode(DialogFlowNodeDTO dialogFlowNode, String userWords) {
        DialogFlowLinkNodeDTO dialogFlowLinkNodeDTO = (DialogFlowLinkNodeDTO) dialogFlowNode;
        this.dialogManagerNew.backToLinkNode(dialogFlowLinkNodeDTO, userWords, this.isNeedMerge);
    }


    /**
     * 匹配到意图不明确, 且继续放音
     */
    private void keyWordDetectionToAiSay(String userSayText, String debugLog) {
        clearLastLanguageEnvironment();
        this.isAiContinueSay = true;
        this.dialogManagerNew.keyWordDetectionToAiSay(userSayText, debugLog);
        duplicateLastAiDetailIfMergeInput();
    }

    /**
     * 匹配到分支
     */
    private void intentDetectionToBranch(String userWords, IntentBranchPO intentBranch, DialogFlowChatNodeDTO chatNode, Double aiProgress, PredictResult predictResult) {
        recordKnowledgeRejectCount(intentBranch);
        setKnowledgeManagerInfo(false, false, true, false, null);
        clearLastLanguageEnvironment();
        // 匹配到分支，那么就是还在这个流程中
        String debugLog = DebugLogHelper.predictResult(this.isNeedMerge, predictResult);
        int nextNodeIndex = chatNode.getIntentBranchToChildrenNode().get(intentBranch.getUid());
        this.matchingStepInfoManager.setNodeIndex(nextNodeIndex);
        if (!accumulateNegative(intentBranch, userWords, aiProgress)) {
            this.dialogManagerNew.keyWordDetectionToBranch(intentBranch, userWords, debugLog, this.isNeedMerge, aiProgress);
        }
    }

    private void recordKnowledgeRejectCount(IntentBranchPO intentBranch) {
        if (Objects.isNull(intentBranch)
                || Objects.isNull(knowledgeManagerInfo.getKnowledge())
                || Objects.isNull(intentBranch.getCategory())) {
            logger.debug("当前没有上轮命中的问答知识");
            return;
        }

        if (IntentBranchCategoryEnum.DECLINE.equals(intentBranch.getCategory())) {
            // 当前问答知识后续命中拒绝分支
            logger.debug("当前问答知识后续命中拒绝分支, 问答知识={}, 当前分支={}", knowledgeManagerInfo.getKnowledge().getTitle(), intentBranch.getName());
            dialogManagerNew.recordKnowledgeRejectCount(knowledgeManagerInfo.getKnowledge());
        }
    }

    /**
     * 匹配到分支
     */
    public void keyWordDetectionToBranch(String userWords, PredictResult predictResult, DialogFlowChatNodeDTO chatNode, Double aiProgress) {
        IntentBranchPO intentBranch = predictResult.getIntentBranchPO();
        recordKnowledgeRejectCount(intentBranch);
        setKnowledgeManagerInfo(false, false, true, false, null);
        clearLastLanguageEnvironment();
        // 匹配到分支，那么就是还在这个流程中
        logger.info("当前节点分支映射={}", chatNode.getIntentBranchToChildrenNode());
        int nextNodeIndex = chatNode.getIntentBranchToChildrenNode().get(intentBranch.getUid());
        this.matchingStepInfoManager.setNodeIndex(nextNodeIndex);

        String debugLog;
        if (DialogFlowNodeTypeEnum.INFO.equals(chatNode.getType())) {
            debugLog = DebugLogHelper.detectionToCollectSuccess(this.isNeedMerge, predictResult.getMatchText());
        } else {
            debugLog = DebugLogHelper.predictResult(this.isNeedMerge, predictResult);
        }
        keyWordDetectionToBranch(userWords, intentBranch, aiProgress, debugLog);
    }

    /**
     * 匹配到默认分支
     */
    public void keyWordDetectionToDefaultBranch(String userWords, String debugLog, DialogFlowChatNodeDTO chatNode, Double aiProgress) {
        metrics.defaultBranchCount.incrementAndGet();
        if (ignoreBranchPredictResult) {
            // 重新播放当前节点的语音
            ignoreBranchPredictResult = false;
            this.dialogManagerNew.repeatCurrentNode(userWords, "未命中问答知识, 重复播放当前节点", false);
        } else {
            setKnowledgeManagerInfo(false, false, true, false, null);
            clearLastLanguageEnvironment();
            Integer defaultNodeIndex = chatNode.getIntentBranchToChildrenNode().get("default");
            this.matchingStepInfoManager.setNodeIndex(defaultNodeIndex);
            keyWordDetectionToBranch(userWords, null, aiProgress, debugLog);
        }
    }

    /**
     * 走分支逻辑
     */
    private void keyWordDetectionToBranch(String userWords, IntentBranchPO intentBranch, Double aiProgress, String debugLog) {
        if (!accumulateNegative(intentBranch, userWords, aiProgress)) {
            // 重新设置用户无应答时间
            logger.info("匹配到分支，进入分支匹配");
            this.dialogManagerNew.keyWordDetectionToBranch(intentBranch, userWords, debugLog, this.isNeedMerge, aiProgress);
        }
    }

    /**
     * AI无法应答
     */
    public void keyWordDetectionToAiUnknown(String userWords) {
        metrics.aiUnknownCount.incrementAndGet();
        if (ignoreBranchPredictResult) {
            ignoreBranchPredictResult = false;
            this.dialogManagerNew.repeatCurrentNode(userWords, "未命中问答知识, 重复播放当前节点", false);
        } else {
            this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
            String languageEnvironmentId = this.dialogManagerNew.keyWordDetectionToAiUnknow(userWords);
            if (!this.dialogManagerNew.isBeforeJumpWaitUserAnswer()) {
                setKnowledgeManagerInfo(false, false, true, false, this.dialogFlowInfoBO.getAiUnKnownKnowledge());
            }
            // 记录语境
            recordLastLanguageEnvironment(languageEnvironmentId);
        }
    }

    /**
     * 匹配到知识库
     */
    private void keyWordDetectionToKnowledgeLib(DialogFlowStepListDTO currStep, PredictResult knowledgePredictResult, String userWords, Double aiProgress) {
        if (Objects.isNull(knowledgePredictResult)) {
            return;
        }
        RobotKnowledgePO predictKnowledge = knowledgePredictResult.getRobotKnowledgePO();

        GlobalAssignInfo globalAssignInfo = checkCurrentIsWaitGlobalAssign(currStep);

        if (globalAssignInfo != null) {
            addRepeatAssignAsk(globalAssignInfo);
        }

        // 写入debug日志
        String debugLog = DebugLogHelper.predictResult(this.isNeedMerge, knowledgePredictResult);

        // 走AI等待用户处理
        boolean used = dialogFlowInfoBO.getAiWaitKnownKnowledge().isUsed();
        if (used && (predictKnowledge.getTitle().equals(dialogFlowInfoBO.getAiWaitKnownKnowledge().getAiWaitPreKnownKnowledge().getTitle())
                || predictKnowledge.getTitle().equals(dialogFlowInfoBO.getAiWaitKnownKnowledge().getAiWaitBackKnownKnowledge().getTitle()))) {
            predictKnowledge = this.dialogFlowInfoBO.getAiWaitKnownKnowledge().getAiWaitPreKnownKnowledge();
        }
        keyWordDetectionToKnowledge(currStep, predictKnowledge, userWords, aiProgress, debugLog);
    }

    public CallEventLogBatchPO getCallEventLogBatch() {
        List<CallEventLogPO> logList = eventLogProcessor.eventLogList;
        CallEventLogBatchPO result = new CallEventLogBatchPO();
        result.setLogList(logList);
        result.setDialogFlowId(dialogFlowInfoBO.getDialogFlowId());
        logger.debug("eventLog:{}", JsonUtils.object2String(logList));

        return result;
    }

    public WeipinhuiCallRecordExtendInfoBO getWeipinhuiExtendInfo() {
        if (StringUtils.isNotBlank(WEIPINHUI_SHARE_ANSWER_DIALOGFLOW_IDS)
                && WEIPINHUI_SHARE_ANSWER_DIALOGFLOW_IDS.contains(String.valueOf(dialogFlowInfoBO.getDialogFlowId()))) {

            processWeipinhuiLog();

            WeipinhuiCallRecordExtendInfoBO result = new WeipinhuiCallRecordExtendInfoBO();
            List<WeipinhuiCallDetailExtendInfoBO> callDetailExtendInfoList = new ArrayList<>();
            result.setCallDetailExtendInfoList(callDetailExtendInfoList);

            logger.debug("weipinhuiLogList:{}", JsonUtils.object2String(eventLogProcessor.weipinhuiEventLogList));
            long pickupTime = getPickupTime(eventLogProcessor.weipinhuiEventLogList);
            long startPlayTime = getStartPlayTime(eventLogProcessor.weipinhuiEventLogList);
            // 一个详情一个

            logger.debug("getCallDetailInfoBOList:{}", JsonUtils.object2String(aliyunAsrSoundManager.getCallDetailInfoBOList()));

            for (AnalyzeDetail item : aliyunAsrSoundManager.getCallDetailInfoBOList()) {
                CallDetailBO callDetail = (CallDetailBO) item;

                // 为每一个详情添加一个extendInfo
                WeipinhuiCallDetailExtendInfoBO extendInfo = new WeipinhuiCallDetailExtendInfoBO();
                callDetailExtendInfoList.add(extendInfo);
                extendInfo.setCallDetailId(callDetail.getCallDetailId());
                extendInfo.setCustomerHangup(false);
                extendInfo.setType(callDetail.getType());
                // 判断类型
                if (CharacterEnum.ROBOT.equals(callDetail.getType())) {
                    for (WeipinhuiCallEventLog log : eventLogProcessor.weipinhuiEventLogList) {
                        if (callDetail.getAudioPlayIndex() == log.getAudioPlayIndex()
                                && CollectionUtils.isNotEmpty(log.getAudioInfoList())) {
                            long durationMs = 0;
                            for (WeipinhuiCallEventLog.AudioInfo audioInfo : log.getAudioInfoList()) {
                                durationMs += audioInfo.getDuration();
                            }
                            extendInfo.setDuration((int)(durationMs + 999) / 1000);
                            extendInfo.setVariableNameShareAnswerIdMap(log.getVarName2ShareAnswerIdMap());
                            break;
                        }
                    }
                    boolean audioPlayFinish = false;
                    for (WeipinhuiCallEventLog log : eventLogProcessor.weipinhuiEventLogList) {
                        if (callDetail.getAudioPlayIndex() == log.getAudioPlayIndex()
                                && WeipinhuiCallEventLog.TYPE_AI_SAY_END.equals(log.getType())) {
                            audioPlayFinish = true;
                            break;
                        }
                    }
                    extendInfo.setAudioPlayFinish(audioPlayFinish);
                    List<WeipinhuiAudioPlayDetail> audioPlayDetailList = new ArrayList<>();
                    WeipinhuiAudioPlayDetail currentPlayDetail = null;
                    for (WeipinhuiCallEventLog log : eventLogProcessor.weipinhuiEventLogList) {
                        if (callDetail.getAudioPlayIndex() == log.getAudioPlayIndex()) {
                            if (WeipinhuiCallEventLog.TYPE_AI_SAY_BEGIN.equals(log.getType())
                                    || WeipinhuiCallEventLog.TYPE_AI_SAY_RESUME.equals(log.getType())) {
                                if (currentPlayDetail != null) {
                                    audioPlayDetailList.add(currentPlayDetail);
                                }
                                currentPlayDetail = new WeipinhuiAudioPlayDetail();
                                currentPlayDetail.setStartOffset(log.getOffset());
                            } else if (WeipinhuiCallEventLog.TYPE_AI_SAY_END.equals(log.getType())
                                    || WeipinhuiCallEventLog.TYPE_AI_SAY_PAUSE.equals(log.getType())) {
                                if (currentPlayDetail != null) {
                                    currentPlayDetail.setEndOffset(log.getOffset());
                                }
                            }
                        }
                    }
                    if (currentPlayDetail != null) {
                        audioPlayDetailList.add(currentPlayDetail);
                    }
                    extendInfo.setAudioPlayDetailList(
                            audioPlayDetailList.stream()
                                    .filter(aa -> Objects.nonNull(aa.getEndOffset()) && Objects.nonNull(aa.getStartOffset()))
                                    .collect(Collectors.toList())
                    );
                } else {
                    if (Objects.nonNull(callDetail.getRobotKnowledge())) {
                        extendInfo.setIntentName(callDetail.getRobotKnowledge().getTitle());
                    } else if (Objects.nonNull(callDetail.getIntentBranch())) {
                        extendInfo.setIntentName(callDetail.getIntentBranch().getName());
                        extendInfo.setIntentCategory(callDetail.getIntentBranch().getCategory().getDesc());
                    }
                }
            }

            if (isRemoteHangup(eventLogProcessor.weipinhuiEventLogList)) {
                WeipinhuiCallEventLog hangupLog = getRemoteHangupLog(eventLogProcessor.weipinhuiEventLogList);
                for (int i = callDetailExtendInfoList.size() - 1 ; i >= 0; i--) {
                    WeipinhuiCallDetailExtendInfoBO extendInfo = callDetailExtendInfoList.get(i);
                    if (CharacterEnum.ROBOT.equals(extendInfo.getType())) {
                        extendInfo.setCustomerHangup(true);
                        extendInfo.setHangupOffset(hangupLog.getOffset());
                        if (CollectionUtils.isNotEmpty(extendInfo.getAudioPlayDetailList())) {
                            extendInfo.getAudioPlayDetailList().forEach(item -> {
                                if (Objects.isNull(item.getEndOffset())) {
                                    item.setEndOffset(hangupLog.getOffset());
                                }
                            });
                        }
                        break;
                    }
                }
            }

            ShareAnswerHelper.getSceneByDialogFlowId(dialogFlowInfoBO.getDialogFlowId()).ifPresent(scene -> {
                result.setShareAnswerSceneId(scene.getShareAnswerSceneId());
                result.setShareAnswerSceneName(scene.getName());
            });

            return result;
        }
        return null;
    }



    private long getPickupTime(List<WeipinhuiCallEventLog> logList) {
        for (WeipinhuiCallEventLog log : logList) {
            if (WeipinhuiCallEventLog.TYPE_START.equals(log.getType())) {
                return log.getTimestamp();
            }
        }
        return 0;
    }

    private boolean isRemoteHangup(List<WeipinhuiCallEventLog> logList) {
        return getRemoteHangupLog(logList) != null;
    }

    private WeipinhuiCallEventLog getRemoteHangupLog(List<WeipinhuiCallEventLog> logList) {
        for (WeipinhuiCallEventLog log : logList) {
            if (WeipinhuiCallEventLog.TYPE_USER_HANGUP.equals(log.getType())) {
                return log;
            }
        }
        return null;
    }

    private long getStartPlayTime(List<WeipinhuiCallEventLog> logList) {
        for (WeipinhuiCallEventLog log : logList) {
            if (WeipinhuiCallEventLog.TYPE_AI_SAY_BEGIN.equals(log.getType())) {
                return log.getTimestamp();
            }
        }
        return 0;
    }

    private void processWeipinhuiLog() {
        if (CollectionUtils.isNotEmpty(eventLogProcessor.weipinhuiEventLogList)) {
            eventLogProcessor.weipinhuiEventLogList.forEach(log -> {
                int playIndex = log.getAudioPlayIndex();
                TextAudioContentPO textContent = dialogManagerNew.getPlayIndexTextAudioContentMap().get(playIndex);
                if (textContent != null) {

                    // 设置变量信息
                    TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(textContent.getText());
                    Map<String, Long> varNameShareIdMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(splitter.getPlaceholderSet())) {
                        splitter.getPlaceholderSet().forEach(placeholder -> {
                            Long shareAnswerId = aliyunAsrSoundManager.getVariableNameShareAnswerIdMap().get(placeholder);
                            if (Objects.nonNull(shareAnswerId)) {
                                varNameShareIdMap.put(placeholder, shareAnswerId);
                            }
                        });
                    }

                    log.setVarName2ShareAnswerIdMap(varNameShareIdMap);

                    // 设置音频时长
                    RuntimeTextAudioContent runtimeTextAudioContent = runtimeTextAudioContentMap.get(textContent);
                    if (runtimeTextAudioContent != null) {
                        List<WeipinhuiCallEventLog.AudioInfo> audioInfoList = new ArrayList<>();
                        log.setAudioInfoList(audioInfoList);
                        if (CollectionUtils.isNotEmpty(runtimeTextAudioContent.getTextFileSizeList())) {
                            for (Tuple2<String, Integer> tuple2 : runtimeTextAudioContent.getTextFileSizeList()) {
                                WeipinhuiCallEventLog.AudioInfo audioInfo = new WeipinhuiCallEventLog.AudioInfo();
                                audioInfo.setText(tuple2._1);
                                audioInfo.setFileSize(tuple2._2);
                                audioInfo.setDuration(audioInfo.getFileSize() / 16);
                                audioInfoList.add(audioInfo);
                            }
                        }
                    } else {
                        List<WeipinhuiCallEventLog.AudioInfo> audioInfoList = new ArrayList<>();
                        log.setAudioInfoList(audioInfoList);
                        AtomicInteger duration = aliyunAsrSoundManager.getRealTimeAudioPlayIndexDurationMap().get(playIndex);
                        if (Objects.nonNull(duration)) {
                            WeipinhuiCallEventLog.AudioInfo audioInfo = new WeipinhuiCallEventLog.AudioInfo();
                            audioInfo.setDuration(duration.get());
                            audioInfoList.add(audioInfo);
                        }
                    }
                }
            });
        }
    }

    /**
     * 知识库（FAQ）的问法识别
     *
     * @param userWords 用户输入
     * @return 若匹配不到知识点: Optional.empty(); 匹配到知识点: Optional.of(知识点)
     */
    private Optional<PredictResultWrapper> predictKnowledgeByAlgorithm(String userWords, Set<String> excludeKnowledgeIdSet) {
        logger.info("当前问法开关：{}", dialogFlowInfoBO.isEnableAskService());
        logger.info("问法介入字数：{}，用户输入字数：{}", dialogFlowInfoBO.getAskNumber(), StringUtils.length(userWords));
        logger.info("问答知识问法识别：userWords={}", userWords);
        if (dialogFlowInfoBO.isEnableAskService() && StringUtils.length(userWords) >= this.dialogFlowInfoBO.getAskNumber()) {
            logger.info("知识库（FAQ）问法识别：userWords={}", userWords);
            PredictResponse predictResponse = doPredict(dialogFlowInfoBO.getDialogFlowId(), userWords, AlgorithmTrainTypeEnum.KNOWLEDGE);
            logger.info("知识库（FAQ）问法识别：userWords={}, dialogFlowId={},结果={}", userWords, this.dialogFlowInfoBO.getDialogFlowId(), predictResponse);
            if (Objects.nonNull(predictResponse) && Objects.nonNull(predictResponse.getIntent()) && Objects.nonNull(predictResponse.getIntent().getIntent())) {
                IntentBO top1Result = predictResponse.getIntent().getIntent();
                String intent = top1Result.getName();
                double confidence = Double.parseDouble(top1Result.getConfidence());
                Map<String, RobotKnowledgePO> robotKnowledgeMap = this.dialogFlowInfoBO.getRobotKnowledgeMap();
                RobotKnowledgePO knowledge = robotKnowledgeMap.get(intent);
                if (knowledge != null && CollectionUtils.isNotEmpty(excludeKnowledgeIdSet) && excludeKnowledgeIdSet.contains(knowledge.getId())) {
                    logger.info("当前节点设置不允许命中该问答知识:id={}, name={}", knowledge.getId(), knowledge.getTitle());
                    return Optional.empty();
                }
                logger.info("当前问法阈值设置为:[{}, {}]", dialogFlowInfoBO.getKnowledgeLowerThreshold(), dialogFlowInfoBO.getKnowledgeUpperThreshold());
                if (knowledge != null && BigDecimal.valueOf(dialogFlowInfoBO.getKnowledgeLowerThreshold()).compareTo(BigDecimal.valueOf(confidence)) <= 0) {
                    PredictResultWrapper robotKnowledgeConfidenceWrapper = new PredictResultWrapper();
                    PredictResult predictResult = new PredictResult();
                    predictResult.setAlgorithmType(AlgorithmTypeEnum.KNOWLEDGE);
                    predictResult.setPredictType(PredictTypeEnum.ALGORITHM);
                    predictResult.setRobotKnowledgePO(knowledge);
                    predictResult.setKeyword(knowledge.getTitle());
                    predictResult.setMatchText(top1Result.getSim_sentence());
                    predictResult.setConfidence(confidence);
                    predictResult.setRequestId(predictResponse.getRequest_id());
                    robotKnowledgeConfidenceWrapper.setPredictResult(predictResult);
                    List<IntentBO> intentRanking = predictResponse.getIntent().getIntent_ranking();
                    if (CollectionUtils.isNotEmpty(intentRanking)) {
                        intentRanking = intentRanking.stream().filter(item -> robotKnowledgeMap.containsKey(item.getName())).collect(Collectors.toList());
                    }
                    robotKnowledgeConfidenceWrapper.setIntentBOList(intentRanking);
                    return Optional.of(robotKnowledgeConfidenceWrapper);
                } else {
                    logger.info("探意获取FAQ的没有正确匹配");
                }
            }
        }

        return Optional.empty();
    }

    private PredictResponse doPredict(Long dialogFlowId, String text, AlgorithmTrainTypeEnum trainType) {
        boolean isVerbalTraining = this.isVerbalTraining || this.isTextDialog;
        PredictResponse result = null;
        String modelType = dialogFlowInfoBO.getModelType();
        Boolean mixedModel = dialogFlowInfoBO.getMixedModel();
        PredictRequest predictRequest = PredictRequest.builder()
                                                .tenantId(0L)
                                                .robotId(dialogFlowId)
                                                .userInput(text)
                                                .inputMinLength(dialogFlowInfoBO.getAskNumber())
                                                .trainType(trainType)
                                                .snapshotType(SnapshotTypeEnum.PUBLISHED)
                                                .isVerbalTraining(isVerbalTraining)
                                                .modelType(modelType)
                                                .build();
        if (BooleanUtils.isTrue(mixedModel)) {
            logger.info("调用混合模型");
            predictRequest.setTrainType(AlgorithmTrainTypeEnum.MIXED_MODEL);
            result = YiwiseAskHelper.predict(predictRequest);
        } else if (!ModelTypeEnum.CUSTOMIZED.name().equals(modelType)) {
            logger.info("当前话术算法模型请求到活动通知模型：{}", modelType);
            result = YiwiseAskHelper.getNotifyIntentQuestion(text, modelType);
        } else {
            result = YiwiseAskHelper.predict(predictRequest);
        }
        logger.info("原始结果={}", JsonUtils.object2String(result));
        if (AlgorithmTrainTypeEnum.KNOWLEDGE.equals(trainType)) {
            // 需要对问答知识的结果进行过滤
            if (Objects.nonNull(result) && Objects.nonNull(result.getIntent()) && CollectionUtils.isNotEmpty(result.getIntent().getIntent_ranking())) {
                List<IntentBO> rankingList = result.getIntent().getIntent_ranking();
                Set<String> knowledgeTitleSet = dialogFlowInfoBO.getRobotKnowledgeMap().keySet();
                logger.info("当前存在的问答知识标题={}", knowledgeTitleSet);
                List<IntentBO> filteredList = rankingList.stream()
                        .filter(item -> knowledgeTitleSet.contains(item.getName()))
                        .collect(Collectors.toList());

                result.getIntent().setIntent_ranking(filteredList);
                if (CollectionUtils.size(filteredList) > 0) {
                    result.getIntent().setIntent(filteredList.get(0));
                } else {
                    result = null;
                }
            }

            logger.info("对问答知识结果进行过滤后={}", JsonUtils.object2String(result));
        } else {
            // 对分支进行过滤处理
            if (Objects.nonNull(result) && Objects.nonNull(result.getIntent()) && CollectionUtils.isNotEmpty(result.getIntent().getIntent_ranking())) {
                List<IntentBO> rankingList = result.getIntent().getIntent_ranking();
                Set<String> branchNameSet = dialogFlowInfoBO.getNameBranchMap().keySet();
                logger.info("当前存在的分支名称={}", branchNameSet);
                List<IntentBO> filteredList = rankingList.stream()
                        .filter(item -> branchNameSet.contains(item.getName()))
                        .collect(Collectors.toList());
                result.getIntent().setIntent_ranking(filteredList);
                if (CollectionUtils.size(filteredList) > 0) {
                    result.getIntent().setIntent(filteredList.get(0));
                } else {
                    result = null;
                }
            }

            logger.info("对分支结果进行过滤后={}", JsonUtils.object2String(result));
        }

        return result;
    }

    /**
     * 意图分支的问法识别
     *
     * @param userWords 用户输入
     */
    private Optional<PredictResultWrapper> predictIntentBranchByAlgorithm(String userWords) {
        logger.info("当前问法开关：{}", dialogFlowInfoBO.isEnableAskService());
        logger.info("问法介入字数：{}，用户输入字数：{}", dialogFlowInfoBO.getAskNumber(), StringUtils.length(userWords));
        logger.info("意图分支问法识别：userWords={}", userWords);
        if (dialogFlowInfoBO.isEnableAskService() && StringUtils.length(userWords) >= this.dialogFlowInfoBO.getAskNumber()) {
            PredictResponse predictResponse = doPredict(dialogFlowInfoBO.getDialogFlowId(), userWords, AlgorithmTrainTypeEnum.INTENT);
            logger.info("意图问法识别：userWords={}，结果={}", userWords, predictResponse);
            if(Objects.nonNull(predictResponse) && Objects.nonNull(predictResponse.getIntent()) && Objects.nonNull(predictResponse.getIntent().getIntent())) {
                IntentBO top1Result = predictResponse.getIntent().getIntent();
                String intent = top1Result.getName();
                double confidence = Double.parseDouble(top1Result.getConfidence());
                branchConfidence = Float.parseFloat(top1Result.getConfidence());
                // 当前节点的所有分支名字
                Set<String> currBranchSet = Sets.newHashSet();
                Map<String, String> currBranchMap = Maps.newHashMap();
                // 当前流程和节点
                DialogFlowStepListDTO currStep = this.matchingStepInfoManager.getStep();
                DialogFlowNodeDTO dialogFlowNode = currStep.getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());
                DialogFlowChatNodeDTO chatNode = (DialogFlowChatNodeDTO) dialogFlowNode;
                // 关键词和分支的UID对应的Map
                Map<String, Integer> branches = chatNode.getIntentBranchToChildrenNode();
                // 分支属性迭代
                for (Map.Entry<String, Integer> entry : branches.entrySet()) {
                    if (entry.getKey().equals("default") || Integer.parseInt(entry.getKey()) <= 0) {
                        continue;
                    }
                    IntentBranchPO branch = this.dialogFlowInfoBO.getIntentBranchMap().get(entry.getKey());
                    currBranchSet.add(branch.getName());
                    currBranchMap.put(branch.getName(), entry.getKey());
                }

                boolean contains = currBranchSet.contains(intent);
                logger.info("当前问法阈值设置为:[{}, {}]", dialogFlowInfoBO.getKnowledgeLowerThreshold(), dialogFlowInfoBO.getKnowledgeUpperThreshold());
                if (contains && BigDecimal.valueOf(dialogFlowInfoBO.getKnowledgeLowerThreshold()).compareTo(BigDecimal.valueOf(confidence)) <= 0) {
                    String sKey = currBranchMap.get(intent);
                    Map<String, IntentBranchPO> intentBranchMap = this.dialogFlowInfoBO.getIntentBranchMap();
                    IntentBranchPO intentBranchPO = intentBranchMap.get(sKey);
                    PredictResultWrapper intentBranchConfidenceWrapper = new PredictResultWrapper();
                    PredictResult predictResult = new PredictResult();
                    predictResult.setAlgorithmType(AlgorithmTypeEnum.INTENT_BRANCH);
                    predictResult.setPredictType(PredictTypeEnum.ALGORITHM);
                    predictResult.setIntentBranchPO(intentBranchPO);
                    predictResult.setKeyword(intentBranchPO.getName());
                    predictResult.setMatchText(top1Result.getSim_sentence());
                    predictResult.setConfidence(confidence);
                    predictResult.setRequestId(predictResponse.getRequest_id());
                    intentBranchConfidenceWrapper.setPredictResult(predictResult);
                    List<IntentBO> intentRanking = predictResponse.getIntent().getIntent_ranking();
                    if (CollectionUtils.isNotEmpty(intentRanking)) {
                        intentRanking = intentRanking.stream().filter(item -> currBranchSet.contains(item.getName())).collect(Collectors.toList());
                    }
                    intentBranchConfidenceWrapper.setIntentBOList(intentRanking);
                    return Optional.of(intentBranchConfidenceWrapper);
                }
            }
        }

        return Optional.empty();
    }

    /**
     * 根据关键词/问法识别匹配到问答知识
     *
     * @param predictKnowledge 匹配到的知识点
     * @param userWords        用户的话
     * @param aiProgress       ai
     */
    private void keyWordDetectionToKnowledge(DialogFlowStepListDTO currStep, RobotKnowledgePO predictKnowledge, String userWords, Double aiProgress, String debugLog) {
        recordKnowledgeMetrics(predictKnowledge);
        // 如果当前流程是主流程进入问答知识流程的话，需要记录主流程信息
        this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
        // 记录语境
        recordLastLanguageEnvironment(predictKnowledge.getLanguageEnvironmentId());
        // 获取重复的id，放入set中
        predictKnowledge.getRepeatDialogFlowIdList().stream().filter(StringUtils::isNotBlank).forEach(repeatDialogFlowIdSet::add);
        // 单个节点测试t
        if (Objects.nonNull(this.testOneNodeVO)) {
            this.testOneNodeVO.setIntentLevel(predictKnowledge.getCustomerIntentLevel());
        }
        // 命中业务次数
        if (predictKnowledge.getType().equals(RobotKnowledgeTypeEnum.BUSINESS)) {
            boolean b = accumulateBusinessQuestion(predictKnowledge, userWords, aiProgress);
            if (b) {
                return;
            }
        }
        if (predictKnowledge.getAnswerType() == null) {
            logger.error("[LogHub_Warn]问答知识回答方式为空, 话术id={}, 问答知识={}", predictKnowledge.getDialogFlowId(), predictKnowledge.getTitle());
            return;
        }
        // 重新设置用户无应答时间
        if (predictKnowledge.getAnswerType().equals(RobotKnowledgeAnswerTypeEnum.DIALOG_FLOW_ANSWER)) {
            if (this.inaudibleAndRepeatMode) {
                logger.info("命中问答知识流程, 取消听不清并重复上一句状态");
                this.inaudibleAndRepeatMode = false;
            }
            // 命中问答知识流程
            setKnowledgeManagerInfo(false, false, true, true, null);
            // 问答知识流程
            debugLog = debugLog + "（问答知识流程）";
            DialogFlowStepListDTO multiRoundDialog = this.dialogFlowInfoBO.getMultiRoundStepMap().get(predictKnowledge.getDialogFlowStepId());
            // 在问答知识流程中又命中这个问答知识流程;
            SimpleStepInfoBO step = this.matchingStepInfoManager.getPreUserSayMatchingStepInfoBO();
            if (Objects.nonNull(multiRoundDialog) && Objects.nonNull(step) && multiRoundDialog.getId().equals(step.getStepId())) {
                // 断句补齐, 继续放音
                if (isNeedMerge && aiProgress != ApplicationDialogConstant.DEFAULT_AI_PROGRESS) {
                    keyWordDetectionToAiSay(userWords, DebugLogHelper.againKnowledgeFlowAndContinue(isNeedMerge, multiRoundDialog.getName()));
                    return;
                }
                // 当前AI的节点
                DialogFlowNodeDTO dialogFlowNodeDTO = this.matchingStepInfoManager.getStep().getFlattenTreeNodeList().get(this.matchingStepInfoManager.getNodeIndex());
                // 判断当前节点是否是第一个节点, 如果是第一个节点
                if (checkNodeIsStepRootNode(this.matchingStepInfoManager.getStep(), dialogFlowNodeDTO)) {
                    if (aiProgress != ApplicationDialogConstant.DEFAULT_AI_PROGRESS) {
                        // 当前还在根节点, 且录音还未播放完成, 则继续播放当前录音
                        keyWordDetectionToAiSay(userWords, "重复命中问答知识流程根节点, 继续放音");
                    } else {
                        // 重复命中根节点了, 且跟节点录音已播放完成, 则重新播放或者切换话术
                        List<TextAudioContentPO> textAudioContentList = dialogFlowNodeDTO.getTextAudioContentList();
                        // 重复命中，而且节点又有多个话术的时候直接获取下一个话术
                        debugLog = DebugLogHelper.againKnowledgeFlowAndChange(isNeedMerge, multiRoundDialog.getName());
                        this.dialogManagerNew.changeAiTextAudioContentPO(dialogFlowNodeDTO, multiRoundDialog, this.matchingStepInfoManager.getNodeIndex(), userWords, debugLog);
                    }
                } else {
                    // 如果不是在根节点, 或者跟节点已经播放完成了, 这个时候需要通过默认节点走到下一个流程
                    // 当然这里需要判断下, 当前的节点是否是跳转节点, 如果是跳转节点, 则直接执行跳转操作, 如果是普通节点执行默认分支
                    // 不是在第一个节点重复命中该流程的
                    if (dialogFlowNodeDTO.canLink()) {
                        // 跳转节点
                        keyWordDetectionToAiSay(userWords, "命中自己，跳转节点不会被自己打断，继续放音");
                    } else {
                        // 普通节点, 走默认节点, 或者ai无法应答
                        DialogFlowChatNodeDTO chatNode = (DialogFlowChatNodeDTO) dialogFlowNodeDTO;
                        boolean containsDefault = chatNode.getIntentBranchToChildrenNode().containsKey("default");
                        if (containsDefault) {
                            debugLog = DebugLogHelper.againKnowledgeFlowAndDefault(isNeedMerge, multiRoundDialog.getName());
                            keyWordDetectionToDefaultBranch(userWords, debugLog, chatNode, aiProgress);
                        } else {
                            keyWordDetectionToAiUnknown(userWords);
                        }
                    }
                }
                return;
            }
            this.matchingStepInfoManager.init(multiRoundDialog, -1, 0);
            this.dialogManagerNew.keyWordDetectionToKnowledgeFlow(predictKnowledge, userWords, debugLog, aiProgress);
        } else {
            // 普通问答知识
            boolean isAiWaitKnownKnowledge = dialogFlowInfoBO.isAiWaitKnownKnowledge(predictKnowledge);
            boolean isAiWait2KnownKnowledge = dialogFlowInfoBO.isAiWait2KnownKnowledge(predictKnowledge);
            boolean isRepeatKnownKnowledge = dialogFlowInfoBO.isRepeatKnownKnowledge(predictKnowledge);
            boolean isInaudibleAndRepeatKnowledge = dialogFlowInfoBO.isInaudibleAndRepeatKnowledge(predictKnowledge);

            if (isInaudibleAndRepeatKnowledge) {
                logger.info("命中听不清并重复上一句问答知识, 后续进入重复播放逻辑");
                // 先把当前的event保存起来, 用来在后续重复的时候使用
                if (!this.inaudibleAndRepeatMode) {
                    // 重复触发时, 只重复记录第一次触发的状态
                    this.dialogManagerNew.recordInaudibleAndRepeatEvent();
                }

                this.inaudibleAndRepeatMode = true;
            }

            if (this.inaudibleAndRepeatMode
                    && (isAiWait2KnownKnowledge || isAiWaitKnownKnowledge)) {
                logger.info("命中特殊语境:AI等待客户处理, 退出听不清重复语境");
                this.inaudibleAndRepeatMode = false;
            }

            int answerIndex = this.dialogManagerNew.keyWordDetectionToKnowledge(predictKnowledge, isAiWaitKnownKnowledge, isAiWait2KnownKnowledge, isRepeatKnownKnowledge,
                    this.matchingStepInfoManager.getStep(), this.matchingStepInfoManager.getNodeIndex(), userWords, debugLog,
                    aiProgress, this.dialogFlowInfoBO.getAiWaitKnownKnowledge().getAiWaitPreKnownKnowledge(), this.isNeedMerge);

            if (answerIndex!=-1) {
                List<? extends TextAudioContentPO> robotKnowledgeAnswers = predictKnowledge.getRobotKnowledgeAnswers();
                try {
                    TextAudioContentPO textAudioContentPO = robotKnowledgeAnswers.get(answerIndex);
                    logger.debug("问答知识的操作={}", textAudioContentPO.getPostActionType());
                    if (DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW.equals(currStep.getType())
                            && aiProgress > ApplicationDialogConstant.DEFAULT_AI_PROGRESS && aiProgress < ApplicationDialogConstant.REPEAT_AI_PROGRESS
                            && (PostActionTypeEnum.WAIT.equals(textAudioContentPO.getPostActionType())
                            ||Objects.isNull(textAudioContentPO.getPostActionType())) && !knowledgeManagerInfo.isInKnowledge()) {
                        setKnowledgeManagerInfo(false, true, true, false, predictKnowledge);
                    }
                    Boolean interuptable = textAudioContentPO.getInteruptable();
                    int interruptableThreshold = textAudioContentPO.getInterruptableThreshold() == null ? 100 : textAudioContentPO.getInterruptableThreshold();

                    if (PostActionTypeEnum.HANG_UP.equals(textAudioContentPO.getPostActionType())) {
                        setKnowledgeManagerInfo(false, false, !Objects.isNull(interuptable) && interuptable, true, interruptableThreshold, false, predictKnowledge);
                    } else {
                        setKnowledgeManagerInfo(false, false, Objects.isNull(interuptable) || interuptable, true, interruptableThreshold, false, predictKnowledge);
                    }
                } catch (Exception e) {
                    logger.error("[LogHub]判断问答知识是否是挂机节点，话术id={}的问答知识标题={}答案列表为空", this.dialogFlowInfoBO.getDialogFlowId(), predictKnowledge.getTitle(), e);
                }
                // 设置
            } else {
                setKnowledgeManagerInfo(false, false, true, false, predictKnowledge);
            }
            this.matchingStepInfoManager.setStepIndex(-99);
        }
    }

    private void recordKnowledgeMetrics(RobotKnowledgePO predictKnowledge) {
        if (Objects.isNull(predictKnowledge)) {
            return;
        }
        // 判断命中的问答知识
        if (isAbuseKnowledge(predictKnowledge)) {
            metrics.abuseKnowledgeCount.incrementAndGet();
        } else if (isInaudibleKnowledge(predictKnowledge)) {
            // 累加命中听不清次数
            metrics.totalInaudibleKnowledgeCount.incrementAndGet();
            // 连续听不清次数
            int preInaudibleIndex = metrics.preInaudibleIndex.get();
            logger.info("preInaudibleIndex={}, currentUserSayFinishCount={}", preInaudibleIndex, metrics.userSayFinishCount.get());
            if (preInaudibleIndex + 1 == metrics.getUserSayFinishCount().get()) {
                metrics.continuousInaudibleKnowledgeCount.incrementAndGet();
            } else {
                metrics.continuousInaudibleKnowledgeCount.set(1);
            }
            if (metrics.continuousInaudibleKnowledgeCount.get() > metrics.getMaxContinuousInaudibleKnowledgeCount().get()) {
                metrics.maxContinuousInaudibleKnowledgeCount.set(metrics.continuousInaudibleKnowledgeCount.get());
            }
            metrics.preInaudibleIndex.set(metrics.getUserSayFinishCount().get());
        } else if (isRobotKnowledge(predictKnowledge)) {
            metrics.robotKnowledgeCount.incrementAndGet();
        }
    }

    private boolean isAbuseKnowledge(RobotKnowledgePO predictKnowledge) {
        return "骂人".equals(predictKnowledge.getTitle()) || "骚扰电话".equals(predictKnowledge.getTitle());
    }

    private boolean isInaudibleKnowledge(RobotKnowledgePO predictKnowledge) {
        return "听不清楚AI说话".equals(predictKnowledge.getTitle());
    }

    private boolean isRobotKnowledge(RobotKnowledgePO predictKnowledge) {
        return "机器人".equals(predictKnowledge.getTitle());
    }

    /**
     * 判断目标节点是否是step的根节点
     */
    private boolean checkNodeIsStepRootNode(DialogFlowStepListDTO step, DialogFlowNodeDTO targetNode) {
        if (Objects.isNull(step) || CollectionUtils.isEmpty(step.getFlattenTreeNodeList()) || Objects.isNull(targetNode)) {
            return false;
        }
        return targetNode.equals(step.getFlattenTreeNodeList().get(0));
    }

    /**
     * 命中的用户持续说话时间
     */
    private boolean accumulateUserSay(Integer userSayTimes, String userWords, Double aiProgress) {
        List<DialogFlowUserSayConfigRulePO> dialogFlowUserSayConfigRuleList = this.dialogFlowInfoBO.getDialogFlowUserSayConfigRuleList();
        for (DialogFlowUserSayConfigRulePO dialogFlowUserSayConfigRulePO : dialogFlowUserSayConfigRuleList) {
            Integer time = dialogFlowUserSayConfigRulePO.getCount() *1000;
            if (userSayTimes >= time) {
                // 如果当前流程是主流程进入问答知识流程的话，需要记录主流程信息
                this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
                // 是直接问答还是问答知识
                RobotKnowledgeAnswerTypeEnum answerType = dialogFlowUserSayConfigRulePO.getAnswerType();
                TextAudioContentPO textAudioContent = dialogFlowUserSayConfigRulePO.getTextAudioContent();
                String realText = RuntimeTextAudioContentUtils.getRealText(textAudioContent, this.getRuntimeTextAudioContentMap());
                if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(answerType)) {
                    logger.info("accumulateUserSay，用户说话长度={}，超过{}, 触发特殊话术配置的直接回答={}", userSayTimes, time, realText);
                    String debugLog = DebugLogHelper.userSayTooLong(userSayTimes, time, "直接回答");
                    PostActionTypeEnum postActionType = dialogFlowUserSayConfigRulePO.getPostActionType();
                    String dialogFlowStepId = dialogFlowUserSayConfigRulePO.getDialogFlowStepId();
                    textAudioContent.setIsWaitUserAnswer(dialogFlowUserSayConfigRulePO.getIsWaitUserAnswer());
                    setKnowledgeManagerInfo(true, false, dialogFlowUserSayConfigRulePO.isInterrupt(), false, null);
                    this.dialogManagerNew.keyWordDetectionToConfig(postActionType, userWords, debugLog, textAudioContent, dialogFlowStepId, aiProgress);
                } else {
                    // 问答知识流程
                    String dialogFlowStepId = dialogFlowUserSayConfigRulePO.getDialogFlowStepId();
                    DialogFlowStepListDTO dialogFlowStepListDTO = this.dialogFlowInfoBO.getMultiRoundStepMap().get(dialogFlowStepId);
                    this.matchingStepInfoManager.init(dialogFlowStepListDTO, -1, 0);
                    logger.info("accumulateUserSay，用户说话长度={}，超过{}, 触发特殊话术配置的到问答知识流程={}", userSayTimes, time, realText);
                    String debugLog = DebugLogHelper.userSayTooLong(userSayTimes, time, "直接回答");
                    this.dialogManagerNew.keyWordDetectionToKnowledgeFlow(null, userWords, debugLog, aiProgress);
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 命中的业务问题次数
     */
    private boolean accumulateBusinessQuestion(RobotKnowledgePO predictKnowledge, String userWords, Double aiProgress) {
        int businessCount;
        // 是否匹配同一业务问题统计次数
        if (this.dialogFlowInfoBO.isSameQuestion()) {
            // 同一问题出发的次数
            String id = predictKnowledge.getId();
            Integer count = this.businessQuestionCountMap.get(id);
            count = count == null ? 1 : (++count);
            this.businessQuestionCountMap.put(id, count);
            businessCount = count;
        } else {
            businessQuestionCount++;
            businessCount = businessQuestionCount;
        }
        // 如果当前流程是主流程进入问答知识流程的话，需要记录主流程信息
        this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
        // 当前次数是否存在操作
        DialogFlowBusinessQAConfigRulePO dialogFlowBusinessQAConfigRulePO = this.dialogFlowInfoBO.getDialogFlowBusinessQAConfigRuleMap().get(businessCount);
        if (Objects.nonNull(dialogFlowBusinessQAConfigRulePO)) {
            if (dialogFlowBusinessQAConfigRulePO.getHumanIntervention()) {
                this.dialogManagerNew.keyWordDetectionToConfigHuman();
            }
            // 是直接问答还是问答知识
            RobotKnowledgeAnswerTypeEnum answerType = dialogFlowBusinessQAConfigRulePO.getAnswerType();
            TextAudioContentPO textAudioContent = dialogFlowBusinessQAConfigRulePO.getTextAudioContent();
            String realText = RuntimeTextAudioContentUtils.getRealText(textAudioContent, this.getRuntimeTextAudioContentMap());
            if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(answerType)) {
                logger.info("accumulateBusinessQuestion，命中业务知识库={}，命中业务问题数量={}, 触发特殊话术配置的直接回答={}", predictKnowledge.getTitle(), businessCount, realText);
                String debugLog = DebugLogHelper.businessSpecialDialog(predictKnowledge.getTitle(), this.isNeedMerge, businessCount, "直接回答");
                PostActionTypeEnum postActionType = dialogFlowBusinessQAConfigRulePO.getPostActionType();
                String dialogFlowStepId = dialogFlowBusinessQAConfigRulePO.getDialogFlowStepId();
                textAudioContent.setIsWaitUserAnswer(dialogFlowBusinessQAConfigRulePO.getIsWaitUserAnswer());
                setKnowledgeManagerInfo(true, false, dialogFlowBusinessQAConfigRulePO.isInterrupt(), false, null);
                this.dialogManagerNew.keyWordDetectionToConfig(postActionType, userWords, debugLog, textAudioContent, dialogFlowStepId, aiProgress, predictKnowledge);
            } else {
                // 问答知识流程
                String dialogFlowStepId = dialogFlowBusinessQAConfigRulePO.getDialogFlowStepId();
                DialogFlowStepListDTO dialogFlowStepListDTO = this.dialogFlowInfoBO.getMultiRoundStepMap().get(dialogFlowStepId);
                this.matchingStepInfoManager.init(dialogFlowStepListDTO, -1, 0);
                logger.info("accumulateBusinessQuestion，命中业务知识库={}，命中业务问题数量={}, 触发特殊话术配置的到问答知识流程={}", predictKnowledge.getTitle(), businessCount, realText);
                String debugLog = DebugLogHelper.businessSpecialDialog(predictKnowledge.getTitle(), this.isNeedMerge, businessCount, "问答知识流程");
                this.dialogManagerNew.keyWordDetectionToKnowledgeFlow(predictKnowledge, userWords, debugLog, aiProgress);
            }
            return true;
        }
        return false;
    }

    /**
     * 命中的否定语义次数
     */
    private boolean accumulateNegative(IntentBranchPO intentBranch, String userWords, Double aiProgress) {
        // 如果当前流程是主流程进入问答知识流程的话，需要记录主流程信息
        // 当前次数是否存在操作
        // 命中拒绝分支
        if (Objects.nonNull(intentBranch) && IntentBranchCategoryEnum.DECLINE.equals(intentBranch.getCategory())) {
            negativeCount += 1;
            logger.debug("命中拒绝问题数量={}", negativeCount);
        } else {
            return false;
        }
        // 如果当前流程是主流程进入问答知识流程的话，需要记录主流程信息
        this.matchingStepInfoManager.setWhenFromMainToKnowledgeStepInfoBO();
        DialogFlowDeclineConfigRulePO dialogFlowDeclineConfigRulePO = this.dialogFlowInfoBO.getDialogFlowDeclineConfigRuleMap().get(negativeCount);
        // 是否有个性化配置
        if (Objects.nonNull(dialogFlowDeclineConfigRulePO)) {
            if (dialogFlowDeclineConfigRulePO.getHumanIntervention()) {
                this.dialogManagerNew.keyWordDetectionToConfigHuman();
            }
            // 是直接问答还是问答知识
            RobotKnowledgeAnswerTypeEnum answerType = dialogFlowDeclineConfigRulePO.getAnswerType();
            TextAudioContentPO textAudioContent = dialogFlowDeclineConfigRulePO.getTextAudioContent();
            String realText = RuntimeTextAudioContentUtils.getRealText(textAudioContent, this.getRuntimeTextAudioContentMap());
            if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(answerType)) {
                logger.info("accumulateBusinessQuestion，命中拒绝分支={}，命中拒绝问题数量={}, 触发特殊话术配置的直接回答={}", intentBranch.getName(), negativeCount, realText);
                String debugLog = DebugLogHelper.refuseSpecialDialog(intentBranch.getName(), this.isNeedMerge, negativeCount);
                PostActionTypeEnum postActionType = dialogFlowDeclineConfigRulePO.getPostActionType();
                String dialogFlowStepId = dialogFlowDeclineConfigRulePO.getDialogFlowStepId();
                textAudioContent.setIsWaitUserAnswer(dialogFlowDeclineConfigRulePO.getIsWaitUserAnswer());
                setKnowledgeManagerInfo(true, false, dialogFlowDeclineConfigRulePO.isInterrupt(), false, null);
                this.dialogManagerNew.keyWordDetectionToConfig(postActionType, userWords, debugLog, textAudioContent, dialogFlowStepId, aiProgress, intentBranch);
            } else {
                // 问答知识流程
                String dialogFlowStepId = dialogFlowDeclineConfigRulePO.getDialogFlowStepId();
                DialogFlowStepListDTO dialogFlowStepListDTO = this.dialogFlowInfoBO.getMultiRoundStepMap().get(dialogFlowStepId);
                this.matchingStepInfoManager.init(dialogFlowStepListDTO, -1, 0);
                logger.info("accumulateBusinessQuestion，命中拒绝分支={}，命中拒绝问题数量={}, 触发特殊话术配置的问答知识流程", intentBranch.getName(), negativeCount);
                String debugLog = DebugLogHelper.refuseSpecialDialog(intentBranch.getName(), this.isNeedMerge, negativeCount);
                this.dialogManagerNew.keyWordDetectionToKnowledgeFlow(null, userWords, debugLog, aiProgress, intentBranch);
            }

            return true;
        }
        return false;
    }

    private boolean isCallTimeout() {
        if (BooleanUtils.isNotTrue(this.enableCallTimeout)) {
            return false;
        }

        if (playingPreHangupAnswer) {
            return false;
        }
        // 判断时间是否达到超时要求
        long startTime = this.startCallTime;
        long now = System.currentTimeMillis();
        long calledTime = now - startTime;
        if (calledTime < this.callTimeoutMillis) {
            return false;
        }
        logger.info("isCallTimeout，通话时长超过限制，开始时间={}，当前时间={}, 耗时={}, 限制={}", startTime, now, calledTime, callTimeoutMillis);
        return true;
    }

    private void hangupByCallTimeout(String userInput) {
        logger.info("hangupByCallTimeout，通话时长超过限制, 开始执行挂机操作");
        playingPreHangupAnswer = true;
        String debugLog = "通话超过话术最长时间限制";
        TextAudioContentPO textAudioContent = this.callTimeoutHangupAnswer;
        if (Objects.isNull(textAudioContent)) {
            logger.error("对话数据错误，对话超时前挂机话术为空");
            return;
        }
        textAudioContent.setIsWaitUserAnswer(false);
        textAudioContent.setInteruptable(false);
        setKnowledgeManagerInfo(true, false, false, false, null);
        this.dialogManagerNew.keyWordDetectionToConfig(PostActionTypeEnum.HANG_UP, userInput, debugLog, textAudioContent, null, 0.0);
    }

    /**
     * 信息收集结点收集到信息
     */
    private void collectInfo(DialogFlowInfoNodeDTO infoNodeDTO, Set<String> value) {
        this.collectCount = 0;
        // 旧逻辑: 直接存字段
        Long fieldId = infoNodeDTO.getFieldId();
        if (fieldId != null) {
            collectMap.put(fieldId, value);
        } else {
            // crm的不规范操作可能导致该字段为null
            logger.warn("信息收集节点要保存到的字段id为null, 节点名称 = [{}]", infoNodeDTO.getName());
        }
        // 新逻辑: 存动态变量
        dialogManagerNew.getAssignmentManager().updateVariable(infoNodeDTO.getVariableId(), String.join(", ", value));
        if (BooleanUtils.isTrue(infoNodeDTO.getRedialTime())) {
            redialTime = CollectExtraInfoBO.getRedialTime(Lists.newArrayList(value), infoNodeDTO.getRedialTimeChoose());
        }
    }

    /**
     * 获取对话中收集到的动态变量信息
     * @return
     */
    public Map<String, String> getCollectedDynamicVariable() {
        if (CollectionUtils.isEmpty(dialogFlowInfoBO.getVariableList())) {
            return Collections.emptyMap();
        }
        List<VariableVO> dynamicVariableList = dialogFlowInfoBO.getVariableList().stream()
                .filter(item -> VariableTypeEnum.DYNAMIC.getCode().equals(item.getType()))
                .collect(Collectors.toList());

        Map<String, String> result = new HashMap<>(dynamicVariableList.size());
        dynamicVariableList.forEach(variable -> {
            String variableName = variable.getName();
            String value = "";
            result.put(variableName, value);
        });

        if (MapUtils.isNotEmpty(dialogManagerNew.getAssignmentManager().getDynamicVariableMap())) {
            result.putAll(dialogManagerNew.getAssignmentManager().getDynamicVariableMap());
        }
        if (MapUtils.isNotEmpty(dialogManagerNew.getAssignmentManager().getCollectVariableMap())) {
            dialogManagerNew.getAssignmentManager().getCollectVariableMap().forEach((key, value) -> {
                if (result.containsKey(key)) {
                    result.put(key, value);
                }
            });
        }
        return result;
    }

    /**
     * 信息收集失败
     * @param infoNodeDTO 当前节点
     * @param userSayText 用户说的话
     * @param aiProgress  ai进度
     */
    private void collectFailed(DialogFlowInfoNodeDTO infoNodeDTO, String userSayText, Double aiProgress, Integer pressTime, boolean needMerge) {
        String debugLog;
        // 总收集次数 = 重复收集次数 + 1
        int totalTimes = infoNodeDTO.getRepeatCollectTimes() + 1;
        collectCount++;
        boolean continueCollect = collectCount < totalTimes;
        boolean contentIsEmpty = StringUtils.isEmpty(userSayText);
        if (pressTime == null) {
            // 语音收集
            debugLog = DebugLogHelper.voiceCollectFailed(collectCount, continueCollect, contentIsEmpty, needMerge);
        } else {
            // 按键收集
            debugLog = DebugLogHelper.buttonCollectFailed(collectCount, continueCollect, contentIsEmpty,
                    userSilenceMillisecond, pressTime, needMerge);
        }
        logger.info(debugLog);
        if (continueCollect) {
            // 继续采集
            this.dialogManagerNew.repeatCurrentNode(userSayText, debugLog, this.isNeedMerge);
        } else {
            // 重复采集失败
            collectCount = 0;
            keyWordDetectionToDefaultBranch(userSayText, debugLog, infoNodeDTO, aiProgress);
        }
    }

    /**
     * 如果命中的是问答知识，当前流程是否存在答案
     * @param predictKnowledge
     * @return
     */
    private Integer hasAnswer(RobotKnowledgePO predictKnowledge) {
        if (Objects.isNull(predictKnowledge)) {
            return null;
        }

        if (Objects.equals("AI重复上句语音", predictKnowledge.getTitle())) {
            return 0;
        }

        Integer stepIndex = matchingStepInfoManager.getStepIndex();

        // 问答知识流程按照兜底来匹配
        if (RobotKnowledgeAnswerTypeEnum.DIALOG_FLOW_ANSWER.equals(predictKnowledge.getAnswerType()) || stepIndex < 0) {
            return 0;
        }

        Integer answerIndex = this.dialogManagerNew.getRobotKnowledgeManager().getAnswerIndex(predictKnowledge, stepIndex);
        logger.info("问答知识的匹配到的index={}", answerIndex);
        return answerIndex;

    }

    /**
     * 记录语境
     */
    private void recordLastLanguageEnvironment(String lastLanguageEnvironmentId) {
        if (StringUtils.isNotEmpty(lastLanguageEnvironmentId)) {
            this.lastLanguageEnvironmentId = lastLanguageEnvironmentId;
            logger.debug("记录语境lastLanguageEnvironmentId={}", lastLanguageEnvironmentId);
        }
    }

    /**
     * 清理语境
     */
    private void clearLastLanguageEnvironment() {
        this.lastLanguageEnvironmentId = null;
        logger.debug("清理语境lastLanguageEnvironmentId");
    }

    /**
     * 获取当前对话事件对应的对话节点是否允许打断，用于判断是否暂停播放录音
     *
     * @return true代表可以被打断，false不支持打断
     */
    public boolean getCurrentChatEventInterrupt(Double aiProcess) {
        return dialogManagerNew.getCurrentChatEventInterrupt(knowledgeManagerInfo, aiProcess);
    }

    /**
     * 获取上一个节点信息
     */
    public DialogFlowNodeDTO getPrevDialogFlowNode() {
        try {
            boolean isKnowledge = Objects.nonNull(knowledgeManagerInfo.getKnowledge());
            if (isKnowledge) {
                logger.info("从知识库出来，不需要收集信息");
                return null;
            }
            List<DialogFlowStepListDTO> dialogFlowSteps = this.dialogFlowInfoBO.getDialogFlowSteps();
            SimpleStepInfoBO stepInfoBO = this.matchingStepInfoManager.getPreUserSayMatchingStepInfoBO();
            if (Objects.nonNull(stepInfoBO)) {
                Integer preStepIndex = stepInfoBO.getStepIndex();
                Integer preNodeIndex = stepInfoBO.getNodeIndex();
                if (Objects.nonNull(preStepIndex) && preStepIndex >= 0 && preStepIndex < dialogFlowSteps.size()) {
                    DialogFlowStepListDTO dialogFlowStepListDTO = dialogFlowSteps.get(preStepIndex);
                    if(Objects.nonNull(dialogFlowStepListDTO)) {
                        if (Objects.nonNull(preNodeIndex) && preNodeIndex >= 0 && preNodeIndex < dialogFlowStepListDTO.getFlattenTreeNodeList().size()) {
                            return dialogFlowStepListDTO.getFlattenTreeNodeList().get(preNodeIndex);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("属性收集出错", e);
        }
        return null;
    }

    public DialogManager getDialogManager() {
        return dialogManagerNew;
    }

    /**
     * 用户点击拨号盘事件，获取的按键信息
     */
    public void whenUserClick(char signal) {
        // 当前流程和节点
        eventLogProcessor.createEventLog(CallEventTypeEnums.USER_DIAL_CLICK);
        Event currentEvent = dialogManagerNew.getCurrentEvent();
        if (currentEvent == null) {
            logger.info("currentEvent == null时接收到按键信息{}", signal);
            return;
        }
        DialogFlowStepListDTO currStep = currentEvent.getDialogFlowStep();
        DialogFlowNodeDTO nodeDTO = currStep.getFlattenTreeNodeList().get(currentEvent.getCurrNodeIndex());
        if (dtmfManager.isWorking() && DialogFlowNodeTypeEnum.INFO.equals(nodeDTO.getType())) {
            logger.info("收集到按键{}", signal);
            dtmfManager.receive(signal);
            if (dtmfManager.canStop()) {
                DialogFlowInfoNodeDTO infoNodeDTO = (DialogFlowInfoNodeDTO) nodeDTO;
                String info = dtmfManager.getInfo();
                if (StringUtils.isBlank(info) && ApplicationConstant.POC_DIALOGFLOW_ID.contains(String.valueOf(dialogFlowInfoBO.getDialogFlowId()))) {
                    info = "#";
                    logger.info("POC环境下，按键收集为空，强制设置为#");
                }
                dtmfManager.terminal();

                if ("#".equals(info)) {
                    logger.info("poc测试, 接收到#号, 直接重复当前节点");
                    dialogManagerNew.repeatCurrentNode(info, "接收到#号, 重复播报当前节点", false);
                    return;
                }

                Set<String> value = Sets.newHashSet(info);
                String debugLog = DebugLogHelper.collectInfoSuccess(infoNodeDTO.getFieldId(), value, false);
                logger.info(debugLog);
                collectInfo(infoNodeDTO, value);
                // 采集成功分支
                IntentBranchPO successBranch = null;
                for (String key : infoNodeDTO.getIntentBranchToChildrenNode().keySet()) {
                    if (!"default".equals(key)) {
                        successBranch = dialogFlowInfoBO.getIntentBranchMap().get(key);
                    }
                }
                if (successBranch != null) {
                    successBranch.setName("收集成功");
                    successBranch.setCategory(IntentBranchCategoryEnum.COLLECT_DATA);
                }

                // 按键采集只使用了采集到的信息
                // 20210204 - 这边successBranch是有可能为空的，但原先的逻辑就没有异常处理，暂时保持原逻辑不变
                PredictResult intentPredictResult = new PredictResult();
                intentPredictResult.setAlgorithmType(AlgorithmTypeEnum.INTENT_BRANCH);
                intentPredictResult.setPredictType(PredictTypeEnum.REGEX);
                intentPredictResult.setIntentBranchPO(successBranch);
                intentPredictResult.setKeyword(info);
                intentPredictResult.setMatchText(info);
                Optional<CallEventLogPO> eventLogOptional = eventLogProcessor.createEventLog(CallEventTypeEnums.USER_DIAL_INPUT);

                analyzeKeyWordDetection(info, info, intentPredictResult, null, ApplicationDialogConstant.DEFAULT_AI_PROGRESS, currStep, eventLogOptional);
                eventLogOptional.ifPresent(eventLogProcessor::updateStepNodeState);
            }
        }
    }

    public void userDtmfSilence() {
        if (isCallTimeout()) {
            hangupByCallTimeout("通话时间超时");
        }
        if (isContinuousMuteTimeout()) {
            hangupByContinuousMuteTimeout("连续静音时长超过限制");
            return;
        }
        if (dtmfManager.isWorking()) {
            logger.info("按键收集超时");
            collectCount++;
            dtmfManager.terminal();
            userSilence();
        }
    }

    public void remoteHangup() {
        eventLogProcessor.createWeipinhuiEventLog(aliyunAsrSoundManager.getCurrentAudioPlayIndex(), WeipinhuiCallEventLog.TYPE_USER_HANGUP);
        dialogManagerNew.remoteHangup();
        eventLogProcessor.createEventLog(CallEventTypeEnums.USER_HANGUP);
    }

    public Map<DialogFlowNodeStatsDetailKey, AtomicInteger> getNodeStatsMap() {
        return dialogManagerNew.getNodeStatsMap();
    }

    /**
     * 设置问答知识、个性化配置是否打断
     */
    private void setKnowledgeManagerInfo(boolean inConfig, boolean repeat, boolean interrupt, boolean inKnowledge, RobotKnowledgePO robotKnowledgePO) {
        setKnowledgeManagerInfo(inConfig, repeat, interrupt, false, 100, inKnowledge, robotKnowledgePO);
    }

    /**
     * 设置问答知识、个性化配置是否打断
     */
    private void setKnowledgeManagerInfo(boolean inConfig, boolean repeat, boolean interrupt, boolean interruptByThreshold, int interruptThreshold, boolean inKnowledge, RobotKnowledgePO robotKnowledgePO) {
        knowledgeManagerInfo.setKnowledge(robotKnowledgePO);
        knowledgeManagerInfo.setRepeat(repeat);
        knowledgeManagerInfo.setInterrupt(interrupt);
        knowledgeManagerInfo.setInterruptByThreshold(interruptByThreshold);
        knowledgeManagerInfo.setInterruptThreshold(interruptThreshold);
        knowledgeManagerInfo.setInKnowledge(inKnowledge);
        knowledgeManagerInfo.setInConfig(inConfig);
    }

    private boolean isPinyinPattern(Pattern pattern) {
        return pattern != null
                && pattern.toString() != null
                && pattern.toString().startsWith("<")
                && pattern.toString().endsWith(">");
    }

    /**
     * 重新设置超时时间
     */
    public void resetUserSilenceMillisecond(Double userSilenceSecond) {
        Integer userSilenceMillisecond = null;
        if (Objects.nonNull(userSilenceSecond)) {
            userSilenceSecond = userSilenceSecond * 1000;
            userSilenceMillisecond = userSilenceSecond.intValue();
        }
        logger.info("问答知识的用户无应答时间={}", userSilenceMillisecond);
        if (userSilenceMillisecond != null) {
            this.userSilenceMillisecond = userSilenceMillisecond;
        } else {
            this.userSilenceMillisecond = this.dialogFlowInfoBO.getDefaultUserSilenceMillisecond();
        }
    }

    public Set<Long> getAlreadySendSmsTemplateIdSet() {
        return dialogManagerNew.getSendSmsTemplateIdSet();
    }

    public void createEventLog(CallEventTypeEnums type) {
        eventLogProcessor.createEventLog(type);
    }

    public void createVolumeSnapshotEvent(short[] volumes) {
        eventLogProcessor.createVolumeSnapshotEvent(volumes);
    }

    public void setIgnoreBranchPredict(boolean ignoreBranchPredict) {
        this.ignoreBranchPredictResult = ignoreBranchPredict;
    }
    /**
     * 在断句补齐且继续播放的情况下, 需要补充一个和上一个轮次一模一样的ai侧日志
     * 用来判断ai说话逻辑
     */
    public void duplicateLastAiDetailIfMergeInput() {
        if (!SemanticManager.this.isNeedMerge) {
            logger.info("当前未进行断句补齐, 不需要补充ai侧日志");
            return;
        }
        AnalyzeDetail lastDetail = null;
        for (AnalyzeDetail analyzeDetail : noRollbackAnalyzeDetailList) {
            if (CharacterEnum.ROBOT.equals(analyzeDetail.getType())) {
                lastDetail = analyzeDetail;
            }
        }
        if (Objects.nonNull(lastDetail)) {
            logger.info("补充上一个ai侧日志={}", lastDetail.getText());
            analyzeDetailList.add(lastDetail);
        }
    }

    public void createUserSayFinishEvent(String userInput, int startTime, int endTime) {
        WeipinhuiCallEventLog log = eventLogProcessor.createWeipinhuiEventLog(0, WeipinhuiCallEventLog.TYPE_USER_SAY_END);
        log.setUserInput(userInput);
        log.setUserSayBeginOffset(startTime);
        log.setUserSayEndOffset(endTime);
    }

    class AnalyzableDetailListener implements AnalyzableDetailCreatedListener {

        @Override
        public void onCreate(AnalyzeDetail analyzeDetail) {
            logger.info("创建分析详情={}", analyzeDetail.getText());
            noRollbackAnalyzeDetailList.add(analyzeDetail);
            analyzeDetailList.add(analyzeDetail);
        }
    }

    @Data
    public static class WaitUserSayFinishBO {
        private int beginTime;
        private int endTime;
        private String text;

    public WaitUserSayFinishBO(int beginTime, int endTime, String text) {
            this.beginTime = beginTime;
            this.endTime = endTime;
            this.text = text;
        }
    }

    private void updateUserSayFinishTimestamp() {
        lastUserSayFinishTimestamp = System.currentTimeMillis();
        lastUserActiveTimestamp = Long.MAX_VALUE;
    }

    private void updateAiSayFinishTimestamp() {
        if (lastUserSayFinishTimestamp > lastAiSayFinishTimestamp) {
            lastUserActiveTimestamp = System.currentTimeMillis();
        }
        lastAiSayFinishTimestamp = System.currentTimeMillis();
    }

    private boolean isContinuousMuteTimeout() {
        if (!this.enableContinuousMuteTimeout) {
            return false;
        }
        if (playingPreHangupAnswer) {
            return false;
        }
        long muteMillis = System.currentTimeMillis() - lastUserActiveTimestamp;
        if (muteMillis > this.continuousMuteTimeoutMillis) {
            logger.info("检测到用户连续静音时长超过限制, lastUserActiveTimestamp={}, muteMillis={}", lastUserActiveTimestamp, muteMillis);
            return true;
        }
        return false;
    }

    private void hangupByContinuousMuteTimeout(String userInput) {
        logger.info("hangupByContinuousMuteTimeout，触发连续静音时长限制, 开始执行挂机操作");
        playingPreHangupAnswer = true;
        String debugLog = "超过连续静音时长限制";
        TextAudioContentPO textAudioContent = this.continuousMuteHangupAnswer;
        if (Objects.isNull(textAudioContent)) {
            logger.error("对话数据错误，连续静音超时前挂机话术为空");
            return;
        }
        textAudioContent.setIsWaitUserAnswer(false);
        textAudioContent.setInteruptable(false);
        setKnowledgeManagerInfo(true, false, false, false, null);
        this.dialogManagerNew.keyWordDetectionToConfig(PostActionTypeEnum.HANG_UP, userInput, debugLog, textAudioContent, null, 0.0);
    }

    class CallEventLogProcessor {
        private final List<CallEventLogPO> eventLogList = new ArrayList<>(256);
        private final List<CallEventLogPO> preEventLogList = new ArrayList<>(256);

        private final List<WeipinhuiCallEventLog> weipinhuiEventLogList = new ArrayList<>(256);
        private final List<WeipinhuiCallEventLog> preWeipinhuiEventLogList = new ArrayList<>(256);

        public synchronized void commitLog() {
            // 记录已经提交的状态
            preEventLogList.clear();
            preEventLogList.addAll(eventLogList);
            preWeipinhuiEventLogList.clear();
            preWeipinhuiEventLogList.addAll(weipinhuiEventLogList);
        }

        public synchronized void rollbackLog() {
            eventLogList.clear();
            eventLogList.addAll(preEventLogList);
            weipinhuiEventLogList.clear();
            weipinhuiEventLogList.addAll(preWeipinhuiEventLogList);
        }

        public synchronized Optional<CallEventLogPO> createEventLog(CallEventTypeEnums type) {
            try {
                return doCreateEventLog(type, null);
            } catch (Exception e) {
                logger.error("记录对话事件异常", e);
                return Optional.empty();
            }
        }

        public synchronized WeipinhuiCallEventLog createWeipinhuiEventLog(int audioPlayIndex, String type) {
            return createWeipinhuiEventLog(audioPlayIndex, type, 0);
        }

        public synchronized WeipinhuiCallEventLog createWeipinhuiEventLog(int audioPlayIndex, String type, int duration) {
            WeipinhuiCallEventLog log = new WeipinhuiCallEventLog();
            log.setEventIndex(eventIndex.get());
            log.setAudioPlayIndex(audioPlayIndex);
            log.setType(type);
            log.setTimestamp(System.currentTimeMillis());
            log.setOffset(System.currentTimeMillis() - enterTimestamp);
            switch (type) {
                case WeipinhuiCallEventLog.TYPE_AI_SAY_BEGIN:
                case WeipinhuiCallEventLog.TYPE_AI_SAY_END:
                case WeipinhuiCallEventLog.TYPE_AI_SAY_PAUSE:
                case WeipinhuiCallEventLog.TYPE_AI_SAY_RESUME:
                    break;
                case WeipinhuiCallEventLog.TYPE_USER_SAY_BEGIN:
                case WeipinhuiCallEventLog.TYPE_USER_SAY_END:
                    log.setUserSayMs(duration);
                default:
                    break;
            }
            weipinhuiEventLogList.add(log);
            return log;
        }

        private void updatePlayProgressState(CallEventLogPO log) {
            if (Objects.isNull(log) || Objects.isNull(log.getEventType())) {
                return;
            }
            switch (log.getEventType()) {
                case VAD_START:
                case VAD_END:
                case BASE_VAD_START:
                case BASE_VAD_END:
                case USER_VOICE_VOLUME:
                case AI_SAY_PAUSE:
                case AI_SAY_RESUME:
                case USER_INPUT:
                case USER_INPUT_FINISH:
                case USER_DIAL_INPUT:
                case USER_DIAL_CLICK:
                case USER_HANGUP:
                case AI_HANGUP:
                    recordFilePlayProgressToEventLog(log);
                    break;
                default:
                    break;
            }
        }

        private void updateStepNodeState(CallEventLogPO log) {
            if (Objects.isNull(log) || Objects.isNull(log.getEventType())) {
                return;
            }
            try {
                switch (log.getEventType()) {
                    case START:
                    case USER_SILENCE:
                    case USER_INPUT_FINISH:
                    case USER_DIAL_INPUT:
                    case AI_SAY_FINISH:
                        recordStateToEventLog(log);
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                logger.error("记录当前对话节点状态异常", e);
            }
        }

        private synchronized Optional<CallEventLogPO> doCreateEventLog(CallEventTypeEnums type, CallEventLogPO log) {
            if (log == null) {
                log = new CallEventLogPO();
                log.setEventIndex(eventIndex.get());
            }
            if (Objects.isNull(log.getOffset())) {
                long now = System.currentTimeMillis();
                long offset = now - startCallTime;
                log.setOffset(offset);
            }
            log.setEventType(type);
            this.eventLogList.add(log);
            updatePlayProgressState(log);
            return Optional.of(log);
        }

        // 记录当前状态到事件日志中
        private void recordStateToEventLog(CallEventLogPO log) {
            if (log == null) {
                throw new NullPointerException("log 不能为空");
            }

            Event currentEvent = SemanticManager.this.dialogManagerNew.currentEvent;
            DialogFlowStepListDTO currentStep = currentEvent.getDialogFlowStep();
            int currentNodeIndex = currentEvent.getCurrNodeIndex();
            if (Objects.nonNull(currentStep)) {
                logger.debug("更新状态信息, stepId={}, stepName={}", currentStep.getId(), currentStep.getName());
                log.setStepId(currentStep.getId());
                log.setStepType(currentStep.getType());
                DialogFlowNodeDTO dialogFlowNodeDTO = currentStep.getFlattenTreeNodeList().get(currentNodeIndex);
                if (Objects.nonNull(dialogFlowNodeDTO)) {
                    log.setStepNodeId(dialogFlowNodeDTO.getId());
                    logger.debug("更新状态信息, nodeId={}", dialogFlowNodeDTO.getId());
                }
            }

            RobotKnowledgePO currentKnowledge = SemanticManager.this.knowledgeManagerInfo.getKnowledge();
            if (Objects.nonNull(currentKnowledge)) {
                log.setKnowledgeType(currentKnowledge.getType());
                log.setKnowledgeId(currentKnowledge.getId());
            }
            String answerText = SemanticManager.this.dialogManagerNew.getCurrentAnswerText();
            if (StringUtils.isNotBlank(answerText)) {
                logger.debug("更新状态信息, answer={}", answerText);
                log.setAnswerText(answerText);
            }

        }

        private void recordFilePlayProgressToEventLog(CallEventLogPO log) {
            if (log == null) {
                throw new NullPointerException(" log 不能为空");
            }
            if (SemanticManager.this.aliyunAsrSoundManager == null) {
                return;
            }
            try {
                log.setProgress(SemanticManager.this.aliyunAsrSoundManager.getCurrentFilePlayProcess());
            } catch (Exception e) {
                logger.error("记录当前录音播放进度异常", e);
            }
        }

        private CallEventLogExtraPO.PredictResultBO convert(PredictResult predictResult) {
            CallEventLogExtraPO.PredictResultBO eventLogPredictResult = new CallEventLogExtraPO.PredictResultBO();
            boolean isKnowledge = AlgorithmTypeEnum.KNOWLEDGE.equals(predictResult.getAlgorithmType());
            eventLogPredictResult.setResourceType(isKnowledge ? CallEventLogExtraPO.PredictResultBO.RESOURCE_TYPE_KNOWLEDGE : CallEventLogExtraPO.PredictResultBO.RESOURCE_TYPE_BRANCH);
            eventLogPredictResult.setPredictType(PredictTypeEnum.REGEX.equals(predictResult.getPredictType()) ? CallEventLogExtraPO.PredictResultBO.PREDICT_TYPE_REGEX : CallEventLogExtraPO.PredictResultBO.PREDICT_TYPE_ALGORITHM);

            if (Objects.nonNull(predictResult.getRobotKnowledgePO())) {
                eventLogPredictResult.setCategory(predictResult.getRobotKnowledgePO().getType().name());
                eventLogPredictResult.setId(predictResult.getRobotKnowledgePO().getId());
                eventLogPredictResult.setName(predictResult.getRobotKnowledgePO().getTitle());
            }
            if (Objects.nonNull(predictResult.getIntentBranchPO())) {
                eventLogPredictResult.setCategory(predictResult.getIntentBranchPO().getCategory().name());
                eventLogPredictResult.setId(predictResult.getIntentBranchPO().getId());
                eventLogPredictResult.setName(predictResult.getIntentBranchPO().getName());
            }
            eventLogPredictResult.setKeyword(predictResult.getKeyword());
            eventLogPredictResult.setSentence(predictResult.getMatchText());
            eventLogPredictResult.setConfidence(predictResult.getConfidence());
            return eventLogPredictResult;
        }

        public void updateOriginPredictResult(Optional<CallEventLogPO> eventLogOpt, boolean isKnowledge, boolean isRegex, Optional<PredictResultWrapper> resultWrapper) {
            if (!resultWrapper.isPresent() || !eventLogOpt.isPresent()) {
                return;
            }

            // todo
        }

        public void updatePredictResult(Optional<CallEventLogPO> eventLog, boolean isKnowledge, PredictResult predictResult) {
            try {
                if (Objects.isNull(predictResult) || !eventLog.isPresent()) {
                    return;
                }
                CallEventLogPO log = eventLog.get();
                CallEventLogExtraPO extra = log.getExtra();
                if (extra == null) {
                    extra = new CallEventLogExtraPO();
                    log.setExtra(extra);
                }

                if (isKnowledge) {
                    extra.setKnowledgeResult(convert(predictResult));
                } else {
                    extra.setBranchResult(convert(predictResult));
                }
            } catch (Exception e) {
                logger.warn("记录当前匹配信息异常", e);
            }
        }

        public void updateFinalPredictResult(Optional<CallEventLogPO> eventLog, PredictResult predictResult) {
            try {
                if (Objects.isNull(predictResult) || !eventLog.isPresent()) {
                    return;
                }
                CallEventLogPO log = eventLog.get();
                CallEventLogExtraPO extra = log.getExtra();
                if (extra == null) {
                    extra = new CallEventLogExtraPO();
                    log.setExtra(extra);
                }
                extra.setFinalPredictResult(convert(predictResult));
            } catch (Exception e) {
                logger.warn("记录当前匹配信息异常", e);
            }
        }

        public void createVolumeSnapshotEvent(short[] volumes) {
            try {
                Optional<CallEventLogPO> logOpt = createEventLog(CallEventTypeEnums.USER_VOICE_VOLUME);
                logOpt.ifPresent(log -> {
                    short max = volumes[0];
                    for (int i = 1; i < volumes.length; i++) {
                        if (max < volumes[i]) {
                            max = volumes[i];
                        }
                    }
                    log.setMaxVolume(max);
                    log.setVolume0(volumes[0]);
                    log.setVolume1(volumes[1]);
                    log.setVolume2(volumes[2]);
                    log.setVolume3(volumes[3]);
                    log.setVolume4(volumes[4]);
                });
            } catch (Exception e) {
                logger.warn("记录用户输入音量值异常", e);
            }
        }
    }

    @Data
    private static class IntentBranchPredictDataContext {
        /**
         * 正则匹配用到的意图分支关键词列表
         */
        private List<String> keywordsIntentList;
        /**
         * 意向分支关键词-正则映射
         */
        private Map<String, Pattern> intentBranchKeywordPatternMap;
        /**
         * 意向分支关键词-对象
         */
        private Map<String, IntentBranchPO> keywordsBranchMap;

    }

    public interface StateRollbackEventProcessor {

        void recordState();

        void rollbackState();
    }

    @Data
    public static class GlobalAssignInfo {

        long startTimestamp = System.currentTimeMillis();

        String stepId;

        Long nodeId;

        String stepName;

        String nodeLabel;

        DynamicAssignmentPO dynamicAssignmentPO;
    }

    @Data
    public static class BusiMetricsDetail {
        /**
         * 用户说完次数, 即执行了预测的次数, 也可以用这个来计算无意图的次数
         * 目前用来判断是否连续命中听不清
         */
        AtomicInteger userSayFinishCount = new AtomicInteger();
        /**
         * 用户无应答次数, 用来最后判断是否属于[用户全程无应答]
         */
        AtomicInteger userSilenceCount = new AtomicInteger();
        /**
         * 累计命中听不清的次数
         */
        AtomicInteger totalInaudibleKnowledgeCount = new AtomicInteger();
        /**
         * 上一次命中听不清的流水号
         */
        AtomicInteger preInaudibleIndex = new AtomicInteger();
        /**
         * 连续命中听不清的次数
         */
        AtomicInteger continuousInaudibleKnowledgeCount = new AtomicInteger();

        AtomicInteger maxContinuousInaudibleKnowledgeCount = new AtomicInteger();
        /**
         * 机器人次数
         */
        AtomicInteger robotKnowledgeCount = new AtomicInteger();
        /**
         * 骂人次数
         */
        AtomicInteger abuseKnowledgeCount = new AtomicInteger();
        /**
         * 算法命中次数
         */
        AtomicInteger algorithmMatchCount = new AtomicInteger();
        /**
         * 正则命中次数
         */
        AtomicInteger regexMatchCount = new AtomicInteger();
        /**
         * ai无法应该命中次数
         */
        AtomicInteger aiUnknownCount = new AtomicInteger();
        /**
         * 命中默认分支次数
         */
        AtomicInteger defaultBranchCount = new AtomicInteger();

        public void copyTo(BusiMetricsDetail targetMetrics) {
            targetMetrics.userSayFinishCount.set(this.userSayFinishCount.get());
            targetMetrics.userSilenceCount.set(this.userSilenceCount.get());
            targetMetrics.totalInaudibleKnowledgeCount.set(this.totalInaudibleKnowledgeCount.get());
            targetMetrics.preInaudibleIndex.set(this.preInaudibleIndex.get());
            targetMetrics.continuousInaudibleKnowledgeCount.set(this.continuousInaudibleKnowledgeCount.get());
            targetMetrics.robotKnowledgeCount.set(this.robotKnowledgeCount.get());
            targetMetrics.abuseKnowledgeCount.set(this.abuseKnowledgeCount.get());
            targetMetrics.algorithmMatchCount.set(this.algorithmMatchCount.get());
            targetMetrics.regexMatchCount.set(this.regexMatchCount.get());
            targetMetrics.aiUnknownCount.set(this.aiUnknownCount.get());
            targetMetrics.defaultBranchCount.set(this.defaultBranchCount.get());
        }
    }

}
