package com.yiwise.aicall.engine.aliyun;

import com.alibaba.fastjson.JSONObject;
import com.yiwise.aicall.engine.config.ApplicationConfig;
import com.yiwise.aicall.engine.helper.EchoAnalyzer;
import com.yiwise.aicall.engine.model.*;
import com.yiwise.aicall.engine.robot.Robot;
import com.yiwise.aicall.engine.service.IntentAnalysisService;
import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.helper.*;
import com.yiwise.core.model.alimessagequeue.websocket.CallJobCsStaffTransferMsg;
import com.yiwise.core.model.analysis.RobotCallAnalysisResult;
import com.yiwise.core.model.bo.customerwhitelist.AddWhiteListBO;
import com.yiwise.core.model.bo.dialogflownodestats.DialogFlowNodeStatsDetailKey;
import com.yiwise.core.model.bo.robotcalljob.TaskCallResultBO;
import com.yiwise.core.model.bo.sms.SmsTemplateSendBO;
import com.yiwise.core.model.bo.websocket.TenantUserIdPrincipal;
import com.yiwise.core.model.dialogflow.entity.DialogBusiMetricsPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.nlp.bo.NlpAfterChatBO;
import com.yiwise.core.model.nlp.bo.NlpAfterKnowledgeBO;
import com.yiwise.core.model.vo.calldetail.CallMonitorMessage;
import com.yiwise.core.model.vo.callrecord.TransferCallVO;
import com.yiwise.core.model.vo.csseat.CallJobCsStaffTransferMsgVO;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.dialogflow.DialogBusiMetricsService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.monitor.AlgorithmConcurrencyService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.dialogflow.engine.share.*;
import com.yiwise.dialogflow.engine.share.enums.RobotSnapshotUsageTargetEnum;
import com.yiwise.rcs.api.enums.CustomerBlackListAddTypeEnum;
import javaslang.control.Try;
import lombok.Getter;
import lombok.Setter;
import net.sourceforge.peers.sip.transport.SipResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.net.ConnectException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.yiwise.base.common.helper.ServerInfoConstants.SERVER_HOSTNAME;
import static com.yiwise.core.config.ApplicationConstant.CALLJOB_SAE_PREFIX;

/**
 * <AUTHOR>
 */
public class AudioRecordAndIntentLevelAnalysisSoundManager extends FreeSwitchTransferSoundManager {
    private static final Logger logger = LoggerFactory.getLogger(AudioRecordAndIntentLevelAnalysisSoundManager.class);

    private static final IntentAnalysisService intentAnalysisService = AppContextUtils.getBean(IntentAnalysisService.class);
    private static final SmsTemplateService smsTemplateService = AppContextUtils.getBean(SmsTemplateService.class);
    private static final RestTemplate restTemplate = (RestTemplate) AppContextUtils.getBean("calloutRestTemplate");
    private static final DialogFlowNodeStatsService dialogFlowNodeStatsService = AppContextUtils.getBean(DialogFlowNodeStatsService.class);

    private static final CallRecordActionLogService callRecordActionLogService = AppContextUtils.getBean(CallRecordActionLogService.class);

    private static final DialogBusiMetricsService dialogBusiMetricsService = AppContextUtils.getBean(DialogBusiMetricsService.class);
    private static final AlgorithmConcurrencyService algorithmConcurrencyService = AppContextUtils.getBean(AlgorithmConcurrencyService.class);

    private final SoundManagerAudioRecorder audioRecorder;    // 录音类
    protected TaskCallResultBO taskCallResultInfo;
    protected RobotCallTask robotCallTask;
    protected boolean isValidUserPcm = true;
    protected boolean csTransferAccept = false;
    protected boolean intercepted = false;
    protected LocalDateTime csMonitorTime = null;
    // 微保项目增加信息 通话id 话单id
    protected String callSid;
    protected NlpAfterChatBO nlpAfterChatBO;
    protected Long transferDuration = 0L;
    protected List<TransferCallVO> transferToCsList = new ArrayList<>();

    public static final Integer LINE_INTERCEPT_SIP = 601;

    /**
     * 成功发送分机号码
     */
    @Setter
    @Getter
    private volatile boolean sendExtension = false;

    /**
     * 外呼监控的人工介入是否触发
     */
    protected CallMonitorMessage monitorMsg;

    @Setter
    @Getter
    private Long persistentCallRecordId;

    protected BotBusiMetrics botBusiMetrics;

    protected LLMTokenUsageInfo llmTokenUsageInfo;

    protected IntentLevelAnalysisResult intentLevelAnalysisResult;

    protected Long chatDuration = 0L;

	private final FailureRateRecorder canNotConnectRecorder;

    public AudioRecordAndIntentLevelAnalysisSoundManager(TenantPO tenant, Long tenantId, RobotCallTask robotCallTask, Long subRobotCallJobId, Robot robot, boolean isVerbalTrickTraining, RobotCallJobPO robotCallJob) {
        super(tenant, robotCallTask.getRobotTaskCallInfo().getRobotCallJobId(), subRobotCallJobId, robotCallTask, robot, isVerbalTrickTraining, robotCallJob);

        RobotCallTaskPO robotCallTaskInfo = robotCallTask.getRobotTaskCallInfo();
        this.robotCallTask = robotCallTask;
        CallOutAudioRetentionTimeEnum retentionTime;
        if (tenantPO == null || tenantPO.getCallOutAudioRetentionTime() == null) {
            retentionTime = CallOutAudioRetentionTimeEnum.getDefault();
        } else {
            retentionTime = tenantPO.getCallOutAudioRetentionTime();
        }
        this.audioRecorder = new SoundManagerAudioRecorder();
        this.audioRecorder.init(tenantId, robotCallTaskInfo.getRobotCallJobId(), getTaskId(), getIdentifyId(), retentionTime, robotCallTask.getCompressAudio());
        this.taskCallResultInfo = new TaskCallResultBO();
	    canNotConnectRecorder = new FailureRateRecorder(redisOpsService, FailureRateRecorder.Topic.canNotConnect, "JobId:" + robotCallTaskInfo.getRobotCallJobId());
	    canNotConnectRecorder.success();
    }

    /**
     * 用户语音流
     */
    @Override
    public int writeData(byte[] buffer, int offset, int length) {
        if (!isFinished()) {
            if (isSip200Success()) {
                audioRecorder.writeUserAudio(buffer, offset, length);
            } else if (BooleanUtils.isTrue(robotCallTask.getExtensionMode())) {
                if (BooleanUtils.isTrue(isExtensionSip200Success())) {
                    // 分机号码，收到200后再保存早期媒体
                    audioRecorder.writeEarlyMediaAudio(buffer, offset, length);
                }
            } else {
                // 写入 earlyMedia
                audioRecorder.writeEarlyMediaAudio(buffer, offset, length);
            }

            if (BooleanUtils.isTrue(robotCallTask.getExtensionMode())) {
                //拨打分机号
                dialExtensionNumber(buffer, offset, length);

                //判断是否接通
                if (!isSip200Success() && sendExtension) {
                    //发送音频数据给算法
                    initExtensionNettyAgent();
                    if (Objects.nonNull(getExtensionNettyAgent())) {
                        getExtensionNettyAgent().send(buffer, offset, length);
                    } else {
                        overTimeHangup("调用算法判断接通失败");
                        logger.info("initExtensionNettyAgent failed overTimeHangup");
                    }
                }
                if (sendExtension) {
                    //保存完整录音
                    audioRecorder.writeExtensionAudio(buffer, offset, length);
                }
            }

            return super.writeData(buffer, offset, length);
        } else {
            return length;
        }
    }

    @Override
    protected void writeSendToAsrAudio(byte[] buffer, int offset, int length) {
        try {
            audioRecorder.writeSendAsrAudio(buffer, offset, length);
        } catch (Exception e) {
            logger.warn("[LogHub_Warn]writeSendToAsrAudio error", e);
        }
    }

    @Override
    public void error(SipResponse sipResponse) {
        logger.info("收到的ERROR信令是 {}", sipResponse.getStatusCode());
        if(Objects.equals(sipResponse.getStatusCode(), LINE_INTERCEPT_SIP)){
            intercepted = true;
        }
        super.error(sipResponse);
    }
    /**
     * AI的语言流
     */
    @Override
    public byte[] readData() {
        byte[] buffer = super.readData();
        if (isSip200Success() && !isFinished()) {
            audioRecorder.writeAiAudio(buffer);
        }
        return buffer;
    }

    @Override
    public void syncDoAfterTaskFinished() {
        super.syncDoAfterTaskFinished();

        try {
            // 计算通话时长
            // 分机号 呼损
            if (calleePickup.get()) {
                if (hangupTime == 0L) {
                    hangupTime = System.currentTimeMillis();
                    logger.warn("hangupTime 为0");
                }
                long pickupTime = hangupTime;
                if (softPhone.getPickupTime() > 0L) {
                    pickupTime = softPhone.getPickupTime();
                }
                chatDuration = (long) Math.ceil((hangupTime - pickupTime) * 1.0 / 1000);
                chatDuration = chatDuration == 0 ? 1 : chatDuration;
            }

            audioRecorder.closeAudioRecordingAndFlush();

            try {
                // 联通小号外呼的外呼失败，无法接通等，暂停20秒
                boolean needSleep = false;
                if (!isCalleePickup()) {
                    if (StringUtils.equals(robotCallTask.getPhoneNumberWithPrefixInfo().getPrefix(), AXHelper.AX_FAIL_PREFIX)) {
                        needSleep = true;
                    }

                    String callerSipAccount = robotCallTask.getRobotCallPhoneNumberWithSipInfo().getSipAccount();
                    if (AXHelper.isAxLine(callerSipAccount) || CustomizedAxHelper.isCustomizedAx(callerSipAccount)) {
                        File earlyMediaPcmFile = getEarlyMediaPcmFile();
                        // 如果文件不存在或者大小为0，说明没拿到earlyMedia, 属于外呼失败，但对外展示为无法接通
                        if (!earlyMediaPcmFile.exists() || !earlyMediaPcmFile.isFile() || earlyMediaPcmFile.length() == 0) {
                            needSleep = true;
                        }
                    }
                }
                if (needSleep) {
                    logger.info("AX外呼失败，暂停20秒");
                    MyThreadUtils.sleepSeconds(20);
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn]联通小号失败处理异常", e);
            }

            // 录音上传，并将上传后的信息
            Try.run(() -> {
                File transferFile = null;
                boolean answer = false;
                if (isTransferThirdParty() && CallRecordTransferTypeEnum.TRANSFERED.equals(transferType)) {
                    //第三方呼叫中心
                    FreeswitchInfoPO freeswitchInfo = getFreeswitchInfo();
                    // 转人工录音文件名
                    String fileName = "record/cs-external-record-" + robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() + "-" + robotCallTask.getRobotTaskCallInfo().getRobotCallTaskId();
                    transferFile = getTransferFile(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), fileName, freeswitchInfo.getName());
                    logger.info("第三方呼叫中心转人工录音获取");
                    transferEndTime = LocalDateTime.now();
                    String answerTimeFileName = "cs-external-record-" + robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() + "-" + robotCallTask.getRobotTaskCallInfo().getRobotCallTaskId();
                    transferAnswerTime = getTransferAnswerTime(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), answerTimeFileName);
                    if (transferAnswerTime != null && Duration.between(transferAnswerTime, LocalDateTime.now()).getSeconds() < 10 * 60) {
                        answer = true;
                    }
                } else {
                    // 使用动态生成的转人工才需要下列步骤
                    if (CallRecordTransferTypeEnum.TRANSFERED.equals(transferType) && isUseDynamicDialplan()) {
                        logger.info("动态生成的转人工录音获取");
                        FreeswitchInfoPO freeswitchInfo = getFreeswitchInfo();
                        // 转人工录音文件名
                        String fileName = getIdentifyId() + "-transfertohuman";
                        transferFile = getTransferFile(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), fileName, freeswitchInfo.getName());
                        transferEndTime = LocalDateTime.now();
                        String answerTimeFileName = "record-transfer" + "-" + getIdentifyId();
                        transferAnswerTime = getTransferAnswerTime(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), answerTimeFileName);
                        if (transferAnswerTime != null && Duration.between(transferAnswerTime, LocalDateTime.now()).getSeconds() < 10 * 60) {
                            answer = true;
                        }
                    }
                }

                //人工介入
                if (getCsTransferNotify() == CsTransferNotifyEnum.NOTIFY_SUCCESS) {
                    try {
                        logger.info("人工介入的录音获取");
                        //需要查找人工坐席所在freeswitch的录音
                        transferFile = getCsTransferFile(getCsTransFsInfo().getFreeswitchHost(), getCsTransFsInfo().getAudioDownloadPort(), "intercept-record-" + getDialCode());
                        transferEndTime = LocalDateTime.now();
                        //这个放在获取录音文件后
                        csMonitorTime = getCsEavesdropTime(getCsTransFsInfo().getFreeswitchHost(), getCsTransFsInfo().getAudioDownloadPort(), "eavesdrop-" + getDialCode());
                        if (csTransferAccept) {
                            answer = true;
                        }
                    } catch (Exception e) {
                        logger.error("获取转人工录音失败", e);
                    }
                }

                //新的人工介入
                if (isNewIntercept()) {
                    try {
                        //第三方呼叫中心
                        FreeswitchInfoPO freeswitchInfo = getFreeswitchInfo();
                        // 转人工录音文件名
//                        String fileName = "record/cs-external-record-" + robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() + "-" + robotCallTask.getRobotTaskCallInfo().getRobotCallTaskId();
                        String fileName = "record/intercept-record-" + getSipAccountInfo().getSipAccount() + getCallee();
                        transferFile = getTransferFile(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), fileName, freeswitchInfo.getName());
                        logger.info("第三方呼叫中心人工计入录音获取");
                        transferEndTime = LocalDateTime.now();
                        String answerTimeFileName = "intercept-record-" + transferCallRecordId;
                        transferAnswerTime = getTransferAnswerTime(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), answerTimeFileName);
                        if (transferAnswerTime != null && Duration.between(transferAnswerTime, LocalDateTime.now()).getSeconds() < 10 * 60) {
                            answer = true;
                        }
                        logger.debug("新人工介入， transferAnswerTime：{}", transferAnswerTime);
                    } catch (Exception e) {
                        logger.error("新人工介入获取录音失败", e);
                    }
                }

                // 外呼监控人工介入
                if (isMonitorTransfer()) {
                    try {
                        logger.debug("触发外呼监控人工介入");
                        // transferFile不为空说明已触发了其他转人工规则,transferFile已下载完成不能再重新下载
                        // 触发了外呼监控介入则需要连接AI音频与介入音频,answer置为true
                        if (!wavFileExists(transferFile)) {
                            // 需要查找人工坐席所在freeswitch的录音
                            transferFile = getCsTransferFile(monitorMsg.getFreeswitchHost(), monitorMsg.getAudioDownloadPort(), "intercept-record-" + monitorMsg.getDialCode());
                            transferEndTime = LocalDateTime.now();
                            // 这个放在获取录音文件后
                            csMonitorTime = getCsEavesdropTime(monitorMsg.getFreeswitchHost(), monitorMsg.getAudioDownloadPort(), "eavesdrop-" + monitorMsg.getDialCode());
                            logger.info("外呼监控人工介入音频获取");
                        } else {
                            logger.debug("人工介入音频已被其他触发的转人工规则获取");
                        }
                        answer = true;
                    } catch (Exception e) {
                        logger.error("处理外呼监控人工介入音频出错", e);
                    }
                }

	            taskCallResultInfo.setCsTransferTrigger(getCsTransferNotify() == CsTransferNotifyEnum.NOTIFY_SUCCESS || getCsTransferNotify() == CsTransferNotifyEnum.NOTIFY_FAIL
			            || CallRecordTransferTypeEnum.TRANSFERED.equals(transferType) || CallRecordTransferTypeEnum.TRANSFER_CUSTOMER_FAILED.equals(transferType)
			            || CallRecordTransferTypeEnum.TRANSFER_MANUAL_FAILED.equals(transferType) || CallRecordTransferTypeEnum.TRANSFER_ERROR.equals(transferType));

	            taskCallResultInfo.setCsTransferAccept(csTransferAccept);

                //如果坐席并没有发起监听。并且是ws推送成功的情况下, 需要排除移动端的人工介入
                if (!taskCallResultInfo.getCsTransferAccept() && taskCallResultInfo.getCsTransferTrigger() && StringUtils.isBlank(csTelTransfer)) {
                    //推送通话结束的消息
                    //初始化消息
                    logger.info("推送通话结束给人工坐席");
                    CallJobCsStaffTransferMsgVO msgVO = new CallJobCsStaffTransferMsgVO();
                    msgVO.setIdentifyId(getIdentifyId());
                    msgVO.setCallStatus("HANGUP");
                    msgVO.setRobotCallJobId(this.robotCallTask.getRobotTaskCallInfo().getRobotCallJobId());
                    TenantUserIdPrincipal tenantUserIdPrincipal = new TenantUserIdPrincipal(this.robotCallTask.getRobotTaskCallInfo().getTenantId(), this.getUserId(), this.getUserSessionId());
                    webSocketOverMQService.sendCallCsStaffTransferToUser(tenantUserIdPrincipal.getName(), CallJobCsStaffTransferMsg.toMsg(msgVO));
                }

                try {
                    transferFile = getTransferToCsFile(transferFile);
                    if (answer) {
                        if (wavFileExists(transferFile)) {
                            transferDuration = audioRecorder.getDuration(transferFile);
                        }
                    } else {
                        logger.info("通过判断转的录音虽然有值，但是未成功，录音文件应是早期媒体等");
                    }
                } catch (Exception e) {
                    logger.warn("获取人工的时间错误", e);
                }
                AudioInfoBO audioInfo = audioRecorder.handleAudio(isUseYiwiseAsr(), isCalleePickup(), 0, transferFile, sendExtension, getExtensionAudioPickUpOffset());

                taskCallResultInfo.setFullAudioUrl(audioInfo.getFullAudioUrl());
                taskCallResultInfo.setCustomerAudioUrl(audioInfo.getCustomerAudioUrl());
                taskCallResultInfo.setChatDuration(chatDuration);
            }).onFailure(e -> logger.error("[LogHub_Warn]处理音频并上传至oss出错, TaskId={}", getTaskId(), e));

            try {
                boolean isEcho = EchoAnalyzer.isEcho(getCallDetailInfoBOList());
                if (isEcho) {
                    isValidUserPcm = false;
                    logger.info("user pcm file is not valid, echo detected.");
                }
            } catch (Exception e) {
                logger.error("echo analyze fail", e);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取音频失败", e);
        }
    }

    private File getTransferFile(String fsHost, Integer fsAudioDownloadPort, String fileName, String fsName) {
        File transferFile = null;
        try {
            // 获取录音文件前延迟1s 防止获取空文件
            Thread.sleep(1000);
            String fsSourceFileUrl = "http://" + fsHost + ":" + fsAudioDownloadPort + "/" + fileName + ".wav";
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFSDownloadTempFilePath(getIdentifyId());
            transferFile = new File(fsDownloadTempFilePath);
            if (!transferFile.exists()) {
                transferFile.getParentFile().mkdirs();
                transferFile.createNewFile();
            }
            long lastLength = 0L;
            long thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
            //
            while (lastLength < thisLength) {
                Thread.sleep(3000);
                logger.info("转人工未完成,lastLength={},thisLength={}", lastLength, thisLength);
                lastLength = thisLength;
                thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
                updateJobHeartBeatWhenWaiting();
            }
            // 下载文件
            MyFileUtils.downloadFromUrl(fsSourceFileUrl, transferFile);
        } catch (Exception e) {
            if (e instanceof ConnectException) {
                logger.error("[LogHub_Warn] {}nginx连接失败 {}:{}拒绝连接", fsName, fsHost, fsAudioDownloadPort, e);
            }
            // 转人工失败标记
            setTransferType(CallRecordTransferTypeEnum.TRANSFER_ERROR);
            // 文件置空 防止合并文件出错
            transferFile = null;
            logger.error("检查并获取转人工录音失败", e);
        }
        return transferFile;
    }

    public LocalDateTime getTransferAnswerTime(String fsHost, Integer fsAudioDownloadPort, String fileName) {
        try {
            String fsSourceFileUrl = "http://" + fsHost + ":" + fsAudioDownloadPort + "/record/" + fileName + ".txt";
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFsDownloadTxtFilePath(getIdentifyId());
            File answerTimeFile = new File(fsDownloadTempFilePath);
            if (!answerTimeFile.exists()) {
                answerTimeFile.getParentFile().mkdirs();
                answerTimeFile.createNewFile();
            }
            try {
                MyFileUtils.downloadFromUrl(fsSourceFileUrl, answerTimeFile);
                String timeStr = MyFileUtils.readTxtFileFirstLineContent(answerTimeFile);
                logger.info("转人工时间记录文件读取={}", timeStr);
                if (StringUtils.isBlank(timeStr)) {
                    return null;
                }
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return LocalDateTime.parse(timeStr, fmt);
            } catch (Exception e) {
                logger.error("转人工时间获取失败，", e);
            }

        } catch (Exception e) {
            logger.error("检查并转人工时间失败", e);
        }
        return null;
    }

    private long contentLength(String fsSourceFileUrl) {
        try {
            return MyFileUtils.getRemoteFileLength((fsSourceFileUrl));
        } catch (Exception e) {
            logger.info("转人工介入录音获取失败，", e);
            return 0;
        }
    }

    private File getCsTransferFile(String fsHost, Integer fsAudioDownloadPort, String fileName) {
        File transferFile = null;
        try {
            String fsSourceFileUrl = "http://" + fsHost + ":" + fsAudioDownloadPort + "/record/" + fileName + ".wav";
            logger.debug("fsSourceFileUrl=[{}]", fsSourceFileUrl);
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFSDownloadTempFilePath(getIdentifyId());
            transferFile = new File(fsDownloadTempFilePath);
            if (!transferFile.exists()) {
                transferFile.getParentFile().mkdirs();
                transferFile.createNewFile();
            }
            int count = 0;
            long lastLength = -1L;
            long thisLength = contentLength(fsSourceFileUrl);
            while (lastLength < thisLength) {
                Thread.sleep(5000);
                logger.info("转人工介入未完成1,lastLength={},thisLength={}", lastLength, thisLength);
                lastLength = thisLength;
                thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
                if (thisLength == 44 || lastLength == 44) {
                    logger.info("如果发现人工介入的录音生成比较慢，则再次等待5秒重新获取");
                    Thread.sleep(5000);
                    thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
                }
                if (count == 0 && thisLength == lastLength && lastLength > 0) {
                    //说明并没有人工介入。此文件为以前通话遗留的
                    logger.info("此通话坐席并未发生人工介入");
                    return null;
                }
                count++;
                logger.info("转人工介入未完成2,lastLength={},thisLength={}", lastLength, thisLength);
                if (thisLength > 0) {
                    csTransferAccept = true;
                }
                updateJobHeartBeatWhenWaiting();
            }
            if (thisLength == 0) {
                return null;
            }
            // 下载文件
            MyFileUtils.downloadFromUrl(fsSourceFileUrl, transferFile);
        } catch (Exception e) {
            logger.error("检查并获取人工介入录音失败", e);
        }
        return transferFile;
    }

    public LocalDateTime getCsEavesdropTime(String fsHost, Integer fsAudioDownloadPort, String fileName) {
        try {
            String fsSourceFileUrl = "http://" + fsHost + ":" + fsAudioDownloadPort + "/record/" + fileName + ".txt";
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFsDownloadTxtFilePath(getIdentifyId());
            File eavesdropFile = new File(fsDownloadTempFilePath);
            if (!eavesdropFile.exists()) {
                eavesdropFile.getParentFile().mkdirs();
                eavesdropFile.createNewFile();
            }
            try {
                MyFileUtils.downloadFromUrl(fsSourceFileUrl, eavesdropFile);
                String timeStr = MyFileUtils.readTxtFileFirstLineContent(eavesdropFile);
                if (StringUtils.isBlank(timeStr)) {
                    return null;
                }
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return LocalDateTime.parse(timeStr, fmt);
            } catch (Exception e) {
                logger.error("转人工介入监听时间获取失败，", e);
            }

        } catch (Exception e) {
            logger.error("检查并转人工介入监听时间失败", e);
        }
        return null;
    }

    @Override
    public RobotCallAnalysisResult intentAnalyzeInCall() {
        Map<DialogFlowNodeStatsDetailKey, AtomicInteger> nodeStatsData = new HashMap<>();
        if (semanticManager != null) {
            nodeStatsData.putAll(semanticManager.getNodeStatsMap());
        }

        // 通话时长
        Long chatDuration = (System.currentTimeMillis() - getSip200SuccessTimestamp()) / 1000L;
        // 通话状态
        DialStatusEnum dialStatus = DialStatusEnum.ANSWERED;
        // 转人工算作AI挂断
        boolean isUserHangUp = false;

        Optional<RobotCallAnalysisResult> robotCallAnalysisResultOpt;
        if (getEngineVersion().isV3()) {
            robotCallAnalysisResultOpt = Try.of(() -> {
                        Long callRecordId = getPersistentCallRecordId();

                        CallDataInfo callDataInfo = new CallDataInfo();
                        callDataInfo.setStartTime(getSip200SuccessTimestamp());
                        callDataInfo.setEndTime(System.currentTimeMillis());
                        callDataInfo.setChatDuration(chatDuration);
                        callDataInfo.setDialStatus(dialStatus.getCode());
                        callDataInfo.setUserHangup(isUserHangUp);
                        callDataInfo.setCallJobId(robotCallTask.getRobotTaskCallInfo().getRobotCallJobId());
                        callDataInfo.setTenantId(robotCallTask.getRobotTaskCallInfo().getTenantId());
                        callDataInfo.setCallRecordId(callRecordId);

                        Long callTaskId = robotCallTask.getRobotCallTaskId();
                        IntentLevelAnalysisResult result = V3ChatController.analysisInCall(callTaskId, callDataInfo);
                        return MyBeanUtils.copy(result, RobotCallAnalysisResult.class);
                    }
            ).onFailure(e -> logger.error("通话中进行意向分析失败", e)).toJavaOptional();
        } else if (getEngineVersion().isV2()) {
            robotCallAnalysisResultOpt = Try.of(
                            () -> intentAnalysisService.analysis(dialStatus, getCallDetailInfoBOList(),
                                    dialogFlow, robotCallTask.getRobotCallTaskId(), chatDuration,
                                    isUserHangUp, robotCallTask, getIsAiWait(), getEngineVersion().isV2(), null,
                                    csTransferAccept, Collections.emptySet(), nodeStatsData))
                    .onFailure(e -> logger.error("[LogHub_Warn]获取意向分析结果出错", e)).toJavaOptional();
        } else {
            // v1
            List<AnalyzeDetail> analyzeDetailList = Objects.isNull(semanticManager) ? Collections.emptyList() : semanticManager.getAnalyzeDetailList();
            robotCallAnalysisResultOpt = Try.of(
                            () -> intentAnalysisService.analysis(dialStatus, getCallDetailInfoBOList(),
                                    dialogFlow, robotCallTask.getRobotCallTaskId(), chatDuration,
                                    isUserHangUp, robotCallTask, getIsAiWait(), getEngineVersion().isV2(), null,
                                    csTransferAccept, Collections.emptySet(), nodeStatsData, analyzeDetailList))
                    .onFailure(e -> logger.error("[LogHub_Warn]获取意向分析结果出错", e)).toJavaOptional();
        }
        return robotCallAnalysisResultOpt.orElse(new RobotCallAnalysisResult());
    }

    @Override
    protected void doAfterTaskAnalyzeMission() {
        super.doAfterTaskAnalyzeMission();

        Try.run(() -> {

            // 设置通话状态
            taskCallResultInfo.setResultStatus(getFinishStatus());

            //设置振铃时长
            try {
                if (BooleanUtils.isTrue(robotCallTask.getExtensionMode())) {
                    //分机号外呼
                    if (softPhone.getEarlyMediaTime() > 0 && isExtensionSip200Success()) {
                        dealRingDuration();
                    } else {
                        taskCallResultInfo.setRingDuration(0L);
                    }
                } else if (softPhone.getEarlyMediaTime() > 0) {
                    //正常号码外呼
                    dealRingDuration();
                } else {
                    taskCallResultInfo.setRingDuration(0L);
                }
            } catch (Exception e) {
                taskCallResultInfo.setRingDuration(0L);
                logger.error("dealRingDuration error", e);
            }

            // 删除本地录音文件
            boolean isSAE = StringUtils.startsWith(SERVER_HOSTNAME, CALLJOB_SAE_PREFIX);
            audioRecorder.deleteLocalAudioFiles(isSAE);

            List<NlpAfterKnowledgeBO> nlpAfterKnowledgeBO = nlpAfterChatBO == null ? null : nlpAfterChatBO.getIntentLevelInfoList();

            Set<Long> alreadySendSmsTemplateIdSet = Collections.emptySet();
            Map<DialogFlowNodeStatsDetailKey, AtomicInteger> nodeStatsData = new HashMap<>();
            if (semanticManager != null) {
                alreadySendSmsTemplateIdSet = semanticManager.getAlreadySendSmsTemplateIdSet();
                nodeStatsData.putAll(semanticManager.getNodeStatsMap());
            }

            if (hangupTime == 0) {
                hangupTime = System.currentTimeMillis();
                logger.warn("hangupTime 为0");
            }
            long pickup = softPhone.getPickupTime() == 0 ? hangupTime : softPhone.getPickupTime();
            long chatDuration = (long) Math.ceil((hangupTime - pickup) * 1.0 / 1000);
            Set<Long> finalAlreadySendSmsTemplateIdSet = alreadySendSmsTemplateIdSet;
            // 分析意向结果
            Optional<RobotCallAnalysisResult> robotCallAnalysisResultOpt = Optional.empty();
            if (getEngineVersion().isV3()) {
                Long callRecordId = getPersistentCallRecordId();

                CallDataInfo callDataInfo = new CallDataInfo();
                callDataInfo.setStartTime(pickup);
                callDataInfo.setEndTime(hangupTime);
                callDataInfo.setChatDuration(chatDuration);
                callDataInfo.setDialStatus(taskCallResultInfo.getResultStatus().getCode());
                callDataInfo.setUserHangup(this.isCustomerHangup.get());
                callDataInfo.setCallJobId(robotCallTask.getRobotTaskCallInfo().getRobotCallJobId());
                callDataInfo.setTenantId(robotCallTask.getRobotTaskCallInfo().getTenantId());
                callDataInfo.setCallRecordId(callRecordId);

                Long callTaskId = robotCallTask.getRobotCallTaskId();
                intentLevelAnalysisResult = V3ChatController.analysis(callTaskId, callDataInfo);
                RobotCallAnalysisResult copy = MyBeanUtils.copy(intentLevelAnalysisResult, RobotCallAnalysisResult.class);

                // 处理对话业务指标数据
                if (Objects.nonNull(intentLevelAnalysisResult)) {
                    botBusiMetrics = intentLevelAnalysisResult.getBotBusiMetrics();
                    llmTokenUsageInfo = intentLevelAnalysisResult.getLlmTokenUsageInfo();
                }

                if (CollectionUtils.isNotEmpty(intentLevelAnalysisResult.getAddWhiteListGroupIdList())) {
                    List<AddWhiteListBO> addWhiteListBOList = new ArrayList<>();
                    intentLevelAnalysisResult.getAddWhiteListGroupIdList().forEach(whiteListGroup -> {
                        AddWhiteListBO addWhiteListBO = new AddWhiteListBO();
                        BeanUtils.copyProperties(whiteListGroup, addWhiteListBO);
                        addWhiteListBO.setAddType(CustomerBlackListAddTypeEnum.OTHER);
                        addWhiteListBOList.add(addWhiteListBO);
                    });
                    copy.setAddWhiteListGroupIdList(addWhiteListBOList);
                }
                robotCallAnalysisResultOpt = Optional.of(copy);

                // v3需要记录特殊动作执行结果
                try {
                    if (Objects.nonNull(V3ChatController.getSessionInfo())
                            && RobotSnapshotUsageTargetEnum.SPEECH_TEST.equals(V3ChatController.getSessionInfo().getUsageTarget())
                            && CollectionUtils.isNotEmpty(intentLevelAnalysisResult.getIntentRuleActionResultList())) {
                        List<CallRecordActionLogPO> list = intentLevelAnalysisResult.getIntentRuleActionResultList().stream()
                                .map(item -> {
                                    CallRecordActionLogPO log = new CallRecordActionLogPO();
                                    log.setCallRecordId(callRecordId);
                                    log.setDialogFlowId(dialogFlow.getId());
                                    log.setReason(item.getReason());
                                    log.setRuleId(item.getRuleId());
                                    log.setCustomerTagIdList(item.getCustomerTagIdList());
                                    log.setSmsTemplateIdList(item.getSmsTemplateIdList());
                                    log.setWhiteGroupIdList(item.getWhiteGroupIdList());
                                    log.setAddWechat(item.getAddWechat());
                                    return log;
                                }).collect(Collectors.toList());
                        callRecordActionLogService.batchInsert(callRecordId, list);
                    }
                } catch (Exception e) {
                    logger.warn("记录特殊动作执行结果失败", e);
                }

            } else if (getEngineVersion().isV2()) {
                robotCallAnalysisResultOpt = Try.of(
                                () -> intentAnalysisService.analysis(taskCallResultInfo.getResultStatus(), getCallDetailInfoBOList(),
                                        dialogFlow, robotCallTask.getRobotCallTaskId(), chatDuration,
                                        this.isCustomerHangup.get(), robotCallTask, getIsAiWait(), getEngineVersion().isV2(), nlpAfterKnowledgeBO,
                                        taskCallResultInfo.getCsTransferAccept(), finalAlreadySendSmsTemplateIdSet, nodeStatsData))
                        .onFailure(e -> logger.error("[LogHub_Warn]获取意向分析结果出错", e)).toJavaOptional();
            } else {
                // v1
                List<AnalyzeDetail> analyzeDetailList = Objects.isNull(semanticManager) ? Collections.emptyList() : semanticManager.getAnalyzeDetailList();
                robotCallAnalysisResultOpt = Try.of(
                                () -> intentAnalysisService.analysis(taskCallResultInfo.getResultStatus(), getCallDetailInfoBOList(),
                                        dialogFlow, robotCallTask.getRobotCallTaskId(), chatDuration,
                                        this.isCustomerHangup.get(), robotCallTask, getIsAiWait(), getEngineVersion().isV2(), nlpAfterKnowledgeBO,
                                        taskCallResultInfo.getCsTransferAccept(), finalAlreadySendSmsTemplateIdSet, nodeStatsData, analyzeDetailList))
                        .onFailure(e -> logger.error("[LogHub_Warn]获取意向分析结果出错", e)).toJavaOptional();
            }

            // 属性收集log
            robotCallAnalysisResultOpt.ifPresent(result -> {
                Map<Integer, Map<Long, String>> map = result.getExtraInfoMapId();
                if (MapUtils.isEmpty(map)) {
                    logger.debug("属性收集结束, extraInfoMapId is empty");
                } else {
                    map.forEach((key1, value1) -> value1.forEach((key2, value2) -> logger.debug("属性收集结束, extraInfoMapId: {}, {}, {}", key1, key2, value2)));
                }
            });

            robotCallAnalysisResultOpt.ifPresent(robotCallAnalysisResult -> {
                if (!isValidUserPcm && (robotCallAnalysisResult.getIntentLevel().equals(0) || robotCallAnalysisResult.getIntentLevel().equals(1))) {
                    logger.info("user pcm not valid, change intentionLevel to D");
                    // 产品中已增加少于5个字的意向判别，此处应不需要了
                    robotCallAnalysisResult.setIntentLevel(IntentLevelEnum.D.getCode());
                }

                taskCallResultInfo.setIntentLevel(robotCallAnalysisResult.getIntentLevel());
                taskCallResultInfo.setDeclineCount(robotCallAnalysisResult.getDeclineCount());
                taskCallResultInfo.setCustomerConcern(robotCallAnalysisResult.getCustomerFocus());
                taskCallResultInfo.setChatRound(robotCallAnalysisResult.getAiRounds());
                taskCallResultInfo.setAnalysisBasis(robotCallAnalysisResult.getBasis());
                taskCallResultInfo.setCustomerAttribute(robotCallAnalysisResult.getCustomerAttribute());
                taskCallResultInfo.setSkip(robotCallAnalysisResult.isSkip());
                taskCallResultInfo.setDefiniteCount(robotCallAnalysisResult.getDefiniteCount());
                taskCallResultInfo.setBusinessQuestionCount(robotCallAnalysisResult.getBusinessQuestionCount());
                taskCallResultInfo.setNegativeCount(robotCallAnalysisResult.getNegativeCount());
                taskCallResultInfo.setAddWhiteList(robotCallAnalysisResult.getAddWhiteList());
                taskCallResultInfo.setAddWhiteListGroupIdList(robotCallAnalysisResult.getAddWhiteListGroupIdList());
                taskCallResultInfo.setExtraInfo(robotCallAnalysisResult.getExtraInfo());
                taskCallResultInfo.setCustomerLevelTagDetailIdSet(robotCallAnalysisResult.getCustomerLevelTagDetailIdSet());
                taskCallResultInfo.setCustomMetricsMap(robotCallAnalysisResult.getCustomMetricsMap());
                if (getEngineVersion().isV2()) {
                    Set<Long> smsTemplateIdSet = this.nlpSemanticManager.getSmsTemplateIdSet();
                    logger.info("nlp发送短信指令，模板id的集合={}", smsTemplateIdSet);
                    Set<Long> smsTemplateIds = robotCallAnalysisResult.getSmsTemplateIds();
                    if (CollectionUtils.isNotEmpty(smsTemplateIds)) {
                        robotCallAnalysisResult.getSmsTemplateIds().addAll(smsTemplateIds);
                    } else {
                        smsTemplateIds = new HashSet<>(smsTemplateIdSet);
                        robotCallAnalysisResult.setSmsTemplateIds(smsTemplateIds);
                    }
                }
                if (CollectionUtils.isNotEmpty(robotCallAnalysisResult.getSmsTemplateIds())) {
                    Set<Long> ids = robotCallAnalysisResult.getSmsTemplateIds();
                    logger.info("意向规则判断获取短信模板,id={}", ids);
                    List<SmsTemplateSendBO> smsTemplateInfoList = new ArrayList<>();
                    if (tenantPO != null && ApplicationConstant.QIYU_DISTRIBUTOR.contains(tenantPO.getDistributorId())) {
                        // 七鱼短信模版只有id
                        List<SmsTemplateSendBO> finalSmsTemplateInfoList = smsTemplateInfoList;
                        ids.forEach(id -> {
                            SmsTemplateSendBO smsTemplateInfoBO = new SmsTemplateSendBO();
                            smsTemplateInfoBO.setSmsTemplateId(id);
                            finalSmsTemplateInfoList.add(smsTemplateInfoBO);
                        });
                    } else {
                        // 话术短信随任务配置 传-1
                        if (ids.contains(-1L)) {
                            Long getBotSmsTemplateId = getBotSmsTemplateId(-1L);
                            ids.add(getBotSmsTemplateId);
                        }
                        smsTemplateInfoList = smsTemplateService.getSmsTemplateSendBOList(tenantId, ids);
                    }
                    taskCallResultInfo.setSmsTemplateInfoList(smsTemplateInfoList);
                }
            });

            if (getEngineVersion().isV1()) {
                // 统计意向规则命中率
                robotCallAnalysisResultOpt.ifPresent(robotCallAnalysisResult -> {
                    logger.info("robotCallAnalysisResult={}", JsonUtils.object2String(robotCallAnalysisResult));
                    if (StringUtils.isBlank(robotCallAnalysisResult.getMatchedIntentRuleId())) {
                        return;
                    }

                    if (robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() == null || robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() < 1) {
                        if (BooleanUtils.isNotTrue(ApplicationConstant.ENABLE_TRAINING_DIALOG_FLOW_NODE_STATS)) {
                            logger.info("当前环境训练测试数据不进行统计");
                            return;
                        }
                    }
                    Long dialogFlowId = dialogFlow.getId();
                    String intentRuleId = robotCallAnalysisResult.getMatchedIntentRuleId();
                    Long tenantId = 0L;
                    Long distributorId = 0L;
                    if (Objects.nonNull(tenantPO)) {
                        tenantId = tenantPO.getTenantId();
                        distributorId = tenantPO.getDistributorId();
                    }

                    dialogFlowNodeStatsService.sendIntentRuleStats(distributorId, tenantId, robotCallTask.getRobotTaskCallInfo().getRobotCallJobId(), dialogFlowId, intentRuleId, startTime);
                });

                // 统计标签规则命中率
                robotCallAnalysisResultOpt.ifPresent(robotCallAnalysisResult -> {
                    if (CollectionUtils.isEmpty(robotCallAnalysisResult.getMatchedCustomerTagRuleIdSet())) {
                        return;
                    }

                    if (robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() == null || robotCallTask.getRobotTaskCallInfo().getRobotCallJobId() < 1) {
                        if (BooleanUtils.isNotTrue(ApplicationConstant.ENABLE_TRAINING_DIALOG_FLOW_NODE_STATS)) {
                            logger.info("当前环境训练测试数据不进行统计");
                            return;
                        }
                    }
                    Long dialogFlowId = dialogFlow.getId();
                    Long tenantId = 0L;
                    Long distributorId = 0L;
                    if (Objects.nonNull(tenantPO)) {
                        tenantId = tenantPO.getTenantId();
                        distributorId = tenantPO.getDistributorId();
                    }

                    dialogFlowNodeStatsService.sendCustomerTagRuleStats(distributorId, tenantId, robotCallTask.getRobotTaskCallInfo().getRobotCallJobId(), dialogFlowId, robotCallAnalysisResult.getMatchedCustomerTagRuleIdSet(), startTime);
                });
            }

            try {
                Long callRecordId = getPersistentCallRecordId();
                Long jobId = robotCallTask.getRobotTaskCallInfo().getRobotCallJobId();
                Long dialogFlowId = dialogFlow.getId();
                CallJobHangupEnum hangupBy = CallJobHangupEnum.INITIAL_HANGUP;
                if (isCustomerHangup()) {
                    hangupBy = CallJobHangupEnum.REMOTE_HANGUP;
                }

                Map<Long, Long> customerTagCountMap = new HashMap<>();
                robotCallAnalysisResultOpt.ifPresent(robotCallAnalysisResult -> {
                    if (CollectionUtils.isNotEmpty(robotCallAnalysisResult.getCustomerLevelTagDetailIdSet())) {
                        robotCallAnalysisResult.getCustomerLevelTagDetailIdSet().forEach(tagDetailId -> customerTagCountMap.put(tagDetailId, 1L));
                    }
                });
                // 持久化话术业务指标统计
                if (getEngineVersion().isV1() && semanticManager != null) {
                    DialogBusiMetricsPO metrics = semanticManager.getBusiMetrics();
                    metrics.setCustomerTagCountMap(customerTagCountMap);

                    dialogBusiMetricsService.sendMetrics(callRecordId, jobId, metrics, hangupBy, taskCallResultInfo.getResultStatus());
                } else if (getEngineVersion().isV3() && Objects.nonNull(botBusiMetrics)) {
                    DialogBusiMetricsPO metrics = MyBeanUtils.copy(botBusiMetrics, DialogBusiMetricsPO.class);
                    metrics.setDialogFlowId(dialogFlowId);
                    metrics.setCustomerTagCountMap(customerTagCountMap);
                    dialogBusiMetricsService.sendMetrics(callRecordId, jobId, metrics, hangupBy, taskCallResultInfo.getResultStatus());
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn] 话术业务指标统计持久化错误", e);
            }


        }).onFailure(e -> logger.error("[LogHub_Warn]处理音频出错", e));
    }

    /**
     * 设置振铃时长
     */
    private void dealRingDuration() {
        Long ringDuration = 0L;
        if (softPhone.getPickupTime() > 0) {
            //已接听
            ringDuration = (softPhone.getPickupTime() - softPhone.getEarlyMediaTime()) / 1000;
            logger.info("AudioRecordAndIntentLevelAnalysisSoundManager setRingDuration PickupTime={}, EarlyMediaTime={}", softPhone.getPickupTime(), softPhone.getEarlyMediaTime());
        } else if (hangupTime > 0) {
            //挂断
            ringDuration = (hangupTime - softPhone.getEarlyMediaTime()) / 1000;
            logger.info("AudioRecordAndIntentLevelAnalysisSoundManager setRingDuration hangupTime={}, EarlyMediaTime={}", hangupTime, softPhone.getEarlyMediaTime());
        }
        taskCallResultInfo.setRingDuration(ringDuration > 0 ? ringDuration : 0L);
    }


    /**
     * 获取通话状态
     */
    private DialStatusEnum getFinishStatus() {

        if (BooleanUtils.isTrue(robotCallTask.getExtensionMode()) && BooleanUtils.isNotTrue(isExtensionSip200Success())) {
            //分机号外呼 且 未收到200 外呼失败
            return DialStatusEnum.SYSTEM_ERROR;
        }
        //黑名单拦截信令返回线路拦截
        if (BooleanUtils.isTrue(intercepted)){
            return DialStatusEnum.LINE_INTERCEPT;
        }

        DialStatusEnum dialStatus;
        if (isCalleePickup()) {
            if (getRobot() == null) {
                dialStatus = DialStatusEnum.CALL_LOSS;
            } else {
                dialStatus = DialStatusEnum.ANSWERED;
            }
        } else {
            if (StringUtils.equals(robotCallTask.getPhoneNumberWithPrefixInfo().getPrefix(), AXHelper.AX_FAIL_PREFIX)) {
                dialStatus = DialStatusEnum.SYSTEM_ERROR;
            } else {
                dialStatus = getPhoneFailReasonByEarlyMedia();
            }
        }
        logger.debug("TaskId={} : =============EarlyMedia分析结束, 最后通话状态为: {}.", getTaskId(), dialStatus.name());
        return dialStatus;
    }

    /**
     * 全量分析EarlyMedia，获取未接通原因
     *
     * @return 未接通原因
     */
    private DialStatusEnum getPhoneFailReasonByEarlyMedia() {
        // 分析一下earlyMedia，找下电话未接通的原因
        AtomicReference<DialStatusEnum> answerStatus = new AtomicReference<>();

        File earlyMediaPcmFile = getEarlyMediaPcmFile();

        try {
            // 如果文件不存在或者大小为0，说明没拿到earlyMedia, 属于外呼失败，但对外展示为无法接通
            if (!earlyMediaPcmFile.exists() || !earlyMediaPcmFile.isFile() || earlyMediaPcmFile.length() == 0) {
                answerStatus.set(DialStatusEnum.CAN_NOT_CONNECT);
                logger.info("TaskId={}, 未能获取earlyMedia!", getTaskId());

				if (canNotConnectRecorder.fail()) {
					canNotConnectRateExceeds();
				}
            } else {
                // TODO 用pcm 做判断 调用asr时使用mav
                long duration = earlyMediaPcmFile.length() / ApplicationConfig.MONO_FILE_LENGTH_PER_SECONDS;
                logger.info("TaskId={}: early media cost={}s", getTaskId(), duration);
                Try.run(() -> {
                    // 初始化answerStatus
                    if (duration < 10) {
                        // 早期媒体小于10秒，算拒接
                        answerStatus.set(DialStatusEnum.REFUSED);
                    } else {
                        // 未接
                        answerStatus.set(DialStatusEnum.NO_ANSWER);
                    }

                    ResponseEntity<String> response;

                    Map<String, Object> param = new HashMap<>();
                    param.put("callRecordId", getPersistentCallRecordId());
                    param.put("tenantId", tenantId);
                    param.put("url", taskCallResultInfo.getFullAudioUrl());

                    HttpHeaders httpHeaders = new HttpHeaders();
                    httpHeaders.add("Content-Type", "application/json");
                    HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.object2String(param), httpHeaders);

                    algorithmConcurrencyService.increment(AlgorithmConcurrencyTypeEnum.EARLY_MEDIA, 1L);
                    response = restTemplate.postForEntity(ApplicationConstant.AI_CALL_PLATFORM_BACK_ANALYZE_PHONE_WITH_URL_URL, httpEntity, String.class);


                    if (StringUtils.isEmpty(response.getBody())) {
                        logger.warn("no response from analyze phone");
                    } else {
                        answerStatus.set(DialStatusEnum.valueOf(response.getBody()));
                        logger.info("TaskId={}, 远程服务识别未接通原因为{}", getTaskId(), answerStatus.get());
                    }
                }).onFailure(e -> logger.error("[LogHub] TaskId={},调用服务判断通话结果失败", getTaskId(), e));
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]TaskId={}分析earlyMedia结果失败", getTaskId(), e);
        }

        return answerStatus.get();
    }

    protected File getEarlyMediaPcmFile() {
        return audioRecorder.getEarlyMediaPcmFile();
    }

    public TaskCallResultBO getTaskCallResultInfo() {
        return taskCallResultInfo;
    }

    public boolean getIsAiWait() {
        try {
            if (getEngineVersion().isV2()) {
                return nlpSemanticManager != null && nlpSemanticManager.isAiWait();
            } else if (getEngineVersion().isV3()) {
                // todo v3
                return false;
            }
            return semanticManager != null && semanticManager.getDialogManagerNew().isAiWait();
        } catch (Exception e) {
            logger.error("获取是否ai等待用户应答错误", e);
            return false;
        }
    }


    public String getCallSid() {
        return callSid;
    }

    public void setCallSid(String callSid) {
        this.callSid = callSid;
    }

    public File getTransferToCsFile(File fromFile) {
        if (fromFile == null || fromFile.length() < ApplicationConstant.EMPTY_WAV_FILE_LENGTH) {
            return fromFile;
        }
        File transferFile = null;
        boolean transfer = false;
        try {
            String key = RedisKeyCenter.getTransferCallKey(getIdentifyId());
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFSDownloadTempFilePath(getIdentifyId() + "-transfer-to-cs-record");
            transferFile = new File(fsDownloadTempFilePath);
            if (!transferFile.exists()) {
                transferFile.getParentFile().mkdirs();
                transferFile.createNewFile();
            }
            if (StringUtils.isNoneEmpty(key)) {
                String pop = (String) redisOpsService.getRedisTemplate().opsForList().leftPop(key);
                if (StringUtils.isNoneEmpty(pop)) {
                    FreeswitchInfoPO freeswitchInfo = getFreeswitchInfo();
                    while (StringUtils.isNoneEmpty(pop)) {
                        transfer = true;
                        try {
                            logger.info("pop 转接数据:{}", pop);
                            TransferCallVO vo = JSONObject.parseObject(pop, TransferCallVO.class);
                            transferToCsList.add(vo);
                            if (vo != null && CsCallCategoryEnum.AI_INT.equals(getCsCallCategoryEnum())) {
                                File file = getTransferFile(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), "transfer-to-cs-record-" + getIdentifyId() + "-" + vo.getCurrentCsStaffId() + "-" + vo.getIndex(), freeswitchInfo.getName());
                                if (transferFile.length() < ApplicationConstant.EMPTY_WAV_FILE_LENGTH) {
                                    Files.copy(file.toPath(), transferFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                                } else {
                                    if (file != null) {
                                        AudioHandleUtils.merge2Wav(transferFile.getAbsolutePath(), file.getAbsolutePath(), transferFile.getAbsolutePath());
                                        transferFile = new File(transferFile.getAbsolutePath());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.error("获取文件失败", e);
                        }
                        pop = (String) redisOpsService.getRedisTemplate().opsForList().leftPop(key);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取转接文件失败", e);
        }
        if (transfer && CsCallCategoryEnum.AI_INT.equals(getCsCallCategoryEnum())) {
            try {
                AudioHandleUtils.merge2Wav(fromFile.getAbsolutePath(), transferFile.getAbsolutePath(), transferFile.getAbsolutePath());
                transferFile = new File(transferFile.getAbsolutePath());
                return transferFile;
            } catch (Exception e) {
                logger.error("获取文件失败", e);
            }
        }
        return fromFile;
    }

    protected boolean isMonitorTransfer() {
        return monitorMsg != null;
    }

    /**
     * wav文件存在且音频内容不为空
     */
    private boolean wavFileExists(File file) {
        return file != null && file.length() > ApplicationConstant.EMPTY_WAV_FILE_LENGTH;
    }

    /**
     * 拨打分机号
     */
    private synchronized void dialExtensionNumber(byte[] buffer, int offset, int length) {
        try {
            if (isExtensionSip200Success() && !sendExtension) {
                initExtensionVadProcessor();
                if (Objects.nonNull(getVadExtension())) {
                    boolean isMute = getVadExtension().isMuteDetected(AudioHandleUtils.getShorts(buffer, offset, length));
                    logger.info("dialExtensionNumber isMute={}", isMute);
                    if (!isMute) {
                        //拨打分机号
                        logger.info("dialExtensionNumber start");
                        String[] phoneArr = getRobotCallTask().getCalledPhoneNumberWithReal().split("-");
                        if (phoneArr.length > 1) {
                            Thread.sleep(1000);
                            sendDTMFExtensionWithSleep(phoneArr[1]);
                            sendDTMFExtensionWithSleep("#");
                            setSendExtension(true);
                            //重置早期媒体的时间
                            setHasEarlyMedia(true);
                        }
                        logger.info("dialExtensionNumber end");
                    }
                }
            }
        } catch (Exception e) {
            logger.info("dialExtensionNumber error", e);
        }
    }

    protected Long getBotSmsTemplateId(Long smsTemplateId) {
        return smsTemplateId;
    }

	/**
	 * 检测到无法接通的比例过高
	 */
	protected void canNotConnectRateExceeds() {
		logger.debug("检测到无法接通的比例过高");
	}
}
