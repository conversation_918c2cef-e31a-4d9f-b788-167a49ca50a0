package com.yiwise.aicall.engine.service.impl;

import com.yiwise.aicall.engine.model.RobotCallTask;
import com.yiwise.aicall.engine.service.WechatTemplateMessageService;
import com.yiwise.base.common.utils.encrypt.AesUtils;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.wechatoauth.WechatOAuthEnum;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.feignclient.rcs.AccountClient;
import com.yiwise.core.model.bo.callstats.CallJobStatsInfoBO;
import com.yiwise.core.model.enums.WechatSendMethodEnum;
import com.yiwise.core.service.engine.CallRecordInfoService;
import com.yiwise.core.service.engine.IntentLevelTagService;
import com.yiwise.core.service.engine.callstats.CallStatsService;
import com.yiwise.core.service.engine.wechat.WechatPushCustomerService;
import com.yiwise.core.service.platform.impl.CHWeixinService;
import com.yiwise.core.service.platform.impl.WeixinService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.thread.DynamicDataSourceApplicationExecutorHolder;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.assertj.core.util.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.yiwise.core.config.WechatConstant.*;


/**
 * <AUTHOR>
 * @date 06/08/2018
 */
@Service
public class WechatTemplateMessageServiceImpl implements WechatTemplateMessageService {

    private Logger logger = LoggerFactory.getLogger(WechatTemplateMessageServiceImpl.class);

    @Resource(name = "weixinService")
    protected WeixinService weixinService;
    @Resource(name = "cHWeixinService")
    protected CHWeixinService chWeixinService;
    @Resource
    private CallStatsService callStatsService;
    @Resource
    private IntentLevelTagService intentLevelTagService;
    @Resource
    private CallRecordInfoService callRecordInfoService;
    @Resource
    private WechatPushCustomerService wechatPushCustomerService;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private AccountClient accountClient;

    // 通知模板first的颜色
    private static final String NOTICE_FIRST_COLOR = "#72B7E9";
    // 通知模板keyword的颜色
    private static final String NOTICE_KEYWORD_COLOR = "#686868";
    // 通知模板remark的颜色
    private static final String NOTICE_REMARK_COLOR = "#72B7E9";


    @Override
    public void sendIntentionCustomerFindMsg(RobotCallTask task, String intentLevelName, List<UserPO> alertWechatUserList,
                                             WechatSendMethodEnum wechatSendMethod, AtomicLong wechatUserPoint, CallRecordPO callRecordInfo,
                                             RobotCallJobPO jobInfo, boolean transferCustomer, boolean distributed, int conditionIndex) {
        Long taskId = task.getRobotCallTaskId();                //  机器人任务id
        RobotCallTaskPO taskInfo = task.getRobotTaskCallInfo(); // 机器人任务信息
        Set<Long> wechatPushUserIds = new HashSet<>();
        // 手机号码为名称默认值
        AtomicReference<String> customerName = new AtomicReference<>("[客户：" + task.getRobotTaskCallInfo().getCalledPhoneNumber() + "]");
        task.getAccountInfoOpt().ifPresent(customerPersonPO -> customerName.set(customerPersonPO.getName()));

        if (wechatSendMethod == WechatSendMethodEnum.SENDTONONE) {          // 不推送
            logger.info("TaskId={}, 不推送意向客户信息", taskId);
        } else if (wechatSendMethod == WechatSendMethodEnum.SENDTOALL) {    // 全推送
            String title = "微信推送意向客户信息(全推送)";
            //更新销售crm对应的负责人
            alertWechatUserList.parallelStream().forEach(alertUserInfo -> {
                String first = jobInfo.getName() + "找到新的" + intentLevelName + "级客户啦！";
                sendOneIntentionCustomerFindMsg(title, taskId, alertUserInfo.getWxPublicAccountOpenId(), first, taskInfo, callRecordInfo, customerName.get());
            });
            if (CollectionUtils.isNotEmpty(alertWechatUserList)) {
                accountClient.updateAccountBelongUser(callRecordInfo.getTenantId(), callRecordInfo.getCustomerPersonId(), alertWechatUserList.get(0).getUserId());
                logger.info("更新客服工作台负责人accountId={},userId={}", callRecordInfo.getCustomerPersonId(),  alertWechatUserList.get(0).getUserId());
            }
            wechatPushUserIds = alertWechatUserList.stream().map(UserPO::getUserId).collect(Collectors.toSet());
        } else if (wechatSendMethod == WechatSendMethodEnum.SENDTOONE) {    // 个别推送
            long l = wechatUserPoint.incrementAndGet();
            // TODO 实际用的是前一个值
            l = l - 1;
            logger.info("wechatUserPoint:" + l);
            int index = (int) ( l % alertWechatUserList.size());
            UserPO alertUserInfo = alertWechatUserList.get(index);
            logger.info("获取第" + index + "位推送用户[" + alertUserInfo.getName() + "]");
            String title = "微信推送意向客户信息(轮流推送)";
            String first = jobInfo.getName() + "找到新的" + intentLevelName + "级客户啦！";
            sendOneIntentionCustomerFindMsg(title, taskId, alertUserInfo.getWxPublicAccountOpenId(), first, taskInfo, callRecordInfo, customerName.get());
            accountClient.updateAccountBelongUser(callRecordInfo.getTenantId(), callRecordInfo.getCustomerPersonId(), alertUserInfo.getUserId());
            logger.info("更新客服工作台负责人accountId={},userId={}", callRecordInfo.getCustomerPersonId(), alertUserInfo.getUserId());
            wechatPushUserIds.add(alertUserInfo.getUserId());
        }else if(wechatSendMethod == WechatSendMethodEnum.SENDTOTRANSFER){  // 优先介入人推送
            //有介入人全部推送，无介入人依次推送
            if(alertWechatUserList.size() <= 1){
                String title = "微信推送意向客户信息(优先介入人推送)";
                alertWechatUserList.parallelStream().forEach(alertUserInfo -> {
                    String first = jobInfo.getName() + "找到新的" + intentLevelName + "级客户啦！";
                    sendOneIntentionCustomerFindMsg(title, taskId, alertUserInfo.getWxPublicAccountOpenId(), first, taskInfo, callRecordInfo, customerName.get());
                });
                if (CollectionUtils.isNotEmpty(alertWechatUserList)) {
                    accountClient.updateAccountBelongUser(callRecordInfo.getTenantId(), callRecordInfo.getCustomerPersonId(), alertWechatUserList.get(0).getUserId());
                    logger.info("更新客服工作台负责人accountId={},userId={}", callRecordInfo.getCustomerPersonId(),  alertWechatUserList.get(0).getUserId());
                }
              wechatPushUserIds = alertWechatUserList.stream().map(UserPO::getUserId).collect(Collectors.toSet());
            }else {
                long l;
                // TODO 启动停止任务时
                // 分布式job使用redis获取计数
                if (distributed) {
                    String jobWechatPushKey = RedisKeyCenter.getJobWechatPushKey(jobInfo.getRobotCallJobId(), conditionIndex);
                    l = redisOpsService.incrementKey(jobWechatPushKey);
                    logger.info("wechatUserPoint from redis:" + l);
                } else {
                    l = wechatUserPoint.incrementAndGet();
                    logger.info("wechatUserPoint:" + l);
                }
                int index = (int) (l % alertWechatUserList.size());
                UserPO alertUserInfo = alertWechatUserList.get(index);
                logger.info("获取第" + index + "位推送用户[" + alertUserInfo.getName() + "]");
                String title = "微信推送意向客户信息(优先介入人推送)";
                String first = jobInfo.getName() + "找到新的" + intentLevelName + "级客户啦！";
                sendOneIntentionCustomerFindMsg(title, taskId, alertUserInfo.getWxPublicAccountOpenId(), first, taskInfo, callRecordInfo, customerName.get());

                accountClient.updateAccountBelongUser(callRecordInfo.getTenantId(), callRecordInfo.getCustomerPersonId(), alertUserInfo.getUserId());
                logger.info("更新客服工作台负责人accountId={},userId={}", callRecordInfo.getCustomerPersonId(),  alertWechatUserList.get(0).getUserId());
                wechatPushUserIds.add(alertUserInfo.getUserId());
            }
        }
        try {
            wechatPushCustomerService.addWechatPushCustomer(callRecordInfo.getTenantId(), wechatPushUserIds, callRecordInfo.getCallRecordId(), callRecordInfo.getCustomerPersonId(), callRecordInfo.getIntentLevelTagId());
        } catch (Exception e) {
            logger.error("添加意向推送客户副本失败");
        }
        LocalDateTime now = LocalDateTime.now();
        callRecordInfoService.updateWechatInfoUsers(callRecordInfo.getTenantId(), callRecordInfo.getCallRecordId(), wechatPushUserIds, now);
        callRecordInfo.setWechatPushUsers(wechatPushUserIds);
        callRecordInfo.setWechatPushTime(now);
    }

    @Override
    public void sendJobFinishedMsg(RobotCallJobPO robotCallJob, List<UserPO> alertWechatUserSet, String dialogFlowName) {

        if (CollectionUtils.isEmpty(alertWechatUserSet)) {
            logger.info("推送列表为空");
            return;
        }

        CallJobStatsInfoBO callJobStatsInfoBO = callStatsService.getCallJobStatsInfo(robotCallJob.getTenantId(), robotCallJob.getRobotCallJobId());
        String jobName = robotCallJob.getName();
        LocalDateTime createTime = robotCallJob.getCreateTime();

        long callTotal = callJobStatsInfoBO.getTaskCallTotal() == null ? 0 : callJobStatsInfoBO.getTaskCallTotal();
        long callAnswered = callJobStatsInfoBO.getAnsweredTask() == null ? 0 : callJobStatsInfoBO.getAnsweredTask();
        long taskTotalCompleted = callJobStatsInfoBO.getTaskTotalCompleted() == null  ? 0 : callJobStatsInfoBO.getTaskTotalCompleted();
        double chatDurationAverage = callAnswered == 0 ? 0 : 1.0 * callJobStatsInfoBO.getChatDurationTotal() / callAnswered;
        Map<Integer, Long> intentLevel = callJobStatsInfoBO.getIntentLevel();
        List<IntentLevelTagDetailPO> intentLevelDetailList = intentLevelTagService.getIntentLevelTagByCllJobId(robotCallJob.getRobotCallJobId(), false).getDetails();
        double aLevel = 0.0;
        double bLevel = 0.0;

        if (0L != callAnswered) {
            if (intentLevelDetailList.size() > 0) {
                IntentLevelTagDetailPO topADetail = intentLevelDetailList.get(0);
                aLevel = intentLevel.get(topADetail.getCode()) == null ? 0 : 100.0 * intentLevel.get(topADetail.getCode()) / callAnswered;
            }
            if (intentLevelDetailList.size() > 1) {
                IntentLevelTagDetailPO topBDetail = intentLevelDetailList.get(1);
                bLevel = intentLevel.get(topBDetail.getCode()) == null ? 0 : 100.0 * intentLevel.get(topBDetail.getCode()) / callAnswered;
            }
        }

        double answerRate = taskTotalCompleted == 0 ? 0 : callAnswered * 100.0 / taskTotalCompleted;
        String detailInfo = String.format("外呼总量%d通,接通率%.2f%%,平均通话时长%.1f秒，重点关注客户占比分别为%.2f%%/%.2f%%!", callTotal, answerRate, chatDurationAverage, aLevel, bLevel);

        try {
            String templateId;
            if (CommonApplicationConstant.CHANG_HE_TENANT_ID.equals(robotCallJob.getTenantId())) {
                templateId = TPN_CALL_JOB_FINISH_MSG_CH;
            } else {
                templateId = TPN_CALL_JOB_FINISH_MSG;
            }
            WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                    .templateId(templateId)
                    .build();

            templateMessage.addData(new WxMpTemplateData("first", "您的任务已外呼完成", NOTICE_FIRST_COLOR));
            templateMessage.addData(new WxMpTemplateData("keyword1", jobName, NOTICE_KEYWORD_COLOR));
            templateMessage.addData(new WxMpTemplateData("keyword2", dialogFlowName, NOTICE_KEYWORD_COLOR));
            templateMessage.addData(new WxMpTemplateData("keyword3", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(createTime), NOTICE_KEYWORD_COLOR));
            templateMessage.addData(new WxMpTemplateData("remark", detailInfo, NOTICE_KEYWORD_COLOR));

            for (UserPO item : alertWechatUserSet) {
                if (StringUtils.isEmpty(item.getWxPublicAccountOpenId())) {
                    logger.info("用户{}未绑定微信，id={}, phoneNumber={}", item.getName(), item.getUserId(), item.getPhoneNumber());
                    continue;
                }
                templateMessage.setToUser(item.getWxPublicAccountOpenId());
                try {
                    weixinService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                    logger.info("RobotCallJobId={}, name ={}, OpenID={}已经推送", robotCallJob.getRobotCallJobId(), jobName, item.getWxPublicAccountOpenId());
                } catch (WxErrorException e) {
                    logger.error("RobotCallJobId={}, name ={}, userId={}, wechatOpenId={} ,发送任务完成微信消息失败", robotCallJob.getRobotCallJobId(), jobName, item.getUserId(), item.getWxPublicAccountOpenId(), e);
                }
            }

        } catch (Exception e) {
            logger.error("RobotCallJobId={}, name ={} ,发送任务完成微信消息失败", robotCallJob.getRobotCallJobId(), jobName, e);
        }

    }

    @Override
    public void sendOneIntentionCustomerFindMsg(String title, Long taskId, String openId, String first, RobotCallTaskPO taskInfo, CallRecordPO callRecordInfo, String customerName) {
        sendOneIntentionCustomerFindMsg(title, taskId, openId, first, taskInfo, callRecordInfo, customerName, false);
    }

    @Override
    public void sendOneIntentionCustomerFindMsg(String title, Long taskId, String openId, String first, RobotCallTaskPO taskInfo, CallRecordPO callRecordInfo, String customerName, boolean isFreeUser) {
        DynamicDataSourceApplicationExecutorHolder.execute(title + ", TaskId=" + taskId, () -> {
            try {
                String templateId;
                if (CommonApplicationConstant.CHANG_HE_TENANT_ID.equals(taskInfo.getTenantId())) {
                    templateId = TPL_INTENTION_CUSTOMER_FIND_MSG_CH;
                } else {
                    templateId = TPL_INTENTION_CUSTOMER_FIND_MSG;
                }
                WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                        .toUser(openId)
                        .templateId(templateId)
                        .build();

                //归属地信息
//                PhoneHomeLocationBO location = null;
//                String phoneNumber = taskInfo.getCalledPhoneNumber();
//                if (StringUtils.isNotEmpty(phoneNumber) && phoneNumber.length() >= 7){
//                    location = phoneLocationService.getLocationOrNewByPhoneNumber(phoneNumber.substring(0,7));
//                }
//                String customerInfo;
//                if (Objects.nonNull(location)){
//                    if (StringUtils.equals(location.getProv(),location.getCity())){
//                        customerInfo = taskInfo.getCalledPhoneNumber() + "(" + location.getCity() + ")";
//                    }else {
//                        customerInfo = taskInfo.getCalledPhoneNumber() + "(" + location.getProv() + location.getCity() + ")";
//                    }
//                }else {
//                    customerInfo = taskInfo.getCalledPhoneNumber();
//                }
                String  jobName,intentLevelName;
                try {
                    int index1 = first.indexOf("找到新的") + 4;
                    int index2 = first.indexOf("级客户啦");
                    jobName = first.substring(0, index1-4);
                    intentLevelName = first.substring(index1, index2);
                }catch (Exception e) {
                    jobName = "";
                    intentLevelName = "";
                }
                templateMessage.addData(new WxMpTemplateData("thing5", StringUtils.isNotEmpty(jobName) ? substring20(jobName) : "未知", NOTICE_KEYWORD_COLOR));
                templateMessage.addData(new WxMpTemplateData("thing2", StringUtils.isNotEmpty(customerName) ? substring20(customerName) : "未知", NOTICE_KEYWORD_COLOR));
                templateMessage.addData(new WxMpTemplateData("thing3", StringUtils.isNotEmpty(intentLevelName) ? substring20(intentLevelName) : "未知", NOTICE_KEYWORD_COLOR));
                templateMessage.addData(new WxMpTemplateData("phone_number6", taskInfo.getCalledPhoneNumber(), NOTICE_KEYWORD_COLOR));
                templateMessage.addData(new WxMpTemplateData("time8", DateFormatUtils.format(new Date(), "MM-dd HH:mm"), NOTICE_KEYWORD_COLOR));
                // SCRM推送微信h5页面加密逻辑相同,后续如果修改加密方式的话要同步修改
                String encryptedText = AesUtils.encrypt(callRecordInfo.getCallRecordId());
                Map<String, Object> map = Maps.newHashMap("callRecordId", encryptedText);
                map.put("enableSetIntent", !isFreeUser);
                String backEndUrl;
                if (CommonApplicationConstant.CHANG_HE_TENANT_ID.equals(taskInfo.getTenantId())) {
                    backEndUrl = WechatOAuthEnum.GET_CALL_RECORD_INFO_CH.getWechatOAuthConfig().getParamBackEndUrl(map);
                } else {
                    backEndUrl = WechatOAuthEnum.GET_CALL_RECORD_INFO.getWechatOAuthConfig().getParamBackEndUrl(map);
                }
                templateMessage.setUrl(backEndUrl);

                if (CommonApplicationConstant.CHANG_HE_TENANT_ID.equals(taskInfo.getTenantId())) {
                    chWeixinService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                } else {
                    weixinService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                }
                logger.info("TaskId={}, " + title + ", OpenID={}", taskId, openId);
            } catch (Exception e) {
                logger.error("微信推送意向客户信息失败, TaskId={}, " + title + ", OpenID={}", taskId, openId, e);
            }
        });
    }

    /**
     * 微信限制 20字符
     * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html
     */
    private String substring20(String thing){
        if (thing != null && thing.length() > 20) {
            thing = thing.substring(0,19) + "…";
        }
        return thing;
    }

}
