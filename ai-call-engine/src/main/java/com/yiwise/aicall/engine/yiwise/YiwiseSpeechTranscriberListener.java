package com.yiwise.aicall.engine.yiwise;

import com.yiwise.aicall.engine.engine.brain.SemanticManager;
import com.yiwise.aicall.engine.engine.brain.NlpSemanticManager;
import com.yiwise.aicall.engine.engine.listener.AsrMonitorEventListener;
import com.yiwise.asr.AsrRecognizerListener;
import com.yiwise.asr.common.client.protocol.AsrRecognizerResult;
import com.yiwise.aicall.engine.aliyun.AliyunAsrSoundManager;
import javaslang.control.Try;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class YiwiseSpeechTranscriberListener implements AsrRecognizerListener {
    private static final Logger logger = LoggerFactory.getLogger(YiwiseSpeechTranscriberListener.class);

    private AliyunAsrSoundManager aliyunAsrSoundManager;
    private String dialogFlowName;

    private YiwiseRobot robot;

    public YiwiseSpeechTranscriberListener(YiwiseRobot robot, AliyunAsrSoundManager aliyunAsrSoundManager, String dialogFlowName) {
        this.robot = robot;
        this.dialogFlowName = dialogFlowName;
        this.aliyunAsrSoundManager = aliyunAsrSoundManager;
    }

    @Override
    public void onSentenceBegin(AsrRecognizerResult result) {
        // asr开始
        aliyunAsrSoundManager.setAsrEnd(false);
        aliyunAsrSoundManager.setAsrProceedingTime(System.currentTimeMillis());
        for (AsrMonitorEventListener asrMonitorEventListener : robot.getAsrMonitorEventListenerList()) {
            asrMonitorEventListener.onSentenceBegin(result.getBeginTime());
        }
        try {
            aliyunAsrSoundManager.getAsrResultEventListener().onSentenceBegin();
        } catch (Exception e) {
            logger.error("[LogHub_Warn] 处理onSentenceBegin异常 ", e);
        }
    }

    @Override
    public void onSentenceBeginChanged(AsrRecognizerResult result) {
        try {
            if (StringUtils.isEmpty(result.getResultText())) {
                return;
            }
            aliyunAsrSoundManager.setAsrProceedingTime(System.currentTimeMillis());
            aliyunAsrSoundManager.setLastUserSayEndTime(System.currentTimeMillis());

            logger.debug("TaskId={}, TransSentenceIndex={}, asrTaskId={}, 中间结果：{}", aliyunAsrSoundManager.getTaskId(), result.getSentenceIndex(), result.getTaskId(), result.getResultText());
            Try.run(() -> {
                Double filePlayProgress = aliyunAsrSoundManager.getFilePlayProgressRecorder().getFilePlayProgress(aliyunAsrSoundManager.getCurrPlayFileName());
                Integer filePlayDuration = aliyunAsrSoundManager.getFilePlayProgressRecorder().getFilePlayDurationProgress(aliyunAsrSoundManager.getCurrPlayFileName());
                int beginTime = result.getBeginTime().intValue() + aliyunAsrSoundManager.getAsrWaitingSoundOffsetTime();
                int endTime = result.getTime().intValue() + aliyunAsrSoundManager.getAsrWaitingSoundOffsetTime();
                aliyunAsrSoundManager.getAsrResultEventListener().processUserSay(result.getResultText(), filePlayProgress, filePlayDuration, false, beginTime, endTime);
            }).onFailure((e) -> logger.error("[LogHub_Warn]调用Yiwise-ASR中间结果处理", e));
        } catch (Exception ex) {
            logger.error("TaskId=" + aliyunAsrSoundManager.getTaskId() + ": " + ex.getMessage(), ex);
        }
    }

    @Override
    public void onSentenceEnd(AsrRecognizerResult result) {
        //asr 识别结束标识, 不管返回的内容如何，已经标志着Asr结束
        aliyunAsrSoundManager.setAsrEnd(true);
        aliyunAsrSoundManager.setAsrProceedingTime(System.currentTimeMillis());

        try {
            // 判断是否有文本和语义
            for (AsrMonitorEventListener asrMonitorEventListener : robot.getAsrMonitorEventListenerList()) {
                asrMonitorEventListener.onSentenceEnd(result.getBeginTime(), result.getTime(), result.getResultText());
            }
            if (StringUtils.isEmpty(result.getResultText()) && !(aliyunAsrSoundManager.getEngineVersion().isV3())) {
                return;
            }

            aliyunAsrSoundManager.setLastUserSayEndTime(System.currentTimeMillis());

            // 最终结果
            logger.debug("TaskId={}, TransSentenceIndex={}, asrTaskId={}, 最终结果：{}", aliyunAsrSoundManager.getTaskId(), result.getSentenceIndex(), result.getTaskId(), result.getResultText());
            Double filePlayProgress = aliyunAsrSoundManager.getFilePlayProgressRecorder().getFilePlayProgress(aliyunAsrSoundManager.getCurrPlayFileName());
            Integer filePlayDuration = aliyunAsrSoundManager.getFilePlayProgressRecorder().getFilePlayDurationProgress(aliyunAsrSoundManager.getCurrPlayFileName());
            int beginTime = result.getBeginTime().intValue() + aliyunAsrSoundManager.getAsrWaitingSoundOffsetTime();
            int endTime = result.getTime().intValue() + aliyunAsrSoundManager.getAsrWaitingSoundOffsetTime();
            aliyunAsrSoundManager.getAsrResultEventListener().processUserSay(result.getResultText(), filePlayProgress, filePlayDuration, true, beginTime, endTime);
        } catch (Exception ex) {
            logger.error("[LogHub_Warn]调用Yiwise-ASR最终结果处理，TaskId=" + aliyunAsrSoundManager.getTaskId() + ": " + ex.getMessage(), ex);
        }
    }
}
