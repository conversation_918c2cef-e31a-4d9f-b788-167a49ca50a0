package com.yiwise.aicall.engine.engine;

import com.yiwise.aicall.engine.aliyun.LogDecorateDialogManager;
import com.yiwise.aicall.engine.aliyun.NluCallListener;
import com.yiwise.aicall.engine.engine.brain.SemanticManager;
import com.yiwise.aicall.engine.engine.brain.entities.DialogFlow;
import com.yiwise.aicall.engine.engine.dialog.DialogFlowHandler;
import com.yiwise.aicall.engine.model.*;
import com.yiwise.aicall.engine.model.websocket.*;
import com.yiwise.aicall.engine.service.IntentAnalysisService;
import com.yiwise.aicall.engine.service.WebSocketSendMsgService;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.text.TextPlaceholderSplitter;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.string.MyRandomStringUtils;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.helper.*;
import com.yiwise.core.model.analysis.RobotCallAnalysisResult;
import com.yiwise.core.model.bo.callrecord.CallRecordWebSocketBO;
import com.yiwise.core.model.bo.websocket.*;
import com.yiwise.core.model.dialogflow.entity.KeyWordDetectionPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.callin.CallTypeEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.service.dialogflow.KeyWordDetectionService;
import com.yiwise.core.service.engine.CallDetailInfoService;
import com.yiwise.core.service.engine.CallRecordInfoService;
import com.yiwise.core.service.platform.UserService;
import javaslang.control.Try;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jboss.logging.MDC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.ANONYMOUS_JOB_ID;
import static com.yiwise.core.config.ApplicationConstant.VERBAL_TRICK_TRAINING_CUSTOMER_PERSON_ID;

/**
*
* <AUTHOR> yangdehong
* @date : 2019/5/7 19:14
*/
public class TextDialogManager implements NluCallListener {

    private static final Logger logger = LoggerFactory.getLogger(TextDialogManager.class);

    private static final WebSocketSendMsgService webSocketSendMsgService = AppContextUtils.getBean(WebSocketSendMsgService.class);
    private static final CallRecordInfoService callRecordInfoService = AppContextUtils.getBean(CallRecordInfoService.class);
    private static final CallDetailInfoService callDetailInfoService = AppContextUtils.getBean(CallDetailInfoService.class);
    private static final UserService userService = AppContextUtils.getBean(UserService.class);
    private static final IntentAnalysisService intentAnalysisService = AppContextUtils.getBean(IntentAnalysisService.class);
    private static final KeyWordDetectionService keyWordDetectionService = AppContextUtils.getBean(KeyWordDetectionService.class);
    private static final AliMessageQueueHelper aliMessageQueueHelper = AppContextUtils.getBean(AliMessageQueueHelper.class);
    private static final Pattern BUTTON_PATTERN = Pattern.compile("\\{([0-9*# ])}");

    @Setter
    private SemanticManager semanticManager;
    private DialogFlow dialogFlow;

    private UserIdPrincipal principal;
    private SystemEnum clientType;

    CallRecordPO callRecordInfo;

    private LocalDateTime startTime = LocalDateTime.now();
    /**
     * 自定义变量
     */
    @Getter
    private Map<String, String> properties;
    private Map<String, String> originProperties;
    /**
     * 是否挂机
     */
    private boolean isHangup = false;
    /**
     * 是否已经执行了AI说话完成
     */
    private boolean isAiSayFinish = false;
    /**
     * 用户过滤之后说的话，在第二次进入oneRound的时候，记录的是上一轮的
     */
    private String text;
    /**
     * 流程id
     */
    private String stepId;
    /**
     * 节点index
     */
    private Long nodeId;

    @Setter
    private String logId;

    private List<CallDetailBO> callDetailList;

    private long startTimestamp;

    private LocalDateTime endTime;

    public TextDialogManager(UserIdPrincipal principal, SystemEnum clientType, DialogFlow dialogFlow, TextDialogVO textDialogVO) {
        this.startTimestamp = System.currentTimeMillis();
        this.principal = principal;
        this.clientType = clientType;
        this.dialogFlow = dialogFlow;
        this.stepId = textDialogVO.getStepId();
        this.nodeId = textDialogVO.getNodeId();
        this.callDetailList = new ArrayList<>();
        this.logId = String.format("%s_Did_%s_Uid_%s", MyRandomStringUtils.getRandomStringByLength(4), principal.getUserId(), dialogFlow.getDialogFlowInfoPO().getId());
        UserPO userInfo = userService.selectByKey(principal.getUserId());
        this.properties = RunTimePropertiesHandler.getProperties(userInfo, textDialogVO.getProperties());
        this.originProperties = textDialogVO.getProperties();
        if (StringUtils.isNotEmpty(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_GENDER))) {
            GenderEnum fromDesc = GenderEnum.UNKNOWN;
            try {
                fromDesc = GenderEnum.valueOf(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_GENDER));
            } catch (Exception e) {
                logger.info("未传入正确性别", e);
            }
            // 设置传入的性别信息
            this.properties.put(DialogVariablesPlaceholder.CUSTOMER_GENDER, GenderEnum.getDialogText(fromDesc));
        }
        // 姓名转姓名总
        if (StringUtils.isNotEmpty(this.properties.get(DialogVariablesPlaceholder.CUSTOMER_NAME))) {
            String customerName = properties.get(DialogVariablesPlaceholder.CUSTOMER_NAME);
            String bossName = Try.of(() -> customerName.substring(0, 1) + "总").getOrElseGet((e) -> "");
            if (StringUtils.isEmpty(bossName)) {
                properties.put(DialogVariablesPlaceholder.CUSTOMER_NAME_FAMILY_NAME_BOSS, DialogFlowHandler.FAMILY_NAME_NOT_FOUND_REPLACER);
            } else {
                properties.put(DialogVariablesPlaceholder.CUSTOMER_NAME_FAMILY_NAME_BOSS, bossName);
            }
        }
        calleePickup();
        initCallRecordInfo(principal, userInfo, dialogFlow.getDialogFlowInfoPO().getId());
    }

    public void enter() {
        MDC.put("MDC_LOG_ID", this.logId);
        semanticManager.enter();
    }

    public void oneRound(TextDialogVO textDialogVO) {
        MDC.put("MDC_LOG_ID", this.logId);
        doOneRound(textDialogVO);
    }

    public void doOneRound(TextDialogVO textDialogVO) {
        String userSayText = textDialogVO.getUserSayText();
        Double aiProgress = textDialogVO.getAiProgress()==null?0.0:textDialogVO.getAiProgress();
        boolean userSayFinish = textDialogVO.getUserSayFinish();
        if (userSayText.equalsIgnoreCase("reset")) {
            // 如果是单个节点测试，每一句话都要重置
            if (BooleanUtils.isTrue(textDialogVO.getTestOneNode())) {
                logger.debug("重置2");
                reset(stepId, nodeId);
            } else {
                logger.debug("重置");
                reset();
            }
            return;
        }

        if (userSayText.equalsIgnoreCase("aiSayFinish")) {
            logger.debug("只执行AI说话完成");
            aiSayFinish();
            return;
        }

        // 挂机的话，直接返回
        if (isHangup) {
            logger.debug("挂机");
            return;
        }

        Matcher matcher = BUTTON_PATTERN.matcher(userSayText);
        if (matcher.find()) {
            execMethod(matcher.group(1).charAt(0));
        } else {
            execMethod(userSayText, aiProgress, userSayFinish);
        }
        // 单个节点测试
        TestOneNodeVO testOneNodeVO = this.semanticManager.getTestOneNodeVO();
        if (Objects.nonNull(testOneNodeVO)) {
            ConcurrentMap<String, String> dynamicVariableMap = this.semanticManager.getDialogManagerNew().getAssignmentManager().getDynamicVariableMap();
            ConcurrentMap<String, String> collectVariableMap = this.semanticManager.getDialogManagerNew().getAssignmentManager().getCollectVariableMap();
            testOneNodeVO.setDynamicVariableMap(dynamicVariableMap);
            testOneNodeVO.setCollectVariableMap(collectVariableMap);
            CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(JsonUtils.object2String(testOneNodeVO), null);
            webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
            // 如果是单个节点测试，每一句话都要重置
            if (BooleanUtils.isTrue(textDialogVO.getTestOneNode())) {
                logger.debug("重置2");
                reset(stepId, nodeId);
            }
        }
    }

    public void afterEnter() {
        // 单个节点测试
        TestOneNodeVO testOneNodeVO = semanticManager.getTestOneNodeVO();
        if (Objects.nonNull(testOneNodeVO) && !testOneNodeVO.getSuccess()) {
            CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(testOneNodeVO.getMessage(), null);
            webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
        }
    }

    private void execMethod(String userSayText, Double aiProgress, boolean userSayFinish) {

        // 执行ai说完
        if (aiProgress==0.0 && !isAiSayFinish) {
            aiSayFinish();
        }

        // 挂机的话，直接返回
        if (isHangup) {
            return;
        }

        isAiSayFinish = false;

        // 执行用户操作
        if (StringUtils.isBlank(userSayText)) {
            logger.debug("执行userSilence方法");
            Method method = getMethod("userSilence");
            try {
                Object[] objects = new Object[0];
                method.invoke(semanticManager, objects);
            } catch (Exception e) {
                logger.error("DialogManager执行方法userSilence出错", e);
            }
        } else {
            logger.debug("执行processUserSay方法");
            Method method = getMethod("processUserSay");
            try {
                Object[] objects = new Object[6];
                objects[0]=userSayText;
                objects[1]=aiProgress;
                objects[2]=0;
                objects[3]=userSayFinish;
                // TODO 下面这个时间先写死
                objects[4]=1000;
                objects[5]=100;
                Object invoke = method.invoke(semanticManager, objects);
                text = invoke==null?null:invoke.toString();
            } catch (InvocationTargetException exception) {
                Throwable cause = exception.getCause();
                logger.error("processUserSay内部错误", cause);
            } catch (Exception e) {
                logger.error("DialogManager执行方法processUserSay出错", e);
            }
        }

    }

    // 在通话结束后分析意向等级
    public void analysisIntentLevel(boolean isCustomerHangup) {
        endTime = LocalDateTime.now();
        DialStatusEnum dialStatus = DialStatusEnum.ANSWERED;
        long now = System.currentTimeMillis();
        long duration = (now - startTimestamp) / 1000;

        List<AnalyzeDetail> analyzeDetailList = callDetailList.stream()
                .map(item -> (AnalyzeDetail) item)
                .collect(Collectors.toList());

        RobotCallAnalysisResult analysisResult = intentAnalysisService.analysis(dialStatus, analyzeDetailList, dialogFlow, 0L, duration, isCustomerHangup, null, false, false, Collections.emptyList(), false, Collections.emptySet(), Collections.emptyMap());
        updateCallRecordInfo(analysisResult, duration);
    }

    private void sendCallRecordMsg(CallRecordPO callRecordInfo) {
        CallRecordWebSocketBO callRecordWebSocketBO = MyBeanUtils.copy(callRecordInfo, CallRecordWebSocketBO.class);
        CallRecordInfoMsg callRecordMsg = new CallRecordInfoMsg(callRecordWebSocketBO);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callRecordMsg, clientType);
    }

    protected void updateCallRecordInfo(RobotCallAnalysisResult analysisResult, long duration) {
        if (analysisResult == null) {
            return;
        }
        callRecordInfo.setWechatPushUsers(Collections.emptySet());
        callRecordInfo.setEndTime(getEndTime());
        callRecordInfo.setStartTime(getStartTime());
        callRecordInfo.setChatRound(analysisResult.getAiRounds());

        logger.info("获取探意分析的意向 intent={}", analysisResult.getIntentLevel());
        callRecordInfo.setIntentLevel(analysisResult.getIntentLevel());
        callRecordInfo.setRealIntentLevel(analysisResult.getIntentLevel());
        String analysisBasis = analysisResult.getBasis();
        if (StringUtils.length(analysisBasis) > 1000) {
            logger.info("analysisBasis超过1000字 截取前1000字");
            analysisBasis = analysisBasis.substring(0, 1000);
        }
        callRecordInfo.setAnalysisBasis(analysisBasis);
        callRecordInfo.setChatDuration(duration);

        callRecordInfo.setIntentLevelTagId(dialogFlow.getIntentLevelTagId());
        // 通话过程中可能人工标记意向,通话结束后不再设值避免覆盖真实值
        // 本次使用的变量
        callRecordInfo.setProperties(originProperties);
        // 客户属性
        callRecordInfo.setAttributes(analysisResult.getCustomerAttribute());
        callRecordInfo.setCallType(CallTypeEnum.LOCAL);
        callRecordInfo.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
        if (Objects.nonNull(semanticManager)) {
            callRecordInfo.setDynamicVariables(semanticManager.getCollectedDynamicVariable());
        } else {
            callRecordInfo.setDynamicVariables(Collections.emptyMap());
        }
        callRecordInfo.setHangupBy(CallJobHangupEnum.INITIAL_HANGUP);
        callRecordInfoService.updateNotNull(callRecordInfo);
        sendCallRecordMsg(callRecordInfo);
    }

    private LocalDateTime getEndTime() {
        return endTime == null ? LocalDateTime.now() : endTime;
    }

    /**
     * 按键
     */
    private void execMethod(char c) {
        if (c == ' ') {
            semanticManager.userDtmfSilence();
        } else {
            semanticManager.whenUserClick(c);
        }
    }

    /**
     * 执行aiSayFinish
     */
    private void aiSayFinish() {
        logger.debug("执行aiSayFinish方法");
        Method method = getMethod("aiSayFinish");
        Object[] objects = new Object[0];
        try {
            method.invoke(semanticManager, objects);
            isAiSayFinish = true;
        } catch (IllegalAccessException e) {
            logger.error("DialogManager执行方法aiSayFinish出错", e);
        } catch (InvocationTargetException e) {
            logger.error("DialogManager执行方法aiSayFinish出错", e);
        }
    }

    private Method getMethod(String methodName) {
        Method[] allMethods = semanticManager.getClass().getDeclaredMethods();
        for (Method m : allMethods) {
            if (m.getName().equals(methodName)) {
                return m;
            }
        }
        return null;
    }

    /**
     * 重置
     */
    private void reset() {
        isHangup = false;
        isAiSayFinish = false;
        this.setSemanticManager(new LogDecorateDialogManager(this, dialogFlow, semanticManager.getRuntimeProperties(),
                semanticManager.getRuntimeTextAudioContentMap()));
        logger.debug("重置");
        this.semanticManager.enter();
    }
    private void reset(String stepId, Long nodeId) {
        isHangup = false;
        isAiSayFinish = false;
        this.setSemanticManager(new LogDecorateDialogManager(this, dialogFlow, semanticManager.getRuntimeProperties(),
                semanticManager.getRuntimeTextAudioContentMap()));
        logger.debug("重置的stepId={},nodeId={}", stepId, nodeId);
        this.semanticManager.enter(stepId, nodeId);
    }


    /**
     * 发送错误信息
     */
    public void whenError(String message) {
        isHangup = true;
        logger.error("[LogHub_Warn]文本测试出现错误={}", message);
        CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(message, null);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
    }

    @Override
    public void pauseAudio() {
        logger.info("【NewAction】pauseAudio");
    }

    @Override
    public void playNewAudio(String audioUrl) {
        logger.info("【NewAction】playNewAudio, audioUrl=" + audioUrl);
    }

    @Override
    public void playAudioWithTts(String text, String originAnswerTemplate, List<String> textOssUrl, Map<String, String> customVariablesProperties, Map<String, Object> ttsParam) {
        logger.info("【NewAction】playAudioWithTts");
    }

    @Override
    public void waitForUserSay(Integer timeoutMillisecond) {
        logger.info("【NewAction】waitForUserSay, timeoutMillisecond=" + timeoutMillisecond);
    }

    @Override
    public void resume() {
        logger.info("【NewAction】resume");
    }

    @Override
    public void hangup() {
        isHangup = true;
        logger.info("【NewAction】hangup，挂机了，需要重新执行reset");
        CallDetailMsg callDetailMsg = CallDetailMsg.toRobot("上一个对话已经挂机了，需要重置，输入reset", null);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);
        updateCallRecordOnFinish(false);
    }

    public void updateCallRecordOnFinish(boolean isCustomerHangup) {
        try {
            MDC.put("MDC_LOG_ID", this.logId);
            logger.info("文本训练结束, 执行意向分析");
            analysisIntentLevel(isCustomerHangup);
            persistenceKeywordDetectionList();
        } catch (Exception e) {
            logger.info("文本训练意向分析异常", e);
        }
    }

    private void persistenceKeywordDetectionList()  {
        try {
            List<KeyWordDetectionPO> keyWordDetectionPOList = null;
            if (semanticManager != null) {
                keyWordDetectionPOList = semanticManager.getDialogManager().getKeyWordDetectionPOList();
            }
            if (CollectionUtils.isNotEmpty(keyWordDetectionPOList)) {
                keyWordDetectionPOList.forEach(keyWordDetectionPO -> {
                    keyWordDetectionPO.setCallRecordId(callRecordInfo.getCallRecordId());
                });
                keyWordDetectionService.insertKeyWordDetectionList(keyWordDetectionPOList);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn]对话详细匹配过程错误", e);
        }
    }

    @Override
    public void onUserSay(UserSayBO userSayBO) {
        logger.debug("【onUserSay】{}", userSayBO.getText());
        CallDetailMsg callDetailMsg = CallDetailMsg.toPerson(userSayBO);
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);

        // 推送关键词检测详情
        KeyWordDetectionPO userDetectionPO = semanticManager.getDialogManager().getUserDetectionPO();
        KeyWordDetectionMsg keyWordDetectionMsg = new KeyWordDetectionMsg(userDetectionPO);
        webSocketSendMsgService.sendKeyWordDetectionMsgToUser(principal, keyWordDetectionMsg, clientType);
        KeyWordDetectionPO keyWordDetectionPO = semanticManager.getKeyWordDetectionPO();
        KeyWordDetectionMsg keyWordDetectionMsg2 = new KeyWordDetectionMsg(keyWordDetectionPO);
        webSocketSendMsgService.sendKeyWordDetectionMsgToUser(principal, keyWordDetectionMsg2, clientType);

        // 保存通话记录
        CallDetailBO callDetail = new CallDetailBO(userSayBO);
        callDetailList.add(callDetail);
        persistenceCallDetail(callDetail);
    }

    @Override
    public void onAiSay(AiSayBO aiSayBO) {
        MDC.put("MDC_LOG_ID", this.logId);
        String text = aiSayBO.getText();
        TextPlaceholderSplitter splitter = new TextPlaceholderSplitter(text);
        String fullSentence;
        try {
            ConcurrentMap<String, String> variableTtsMap = this.semanticManager.getDialogManagerNew().getAssignmentManager().getVariableTtsMap();
            Map<String, String> renderProperties = new HashMap<>();
            variableTtsMap.forEach((k, v) -> {
                TextPlaceholderSplitter subSplitter = new TextPlaceholderSplitter(v);
                if (CollectionUtils.isNotEmpty(subSplitter.getPlaceholderSet())) {
                    // 进行值替换
                    String realVal = subSplitter.getFullSentenceV2(variableTtsMap);
                    renderProperties.put(k, realVal);
                } else {
                    renderProperties.put(k, v);
                }
            });
            logger.debug("renderProperties:{}", renderProperties);

            fullSentence = splitter.getFullSentence(renderProperties);
        } catch (Exception e) {
            logger.error("自定义变量获取失败", e);
            fullSentence = text;
        }
        logger.debug("【onAiSay】{}", fullSentence);
        CallDetailMsg callDetailMsg = CallDetailMsg.toRobot(fullSentence, aiSayBO.getStep() == null ? null : aiSayBO.getStep().getId());
        webSocketSendMsgService.sendTextDialogMsgToUser(principal, callDetailMsg, clientType);

        // 持久化通话记录详情
        CallDetailBO callDetail = new CallDetailBO(aiSayBO);
        callDetailList.add(callDetail);
        persistenceCallDetail(callDetail);
    }

    protected void persistenceCallDetail(CallDetailPO callDetail) {
        try {
            callDetail.setTenantId(callRecordInfo.getTenantId());
            callDetail.setCallRecordId(callRecordInfo.getCallRecordId());
            //任务，话术，通话开始时间冗余到call_detail表
            callDetail.setRobotCallJobId(callRecordInfo.getRobotCallJobId());
            callDetail.setDialogFlowId(callRecordInfo.getDialogFlowId());
            callDetail.setStartTime(getStartTime());
            callDetailInfoService.addCallDetailList(Collections.singletonList(callDetail));
        } catch (Exception e) {
            logger.error("文本训练callDetail序列化失败", e);
        }
    }

    private LocalDateTime getStartTime() {
        return startTime;
    }

    private void initCallRecordInfo(UserIdPrincipal userIdPrincipal, UserPO user, Long dialogFlowId) {
        long tenantId = user.getTenantId();
        long distributorId = user.getDistributorId();

        if (userIdPrincipal instanceof TenantUserIdPrincipal) {
            TenantUserIdPrincipal tenantUserIdPrincipal = (TenantUserIdPrincipal) userIdPrincipal;
            tenantId = tenantUserIdPrincipal.getTenantId();
        }

        if (userIdPrincipal instanceof DistributorUserIdPrincipal) {
            DistributorUserIdPrincipal principal = (DistributorUserIdPrincipal) userIdPrincipal;
            distributorId = principal.getDistributorId();
        }

        // callRecord入库
        callRecordInfo = new CallRecordPO();
        callRecordInfo.setTenantId(tenantId);
        callRecordInfo.setRobotCallTaskId(0L);
        callRecordInfo.setCallRecordType(CallRecordTypeEnum.TEXT_TRAINING);
        callRecordInfo.setDistributorId(distributorId);
        callRecordInfo.setRobotCallJobId(ANONYMOUS_JOB_ID);
        callRecordInfo.setCustomerPersonId(VERBAL_TRICK_TRAINING_CUSTOMER_PERSON_ID);
        callRecordInfo.setResultStatus(DialStatusEnum.NO_ANSWER);
        callRecordInfo.setCalledPhoneNumber(user.getPhoneNumber());
        callRecordInfo.setCreateUserId(user.getUserId());
        callRecordInfo.setStartTime(getStartTime());
        CallRecordPO.DeploymentInformation deploymentInformation = new CallRecordPO.DeploymentInformation();
        deploymentInformation.setServerName(ServerInfoConstants.SERVER_HOSTNAME);
        deploymentInformation.setIpAddress(ServerInfoConstants.SERVER_IP_ADDRESS);
        deploymentInformation.setLogId(this.logId);
        callRecordInfo.setDeploymentInformation(deploymentInformation);
	    callRecordInfo.setLogId(this.logId);
        callRecordInfo.setDialogFlowId(dialogFlowId);
        callRecordInfo.setResultStatus(DialStatusEnum.ANSWERED);
        callRecordInfoService.saveNotNull(callRecordInfo);
    }

    public void calleePickup() {
        MDC.put("MDC_LOG_ID", this.logId);
        SystemMsg systemMsg = new SystemMsg("用户已接听", SystemMsgType.CALLEE_PICKUP);
        webSocketSendMsgService.sendCallTaskInfoMsgToUser(principal, systemMsg, clientType);
    }

//    /**
//     * AIsay的ivr
//     *
//     * @param aiSayIvrBO
//     */
//    @Override
//    public void onAiSayIvr(AiSayIvrBO aiSayIvrBO) {
//
//    }
    @Override
    public void transferToThirdParty(Long humanCsStaffGroupId) {

    }

    @Override
    public void transferToHuman() {

    }

//    /**
//     * ivr转人工介入
//     */
//    @Override
//    public void ivrTransferToCs() {
//
//    }


    @Override
    public void transferToCs() {

    }
}
