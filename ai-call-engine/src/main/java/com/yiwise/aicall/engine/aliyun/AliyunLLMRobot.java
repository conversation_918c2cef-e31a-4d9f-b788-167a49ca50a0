package com.yiwise.aicall.engine.aliyun;

import com.alibaba.dashscope.audio.asr.recognition.Recognition;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
import com.alibaba.dashscope.audio.asr.recognition.RecognitionResult;
import com.alibaba.dashscope.audio.asr.recognition.timestamp.Word;
import com.alibaba.dashscope.common.ResultCallback;
import com.alibaba.dashscope.utils.Constants;
import com.yiwise.aicall.engine.robot.AbstractRobot;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.service.redis.RedisKeyCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 阿里云大模型 asr,
 * https://help.aliyun.com/zh/dashscope/developer-reference/paraformer-speech-recognition-1/?spm=a2c4g.11186623.0.0.26971507KSZbd5
 *
 */
@Slf4j
public class AliyunLLMRobot extends AbstractRobot {

    private static final RedisOpsService redisOpsService = AppContextUtils.getBean(RedisOpsService.class);
    private static final String REDIS_KEY = RedisKeyCenter.getAliyunLLMAsrConcurrencyKey();

    private static final int MERGE_SIZE = 3200;

    private final ByteArrayOutputStream mergedStream = new ByteArrayOutputStream(MERGE_SIZE);   // 用来合并asr语音的stream

    private volatile Recognition recognition;

    private long lastSendDataTime = 0;

    private volatile boolean started = false;

    private volatile boolean restarted = false;

    protected AtomicInteger restartedCount = new AtomicInteger(0);
    protected final AtomicBoolean restarting = new AtomicBoolean(false);

    protected long lastSendErrorTime = -1;

    static {
        Constants.connectionConfigurations =
                com.alibaba.dashscope.protocol.ConnectionConfigurations.builder()
                        .connectTimeout(Duration.ofSeconds(120))
                        .readTimeout(Duration.ofSeconds(300))
                        .writeTimeout(Duration.ofSeconds(60))
                        .connectionIdleTimeout(Duration.ofSeconds(300))
                        .connectionPoolSize(300)
                        .maximumAsyncRequests(300)
                        .maximumAsyncRequestsPerHost(300)
                        .proxyPort(443)
                        .proxyAuthenticator(null)
                        .build();
    }


    @Override
    public void send(AliyunAsrSoundManager soundManager, byte[] buffer, int offset, int length) {
        long lastTempSendDataTime = System.currentTimeMillis();
        try {
            // 接收用户的语音，进行识别，10个rtp包的数据，合并为一个大包，发送给阿里云，减少网络请求
            if (mergedStream.size() + length > MERGE_SIZE) {
                byte[] bytes = mergedStream.toByteArray();
                recognition.sendAudioFrame(ByteBuffer.wrap(bytes, 0, mergedStream.size()));
                mergedStream.reset();
            }
            mergedStream.write(buffer, offset, length);
            if (lastSendDataTime != 0 && lastTempSendDataTime - lastSendDataTime >= 3000) {
                log.error("[LogHub]语音发送超时, 超时时长={}", lastTempSendDataTime - lastSendDataTime);
            }
            lastSendDataTime = lastTempSendDataTime;
        } catch (Exception e) {
            log.warn("发送数据异常", e);
            // 如果坐席非空闲，本通电话没有结束，asr断线了，那么重启asr
            if (!soundManager.isFinished()) {
                restartWhenException(soundManager);
                return;
            }
            long currentTimeMillis = System.currentTimeMillis();
            if (currentTimeMillis - lastSendErrorTime > 1000) {
                lastSendErrorTime = currentTimeMillis;
                log.error("[LogHub_Warn] send data error, Message={}", e.getMessage());
            }
        }
    }

    /**
     * 异常重启
     */
    protected void restartWhenException(AliyunAsrSoundManager soundManager) {
        if (restartedCount.get() > 3) {
            log.error("[LogHub_Warn]重启坐席asr已经超过3次，不再重启");
        } else {
            restarting.set(true);
            restarted = true;
            log.warn("[LogHub_Warn]正在重启坐席asr");
            startAsr(soundManager, soundManager.getTaskId());
            afterRestart();
        }
    }

    @Override
    public void startAsr(AliyunAsrSoundManager soundManager, Long taskId) {
        String model = asrContext.getAsrAppkey();
        RecognitionParam.RecognitionParamBuilder builder = RecognitionParam.builder()
                .model(model)
                .format("pcm")
                .sampleRate(8000)
                .disfluencyRemovalEnabled(false)
                .parameter("language_hints", new String[]{"zh", "en"})
                .parameter("semantic_punctuation_enabled", false)
                .apiKey(ApplicationConstant.ALI_LLM_ASR_API_KEY);

        if (MapUtils.isNotEmpty(asrContext.getAsrParam())) {
            asrContext.getAsrParam().forEach(builder::parameter);
        }

        RecognitionParam param = builder.build();

        try {
            recognition = new Recognition();
            beforeStart();
            log.debug("param:{}", JsonUtils.object2String(param));
            long start = System.currentTimeMillis();
            recognition.call(param, new RecognitionCallback(soundManager));
            long end = System.currentTimeMillis();
            started = true;
            long duration = end - start;
            log.debug("asr启动耗时:{}ms", duration);
            if (duration > 2000) {
                log.warn("[LogHub_Warn] LLM ASR client 初始化超时, TaskId={}.", taskId);
            }
            afterStart();
            redisOpsService.incrementKey(REDIS_KEY);
            redisOpsService.expire(REDIS_KEY, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("[LogHub_Warn] LLM ASR client init failed, TaskId={}.", taskId, e);
            restartWhenException(soundManager);
        }
    }

    @Override
    public void releaseRobot(boolean calleePickup, Long taskId) {
        try {
            beforeStop();
            if (started) {
                recognition.stop();
            }
            afterStop();
        } catch (Exception e) {
            log.warn("释放 bot 异常", e);
        } finally {
            if (started) {
                redisOpsService.incrementKey(REDIS_KEY, -1);
                redisOpsService.expire(REDIS_KEY, 1, TimeUnit.HOURS);
            }
        }
    }

    @Override
    public boolean isRestarted() {
        return restarted;
    }

    static class RecognitionCallback extends ResultCallback<RecognitionResult> {
        final AliyunAsrSoundManager asrManager;

        private final String logId;
        public RecognitionCallback(AliyunAsrSoundManager asrManager) {
            this.asrManager = asrManager;
            logId = MDC.get("MDC_LOG_ID");
        }

        @Override
        public void onEvent(RecognitionResult recognitionResult) {
            String oldId = MDC.get("MDC_LOG_ID");
            MDC.put("MDC_LOG_ID", logId);
            try {
                log.debug("on raw message:{}", recognitionResult);
                try {
                    int beginTime = asrManager.getMuteAudioFilter().convertToRealTime(recognitionResult.getSentence().getBeginTime().intValue());

                    Optional<Integer> endTime = recognitionResult.getSentence().getWords().stream()
                            .map(Word::getEndTime)
                            .max(Long::compareTo)
                            .map(item -> asrManager.getMuteAudioFilter().convertToRealTime(item.intValue()));
                    String userInput = recognitionResult.getSentence().getText();

                    Double filePlayProgress = asrManager.getCurrentFilePlayProcess();
                    Integer filePlayDuration = asrManager.getCurrentFilePlayDuration();
                    asrManager.getAsrResultEventListener().processUserSay(userInput,
                            filePlayProgress,
                            filePlayDuration,
                            recognitionResult.isSentenceEnd(),
                            beginTime,
                            endTime.orElse(beginTime));
                } catch (Exception e) {
                    log.warn("[LogHub_Warn] 处理 阿里云大模型asr 识别结果异常:{}", e.getMessage(), e);
                }
            } finally {
                MDC.put("MDC_LOG_ID", oldId);
            }
        }

        @Override
        public void onComplete() {
            String oldId = MDC.get("MDC_LOG_ID");
            MDC.put("MDC_LOG_ID", logId);
            try {
                log.debug("onComplete()");
            } finally {
                MDC.put("MDC_LOG_ID", oldId);
            }
        }

        @Override
        public void onError(Exception e) {
            String oldId = MDC.get("MDC_LOG_ID");
            MDC.put("MDC_LOG_ID", logId);
            try {
                log.debug("[LogHub_Warn] 阿里大模型 asr 处理异常:{}", e.getMessage(), e);
            } finally {
                MDC.put("MDC_LOG_ID", oldId);
            }
        }
    }
}
