package com.yiwise.freeswitch.api.controller.freeswitch;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.dal.entity.CsStaffGroupPO;
import com.yiwise.core.model.bo.cs.TransferGroupBO;
import com.yiwise.core.service.engine.csseat.CsStaffGroupService;
import com.yiwise.core.service.engine.phonenumber.FreeswitchInfoService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created by 昌夜 on 2023/4/4.
 */
@RestController(value = "LuaController")
@RequestMapping("/apiLua")
public class LuaApiController {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FreeswitchInfoService freeswitchInfoService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private CsStaffGroupService staffGroupService;

    @NoLogin
    @RequestMapping(value = "/queryTransferGroupBO")
    public ResultObject queryTransferGroupBO(@RequestParam Long callRecordId) {
        TransferGroupBO groupBO = redisOpsService.get(RedisKeyCenter.getTransferGroupRecord(callRecordId), TransferGroupBO.class);
        if(groupBO == null) {
            logger.info("获取转坐席组失败，无法取到缓存值 {}", callRecordId);
            return ResultObject.fail(ComErrorCode.NOT_EXIST, "获取转坐席组失败");
        }
        return ResultObject.success(groupBO);
    }

    @NoLogin
    @RequestMapping(value = "/queryCsStaffGroupPO")
    public ResultObject queryCsStaffGroupPO(@RequestParam Long groupId) {
        CsStaffGroupPO staffGroupPO = staffGroupService.selectByKey(groupId);
        return ResultObject.success(staffGroupPO);
    }
}
