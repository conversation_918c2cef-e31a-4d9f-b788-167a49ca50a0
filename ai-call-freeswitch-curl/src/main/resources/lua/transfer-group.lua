json = require "json"

local callRecordId = argv[1] or '';
url_1 = "http://192.168.11.111/apiLua/queryTransferGroupBO?callRecordId=" .. callRecordId
url_2 = "http://192.168.11.111/apiLua/queryCsStaffGroupPO?groupId="

function queryTransferGroupBO()
    session:setVariable("curl_timeout", "2")
    session:execute("curl", url_1)
    local response_code = session:getVariable("curl_response_code")
    local response_data = session:getVariable("curl_response_data")
    if(response_code ~= "200") then
        return ""
    end
    session:consoleLog("INFO", "========= queryTransferGroupBO: " .. response_data .. "\n")
    local res = json.decode(response_data)
    if(res['code'] ~= 200) then
        return ""
    end
    return res["data"]
end

function queryCsStaffGroupPO(groupId)
    session:setVariable("curl_timeout", "2")
    session:execute("curl", url_2 .. groupId)
    local response_code = session:getVariable("curl_response_code")
    local response_data = session:getVariable("curl_response_data")
    if(response_code ~= "200") then
        return ""
    end
    session:consoleLog("INFO", "========= queryCsStaffGroupPO: " .. response_data .. "\n")
    local res = json.decode(response_data)
    if(res['code'] ~= 200) then
        return ""
    end
    return res["data"]
end

local group = queryTransferGroupBO()
if group == "" or group["phoneNumber"] == "" then
    session:execute("respond", "698 Transfer Group Failed")
    os.exit()
end

local groupId = group["groupId"];
local csStaffGroupPO = queryCsStaffGroupPO(groupId);
session:execute("set", "sip_X-BRC=")
if csStaffGroupPO["groupType"] == 4 then
    session:consoleLog("INFO", "========= 转人工第三方呼叫中心: " .. staffGroupPO["transferSipUri"] .. "\n")
    session:execute("set", "sip_h_X-EID=" .. groupBO["robotCallJobId"] .. "-" .. groupBO["robotCallTaskId"]);
    session:execute("set", "sip_h_X-EIP=" .. staffGroupPO["transferSipUri"]);
    session:execute("transfer", "812275 XML default");
    os.exit()
end

local XTP = session:getVariable("sip_h_X-TP") or '';
local sip_call_id = session:getVariable("sip_call_id");
session:consoleLog("INFO", "XTP=" .. XTP .. ", sip_call_id=" .. sip_call_id .. "\n");
if csStaffGroupPO["groupType"] == 3 then
    session:execute("transfer", "XXTT" .. XTP .. groupBO["phoneNumber"] .. "XX" .. sip_call_id .. " XML default");
    os.exit();
end

if csStaffGroupPO["groupType"] == 1 then
    session:execute("set", "sip_X-BRC=BRCS")
    session:execute("transfer", "BRCSXXTT" .. XTP .. groupBO["phoneNumber"] .. "XX" .. sip_call_id .. " XML default");
    os.exit();
end