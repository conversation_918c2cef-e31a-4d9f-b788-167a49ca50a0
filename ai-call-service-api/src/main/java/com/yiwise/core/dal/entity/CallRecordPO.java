package com.yiwise.core.dal.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.core.dal.entity.base.BaseTimePO;
import com.yiwise.core.helper.objectstorage.AddOssPrefix;
import com.yiwise.core.model.asr.AsrProviderEnum;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.callin.CallTypeEnum;
import com.yiwise.core.model.enums.callrecord.CallOutModelEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.enums.callrecord.InterceptStatusEnum;
import com.yiwise.core.model.enums.callrecord.handler.CallRecordTypeEnumHandler;
import com.yiwise.core.model.enums.callrecord.handler.InterceptStatusEnumHandler;
import com.yiwise.core.model.enums.handler.*;
import com.yiwise.core.model.enums.handler.base.*;
import com.yiwise.core.model.enums.ope.CallRecordDefectTypeEnum;
import com.yiwise.core.model.enums.ope.CallRecordTrackStatusEnum;
import com.yiwise.core.model.enums.ope.handler.CallRecordDefectTypeEnumHandler;
import com.yiwise.core.model.enums.ope.handler.CallRecordTrackStatusEnumHandler;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.enums.robotcalljob.handler.CallJobHangupEnumHandler;
import com.yiwise.core.serializer.CustomerPersonPhoneNumber;
import lombok.*;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;


@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "call_record")
public class CallRecordPO extends BaseTimePO implements Serializable {

    /**
     * 通话记录ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long callRecordId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 代理商ID
     */
    private Long distributorId;

    /**
     * 任务ID
     */
    private Long robotCallJobId;

	/**
	 * 外呼计划id
	 */
	 private Long callOutPlanId;

    /**
     * 话术ID
     */
    private Long dialogFlowId;

    /**
     * 子任务ID
     */
    private Long robotCallTaskId;

    /**
     * 客户ID
     */
    private Long customerPersonId;

    /**
     * 客户姓名
     */
    private String customerPersonName;

    /**
     * 被叫号码
     */
    @CustomerPersonPhoneNumber
    private String calledPhoneNumber;

    /**
     * 主叫号码ID
     */
    private Long tenantPhoneNumberId;

    /**
     * 线路ID
     */
    private Long phoneNumberId;

    /**
     * 通话状态
     */
    @ColumnType(typeHandler = DialStatusEnumHandler.class)
    private DialStatusEnum resultStatus;

    /**
     * AI意向
     */
    private Integer intentLevel;

    /**
     * 真实意向
     */
    private Integer realIntentLevel;

    /**
     * 意向标签分组id
     */
    private Long intentLevelTagId;

    /**
     * 用户关注点
     */
    @ColumnType(typeHandler = SetToJsonTypeHandler.class)
    private Set<String> customerConcern;

    /**
     * 完整录音URL
     */
    @AddOssPrefix
    private String fullAudioUrl;

    /**
     * 用户录音URL
     */
    @AddOssPrefix
    private String customerAudioUrl;

    /**
     * 分析基础依据
     */
    private String analysisBasis;

    /**
     * 通话开始时间
     * 一次外呼的通话记录会在开始外呼时保存①,外呼结束时更新②
     * ①的时候不设置startTime
     * ②的时候更新startTime
     * 用startTime是否为空作为两个状态的区分
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 通话结束时间
     */
    @JsonIgnore
    private LocalDateTime endTime;

    /**
     * 通话时长（s）
     */
    private Long chatDuration;

    /**
     * 振铃时长
     */
    private Long ringDuration;

    /**
     * 对话轮次
     */
    private Integer chatRound;

    /**
     * 手动标注标记
     * 是否手动进行了标注 0: 否 1: 是
     */
    private Boolean manualMarked;

    /**
     * 阅读状态
     * 二进制表示 0-未读 1-已读
     * 第一位001-BOSS 第二位010-OPE 第三位100-CRM 第四位1000-外包巡检平台 第五位10000-是否进行通用意图标注
     */
    private Integer readStatus;

    /**
     * 跟进状态
     */
    @ColumnType(typeHandler = CallRecordTrackStatusEnumHandler.class)
    private CallRecordTrackStatusEnum trackStatus;

    /**
     * 巡检类型
     */
    @ColumnType(typeHandler = CallRecordDefectTypeEnumHandler.class)
    private Set<CallRecordDefectTypeEnum> defectType;

    /**
     * 通话类型
     * 0-本地通话 1-外地通话
     */
    @ColumnType(typeHandler = CallTypeEnumHandler.class)
    private CallTypeEnum callType;

    /**
     * 号码归属地表id
     */
    private Long phoneLocationId;

    /**
     * 设置默认值
     * update时 json不可为null
     */
    @ColumnType(typeHandler = MapToJsonTypeHandler.class)
    private Map<String, String> properties;


    /**
     * 动态变量, 来源为对话过程中动态变量收集到的内容
     * update时 json不可为null
     */
    @ColumnType(typeHandler = MapToJsonTypeHandler.class)
    private Map<String, String> dynamicVariables;

    /**
     * 客户属性
     */
    @ColumnType(typeHandler = SetToJsonTypeHandler.class)
    private Set<String> attributes;

    /**
     * 转接类型
     */
    @ColumnType(typeHandler = CallRecordTransferTypeEnumHandler.class)
    private CallRecordTransferTypeEnum transferType;

    /**
     * 转人工号码
     */
    private String transferPhoneNumber;

    /**
     * 情绪
     * 0面无表情，1开心，2生气
     */
    @ColumnType(typeHandler = CustomerEmotionHandler.class)
    private CustomerEmotionEnum emotion;

    /**
     * 挂断类型
     * 0-客户挂断 1-AI挂断
     */
    @ColumnType(typeHandler = CallJobHangupEnumHandler.class)
    private CallJobHangupEnum hangupBy;

    /**
     * 通话记录类型
     */
    @ColumnType(typeHandler = CallRecordTypeEnumHandler.class)
    private CallRecordTypeEnum callRecordType;

    /**
     * 部署信息，数据库中为JSON类型
     */
    @ColumnType(typeHandler = DeploymentInformationToJsonTypeHandler.class)
    private DeploymentInformation deploymentInformation;

    /**
     * 已推送微信的用户列表
     */
    @ColumnType(typeHandler = LongSetToJsonTypeHandler.class)
    private Set<Long> wechatPushUsers;

    /**
     * 微信推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime wechatPushTime;

    /**
     * 创建者id
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 转人工触发时间
     */
    private LocalDateTime transferTime;

    @Data
    public static class DeploymentInformation implements Serializable, IMybatisClassToJson {
        /**
         * 主机名
         */
        private String serverName;
        /**
         * IP地址
         */
        private String ipAddress;
        /**
         * RequestID
         */
        private String logId;
        /**
         * freeswitch信息ID
         */
        private Long freeswitchInfoId;
        /**
         * 通话SipID
         */
        private String callSid;

        /**
         * ASR模型ID，等价于appkey
         */
        private String appkey;

        /**
         * 通话过程中asr是否重启过
         */
        private Boolean asrRestarted;

        /**
         * nlp模型版本
         */
        private String nlpModelVersion;

        /**
         * asr处理延迟次数
         */
        private Integer asrDelayCount;

        /**
         * rtp丢包率
         */
        private Float missPercent;

        /**
         * ASR供应商
         */
        private AsrProviderEnum asrProvider;

        /**
         * 供应商名称
         */
        private String asrProviderName;

        /**
         * 话术重构-ASR热词组ID
         */
        private String asrVocabId;

        /**
         * 话术重构-ASR自学习模型ID
         */
        private String asrSelfLearningId;

        /**
         * asr 累计跳过数据, 单位毫秒
         */
        private Integer asrSkipDuration;

        /**
         * 大模型对话 token 使用信息
         */
        private Integer llmToken;

        private Double llmPrice;
    }

    /**
     * 人机切换通知标志
     * 0-非人工介入 1-未通知 2-通知成功 3-通知失败
     */
    @ColumnType(typeHandler = CsTransferNotifyEnumHandler.class)
    private CsTransferNotifyEnum csTransferNotify;

    /**
     * 是否监听
     * 0-未监听 1-监听
     */
    private Integer csMonitorFlag;

    /**
     * 是否介入
     * 0-未介入 1-介入
     */
    private Integer csTransferAccept;

    /**
     * 坐席组ID
     */
    private Long csStaffGroupId;

    /**
     * 接听人员ID
     */
    private Long csStaffId;

    /**
     * 客户性别
     */
    @ColumnType(typeHandler = GenderEnumHandler.class)
    private GenderEnum customerPersonGender;

    /**
     * 识别性别
     */
    @ColumnType(typeHandler = GenderEnumHandler.class)
    private GenderEnum recognizeGender;

    /**
     * 是否使用一知ASR
     */
    private Boolean useYiwiseAsr;

    /**
     * 外呼次数
     */
    private Integer callCount;

    /**
     * 加微账号名称
     */
    private String addWechatFriendAccountName;

    /**
     * 加微请求状态,不同加微平台的状态转换有区别
     * 0  发送成功
     * 1  加微通过
     * 2  重复加微
     * 3  等待发送
     * 4  发送中
     * -1 加微出错
     */
    private Integer addWechatFriendStatus;

	@ColumnType(typeHandler = ScrmAddFriendResultEnumHandler.class)
	private ScrmAddFriendResultEnum addWechatFriendScrmResult;

    /**
     * 加微申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime addWechatFriendSendTime;

    /**
     * 加微通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime addWechatFriendAdoptTime;

    @ColumnType(typeHandler = LongSetToJsonTypeHandler.class)
    private Set<Long> customerLevelTagDetailIds;

    /**
     * 导入时间 取task create_time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime importTime;

    @ColumnType(typeHandler = CallOutModelEnumHandler.class)
    private CallOutModelEnum callOutModel;

    /**
     * 总语音包数量
     */
    private Long totalPacketsCount;

    /**
     * 语音包丢包数量
     */
    private Long missingPacketsCount;

    /**
     * 是否接收到sip信令
     */
    private Boolean recSip;

	/**
	 * 通话日志id
	 */
	private String logId;

    /**
     * 人工介入状态
     */
    @ColumnType(typeHandler = InterceptStatusEnumHandler.class)
    private InterceptStatusEnum interceptStatus;

}
