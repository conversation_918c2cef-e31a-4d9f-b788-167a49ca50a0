package com.yiwise.core.dal.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.SmsSignatureBindTypeEnum;
import com.yiwise.core.model.enums.handler.SmsSignatureBindTypeEnumHandler;
import com.yiwise.core.model.vo.sms.SmsSignatureBindVO;
import lombok.Data;
import java.io.Serializable;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Table(name = "sms_signature_bind_record")
public class SmsSignatureBindRecordPO implements Serializable {
    /**
     * BIGINT(20) 必填
     * 
     */
    @Id
    @Column(name = "sms_signature_bind_record_id")
    @GeneratedValue(generator = "JDBC")
    private Long smsSignatureBindRecordId;

    /**
     * BIGINT(19) 必填
     * 短信ID
     */
    private Long smsSignatureId;

    /**
     * BIGINT(19) 默认值[0] 必填
     * 直销客户ID,如果绑定的是代理商客户，为0
     */
    private Long keyId;

    @ColumnType(typeHandler = SmsSignatureBindTypeEnumHandler.class)
    private SmsSignatureBindTypeEnum bindType;

    /**
     * TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填
     * 创建时间（纠错时间）
     */
    private LocalDateTime createTime;



    private static final long serialVersionUID = 1L;

}