package com.yiwise.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommonDecryptResultDTO {

    /**
     * 0 成功
     * -1 失败
     */
    private Integer code;

    /**
     * 真实的手机号码
     */
    private String mobile;

    /**
     * 错误原因
     */
    private String errMsg;

    public boolean isSuccess() {
        return this.code == 0;
    }
}
