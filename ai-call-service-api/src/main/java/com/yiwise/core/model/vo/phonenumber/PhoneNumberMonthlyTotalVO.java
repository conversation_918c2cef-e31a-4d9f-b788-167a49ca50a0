package com.yiwise.core.model.vo.phonenumber;

import lombok.Data;

import java.util.Map;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Header.时间;

/**
 * <AUTHOR>
 */
@Data
public class PhoneNumberMonthlyTotalVO extends PhoneNumberMonthlyVO {

    /**
     * 累加求和
     * @param vo
     */
    public void sum(PhoneNumberMonthlyVO vo) {
        if (Objects.isNull(getCallOutMonthlyRent())) {
            setCallOutMonthlyRent(0L);
        }
        if (Objects.nonNull(vo) && Objects.nonNull(vo.getCallOutMonthlyRent())) {
            setCallOutMonthlyRent(getCallOutMonthlyRent() + vo.getCallOutMonthlyRent());
        }
    }

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> rowModelMap = super.getRowModelMap();
        rowModelMap.put(时间, "总计");
        return rowModelMap;
    }
}
