package com.yiwise.core.model.vo.csmcost;

import com.yiwise.core.model.enums.TenantAccountStatusEnum;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/03/15
 */
@Data
public class CsmCostCompositionListVO {

	private Long tenantId;

	private String customerId;

	private String companyName;

	private TenantAccountStatusEnum tenantAccountStatus;

	private AccountTypeEnum accountType;

	private String csmUserName;

	private String accountManager;

	private String deploymentUserName;

	private Long cost;

	private Double costChange;
}
