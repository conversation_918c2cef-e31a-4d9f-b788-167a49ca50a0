package com.yiwise.core.model.vo.scrm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: wangguomin
 * @Date: 2022/11/30 11:44
 */
@Data
public class ServerResourceVO {
    /**
     * 自增id
     *
     * @mbg.generated Fri Jul 23 11:44:53 CST 2021
     */
    private Long serverResourceId;

    private Long aiccTenantId;
    private Long tenantId;
    private Long scrmTenantId;

    /**
     * ip地址
     *
     * @mbg.generated Fri Jul 23 11:44:53 CST 2021
     */
    private String ipAddress;

    /**
     * 服务器名称
     *
     * @mbg.generated Fri Jul 23 11:44:53 CST 2021
     */
    private String hostName;
    /**
     * 绑定租户id
     *
     * @mbg.generated Fri Jul 23 11:44:53 CST 2021
     */
    private Long bandingTenantId;

    /**
     * 绑定关联表id
     *
     * @mbg.generated Fri Jul 23 11:44:53 CST 2021
     */
    private Long bandingId;

    /**
     * 绑定时间
     */
    private LocalDateTime bandingTime;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    protected LocalDateTime createTime;
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    protected LocalDateTime updateTime;
    /**
     * 公司名
     */
    private String companyName;
    /**
     * 客户经理
     */
    private String managerName;
    /**
     * csm
     */
    private String csmName;

    /**
     * 服务周期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 服务周期结束时间
     */
    private LocalDateTime endTime;
    /**
     * 最后使用日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime lastUseTime;

    private String note;
}
