package com.yiwise.core.model.vo;

import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.handler.*;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.util.List;

@Data
public class NewMonitorIndexVO implements Serializable {

    /**
     * 一级指标
     */
    @ColumnType(typeHandler = CallOutJobTypeEnumHandler.class)
    private CallOutJobTypeEnum jobType;

    /**
     * 二级指标
     */
    @ColumnType(typeHandler = CallOutIndexTypeEnumHandler.class)
    private CallOutIndexTypeEnum indexName;

    /**
     * 三级指标
     */
    @ColumnType(typeHandler = CallOutTotalTypesEnumHandler.class)
    private List<CallOutTotalTypesEnum> indexTypes;

    /**
     * 监控指数比较枚举
     */
    @ColumnType(typeHandler = WarningNumEnumHandler.class)
    private WarningNumEnum numType;

    /**
     * 数值
     */
    private Double number;

    /**
     * 是否是百分比
     */
    private Boolean isPercent;

    /**
     * 触发预警值
     */
    private String warnValue;

    /**
     * 预警后动作配置
     */
    @ColumnType(typeHandler = WarningActionEnumHandler.class)
    private WarningActionEnum warningAction;

    /**
     * 多个预警条件之间的操作符 AND OR
     */
    @ColumnType(typeHandler = WarningOperatorEnumHandler.class)
    private WarningOperatorEnum operator;
}
