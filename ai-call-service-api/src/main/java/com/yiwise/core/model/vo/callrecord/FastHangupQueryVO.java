package com.yiwise.core.model.vo.callrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.core.model.enums.callrecord.FastHangupReasonEnum;
import com.yiwise.core.model.enums.handler.FastHangupReasonEnumHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class FastHangupQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 通话记录ID
     */
    private Long callRecordId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 类型
     */
    @ColumnType(typeHandler = FastHangupReasonEnumHandler.class)
    private FastHangupReasonEnum fastHangupReason;
}
