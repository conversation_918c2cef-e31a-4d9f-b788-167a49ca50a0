package com.yiwise.core.model.bo.export;

import com.yiwise.core.model.dto.callcost.CallCostDetailExportDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;

import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORTCALLCOST;

/**
 * <AUTHOR>
 * @date 2023/1/29 14:09:26
 */
@Data
public class CallCostDetailExportBO extends CallCostDetailExportDTO implements CommonExportBO {
    @Override
    public Map<String, Object> getRowModelMap() {
        return getRowModelExportMap(APIENGINE_SPRINGBATCHJOB_EXPORTCALLCOST);
    }

    public Long getCallRecordIdExport() {
        return getCallRecordId();
    }

    public String getCustomerPersonNameExport() {
        return getCustomerPersonName();
    }

    public String getCalledPhoneNumberExport() {
        return getCalledPhoneNumber();
    }

    public String getRobotCallJobNameExport() {
        return getRobotCallJobName();
    }

    public LocalDateTime getStartTimeExport() {
        return getStartTime();
    }

    public String getCallingPhoneNumberNameExport() {
        return getCallingPhoneNumberName();
    }

    public Object getResultStatusExport() {
        return getResultStatus();
    }

    public String getCalledPhoneNumberLocationProvExport() {
        return getCalledPhoneNumberLocationProv();
    }

    public String getCalledPhoneNumberLocationCityExport() {
        return getCalledPhoneNumberLocationCity();
    }

    public Long getChatDurationExport() {
        return getChatDuration();
    }

    public Long getRingDurationExport() {
        return getRingDuration();
    }

    public Long getBillRingDurationExport() {
        return getBillRingDuration();
    }

    public Long getBillChatDurationExport() {
        return getBillChatDuration();
    }

    public Object getCallCostExport() {
        return new BigDecimal(getCallCost()).divide(new BigDecimal(1000).setScale(3, RoundingMode.HALF_UP));
    }

    public Object getCallTypeExport() {
        return getCallType();
    }
}