package com.yiwise.core.model.vo.textservice;




import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.dal.entity.VisitorInfoPO;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@ToString
public class VisitorInfoVO extends VisitorInfoPO implements Serializable {

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
     LocalDateTime connectionTime;

    String accessKey;

    String sessionId;

    Long queueDuration;

    Long chatDuration;

    String customerGroupName;
}
