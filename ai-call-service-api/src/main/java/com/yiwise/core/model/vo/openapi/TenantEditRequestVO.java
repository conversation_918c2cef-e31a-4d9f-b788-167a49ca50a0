package com.yiwise.core.model.vo.openapi;

import com.yiwise.account.api.enums.TenantStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @date: 2024 04 24 15:04
 */
@Data
public class TenantEditRequestVO implements Serializable {

    /**
     * 客户id
     */
    private Long tenantId;
    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 行业
     */
    private Long customerTrackTypeId;

    /**
     * 租户状态：禁用/弃用
     */
    private TenantStatusEnum tenantStatus;

}
