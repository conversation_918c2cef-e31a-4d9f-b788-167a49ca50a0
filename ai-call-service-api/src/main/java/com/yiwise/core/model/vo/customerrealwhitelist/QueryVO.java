package com.yiwise.core.model.vo.customerrealwhitelist;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryVO extends AbstractQueryVO {
    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime beginCreateDateTime;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endCreateDateTime;

    private String phoneNumber;

    private String customerName;

    private List<Long> customerRealWhiteListIds;

    private Long tenantId;
}
