package com.yiwise.core.model.vo.customer;

import com.yiwise.core.model.enums.OrderTimeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.validate.customerPerson.CustomerPersonAddAndImportJobValidate;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/9/6
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerPersonCreateToJobVO extends CustomerPersonCreateVO{

    @NotNull(message = "任务id不能为空！", groups = {CustomerPersonAddAndImportJobValidate.class})
    private Long robotCallJobId;

    private Long smsJobId;

    private boolean callRecordDup = false;
    private SystemEnum systemType;

    // scrm 下单时间字段
    private OrderTimeEnum orderTimeEnum;

	/**
	 * 指定加微账号id
	 */
	private String addWechatAccountId;
	/**
	 * 指定加微组织id
	 */
	private Long addWechatOrgId;
	/**
	 * 指定SCRM员工id
	 */
	private Long addWechatScrmUserId;

	/**
	 * 字段属性ID列表
	 */
	private List<Long> optionIds;
}
