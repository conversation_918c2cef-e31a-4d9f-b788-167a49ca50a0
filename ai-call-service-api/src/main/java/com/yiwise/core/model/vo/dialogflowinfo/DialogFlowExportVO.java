package com.yiwise.core.model.vo.dialogflowinfo;

import com.yiwise.core.model.dialogflow.entity.DialogFlowConfigurationPO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowIntentRulePO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowNlpConfigPO;
import lombok.Data;

import java.util.List;

@Data
public class DialogFlowExportVO {

    private DialogFlowInfoPO dialogFlow;

    private DialogFlowConfigurationPO configuration;

    private List<DialogFlowIntentRulePO> intentRuleList;

    private DialogFlowNlpConfigPO nlpConfiguration;
}
