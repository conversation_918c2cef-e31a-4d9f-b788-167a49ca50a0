package com.yiwise.core.model.vo.financestats;

import com.yiwise.core.model.enums.ope.OpeQuerySmsStatsTypeEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2021/12/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OpeQuerySmsStatsVO extends AbstractQueryVO {

	private Long tenantId;

	@NotNull
	private LocalDate startDate;

	@NotNull
	private LocalDate endDate;

	@NotNull
	private OpeQuerySmsStatsTypeEnum tenantType;

	/**
	 * 导出使用
	 */
	private Long userId;
}
