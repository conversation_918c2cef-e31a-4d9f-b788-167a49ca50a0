package com.yiwise.core.model.vo.sharewhitelist;

import com.yiwise.core.dal.entity.SharePolicyPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import lombok.NoArgsConstructor;

/**
 * @ClassName SharePolicyListVO
 * <AUTHOR>
 * @Date 2019/3/26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SharePolicyListVO extends SharePolicyPO implements Serializable {

    /**
     * 共享组客户数量
     */
    private Integer tenantCount;
    /**
     * 共享黑名单的（手机号）个数
     */
    private Integer whiteListCount;

}
