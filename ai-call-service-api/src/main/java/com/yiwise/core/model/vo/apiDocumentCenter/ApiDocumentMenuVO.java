package com.yiwise.core.model.vo.apiDocumentCenter;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApiDocumentMenuVO extends AbstractQueryVO implements Serializable {

    private static final long serialVersionUID = -2855986944278724632L;

    /**
     * 主键
     */
    private Long apiDocumentMenuId;

    /**
     * 菜单名字
     */
    private String name;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 菜单等级
     */
    private String level;

    /**
     * 菜单实质类型  article：文章； menu:菜单
     */
    private String type;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 意向移动到的按钮Id
     */
    private Long aimMenuId;

    /**
     * 下一层级的节点
     */
    private List<ApiDocumentMenuVO> next;

    /**
     * 发布计数  0：未发布  1~n：发布 或 菜单下有发布的文章
     */
    private Integer publishedCnt;

    /**
     * 类型 0：旗舰版 1：轻量化
     */
    private Integer envType;

}
