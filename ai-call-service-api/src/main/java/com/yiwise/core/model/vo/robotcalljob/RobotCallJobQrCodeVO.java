package com.yiwise.core.model.vo.robotcalljob;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: wux<PERSON><PERSON><EMAIL>
 * @date: 2023 03 28 10:39
 */
@Data
public class RobotCallJobQrCodeVO implements Serializable {

    private Long tenantId;

    private Long userId;

    private Long robotCallJobId;

    /**
     * 二维码有效时长
     */
    private Integer timeoutHour;

    /**
     * 生成的二维码ID
     */
    private String qrCodeId;

    /**
     * 导入客户 二维码展示的 标题
     */
    private String qrCodeTitle;

    /**
     * 导入客户 二维码展示的 内容
     */
    private String qrCodeContent;

    /**
     * 导入客户 二维码展示的 背景
     */
    private String qrCodeBackground;

    /**
     * 二维码失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime qrCodeExpireTime;


}
