package com.yiwise.core.model.vo.openapi.qiyu;

import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.vo.ope.TenantPriceVO;
import com.yiwise.core.validate.openapi.QiyuAddBossTenantValidate;
import com.yiwise.core.validate.openapi.QiyuAddUserValidate;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class TenantQiyuVO extends TenantPO implements Serializable, TenantPriceVO {

    @NotNull(message = "公司名称不能为空！", groups = {QiyuAddUserValidate.class})
    private String tenantName;

    @NotNull(message = "用户登录名不能为空！", groups = {QiyuAddBossTenantValidate.class})
    private String userNumber;

	private Double aiFareDouble;

	private Double comFareDouble;
}
