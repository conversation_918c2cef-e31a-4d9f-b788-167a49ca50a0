package com.yiwise.core.model.vo.callcost;


import com.yiwise.core.model.enums.ChatDurationEnum;
import com.yiwise.core.model.enums.DialStatusEnum;
import com.yiwise.core.model.enums.callin.CallTypeEnum;
import com.yiwise.core.model.enums.callrecord.CallOutModelEnum;
import com.yiwise.core.model.enums.handler.DialStatusEnumHandler;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import com.yiwise.core.serializer.CustomerPersonPhoneNumber;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2018/9/4
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CallCostQueryVO extends AbstractQueryVO implements Serializable {
    private String searchWords;
    private Long callRecordId;
    private String customerPersonName;
    @CustomerPersonPhoneNumber
    private String calledPhoneNumber;
    private Long robotCallJobId;
    private Long tenantPhoneNumberId;
    private String city;
    @ColumnType(typeHandler = DialStatusEnumHandler.class)
    private DialStatusEnum resultStatus;
    private Integer beginChatDuration;
    private Integer endChatDuration;
    private ChatDurationEnum chatDuration;
    private LocalDate beginDate;
    private LocalDate endDate;
    private CallTypeEnum callType;
    private Long tenantId;
    private Integer type = 0;
    private Long userId;
	@ColumnType(typeHandler = DialStatusEnumHandler.class)
    private CallOutModelEnum callOutModel;
    /**
     * 新版外呼的ChatDurationEnum
     * @see com.yiwise.aicc.common.enums.base.ChatDurationEnum
     */
    private String chatDurationNew;

    public void setDefaultDuration(){
        if (null == this.getBeginDate()){
            this.beginDate = LocalDate.now().minusDays(92);
        }
        if (null == this.getEndDate()){
            this.endDate = LocalDate.now().plusDays(1);
        }
    }
    private Long maFlowId;
}
