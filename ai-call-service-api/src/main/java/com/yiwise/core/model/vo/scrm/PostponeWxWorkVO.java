package com.yiwise.core.model.vo.scrm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: wangguomin
 * @Date: 2022/12/7 16:39
 */
@Data
public class PostponeWxWorkVO {
    private Long scrmTenantRelationId;
    private Long tenantId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    private Long userId;
}
