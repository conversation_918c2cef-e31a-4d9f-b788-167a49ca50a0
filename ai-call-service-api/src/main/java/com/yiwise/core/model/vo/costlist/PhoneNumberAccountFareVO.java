package com.yiwise.core.model.vo.costlist;

import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.handler.PhoneTypeEnumHandler;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * @ClassName PhoneNumberAccountFareVO
 * <AUTHOR>
 * @Date 2018/11/12
 **/
@Data
@ToString
public class PhoneNumberAccountFareVO implements Serializable {

    /**
     * 线路id
     */
    private Long phoneNumberId;
    /**
     * 线路手机号
     */
    private String phoneNumber;

    /**
     * 线路名称
     */
    private String phoneName;

    /**
     * 线路费率
     */
    private Long phoneNumberAccountFare;

    /**
     * 线路类型
     */
    @ColumnType(typeHandler = PhoneTypeEnumHandler.class)
    private PhoneTypeEnum phoneType;
}
