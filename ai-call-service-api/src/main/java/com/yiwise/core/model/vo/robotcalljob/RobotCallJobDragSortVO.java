package com.yiwise.core.model.vo.robotcalljob;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @date: 2022 07 25 14:21
 */
@Data
public class RobotCallJobDragSortVO implements Serializable {

    /**
     * 拖拽后的 前前个任务
     * 不为空时 表示任务拖拽到当前分页最下面
     * 把拖拽任务的sortId 与 preJobSortId 互换，然后preJob的sortId 取中值
     */
    private Long prePreJobId;

    /**
     * 拖拽后的 前一个任务
     */
    private Long preJobId;

    /**
     * 拖拽的文件
     */
    @NotNull
    private Long dragJobId;

    /**
     * 拖拽后的 后一个任务
     */
    private Long afterJobId;

    /**
     * 拖拽后的 后后个任务
     * 不为空时 表示任务拖拽到当前分页最上面
     * 把拖拽任务的sortId 与 afterJobSortId 互换，然后afterJob的sortId 取中值
     */
    private Long afterAfterJobId;

    private Long tenantId;

    private Long userId;

    /**
     * 是否需要重新排序
     */
    private boolean reorder = false;
}
