package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.batch.common.ExcelRowModelResolver;
import com.yiwise.core.dal.entity.WechatCpAddFriendPO;
import com.yiwise.core.helper.objectstorage.AddOssPrefixSerializer;
import com.yiwise.core.model.bo.calldetail.CallDetailSimpleInfoBO;
import com.yiwise.core.model.bo.callrecord.CallRecordWithReadStatusBO;
import com.yiwise.core.model.bo.customertag.CustomerTagDetailBO;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.customer.data.platform.rpc.api.service.enums.CreateSourceTypeEnum;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * 用来导出通话记录的。导出的通话记录包含通话记录表原本内容+客户+一连串的通话文本
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CallRecordExportDTO extends CallRecordWithReadStatusBO implements ExcelRowModel {

    protected static ExcelRowModelResolver resolver = new ExcelRowModelResolver();

    /**
     * 任务名称
     */
    private String robotCallJobName;

    /**
     * BOT名称
     */
    private String dialogFlowName;

    private AccountVO accountVO;

    private List<CallDetailSimpleInfoBO> callDetailList;

    /**
     * 主叫号码
     */
    private String tenantPhoneNumber;

    /**
     * 线路名称
     */
    private String phoneName;

    /**
     * 线路类型
     */
    private PhoneTypeEnum phoneType;

    /**
     * 加密id
     */
    private String encryptedId;

    /**
     * 是否在白名单中
     */
    private Boolean inWhiteList;

    /**
     * AI分类
     */
    private String aiIntentLevelName;

    /**
     * 人工分类
     */
    private String realIntentLevelName;

    private int totalIntentLevelTagSize;

    /**
     * 微信推送人
     */
    private String wechatPushUserName;

    /**
     * 人工坐席组
     */
    private String csStaffGroupName;

    /**
     * 人工坐席
     */
    private String csStaffName;

    private Map<Long, List<String>> extraInfo;

    protected Map<String, List<String>> extraNameInfo;

    /**
     * 负责人姓名
     */
    private String followUserName;

    /**
     * 自动重播次数
     */
    private Integer redialTimes;

    /**
     * 失效时间
     */
    private Integer expires;

    /**
     * 是否提醒
     */
    private boolean remind;

    /**
     * 备用号码
     */
    protected Set<String> alternatePhoneNumbers;

    /**
     * 号码归属地
     */
    private String location;

    /**
     * 发送微信好友状态
     */
    private WechatCpAddFriendPO wechatCpAddFriendPO;

    /**
     * 客户标签
     */
    private List<CustomerTagDetailBO> callRecordTagDetails;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = resolver.getRowModelMap(extraNameInfo);
        resolver.setAlterPhoneNumber(retMap, alternatePhoneNumbers);

        if (accountVO != null) {
            retMap.put(来源平台, CollectionUtils.isNotEmpty(accountVO.getCreateSources()) ? accountVO.getCreateSources().stream().map(CreateSourceTypeEnum::getDesc).collect(Collectors.toList()) : "");
            retMap.put(性别, Objects.nonNull(accountVO.getGender()) ? accountVO.getGender().getDesc() : "");
            retMap.put(创建时间, accountVO.getCreateTime());
        }
        retMap.put(客户名, getCustomerPersonName());
        retMap.put(通话记录ID, getCallRecordId());
        retMap.put(联系电话, getCalledPhoneNumber());
        retMap.put(任务名称, robotCallJobName);
        retMap.put(BOT名称, dialogFlowName);
        retMap.put(呼叫时间, getStartTime());
        retMap.put(振铃时长无单位, getRingDuration());
        retMap.put(通话时长, getChatDuration());
        retMap.put(通话状态, getResultStatus());
        retMap.put(客户意向, getRealIntentLevelName());
        retMap.put(AI分类, getAiIntentLevelName());
        retMap.put(人工分类, Boolean.TRUE.equals(getManualMarked()) ? getRealIntentLevelName() : "");
        retMap.put(客户关注点, getCustomerConcern());
        retMap.put(对话详情, getCallDetailList(callDetailList));
        retMap.put(客户属性, getAttributes());
        retMap.put(自定义变量, getProperties());
        retMap.put(动态变量, getDynamicVariables());
        retMap.put(主叫号码, getTenantPhoneNumber());
        retMap.put(微信推送人, getWechatPushUserName());
        retMap.put(微信推送时间, getWechatPushTime());
        retMap.put(挂断状态, getHangupBy() == null ? null : getHangupBy().getDesc());
        retMap.put(转接人工, getTransferType() == null ? null : getTransferType().getDesc());
        retMap.put(请求人工介入, getCsTransferNotify() == null ? null : getCsTransferNotify().getDesc());
        retMap.put(是否监听, getCsMonitorFlag() != null && getCsMonitorFlag() == 1 ? "是" : "否");
        retMap.put(是否切入, getCsTransferAccept() != null && getCsTransferAccept() == 1 ? "是" : "否");
        if(StringUtils.isNotEmpty(getCsStaffGroupName()) && StringUtils.isNotEmpty(getCsStaffName())) {
            retMap.put(介入坐席, getCsStaffName() + "(" + getCsStaffGroupName() + ")");
        }
        retMap.put(对话轮次, getChatRound());
        retMap.put(呼叫次数, getCallCount());
        retMap.put(转接坐席, getTransferPhoneNumber());
        retMap.put(自动重拨次数, getRedialTimes());
        retMap.put(号码归属地,getLocation() == null ? "未知" : getLocation());
        retMap.put(申请加微账号, Objects.isNull(getAddWechatFriendAccountName()) ? "" : getAddWechatFriendAccountName());
	    Integer addWechatFriendStatus = getAddWechatFriendStatus();
	    WechatCpAddFriendPO wechatCpAddFriendPO = getWechatCpAddFriendPO();
	    String addWechatErrorMsg = wechatCpAddFriendPO == null ? "" : wechatCpAddFriendPO.getErrorMessage();
	    retMap.put(加微申请状态, Objects.isNull(addWechatFriendStatus) ? "" : getAddFriendStatus(addWechatFriendStatus, addWechatErrorMsg));
        retMap.put(加微申请时间, Objects.isNull(getAddWechatFriendSendTime()) ? "" : getAddWechatFriendSendTime());
        retMap.put(加微状态, Objects.isNull(addWechatFriendStatus) ? "" : getAddFriendStatus2(addWechatFriendStatus));
        retMap.put(申请通过时间, Objects.isNull(getAddWechatFriendAdoptTime()) ? "" : (1== addWechatFriendStatus) ? getAddWechatFriendAdoptTime() : "");
        retMap.put(通话标签, getCustomerLevelTag());
        retMap.put(首次导入, getImportTime());
        retMap.put(性别识别,getRecognizeGender().getDesc());
        retMap.put(录音文件地址, AddOssPrefixSerializer.getAddOssPrefixUrl(getFullAudioUrl()));
        retMap.put(人工介入状态, getInterceptStatus() != null ? getInterceptStatus().getDesc() : "");
        return retMap;
    }

    /**
     * 加微申请状态
     */
    protected String getAddFriendStatus(Integer status, String message) {
        String statusName;
        switch (status) {
            case -1:
                statusName = "发送失败("+message+")"; break;
            case 0:
            case 1:
	        case 5:
            case 6:
                statusName = "发送成功"; break;
            case 2:
                statusName = "已为好友"; break;
            case 3:
                statusName = "等待发送"; break;
            case 4:
                statusName = "发送中"; break;
	        default:
		        statusName = "";
        }
        return statusName;
    }

	/**
	 * 加微状态
	 */
	protected String getAddFriendStatus2(Integer status) {
		String statusName;
		switch (status) {
			case 0:
				statusName = "待通过"; break;
			case 1:
				statusName = "申请通过"; break;
			case 5:
				statusName = "请求已过期"; break;
            case 6:
                statusName = "发送申请后通过其他方式加微"; break;
			default:
				statusName = "";
		}
		return statusName;
    }

    protected String getCallDetailList(List<CallDetailSimpleInfoBO> callDetailList) {
        if (CollectionUtils.isEmpty(callDetailList)) {
            return Strings.EMPTY;
        }

        StringBuilder sb = new StringBuilder();
        for (CallDetailSimpleInfoBO callDetail : callDetailList) {
            if (sb.length() > 0) {
                sb.append("\r\n");
            }
            sb.append(callDetail.getType().getDesc()).append("：").append(callDetail.getText());
        }
        return sb.toString();
    }

    protected String getCustomerLevelTag() {
        if (CollectionUtils.isNotEmpty(callRecordTagDetails)) {
            List<String> collect = callRecordTagDetails.stream().map(CustomerTagDetailBO::getCustomerTagDetailName).collect(Collectors.toList());
            return StringUtils.join(collect, ",");
        } else {
            return "";
        }
    }
}
