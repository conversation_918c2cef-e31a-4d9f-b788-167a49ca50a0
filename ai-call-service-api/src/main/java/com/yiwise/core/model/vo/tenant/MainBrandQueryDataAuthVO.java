package com.yiwise.core.model.vo.tenant;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Data
public class MainBrandQueryDataAuthVO extends AbstractQueryVO implements Serializable {

    private Long currentUserId;

    private Long tenantId;

    /**
     * 数据权限
     */
    private Set<Long> filterUserIdSet;
}
