package com.yiwise.core.model.enums.handler.base;

import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.core.dal.entity.TenantPO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class ReceptionConfigureToJsonTypeHandler extends BaseTypeHandler<TenantPO.ReceptionConfigure> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, TenantPO.ReceptionConfigure parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtils.object2String(parameter));
    }


    @Override
    public TenantPO.ReceptionConfigure getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String rsString = rs.getString(columnName);
        if (StringUtils.isNotEmpty(rsString)) {
            return JsonUtils.string2Object(rsString, TenantPO.ReceptionConfigure.class);
        }
        return null;
    }

    @Override
    public TenantPO.ReceptionConfigure getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String rsString = rs.getString(columnIndex);
        if (StringUtils.isNotEmpty(rsString)) {
            return JsonUtils.string2Object(rsString, TenantPO.ReceptionConfigure.class);
        }
        return null;
    }

    @Override
    public TenantPO.ReceptionConfigure getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String csString = cs.getString(columnIndex);
        if (StringUtils.isNotEmpty(csString)) {
            return JsonUtils.string2Object(csString, TenantPO.ReceptionConfigure.class);
        }
        return null;
    }

}
