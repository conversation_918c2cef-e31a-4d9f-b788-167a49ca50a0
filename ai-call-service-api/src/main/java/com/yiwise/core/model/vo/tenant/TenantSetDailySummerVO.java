package com.yiwise.core.model.vo.tenant;

import com.yiwise.core.model.enums.handler.base.StringListToJsonTypeHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>@yiwise.com
 * @date: 2022 11 17 11:45
 */
@Data
public class TenantSetDailySummerVO implements Serializable {
    /**
     * 外呼日报小结 配置
     */
    @ColumnType(typeHandler = StringListToJsonTypeHandler.class)
    private List<String> dailySummary;

    private Long tenantId;
    private Long userId;
}
