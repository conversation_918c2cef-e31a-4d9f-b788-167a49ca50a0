package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

public enum RechargeRecordCustomerTypeEnum implements CodeDescEnum {

    ALL(0, "全部客户"),
    DIRECT_CUSTOMER(1, "直销客户"),
    DISTRIBUTOR_CUSTOMER(2, "代理商客户"),
    SUB_DISTRIBUTOR_CUSTOMER(3, "二级代理商客户"),
    DISTRIBUTOR(4, "代理商"),
    SUB_DISTRIBUTOR(5, "二级代理商"),
    FIRST_DISTRIBUTOR(6, "一级代理商"),
    ;
    private Integer value;
    private String name;
    RechargeRecordCustomerTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public String getDesc() {
        return name;
    }

    @Override
    public Integer getCode() {
        return value;
    }
}
