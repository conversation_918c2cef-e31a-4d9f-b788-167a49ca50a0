package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2018/11/27
 **/
@Getter
public enum RobotCallJobHangUpTypeEnum implements CodeDescEnum {
    ACCOUNT_DEBT(0, "账户欠费", "使用的线路账户已欠费"),
    NO_ROBOT_AVAILABLE(1, "没有可用坐席", "当前没有可用坐席"),
    PHONE_UNBIND(2, "线路已解绑", "使用的线路已解绑"),
    LINE_BREAKDOWN(3, "线路故障", "使用的线路状态均为故障"),
    AVAILABLE_ROBOT_NOT_ENOUGH(4,"有效坐席数不足","有效坐席数不足，请检查有效AI坐席数量！"),
    PREEMPTION_BY_HIGH_PRIORITY_JOB(5, "并发被优先任务占用"),
    BOT_UNBOUND(6, "任务使用的话术已解绑，请检查"),
    TTS_UNBOUND(7, "任务使用的话术关联的音色已解绑，请检查"),
    NO_AUTHENTICATION(8, "未进行资质认证"),
    ASR_UNBOUND(9, "任务使用的话术关联的ASR已解绑，请检查"),
    LINE_NOT_SUPPORT(10, "任务使用的线路均不支持AI外呼"),
    TOTAL_ACCOUNT_DEBT(11, "账户余额不足"),
    WECHAT_ADD_FRIEND_TYPE_UPDATE(12, "微信加好友类型已经修改"),
    LINE_CONCURRENCY_NOT_ENOUGH(13, "线路并发数不足"),
    SMS_DEBT(14, "余额不足，无法发送意向短信"),
    INDUSTRY_NOT_MATCH(15, "线路外呼行业限制"),
    SCRM_RELATION_INVALID(16, "SCRM关联关系失效", "未关联SCRM企业，无法推送加微数据"),
	SCRM_ACCOUNTS_UNAVAILABLE(17, "所有加微账号均不可用", "所有加微账号均不可用，具体原因可参见任务详情"),
    SMS_JOB_ARCHIVED(18, "短信自动群发任务已归档", "自动群发所选短信任务已归档，请重选"),
    SMS_TEMPLATE_DISABLE(19, "任务使用的短信模板已删除"),
    CALLBACK_CHECK_ERROR(20, "回调巡检异常"),
    UNKNOWN(21, "未知"),
    ;


	private final Integer code;
	private final String desc;
    private final String msg;

    RobotCallJobHangUpTypeEnum(Integer code, String desc) {
        this(code, desc, desc);
    }

    RobotCallJobHangUpTypeEnum(Integer code, String desc, String msg) {
        this.code = code;
        this.desc = desc;
        this.msg = msg;
    }
}
