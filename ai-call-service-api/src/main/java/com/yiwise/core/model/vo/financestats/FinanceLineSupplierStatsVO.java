package com.yiwise.core.model.vo.financestats;

import lombok.AccessLevel;
import lombok.Data;
import java.io.Serializable;
import lombok.experimental.FieldDefaults;

/**
 * 线路供应商 线路消耗列表
 * <AUTHOR>
 * @Date 2019/2/15 11:23 AM
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FinanceLineSupplierStatsVO extends FinanceLineStatsVO implements Serializable {
    /**
     * voip
     */
    String sipAccount;
    /**
     * 成本
     */
    Long costPrice;
    /**
     * 消耗 成本*时长  单独这一条记录
     */
    Double allCost;
    /**
     * 所有的消耗相加
     */
    Double allPartCost;
}
