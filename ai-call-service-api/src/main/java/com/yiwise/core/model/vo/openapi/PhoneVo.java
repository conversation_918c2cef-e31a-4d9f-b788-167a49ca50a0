package com.yiwise.core.model.vo.openapi;

import com.yiwise.core.dal.entity.AreaPO;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.phonenumber.CallOutIndustryEnum;
import com.yiwise.core.model.enums.phonenumber.LineStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PhoneVo implements Serializable {

    /**
     * BIGINT(19) 必填
     * 自增ID
     */
    private Long tenantPhoneNumberId;
    /**
     * VARCHAR(12) 必填
     * 手机号
     */
    private String phoneNumber;
    /**
     * VARCHAR(11) 默认值[]
     * 手机名称
     */
    private String phoneName;
    /**
     * TINYINT(3) 默认值[0] 必填
     * 电话卡类型 0:机器人手机卡 1:坐席手机卡
     */
    private PhoneTypeEnum phoneType;
    /**
     * 本地话费费率
     */
    private Long localBillRate;
    /**
     * 外地话费费率
     */
    @Deprecated
    private Long otherBillRate;
    /**
     * 外呼行业 0 金融 1 其它
     */
    private CallOutIndustryEnum callOutIndustry;

    /**
     * 号码归属地区号
     */
    private String areaCode;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 不可拨打地区
     */
    private List<AreaPO> deadArea;
    /**
     * 归属者
     */
    private String owner;

    private String lineIp;

    private Long phoneNumberId;

    private LineStatusEnum status;
}
