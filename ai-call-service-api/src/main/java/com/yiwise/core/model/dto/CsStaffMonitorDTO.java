package com.yiwise.core.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-12-21 19:04
 */
@Data
public class CsStaffMonitorDTO implements Serializable {

    //预测试外呼振铃摘机时长
    private Long csRingDuration = 0L;

    //接待振铃摘机时长
    private Long receptionRingDuration = 0L;

    //X秒内接通数
    private Long xSecondCount = 0L;

    //预测试外呼的总接待量 （包含接起的和没接的）
    private Long csTotalReceptionCount = 0L;

    //预测试外呼接听通数
    private Long csAnsweredReceptionCount = 0L;
}
