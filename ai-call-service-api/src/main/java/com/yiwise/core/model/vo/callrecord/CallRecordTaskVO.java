package com.yiwise.core.model.vo.callrecord;

import com.yiwise.core.model.enums.DialStatusEnum;
import com.yiwise.core.model.enums.IntentLevelEnum;
import com.yiwise.core.model.enums.handler.DialStatusEnumHandler;
import com.yiwise.core.model.enums.handler.IntentLevelEnumHandler;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;


public class CallRecordTaskVO implements Serializable {
    //通话ID
    private Long callRecordId;

    //record 客户意向
    @ColumnType(typeHandler = IntentLevelEnumHandler.class)
    private IntentLevelEnum intentLevel;
    @ColumnType(typeHandler = IntentLevelEnumHandler.class)
    private IntentLevelEnum realIntentLevel;

    //robot call job 任务名称
    private String robotCallJobName;

    //客户通话状态
    @ColumnType(typeHandler = DialStatusEnumHandler.class)
    private DialStatusEnum resultStatus;

    public Long getCallRecordId() {
        return callRecordId;
    }

    public void setCallRecordId(Long callRecordId) {
        this.callRecordId = callRecordId;
    }

    public IntentLevelEnum getIntentLevel() {
        if(StringUtils.isEmpty(realIntentLevel)) return intentLevel;
        else return realIntentLevel;
    }

    public void setIntentLevel(IntentLevelEnum intentLevel) {
        this.intentLevel = intentLevel;
    }

    public void setRealIntentLevel(IntentLevelEnum realIntentLevel) {
        this.realIntentLevel = realIntentLevel;
    }

    public String getRobotCallJobName() {
        return robotCallJobName;
    }

    public void setRobotCallJobName(String robotCallJobName) {
        this.robotCallJobName = robotCallJobName;
    }

    public DialStatusEnum getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(DialStatusEnum resultStatus) {
        this.resultStatus = resultStatus;
    }


}
