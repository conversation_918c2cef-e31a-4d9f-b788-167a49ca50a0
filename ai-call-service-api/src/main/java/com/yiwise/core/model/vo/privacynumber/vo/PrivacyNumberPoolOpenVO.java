package com.yiwise.core.model.vo.privacynumber.vo;

import com.yiwise.core.model.enums.handler.PrivacyNumberPoolAllocationModeEnumHandler;
import com.yiwise.core.model.enums.handler.PrivacyNumberTypeEnumHandler;
import com.yiwise.core.model.enums.privacynumber.PrivacyNumberPoolAllocationModeEnum;
import com.yiwise.core.model.enums.privacynumber.PrivacyNumberTypeEnum;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * OpenApi返回的对象
 * <AUTHOR>
 * @create 2021/09/07
 */
@Data
public class PrivacyNumberPoolOpenVO {

	private String name;

	private String poolKey;

	@ColumnType(typeHandler = PrivacyNumberTypeEnumHandler.class)
	private PrivacyNumberTypeEnum numberType;

	@ColumnType(typeHandler = PrivacyNumberPoolAllocationModeEnumHandler.class)
	private PrivacyNumberPoolAllocationModeEnum allocationMode;
}
