package com.yiwise.core.model.vo.mikangyun;

import com.github.pagehelper.Page;
import com.yiwise.base.model.bean.PageResultObject;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/6/15
 * @class <code>MkyPageResVO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class MkyPageResVO<T> extends PageResultObject<T> {
    private int validNum;

    public static <T> PageResultObject<T> of(List<T> list, int pageNum, int pageSize, int totalCount, int validNum) {
        MkyPageResVO<T> pageResVO = new MkyPageResVO<>();
        pageResVO.setValidNum(validNum);
        pageResVO.setContent(list);
        if (list instanceof Page) {
            Page page = (Page) list;
            pageResVO.setNumber(page.getPageNum());
            pageResVO.setPageSize(page.getPageSize());
            pageResVO.setPages(page.getPages());
            pageResVO.setTotalElements(page.getTotal());
        } else if (!CollectionUtils.isEmpty(list)) {
            pageResVO.setPageSize(pageSize);
            pageResVO.setNumber(pageNum);
            pageResVO.setTotalElements((long) totalCount);
            if (totalCount % pageSize == 0) {
                pageResVO.setPages(Math.floorDiv(totalCount, pageSize));
            } else {
                pageResVO.setPages(Math.floorDiv(totalCount, pageSize) + 1);
            }
        }

        return pageResVO;
    }
}
