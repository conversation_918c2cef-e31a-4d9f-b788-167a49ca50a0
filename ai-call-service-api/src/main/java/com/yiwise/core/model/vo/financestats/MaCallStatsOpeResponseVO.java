package com.yiwise.core.model.vo.financestats;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.model.enums.CallSmsAccountEnum;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;


@Data
public class MaCallStatsOpeResponseVO implements ExcelRowModel {

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private LocalDate date;

	@JsonIgnore
	private Integer year;
	@JsonIgnore
	private Integer month;
	@JsonIgnore
	private Integer day;

	@JsonIgnore
	private Long tenantId;

	private String tenantName;

	/** 外呼数 */
	private Long totalCompleted;

	/** 接通数 */
	private Long totalAnsweredCall;

	/**
	 * 平均通话时长
	 */
	private Double averageCallTime = 0d;

	/** 计费分钟数 */
	private Long billChatDuration;

	/**
	 * 总通话时长
	 */
	private Long totalChatDuration;

	/**
	 * 计费接通数
	 */
	private Long billingAnsweredCall = 0L;

	/**
	 * 计费单价
	 */
	private String billingUnitPrice = "";

	/** 费用消耗(元) */
	private Double callCost;

	/**
	 * PIECE 按接通付费
	 * MINUTE 按分钟付费
	 * LOCAL 订阅制本地
	 * LONG 订阅制外地
	 */
	private String payType;

	private String maFare;

	/**
	 * 振铃计费通数
	 */
	private Long billRingCount = 0L;

	/**
	 * 振铃计费单价
	 */
	private Double billingRingUnitPrice = 0d;

	@Override
	public Map<String, Object> getRowModelMap() {
		Map<String, Object> map = new HashMap<>();
		map.put(时间, date);
		map.put(客户名称, tenantName);
		map.put(外呼数, totalCompleted);
		map.put(接通数, totalAnsweredCall);
		map.put(平均通话时长, averageCallTime);
		map.put(计费分钟数, billChatDuration);
		map.put(计费接通数, billingAnsweredCall);
		map.put(计费单价, billingUnitPrice);
		map.put(费用消耗, callCost);
		return map;
	}
}
