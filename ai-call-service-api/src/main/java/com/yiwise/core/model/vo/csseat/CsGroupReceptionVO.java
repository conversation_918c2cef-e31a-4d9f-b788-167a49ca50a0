package com.yiwise.core.model.vo.csseat;

import lombok.AccessLevel;
import lombok.Data;
import java.io.Serializable;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/14 4:53 PM
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CsGroupReceptionVO implements Serializable {
    Long csGroupId;
    String csGroupName;
    String dialogFlowName;
    // 转人工接待坐席组
    CsGroupReceptionVO transferCsGroup;
    // 人工坐席组中在线客服名称
    List<String> onlineStaffNameList;
    // 人工坐席组中离线客服名称
    List<String> offlineStaffNameList;
}
