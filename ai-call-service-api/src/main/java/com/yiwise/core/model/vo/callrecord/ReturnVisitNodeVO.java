package com.yiwise.core.model.vo.callrecord;

import com.yiwise.core.model.enums.dialogflow.IntentBranchCategoryEnum;
import com.yiwise.core.model.enums.robotcalljob.ReturnVisitNodeTypeEnum;
import javaslang.Tuple3;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import java.io.Serializable;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @description 回访vo单个流程的节点信息
 * @create 2019/07/12
 **/
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ReturnVisitNodeVO implements Serializable {

    /**
     * 节点名
     */
    String name;

    /**
     * 节点类型
     */
    ReturnVisitNodeTypeEnum type;

    /**
     * 如果为节点类型为IntentBranch的话, 分支属性
     */
    IntentBranchCategoryEnum intentBranchCategory;
}
