package com.yiwise.core.model.vo.organization;

import com.yiwise.core.dal.entity.OrganizationPO;
import com.yiwise.core.model.dto.OrganizationDTO;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrganizationVO extends OrganizationPO implements Serializable {
    private OrganizationDTO parent;
    private String manager;

}
