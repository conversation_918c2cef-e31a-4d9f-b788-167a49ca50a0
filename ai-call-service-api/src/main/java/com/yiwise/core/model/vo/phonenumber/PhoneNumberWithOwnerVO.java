package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.dal.entity.PhoneNumberPO;
import com.yiwise.core.model.enums.CompanyEnum;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 电话卡po+owner
 * @create 2018/11/14
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class PhoneNumberWithOwnerVO extends PhoneNumberPO implements Serializable {

    private String owner;

    private CompanyEnum ownerType;

    // 根据不同的companyType, 这个id表示 distributorId 或 tenantId
    private Long ownerId;

}
