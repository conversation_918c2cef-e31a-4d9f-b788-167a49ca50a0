package com.yiwise.core.model.vo.sharewhitelist;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ShareWhiteListVO
 * <AUTHOR>
 * @Date 2019/3/27
 **/
@Data
public class ShareWhiteListVO implements Serializable {
    /**
     * 手机号集合
     */
    private String[] phoneList;

    /**
     * 共享组id
     */
    @NotNull(message = "共享组id不能为空！")
    private Long policyId;

    /**
     * 单个黑名单手机号
     */
    private String phone;

    private Long tenantId;

    private Long currentUserId;

    /**
     * 批量导入文件路径
     */
    private String objectName;
}
