package com.yiwise.core.model.vo.rechargestream;

import com.yiwise.core.model.enums.RechargeStreamHandleTypeEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import java.io.Serializable;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/22 2:06 PM
 **/
@Data
public class DistributorRechargeStreamQueryVO extends AbstractQueryVO implements Serializable {
    Long distributorId;
    Long phoneNumberId;
    String startDate;
    String endDate;
    List<RechargeStreamHandleTypeEnum> enums;
    Long currentLoginUserId;
    Boolean withoutPageCount;
}
