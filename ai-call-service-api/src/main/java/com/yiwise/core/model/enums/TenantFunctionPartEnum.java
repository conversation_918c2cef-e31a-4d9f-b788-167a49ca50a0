package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.dal.entity.RobotStasPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.enums.authority.AiccModuleEnum;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@ToString
public enum TenantFunctionPartEnum implements CodeDescEnum {
	crm_out_call_platform(0, "呼叫中心", null, null),
	NEW_CALL_OUT_PLATFORM(9, "呼叫中心", null, "intelligent_out_call_platform_system_status"),
	scrm(1, "私域加粉", ((tenant, map) -> !AiccModuleEnum.SCRM.getFunction().apply(tenant, map)), "scrm_system_status"),
	sms(2, "短信平台", (tenant, map) -> true, "sms_center_status"),
	ma(3, "自动化营销", (tenant, map) -> tenant.getEnableMaControl(), null),
	member_center(4, "会员中心", (tenant, map) -> tenant.getEnableMemberControl(), null),
	sale_crm(8, "销售CRM模块", (tenant, map) -> tenant.getEnableCrmControl(), null),
	risk_management(5, "风控管理", (tenant, map) -> true, "aicc_rcs%"),
	ai_engine(6, "AI引擎", ((tenant, map) -> !AiccModuleEnum.AiEngine.getFunction().apply(tenant, map)), "ai_engine_status"),
	manual_outbound_call(7, "人工外呼", (tenant, map) -> Objects.nonNull(tenant.getEnableVideoCallOut()) && tenant.getEnableMikangyun(), null),
	VIDEO_CALL_OUT_PLATFORM(10, "视频外呼", (tenant, map) -> Objects.nonNull(tenant.getEnableVideoCallOut()) && tenant.getEnableVideoCallOut(), null),
    volcCdp(11, "抖店营销", (tenant, map) -> true, null),

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;
	/**
	 * 客户有该模块权限, 返回true
	 */
	private BiFunction<TenantPO, Map<TenantAiccPartEnum, RobotStasPO>, Boolean> function;
	/**
	 * 对应的角色权限
	 */
	private final String resourceUri;

	static {
		// 呼叫中心的展示判断逻辑, 有任意一个子模块开通则认为呼叫中心已开通
		crm_out_call_platform.function = (tenant, map) -> {
			Boolean callOut = !AiccModuleEnum.CallOut.getFunction().apply(tenant, map);
			Boolean privacyNumber = !AiccModuleEnum.PrivacyNumber.getFunction().apply(tenant, map);
			Boolean qcJob = !AiccModuleEnum.QcJob.getFunction().apply(tenant, map);
			Boolean clue = tenant.getEnableClue();
			Boolean csConsole = !AiccModuleEnum.CsConsole.getFunction().apply(tenant, map);
			Boolean callInReception = !AiccModuleEnum.CallInRecp.getFunction().apply(tenant, map);
			Boolean oldTenant = BooleanUtils.isTrue(tenant.getEnableOldCallOut());
			return oldTenant && (callOut || privacyNumber || qcJob || clue || csConsole || callInReception);
		};
		// 新版呼叫中心的展示判断逻辑, 有任意一个子模块开通则认为呼叫中心已开通
		NEW_CALL_OUT_PLATFORM.function = (tenant, map) -> {
			Boolean callOut = !AiccModuleEnum.CallOutNew.getFunction().apply(tenant, map);
			Boolean privacyNumber = !AiccModuleEnum.PrivacyNumber.getFunction().apply(tenant, map);
			Boolean qcJob = !AiccModuleEnum.QcJob.getFunction().apply(tenant, map);
			Boolean clue = tenant.getEnableClue();
			Boolean csConsole = !AiccModuleEnum.CsConsole.getFunction().apply(tenant, map);
			Boolean callInReception = !AiccModuleEnum.CallInRecp.getFunction().apply(tenant, map);
			Boolean newTenant = BooleanUtils.isTrue(tenant.getEnableNewCallOut());
			return newTenant && (callOut || privacyNumber || qcJob || clue || csConsole || callInReception);
		};
	}

    TenantFunctionPartEnum(Integer code, String desc, BiFunction<TenantPO, Map<TenantAiccPartEnum, RobotStasPO>, Boolean> function, String resourceUri) {
        this.code = code;
        this.desc = desc;
        this.function = function;
        this.resourceUri = resourceUri;
    }

	/**
	 * 根据tenant的信息, 返回指定客户不需要展示的模块
	 */
	public static Set<TenantFunctionPartEnum> getDisabledPart(TenantPO tenantPO, Map<TenantAiccPartEnum, RobotStasPO> map, Collection<String> resourceUris) {
		if (tenantPO == null || map == null) {
			return Arrays.stream(TenantFunctionPartEnum.values()).collect(Collectors.toSet());
		}
		Predicate<TenantFunctionPartEnum> filterByTenant = module -> !module.function.apply(tenantPO, map);
		//增加可以模糊查询的功能，有些模块没有开关功能，但是只要勾选了权限点就需要打开
		Predicate<TenantFunctionPartEnum> filterByAuth = module -> module.resourceUri != null && (!(resourceUris.contains(module.resourceUri)
		|| (module.resourceUri != null && module.resourceUri.contains("%") && resourceUris.stream().filter( x -> x.contains(module.resourceUri.replace("%", ""))).findFirst().isPresent())
		));
		return Arrays.stream(TenantFunctionPartEnum.values())
				.filter(filterByTenant.or(filterByAuth))
				.collect(Collectors.toSet());
	}
}
