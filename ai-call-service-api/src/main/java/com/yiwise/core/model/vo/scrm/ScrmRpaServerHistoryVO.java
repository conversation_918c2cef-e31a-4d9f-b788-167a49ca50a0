package com.yiwise.core.model.vo.scrm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: wangguomin
 * @Date: 2022/11/30 10:30
 */
@Data
public class ScrmRpaServerHistoryVO {
    /**
     * 自增id
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    private Long scrmTenantRelationHistoryId;

    /**
     * 租户id
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    private Long tenantId;

    /**
     *
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    private String ip;

    /**
     * 服务开始时间
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime startTime;

    /**
     * 服务结束时间
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime endTime;

    /**
     * 总数
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    private Integer totalCount;

    /**
     * 0正式,1试用,3试用转正式
     *
     * @mbg.generated Tue Jul 20 10:15:27 CST 2021
     */
    private String accountType;

    /**
     * OVERDUE:过期，DEL：删除
     */
    private String operationType;

    private Long userId;

    /**
     * 操作时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime createTime;
    /**
     * 操作人
     */
    private String createUserName;
}
