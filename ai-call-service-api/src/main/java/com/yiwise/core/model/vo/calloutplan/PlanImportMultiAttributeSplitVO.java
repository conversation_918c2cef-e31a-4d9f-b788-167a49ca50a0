package com.yiwise.core.model.vo.calloutplan;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yiwise.core.serializer.MultiAttributeMapJsonSerializer;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * 多属性模式导入字段拆分对象
 * <AUTHOR>
 * @create 2022/08/30
 */
@Data
public class PlanImportMultiAttributeSplitVO {

	@JsonSerialize(using = MultiAttributeMapJsonSerializer.class)
	private Map<String, Set<String>> attributes = new LinkedHashMap<>();
	private Map<String, String> urls;

	public void addAttribute(String key, String value) {
		attributes.computeIfAbsent(key, k -> new LinkedHashSet<>()).add(value);
	}
}
