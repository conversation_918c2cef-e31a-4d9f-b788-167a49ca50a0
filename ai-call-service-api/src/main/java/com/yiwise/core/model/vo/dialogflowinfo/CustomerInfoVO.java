package com.yiwise.core.model.vo.dialogflowinfo;

import com.yiwise.core.model.enums.CustomerTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * Created on 2019-01-02.
 *
 * <AUTHOR>
 * email <EMAIL>
 * description 话术筛选的客户类型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CustomerInfoVO implements Serializable {
    /**
     * 筛选框显示的名称
     */
    private String name;

    /**
     * 客户的id
     */
    private Long customerId;

    /**
     * 客户的类型
     */
    private CustomerTypeEnum customerType;
}
