package com.yiwise.core.model.vo.calldetail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2020/08/18
 */
@Data
public class CallMonitorJobVO implements Serializable {

    private static final long serialVersionUID = -170642670651804834L;

    Long jobId;
    String jobName;
    Integer taskCount;
    @JsonIgnore
    private LocalDateTime createTime;

    private CallMonitorJobVO() {}

    public CallMonitorJobVO(Long jobId, String jobName, LocalDateTime createTime) {
        this();
        this.jobId = jobId;
        this.jobName = jobName;
        this.createTime = createTime;
        taskCount = 0;
    }
}
