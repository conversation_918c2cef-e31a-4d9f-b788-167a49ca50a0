package com.yiwise.core.model.vo;


import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmsTestParams extends AbstractQueryVO implements Serializable {
    private Long userId;

    private Long smsPlatformChannelId;

    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;


}
