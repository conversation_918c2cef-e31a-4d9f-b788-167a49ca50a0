package com.yiwise.core.model.vo.tenantchurnwarn;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.model.enums.CustomerIndustryEnum;
import com.yiwise.core.model.enums.CustomerSubIndustryEnum;
import com.yiwise.core.model.enums.TenantAccountStatusEnum;
import com.yiwise.core.model.enums.TenantPayTypeEnum;
import com.yiwise.core.model.enums.handler.TenantPayTypeEnumHandler;
import com.yiwise.core.model.enums.handler.base.AccountTypeListToJsonTypeHandler;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.handler.TenantAccountStatusEnumHandler;
import com.yiwise.core.model.enums.tenantchurnwarn.TenantChurnWarnEnum;
import com.yiwise.core.model.enums.tenantchurnwarn.handler.TenantChurnWarnEnumHandler;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.annotation.ColumnType;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * @author: <EMAIL>
 * @date: 2022 08 31 15:47
 */
@Data
public class TenantChurnWarnListVO implements ExcelRowModel {

    private static final DateTimeFormatter yyyy_MM = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final String EMPTY_RESULT = "--";

    private LocalDate yearMonth;

    private Long tenantId;

    private String companyName;

    /**
     * 行业
     */
    private CustomerIndustryEnum customerIndustry;
    public String customerIndustryName;
    /**
     * 客户子行业
     */
    private CustomerSubIndustryEnum customerSubIndustry;
    private String customerSubIndustryName;
    /**
     * 客户赛道类型
     */
    private Integer customerTrackType;
    /**
     * 客户二级赛道类型
     */
    private Integer customerTrackSubType;
    /**
     * 一级赛道
     */
    private String customerTrackTypeName;
    /**
     * 二级赛道
     */
    private String customerTrackSubTypeName;
    /**
     * 客户等级
     */
    @ColumnType(typeHandler = TenantAccountStatusEnumHandler.class)
    private TenantAccountStatusEnum tenantAccountStatus;
    /**
     * 客户经理
     */
    private Long accountManagerId;
    private String accountManager;

    private Long csmUserId;
    private String csmUserName;
    /**
     * 实施负责人的姓名
     */
    private Long deploymentUserId;
    private String deploymentUser;

    /**
     * 账号类型
     */
    @ColumnType(typeHandler = AccountTypeListToJsonTypeHandler.class)
    private AccountTypeEnum accountType;

    /**
     * 客户付费类型
     */
    @ColumnType(typeHandler = TenantPayTypeEnumHandler.class)
    private TenantPayTypeEnum tenantPayType;

    /**
     * 上月 预警
     */
    @ColumnType(typeHandler = TenantChurnWarnEnumHandler.class)
    private List<TenantChurnWarnEnum> tenantChurnWarnType;

    /**
     * 近一年累计预警次数
     */
    private Integer lastYearWarnCount;

    /**
     * 累计消耗
     */
    private Long cumulativeCost;

    /**
     * 当月消耗
     */
    private Long thisMonthCost;

    private Long lastOneMonthCost;

    private Long lastTwoMonthCost;

    private Long lastThreeMonthCost;

    private Long lastFourMonthCost;

    private Long lastFiveMonthCost;


    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = new HashMap<>();
        map.put(公司名称, getCompanyName());
        String industry = EMPTY_RESULT;
        if (Objects.nonNull(getCustomerTrackType())) {
            industry = getCustomerTrackTypeName();
        }
        map.put(行业, industry);
        if (Objects.nonNull(getCustomerTrackSubType())) {
            if (StringUtils.isNotBlank(getCustomerTrackSubTypeName())) {
                map.put(细分赛道, getCustomerTrackSubTypeName());
            }
        }
        map.put(客户等级, getTenantAccountStatus());
        map.put(客户经理, getAccountManager());
        map.put(CSM负责人, getCsmUserName());
        map.put(实施负责人, getDeploymentUser());
        map.put(账号类型, getAccountType() != null ? getAccountType().getDesc() : null);
        map.put(付费类型, getTenantPayType() != null ? getTenantPayType().getDesc() : null);
        if (getYearMonth() != null) {
            LocalDate nowDate = LocalDate.now();
            boolean isThisMonth = true;
            // 判断 yearMonth 是否在当前月
            if (getYearMonth().getYear() == nowDate.getYear() && getYearMonth().getMonthValue() == nowDate.getMonthValue()) {
                isThisMonth = true;
            }else {
                isThisMonth = false;
            }
            String thisMonth = getYearMonth().format(yyyy_MM);
            String lastOneMonth = getYearMonth().minusMonths(1).format(yyyy_MM);
            String lastTwoMonth = getYearMonth().minusMonths(2).format(yyyy_MM);
            String lastThreeMonth = getYearMonth().minusMonths(3).format(yyyy_MM);
            String lastFourMonth = getYearMonth().minusMonths(4).format(yyyy_MM);
            String lastFiveMonth = getYearMonth().minusMonths(5).format(yyyy_MM);
            StringBuilder warnType = new StringBuilder();
            if (getTenantChurnWarnType() != null && getTenantChurnWarnType().size() > 0 ) {
                for (TenantChurnWarnEnum warnEnum : getTenantChurnWarnType()) {
                    warnType.append(warnEnum.getDesc()).append("\t");
                }
            }
            if (isThisMonth) {
                map.put(lastOneMonth + "月触发预警", warnType.toString());
            }else {
                map.put(thisMonth + "月触发预警", warnType.toString());
            }
            map.put(近1年累计预警次数, lastYearWarnCount);
            map.put(累计消耗, cumulativeCost == null ? null : cumulativeCost / 1000d);
            map.put(lastFiveMonth + "月月消耗", lastFiveMonthCost == null ? null : lastFiveMonthCost / 1000d);
            map.put(lastFourMonth + "月月消耗", lastFourMonthCost == null ? null : lastFourMonthCost / 1000d);
            map.put(lastThreeMonth + "月月消耗", lastThreeMonthCost == null ? null : lastThreeMonthCost / 1000d);
            map.put(lastTwoMonth + "月月消耗", lastTwoMonthCost == null ? null : lastTwoMonthCost / 1000d);
            map.put(lastOneMonth + "月月消耗", lastOneMonthCost == null ? null : lastOneMonthCost / 1000d);
            map.put(thisMonth + "月月消耗", thisMonthCost == null ? null : thisMonthCost / 1000d);
        }
        return map;
    }
}
