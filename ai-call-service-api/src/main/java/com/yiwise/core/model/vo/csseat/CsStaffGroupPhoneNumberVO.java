package com.yiwise.core.model.vo.csseat;

import com.yiwise.core.dal.entity.CsStaffGroupPhoneNumberPO;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.handler.PhoneTypeEnumHandler;
import lombok.Data;
import java.io.Serializable;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * @Author: wangguomin
 * @Date: 2019-03-01 11:17
 */
@Data
public class CsStaffGroupPhoneNumberVO extends CsStaffGroupPhoneNumberPO implements Serializable {
    String phoneName;
    String phoneNumber;

    @ColumnType(typeHandler = PhoneTypeEnumHandler.class)
    PhoneTypeEnum phoneType;

    private Integer dailyCallCount;
}
