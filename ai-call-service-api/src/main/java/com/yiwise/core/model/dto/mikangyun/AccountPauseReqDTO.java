package com.yiwise.core.model.dto.mikangyun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountPauseReqDTO {

    /**
     * 租户ID 更新时使用
     */
    @JsonProperty(value = "account_id")
    private Long accountId;

    /**
     * 租户状态
     * 2 暂停
     * 4 停止
     */
    private Integer status;
}
