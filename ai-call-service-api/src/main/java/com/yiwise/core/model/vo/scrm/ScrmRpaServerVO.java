package com.yiwise.core.model.vo.scrm;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wangguomin
 * @Date: 2022/11/30 10:30
 */
@Data
public class ScrmRpaServerVO {
    private Long tenantId;
    private Long scrmTenantId;
    /**
     * 编辑/延期/删除需要传
     */
    private Long scrmTenantRelationId;
    /**
     * ip地址
     *
     * @mbg.generated Wed Jan 20 15:59:59 CST 2021
     */
    private String ip;

    /**
     * 绑定多个
     */
    private List<String> ipList;

    /**
     * 企业微信最大登陆用户数
     *
     * @mbg.generated Wed Jan 20 15:59:59 CST 2021
     */
    private Integer totalCount;

    /**
     * 企业微信已登陆用户数
     *
     * @mbg.generated Wed Jan 20 15:59:59 CST 2021
     */
    private Integer usedCount;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime startTime;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime endTime;

    /**
     * 变更时间
     */
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime createTime;

    /**
     * 0正式1试用3试用转正式
     */
    private String accountType;

    private Long userId;

    /**
     * 在线个数
     */
    private Integer onlineCount = 0;
}
