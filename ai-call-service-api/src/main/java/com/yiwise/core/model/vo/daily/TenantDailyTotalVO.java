package com.yiwise.core.model.vo.daily;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.外呼日期;

/**
 * @author: wux<PERSON><PERSON><PERSON>@yiwise.com
 * @date: 2022 11 24 20:04
 */
@Data
public class TenantDailyTotalVO extends TenantDailyVO {

    private String summary;
    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = new HashMap<>();
        map.put(外呼日期, summary);
        return map;
    }
}
