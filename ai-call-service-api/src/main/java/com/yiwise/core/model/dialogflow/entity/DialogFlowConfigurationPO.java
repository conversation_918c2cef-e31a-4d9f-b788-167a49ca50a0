package com.yiwise.core.model.dialogflow.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.model.enums.dialogflow.RobotKnowledgeAnswerTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "dialogFlowConfiguration")
public class DialogFlowConfigurationPO implements Serializable {
    public final static String COLLECTION_NAME = "dialogFlowConfiguration";

    @Id @NotNull
    private Long dialogFlowId;
    private DialogFlowCollectionConfigRulePO collectionConfigRule;
    // 用户说话长度
    private List<DialogFlowUserSayConfigRulePO> dialogFlowUserSayConfigRuleList;
    // 多次拒绝
    private List<DialogFlowDeclineConfigRulePO> dialogFlowDeclineConfigRuleList;
    // 多次业务问题
    private List<DialogFlowBusinessQAConfigRulePO> dialogFlowBusinessQAConfigRuleList;
    //人工介入设置
    private DialogFlowHumanInterventionPO dialogFlowHumanIntervention;
    // 触发多次问题是否是同一问题
    private Boolean sameQuestion = false;

    /**
     * 用户无应答时间，默认7000ms
     */
    private Integer userSilenceMillisecond;

    private Boolean callNextImmediately;

    private Boolean enableCsControl;

    private Integer csControlTime;

    private Boolean enableBackgroundSound = false;

    private List<DialogFlowBackgroundSoundPO> backgroundSoundList;

    // 是否开启语气词打断
    private Boolean enableToneInterrupt;
    // 语气词打断百分比
    private Integer toneInterruptPercent;
    // 语气词列表
    private List<String> toneWordList;

    public Long getDialogFlowId() {
        return dialogFlowId;
    }

    public void setDialogFlowId(Long dialogFlowId) {
        this.dialogFlowId = dialogFlowId;
    }

    // 是否启用通话最长时间限制
    private Boolean enableCallTimeout;

    // 通话最长时间， 单位分钟
    private Integer callTimeoutMinutes;

    private Integer callTimeoutSeconds;

    private TextAudioContentPO callTimeoutHangupAnswer;

    /**
     * 是否启用连续静音时长限制
     */
    private Boolean enableContinuousMuteTimeout;


    /**
     * 连续静音时长，单位秒
     */
    private Integer continuousMuteTimeoutSeconds;

    /**
     * 连续静音超时挂机话术
     */
    private TextAudioContentPO continuousMuteHangupAnswer;

    /**
     * 开启延迟挂机
     */
    private Boolean enableDelayHangup;

    /**
     * 延迟挂机时间, 单位秒
     */
    private Double delayHangupSeconds;

    /**
     * 开启语气词承接
     */
    private Boolean enableModalParticle;

    /**
     * 语气词承接音频列表
     */
    private List<AudioUrlPO> modalParticleAudioUrlList;

    @JSONField(serialize = false)
    @JsonIgnore
    public List<TextAudioContentPO> getAllRuleTextAudio() {
        return getAllRuleTextAudioMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public Map<String, List<TextAudioContentPO>> getAllRuleTextAudioMap() {
        Map<String, List<TextAudioContentPO>> result = new HashMap<>();
        if(CollectionUtils.isNotEmpty(dialogFlowDeclineConfigRuleList)) {
            result.put("触发多次拒绝", dialogFlowDeclineConfigRuleList.stream().filter(item -> item.getTextAudioContent() != null).map(DialogFlowDeclineConfigRulePO::getTextAudioContent).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(dialogFlowBusinessQAConfigRuleList)) {
            result.put("触发业务问题", dialogFlowBusinessQAConfigRuleList.stream().filter(item -> item.getTextAudioContent() != null).map(DialogFlowBusinessQAConfigRulePO::getTextAudioContent).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(dialogFlowUserSayConfigRuleList)) {
            result.put("用户说话时长", dialogFlowUserSayConfigRuleList.stream().filter(item -> item.getTextAudioContent() != null).map(DialogFlowUserSayConfigRulePO::getTextAudioContent).collect(Collectors.toList()));
        }
        if (BooleanUtils.isTrue(enableCallTimeout) && Objects.nonNull(callTimeoutHangupAnswer)) {
            result.put("通话时长限制", Collections.singletonList(callTimeoutHangupAnswer));
        }
        if (BooleanUtils.isTrue(enableContinuousMuteTimeout) && Objects.nonNull(continuousMuteHangupAnswer)) {
            result.put("连续静音时长", Collections.singletonList(continuousMuteHangupAnswer));
        }
        return result;
    }

    public void fullAttrValidAndThrow(List<DialogFlowStepPO> dialogFlowStepList) {
        Set<String> knowledgeStepIdSet = dialogFlowStepList.stream().map(DialogFlowStepPO::getId).collect(Collectors.toSet());
        // 触发业务问题
        if (CollectionUtils.isNotEmpty(dialogFlowBusinessQAConfigRuleList)) {
            dialogFlowBusinessQAConfigRuleList.forEach(rule -> {
                validHangupRule("个性化配置-特殊挂机流程-触发多个业务问题", rule, knowledgeStepIdSet);
            });
        }

        // 触发多次拒绝
        if (CollectionUtils.isNotEmpty(dialogFlowDeclineConfigRuleList)) {
            dialogFlowDeclineConfigRuleList.forEach(rule -> {
                validHangupRule("个性化配置-特殊挂机流程-触发多次拒绝", rule, knowledgeStepIdSet);
            });
        }

        // 客户单句时长
        if (CollectionUtils.isNotEmpty(dialogFlowUserSayConfigRuleList)) {
            dialogFlowUserSayConfigRuleList.forEach(rule -> {
                validHangupRule("个性化配置-特殊挂机流程-客户单句时长", rule, knowledgeStepIdSet);
            });
        }

        if (BooleanUtils.isTrue(enableCsControl) && Objects.isNull(csControlTime)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "个性化配置-特殊挂机流程-客户单句时长, 时长设置不能为空");
        }

        if (BooleanUtils.isTrue(enableCallTimeout)
                && (Objects.isNull(callTimeoutHangupAnswer) || StringUtils.isBlank(callTimeoutHangupAnswer.getText()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "个性化配置-特殊挂机流程-通话时长设置, 挂断提示语不能为空");
        }

        if (BooleanUtils.isTrue(enableContinuousMuteTimeout)
                && (Objects.isNull(continuousMuteHangupAnswer) || StringUtils.isBlank(continuousMuteHangupAnswer.getText()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "个性化配置-特殊挂机流程-持续静音挂断, 挂断提示语不能为空");
        }

    }

    private void validHangupRule(String errorPrefix, DialogFlowAnswerConfigRulePO rule, Set<String> knowledgeIdSet) {
        if (rule.getCount() == null || rule.getCount() < 1) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR,  String.format("%s, 数量不能为空", errorPrefix));
        }
        if (rule.getAnswerType() == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s, 回答方式不能为空", errorPrefix));
        } else if (rule.getAnswerType() == RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER) {
            if (Objects.isNull(rule.getTextAudioContent()) || StringUtils.isBlank(rule.getTextAudioContent().getText())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s, 挂机话术不能为空",errorPrefix));
            }
            if (Objects.isNull(rule.getPostActionType())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s, 挂机后操作不能为空", errorPrefix));
            }
        } else if (rule.getAnswerType() == RobotKnowledgeAnswerTypeEnum.DIALOG_FLOW_ANSWER) {
            if (!knowledgeIdSet.contains(rule.getDialogFlowStepId())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, String.format("%s, 问答知识流程[id=%s]不存在", errorPrefix, rule.getDialogFlowStepId()));
            }
        }
    }

    public Integer getCallTimeoutSeconds() {
        if (Objects.isNull(callTimeoutSeconds) && Objects.nonNull(callTimeoutMinutes)) {
            callTimeoutSeconds = callTimeoutMinutes * 60;
        }
        return callTimeoutSeconds;
    }
}