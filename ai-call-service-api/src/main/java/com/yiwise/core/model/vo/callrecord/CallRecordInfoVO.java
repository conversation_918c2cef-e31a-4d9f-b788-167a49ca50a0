package com.yiwise.core.model.vo.callrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.helper.objectstorage.AddOssPrefix;
import com.yiwise.core.model.enums.CustomerEmotionEnum;
import com.yiwise.core.model.enums.DialStatusEnum;
import com.yiwise.core.model.enums.GenderEnum;
import com.yiwise.core.model.enums.callin.CallRecordReadStatusEnum;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.textservice.ChannelTypeEnum;
import com.yiwise.core.model.enums.textservice.CustomerServiceModelEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName CallRecordInfoVO
 * <AUTHOR>
 * @Date 2018 7 29 23:11
 * @Version 1.0
 **/
@Data
public class CallRecordInfoVO implements Serializable {
    //通话id
    private Long callRecordId;
    //任务名称
    private String robotCallJobName;
    //任务id
    private Long robotCallJobId;
    //意向等级
//    @ColumnType(typeHandler = IntentLevelEnumHandler.class)
    private Integer realIntentLevel;

    /**
     * 意向等级分组ID
     */
    private Long intentLevelTagId;

    //呼叫时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime startTime;
    //AI和用户的合成总录音
    @AddOssPrefix
    private String fullAudioUrl;
    //用户的录音
    @AddOssPrefix
    private String customerAudioUrl;

    //通话历史类型 0 代表AI， 1代表人工外呼，2 Ai呼入接待, 3 人工呼入接待,4 文本接待
    private Integer type;

    private String dialogFlowName;
    private Long chatDuration;
    private CallRecordReadStatusEnum read;
    private DialStatusEnum resultStatus;
    // 加密id
    private String encryptedId;

    private String realIntentLevelName;

    private int totalIntentLevelTagSize;

    private String intentLevelTagName;

    private String callInCsName;

    /**
     * 是否监听
     */
    private Integer csMonitorFlag;
    /**
     * 是否人工介入
     */
    private Integer csTransferAccept;

    /**
     * 人工坐席名称
     */
    private String CsStaffName;

    /**
     * 人工坐席组的名称
     */
    private String CsStaffGroupName;

    private Long csStaffGroupId;

    /**
     * 情绪
     */
    private CustomerEmotionEnum emotion;

    private GenderEnum customerPersonGender;
    private GenderEnum recognizeGender;
    private String ivrName;
    //0 外呼, 1 人工介入, 2-转人工, 3-话机端, 4-呼叫中心
    private Integer csType = 0;
    private CallRecordTransferTypeEnum transferType;

    //文本记录
    private Long textRecordId;

    private String robotName;

    private ChannelTypeEnum channelType;

    private CustomerServiceModelEnum customerServiceModel;
}
