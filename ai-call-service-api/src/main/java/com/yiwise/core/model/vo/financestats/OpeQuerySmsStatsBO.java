package com.yiwise.core.model.vo.financestats;

import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 * @create 2021/12/24
 */
@Data
public class OpeQuerySmsStatsBO implements ExcelRowModel {

	/**
	 * 时间
	 */
	private LocalDate localDate;

	private Long tenantId;

	/**
	 * 客户名
	 */
	private String tenantName;

	/**
	 * 外呼短信有效计费条数
	 */
	private Integer callOutSmsBillingCount = 0;
	/**
	 * 外呼短信费用
	 */
	private Long callOutSmsCost = 0L;
	/**
	 * 外呼短信退费
	 */
	private Long callOutSmsRefund = 0L;
	/**
	 * 赠送短信有效条数
	 */
	private Integer freeSmsCount = 0;
	/**
	 * 非外呼短信有效计费条数
	 */
	private Integer otherSmsBillingCount = 0;
	/**
	 * 非外呼短信费用
	 */
	private Long otherSmsCost = 0L;
	/**
	 * 非外呼短信退费
	 */
	private Long otherSmsRefund = 0L;

	/**
	 * 短信有效计费条数
	 */
	private Integer smsBillingCount = 0;
	/**
	 * 短信费用
	 */
	private Long smsFare = 0L;
	/**
	 * 短信退费
	 */
	private Long smsRefund = 0L;

	/**
	 * 短信内部结算金额
	 */
	private Long smsInternalSettlementFare = 0L;

	/**
	 * 短信成本
	 */
	private Long smsCost = 0L;

	/**
	 * 短信利润
	 */
	private Long smsProfit = 0L;

	/**
	 * 余额
	 */
	private Long balance = 0L;

	public Key getKey() {
		return new Key(localDate, tenantId);
	}

	public void append(OpeQuerySmsStatsBO that) {
		callOutSmsBillingCount += that.callOutSmsBillingCount;
		callOutSmsCost += that.callOutSmsCost;
		callOutSmsRefund += that.callOutSmsRefund;
		freeSmsCount += that.freeSmsCount;
		otherSmsBillingCount += that.otherSmsBillingCount;
		otherSmsCost += that.otherSmsCost;
		otherSmsRefund += that.otherSmsRefund;
		smsBillingCount += that.smsBillingCount;
		smsFare += that.smsFare;
		smsRefund += that.smsRefund;
		smsInternalSettlementFare += that.smsInternalSettlementFare;
		smsCost += that.smsCost;
		smsProfit += that.smsProfit;
	}

	@Override
	public Map<String, Object> getRowModelMap() {
		Map<String, Object> map = new HashMap<>(18);
		map.put(时间, localDate);
		map.put(客户名, tenantName);
		map.put(有效计费数条, smsBillingCount);
		map.put(短信费, smsFare / 1000.0);
		map.put(余额, balance / 1000.0);
		map.put(外呼短信有效计费条数, callOutSmsBillingCount);
		map.put(赠送短信有效条数, freeSmsCount);
		map.put(非外呼短信有效计费条数, otherSmsBillingCount);
		map.put(外呼短信费用, callOutSmsCost / 1000.0);
		map.put(非外呼短信费用, otherSmsCost / 1000.0);
		map.put(外呼短信退费, callOutSmsRefund / 1000.0);
		map.put(非外呼短信退费, otherSmsRefund / 1000.0);
		map.put(短信内部结算金额, smsInternalSettlementFare / 1000.0);
		map.put(短信成本, smsCost / 1000.0);
		map.put(短信利润, smsProfit / 1000.0);
		map.put(短信有效计费条数, smsBillingCount);
		map.put(短信费用, smsFare / 1000.0);
		map.put(短信退费, smsRefund / 1000.0);
		return map;
	}

	/**
	 * 按时间统计
	 */
	@Data
	@AllArgsConstructor
	public static class Key {
		private LocalDate localDate;
		private Long tenantId;
	}
}
