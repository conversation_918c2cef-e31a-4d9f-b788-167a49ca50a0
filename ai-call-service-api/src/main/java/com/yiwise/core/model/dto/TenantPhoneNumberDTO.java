package com.yiwise.core.model.dto;

import com.yiwise.core.model.enums.CallInBillModeEnum;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.handler.CallInBillModeEnumHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;

/**
*
* <AUTHOR> yangdehong
* @date : 2018/11/15 2:17 PM
*/
@Data
public class TenantPhoneNumberDTO implements Serializable {
    /**
     * BIGINT(19) 必填
     * 自增ID
     */
    private Long tenantPhoneNumberId;
    /**
     * 本地话费费率
     */
    private Long localBillRate;
    /**
     * 外地话费费率
     */
    @Deprecated
    private Long otherBillRate;

    /**
     * BIGINT(19) 必填
     * 租户id
     */
    private Long tenantId;

    private Integer concurrency;

    /**
     * BIGINT(19) 必填
     * VoIP账号表中的id
     */
    private Long phoneNumberId;

    /**
     * VARCHAR(12) 必填
     * 手机号
     */
    private String phoneNumber;

    /**
     * VARCHAR(11) 默认值[]
     * 手机名称
     */
    private String phoneName;

    /**
     * TINYINT(3) 默认值[0] 必填
     * 电话卡类型 0:机器人手机卡 1:坐席手机卡
     */
    private PhoneTypeEnum phoneType;

    /**
     * TINYINT(3) 默认值[60]
     * 计费周期，单位秒(默认60)
     */
    private Integer billPeriod;

    /**
     * 账户
     */
    private Long accountFare;

    /**
     * VARCHAR(200)
     * 备注
     */
    private String remark;

    private Long ownerDistributorId;

    private Long ownerTenantId;

    /**
     * 呼入月租费用
     */
    private Long monthlyBillRate;

    /**
     * 是否支持呼入
     */
    private Boolean supportCallIn;

    /**
     * 是否支持呼出
     */
    private Boolean supportCallOut;

    /**
     * 呼入本地分钟计费，单位 厘
     */
    private Long callInLocalBillRate;

    /**
     * 呼入外地分钟计费，单位 厘
     */
    @Deprecated
    private Long callInOtherBillRate;

    /**
     * 呼入计费方式，月租或者按分钟计费
     */
    @ColumnType(typeHandler = CallInBillModeEnumHandler.class)
    private CallInBillModeEnum callInBillMode;

    private Integer concurrenceLimit;
    private Integer dailyCallCount;
    private Integer alertCountThreshold;
    //线路供应商的状态
    private Integer supplierStatus;
}
