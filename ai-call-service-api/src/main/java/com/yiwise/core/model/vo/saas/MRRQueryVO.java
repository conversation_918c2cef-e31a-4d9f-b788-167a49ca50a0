package com.yiwise.core.model.vo.saas;

import com.yiwise.core.model.enums.CustomerIndustryEnum;
import com.yiwise.core.model.enums.TenantAccountStatusEnum;
import com.yiwise.core.model.enums.TenantPayTypeEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collection;

/**
 * <AUTHOR>
 * @create 2022/06/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MRRQueryVO extends AbstractQueryVO implements StartEndDate {

	@NotNull
	LocalDate startDate;
	@NotNull
	LocalDate endDate;
	Collection<TenantAccountStatusEnum> tenantAccountStatuses;
	Collection<TenantPayTypeEnum> tenantPayTypes;

	Collection<CustomerIndustryEnum> customerIndustries;
	Collection<CustomerTrackPair> customerTrackTypes;
}
