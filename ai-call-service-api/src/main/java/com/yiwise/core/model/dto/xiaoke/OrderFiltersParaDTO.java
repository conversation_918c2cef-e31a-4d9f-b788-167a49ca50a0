package com.yiwise.core.model.dto.xiaoke;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xiayu
 * @date : 2021/8/26 16:29
 **/
@Data
public class OrderFiltersParaDTO {

    @JsonProperty(value = "field_name")
    private String fieldName;

    @JsonProperty(value = "field_values")
    private List fieldValues;

    private String operator;

    public OrderFiltersParaDTO(String fieldName, List<String> fieldValues, String operator) {
        this.fieldName = fieldName;
        this.fieldValues = fieldValues;
        this.operator = operator;
    }
}
