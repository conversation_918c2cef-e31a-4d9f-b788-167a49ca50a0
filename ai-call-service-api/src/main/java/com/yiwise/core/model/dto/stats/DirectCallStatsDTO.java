package com.yiwise.core.model.dto.stats;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 直接拨打外呼结束后发送的统计信息
 * <AUTHOR>
 * @create 2022/10/12
 */
@Data
public class DirectCallStatsDTO {

	private LocalDateTime dateTime;
	private Integer year;
	private Integer month;
	private Integer day;
	private Integer hour;
	private Long tenantId;
	private Long dialogFlowId;
	private Long phoneNumberId;
	/** 客户报价 */
	private Long aiFare;

	/** 通话时长, 秒 */
	private Long chatDuration;
	/** 振铃时长, 秒 */
	private Long ringDuration;
	/** 外呼数 */
	private Long taskTotalCompleted;
	/** 接通数 */
	private Long totalAnsweredCall;
	/** 通话计费时长, 分钟 */
	private Long billChatDuration;
	/** 振铃计费通数, 分钟 */
	private Long billRingDuration;
	/** 费用消耗(元) */
	private Long callCost;

	private Long callRecordId;
}
