package com.yiwise.core.model.vo.algorithm;

import com.yiwise.core.model.enums.train.AlgorithmTrainTypeEnum;
import com.yiwise.core.model.enums.train.AlgorithmTypeEnum;
import com.yiwise.core.model.enums.train.SnapshotTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/13
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TrainSignalVO implements Serializable {

    /**
     * 环境
     */
    String environment;

    /**
     * 租户ID
     */
    Long tenantId;

    /**
     * 机器人ID
     */
    Long robotId;

    /**
     * 算法种类
     */
    AlgorithmTypeEnum algorithmType;

    /**
     * 训练类型
     */
    AlgorithmTrainTypeEnum trainType;

    /**
     * 版本类型
     */
    SnapshotTypeEnum snapshotType;

    /**
     * 领域模型名称
     */
    String domainName;

    /**
     * 模型插件
     */
    String patch;
}
