package com.yiwise.core.model.vo.privacynumber.changgong;

import lombok.Data;

import java.util.List;

/**
 * @author: <EMAIL>
 * @date: 2024 10 11 16:50
 */
@Data
public class ChangGongResponseVO {

    /**
     * 请求唯一流水号，与请求对应。
     */
    private String id;

    /**
     * 响应是否成功。
     */
    private Boolean success;

    /**
     * 响应消息，用于提供额外的响应信息。
     */
    private String message;

    /**
     * 响应编码，用于标识响应的状态或类型。
     */
    private String code;

    /**
     * 请求API功能响应数组，包含多个API功能的响应数据。
     */
    private List<ChangGongResponseInfoVO> response;

}
