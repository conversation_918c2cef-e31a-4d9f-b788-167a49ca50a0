package com.yiwise.core.model.vo.filteredtask;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.model.enums.FilterTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/9/1 15:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FilteredCsCallJobTaskQueryVO extends AbstractQueryVO {
    private Long tenantId;

    private Long csBatchCallJobId;

    private String customerPersonName;

    private String calledPhoneNumber;

    private FilterTypeEnum filterType;

    private Long targetCsBatchCallJobId;

    private SystemEnum systemType;

    private Long userId;

    private Set<Long> filteredCsCallJobIds;

}
