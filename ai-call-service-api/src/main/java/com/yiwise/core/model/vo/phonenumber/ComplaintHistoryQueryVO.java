package com.yiwise.core.model.vo.phonenumber;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.phonenumber.GatewayIndustryEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ComplaintHistoryQueryVO extends AbstractQueryVO implements Serializable {

    //通话时间筛选
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;
    //通话时间筛选
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 客户ID
     */
    private Long tenantId;

    /**
     * 行业ID
     */
    private Integer customerTrackType;

    private List<Long> tenantIds;

    /**
     * 线路代理商ID
     */
    private Long phoneNumberSupplierId;

    /**
     * 仅显示有效供应商
     */
    private Boolean validPhoneNumberSupplier;

    /**
     * 当前登录的租户ID
     */
    private Long currentTenantId;

    /**
     * 当前登陆的用户ID
     */
    private Long userId;

    /**
     * 0租户维度
     * 1线路商维度
     * 2赛道维度
     */
    private Integer type;

    //创建时间筛选
    private LocalDate createRecordStartDate;

    //创建时间筛选
    private LocalDate createRecordEndDate;

    /**
     * 0:升序
     * 1：降序
     * 不传：默认排序
     */
    private Integer sort;

    /**
     * 网关行业
     */
    private List<String> gatewayIndustry;

}
