package com.yiwise.core.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.bo.callrecord.CallRecordWithJobInfoBO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.customer.CustomerPersonInfoVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
public class ApiCustomerVO implements Serializable {
    /**
     * 客户Id
     */
    private Long customerPersonId;
    /**
     * 客户姓名
     */
    private String customerPersonName;
    /**
     * 联系电话
     */
    private Set<String> phoneNumber;
    /**
     * 微信
     */
    private String weChat;
    /**
     * 客户来源
     */
    private String source;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 领取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime assignTime;
    /**
     * 性别
     */
    private String gender;
    /**
     * 客户分组
     */
    private String customerGroup;
    /**
     * 自定义属性
     */
    private Map<String, String> properties;
    /**
     * 客户标签
     */
    private String customerLevelTag;
    /**
     * 创建人信息
     */
    private String createdByUserName;
    /**
     * 跟进状态
     */
    private Integer followStatus;
    /**
     * 最近通话状态
     */
    private String lastDialStatus;
    /**
     * 最近客户意向
     */
    private String lastIntentLevelName;
    /**
     * 最近通话时长
     */
    private Long lastChatDuration;
    /**
     * 最近呼叫时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastStartTime;
    /**
     * 最近外呼任务
     */
    private String callRecordList;
    /**
     * 最近跟进时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastFollowTime;

    public ApiCustomerVO(){}

    public ApiCustomerVO(CustomerPersonInfoVO customerPersonInfo){
        this.assignTime = customerPersonInfo.getAssignTime();
        this.createTime = customerPersonInfo.getCreateTime();
        this.customerPersonId = customerPersonInfo.getCustomerPersonId();
        this.customerPersonName = customerPersonInfo.getName();
        if(customerPersonInfo.getGender() != null) {
            this.gender = customerPersonInfo.getGender().getDesc();
        }
        if(customerPersonInfo.getSource() != null) {
            this.source = customerPersonInfo.getSource().getDesc();
        }
        this.weChat = customerPersonInfo.getWechatNumber();
        this.phoneNumber = new HashSet<>();
        this.phoneNumber.add(customerPersonInfo.getPhoneNumber());
        if(customerPersonInfo.getAlternatePhoneNumbers() != null){
            this.phoneNumber.addAll(customerPersonInfo.getAlternatePhoneNumbers());
        }
        this.customerGroup = customerPersonInfo.getCustomerGroup();
        this.properties = customerPersonInfo.getProperties();
        this.customerLevelTag = customerPersonInfo.getCustomerLevelTag();
        this.createdByUserName = customerPersonInfo.getCreatedByUserName();
        this.followStatus = customerPersonInfo.getFollowStatus();
//        if(customerPersonInfo.getFollowStatus() != null) {
//            this.followStatus = customerPersonInfo.getFollowStatus().getDesc();
//        }
        if(customerPersonInfo.getLastDialStatus() != null) {
            this.lastDialStatus = customerPersonInfo.getLastDialStatus().getDesc();
        }
        this.lastIntentLevelName = customerPersonInfo.getLastIntentLevelName();
        this.lastChatDuration = customerPersonInfo.getLastChatDuration();
        this.lastStartTime = customerPersonInfo.getLastStartTime();
        this.callRecordList = getCallRecordList(customerPersonInfo.getCallRecordList());
        this.lastFollowTime = customerPersonInfo.getLastFollowTime();
    }

    public void copy(CustomerPersonInfoVO customerPersonInfo){
        this.assignTime = customerPersonInfo.getAssignTime();
        this.createTime = customerPersonInfo.getCreateTime();
        this.customerPersonId = customerPersonInfo.getCustomerPersonId();
        this.customerPersonName = customerPersonInfo.getName();
        if(customerPersonInfo.getGender() != null) {
            this.gender = customerPersonInfo.getGender().getDesc();
        }
        if(customerPersonInfo.getSource() != null) {
            this.source = customerPersonInfo.getSource().getDesc();
        }
        this.weChat = customerPersonInfo.getWechatNumber();
        this.phoneNumber = new HashSet<>();
        this.phoneNumber.add(customerPersonInfo.getPhoneNumber());
        if(customerPersonInfo.getAlternatePhoneNumbers() != null){
            this.phoneNumber.addAll(customerPersonInfo.getAlternatePhoneNumbers());
        }
        this.customerGroup = customerPersonInfo.getCustomerGroup();
        this.properties = customerPersonInfo.getProperties();
        this.customerLevelTag = customerPersonInfo.getCustomerLevelTag();
        this.createdByUserName = customerPersonInfo.getCreatedByUserName();
        this.followStatus = customerPersonInfo.getFollowStatus();
//        if(customerPersonInfo.getFollowStatus() != null) {
//            this.followStatus = customerPersonInfo.getFollowStatus().getDesc();
//        }
        if(customerPersonInfo.getLastDialStatus() != null) {
            this.lastDialStatus = customerPersonInfo.getLastDialStatus().getDesc();
        }
        this.lastIntentLevelName = customerPersonInfo.getLastIntentLevelName();
        this.lastChatDuration = customerPersonInfo.getLastChatDuration();
        this.lastStartTime = customerPersonInfo.getLastStartTime();
        this.callRecordList = getCallRecordList(customerPersonInfo.getCallRecordList());
        this.lastFollowTime = customerPersonInfo.getLastFollowTime();
    }

    private String getCallRecordList(List<CallRecordWithJobInfoBO> callRecordList) {
        if (CollectionUtils.isEmpty(callRecordList)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        callRecordList.forEach(callRecord -> {
            if (sb.length() > 0) {
                sb.append("\r\n");
            }
            String jobName = callRecord.getRobotCallJobName();
            DialStatusEnum resultStatus = callRecord.getResultStatus();
            Integer intentLevel = callRecord.getIntentLevel();
            sb.append(jobName).append(":").append(resultStatus == null ? "" : resultStatus.getDesc()).append(":").append(intentLevel == null ? "" : intentLevel);
        });

        return sb.toString();
    }
}
