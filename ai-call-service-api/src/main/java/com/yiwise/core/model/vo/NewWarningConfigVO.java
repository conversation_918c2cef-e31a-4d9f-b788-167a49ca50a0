package com.yiwise.core.model.vo;

import com.yiwise.core.dal.entity.NewWarningConfigPO;
import com.yiwise.core.model.dto.RobotCallJobSimpleInfoDTO;
import com.yiwise.core.model.enums.WarningDateEnum;
import com.yiwise.core.model.vo.user.UserSimpleInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class NewWarningConfigVO extends NewWarningConfigPO implements Serializable {

    /**
     * 客户名称
     */
    private String companyName;

    /**
     * 预警范围
     */
    private List<String> warningExtent;

    /**
     * 任务列表
     */
    private List<RobotCallJobSimpleInfoDTO> robotCallJobList;

    /**
     * 预警推送人列表
     */
    private List<UserSimpleInfoVO> warningUserList;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 预警方式
     */
    private String warningMethodDesc;

    /**
     * 线路名称
     */
    private String phoneNumber;

    private Integer warningDateCode;

    /**
     * 预警范围
     */
    private List<List<String>> warningMultipleExtent;
}
