package com.yiwise.core.model.vo.user;

import com.yiwise.core.model.enums.UserStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class UserGuideListVO extends UserByWechatListVO implements Serializable {

    private String wxPublicAccountOpenId;

    private UserStatusEnum status;

    public UserGuideListVO() {
    }

    public UserGuideListVO(Long id, String name, String nickname, String wxPublicAccountOpenId, UserStatusEnum status) {
        super(id, name, nickname);
        this.wxPublicAccountOpenId = wxPublicAccountOpenId;
        this.status = status;
    }
}
