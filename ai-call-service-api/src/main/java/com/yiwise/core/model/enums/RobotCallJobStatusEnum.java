package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 24/07/2018
 */
public enum RobotCallJobStatusEnum implements CodeDescEnum {
    NOT_STARTED(0, "未开始"),
    IN_PROCESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    RUNNABLE(3, "可运行"),
    USER_PAUSE(4, "用户暂停"),
    SYSTEM_SUSPENDED(5, "系统暂停"),
    TERMINATE(6, "已终止"),
    IN_QUEUE(7, "排队中"),
    SYSTEM_HANG_UP(10, "系统挂起"),
    WAITING_FOR_REDIAL(11, "等待重呼"),
    ACCOUNT_DISABLE(12, "账户禁用"),
    MAINTAIN(13, "系统维护"),
    /**
     * 还有未呼客户, 但是已经到了用户设置的任务的结束时间
     */
    EXPIRED(14, "任务超时"),
    NOT_GENERATED(15, "未生成"),
	ARCHIVED(16, "已归档"),
	;

    private Integer code;
    private String desc;

    RobotCallJobStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * 是否仍然可以运行
     */
    public static boolean isRunnable(RobotCallJobStatusEnum status) {
        return !TERMINATE.equals(status)
            && !USER_PAUSE.equals(status)
            && !SYSTEM_HANG_UP.equals(status)
            && !EXPIRED.equals(status);
    }

    /**
     * 是否可编辑
     */
    public static boolean isEditable(RobotCallJobStatusEnum status) {
        return USER_PAUSE.equals(status)
                || NOT_STARTED.equals(status)
                || SYSTEM_HANG_UP.equals(status)
                || COMPLETED.equals(status)
                ||NOT_GENERATED.equals(status);
    }

    /**
     * 是否可开始
     */
    public static boolean isStartAble(RobotCallJobStatusEnum status) {
        return NOT_STARTED.equals(status)
                || USER_PAUSE.equals(status)
                || SYSTEM_HANG_UP.equals(status)
                || ACCOUNT_DISABLE.equals(status)
                || MAINTAIN.equals(status);
    }

    /**
     * 是否可停止
     */
    public static boolean isPauseAble(RobotCallJobStatusEnum status) {
        return IN_PROCESS.equals(status)
            || SYSTEM_SUSPENDED.equals(status)
            || IN_QUEUE.equals(status)
            || WAITING_FOR_REDIAL.equals(status)
            || EXPIRED.equals(status) || RUNNABLE.equals(status);
    }

    /**
     * 是否可终止
     */
    public static boolean isTerminateAble(RobotCallJobStatusEnum status) {
        return IN_PROCESS.equals(status)
		        || COMPLETED.equals(status)
		        || USER_PAUSE.equals(status)
		        || SYSTEM_SUSPENDED.equals(status)
		        || IN_QUEUE.equals(status)
		        || SYSTEM_HANG_UP.equals(status)
		        || WAITING_FOR_REDIAL.equals(status)
		        || EXPIRED.equals(status);
    }

	/**
	 * 能否重新统计
	 */
	public static boolean canReStats(RobotCallJobStatusEnum status) {
		return COMPLETED.equals(status) || USER_PAUSE.equals(status) || SYSTEM_SUSPENDED.equals(status) || TERMINATE.equals(status) ||
				SYSTEM_HANG_UP.equals(status) || ACCOUNT_DISABLE.equals(status) || MAINTAIN.equals(status) || EXPIRED.equals(status);
    }

    /**
     * 获取在首页展示的任务状态列表
     */
    public static List<RobotCallJobStatusEnum> showInFrontPage() {
        return Arrays.asList(IN_PROCESS, IN_QUEUE, SYSTEM_SUSPENDED, SYSTEM_HANG_UP, MAINTAIN, WAITING_FOR_REDIAL, EXPIRED);
    }

    /**
     * 是否需要更新任务的上一次运行结束时间
     */
    public static boolean isUpdateLastRunEndTime(RobotCallJobStatusEnum status) {
        return COMPLETED.equals(status)
                || USER_PAUSE.equals(status)
                || SYSTEM_SUSPENDED.equals(status)
                || SYSTEM_HANG_UP.equals(status)
                || WAITING_FOR_REDIAL.equals(status);
    }

    public static boolean isPauseStatus(RobotCallJobStatusEnum status) {
        return COMPLETED.equals(status)
                || USER_PAUSE.equals(status)
                || SYSTEM_SUSPENDED.equals(status)
                || SYSTEM_HANG_UP.equals(status)
                || WAITING_FOR_REDIAL.equals(status);
    }

	/**
	 * 能否导入客户
	 */
	public static boolean canNotImportCustomer(RobotCallJobStatusEnum status) {
    	return ARCHIVED.equals(status);
    }
}
