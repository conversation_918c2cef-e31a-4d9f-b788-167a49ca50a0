package com.yiwise.core.model.vo.csmconsole;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CustomerIndustryRowVO {
    /**
     * 测试开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime evaluationStartTime;
    /**
     * 转正日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime regularizationTime;
    /**
     * 客户转正时长
     */
    private Integer regularizationDayCount;
    /**
     * 行业平均转正时长
     */
    private Integer averageRegularizationDayCount;
}
