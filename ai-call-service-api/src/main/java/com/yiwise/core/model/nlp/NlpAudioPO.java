package com.yiwise.core.model.nlp;

import com.yiwise.core.model.enums.nlp.TextSourceEnum;
import com.yiwise.core.model.nlp.bo.NlpInstructionBO;
import com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 和nlp交互时处理的音频
 * @create 2019/08/07
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NlpAudioPO implements Serializable {

    /**
     * NLP系统发布后合成的音频位置
     */
    public final static String COLLECTION_NAME = "nlp_audio";
    /**
     * TANYI系统审核通过后的音频位置, 供外呼使用
     */
    public final static String COLLECTION_NAME_APPROVED = "nlp_audio_approved";

    /*
      nlp端返回字段
     */
    /**
     * nlp端的存储id(机器人id/对话流节点id/知识库id)
     * 如果不加注解, mongo会把该字段设置为_id
     */
    @Field("id")
    String id;
    /**
     * 展示的ai文本
     */
    String text;

    /**
     * 标签，同一机器人内唯一且自增
     */
    String label;

    /**
     * 待录音文本内容
     */
    @NotBlank
    String template;

    /**
     * 待录音文本内容的md5
     */
    @NotBlank
    String templateMD5;

    /**
     * 待录音的文本来源
     */
    @NotNull
    TextSourceEnum type;

    /**
     * 二级标题(前端展示用)
     */
    String title;

    /**
     * 指令列表
     */
    @NotEmpty
    List<NlpInstructionBO> instructionList;

    /**
     * key of map
     */
    @Override
    public String toString() {
        return "NlpAudioPO{" +
            "id='" + id + '\'' +
            ", template='" + template + '\'' +
            ", type=" + type +
            '}';
    }

    /*
      探意处理字段
     */
    /**
     * 机器人id
     */
    Long nlpRobotId;

    @Id
    ObjectId mongo_id;

    /**
     * 话术id
     */
    Long dialogFlowId;

    /**
     * 音频产生方式
     */
//    DialogVoiceTypeEnum audio;

    /**
     * 多段录音文本
     */
    List<String> templateList;

    /**
     * 多段录音url, 可以直接下载, 如果没有录则对应位置为null
     */
    List<String> fullUrl;

    /**
     * 多段录音url, 不含前缀, 如果没有录则对应位置为null
     */
    List<String> relativeUrl;

    /**
     * tts 录音文件url
     */
    List<String> ttsFullUrl;

    /**
     * tts 录音文件url 不含前缀
     */
    List<String> ttsRelativeUrl;

    /**
     * 多段录音path
     */
    List<String> localPath;

    /**
     * 最后播放的录音path
     */
    String finalLocalPath;

    public boolean ready(DialogVoiceTypeEnum voiceType) {
        List<String> testUrl = fullUrl;
        if (voiceType.equals(DialogVoiceTypeEnum.COMPOSE)) {
            testUrl = ttsFullUrl;
        }
        if (testUrl == null) {
            return false;
        } else {
            return testUrl.stream().allMatch(StringUtils::isNotEmpty);
        }
    }

    public List<String> getFullUrl() {
        if (Objects.isNull(fullUrl)) {
            fullUrl = new ArrayList<>(Collections.nCopies(CollectionUtils.size(templateList), null));
        }
        return fullUrl;
    }

    public List<String> getTtsFullUrl() {
        if (Objects.isNull(ttsFullUrl)) {
            ttsFullUrl = new ArrayList<>(Collections.nCopies(CollectionUtils.size(templateList), null));
        }
        return ttsFullUrl;
    }

    public List<String> getRelativeUrl() {
        if (Objects.isNull(relativeUrl)) {
            relativeUrl = new ArrayList<>(Collections.nCopies(CollectionUtils.size(templateList), null));
        }
        return relativeUrl;
    }

    public List<String> getTtsRelativeUrl() {
        if (Objects.isNull(ttsRelativeUrl)) {
            ttsRelativeUrl = new ArrayList<>(Collections.nCopies(CollectionUtils.size(templateList), null));
        }
        return ttsRelativeUrl;
    }

    /**
     * nlp改为使用指令表示音频合成方式, 后端使用audio字段
     * nlp更新机器人时调用该接口对audio赋值
     */
//    public void explainInstruction() {
//        if (CollectionUtils.isNotEmpty(instructionList)) {
//            // 只存表示音频来源的指令
//            instructionList = instructionList.stream()
//                    .filter(instruction -> NlpCommandEnum.audio.equals(instruction.getResponseContent().getType()))
//                    .collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(instructionList)) {
//                // 设置音频来源
//                audio = instructionList.get(0).getResponseContent().getVoiceType();
//            }
//        }
//        // 默认为tts
//        if (audio == null) {
//            audio = DialogVoiceTypeEnum.COMPOSE;
//        }
//    }
}
