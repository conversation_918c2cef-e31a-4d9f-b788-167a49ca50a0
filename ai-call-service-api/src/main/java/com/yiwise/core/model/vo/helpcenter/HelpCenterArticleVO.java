package com.yiwise.core.model.vo.helpcenter;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* 帮助中心菜单
* <AUTHOR> yang<PERSON><PERSON>
* @date : 2020-06-28 09:47
*/
@Data
public class HelpCenterArticleVO extends AbstractQueryVO implements Serializable {

    private static final long serialVersionUID = 8600039942522767495L;

    private String name;

    private String context;

    /**
     * 状态 0-未发布 1-发布
     */
    private String status;

    /**
     * 菜单id
     */
    private Long helpCenterMenuId;
    /**
     * 点击量
     */
    private Long clickNumber;

    /**
     * 搜索关键词
     */
    private String searchWords;

    /**
     * 排序 0-默认，1-最新，2-最热
     */
    private int sortFiled;

    private List<String> imgList;


}
