package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.phonenumber.CallOutIndustryEnum;
import com.yiwise.core.model.enums.phonenumber.CallOutRangeEnum;
import com.yiwise.core.model.enums.phonenumber.PhoneShowMethodEnum;
import com.yiwise.core.model.enums.phonenumber.PhoneUsageEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import java.io.Serializable;

import java.util.Set;

@Data
public class LineMarketQueryVO extends AbstractQueryVO implements Serializable {
    private PhoneUsageEnum usage;
    private CallOutRangeEnum callOutRange;
    private PhoneShowMethodEnum showMethod;
    private CallOutIndustryEnum callOutIndustry;
    private Long distributorId;

    private Set<Long> distributorFilterPhoneNumberId;
}
