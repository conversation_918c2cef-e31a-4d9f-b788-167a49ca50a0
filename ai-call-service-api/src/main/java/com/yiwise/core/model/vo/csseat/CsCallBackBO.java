package com.yiwise.core.model.vo.csseat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.helper.objectstorage.AddOssPrefix;
import com.yiwise.core.model.enums.DialStatusEnum;
import com.yiwise.core.model.enums.handler.DialStatusEnumHandler;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.enums.robotcalljob.handler.CallJobHangupEnumHandler;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wangguomin
 * @Date: 2020-08-25 18:32
 */
@Data
public class CsCallBackBO implements Serializable {
    private Long csRecordId;

    private Long tenantId;

    private Long customerPersonId;

    private String customerPersonName;

    private String customerPersonNumber;

    private Long phoneNumberId;

    @ColumnType(typeHandler = DialStatusEnumHandler.class)
    private DialStatusEnum resultStatus;

    /**
     * 坐席通话结果
     */
    @ColumnType(typeHandler = DialStatusEnumHandler.class)
    private DialStatusEnum seatStatus;

    @AddOssPrefix
    private String fullAudioUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private Long csStaffId;

    private String csStaffName;

    private Long chatDuration;

    @ColumnType(typeHandler = CallJobHangupEnumHandler.class)
    private CallJobHangupEnum hangupBy;

}
