package com.yiwise.core.model.nlp;

import lombok.AccessLevel;
import lombok.Data;
import java.io.Serializable;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @description 录音中的变量
 * @create 2019/08/07
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class NlpVariablePO implements Serializable {

    /**
     * 文本模板中的变量
     */
    String name;

    /**
     * 变量值
     */
    String value;

    /**
     * 该变量在文本中的开始index
     */
    Integer start;

    /**
     * 该变量在文本中的结束index
     */
    Integer end;
}
