package com.yiwise.core.model.vo.ope;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.handler.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.handler.AccountTypeEnumHandler;
import com.yiwise.core.validate.boss.customer.AddBossDirectCustomerValidate;
import com.yiwise.core.validate.boss.customer.AddBossDistributorCustomerValidate;
import com.yiwise.core.validate.ope.directCustomer.DirectCustomerInsertValidate;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerInsertValidate;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.*;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName CustomerInsertVO
 * <AUTHOR>
 * @Date 2018 8 21 22:31
 * @Version 1.0
 **/
@Data
public class CustomerInsertVO implements Serializable, OpenAPICustomerEditVO {

    private List<Long> dialogFlowIds;

    @Id
    @GeneratedValue(generator = "JDBC")
    private Long tenantId;

    private String customerId;

    /**
     * VARCHAR(50) 必填
     * 客户名（公司名称）
     */
    @NotEmpty(message = "客户名不能为空！", groups = {DirectCustomerInsertValidate.class, DistributorCustomerInsertValidate.class, AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class})
    private String companyName;

    /**
     * 经纪人id
     */
    private Long accountManagerId;

    /**
     * VARCHAR(50) 必填
     * 联系人
     */
    @NotEmpty(message = "联系人不能为空！", groups = {DirectCustomerInsertValidate.class, DistributorCustomerInsertValidate.class, AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class})
    private String linkman;

    /**
     * VARCHAR(15) 必填
     * 联系电话
     */
    @NotEmpty(message = "联系电话不能为空！", groups = {DirectCustomerInsertValidate.class, DistributorCustomerInsertValidate.class, AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class})
    private String phoneNumber;

    /**
     * VARCHAR(20)
     * 客户经理
     */
    private String accountManager;

    /**
     * INTEGER(10) 必填
     * 账号类型0代表正式，1代表试用, 2代表测试 3试用转正式 4 渠道内测
     */
    @NotNull(message = "是否试用不能为空", groups = {AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class})
    @ColumnType(typeHandler = AccountTypeEnumHandler.class)
    private AccountTypeEnum accountType;

    /**
     * VARCHAR(100)
     * 备注
     */
    @Size(max = 500, message = "备注最多只能500个字", groups = {AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class, DirectCustomerInsertValidate.class, DistributorCustomerInsertValidate.class})
    private String comment;

    /**
     * VARCHAR(20)
     * 话术名称
     */
    private String dialogFlowName;

    /**
     * BIGINT(19) 必填
     * ai的并发数（关联的robot表）
     */
    private Long aiConcurrencyLevel;
    /**
     * BIGINT(19)
     * 绑定线路id
     */
    private Long bindingId;

    /**
     * VARCHAR(20)
     * 绑定线路名称
     */
    private String bindingName;

    /**
     * BIGINT(19)
     * 话费余额
     */
    private Long telephoneFare;

    /**
     * TIMESTAMP(19) 必填
     * 开始服务时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startTime;

    /**
     * TIMESTAMP(19) 必填
     * 结束服务时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endTime;

    /**
     * BIGINT(19)
     * 经销商的id
     */
    @NotNull(message = "代理商的名称不能为空！", groups = {DistributorCustomerInsertValidate.class, AddBossDistributorCustomerValidate.class})
    private Long distributorId;

    @NotNull(message = "是否启用不能为空！", groups = {AddBossDirectCustomerValidate.class, AddBossDistributorCustomerValidate.class})
    @ColumnType(typeHandler = UserStatusEnumHandler.class)
    private UserStatusEnum status;

    private String password;

    /**
     * 客户经理电话
     */
    private String accountManagePhone;
    /**
     * CSM负责人电话
     */
    private String csmPhone;

    private String phoneNumberUserPhone;
    private Long phoneNumberUserId;

	private List<String> deploymentUserPhoneList;

    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 实施负责人的id
     */
    private Long deploymentUserId;

    /**
     * 客户行业
     */
    @Column
    @NotNull(message = "行业不能为空")
    private CustomerIndustryEnum customerIndustry;
    /**
     * 客户子行业
     */
    @Column
    @NotNull(message = "子行业不能为空")
    private CustomerSubIndustryEnum customerSubIndustry;

    private List<Long> deploymentUserIdList;

    /**
     * CSM负责人id
     */
    private Long csmUserId;

    @ColumnType(typeHandler = TenantPayTypeEnumHandler.class)
    private TenantPayTypeEnum tenantPayType;

    private Double aiFareDouble;
    private Double comFareDouble;

	/**
	 * AI外呼报价 单位 厘
	 */
	private Long aiFare;
	/**
	 * 通讯结算价
	 */
	private Long comFare;
	private Long smsComFare;
	private Long distributorComFare;
	private Long distributorSmsComFare;

	/**
	 * 免费赠送短信数
	 */
	private Integer freeSmsCount;

    private Long scrmId;

    private String scrmName;

    private TenantAccountStatusEnum tenantAccountStatus;

    /**
     * 加密模式
     */
    @ColumnType(typeHandler = TenantPayTypeEnumHandler.class)
    private TenantEncryptTypeEnum encryptType;

    /**
     * 纷享销客加密模式字段
     */
    private String fkEncryptType;

    /**
     * 客户赛道类型
     */
    private Integer customerTrackType;

    /**
     * 客户二级赛道类型
     */
    private Integer customerTrackSubType;

    /**
     * 赠送话术套数
     */
    private Integer freeDialogFlowCount;

    /**
     * 赠送话术套数-已使用
     */
    private Integer usedFreeDialogFlowCount;

    /**
     * 话术制作成本（不含录音）
     */
    private Integer dialogFlowCostWithoutRecord;

    /**
     * 话术制作成本（含录音）
     */
    private Integer dialogFlowCostWithRecord;

    /**
     * 话术制作成本（10句以内）
     */
    private Integer dialogFlowCostWithinTenSentence;

    /**
     * 话术制作成本（50句以内）
     */
    private Integer dialogFlowCostWithinFiftySentence;

    /**
     * 赠送话术修改句
     */
    private Integer freeDialogFlowSentenceCount;

    /**
     * 赠送话术修改句-已使用
     */
    private Integer usedFreeDialogFlowSentenceCount;

    /**
     * 单句修改成本
     */
    private Integer sentenceCost;

    /**
     * 新的主品牌ID（一级账号）
     */
    private Long newMainBrandId;

    /**
     * 打款主体ID
     */
    private Long paymentId;

    /**
     * 纷享销客主品牌ID
     */
    private String fkMainBrandId;

    /**
     * 纷享销客打款主体ID
     */
    private String fkPaymentId;

	/**
	 * AI外呼音频保留时间
	 */
	@ColumnType(typeHandler = CallOutAudioRetentionTimeEnumHandler.class)
	private CallOutAudioRetentionTimeEnum callOutAudioRetentionTime;

    /**
     * 是否是集团管理账号
     */
    private Boolean isGroupManager;

    /**
     * 是否是openApi
     */
    private Boolean isOpenApi;
    /**
     * 业务单元
     */
    @ColumnType(typeHandler = BusinessUnitEnumHandler.class)
    private BusinessUnitEnum businessUnit;

    /**
     * 客户来源
     */
    @ColumnType(typeHandler = TrafficSourceEnumHandler.class)
    private TrafficSourceEnum trafficSource;

    /**
     * 生态客户
     */
    private String ecoCustomer;
}
