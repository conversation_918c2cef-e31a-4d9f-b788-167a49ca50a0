package com.yiwise.core.model.vo.rechargestream;

import com.yiwise.core.model.enums.RechargeStreamHandleTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.rechargstream.RechargeBillApplyStatusEnum;
import com.yiwise.core.model.enums.rechargstream.RechargeMethodEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/2/27 2:39 PM
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TenantRechargeStreamQueryVO implements Serializable {
    List<Long> tenantIds;
    Long tenantId;
    Long currTenantId;
    Long phoneNumberId;
    List<Long> phoneNumberIds;
    Long  distributorId;
    String startDate;
    String endDate;
    Integer pageNum;
    Integer pageSize;
    List<RechargeStreamHandleTypeEnum> enums;
    Long currentLoginUserId;
    Boolean withoutPageCount;
    //发票申请状态
    RechargeBillApplyStatusEnum rechargeBillApplyStatus;
    List<RechargeMethodEnum> rechargeMethods;
    String aliPayReference;
    String orderId;
    SystemEnum systemType;
}
