package com.yiwise.core.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.dal.entity.CustomerWhiteGroupPO;
import com.yiwise.core.model.enums.TenantCustomerTypeEnum;
import com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * @ClassName SimpleShareTenantDTO
 * <AUTHOR>
 * @Date 2019/3/27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimpleShareTenantDTO implements Serializable {

    private Long sharePolicyTenantId;

    private Long tenantId;

    private String tenantName;

    private TenantCustomerTypeEnum customerType;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate joinTime;

    private Long distributorId;

    private Long policyId;

    /**
     * 客户自定义黑名单分组ID列表
     */
    @ColumnType(typeHandler = LongSetToJsonTypeHandler.class)
    private Set<Long> customerWhiteGroupIds;

    /**
     * 客户自定义黑名单分组列表
     */
    private List<CustomerWhiteGroupPO> customerWhiteGroupList;
}
