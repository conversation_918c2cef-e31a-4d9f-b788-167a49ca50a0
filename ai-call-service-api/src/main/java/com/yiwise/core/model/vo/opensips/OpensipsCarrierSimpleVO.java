package com.yiwise.core.model.vo.opensips;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import com.yiwise.core.model.vo.phonenumber.PhoneNumberOpensipsGwAddVO;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class OpensipsCarrierSimpleVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 网关组ID
     */
    private String carrierid;

    /**
     * 网关组名称
     */
    private String description;

    /**
     * 0 Active
     * 1 Inactive
     */
    private Integer state;

    /**
     * 使用方式 N：顺序 W：权重
     */
    private String sortAlg;

    /**
     * 网关
     */
    private String gwIdList;

    private List<PhoneNumberOpensipsGwAddVO> gwListDetail;

    private String createSource;


}
