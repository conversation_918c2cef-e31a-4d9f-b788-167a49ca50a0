package com.yiwise.core.model.vo.operationlog;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import com.yiwise.core.model.enums.OperationLogLogTypeEnum;
import com.yiwise.core.model.enums.OperationLogOperationTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.validate.operationlog.QueryOperationLogValidate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OperationLogAddVO {

    /**
     * 任务ID 必填
     */
    private Long robotCallJobId;

    /**
     * 客户ID 必填
     */
    private Long tenantId;

    /**
     * 用户ID 必填
     */
    private Long currentUserId;

    /**
     * 操作类型 （选填 默认为 批量导入客户）
     */
    private OperationLogOperationTypeEnum operationType;

    /**
     * 操作日志内容 必填
     */
    private String content;
}
