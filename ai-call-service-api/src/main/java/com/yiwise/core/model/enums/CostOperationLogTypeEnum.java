package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>@yiwise.com
 * @date: 2022 06 01 18:57
 */
public enum CostOperationLogTypeEnum implements CodeDescEnum {

    TENANT_PAY_TYPE(0, "付费模式"),

    FREE_DIALOGFLOW_COUNT(1, "赠送话术套数"),
    DIALOGFLOW_COST_WITHOUT_RECORD(2, "话术制作成本（不含录音）"),
    DIALOGFLOW_COST_WITH_RECORD(3, "话术制作成本（含录音）"),
    DIALOGFLOW_COST_WITH_IN_TEN_SENTENCE(4, "话术制作成本（10句以内）"),
    DIALOGFLOW_COST_WITH_IN_FIFTY_SENTENCE(5, "话术制作成本（50句以内）"),
    FREE_DIALOGFLOW_SENTENCE_COUNT(6, "赠送话术修改句"),
    SENTENCE_COST(7, "单句修改成本"),
    AI_FARE(8, "AI外呼报价"),
    COM_FARE(9, "通讯内部结算价（线路）"),

    FREE_SMS_COUNT(10, "是否赠送短信"),
    INDEPENDENT_SET_SMS_PRICE(11, "独立设置短信单价"),
    CUSTOMER_CONFIG_LINE(12, "客户配置线路"),
    SET_QC(13, "智能质检"),
    SMS_CHANNEL(14, "短信通道"),

	CALL_OUT_AUDIO_RETENTION_TIME(15, "AI外呼音频保留时长"),

    VIDEO_UPGRADE_FARE(16,"视频插播单价"),

	SMS_INTERNAL_SETTLEMENT_FARE(17, "短信内部结算价"),

    RECEPTION_FARE(50,"AI通话平台单价"),

    PRIVACY_NUMBER_PHONE_OUT_COST(60, "隐私号月租对外报价"),
    PRIVACY_NUMBER_CALL_OUT_COST(61, "隐私号通话对外报价"),
    PRIVACY_NUMBER_PHONE_INTERNAL_COST(62, "隐私号月租内部结算价"),
    PRIVACY_NUMBER_CALL_INTERNAL_COST(63, "隐私号通话内部结算价"),

    UNKNOWN(999,"未知类型")
    ;

    private final Integer code;
    private final String desc;

    CostOperationLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CostOperationLogTypeEnum getMethodByName(String name){
        for(CostOperationLogTypeEnum item : CostOperationLogTypeEnum.values()){
            if(item.name().equalsIgnoreCase(name)){
                return item;
            }
        }
        return CostOperationLogTypeEnum.UNKNOWN;
    }

    public static String getDescByName(String name){
        for(CostOperationLogTypeEnum item : CostOperationLogTypeEnum.values()){
            if(item.name().equalsIgnoreCase(name)){
                return item.getDesc();
            }
        }
        return CostOperationLogTypeEnum.UNKNOWN.getDesc();
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

}
