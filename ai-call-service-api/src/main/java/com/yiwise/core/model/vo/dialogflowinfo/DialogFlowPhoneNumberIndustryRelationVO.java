package com.yiwise.core.model.vo.dialogflowinfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.DialogFlowPhoneNumberIndustryRelationPO;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DialogFlowPhoneNumberIndustryRelationVO extends DialogFlowPhoneNumberIndustryRelationPO implements ExcelRowModel {

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = new HashMap<>(16);
        map.put(话术一级分类, getDialogFlowIndustry().getDesc());
        map.put(话术二级分类, getDialogFlowSubIndustry().getDesc());
        if (Objects.isNull(getPhoneNumberIndustry())) {
            map.put(线路一级分类, "--");
            map.put(线路二级分类, "--");
        } else {
            map.put(线路一级分类, getPhoneNumberIndustry().getDesc().split("-")[0]);
            map.put(线路二级分类, getPhoneNumberIndustry().getDesc().split("-").length > 1 ? getPhoneNumberIndustry().getDesc().split("-")[1] : "--");
        }
        return map;
    }
}
