package com.yiwise.core.model.vo.dialogflownodestats;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class DialogFlowTotalNodeStatsVO {

    Long tenantId;

    Long dialogFlowNodeId;

    String nodeLabel;

    String nodeName;

    String dialogFlowStepId;

    private LocalDate localDate;

    private Integer hour;

    List<DialogFlowNodeFanOutStatsVO> branchStatsList;

    DialogInterruptStatsTotalVO interruptStats;
}
