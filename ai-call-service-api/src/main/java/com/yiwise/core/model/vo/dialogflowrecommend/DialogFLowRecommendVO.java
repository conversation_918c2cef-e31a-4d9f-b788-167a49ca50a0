package com.yiwise.core.model.vo.dialogflowrecommend;

import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.DialogFLowRecommendPO;
import com.yiwise.core.model.enums.SystemEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class DialogFLowRecommendVO extends DialogFLowRecommendPO implements ExcelRowModel {
    private static final long serialVersionUID = 576153636232445329L;

    /**
     * 更新用户
     */
    private String updateUserName;

    /**
     * 话术推荐id集合
     */
    private List<Long> dialogFlowRecommendIdList;

    /**
     * 问答知识标题集合
     */
    private Set<String> titleSet;

    /**
     * 导入文件的位置
     */
    private String objectName;

    private SystemEnum systemType;

    /**
     * 对应的话术
     */
    private List<String> contentList;

    /**
     * 导入错误明细
     */
    private String errorMessage;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("话术内容", getContent());
        map.put("问答知识标题", getTitle());
        map.put(BatchConstant.Header.错误信息, getErrorMessage());
        return map;
    }
}
