package com.yiwise.core.model.vo.tenant;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.MainBrandPO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;


/**
 * <AUTHOR>
 */
@Data
public class MainBrandVO extends MainBrandPO implements ExcelRowModel {

    /**
     * 前端使用，历史字段问题同mainBrandId
     */
    private Long id;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> rowModelMap = new HashMap<>(8);
        rowModelMap.put(主公司编号, getMainBrandCode());
        rowModelMap.put(主公司名称, getMainBrandName());
        rowModelMap.put(联系人, getContactName());
        rowModelMap.put(联系电话, getContactPhone());
        return rowModelMap;
    }
}
