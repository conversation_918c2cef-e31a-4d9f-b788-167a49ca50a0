package com.yiwise.core.model.vo.robotcalljob;

import com.yiwise.core.model.bo.robotcalljob.WechatWelcomeMsgBO;
import com.yiwise.core.model.enums.OperationLogOperationTypeEnum;
import com.yiwise.core.model.enums.SmsPushEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callin.StaffGroupTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.handler.SmsPushEnumHandler;
import com.yiwise.core.validate.robotcalljob.CreateRobotCallJobValidate;
import com.yiwise.core.validate.robotcalljob.ModifyRobotCallJobValidate;
import lombok.Data;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/7/26
 **/
@Data
public class RobotCallJobModifyVO implements Serializable {
    @Valid
    @NotNull(message = "robotCallJob对象不能为空", groups = {CreateRobotCallJobValidate.class, ModifyRobotCallJobValidate.class})
    private RobotCallJobCompatibleVO robotCallJob;
    /**
     * tenant_phone_number_id
     */
    @NotEmpty(message = "主叫号码不能为空", groups = {CreateRobotCallJobValidate.class})
    @NotNull(message = "主叫号码不能为NULL", groups = {ModifyRobotCallJobValidate.class})
    private List<Long> jobPhoneNumberIdList;
    /**
     * 无限制权限
     */
    private boolean admin = false;
    /**
     * 是否开启一线多并发
     */
    private Boolean enableConcurrency = false;
    /**
     * 并发数
     */
    @NotNull(message = "线路并发数不能为空", groups = {CreateRobotCallJobValidate.class})
    private Integer concurrencyQuota;

    private SystemEnum systemType;

    //短信推送的高级模式
    private Boolean smsSendAdvance;

    @ColumnType(typeHandler = SmsPushEnumHandler.class)
    private SmsPushEnum smsPushType;

    /**
     * 加微欢迎语设置
     */
    private WechatWelcomeMsgBO wechatWelcomeMsgBO;

    /**
     * 外呼计划id
     */
    private Long callOutPlanId;
    /**
     * 是否行内编辑
     */
    private Boolean isLine = false;

    /**
     * 是否修改闪信
     */
    private Boolean modifyFms = true;
    /**
     * 是否修改加微欢迎语
     */
    private Boolean modifyAddWc = true;
    /**
     * 是否修改短信群发
     */
    private Boolean modifySmsJob = true;
    /**
     * 是否修改拨打时间段相关配置
     */
    private Boolean modifyCallDuration = true;
    /**
     * 是否修改线路
     */
    private Boolean modifyLine = true;
    /**
     * 挂机短信是否需要配推送条件
     */
    private Boolean needSmsPushCondition = true;
    /**
     * 是否修改风控
     */
    private Boolean modifyRiskControl = true;
    /**
     * 是否打印外呼计划日志
     */
    private Boolean needCallOutPlanLog = false;
    /**
     * 外呼计划类型
     */
    private OperationLogOperationTypeEnum type;

    /**
     * 模板名称
     */
    private String smsTemplateName;
    /**
     * 模板内容
     */
    private String smsTemplateText;
}
