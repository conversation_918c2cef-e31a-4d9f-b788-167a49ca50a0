package com.yiwise.core.model.vo.openapi.ant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CustomerPersonImportAntResponseVO implements Serializable {

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 成功导入数量
     */
    private Integer importNum;

    /**
     * 错误信息
     */
    private String errorMsg;

}
