package com.yiwise.core.model.bo.mq;

import com.yiwise.core.dal.entity.CallRecordPO;
import com.yiwise.core.model.bo.IntegerIntegerBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @desc 外呼后的其他处理
 */
@Data
public class CallOutOtherMessageBO implements Serializable {
    /**
     * 当前通话数据
     */
    private CallRecordPO callRecord;

    /**
     * 性别分析
     */
    private List<IntegerIntegerBO> userSayOffsetList;

    private Boolean updateGender;

    /**
     * 情绪识别
     */
    private List<String> userSayTextList;
}
