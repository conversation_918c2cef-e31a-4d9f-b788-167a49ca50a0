package com.yiwise.core.model.vo.callin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.ChatDurationEnum;
import com.yiwise.core.model.enums.CsTransferNotifyEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callin.CallInRecordTypeEnum;
import com.yiwise.core.model.enums.callin.CallInResultStatusEnum;
import com.yiwise.core.model.enums.callin.StaffGroupTypeEnum;
import com.yiwise.core.model.enums.handler.CallInResultStatusEnumHandler;
import com.yiwise.core.model.enums.handler.CsTransferNotifyEnumHandler;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import com.yiwise.core.validate.callin.QueryCallInRecordValidate;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/1/14 2:25 PM
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CallInRecordQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 客户名称
     */
    String searchCustomerName;

    String searchCustomerPhone;


    /**
     * 呼入通话记录
     */
    Long searchCallInRecordId;

    List<Long> callRecordIds;

    /**
     * 接听坐席
     */
    String searchReceptionSeatName;

    Long phoneNumberId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    LocalDateTime endTime;

    /**
     * 0：未读，1:已读
     */
    Integer readStatus;

    @ColumnType(typeHandler = CallInResultStatusEnumHandler.class)
    CallInResultStatusEnum resultStatus;

    Long minChatDuration;

    Long maxChatDuration;

    ChatDurationEnum chatDuration;

    Long dialogFlowId;

    Integer readParameter;

    @NotNull(message = "接待类型不能为空", groups = {QueryCallInRecordValidate.class})
    StaffGroupTypeEnum seatType;

    List<StaffGroupTypeEnum> seatTypeList;

    /**
     * 接待场景id
     */
    List<Long> receptionIdList;

    List<CallInResultStatusEnum> resultStatusList;

    CallJobHangupEnum hangupBy;
    /**
     * 人机介入通知状态
     */
    @ColumnType(typeHandler = CsTransferNotifyEnumHandler.class)
    private CsTransferNotifyEnum csTransferNotify;

    /**
     * 坐席组Id
     */
    private Long csStaffGroupId;

    private List<Long> csStaffGroupIdList;

    /**
     * 坐席ID
     */
    private Long csStaffId;

    /**
     * 呼入类型
     */
    private CallInRecordTypeEnum callInRecordType;

    private Long tenantId;

    /**
     * 代理商id
     */
    private Long distributorId;
    /**
     * 客户id集合
     */
    private List<Long> filterTenantIdList;
    private List<Long> filterDistributorIdList;

    /**
     * 是否有人工意向分类
     */
    private Boolean realIntent;

    /**
     * 接听坐席ID
     */
    private List<Long> filterStaffInfoIdList;

    private Long userId;

    private SystemEnum systemType;
}
