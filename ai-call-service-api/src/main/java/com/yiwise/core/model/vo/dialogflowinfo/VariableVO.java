package com.yiwise.core.model.vo.dialogflowinfo;


import com.yiwise.core.model.enums.CustomerPersonFieldTimeTypeEnum;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@ToString
public class VariableVO implements Serializable {

    private Long variableId;

    /**
     * 变量名称
     */
    @NotNull(message = "变量名称不能为空")
    private String name;

    /**
     * 变量类型 1-自定义变量 2-动态变量
     */
    @NotNull(message = "变量类型不能为空")
    private Integer type;

    /**
     * 0:内置变量，1:不是
     */
    private Integer variableType;
    /**
     * 关联话术id
     */
    @NotNull(message = "关联话术id不能为空")
    private Long dialogFlowId;

    /**
     * 保存字段id，默认为0不需要保存
     */
    @NotNull(message = "保存字段id不能为空")
    private Long saveFieldId;

    /**
     * 变量值 用于OPE保存字段
     */
    private String fieldValue;

    /**
     * 变量说明
     */
    private String instructions;

    /**
     * 创建人的id
     */
    private Long createUserId;
    /**
     * 更新人的id
     */
    private Long updateUserId;

    /**
     * 归一格式
     */
    private CustomerPersonFieldTimeTypeEnum fieldTimeType;
}
