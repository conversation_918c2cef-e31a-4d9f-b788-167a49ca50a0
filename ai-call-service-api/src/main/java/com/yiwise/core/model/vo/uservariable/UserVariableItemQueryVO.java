package com.yiwise.core.model.vo.uservariable;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/8
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserVariableItemQueryVO implements Serializable {

    @NotNull
    Long userVariableId;

    @NotNull
    Long recordUserId;

    @NotNull
    Integer pageNum;

    @NotNull
    Integer pageSize;

    /**
     * 模糊查询
     */
    String search;
}
