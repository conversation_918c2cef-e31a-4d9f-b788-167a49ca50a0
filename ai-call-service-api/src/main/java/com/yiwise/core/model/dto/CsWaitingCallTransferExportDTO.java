package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-22 15:39
 */
@Data
public class CsWaitingCallTransferExportDTO implements ExcelRowModel {

    private String name;
    private String phoneNumber;
    private Long customerPersonId;
    private String csStaffName;
    private String transferCsStaffName;
    private Long csStaffId;
    private Long transferCsStaffId;
    private LocalDateTime transferTime;
    private Long tenantId;

    private String errorMessage;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("姓名",getName());
        retMap.put("联系电话", getPhoneNumber());
        retMap.put("转移前坐席", getCsStaffName());
        retMap.put("转移至坐席", getTransferCsStaffName());
        retMap.put("转移时间", getTransferTime());
        retMap.put("错误信息", errorMessage);
        return retMap;
    }
}
