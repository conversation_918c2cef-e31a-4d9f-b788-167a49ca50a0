package com.yiwise.core.model.dto;

import com.yiwise.core.model.enums.CustomerSubIndustryEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * @ClassName TradeSubTypeInfoDTO
 * <AUTHOR>
 * @Date 2019/11/01
 **/
@Data
public class CustomerSubIndustryDTO implements Serializable {
    private String name;
    private String desc;

    public CustomerSubIndustryDTO(CustomerSubIndustryEnum subTypeEnum){
        this.name = subTypeEnum.name();
        this.desc = subTypeEnum.getDesc();
    }

}