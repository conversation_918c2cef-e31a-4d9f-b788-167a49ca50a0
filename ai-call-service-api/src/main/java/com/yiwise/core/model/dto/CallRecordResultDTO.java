package com.yiwise.core.model.dto;

import com.yiwise.core.model.enums.DialStatusEnum;
import com.yiwise.core.model.enums.IntentLevelEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 26/07/2018
 */
public class CallRecordResultDTO implements Serializable {
    private DialStatusEnum resultStatus;
    private IntentLevelEnum intentLevel;

    public DialStatusEnum getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(DialStatusEnum resultStatus) {
        this.resultStatus = resultStatus;
    }

    public IntentLevelEnum getIntentLevel() {
        return intentLevel;
    }

    public void setIntentLevel(IntentLevelEnum intentLevel) {
        this.intentLevel = intentLevel;
    }
}
