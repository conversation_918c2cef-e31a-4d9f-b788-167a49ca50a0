package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelDateUtil;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.CustomerWhiteListPO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * 用来导出通话记录的。导出的通话记录包含通话记录表原本内容+客户+一连串的通话文本
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper=true)
public class CustomerWhiteListExportDTO extends CustomerWhiteListPO implements ExcelRowModel {

    private String userName;


    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();

        retMap.put(客户名, getName());
        retMap.put(联系电话, getPhoneNumber());
        retMap.put(加入原因, getAddType());
        retMap.put(来源, getSourceType());
        retMap.put(备注, getComment());
        retMap.put(加入黑名单时间, getCreateTime());
        retMap.put(剩余有效期, Objects.isNull(getExpiry())? "永久" : ExcelDateUtil.dateDiff(LocalDateTime.now(), getExpiry()));
        retMap.put(创建者, getUserName());
        return retMap;
    }
}
