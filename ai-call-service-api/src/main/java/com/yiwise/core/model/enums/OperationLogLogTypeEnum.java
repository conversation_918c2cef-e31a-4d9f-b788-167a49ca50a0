package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * <AUTHOR>
 * @Date 2018/7/30
 **/
public enum OperationLogLogTypeEnum implements CodeDescEnum {
    // 下面为优化新增的枚举
    //AI外呼任务日志
    ROBOTCALLJOB(0, "任务日志"),
    TENANT(2, "客户"), // ope boss
    DISTRIBUTOR(3, "代理商"), // ope boss
    //团队管理
    USER(4, "用户"),
    ROLE(5, "角色权限"),
    ORGANIZATION(6, "组织架构"),
    ISV(7, "客户中心-ISV"),
    PHONE_NUMBER(8, "电话线路"),
    DIALOG_FLOW(9, "话术"),
    //意向标签
    INTENT_LEVEL_TAG(10, "意向标签分组"),
    //我的客户日志
    CUSTOMER(11, "CRM客户"),
    RECORDER(15, "录音师"),
    //短信任务日志
    SMS(16, "短信"),
    GATEWAY(17, "网关"),
    IVR_NAVIGATION(19,"IVR导航"),
    CALL_RECORD(20, "呼叫记录"),
    CALL_IN(21, "呼入"),
    QUALITY_CONTROL(22, "质检"),
    CALL_POLICY_GROUP(23, "外呼策略组"),
    //标准版话术配置
    CRM_SHOW_DIALOG_FLOW(24, "在crm端展示的话术的操作日志"),
    SYSTEM_MAINTAIN(25, "ope系统维护"),
    DOOR_LOGIN_AICC(26, "OPE登录(AICC)"),
    DOOR_LOGIN(27, "OPE登录"),
    TENANT_STATUS(28, "客户状态"),
    AUTHENTICATION(29, "客户资质审核"),
    AUTHENTICATION_DISTRIBUTOR_CONFIG(30, "代理商资质认证配置"),
    TTS_VOICE_BIND(38, "音色绑定"),
    ASR_MODEL(39, "ASR模型管理"),
    //预测式外呼任务日志
    BATCH_JOB(40, "预测试外呼任务日志"),

    //AICC日志类型
    AI_CONTACT_HISTORY(45, "AI外呼联系历史"),
    CALL_OUT_CONFIG(46, "AI外呼通用设置"),
    INTELLIGENT_ASSISTANT(48, "智能辅助"),
    MANUAL_CONTACT_HISTORY(49, "人工外呼联系历史"),
    SEAT_MANAGEMENT(50, "语音坐席管理"),
    RECEPTION_SCENE(51, "接待场景日志"),
    CALL_IN_CONTACT_HISTORY(52, "呼入联系历史"),
    SMS_TEMPLATE(54, "短信模板创建日志"),
    SMS_SEND_HISTORY(55, "短信发送历史"),
    BLACKLIST_MANAGEMENT(58, "黑名单管理"),
    CUSTOMER_CONTACT_HISTORY(59, "客户中心联系历史"),
    PHONE_SMS_CONFIG(61, "线路/短信设置"),
    ACCOUNT(62, "账号信息"),
    QC(63, "质检"),
    FILTER_STRATEGY(64, "过滤策略"),
    TENANT_CALL_INTERCEPT(65, "外呼过滤"),

    //纷享销客日志
    FENXIANGXIAOKE_OPERATION(66, "纷享销客操作"),
    //外呼计划
    CALL_OUT_PLAN(67, "外呼计划"),
    WHITELIST_MANAGEMENT(68, "白名单管理"),

    DOOR_LOGIN_BOSS_TO_AICC(69, "BOSS登录AICC"),

	CALL_OUT_RISK_CONTROL(70, "外呼风控"),
	SMS_RISK_CONTROL(71, "短信风控"),

    RSA_KEY_UPDATE(72,"修改RSA密钥"),
    CALLBACK_TOKEN_KEY_UPDATE(73,"修改事件订阅（回调）配置"),
    OPEN_API(74,"接口对接")
    ;

    private Integer code;
    private String desc;

    OperationLogLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
