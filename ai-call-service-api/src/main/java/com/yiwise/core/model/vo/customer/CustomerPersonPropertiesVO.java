package com.yiwise.core.model.vo.customer;

import com.yiwise.core.dal.entity.CustomerPersonPO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.validate.customerPerson.CustomerPersonPropertiesUpdateValidate;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/10/24
 **/
public class CustomerPersonPropertiesVO implements Serializable {
    @Valid
    @NotEmpty(message = "修改列表不能为空！", groups = {CustomerPersonPropertiesUpdateValidate.class})
    private List<CustomerPersonPO> customerPersonList;

    private SystemEnum systemType;
    public List<CustomerPersonPO> getCustomerPersonList() {
        return customerPersonList;
    }

    public void setCustomerPersonList(List<CustomerPersonPO> customerPersonList) {
        this.customerPersonList = customerPersonList;
    }

    public SystemEnum getSystemType() {
        return systemType;
    }

    public void setSystemType(SystemEnum systemType) {
        this.systemType = systemType;
    }
}
