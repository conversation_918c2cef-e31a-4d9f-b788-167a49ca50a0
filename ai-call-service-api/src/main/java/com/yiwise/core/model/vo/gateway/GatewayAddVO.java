package com.yiwise.core.model.vo.gateway;

import com.yiwise.core.model.dto.LineStatusDTO;
import com.yiwise.core.model.vo.phonenumber.BasePhoneNumberAddVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2018/11/27
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayAddVO extends BasePhoneNumberAddVO implements Serializable {

    @NotNull(message = "设备编号为空")
    private String deviceId;

    //@NotNull(message = "设备名称为空")
    private String deviceName;

    private String deviceBrand;

    //@NotNull(message = "端口个数为空")
    //@Max(value = 64, message = "端口个数过多")
    private Integer portNumber;

    /**
     * 线路状态
     */
    private LineStatusDTO lineStatus;

    private Boolean supportCsSeat = true;

}
