package com.yiwise.core.model.bo.export;

import com.yiwise.core.model.vo.financestats.FinanceLineStatsVO;

import java.util.Map;

import static com.yiwise.core.config.TableUrlConstant.APIOPE_FINANCE_STATS_TENANTLINEFORQIYU;

public class FinanceLineStatsForQiyuExportBO extends FinanceLineStatsVO implements CommonExportBO {
    @Override
    public Map<String, Object> getRowModelMap() {
        return getRowModelExportMap(APIOPE_FINANCE_STATS_TENANTLINEFORQIYU);
    }

    public String getDistributorNameExport() {
        return super.getDistributorName();
    }

    public String getTenantNameExport() {
        return super.getTenantName();
    }

    public String getLinkNameExport() {
        return super.getLinkName();
    }

    public Long getBillChatTimeExport() {
        return super.getBillChatTime();
    }

    public String getBalanceExport() {
        return money2Yuan(super.getSumCost());
    }
}
