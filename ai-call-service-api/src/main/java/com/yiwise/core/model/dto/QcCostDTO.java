package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.model.vo.qc.QcCostVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

@Data
@EqualsAndHashCode(callSuper=true)
public class QcCostDTO extends QcCostVO implements ExcelRowModel {

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();
        Double money = Double.valueOf(getMoney()) / 1000;
        Double minutes = getDuration() / 60000.0;
        //String duration = String.format("%.3f分钟（%d秒）", minutes, getDuration());
        retMap.put(时间, getTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
        retMap.put(任务名称, getJobName());
        retMap.put(数量, getDuration());
        retMap.put(金额, money);

        return retMap;
    }

}
