package com.yiwise.core.model.vo.tenant;

import com.yiwise.core.model.enums.CommonStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantSmsShowPriceVO implements Serializable {

    private Long tenantId;

    /**
     * 短线单价（三位小数，单位元）
     */
    private Double price;

    private CommonStatusEnum status;
}
