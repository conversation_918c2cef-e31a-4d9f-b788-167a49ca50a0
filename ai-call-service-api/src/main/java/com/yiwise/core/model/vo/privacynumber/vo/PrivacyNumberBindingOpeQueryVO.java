package com.yiwise.core.model.vo.privacynumber.vo;

import com.yiwise.core.model.enums.AuthenticationStateEnum;
import com.yiwise.core.model.enums.EnabledStatusEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2021/08/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrivacyNumberBindingOpeQueryVO extends AbstractQueryVO {

	private LocalDate startDate;

	private LocalDate endDate;

	private String numberA;

	private AuthenticationStateEnum authenticationState;

	private EnabledStatusEnum enabledStatus;

	private Long tenantId;

	private Long privacyNumberOperatorId;

	private String numberX;

	private String bindingId;

	/**
	 * 发出请求的员工id
	 */
	private Long userId;
}
