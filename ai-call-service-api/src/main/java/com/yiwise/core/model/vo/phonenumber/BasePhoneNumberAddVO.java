package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.dal.entity.PhoneNumberPO;
import com.yiwise.core.model.enums.MobileOperatorEnum;
import com.yiwise.core.model.enums.handler.UseDialplanTypeEnumHandler;
import com.yiwise.core.model.enums.handler.base.MapToJsonTypeHandler;
import com.yiwise.core.model.enums.handler.base.MobileOperatorListToJsonTypeHandler;
import com.yiwise.core.model.enums.phonenumber.UseDialplanTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import static com.yiwise.core.helper.PhoneNumberHelper.REGEX_IP_ADDRESS;

/**
 * <AUTHOR>
 * @create 2018/08/17
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class BasePhoneNumberAddVO extends PhoneNumberOwnerVO implements Serializable {

    // 从必填改为了非必填, 增加默认值避免数据库做兼容
    private String defaultCallPrefix = "";

    private String otherCallPrefix;

    private String province;

    private String city;

    private List<String> cities;

    private String remark;

    private String selfRemark;

    // 自动绑定到租户时需要
    private Double localBillRate;

    // 自动绑定租户时需要
    @Deprecated
    private Double otherBillRate;

    /**
     * 后端传参
     */
    // 创建者的代理商id
    private Long createdByDistributorId;

    // 地区码
    private String areaCode;

    /**
     * Freeswitch服务器分组信息ID
     */
    private Long freeswitchGroupId;

    private String dingTalkAlertUrl;

    @Pattern(regexp = REGEX_IP_ADDRESS, message = "线路IP不正确")
    private String lineIp;

    private Integer linePort;

    @ColumnType(typeHandler = MapToJsonTypeHandler.class)
    private Map<String, String> variableSet;

    // @NotNull(message = "线路配置dial plan类型不能为空")
    @ColumnType(typeHandler = UseDialplanTypeEnumHandler.class)
    private UseDialplanTypeEnum useDialplanType;

    private Integer alertCountThreshold;

    private Boolean operatorRestriction;
    @ColumnType(typeHandler = MobileOperatorListToJsonTypeHandler.class)
    private List<MobileOperatorEnum> mobileOperator;

    /**
     * 运营商和外呼地区限制
     */
    private PhoneNumberPO.OperatorAndArea operatorAndArea;

    /**
     * 运营商和外呼地区限制开关
     */
    private Boolean operatorAndAreaSwitch;

    /**
     * 线路caps限制
     */
    private Integer caps;
}
