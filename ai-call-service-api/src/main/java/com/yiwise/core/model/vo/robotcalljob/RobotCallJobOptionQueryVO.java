package com.yiwise.core.model.vo.robotcalljob;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class RobotCallJobOptionQueryVO implements Serializable {

    /**
     * 客户ID
     */
    private Long tenantId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 字段ID（变量）
     */
    private Long robotCallJobFieldId;

    /**
     * 选项ID列表 (常量列表)
     */
    private List<Long> optionIdList;

    /**
     * 已选择的任务ID列表
     */
    private List<Long> robotCallJobIdList;

}
