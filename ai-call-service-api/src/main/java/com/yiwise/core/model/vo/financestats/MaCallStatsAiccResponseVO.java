package com.yiwise.core.model.vo.financestats;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;


@Data
public class MaCallStatsAiccResponseVO {

	private List<Item> list;
	private Total total;

	@Data
	public static class Item implements ExcelRowModel {

		@DateTimeFormat(pattern = "yyyy-MM-dd")
		private LocalDate date;

		private String maFlowName;

		@JsonIgnore
		private Integer year;
		@JsonIgnore
		private Integer month;
		@JsonIgnore
		private Integer day;

		@JsonIgnore
		private Long phoneNumberId;

		private Long dialogFlowId;

		/**
		 * 外呼数
		 */
		private Long totalCompleted = 0L;

		/**
		 * 接通数
		 */
		private Long totalAnsweredCall = 0L;


		/**
		 * 平均通话时长
		 */
		private Double averageCallTime = 0d;

		/**
		 * 通话时长
		 */
		private Long chatDuration = 0L;

		/**
		 * 计费分钟数
		 */
		private Long billChatDuration = 0L;

		/**
		 * 计费接通数
		 */
		private Long billingAnsweredCall = 0L;

		/**
		 * 计费单价
		 */
		private Double billingUnitPrice = 0d;

		/**
		 * 振铃计费通数
		 */
		private Long billRingCount = 0L;

		/**
		 * 振铃计费单价
		 */
		private Double billingRingUnitPrice = 0d;

		/**
		 * 费用消耗
		 */
		private Double callCost = 0d;
		@JsonIgnore
		private Long callCostLong = 0L;

		private String phoneNumberName;

		private String dialogFlowName;

		private String payType;

		private String maFare;

		@Override
		public Map<String, Object> getRowModelMap() {
			Map<String, Object> map = new HashMap<>();

			map.put(时间, date);
			map.put(流程名称, maFlowName);
			map.put(线路名称, phoneNumberName);
			map.put(外呼数, totalCompleted);
			map.put(接通数, totalAnsweredCall);
			map.put(平均通话时长, averageCallTime);
			map.put(计费分钟数, billChatDuration);
			map.put(计费接通数, billingAnsweredCall);
			map.put(计费单价, billingUnitPrice);
			map.put(话术id, dialogFlowId);
			map.put(话术名称, dialogFlowName);
			map.put(接听数_通, totalAnsweredCall);
			map.put(计费时长_分钟, billChatDuration);
			map.put(计费单价_分钟, billingUnitPrice);
			map.put(振铃计费单价, billingRingUnitPrice);
			map.put(振铃计费通数, billRingCount);
			map.put(费用消耗, callCost);
			return map;
		}
	}

	@Data
	public static class Total implements ExcelRowModel {

		private String maFlowName;

		/**
		 * 总外呼数
		 */
		private Long totalCompleted = 0L;

		/**
		 * 总接听数
		 */
		private Long totalAnsweredCall = 0L;

		/**
		 * 平均通话时长
		 */
		private Double averageCallTime = 0d;

		/**
		 * 计费分钟数
		 */
		private Long totalBillChatDuration = 0L;

		/**
		 * 计费接通数
		 */
		private Long totalBillingAnsweredCall = 0L;

		/**
		 * 计费单价
		 */
		private Long totalBillingUnitPrice = 0L;

		/**
		 * 费用消耗
		 */
		private Double callCost = 0d;
		@JsonIgnore
		private Long callCostLong = 0L;

		/**
		 * 振铃计费通数
		 */
		private Long billRingCount = 0L;

		/**
		 * 振铃计费单价
		 */
		private Double billingRingUnitPrice = 0d;

		@Override
		public Map<String, Object> getRowModelMap() {
			Map<String, Object> map = new HashMap<>();
			map.put(时间, "总计");
			map.put(流程名称, "--");
			map.put(线路名称, "--");
			map.put(话术id, "--");
			map.put(话术名称, "--");
			map.put(接听数_通, totalAnsweredCall);
			map.put(计费时长_分钟, totalBillChatDuration);
			map.put(计费单价_分钟, "--");
			map.put(振铃计费单价, "--");
			map.put(振铃计费通数, billRingCount);
			map.put(费用消耗, callCost);
			return map;
		}
	}
}
