package com.yiwise.core.model.vo.customerleveltag;

import com.yiwise.core.dal.entity.CustomerLevelTagPO;
import com.yiwise.core.model.dto.CustomerLevelTagDetailDto;
import com.yiwise.core.model.enums.CustomerLevelTagEnum;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CustomerLevelTagVO extends CustomerLevelTagPO implements Serializable {
//    private CustomerLevelTagEnum customerLevelTagEnum;
    private List<CustomerLevelTagDetailDto> details;
}
