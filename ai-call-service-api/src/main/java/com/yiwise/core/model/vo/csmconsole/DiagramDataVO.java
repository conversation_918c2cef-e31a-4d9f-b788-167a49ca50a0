package com.yiwise.core.model.vo.csmconsole;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

@Data
public class DiagramDataVO {
    /**
     * x轴显示名
     */
    private String dataName;
    /**
     * 计数
     */
    private Long count;
    /**
     * 百分数
     */
    private Double percent;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    public DiagramDataVO(String dataName, Long count){
        this.dataName = dataName;
        this.count = count;
    };

    public DiagramDataVO(){}
}
