package com.yiwise.core.model.enums.authentication.handler;

import com.yiwise.core.model.enums.authentication.AuthenticationCertTypeEnum;
import com.yiwise.base.common.handler.CodeIdentifyEnumHandler;
import com.yiwise.core.model.enums.ope.AccountStatusEnum;

/**
*
* <AUTHOR> yangde<PERSON>
* @date : 2020-08-05 17:18
*/
public class AuthenticationCertTypeEnumHandler extends CodeIdentifyEnumHandler<AuthenticationCertTypeEnum> {
    public AuthenticationCertTypeEnumHandler() {
        super(AuthenticationCertTypeEnum.class);
    }
}

