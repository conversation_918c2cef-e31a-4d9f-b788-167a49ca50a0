package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;
import lombok.ToString;

/**
 * 修改项
 */
@ToString
public enum OpeDistributorBatchEditEnum implements CodeDescEnum {
    AI_ROBOT(0,"AI坐席数"),
    SKIP_PHONE_VALIDATION(1,"导入客户校验"),
    ENCRYPT_TYPE(2,"加密方式"),
    PHONE_NUMBER_ID(3,"绑定线路"),
    SMS_PLATFORM(4,"短信通道设置"),
    SMS_PRICE(5,"独立设置短信单价"),
    ENABLE_RECHARGE_ONLINE(6, "AICC在线充值"),
    AUTO_SERVICE(7, "服务团队"),
    ;

    private Integer code;
    private String desc;

    OpeDistributorBatchEditEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

}
