package com.yiwise.core.model.vo.folder;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: wux<PERSON><PERSON><PERSON>@yiwise.com
 * @date: 2022 07 20 15:10
 */
@Data
public class RobotCallJobFolderDragSortVO implements Serializable {

    /**
     * 拖拽后的 前一个文件
     */
    private Long preFolderId;

    /**
     * 拖拽的文件
     */
    @NotNull
    private Long dragFolderId;

    /**
     * 拖拽后的 后一个文件
     */
    private Long afterFolderId;

    private Long tenantId;

    private Long userId;

    /**
     * 是否需要重新排序
     */
    private boolean reorder = false;
}
