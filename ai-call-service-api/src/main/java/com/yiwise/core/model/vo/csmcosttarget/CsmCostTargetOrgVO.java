package com.yiwise.core.model.vo.csmcosttarget;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.SortedMap;

/**
 * 小组消耗目标
 * <AUTHOR>
 * @create 2023/03/10
 */
@Data
public class CsmCostTargetOrgVO {

	/**
	 * 目标年份
	 */
	private Integer year;
	/**
	 * 小组年目标
	 */
	private Long cost;
	/**
	 * 小组季目标, 1-4
	 */
	private SortedMap<Integer, Long> orgSeasonTargets;
	/**
	 * 小组月目标, 1-12
	 */
	@NotNull
	private SortedMap<Integer, Long> orgMonthTargets;
}
