package com.yiwise.core.model.vo.filteredtask;

import com.yiwise.core.model.enums.FilterTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 4/12/24
 */
@Data
public class FilteredCallTaskResultVO {
    

    /**
     * TINYINT(3)
     * 过滤类型
     */
    private String filterType;

    /**
     * 黑名单分组id
     */
    private String blackGroupName;

    private Integer count;
    
    private BigDecimal percent;
    
    private List<FilteredCallTaskResultVO> filteredCallTaskResultVOList;
}
