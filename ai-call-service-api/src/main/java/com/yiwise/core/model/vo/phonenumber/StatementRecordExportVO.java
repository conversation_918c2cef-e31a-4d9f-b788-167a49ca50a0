package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.PhoneNumberSupplierStatementPO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/2/22
 * @class <code>StatementRecordExportVO</code>
 * @see
 * @since JDK1.8
 */
@Data
public class StatementRecordExportVO extends PhoneNumberSupplierStatementPO implements ExcelRowModel {

    private String userName;

    private BigDecimal preAmount;

    @Override
    public Map<String, Object> getRowModelMap() {
        return null;
    }
}
