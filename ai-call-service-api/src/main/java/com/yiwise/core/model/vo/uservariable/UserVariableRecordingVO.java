package com.yiwise.core.model.vo.uservariable;

import com.yiwise.core.helper.objectstorage.AddOssPrefix;
import lombok.Data;

import java.io.Serializable;

/**
 * Created on 2019-04-15.
 *
 * <AUTHOR>
 * email <EMAIL>
 * description 上传的录音，录音进度
 */
@Data
public class UserVariableRecordingVO implements Serializable {
    /**
     * 录音路径
     */
    private String audioUrl;

    /**
     * 录音的完整路径
     */
    @AddOssPrefix
    private String prefixAudioUrl;

    /**
     * 变量的值
     */
    private String variableValue;

    private Long userVariableItemId;

    private String variableContent;

    /**
     * 音量
     */
    private Long volume;

    /**
     * 时长
     */
    private Integer duration;

    public UserVariableRecordingVO() {

    }

    public UserVariableRecordingVO(String audioUrl, String prefixAudioUrl, String variableValue) {
        this.audioUrl = audioUrl;
        this.prefixAudioUrl = prefixAudioUrl;
        this.variableValue = variableValue;
    }

    public UserVariableRecordingVO(String audioUrl, String prefixAudioUrl, String variableValue, Long userVariableId) {
        this.audioUrl = audioUrl;
        this.prefixAudioUrl = prefixAudioUrl;
        this.variableValue = variableValue;
        this.userVariableId = userVariableId;
    }

    private Long userVariableId;
}