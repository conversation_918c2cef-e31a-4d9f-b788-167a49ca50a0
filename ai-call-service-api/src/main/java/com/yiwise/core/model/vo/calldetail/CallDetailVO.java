package com.yiwise.core.model.vo.calldetail;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName CallDetailVO
 * <AUTHOR>
 * @Date 2019/3/11
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CallDetailVO implements Serializable {

    /**
     * 通话详情的id
     */
    @NotNull(message = "通话详情的id不能为空！")
    private Long callDetailId;

    /**
     * 修改后的文本
     */
    @NotEmpty(message = "修改后的文本不能为空！")
    private String afterText;

    private Long createUserId;

    /**
     * 期望的意向分支uid
     */
    private String intentBranchUid;

    /**
     * 期望的问答知识id
     */
    private String robotKnowledgeId;

    private Long tenantId;

    /**
     * 这个类型暂时代替区别是之前的意图还是后面加的通用意图，0是之前的，1是通用意图
     */
    private Integer intentType;
}
