package com.yiwise.core.model.vo.openapi.qiyu;

import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.model.enums.RoleTypeEnum;
import com.yiwise.core.validate.openapi.QiyuAddUserValidate;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UserQiyuVO extends UserPO implements Serializable {

    @NotNull(message = "用户登录名不能为空！", groups = {QiyuAddUserValidate.class})
    private String userNumber;

    private RoleTypeEnum roleType;


}
