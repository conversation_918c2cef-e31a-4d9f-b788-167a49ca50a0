package com.yiwise.core.model.vo.dialogflowinfo;

import com.yiwise.core.dal.entity.DialogFlowDistributorRelationPO;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

/**
 * @ClassName DialogFlowDistributorRelationVO
 * <AUTHOR>
 * @Date 2019/2/25
 **/
@Data
@ToString(callSuper = true)
public class DialogFlowDistributorRelationVO extends DialogFlowDistributorRelationPO implements Serializable {
    /**
     * 是否消耗话术库存
     */
    private Boolean flag;

    /**
     * 消耗库存套数
     */
    private Integer count;
}
