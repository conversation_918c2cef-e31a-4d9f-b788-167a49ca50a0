package com.yiwise.core.model.vo.miniapp;

import com.yiwise.core.model.enums.DialogFlowTypeEnum;
import com.yiwise.core.model.enums.GenderEnum;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Date 2018/11/12
 **/
@Data
@ToString
public class FreeUserDirectlyCallVO implements Serializable {
    @NotEmpty(message = "电话号码不能为空")
    private String phoneNumber;
    @NotEmpty(message = "名称不能为空")
    private String name;
    /**
     * 信用卡尾号
     */
    private String creditCardLastNumber;
    /**
     * 欠款金额
     */
    private String debtMoney;
    /**
     * 逾期天数
     */
    private String overDays;
    /**
     * 地址
     */
    private String address;
    /**
     * 电费金额
     */
    private String electricityFees;
    /**
     * 户号
     */
    private String houseNumber;
    private GenderEnum gender;
    private String companyName;
    private String industry;
    private String openId;
    private DialogFlowTypeEnum dialogFlowType;
}
