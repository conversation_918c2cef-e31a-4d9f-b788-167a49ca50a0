package com.yiwise.core.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

@Data
public class RobotCallJobActiveTimeDTO implements Serializable {

    private Long robotCallJobId;

    private String robotCallJobName;

    private LocalTime dailyStartTime;

    private LocalTime dailyEndTime;

    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private List<Date> activeDateList;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime firstCallDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime lastCallDate;
}
