package com.yiwise.core.model.vo.scrm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/11/08
 */
@Data
public class OrgUserNode {

	private Long organizationId;
	private Boolean hasChildren;
	//给前端判断主组织下是有部门
	private Boolean hasOrg;

	private Long userId;
	private String avatarUrl;

	private String name;
	/**
	 * user是否全部返回
	 */
	private Boolean allUserLoaded;

	private String wechatCpUid;

	private List<OrgUserNode> nodes;

}
