package com.yiwise.core.model.vo.privacynumber.vo;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.privacynumber.PrivacyNumberBindingPO;
import com.yiwise.core.model.enums.EnabledStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

@Data
@EqualsAndHashCode(callSuper = true)
public class PrivacyNumberBindingOpeVO extends PrivacyNumberBindingPO implements ExcelRowModel {

    private String operatorName;

	private String province;
	private String city;

	private String tenantName;

	@Override
	public Map<String, Object> getRowModelMap() {
		Map<String, Object> map = new HashMap<>();
		map.put(ID, getPrivacyNumberBindingId());
		map.put(来源, getSource().getDesc());
		map.put(绑定id, getBindingId());
		map.put(运营商名称, getOperatorName());
		map.put(X号码, getNumberX());
		map.put(X号码归属地, getProvince() + getCity());
		map.put(A号码, getNumberA());
		map.put(创建时间, getCreateTime());
		map.put(绑定状态, EnabledStatusEnum.ENABLE.equals(getEnabledStatus()) ? "已绑定" : "已解绑");
		map.put(解绑时间, getUnbindTime());
		map.put(实名认证状态, getAuthenticationState().getDesc());
		map.put(实名认证回调信息, getAuthenticationInfo());
		map.put(实名信息回调时间, getAuthenticationTime());
		map.put(归属客户, getTenantName());
		map.put(AX绑定响应信息一知, getYiwiseResponse());
		map.put(AX绑定响应信息供应商, getOperationResponse());
		return map;
	}
}
