package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.dal.entity.DistributorPhoneNumberPO;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description DistributorPhoneNumberPO + PhoneNumberPO的phoneName
 * @create 2018/12/05
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class DistributorPhoneNumberPOWithName extends DistributorPhoneNumberPO implements Serializable {
    private String phoneName;
    private String phoneNumber;
}
