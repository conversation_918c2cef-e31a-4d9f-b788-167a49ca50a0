package com.yiwise.core.model.vo.callin;

import com.yiwise.core.validate.csbatchcalljob.AddCustomerToCsBatchCallJobValidate;
import com.yiwise.core.validate.robotcalltask.AddCustomerPersonsToRobotCallJobValidate;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2020/7/14
 */
@Data
public class CallInRecordExportCustomerVO extends CallInRecordQueryVO implements Serializable {

    @NotNull(message = "targetRobotCallJobId不可以是null", groups = AddCustomerPersonsToRobotCallJobValidate.class)
    private Long targetRobotCallJobId;

    @NotNull(message = "targetCsBatchCallJobId不可以是null", groups = AddCustomerToCsBatchCallJobValidate.class)
    private Long targetCsBatchCallJobId;


    private Set<Long> callInRecordIds;
}
