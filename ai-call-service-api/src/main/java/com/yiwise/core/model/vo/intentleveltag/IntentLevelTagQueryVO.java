package com.yiwise.core.model.vo.intentleveltag;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2018/12/27
 **/
@Data
@ToString(callSuper = true)
public class IntentLevelTagQueryVO extends AbstractQueryVO implements Serializable {
    private Long tenantId;
    private Long distributorId;
    private String name;
}
