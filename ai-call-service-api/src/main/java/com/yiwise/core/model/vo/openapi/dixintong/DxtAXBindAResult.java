package com.yiwise.core.model.vo.openapi.dixintong;

import com.yiwise.base.model.exception.ComErrorCode;
import lombok.Data;

/**
 * Created by 昌夜 on 2021-05-25.
 */
@Data
public class DxtAXBindAResult {
    String code;
    String msg;
    DxtAXBindAResultData data;

    public static DxtAXBindAResult fail(ComErrorCode comErrorCode, String errorMsg) {
        DxtAXBindAResult result = new DxtAXBindAResult();
        result.setCode(String.valueOf(comErrorCode.getCode()));
        result.setMsg(errorMsg);
        result.setData(new DxtAXBindAResultData());
        return result;
    }

    public static DxtAXBindAResult success(DxtAXBindAResultData data) {
        DxtAXBindAResult result = new DxtAXBindAResult();
        result.setCode("0");
        result.setMsg("success");
        result.setData(data);
        return result;
    }
}
