package com.yiwise.core.model.vo.wechat;

import lombok.Data;

import java.io.Serializable;

/**
*text、image、link和miniprogram四者不能同时为空；
 * text与另外三者可以同时发送，此时将会以两条消息的形式触达客户
 * image、link和miniprogram只能有一个，如果三者同时填，则按image、link、miniprogram的优先顺序取参，也就是说，如果image与link同时传值，则只有image生效。
 * media_id和pic_url只需填写一个，两者同时填写时使用media_id，二者不可同时为空。
* <AUTHOR> yangdehong
* @date : 2020-09-01 20:55
*/
@Data
public class WechatSendWelcomeMsgBO implements Serializable {

    private static final long serialVersionUID = -857626660996669066L;

    private String welcome_code;

    private TextBO text;

    private ImageBO image;

    private LinkBO link;

    private MiniprogramBO miniprogram;

    public WechatSendWelcomeMsgBO(String welcome_code, String text) {
        this.welcome_code = welcome_code;
        this.text = new TextBO(text);
    }

    public WechatSendWelcomeMsgBO(String welcome_code, String text, String media_id, String pic_url) {
        this.welcome_code = welcome_code;
        this.text = new TextBO(text);
        this.image = new ImageBO(media_id, pic_url);
        this.link = new LinkBO("", "", "", "");
        this.miniprogram = new MiniprogramBO("", "", "", "");
    }

    @Data
    public class TextBO {
        public TextBO(String content) {
            this.content = content;
        }
        private String content;
    }

    @Data
    public class ImageBO {
        public ImageBO(String media_id, String pic_url) {
            this.media_id = media_id;
            this.pic_url = pic_url;
        }

        private String media_id;
        private String pic_url;
    }

    @Data
    public class LinkBO {
        public LinkBO(String title, String picurl, String desc, String url) {
            this.title = title;
            this.picurl = picurl;
            this.desc = desc;
            this.url = url;
        }
        private String title;
        private String picurl;
        private String desc;
        private String url;
    }

    @Data
    public class MiniprogramBO {
        public MiniprogramBO(String title, String pic_media_id, String appid, String page) {
            this.title = title;
            this.pic_media_id = pic_media_id;
            this.appid = appid;
            this.page = page;
        }
        private String title;
        private String pic_media_id;
        private String appid;
        private String page;
    }

}
