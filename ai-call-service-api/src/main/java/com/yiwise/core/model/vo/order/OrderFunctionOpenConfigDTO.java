package com.yiwise.core.model.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 3/5/24
 */
@Data
public class OrderFunctionOpenConfigDTO {
    public static final String COLLECTION_NAME = "orderFunctionOpenConfig";
    @Id
    private String id;
    /**
     * 是否自动拉取订单
     */
    private Integer autoPullOrderOpen;
    /**
     * 开启自动拉取抖音订单
     */
    private Integer autoPullDouyinOrderOpen;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 是否开启ma订单
     */
    private Integer maOrderOpen;

    /**
     * 历史订单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderStartTime;

    /**
     * 历史订单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime taoOrderStartTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime yzOrderStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime minappOrderStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime platformOrderStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime douyinOrderStartTime;

    /**
     * 小程序订单创建客户
     */
    private Integer minappCreateCustomer;
    /**
     * 有赞订单创建客户
     */
    private Integer yzCreateCustomer;
    /**
     * 淘宝订单创建客户
     */
    private Integer taoCreateCustomer;
    /**
     * 平台订单创建客户
     */
    private Integer platformCreateCustomer;

    /**
     * 抖音订单创建客户
     */
    private Integer douyinCreateCustomer;
    /**
     * 订单自定义解密额度
     */
    private Integer orderLimit;

    /**
     * 自动解密订单打开
     */
    private Integer autoDecryptionOpen;
    /**
     * 已使用额度
     */
    private Integer usedLimit;
}
