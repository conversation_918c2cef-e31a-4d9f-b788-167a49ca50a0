package com.yiwise.core.model.vo.customer;

import com.yiwise.core.model.enums.CustomerLevelEnum;
import com.yiwise.core.model.enums.EnabledStatusEnum;
import com.yiwise.core.model.enums.ScanConditionEnum;
import com.yiwise.core.model.enums.SyncStatusEnum;
import com.yiwise.core.model.enums.handler.EnabledStatusEnumHandler;
import com.yiwise.core.model.enums.handler.ScanConditionEnumHandler;
import com.yiwise.core.model.enums.handler.SyncStatusEnumHandler;
import com.yiwise.core.model.enums.handler.base.MapToJsonTypeHandler;
import lombok.Data;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.ColumnType;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/05/25
 */
@Data
@NoArgsConstructor
public class EnterpriseArticleVO implements Serializable {

    private Long enterpriseArticleId;

    private Long tenantId;

    /**
     * 文章标题
     */
    private String articleTitle;

    /**
     * 文章URL
     */
    private String articleUrl;

    /**
     * 公众号名称
     */
    private String officialAccountsName;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 0未知，1较差，2一般，3优质
     */
    private CustomerLevelEnum customerLevel;

    private Long customerLevelTagId;

    /**
     * 上传图片url
     */
    private String uploadImgUrl;

    /**
     * 备注
     */
    private String remark;

    @ColumnType(typeHandler = SyncStatusEnumHandler.class)
    private SyncStatusEnum syncStatus;

    @ColumnType(typeHandler = EnabledStatusEnumHandler.class)
    private EnabledStatusEnum enabledStatus;

    /**
     * 自动打标签 0:不打,1:自动打
     */
    private Integer autoLevelTag;

    @ColumnType(typeHandler = ScanConditionEnumHandler.class)
    private ScanConditionEnum scanCondition;
    /**
     * 浏览时间（X）秒，自动打标签
     */
    private Long browseTime;

    /**
     * 名片json
     */
    @ColumnType(typeHandler = MapToJsonTypeHandler.class)
    private Map<String, String> visitingCard;
    Long userId;
    String createUserName;

    //分享次数
    Long shareCount;
    //浏览人数
    Long scanCount;

    //总分享次数
    Long totalShareCount;

    //总浏览人数
    Long totalScanCount;
    //分享链接
    String shareLink;

}
