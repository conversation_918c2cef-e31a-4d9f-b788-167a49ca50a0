package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.model.vo.customerrealwhitelist.CustomerRealWhiteListQueryVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.HashMap;
import java.util.Map;


import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper=true)
public class CustomerRealWhiteListExportDTO extends CustomerRealWhiteListQueryVO implements ExcelRowModel {


    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();

        retMap.put(客户名, getCustomerName());
        retMap.put(联系电话, getPhoneNumber());
        retMap.put(备注, getComment());
        retMap.put(号码归属地, getPhoneLocation());
        retMap.put(加入时间, getCreateTime());
        return retMap;
    }
}
