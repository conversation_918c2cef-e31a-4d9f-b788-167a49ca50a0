package com.yiwise.core.model.vo.tencent.market;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/02/02
 */
@Data
public class TencentMarketCreateInstanceResponseVO {

    /**
     * 实例标识 ID，服务商提供的唯一标识。不可为空，长度最长为11位。当为"0"时，系统会认为是异步发货。
     */
    String signId;
    /**
     * 应用信息
     */
    TencentMarketAppInfo appInfo;
    /**
     * 自定义数据，会显示在实例详情中
     */
    List<Entry> additionalInfo;

    @Data
    @AllArgsConstructor
    public static class Entry {
        String name;
        String value;
    }
}
