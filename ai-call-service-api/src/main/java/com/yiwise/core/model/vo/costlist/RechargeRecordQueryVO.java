package com.yiwise.core.model.vo.costlist;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.AccessLevel;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RechargeRecordQueryVO extends AbstractQueryVO implements Serializable {
    Integer searchType = 1; // 1: 客户名, 2: 联系电话, 3: 联系人
    String searchValue;
    String searchName;
    String searchPhone;
    String searchLinkman;
    Integer customerType = 0;// 0: 全部,1: 直销客户, 2: 一级代理商客户,3: 二级代理商客户,4: 代理商
    Long distributorId;//
    Long phoneNumberId;
    Integer type = 1; // 1:线路充值，2: 短信充值
    String startDate;
    String endDate;

    public boolean requireJoinUser() {
        return requireSearch() || filterDistributorId() || (Objects.nonNull(customerType) && customerType != 0);
    }

    public Integer getSearchType() {
        if (StringUtils.isNotBlank(searchName)) {
            return 1;
        }
        if (StringUtils.isNotBlank(searchPhone)) {
            return 2;
        }
        if (StringUtils.isNotBlank(searchLinkman)) {
            return 3;
        }
        return 1;
    }

    public String getSearchValue() {
        if (StringUtils.isNotBlank(searchName)) {
            return searchName;
        }
        if (StringUtils.isNotBlank(searchPhone)) {
            return searchPhone;
        }
        if (StringUtils.isNotBlank(searchLinkman)) {
            return searchLinkman;
        }
        return null;
    }

    public boolean requireSearch() {
        return StringUtils.isNotBlank(getSearchValue());
    }

    public boolean filterDistributorId() {
        return Objects.nonNull(distributorId) && distributorId > 0;
    }

    public boolean isUnionCostList() {
        return Objects.nonNull(customerType) && (customerType == 0 || customerType != 4);
    }

    public boolean isUnionCostListDistributor() {
        return Objects.nonNull(customerType) && (customerType == 0 || customerType == 4);
    }

    public boolean isQueryDistributorTenant() {
        return Objects.nonNull(customerType) && (customerType == 2 || customerType == 3);
    }

    public Integer getCostListCostType() {
        return type == 1 ? 3 : 4;
    }

    public Integer getCostListDistributorCostType() {
        return type == 1 ? 0 : 2;
    }
}
