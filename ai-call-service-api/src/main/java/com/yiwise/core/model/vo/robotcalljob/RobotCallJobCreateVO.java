package com.yiwise.core.model.vo.robotcalljob;

import com.yiwise.core.model.bo.IntentLevelTagBO;
import com.yiwise.core.model.bo.phonenumber.TenantPhoneNumberBO;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 24/07/2018
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class RobotCallJobCreateVO extends RobotCallJobModifyVO implements Serializable {
    // private String jobTasksFileUrl; 1206 TANYI-830 新建任务去掉导入客户字段
    private List<TenantPhoneNumberBO> jobPhoneNumberList;

    private IntentLevelTagBO intentLevelTag;

    private String dialogFlowName;
}
