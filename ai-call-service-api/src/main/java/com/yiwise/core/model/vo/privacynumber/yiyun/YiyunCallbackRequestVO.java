package com.yiwise.core.model.vo.privacynumber.yiyun;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class YiyunCallbackRequestVO {

	/**
	 * 业务id, 消息请求标识
	 */
	@JsonProperty(value = "request_id")
	private String requestId;

	/**
	 * 真实号码
	 */
	@JsonProperty(value = "tel_a")
	private String telA;

	/**
	 * 小号号码
	 */
	@JsonProperty(value = "tel_x")
	private String telX;

	/**
	 * 对端号码
	 */
	@JsonProperty(value = "tel_b")
	private String telB;

	/**
	 * 原始被叫
	 */
	private String telC;

	/**
	 * 绑定id
	 */
	@JsonProperty(value = "sub_id")
	private String subId;

	/**
	 * 呼叫类型, AX业务（Bit7=0时为传统CS方式，Bit7=1时为PS方式）
	 * 0: DTMF方式通话主叫
	 * 1: 通话被叫
	 * 2: 短信发送
	 * 3: 短信接收
	 * 20: 无绑定呼叫
	 * 127: 双呼
	 * 128: PS方式通话主叫
	 */
	@JsonProperty(value = "call_type")
	private String callType;

	/**
	 * 发起呼叫时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty(value = "call_time")
	private LocalDateTime callTime;

	/**
	 * 振铃开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty(value = "ring_time")
	private LocalDateTime ringTime;

	/**
	 * 通话开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty(value = "start_time")
	private LocalDateTime startTime;

	/**
	 * 通话结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonProperty(value = "finish_time")
	private LocalDateTime finishTime;

	/**
	 * 通话标识, 业务参考号
	 */
	@JsonProperty(value = "call_id")
	private String callId;

	/**
	 * 释放方向
	 * 0 平台释放
	 * 1 主叫
	 * 2 被叫
	 */
	@JsonProperty(value = "finish_dir")
	private String finishDir;

	/**
	 * 000 0001（1）  未分配的号码
	 * 000 0010（2）  无路由到指定的转接网
	 * 000 0011（3）  无路由到目的地
	 * 000 0100（4）  发送专用信息音
	 * 001 0000（16） 正常的呼叫拆线
	 * 001 0001（17） 用户忙
	 * 001 0010（18） 用户未响应
	 * 001 0011（19） 用户未应答
	 * 001 0100（20） 用户缺席
	 * 001 0101（21） 呼叫拒收
	 * 001 0110（22） 号码改变
	 * 001 1011（27） 目的地不可达
	 * 001 1100（28） 无效的号码格式（地址不全）
	 * 001 1101（29） 性能拒绝
	 * 001 1111（31） 正常—未指定
	 * <p>
	 * 类别010，资源不可用类：
	 * 010 0010（34） 无电路/通路可用
	 * 010 1010（42） 交换设备拥塞
	 * <p>
	 * 类别011，业务或任选不可用类：
	 * 011 0010（50） 所请求的性能未预定
	 * 011 0101（53） CUG中限制去呼叫
	 * 011 0111（55） CUG中限制来呼叫
	 * 011 1001（57） 承载能力无权
	 * 011 1010（58） 承载能力目前不可用
	 * <p>
	 * 类别100，业务或任选未实现类：
	 * 100 0001（65） 承载能力未实现
	 * 100 0101（69） 所请求的性能未实现
	 * <p>
	 * 类别101，无效的消息（例如参数超出范围）类：
	 * 101 0111（87） 被叫用户不是CUG的成员
	 * 101 1000（88） 不兼容的目的地
	 * 101 1010（90） 不存在的CUG
	 * 101 1011（91） 无效的转接网选择
	 * 101 1111（95） 无效的消息，未指定
	 * <p>
	 * 类别110，协议错误（例如未知的消息）类：
	 * 110 0001（97） 消息类型不存在或未实现
	 * 110 0011（99） 参数不存在或未实现
	 * 110 0110（102）定时器终了时恢复
	 * 110 0101（103）参数不存在或未实现—传递
	 * 110 1110（110）消息带有未被识别的参数—舍弃
	 * 110 1111（111）协议错误，未指定
	 * <p>
	 * 类别111，互通类：
	 * 111 1111（127）互通，未指定
	 * 类别1100、1101，平台拒绝类：
	 * 1100 1010（202）用户忙，MSRN获取失败，平台挂机
	 * 1100 1011（203）用户去活，平台挂机
	 * 1100 1100（204）用户在平台侧关机，平台挂机
	 * 1100 1101（205）用户未开户，平台挂机
	 * 1100 1110（206）小号不允许呼叫，平台挂机
	 * 1100 1111（207）主号拨打小号，平台挂机
	 * 1101 0001（209）主叫打小号带原始被叫，平台挂机
	 * 38被叫网络有问题，信令释放消息没有回应给主叫
	 * 170桥接失败引起可能是业务量突然增大板卡引起的异常呼叫。
	 * 41 临时故障
	 * 44 请求的电路/信道不可用
	 */
	@JsonProperty(value = "finish_cause")
	private String finishCause;

	/**
	 * 录音控制
	 */
	@JsonProperty(value = "is_record")
	private String isRecord;

	/**
	 * 录音地址
	 */
	@JsonProperty(value = "record_url")
	private String recordUrl;

	/**
	 * 录音模式
	 * 1：主叫在左声道
	 * 2：主叫在右声道
	 * 3：混音
	 */
	@JsonProperty(value = "record_mode")
	private String recordMode;

	/**
	 * 呼转号码
	 */
	private String telredir;

	/**
	 * 呼转原因
	 * 0000（0） 未知
	 * 0001（1） 用户忙
	 * 0010（2） 无应答
	 * 0011（3） 无条件
	 * 0110（6） 移动用户不可及
	 */
	private String redirreason;

	/**
	 * 被叫来显号码
	 */
	@JsonProperty(value = "call_display")
	private String callDisplay;

	/**
	 * 区号, 对应绑定请求中area_code
	 */
	@JsonProperty(value = "area_code")
	private String areaCode;

	/**
	 * 通话时长, 单位秒
	 */
	private Integer duration;

	/**
	 * 自定义参数
	 */
	private String extra;
}
