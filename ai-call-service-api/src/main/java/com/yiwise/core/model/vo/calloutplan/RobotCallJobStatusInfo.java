package com.yiwise.core.model.vo.calloutplan;

import com.yiwise.core.model.enums.RobotCallJobStatusEnum;
import com.yiwise.core.model.enums.calloutplan.CallOutPlanJobDialogFlowStatusEnum;
import com.yiwise.core.model.enums.calloutplan.CallOutPlanSmsStatusEnum;
import com.yiwise.core.model.enums.handler.CallOutPlanJobDialogFlowStatusEnumHandler;
import com.yiwise.core.model.enums.handler.CallOutPlanSmsStatusEnumHandler;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RobotCallJobStatusInfo {

    List<JobStatusInfo> JobStatusInfoList;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class JobStatusInfo {
        Long robotCallJobId;
        String robotCallJobName;
        RobotCallJobStatusEnum robotCallJobStatus;

        String dialogFlowName;
        @ColumnType(typeHandler = CallOutPlanJobDialogFlowStatusEnumHandler.class)
        CallOutPlanJobDialogFlowStatusEnum dialogFlowStatus;
        Boolean published;

        String smsTemplateName;
        @ColumnType(typeHandler = CallOutPlanSmsStatusEnumHandler.class)
        CallOutPlanSmsStatusEnum callOutPlanSmsStatus;

        Boolean enabledStatus;
    }
}
