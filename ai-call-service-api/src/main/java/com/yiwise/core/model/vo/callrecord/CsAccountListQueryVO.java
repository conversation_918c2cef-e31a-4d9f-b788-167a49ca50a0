package com.yiwise.core.model.vo.callrecord;


import com.yiwise.core.model.enums.CsStaffOnlineStatusEnum;
import com.yiwise.core.model.enums.EnabledStatusEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callin.StaffTypeEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @Author: chen<PERSON><PERSON>an
 * @Date: 2020-11-12 11:33
 */
@Data
public class CsAccountListQueryVO extends AbstractQueryVO implements Serializable {
    Long tenantId;
    Long userId;
    Long csStaffGroupId;
    EnabledStatusEnum enabledStatus;
    String searchName;
    StaffTypeEnum staffType;
    SystemEnum systemType;
    Boolean useAuth;
    CsStaffOnlineStatusEnum onlineStatus;
    List<Long> userIdList;
    List<Long> filterStaffIds;
}
