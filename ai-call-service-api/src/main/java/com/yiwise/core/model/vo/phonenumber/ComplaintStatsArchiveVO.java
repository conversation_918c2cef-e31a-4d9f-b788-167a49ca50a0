package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = MongoCollectionNameCenter.COMPLAIN_TENANT_TRACK_STATS)
public class ComplaintStatsArchiveVO {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 行业ID
     */
    private Integer customerTrackType;

    /**
     * 投诉个数
     */
    private Integer complaintCount;

    /**
     * 拨打电话数量
     */
    private Long callTaskCompleted;

    /**
     * 接通电话数
     */
    private Long totalAnsweredCall;

    /**
     * 计费时长
     */
    private Long billChatTime;

    /**
     * 内部结算成本
     */
    private Long linePay;

    /**
     * 统计日期
     */
    private LocalDate createTime;

    public boolean isValid() {
        if (Objects.isNull(callTaskCompleted)) {
            callTaskCompleted = 0L;
        }
        if (Objects.isNull(complaintCount)) {
            complaintCount = 0;
        }
        return callTaskCompleted + complaintCount > 0;
    }
}
