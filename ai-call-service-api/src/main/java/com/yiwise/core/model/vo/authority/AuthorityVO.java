package com.yiwise.core.model.vo.authority;

import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * @ClassName AuthorityVO
 * <AUTHOR>
 * @Date 16:24
 * @Version 1.0
 **/
@Data
@ToString
public class AuthorityVO implements Serializable {

    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @NotNull(message = "权限不能为空")
    private List<String> authorityCodes;

    private List<Long> authorityIds;

    private Set<Long> dialogFlowList;
    private Set<Long> callOutDialogFlowList;
    private Set<Long> callInDialogFlowList;

}
