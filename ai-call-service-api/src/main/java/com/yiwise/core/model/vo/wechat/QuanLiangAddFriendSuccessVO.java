package com.yiwise.core.model.vo.wechat;

import lombok.Data;

import java.util.List;

/**
 * @author: wuxian<PERSON><PERSON>@yiwise.com
 * @date: 2022 06 24 17:15
 */
@Data
public class QuanLiangAddFriendSuccessVO {

    private Integer eventType;

    private String robotId;

    /**
     * 客户id
     */
    private String accountId;
    /**
     * 客户类型：0:客户类型未知，1=员工，2=微信外部联系人，3=企业微信外部联系人
     */
    private Integer accountType;

    /**
     * (仅支持扫码号)调用api加好友时传入的标识字段,多次请求会记录多个
     */
    private List<String> markIdList;

    private PofileInfo profile;

    @Data
    public static class PofileInfo {

        private String name;
        /**
         * 头像url
         */
        private String avatar;
        /**
         * 微信union_id，只有微信客户才有
         */
        private String unionId;
        /**
         * 微信external_user_id
         */
        private String externalUserId;
        private String phone;
        /**
         * 性别 1:男，2：女
         */
        private Integer gender;
    }
}
