package com.yiwise.core.model.vo.calloutplan;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: <EMAIL>
 * @date: 2022 09 13 15:04
 */
@Data
public class CallOutPlanResultVO implements Serializable {

    /**
     * 成功数
     */
    private Integer successCount;
    /**
     * 失败数
     */
    private Integer failureCount;
    /**
     * 失败任务列表
     */
    private List<CallOutPlanResultRowVO> resultList;

    /**
     * 生成成功的任务Id
     */
    private List<Long> jobIds;
    /**
     * 生成任务的文件夹
     */
    private Long folderId;
}
