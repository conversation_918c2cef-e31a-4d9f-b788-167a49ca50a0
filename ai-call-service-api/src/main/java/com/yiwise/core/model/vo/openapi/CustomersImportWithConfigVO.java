package com.yiwise.core.model.vo.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * call-open-api 客户导入vo
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CustomersImportWithConfigVO implements Serializable {

    private Long tenantId;

    private Long userId;
    /**
     * 客户信息列表
     */
    List<CustomerPersonImportVO> customerPersonImportVOS;
    /**
     * 省限制，使用‘,’分割 ： "北京，上海，浙江"
     */
    private String provinceStr;
    /**
     * 区限制 使用‘,’分割 ： "杭州，嘉兴，台州"
     */
    private String cityStr;
    /**
     * 所选地区是否为需要导入的地区  true:所选地区导入 false:所选地区不导入
     */
    private Boolean importSelected;

}
