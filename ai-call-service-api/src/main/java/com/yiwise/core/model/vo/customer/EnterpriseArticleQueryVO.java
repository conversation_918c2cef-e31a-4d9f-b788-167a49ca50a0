package com.yiwise.core.model.vo.customer;

import com.yiwise.core.model.enums.OrderByDirectionEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/05/25
 */
@Data
@NoArgsConstructor
public class EnterpriseArticleQueryVO extends AbstractQueryVO {
    Long tenantId;
    String articleTitle;
    Integer syncStatus;
    Long userId;
    List<Long> enterpriseArticleIds;

    /**
     * 排序方向
     */
    private OrderByDirectionEnum direction;
}
