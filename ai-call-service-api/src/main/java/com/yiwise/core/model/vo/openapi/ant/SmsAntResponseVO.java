package com.yiwise.core.model.vo.openapi.ant;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: <EMAIL>
 * @date: 2023 10 19 11:24
 */
@Data
public class SmsAntResponseVO {

    private String templateType;

    private String templateName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    private String templateContent;

    private String failReason;

    private String templateCode;

    private String status;

}
