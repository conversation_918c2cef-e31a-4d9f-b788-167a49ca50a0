package com.yiwise.core.model.vo.robotcalltask;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.GenderEnum;
import com.yiwise.customer.data.platform.rpc.api.service.enums.CreateSourceTypeEnum;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/2/11
 **/
@Data
@ToString
public class RobotCallTaskUpdateVO implements Serializable {
    @NotNull(message = "子任务id不能为空")
    private Long robotCallTaskId;

    /**
     * 客户名
     */
    private String name;

    /**
     * 性别
     */
    private GenderEnum gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate birthday;

    /**
     * 来源平台
     */
    private List<CreateSourceTypeEnum> createSources;

    /**
     * 自定义变量
     */
    private Map<String, String> properties;
}
