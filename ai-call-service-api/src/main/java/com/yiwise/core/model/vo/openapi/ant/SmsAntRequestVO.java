package com.yiwise.core.model.vo.openapi.ant;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <a href="https://k3465odso4.feishu.cn/wiki/WH5JwVkhQiV7l1kkMEacMXVZnEc">...</a>
 */
@Data
public class SmsAntRequestVO implements Serializable {

    private String appKey;

    private String action;

    //-----------查询短信模板相关-----------
    private String customerOwnerId;

    /**
     * APPROVING：审核中
     * APPROVED：审核通过
     * REJECTED:  审核未通过
     */
    private String status = "APPROVED";

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    private String smsType;
    //-----------查询短信模板相关-----------

    //-----------发送短信相关-----------
    private List<SmsAntCustomerInfoVO> customerInfo;
    private String requestId;
    private String signName;
    private String templateCode;
    //-----------发送短信相关-----------

}
