package com.yiwise.core.model.vo.dialogflowinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.OrderByDirectionEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CustomerTagRuleQueryVO extends AbstractQueryVO {
    Long tenantId;
    Long dialogFlowId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime beginDateTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime endDateTime;
    String orderWith;
    OrderByDirectionEnum directionTo;
    List<Long> callJobIdList;
}
