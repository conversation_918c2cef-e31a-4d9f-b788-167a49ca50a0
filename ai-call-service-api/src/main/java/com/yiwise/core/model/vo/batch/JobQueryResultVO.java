package com.yiwise.core.model.vo.batch;

import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.dal.entity.SpringBatchJobLogPO;
import com.yiwise.core.helper.objectstorage.AddOssPrefix;
import com.yiwise.core.model.enums.SpringBatchJobTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ExecutionContext;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JobQueryResultVO extends SpringBatchJobLogPO implements Serializable {

    /**
     * 错误信息。只有在任务状态为失败的时候会出现
     */
    private String errorMessage;

    private String createdByUserName;

    private String jobTypeName;

    /**
     * 客户名称
     */
    private String companyName;

    /**
     * 来源，用于替换掉systemType
     */
    private String source;

    public static JobQueryResultVO of(JobExecution jobExecution) {
        JobQueryResultVO jobQueryResultVO = new JobQueryResultVO();
        jobQueryResultVO.setQueryStatus(QueryStatus.of(jobExecution.getStatus(), jobExecution.getExitStatus()));
        Pair<Integer, Integer> progressCounts = getProgressCountFromExecution(jobExecution);
        jobQueryResultVO.setSuccessCount(progressCounts.getLeft());
        jobQueryResultVO.setFailureCount(progressCounts.getRight());
        JobInstance jobInstance = jobExecution.getJobInstance();
        String jobName = jobInstance.getJobName();
        jobQueryResultVO.setJobType(SpringBatchJobTypeEnum.valueOf(jobName));
        QueryStatus queryStatus = jobQueryResultVO.getQueryStatus();
        if (QueryStatus.FAILED.equals(queryStatus)) {
            String errorMessage = jobExecution.getExitStatus().getExitDescription();
            jobQueryResultVO.setErrorMessage(errorMessage);
        } else {
            SpringBatchJobTypeEnum jobType = jobQueryResultVO.getJobType();
            jobQueryResultVO.setJobTypeName(jobType.getDesc());
            SpringBatchJobTypeEnum importJobType = SpringBatchJobTypeEnum.IMPORT_PRIVATE_CUSTOMERS;
            boolean importHasError = importJobType.equals(jobType) && QueryStatus.COMPLETED_WITH_SKIPS.equals(queryStatus);
            boolean exportHasOutput = !importJobType.equals(jobType) && QueryStatus.COMPLETED.equals(queryStatus);
            if (importHasError || exportHasOutput) {
                String ossUrl = jobExecution.getJobParameters().getString("OSS_RESULT_URL");
                jobQueryResultVO.setOssUrl(ossUrl);
            }
        }
        return jobQueryResultVO;
    }

    /**
     * 从任务执行过程中获取进度
     *
     * @param jobExecution 任务运行
     * @return 第一个值是成功条目数量，第二个是失败条目数量，包括process时skip的和写入时conflict的
     */
    private static Pair<Integer, Integer> getProgressCountFromExecution(JobExecution jobExecution) {
        // getStepExecutions返回的其实是一个list，它是有序的
        List<StepExecution> stepExecutions = (List<StepExecution>) jobExecution.getStepExecutions();

        int successCount = 0;
        int failureCount = 0;
        int size = stepExecutions.size();
        if (size > 0) {
            // 在这里，因为导入和导出都是各自job的第一步，所以我们直接取第一个
            StepExecution lastStepExecution = stepExecutions.get(0);
            int skipCount = lastStepExecution.getSkipCount();
            int writeCount = lastStepExecution.getWriteCount();
            ExecutionContext stepExecutionContext = lastStepExecution.getExecutionContext();
            int conflictCount = 0;
            if (stepExecutionContext.containsKey(BatchConstant.Execution.CONFLICT_COUNT)) {
                conflictCount = stepExecutionContext.getInt(BatchConstant.Execution.CONFLICT_COUNT);
            }
            successCount = writeCount - conflictCount;
            failureCount = skipCount + conflictCount;
        }
        return ImmutablePair.of(successCount, failureCount);
    }
}
