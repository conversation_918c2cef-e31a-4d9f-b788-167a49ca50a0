package com.yiwise.core.model.vo.dialogflowinfo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class KnowledgeBatchUpdateIntentLevelRequestVO {

    @NotNull(message = "dialogFlowId不能为空")
    private Long dialogFlowId;

    @NotEmpty(message = "robotKnowledgeIdList不能为空")
    private List<String> robotKnowledgeIdList;

    @NotNull(message = "intentLevelTagDetailCode不能为空")
    private Integer intentLevelTagDetailCode;
}
