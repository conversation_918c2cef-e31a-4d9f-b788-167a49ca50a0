package com.yiwise.core.model.vo.phonenumber;

import com.yiwise.core.dal.entity.AreaPO;
import com.yiwise.core.model.bo.robotcalljob.timeduration.ActiveTimeBO;
import com.yiwise.core.model.enums.LocationDisplayTypeEnum;
import com.yiwise.core.model.enums.handler.LocationDisplayTypeEnumHandler;
import com.yiwise.core.model.enums.handler.base.ActiveTimeListToJsonTypeHandler;
import com.yiwise.core.model.enums.phonenumber.CallOutRangeEnum;
import com.yiwise.core.model.enums.phonenumber.PhoneShowMethodEnum;
import lombok.Data;
import java.io.Serializable;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.List;

@Data
public class LineMarketResultVO implements Serializable {

    private Long phoneNumberId;

    private String phoneNumber;

    private String phoneName;

    private Integer alertCountThreshold;

    /**
     * 是否支持呼入
     */
    private Boolean supportCallIn;

    /**
     * 是否支持外呼
     */
    private Boolean supportCallOut;

    /**
     * 线路归属地显示方式 0 默认， 1 全国随机显示， 2 不显示归属地
     */
    @ColumnType(typeHandler = LocationDisplayTypeEnumHandler.class)
    private LocationDisplayTypeEnum locationDisplayType;

    private String province;

    private String city;

    private PhoneShowMethodEnum showMethod;

    private CallOutRangeEnum callOutRange;

    private List<AreaPO> deadArea;

    /**
     * 市场价
     */
    private Long marketPrice;

    private Integer dailyCallCount;

    private Integer concurrenceLimit;

    private Integer usedConcurrence;

    private Integer currentConcurrence;

    @ColumnType(typeHandler = ActiveTimeListToJsonTypeHandler.class)
    List<ActiveTimeBO> activeTimeList;

    private String remark;
    //控制在线路市场是否显示
    private Integer lineMarketShow;
}
