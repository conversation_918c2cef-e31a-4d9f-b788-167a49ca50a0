package com.yiwise.core.model.dto;

import java.io.Serializable;

public class CustomerPersonPhoneNumberDTO implements Serializable {
    private Long customerPersonId;
    private String phoneNumber;

    public CustomerPersonPhoneNumberDTO(Long customerPersonId, String phoneNumber) {
        this.customerPersonId = customerPersonId;
        this.phoneNumber = phoneNumber;
    }

    public CustomerPersonPhoneNumberDTO() {
    }

    public Long getCustomerPersonId() {
        return customerPersonId;
    }

    public void setCustomerPersonId(Long customerPersonId) {
        this.customerPersonId = customerPersonId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Override
    public String toString() {
        return "{" +
                "customerPersonId=" + customerPersonId +
                ", phoneNumber='" + phoneNumber + '\'' +
                '}';
    }
}
