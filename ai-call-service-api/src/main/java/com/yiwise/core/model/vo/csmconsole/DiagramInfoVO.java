package com.yiwise.core.model.vo.csmconsole;

import com.yiwise.core.model.enums.TenantAccountStatusEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DiagramInfoVO {
    /**
     *折线图
     */
    private List<DiagramDataVO> diagramDataList;

    private Map<TenantActiveStatus, Integer> activeStatusCountMap;

    private Map<TenantAccountStatusEnum, Integer> nonActiveStatusCountMap;

    private Long totalCount;

    public enum TenantActiveStatus {
        ACTIVE,
        NON_ACTIVE,
    }
}
