package com.yiwise.core.model.vo.phonenumber;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * <AUTHOR>
 */
@Data
public class PhoneNumberSupplierStatsQueryVO extends AbstractQueryVO implements Serializable {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 供应商名称
     */
    private String account;

    private Long tenantId;

    private Long userId;
}
