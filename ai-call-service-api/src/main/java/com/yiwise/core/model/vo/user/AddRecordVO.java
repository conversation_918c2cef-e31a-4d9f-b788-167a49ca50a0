package com.yiwise.core.model.vo.user;

import com.yiwise.core.model.enums.YesOrNoEnum;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * @ClassName AddRecordVO
 * <AUTHOR>
 * @Date 2019/4/25
 **/
@Data
public class AddRecordVO implements Serializable {

    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 修改话术
     */
    private Boolean canModifySpeech;

    private List<Long> dialogFlowInfoIds;

    private String comment;

    private Long updateUserId;

    private Set<Long> knowledgeBaseInfoIds;
    //新逻辑 给一个话术绑定录音师
    private YesOrNoEnum add;

    /**
     * 录音师的字段选项列表
     */
    private Set<Long> recorderOptionIds;
}
