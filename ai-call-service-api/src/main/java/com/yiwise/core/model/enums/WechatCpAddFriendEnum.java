package com.yiwise.core.model.enums;

import com.yiwise.base.model.enums.CodeDescEnum;

/**
 * 微信加好友发送信息类型
 * @date : 2020/11/20 11:46
 */
public enum WechatCpAddFriendEnum implements CodeDescEnum {
    NOT_SEND_ADD_FRIEND_MESSAGE(0, "不发送加好友"),
    SEND_JUZI_ADD_FRIEND_MESSAGE(1, "句子"),
    SEND_WEIBAN_ADD_FRIEND_MESSAGE(2, "微伴"),
    YIWISE_SCRM_AUTO(3, "一知SCRM自动"),
    YIWISE_SCRM_MANUAL(4, "一知SCRM手动"),
    AIKE_WECHAT(5, "艾客个人微信"),
    AIKE_WECHAT_WORK(6, "艾客企业微信"),
	QIWEIBAO(7, "企微宝"),
    QUANLIANG(8,"圈量"),
    YIWISE_SCRM_JUZI(9, "一知SCRM句子"),
    ;

    WechatCpAddFriendEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    String desc;
    Integer code;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

	/**
	 * 手动发送加微请求时是否附带备注"手动添加"
	 */
    public static boolean isManualSendWithRemark(WechatCpAddFriendEnum type) {
    	return SEND_JUZI_ADD_FRIEND_MESSAGE.equals(type) || SEND_WEIBAN_ADD_FRIEND_MESSAGE.equals(type) || AIKE_WECHAT.equals(type) || AIKE_WECHAT_WORK.equals(type);
    }

	/**
	 * 是否支持过滤已加微成功的客户
	 */
    public static boolean supportFilterSuccess(WechatCpAddFriendEnum type) {
    	return YIWISE_SCRM_AUTO.equals(type) || YIWISE_SCRM_MANUAL.equals(type) || YIWISE_SCRM_JUZI.equals(type);
    }

	/**
	 * 是否支持过滤已发送加微请求超过24小时的客户
	 */
    public static boolean supportFilterSendIn24hours(WechatCpAddFriendEnum type) {
    	return YIWISE_SCRM_AUTO.equals(type) || YIWISE_SCRM_MANUAL.equals(type) || YIWISE_SCRM_JUZI.equals(type);
    }

	/**
	 * 是否支持导入客户时设置加微账号
	 */
	public static boolean supportSetWechatAccountWhenImportCustomer(WechatCpAddFriendEnum type) {
    	return YIWISE_SCRM_AUTO.equals(type) || YIWISE_SCRM_MANUAL.equals(type);
	}

	/**
	 * 是否支持导入时指定加微账号
	 */
	public static boolean supportSetAccountWhenImport(WechatCpAddFriendEnum type) {
    	return YIWISE_SCRM_AUTO.equals(type) || YIWISE_SCRM_MANUAL.equals(type);
    }

    public static boolean isYiwise(WechatCpAddFriendEnum type) {
	    return YIWISE_SCRM_AUTO.equals(type) || YIWISE_SCRM_MANUAL.equals(type) || YIWISE_SCRM_JUZI.equals(type);
    }
}
