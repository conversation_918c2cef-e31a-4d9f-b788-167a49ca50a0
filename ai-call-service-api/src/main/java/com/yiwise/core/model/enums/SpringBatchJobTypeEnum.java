package com.yiwise.core.model.enums;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.core.model.bo.batch.SpringBatchJobTypeBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.HeaderList.SMS_TEMPLATE_STATS_EXPORT_HEADER_LIST;

@Slf4j
public enum SpringBatchJobTypeEnum implements CodeDescEnum {
    /**
     * 定义一个未知的类型，用于多个版本开发时的枚举处理前后兼容
     */
    UNKNOWN(CodeDescEnum.UNKNOWN_CODE, 0, "未知类型", "unknownStep"),

    IMPORT_PRIVATE_CUSTOMERS(0, 0, "我的客户导入客户", "customerPersonImportStep"),
    EXPORT_PRIVATE_CUSTOMERS(1, 1, "我的客户导出客户", "customerPersonExportStep"),
    IMPORT_CUSTOMERS_TO_JOB(2, 0, "导入客户到任务", "customerPersonImportStep"),
    EXPORT_CALL_RECORDS(3, 1, "导出联系历史", "callRecordExportStep"),
    EXPORT_CALL_RECORDS_FROM_JOB(4, 1, "已呼客户列表导出通话记录", "callRecordExportStep"),

    EXPORT_CALL_COST(5, 1, "导出话费详单", "callCostDetailExportStep"),
    EXPORT_CALL_STATUS_INFO(6, 1, "导出通话统计信息", "callStatusInfoExportStep"),
    EXPORT_CALL_COST_INFO(7, 1, "导出通话费用信息（按日期汇总）", "callCostInfoExportStep"),
    EXPORT_MESSAGE_COST_INFO(8, 1, "导出短信费用信息（按日期汇总）", "messageCostInfoExportStep"),
    EXPORT_CALL_COST_MONTH_INFO(9, 1, "导出通话费用信息（按月汇总）", "callCostMonthInfoExportStep"),
    EXPORT_CALL_STATUS_INFO_BY_TASK(203, 1, "导出通话统计信息(按任务)", "callStatusInfoByTaskExportStep"),

    EXPORT_MESSAGE_COST_MONTH_INFO(10, 1, "导出短信费用信息（按月汇总）", "messageCostMonthInfoExportStep"),
    IMPORT_CUSTOMERS_RE_ADD_JOB(11, 0, "已呼客户列表重新添加客户到任务", "customerPersonReAddImportStep"),
    IMPORT_CUSTOMERS_ADD_JOB(12, 0, "我的客户导入到任务", "customerPersonAddImportStep"),
    EXPORT_TENANT_COST_LIST_LINE(13, 1, "导出租户线路消费流水", "costListTenantLineExportStep"),
    EXPORT_DISTRIBUTOR_CUSTOMER(14, 1, "导出分销商客户", "distributorCustomerExportStep"),

    EXPORT_DIRECT_CUSTOMER(15, 1, "导出直销客户", "directCustomerExportStep"),
    IMPORT_PUBLIC_CUSTOMERS(16, 0, "客户公海导入客户", "customerPersonImportStep"),
    EXPORT_PUBLIC_CUSTOMERS(17, 1, "客户公海导出客户", "customerPersonExportStep"),
    IMPORT_FROM_TASK_TO_JOB(18, 0, "导入未呼客户列表到其他任务", "toBeCalledTaskImportStep"),
    EXPORT_TENANT_COST_LIST_MESSAGE(19, 1, "导出租户短信消费流水", "costListTenantMessageExportStep"),

    EXPORT_DISTRIBUTOR(22, 1, "导出分销商", "distributorExportStep"),
    EXPORT_TENANT_CALL_STATUS(23, 1, "导出客户统计分析", "tenantCallStatsExportStep"),
    EXPORT_DETAIL_CALL_STATUS(24, 1, "导出客户统计分析详情", "detailCallStatsExportStep"),
    EXPORT_ALL_RECHARGE_RECORD(25, 1, "导出充值记录", "allRechargeRecordExportStep"),
    IMPORT_LAST_RECORD_RE_ADD_JOB(26, 0, "已呼客户列表重新添加客户到任务", "lastCallRecordReAddImportStep"),

    EXPORT_LAST_CALL_RECORDS(27, 1, "导出联系历史", "lastCallRecordExportStep"),
    EXPORT_CALL_RECORDS_FROM_ROBOT_CALL_JOB(28, 1, "导出呼叫任务联系历史通话记录", "callRecordExportFromJobStep"),
    EXPORT_CALL_RECORDS_FROM_TRAINING(29, 1, "导出话术训练联系历史通话记录", "callRecordExportFromTrainingStep"),
    EXPORT_CALL_RECORDS_FROM_DIRECT_CALL(30, 1, "导出快速拨打联系历史通话记录", "callRecordExportFromDirectCallStep"),
    EXPORT_RECHARGE_DISTRIBUTE_STREAM(31, 1, "导出充值记录", "rechargeStreamExportStep"),

    EXPORT_SUB_ACCOUNT_CALL_COST(32, 1, "导出子账号线路计费", "subAccountCallCostExportStep"),
    EXPORT_SUB_ACCOUNT_SMS_COST(33, 1, "导出子账号短信计费", "subAccountMessageCostExportStep"),
    EXPORT_SUB_ACCOUNT_CALL_RECORD(34, 1, "导出子账号话单明细", "subAccountCallRecordExportStep"),
    IMPORT_CALL_RECORD_FROM_JOB_RE_ADD_JOB(35, 0, "呼叫任务联系历史重新添加客户到任务", "customerPersonReAddCallRecordFromJobImportStep"),
    EXPORT_TO_BE_CALLED_TASK(36, 1, "导出未呼客户列表", "toBeCalledTaskExportStep"),

    //cs seat
    EXPORT_SUB_ACCOUNT_CS_CALL_RECORDS(37, 1, "导出子账号人工外呼话单明细", "subAccountCsCallRecordExportStep"),
    EXPORT_FINANCE_TENANT_LINE_STATS(38, 1, "导出计费统计客户线路", "financeStatsExportStep"),
    EXPORT_FINANCE_TENANT_SMS_STATS(39, 1, "导出计费统计客户短信", "financeStatsExportStep"),
    EXPORT_FINANCE_LINE_STATS(40, 1, "导出计费统计线路计费", "financeStatsExportStep"),
    EXPORT_FINANCE_LINE_TEST_STATS(41, 1, "导出计费统计线路测试", "financeStatsExportStep"),


    IMPORT_PHONE_NUMBER_TO_JOB(42, 0, "导入手机号码到短信任务", "smsJobImportStep"),

    EXPORT_SMS_STATS_JOB(43, 1, "导出短信统计", "smsStatsExportStep"),
    // 用于实际excel导入类型 区分excel导入与内部导入
    IMPORT_FROM_EXCEL(44, 0, "excel导入客户", "customerPersonImportStep"),
    QIYU_IMPORT_FROM_EXCEL(44, 0, "excel导入客户", "customerPersonImportStep"),
    EXPORT_CALL_IN_COST_INFO(45, 1, "导出呼入通话费用信息（按日汇总）", "callInCostInfoExportStep"),
    EXPORT_CALL_IN_COST_MONTH_INFO(46, 1, "导出呼入通话费用信息（按月汇总）", "callInCostMonthInfoExportStep"),
    CS_BATCH_IMPORT_EXCEL(47, 0, "导入客户到人工外呼批量任务", "csBatchCustomerPersonImportStep"),
    EXPORT_CS_CALL_COST_INFO(48, 1, "导出人工外呼通话费用信息（按天汇总）", "csCallCostInfoExportStep"),
    EXPORT_CS_CALL_COST_MONTH_INFO(49, 1, "导出人工外呼通话费用信息（按月汇总）", "csCallCostMonthInfoExportStep"),
    EXPORT_SMS_INTENT_RECORD(50, 1, "导出意向短信发送历史", "exportSmsIntentRecordStep"),
    EXPORT_SMS_JOB_MESSAGE_RECORD(51, 1, "导出群发短信发送历史", "groupSmsRecordExportStep"),

    EXPORT_QC_COST_RECORDS(52, 1, "导出质检消费记录", "qcCostExportStep"),
    EXPORT_QC_RECHARGE_RECORDS(53, 1, "导出质检充值记录", "qcRechargeExportStep"),

    EXPORT_OPE_TENANT_STATS_DETAIL(54, 1, "导出客户状态统计数据", "tenantStatsDetailExportStep"),
    EXPORT_OPE_TENANT_STATS_DETAIL_NEW(54, 1, "导出客户状态统计数据", "tenantStatsDetailExportStep"),

    EXPORT_OPE_DIRECT_CUSTOMER(55, 1, "导出OPE直销客户列表", "opeDirectCustomerExportStep"),
    EXPORT_OPE_DIRECT_DISTRIBUTOR(56, 1, "导出OPE的代理商列表", "opeDirectDistributorExportStep"),
    EXPORT_OPE_DISTRIBUTOR_CUSTOMER(57, 1, "导出OPE的代理商客户列表", "opeDistributorCustomerExportStep"),
    EXPORT_OPE_SUB_DISTRIBUTOR(58, 1, "导出OPE的二级代理商列表", "opeSubDistributorExportStep"),
    EXPORT_OPE_SUB_DISTRIBUTOR_CUSTOMER(59, 1, "导出OPE的二级代理商客户列表", "opeSubDistributorCustomerExportStep"),
    EXPORT_OPE_PHONE_LINE(76, 1, "导出OPE线路列表", "opePhoneLineExportStep"),

    OPE_EXPORT_DIALOG_TRAIN_RECORD(60, 1, "OPE导出训练历史", "crmDialogTrainRecordExportStep"),
    CRM_EXPORT_DIALOG_TRAIN_RECORD(61, 1, "CRM导出训练历史", "crmDialogTrainRecordExportStep"),
    BOSS_EXPORT_DIALOG_TRAIN_RECORD(62, 1, "BOSS导出训练历史", "crmDialogTrainRecordExportStep"),

    OPE_EXPORT_TENANT_STATS(63, 1, "导出客户分析趋势", "opeTenantStatsExportStep"),
    BOSS_EXPORT_TENANT_STATS(64, 1, "导出客户分析趋势", "opeTenantStatsExportStep"),

    BOSS_EXPORT_DISTRIBUTOR_AI_RECORD(65, 1, "BOSS导出代理商AI坐席", "bossDistributorAiExportStep"),
    BOSS_EXPORT_DISTRIBUTOR_DIALOG_RECORD(66, 1, "BOSS导出代理商AI话术", "bossDistributorDialogflowExportStep"),
    BOSS_EXPORT_DISTRIBUTOR_ACCOUNT_RECORD(67, 1, "BOSS导出代理商余额流水", "bossDistributorAccountExportStep"),

    BOSS_EXPORT_SMS_RECHARGE_STREAM(68, 1, "导出短信充值记录", "rechargeStreamExportStep"),
    BOSS_EXPORT_LINE_RECHARGE_STREAM(69, 1, "导出线路充值记录", "rechargeStreamExportStep"),

    CRM_EXPORT_BATCH_IMPORT_WHITE_LINE(72, 1, "我的客户批量导入黑名单", "myCustomerBatchImportWhiteLineStep"),
    EXPORT_HUAWEI_FREE_TRY(74, 1, "导出试用申请", "huaweiFreeTryExportStep"),

    EXPORT_CALL_IN_RECEPTION_INFO(73, 1, "导出接待统计信息", "callInReceptionExportStep"),

    EXPORT_CALL_IN_RECORD_HISTORY(75, 1, "导出呼入接待联系历史", "callInRecordExportStep"),

    EXPORT_STAFF_STAT(80, 1, "导出语音坐席统计", "staffStatListExportStep"),

    EXPORT_GROUP_STAT(81, 1, "导出语音坐席组统计", "groupStatListExportStep"),

    EXPORT_TEXT_STAFF_STAT(82, 1, "导出文本坐席统计", "staffTextStatExportStep"),

    EXPORT_TEXT_GROUP_STAT(83, 1, "导出文本坐席组统计", "groupTextStatExportStep"),

    EXPORT_CUSTOMER_SMS_MESSAGE_RECORD(84, 1, "导出单点短信发送历史", "exportCustomerSmsMessageRecordStep"),

    EXPORT_CS_CALL_RECORD1(85, 1, "导出语音客服联系历史", "exportCsCallRecordStep1"),

    EXPORT_CS_CALL_RECORD2(86, 1, "导出语音客服联系历史", "exportCsCallRecordStep2"),

    EXPORT_CS_CALL_RECORD3(87, 1, "导出语音客服联系历史", "exportCsCallRecordStep3"),

    EXPORT_TEXT_RECORD(88, 1, "导出文本客服联系历史", "exportTextRecordStep"),
    QC_EXPORT_RECORD_LIST(74, 1, "导出已质检记录", "qcRecordExportStep"),

    EXPORT_CS_RECORD(89, 1, "人工外呼", "exportCsRecordStep"),

    EXPORT_OPE_DIRECT_AICC_ACCOUNT_RECHARGE_RECORDS(90, 1, "导出总账户的流水记录", "tenantAccountRechargeStep"),
    EXPORT_OPE_DIRECT_AICC_LINE_RECHARGE_RECORDS(91, 1, "导出话费的流水记录", "tenantLineRechargeStep"),
    EXPORT_OPE_DIRECT_AICC_MESSAGE_RECHARGE_RECORDS(92, 1, "导出短信费的流水记录", "tenantMessageRechargeStep"),
    EXPORT_OPE_DIRECT_AICC_QC_RECHARGE_RECORDS(93, 1, "导出质检的流水记录", "tenantQcRechargeStep"),

    QC_JOB_IMPORT_DESC_FILE_STEP(94, 0, "通话描述文件上传", "qcJobImportDescFileStep"),

    EXPORT_QC_CHECK_RECORD(95, 1, "导出抽检任务历史", "qcCheckRecordExportStep"),

    IMPORT_CUSTOMERS_ADD_CS_BATCH_JOB(96, 0, "客户导入到预测式外呼任务", "customerPersonAddImportCsBatchStep"),

    CS_BATCH_JOB_NOT_START_RECORD_EXPORT_JOB(97, 1, "导出未呼客户", "csBatchNotStartExportStep"),

    CS_BATCH_JOB_COMPLETE_RECORD_EXPORT_JOB(98, 1, "导出已呼客户", "csBatchCompleteExportStep"),

    CS_BATCH_NOT_START_IMPORT_JOB(99, 0, "未呼客户导入到预测式外呼任务", "csBatchNotStartImportStep"),

    CS_BATCH_JOB_COMPLETE_RECORD_IMPORT_JOB(100, 0, "已呼客户列表重新添加到预测试外呼任务", "csBatchCompleteImportStep"),

    IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_CS_BATCH_CALL_JOB(101, 0, "从AI外呼任务联系历史导入客户到预测式外呼任务",
            "importCustomerFromCallRecordToCsBatchCallJobStep"),

    IMPORT_CUSTOMER_FROM_CALL_IN_RECORD_TO_CS_BATCH_CALL_JOB(102, 0, "从呼入历史导入客户到预测式外呼任务",
            "importCustomerFromCallInRecordToCsBatchCallJobStep"),

    IMPORT_CUSTOMER_FROM_CALL_IN_RECORD_TO_ROBOT_CALL_JOB(103, 0, "从呼入历史导入客户到AI外呼任务",
            "importCustomerFromCallInRecordToRobotCallJobStep"),

    CUSTOMER_WHITE_LIST_IMPORT(104, 0, "导入客户黑名单", "customerWhiteListImport"),
    CUSTOMER_WHITE_LIST_EXPORT(105, 1, "导出客户黑名单", "customerWhiteListExport"),
    CUSTOMER_WHITE_LIST_MOVE(106, 0, "移动客户黑名单", "customerWhiteListMove"),

    EXPORT_CALL_STATS_DETAIL(107, 1, "导出通话统计数据", "callStatsDetailExportStep"),
    EXPORT_FINANCE_TENANT_ALL_LINE_STATS(108, 1, "导出所有计费统计客户线路", "financeTenantAllLineStatsExportStep"),

    QC_EXPORT_INSPECTION_FEEDBACK_LIST_JOB(109, 1, "导出质检考核反馈记录", "qcInspectionFeedbackExportStep"),

    QC_AUDIT_RECORD_EXPORT_JOB(110, 1, "导出复审记录", "qcAuditRecordExportStep"),

    IMPORT_CUSTOMER_TO_WAITING_CALL(112, 0, "导入客户到坐席待呼任务", "csWaitingCallCustomerPersonImportStep"),

    IMPORT_WAITING_CALL_TO_CS_BATCH_JOB(113, 0, "待呼列表导入到预测式外呼任务", "callWaitingImportCsBatchStep"),

    ASSIGN_CALL_OUT_JOB(114, 1, "为坐席分配待呼任务", "assignCallOutStep"),

    EXPORT_CUSTOMER_PERSON_POTENTIAL(115,1,"导出潜在客户列表","customerPersonPotentialStep"),
    BATCH_UPDATE_CUSTOMER_PERSON_LEVEL_TAG(116, 0, "批量修改客户标签", "batchUpdateCustomerPersonLevelTag"),
    BATCH_UPDATE_CUSTOMER_PERSON_GROUP(117, 0, "批量修改客户分组", "batchUpdateCustomerPersonGroup"),

    IMPORT_RECORD_INTO_CALL_OUT_QC_JOB(118, 0, "向质检范围为坐席外呼的质检任务导入记录", "importRecordIntoCallOutQcJobStep"),
    IMPORT_CALL_IN_RECORD_INTO_CALL_IN_QC_JOB(119, 0, "向质检范围为坐席接待的质检任务导入呼入记录", "importRecordIntoCallInQcJobStep"),
    IMPORT_CS_RECORD_INTO_CALL_IN_QC_JOB(120, 0, "向质检范围为坐席接待的质检任务导入预测式外呼任务记录", "importCsRecordIntoCallInQcJobStep"),
    // 问法语料
    IMPORT_INTENT_BRANCH_CORPUS(121, 0, "导入问法语料", "intentBranchCorpusImportStep"),
    EXPORT_INTENT_BRANCH_CORPUS(122, 1, "导出问法语料", "intentBranchCorpusExportStep"),

    LAST_CALL_RECORD_TO_CS_BATCH_CALL_JOB(123, 0, "从AI外呼任务联系历史导入客户到预测式外呼任务",
            "lastCallRecordToCsBatchCallJobStep"),
    TRANSFER_CS_WAITING_CALL(124, 1, "待呼列表转移", "transferCallWaitingStep"),


    // 问答知识
    IMPORT_KNOWLEDGE_CORPUS(125, 0, "导入问答知识", "knowledgeCorpusImportStep"),
    EXPORT_KNOWLEDGE_CORPUS(126, 1, "导出问答知识", "knowledgeCorpusExportStep"),

    EXPORT_CS_STAFF_INFO(127, 1, "导出语音坐席", "csStaffInfoStep"),
    ASSIGN_CALL_RECORD_JOB(128, 1, "AI外呼导入到待呼任务", "assignCallRecordStep"),
    LAST_ASSIGN_CALL_RECORD_JOB(129, 1, "AI外呼导入到待呼任务", "lastAssignCallRecordJob"),

    SEND_ADD_FRIEND(130, 1, "批量重新发送微信加好友", "sendAddFriendMessageStep"),
    // ope boss 共享黑名单导出
    EXPORT_OPE_BOSS_SHARE_WHITE_LIST(134, 1, "导出共享黑名单", "opeBossShareWhiteListExportStep"),
    //ope 计费统计导出
    EXPORT_OPE_FINANCE_TENANT_PAY_TYPE(135, 1, "导出按量计费统计", "tenantPayTypeExportStep"),

    EXPORT_CALLIN_SMS(131, 1, "导出呼入短信发送历史", "exportCallInSmsStep"),
    EXPORT_CALL_RECORDS_ES_FROM_ROBOT_CALL_JOB(132, 1, "导出呼叫任务的联系历史", "callRecordESExportFromJobStep"),
    IMPORT_TEXT_RECORD_INTO_QC_JOB(133, 0, "导入文本质检记录", "importTextRecordIntoQcJobStep"),

    EXPORT_CS_QC_RULE_TAG(138, 1, "导出客服标签", "exportCsQcRuleTagStep"),
    IMPORT_QC_RULE_TAG(139, 0, "导入语句标签", "importQcRuleTagStep"),
    EXPORT_ORGANIZATION_USER(140, 1, "导出组织架构用户", "organizationUserExportStep"),
    EXPORT_CUSTOMER_QC_RULE_TAG(141, 1, "导出客户标签", "exportCustomerQcRuleTagStep"),
    IMPORT_PLATFORM_CUSTOMERS(142, 0, "组织架构导入客户", "platformCustomerPersonImportStep"),
    //ope 计费统计导出
    EXPORT_OPE_FINANCE_DISTRIBUTOR_PRESTORE(144, 1, "导出代理商充值流水", "opeDistributorPrestoreStep"),
    //AI外呼任务变量批量替换
    WECHAT_VALUE_REPLACE(145, 1, "微信变量批量替换", "wechatValueReplaceStep"),
    FILTERED_TASK_ADD_JOB(145, 0, "外呼过滤导入到外呼任务", "filteredTaskImportStep"),
    FILTERED_TASK_EXPORT_EXCEL(146, 1, "外呼过滤-批量导出", "filteredTaskExportStep"),

    CALL_OUT_IMPORT_TO_WAITING_CALL(150, 1, "外呼联系历史导入到待呼列表", "callOutImportToWaitingCallStep"),
    CALL_IN_IMPORT_TO_WAITING_CALL(151, 1, "呼入联系历史导入到待呼列表", "callInImportToWaitingCallStep"),

    QC_RECORD_IMPORT_TO_CHECK_JOB(152, 1, "已质检记录分配人工抽检", "importToQcCheckStep"),
    QC_RECORD_IMPORT_TO_QC_JOB(153, 1, "已质检记录导入到质检任务", "importQcRecordToQcJobStep"),
    EXPORT_FINANCE_TENANT_LINE_STATS_FOR_QIYU(154, 1, "导出计费统计代理商客户线路", "financeStatsExportForQiyuStep"),
    EXPORT_OPE_DISTRIBUTOR_CUSTOMER_FOR_QIYU(155, 1, "导出OPE的代理商客户AI坐席", "opeDistributorCustomerExportForQiyuStep"),
    EXPORT_OPE_DISTRIBUTOR_DIALOG_FLOW(156, 1, "导出OPE代理商绑定话术列表", "opeDistributorDialogFlowExportStep"),
    TRANSFER_PRIVATE_TO_PUBLIC(157, 0, "将客户退回公海", "transferPrivateToPublic"),
    TRANSFER_PUBLIC_TO_PRIVATE(158, 1, "公海领取客户", "transferPublicToPrivate"),
    DISTRIBUTE_CUSTOMER_PERSON(159, 1, "分配客户", "distributeCustomerPerson"),
    DELETE_PRIVATE_CUSTOMER_PERSON(160, 1, "客户中心批量删除客户", "customerPersonDeleteStep"),
    DELETE_PUBLIC_CUSTOMER_PERSON(161, 1, "公海批量删除客户", "customerPersonDeleteStep"),

    EXPORT_CS_CALL_WAITING(165, 1, "导出待呼列表", "csCallWaitingExportStep"),

    EXPORT_CALL_COST_CS_RECORD(166, 1, "导出话费详单", "callCostCsRecordExportStep"),

    EXPORT_PRIVATE_CUSTOMERS_ES(201, 1, "我的客户导出客户", "customerPersonExportFromEsStep"),

    OPE_EXPORT_QC_COST_STATS(202, 1, "导出质检计费统计", "opeTenantQcCostExportStep"),
    OPE_EXPORT_QC_COST_DETAIL_STATS(203, 1, "导出质检计费明细", "opeTenantQcCostDetailExportStep"),
    EXPORT_OPE_DIALOG_PHONE_RELATION(204, 1, "导出话术线路行业对应关系列表", "opeDialogPhoneRelationExportStep"),
    ENTITY_EXPORT(210, 1, "实体导出", "entityExportStep"),
    ENTITY_IMPORT(211, 0, "实体导入", "entityImportStep"),

    EXPORT_CALL_COST_INFO_BY_TASK(212, 1, "导出通话费用信息（按日期任务汇总）", "callCostInfoByTaskExportStep"),
    EXPORT_CALL_COST_MONTH_INFO_BY_TASK(213, 1, "导出通话费用信息（按月按任务汇总）", "callCostMonthInfoByTaskExportStep"),

    EXPORT_CALL_RECORD_INSPECTS_STATS(217, 1, "导出外呼巡检统计数据", "callRecordInspectsStatsExportStep"),
    EXPORT_CALL_RECORD_INSPECTS(218, 1, "导出外呼巡检数据", "callRecordInspectsStatsExportStep"),

    EXPORT_VOS_REPORT(219, 1, "导出业务网关落地分析表", "vosReportExportStep"),

    EXPORT_OPE_DIRECT_CUSTOMER_BY_ROBOT(220, 1, "按产品导出OPE直销客户列表", "opeDirectCustomerByRobotExportStep"),
    EXPORT_DIRECT_CUSTOMER_BY_ROBOT(221, 1, "导出代理商直销客户", "directCustomerByRobotExportStep"),
    EXPORT_OPE_DISTRIBUTOR_CUSTOMER_BY_ROBOT(222, 1, "导出OPE的代理商客户列表", "opeDistributorCustomerByRobotExportStep"),
    EXPORT_OPE_DIRECT_CUSTOMER_BY_DIALOG_FLOW(223, 1, "按话术导出OPE直销客户列表", "opeDirectCustomerByDialogFlowExportStep"),
    OPE_BATCH_UPDATE_PHONE_BILL_STEP(224, 1, "批量修改客户的话费", "opeBatchUpdatePhoneBill"),
    EXPORT_SMS_TEMPLATE_STATS(225, 1, "短信模板数据统计", "opeDirectCustomerByDialogFlowExportStep", SMS_TEMPLATE_STATS_EXPORT_HEADER_LIST, SystemEnum.SMS_PLATFORM),

    // 问答知识批量测试
    KNOWLEDGE_TEST_IMPORT(226, 0, "批量导入知识问答测试", "knowledgeTestImportStep"),
    RECOMMEND_RESULT_EXPORT(227, 1, "阈值推荐结果导出", "recommendResultExportStep"),
    TEST_RESULT_EXPORT(228, 1, "批量测试结果导出", "testResultExportStep"),

	EXPORT_PRIVACY_NUMBER(230, 1, "导出隐私号码", "privacyNumberExportStep"),
	EXPORT_PRIVACY_NUMBER_HISTORY(231, 1, "导出隐私号联系历史", "privacyNumberHistoryExportStep"),

	EXPORT_DAILY(232, 1, "导出日报", "dailyExportStep"),
    EXPORT_AB_TEST_INFO(233,1,"导出数据对比分析表","abTestExportStep"),

    FILTERED_CS_TASK_EXPORT_EXCEL(251, 1, "预测试外呼过滤导出", "filteredCsTaskExportStep"),
    FILTERED_CS_TASK_ADD_JOB(252, 0, "预测试外呼过滤导入到预测试外呼任务", "filteredCsTaskImportStep"),

    EXPORT_PHONE_NUMBER_SUPPLIER(253, 1, "导出线路供应商消耗表", "phoneNumberSupplierStatsExportStep"),
    EXPORT_COMPLAINT_HISTORY(254, 1, "导出投诉记录统计", "complaintHistoryExportStep"),
    EXPORT_COMPLAINT_STATS(255, 1, "导出投诉记录分析", "complaintStatsExportStep"),

    EXPORT_DATA_REPORT(256, 1, "导出数据报表", "dataReportExportStep"),

	EXPORT_PRIVACY_NUMBER_OPE(257, 1, "OPE导出隐私号码", "privacyNumberOpeExportStep"),

    IMPORT_SHARED_KNOWLEDGE(258, 0, "知识共享批量导入", "sharedKnowledgeImportStep"),

    EXPORT_CUSTOMER_CONCERN(259, 1, "商机分析导出客户关注点", "customerConcernExportStep"),
    EXPORT_KNOWLEDGE_ACTIVE(260, 1, "商机分析导出问答知识触发", "customerKnowledgeActiveExportStep"),

    EXPORT_DIALOG_FLOW_BILLING(261, 1, "话术费用导出", "dialogFlowBillingExportStep"),

	EXPORT_FINANCE_SMS_STATS(265, 1, "OPE导出计费统计客户短信(新)", "exportFinanceSmsStatsStep"),
	EXPORT_FINANCE_DIRECT_CUSTOMER_STATS(266, 1, "OPE导出计费统计直销客户计费统计(新)", "exportFinanceDirectCustomerStatsStep"),
//CustomerFromCallRecordToSmsJob
    IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_SMS_JOB_LAST_CALL(279, 0, "呼叫任务已呼客户添加到短信任务--去重", "CustomerFromCallRecordToSmsJobImportStep"),
    IMPORT_CUSTOMER_FROM_CALL_RECORD_TO_SMS_JOB(280, 0, "呼叫任务已呼客户添加到短信任务", "CustomerFromCallRecordToSmsJobImportStep"),
    IMPORT_LAST_CALL_FROM_CALL_RECORD_TO_SMS_JOB(281, 0, "呼叫任务最后一通已呼客户添加到短信任务", "LastCallFromCallRecordToSmsJobImportStep"),
    IMPORT_TO_BE_CALLED_CUSTOMER_TO_SMS_JOB(282, 0, "呼叫任务未呼客户添加到短信任务", "CustomerFromCallRecordToSmsJobImportStep"),
    IMPORT_FILTERED_CUSTOMER_TO_SMS_JOB(283, 0, "呼叫任务过滤客户添加到短信任务", "CustomerFromCallRecordToSmsJobImportStep"),
    IMPORT_CALL_RECORD_TO_SMS_JOB(284, 0, "呼叫任务联系历史重新添加客户到短信任务", "customerPersonReAddCallRecordToSmsJobImportStep"),
    IMPORT_SHARED_BLACK_LIST(285, 0, "共享黑名单批量导入", "shareBlackListImportStep"),

    EXPORT_CALL_OUT_DATA_REPORT(286, 1, "导出外呼数据分析报表", "callOutDataReportExportStep"),

    EXPORT_CALL_OUT_DATA_DETAIL_REPORT(287, 1, "导出外呼数据分析报表明细", "callOutDataReportDetailExportStep"),

    EXPORT_PHONE_NUMBER_BY_GATEWAYS(288, 1, "导出网关关联线路列表", "phoneNumberByGwExportStep"),

    EXPORT_OPENSIPS_ACC(289, 1, "导出话单信息", "opensipsAccExportStep"),

    IMPORT_DIALOGFLOW_RECOMMEND(290,0,"话术推荐导入","dialogFlowRecommendImportStep"),

    EXPORT_DIALOGFLOW_RECOMMEND(291,1,"话术推荐导出","dialogFlowRecommendExportStep"),

    EXPORT_MAIN_BRAND_LIST(292, 1, "导出主公司列表", "mainBrandExportStep"),

    EXPORT_MAIN_BRAND_TENANT_COST(293, 1, "导出主品牌费用汇总", "mainBrandTenantCostExportStep"),

	EXPORT_SMS_TEST_STAT_JOB(295,1,"导出计费统计短信测试","exportSmsTestStatStep"),

    DELETE_ROBOT_CALL_TASK(301,1,"批量删除未呼客户","robotCallTaskDeleteStep"),
    DELETE_ROBOT_CALL_TASK_JOBS(302, 1, "按任务列表批量删除未呼客户", "robotCallTaskDeleteByJobIdsStep"),

	PLAN_IMPORT_CUSTOMERS_TO_JOB_SINGLE(305, 0, "外呼计划单属性导入客户到任务", "planImportCustomersToJobSingleStep"),
	PLAN_IMPORT_CUSTOMERS_TO_JOB_MULTI(306, 0, "外呼计划多属性导入客户到任务", "planImportCustomersToJobMultiStep"),

	EXPORT_SAAS_FINANCE(310,1,"导出数据大盘财务表","saasFinanceExportStep"),

    CUSTOMER_REAL_WHITE_LIST_IMPORT(311, 0, "导入客户白名单", "customerRealWhiteListImport"),

    CUSTOMER_REAL_WHITE_LIST_EXPORT(312, 1, "导出客户白名单", "customerRealWhiteListExport"),

    EXPORT_PHONE_NUMBER_BY_CARRIER(313, 1, "导出网关组关联线路列表", "phoneNumberByCarrierExportStep"),

    DIRECT_CALL_STATS_OPE_EXPORT_LIST(314, 1, "OPE导出快速拨打统计", "directCallStatsOpeExportStep"),
    DIRECT_CALL_STATS_AICC_EXPORT_LIST(315, 1, "AICC导出快速拨打统计", "directCallStatsAiccExportStep"),
    IMPORT_FROM_USER_GROUP(316, 0, "用户群导入客户", "customerPersonUserGroupImportStep"),

    TENANT_CHURN_WARN_EXPORT_LIST(320, 1, "导出客户流失预警", "tenantChurnWarnExportStep"),
    EXPORT_FILTERED_SMS(321, 1, "短信过滤-批量导出", "filteredSmsExportStep"),
    IMPORT_FILTERED_TO_SMS_JOB(322, 0, "短信过滤-导入到任务", "filteredToSmsJobImportStep"),

    SMS_JOB_DELETE_SEND_WAIT(323, 1, "短信任务-删除待发送列表", "smsJobDeleteSendWaitStep"),


    EXPORT_PHONENUMBER_STATEMENT(324, 1, "导出充值详情", "phoneNumberStatementExportStep"),
    EXPORT_PHONENUMBER_CONSUME(325, 1, "导出消耗详情", "phoneNumberConsumeExportStep"),
    EXPORT_PHONENUMBER_RECHARGE_RECORD(326, 1, "导出流水详情", "phoneNumberReChargeRecordExportStep"),
    EXPORT_PHONENUMBER_INVOICE(327, 1, "导出开票详情", "phoneNumberInvoiceExportStep"),

    MA_CALL_STATS_AICC_EXPORT_LIST(340, 1, "导出MA外呼通话费用", "maCallStatsAiccExportStep"),
    MA_CALL_STATS_OPE_EXPORT_LIST(341, 1, "OPE导出MA外呼统计", "maCallStatsOpeExportStep"),

    BATCH_MANUAL_CALLBACK_JOB(328, 1, "批量手动回调重试", "batchManualCallbackStep"),
    CALLBACK_RECORD_LOG_EXPORT_LIST(329, 1, "导出回调记录明细", "callbackRecordLogExportList"),

    EXPORT_SMS_RECEIVE_RECORD(331, 1, "导出回复短信记录", "smsReceiveRecordExportStep"),
    IMPORT_SMS_RECEIVE_TO_SMS_JOB(332, 0, "回复短信导入到短信任务", "addSmsReceiveRecordToSmsStep"),
    IMPORT_SMS_RECEIVE_TO_CALLOUT_JOB(333, 0, "回复短信导入到外呼任务", "addSmsReceiveRecordToCalloutJobStep"),
    IMPORT_SMS_RECEIVE_TO_BLACK_LIST(334, 0, "回复短信导入到黑名单", "addSmsReceiveRecordToBlackListStep"),
    IMPORT_INTENT_MESSAGE_TO_SMS_JOB(335, 0, "外呼短信发送历史导入到短信任务", "addIntentMessageToSmsStep"),
    IMPORT_SINGLE_SMS_TO_SMS_JOB(337, 0, "单点短信发送历史导入到短信任务", "addSingleSmsToSmsStep"),
    IMPORT_GROUP_SMS_TO_SMS_JOB(338, 0, "群发短信发送历史导入到短信任务", "addGroupSmsToSmsStep"),
    EXPORT_INTENT_MESSAGE_RECORD(339, 1, "导出外呼短信发送历史", "intentMessageRecordExportStep"),
    EXPORT_SINGLE_SMS_RECORD(342, 1, "导出单点短信发送历史", "singleSmsRecordExportStep"),
    EXPORT_GROUP_SMS_RECORD(343, 1, "导出群发短信发送历史", "groupSmsRecordExportStep"),
    EXPORT_SMS_JOB_COST_STATS_JOB(344, 1, "导出短信任务短信费用", "smsJobCostStatsExportStep"),
    EXPORT_CALLJOB_SMS_COST_STATS_JOB(345, 1, "导出外呼任务短信费用", "callJobSmsCostStatsExportStep"),
    EXPORT_CALLJOB_PIECE_SMS_COST_STATS_JOB(346, 1, "导出外呼任务短信费用", "callJobPieceSmsCostStatsExportStep"),
    EXPORT_SMS_TOTAL_COST_STATS_JOB(347, 1, "导出短信费用合计", "smsTotalCostStatsExportStep"),

    OPE_BILLING_ALIPAY_RECHARGE_EXPORT_JOB(350, 1, "OPE导出支付宝账户余额充值", "opeBillingAlipayRechargeExportStep"),
    OPE_BILLING_OFFLINE_RECHARGE_EXPORT_JOB(351, 1, "OPE导出线下账户余额充值", "opeBillingOfflineRechargeExportStep"),
    BILLING_RECHARGE_REFUND_EXPORT_JOB(352, 1, "导出交易流水", "billingRechargeRefundExportStep"),
    EXPORT_MA_CALL_COST(353, 1, "导出MA话费详单", "maCallCostDetailExportStep"),
    EXPORT_SMS_TEMPLATE(354, 0, "批量导入短信模板", "batchImportSmsTemplateStep"),

	EXPORT_CALL_COST_STATS_INFO(355, 1, "新版计费客户导出任务外呼通话费用统计", "callCostStatsInfoExportStep"),

	/**
	 * BOSS导出充值
	 */
	BOSS_BILLING_OFFLINE_RECHARGE_EXPORT_JOB(356, 1, "BOSS导出客户管理消费流水账户余额充值", "bossGetRechargeRefundListExportStep"),
	BOSS_TENANT_ACCOUNT_RECHARGE_EXPORT_JOB(357, 1, "BOSS导出充值记录账户余额充值", "bossTenantAccountRechargeExportStep"),
	BOSS_ALL_ACCOUNT_RECHARGE_EXPORT_JOB(358, 1, "BOSS导出通信费余额变动流水余额分配", "bossAllAccountRechargeExportStep"),

    CUSTOMER_PERSON_EXPORT_JOB(359, 1, "规则圈选人群", "filterCustomerBatch"),
    OPENSIPS_CALL_COST_EXPORT_JOB(360, 1, "直销客户外呼消耗成本", "opensispsCallCostExportStep"),

    SMS_BILLING_COST_EXPORT_JOB(361, 1, "直销客户短信消耗成本", "smsBillingCostExportStep"),

    EXPORT_PRIVACY_NUMBER_COST(362, 1, "导出隐私号计费统计", "exportPrivacyNumberCostStep"),
    EXPORT_PRIVACY_NUMBER_COST_OPE(363, 1, "OPE导出隐私号计费统计", "exportPrivacyNumberCostOpeStep"),

	SUPPLIER_MONTHLY_COST_EXPORT_JOB(370, 1, "导出供应商月成本", "exportSupplierMonthlyCostStep"),

    EXPORT_CALL_RESULT(371, 1, "导出呼叫结果", "exportCallResult"),


    RE_SEND_SMS_JOB(372,0,"短信补发","reSendIntentMessageStep"),
    
    EXPORT_MAGIC_BOT_BOSS_BILL_INFO(373, 1, "导出代理商后台计费详情", "billDetailListExportStep"),
    IMPORT_FIXED_PHONE_CONFIG_LIST(374, 0, "双呼固话配置导入", "fixedPhoneConfigStep"),

	EXPORT_PRIVACY_NUMBER_BINDING_OPE(375, 1, "OPE导出隐私号绑定记录", "exportPrivacyNumberBindingOpeStep"),

    ROBOT_TIME_CALL_TASK_IMPORT(376, 0, "择时外呼excel数据导入", "robotTimeCallTaskImportStep"),
    ROBOT_TIME_CALL_TASK_USER_GROUP_IMPORT(377, 0, "择时外呼人群包数据导入", "robotTimeCallTaskUserGroupImportStep"),

    PHONE_NUMBER_MONTHLY_RENT_EXPORT(378, 1, "线路月租导出", "phoneNumberMonthlyRentExportStep"),

    ROBOT_TIME_CALL_TASK_CACHE_USER_GROUP_IMPORT(379, 0, "择时外呼人群数据全量拉取", "robotTimeCallTaskCacheUserGroupImportStep"),

    ROBOT_TIME_CALL_TASK_TIME_USER_GROUP_IMPORT(380, 0, "择时外呼人群包数据全量导入", "robotTimeCallTaskTimeUserGroupImportStep"),

    GE_TUI_IMPORT_PHONE_NUMBER_TO_JOB(381, 0, "导入手机号码到短信任务", "geTuiSmsJobImportStep"),
    EXPORT_OPE_PRIVACY_NUMBER_HISTORY(382, 1, "导出隐私号联系历史", "privacyNumberOpeHistoryExportStep"),

    ROBOT_CALL_JOB_CUSTOMER_ANALYSIS(383,0,"外呼任务人群分析","robotCallJobCustomerAnalysisStep"),

    IMPORT_HISTORY_COMPLAINT_HISTORY_LIST(384, 0, "批量导入投诉记录", "complaintHistoryStep"),

    IMPORT_TASK_FROM_USER_GROUP(385, 0, "拉取人群到导入任务", "wphCustomerPersonUserGroupImportStep"),

    ROBOT_TIME_CALL_TASK_USER_GROUP_IMPORT_V2(386, 0, "择时外呼拉取人群到导入任务", "robotTimeCallTaskUserGroupImportStepV2"),

    UPLOAD_ORDER_CALLOUT_DATA(387, 0, "导入订单数据", "uploadOrderCallOutData"),

    EXPORT_FAILED_TENANT_ID(388, 0, "代理商客户批量操作", "exportFailedTenantIdStep"),

    EXPORT_OPE_FIXED_PHONE_CONFIG(389,1,"导出隐私号双呼固话号码管理","fixedPhoneConfigExportStep"),

    EXPORT_OPE_PRIVACY_NUMBER_AUTHENTICATION(390,1,"导出隐私号A路号码报备","privacyNumberAuthenticationExportStep"),

    ;
    private Integer code;
    private String desc;
    // 0 输出错误文件 1 输出导出文件
    private Integer fileType;
    //这个是统计导入导出成功失败条数的step, 这个step的存入数据库的名字，名字在step创建的时候写的
    private String jobCountStepName;

    @Getter
    private SystemEnum systemType;

    /**
     * 表头
     */
    @Getter
    private List<String> headerList;

    SpringBatchJobTypeEnum(Integer code, Integer fileType, String desc, String jobCountStepName, List<String> headerList, SystemEnum systemType) {
        this.code = code;
        this.desc = desc;
        this.fileType = fileType;
        this.jobCountStepName = jobCountStepName;
        this.headerList = headerList;
        this.systemType = systemType;
    }

    SpringBatchJobTypeEnum(Integer code, Integer fileType, String desc, String jobCountStepName) {
        this.code = code;
        this.desc = desc;
        this.fileType = fileType;
        this.jobCountStepName = jobCountStepName;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public boolean outputErrorFile() {
        return fileType == 0;
    }

    public String getJobCountStepName() { return jobCountStepName; }

    public boolean outputExportFile() {
        return fileType == 1;
    }

    public Integer getFileType(){
        return fileType;
    }

	/**
	 * 是否为excel导入客户
	 */
	public static boolean importFromExcel(SpringBatchJobTypeEnum type) {
    	return IMPORT_FROM_EXCEL.equals(type) || PLAN_IMPORT_CUSTOMERS_TO_JOB_SINGLE.equals(type) || PLAN_IMPORT_CUSTOMERS_TO_JOB_MULTI.equals(type) || IMPORT_FROM_USER_GROUP.equals(type);
    }

    public static SpringBatchJobTypeBO getByStepName(String jobCountStepName){
        if (Validator.hasChinese(jobCountStepName)){
            Map<String, Object> jobCountStepNameMap = EnumUtil.getNameFieldMap(SpringBatchJobTypeEnum.class, "desc");
            Map<Object, String> inverse = MapUtil.inverse(jobCountStepNameMap);
            String orDefault = inverse.getOrDefault(jobCountStepName, "UNKNOWN");
            SpringBatchJobTypeEnum jobTypeEnum = EnumUtil.fromString(SpringBatchJobTypeEnum.class, orDefault);
            SpringBatchJobTypeBO springBatchJobTypeBO = new SpringBatchJobTypeBO();
            springBatchJobTypeBO.setSpringBatchJobTypeEnum(jobTypeEnum);
            springBatchJobTypeBO.setDesc(jobCountStepName);
            return springBatchJobTypeBO;
        }

        SpringBatchJobTypeEnum[] values = SpringBatchJobTypeEnum.values();
        for (SpringBatchJobTypeEnum springBatchJobTypeEnum : values) {
            if (springBatchJobTypeEnum.getJobCountStepName().equals(jobCountStepName)){
                SpringBatchJobTypeBO springBatchJobTypeBO = new SpringBatchJobTypeBO();
                springBatchJobTypeBO.setSpringBatchJobTypeEnum(springBatchJobTypeEnum);
                springBatchJobTypeBO.setDesc(springBatchJobTypeEnum.getDesc());
                return springBatchJobTypeBO;
            }
        }
        return new SpringBatchJobTypeBO(UNKNOWN, jobCountStepName);
    }

    public static String getByDesc(String desc){

        String fieldBy = EnumUtil.getFieldBy(SpringBatchJobTypeEnum::getJobCountStepName, SpringBatchJobTypeEnum::getDesc, desc);
        return fieldBy;
    }

    public static void main(String[] args) {
        SpringBatchJobTypeBO toBeCalledTaskExportStep = getByStepName("toBeCalledTaskExportStep");
        log.info("---");
        Map<String, Object> jobCountStepName = EnumUtil.getNameFieldMap(SpringBatchJobTypeEnum.class, "desc");
        log.info("---");
        Map<Object, String> inverse = MapUtil.inverse(jobCountStepName);
        log.info("---");
        SpringBatchJobTypeEnum jobTypeEnum = EnumUtil.fromString(SpringBatchJobTypeEnum.class, "IMPORT_FROM_USER_GROUP");
        log.info("---");
        SpringBatchJobTypeBO byStepName = getByStepName("导出未呼客户列表");
        log.info("---");
    }

}
