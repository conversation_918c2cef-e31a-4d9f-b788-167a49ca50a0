package com.yiwise.core.model.dto.xiaoke;

import com.yiwise.core.model.enums.CustomerLevelEnum;
import com.yiwise.core.model.enums.CustomerPersonOrderByEnum;
import com.yiwise.core.model.enums.CustomerTypeEnum;
import com.yiwise.core.model.enums.RobotSeatTypeEnum;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * <AUTHOR> xiayu
 * @date : 2021/8/26 11:04
 **/
@Data
public class RobotAiSeatTimeToXiaokeDTO {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * ai坐席扩容的类型
     */
    private RobotSeatTypeEnum robotSeatTypeEnum;

    /**
     * 正式的才有用
     */
    private AccountTypeEnum accountTypeEnum;

    /**
     * ai开始时间
     */
    private LocalDate aiStartTime;

    /**
     * ai结束时间
     */
    private LocalDate aiEndTime;

}
