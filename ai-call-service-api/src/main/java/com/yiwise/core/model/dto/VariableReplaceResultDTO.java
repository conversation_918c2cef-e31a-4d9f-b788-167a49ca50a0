package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;
import static com.yiwise.core.batch.common.BatchConstant.Header.号码归属地;

@Data
@EqualsAndHashCode
public class VariableReplaceResultDTO implements ExcelRowModel {
    private ToBeCalledTaskExportDTO toBeCalledTaskExportDTO;
    private Boolean isReplaced;
    private String currentValue;
    private String description;


    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();

        retMap.put(客户名, toBeCalledTaskExportDTO.getCustomerPersonName());
        retMap.put(性别, toBeCalledTaskExportDTO.getGender());
        retMap.put(联系电话, toBeCalledTaskExportDTO.getCalledPhoneNumber());
        retMap.put(号码归属地, toBeCalledTaskExportDTO.getLocation()==null?"未知":toBeCalledTaskExportDTO.getLocation());
        retMap.put(自定义变量, currentValue);
        retMap.put(明细, description);

        return retMap;
    }
}
