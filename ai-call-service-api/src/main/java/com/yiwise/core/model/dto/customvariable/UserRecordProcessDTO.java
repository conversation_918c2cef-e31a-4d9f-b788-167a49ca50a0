package com.yiwise.core.model.dto.customvariable;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import lombok.NoArgsConstructor;

/**
 * Created on 2019-04-19.
 *
 * <AUTHOR>
 * email <EMAIL>
 * description 录音师自定变量录音进度
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRecordProcessDTO implements Serializable {
    /**
     * 录音师id
     */
    private Long recordUserId;

    /**
     * 录音数量
     */
    private Integer recordNum;

    /**
     * 变量id
     */
    private Long userVariableId;
}
