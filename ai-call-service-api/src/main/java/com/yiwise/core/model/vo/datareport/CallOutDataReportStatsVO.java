package com.yiwise.core.model.vo.datareport;


import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;


/**
 * <AUTHOR>
 */
@Data
public class CallOutDataReportStatsVO implements ExcelRowModel {


    /**
     * 字段选项ID
     */
    Long robotCallJobFieldOptionId;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段选项名称
     */
    private String optionName;

    /**
     * 客户名
     */
    private String tenantName;

    /**
     * 通话时长
     */
    private Long chatTime;

    /**
     * 接听率
     */
    private Double answeredRate;

    /**
     * AB类意向占比
     */
    private Double abRate;

    /**
     * 意向转化
     */
    private Double intentRate;

    /**
     * 接通转化
     */
    private Double answeredTransformRate;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> rowModelMap = new HashMap<>(16);
        rowModelMap.put(getFieldName(), getOptionName());
        rowModelMap.put(客户名, getTenantName());
        rowModelMap.put(通话时长, getChatTime());
        rowModelMap.put(接听率, getAnsweredRate());
        rowModelMap.put(AB类意向占比, getAbRate());
        rowModelMap.put(意向转化, getIntentRate());
        rowModelMap.put(接通转化, getAnsweredTransformRate());
        return rowModelMap;
    }
}
