package com.yiwise.core.model.vo.privacynumber.changgong;

import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberRemoteVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>@yiwise.com
 * @date: 2024 10 12 10:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChangGongResultResponseVO implements PrivacyNumberRemoteVO {

    private Boolean success;

    private Map<String, Object> data;

    private String code;

    private String msg;

    private String bindingId;

    private String requestId;

    @Override
    public boolean isSuccess() {
        return success;
    }

    @Override
    public String getAxCallingId() {
        // 双呼的callId
        return bindingId;
    }

}
