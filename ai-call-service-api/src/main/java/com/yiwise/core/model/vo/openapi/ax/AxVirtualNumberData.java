package com.yiwise.core.model.vo.openapi.ax;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by 昌夜 on 2021-03-15.
 */
@Data
public class AxVirtualNumberData implements Serializable {
    private static final long serialVersionUID = -5849512239748247968L;

    public static String TYPE_REAL = "real";
    public static String TYPE_VIRTUAL = "virtual";

    /**
     * 被叫客户ID
     */
    String customerId;

    /**
     * 主叫号码
     */
    String telA;

    /**
     * 被叫虚拟号码
     */
    String telX;

    /**
     * 返回虚拟号码类型：
     * virtual-返回虚拟被叫号码，默认
     * real-返回真实被叫号码,用于异常情况
     */
    String telXType;
}
