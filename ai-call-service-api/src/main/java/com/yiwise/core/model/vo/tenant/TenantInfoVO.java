package com.yiwise.core.model.vo.tenant;

import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.enums.OverDueEnum;
import com.yiwise.core.model.vo.ope.RobotExpirationTimeVO;
import lombok.AccessLevel;
import lombok.Data;
import java.io.Serializable;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @Date
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TenantInfoVO extends TenantPO implements Serializable {
    private Integer enabledAiCount;
    private Integer allAgentCount;
    private Integer assistantCount;
    private List<RobotExpirationTimeVO> robotExpirationTimeVOList;
    private OverDueEnum overDueEnum;
    //如果全部过期，这个是还有多少天不能登陆时间
    private long overDueDays;
}
