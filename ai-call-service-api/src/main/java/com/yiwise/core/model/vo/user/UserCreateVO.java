package com.yiwise.core.model.vo.user;

import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.UserStatusEnum;
import com.yiwise.core.validate.user.UserInsertValidate;
import com.yiwise.core.validate.user.UserUpdateValidate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserCreateVO extends UserPO implements Serializable {
    private Boolean isManager;
    @NotNull(message = "组织id不能为空！", groups = {UserInsertValidate.class})
    private Long organizationId;
    private SystemEnum systemType;
    private String confirmPassword;
    /**
     * OPE用户在aicc的角色
     */
    private Long aiccRoleId;

    @NotNull(message = "角色不能为空！", groups = {UserInsertValidate.class, UserUpdateValidate.class})
    private Long roleId;

    private UserStatusEnum status;

    private String pwd;
}
