package com.yiwise.core.model.vo.callrecord;

import com.yiwise.core.model.dto.mikangyun.CallDetailDTO;
import com.yiwise.reception.enums.ReceptionDialStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReceptionRecordVO {

    /**
     * 通话详情
     */
    private List<CallDetailDTO> callDetails;

    /**
     * 客户AI意向等级
     */
    private String intentLevel;

    /**
     * 客户关注点
     */
    private Set<String> customerConcern;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 自定义变量
     */
    private Map<String, String> properties;

    /**
     * 通话状态
     */
    private ReceptionDialStatusEnum dialStatus;
}
