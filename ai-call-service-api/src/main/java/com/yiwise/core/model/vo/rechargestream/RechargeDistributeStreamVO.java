package com.yiwise.core.model.vo.rechargestream;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.dal.entity.RechargeDistributeStreamPO;
import com.yiwise.core.model.enums.RechargeStreamHandleTypeEnum;
import com.yiwise.core.model.enums.rechargstream.RechargeBillApplyStatusEnum;
import com.yiwise.core.model.enums.rechargstream.handler.RechargeBillApplyStatusEnumHandler;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import tk.mybatis.mapper.annotation.ColumnType;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RechargeDistributeStreamVO extends RechargeDistributeStreamPO implements ExcelRowModel {
    String phoneName;
    String createUserName;
    String phoneNumber;
    String targetName;

    @JsonIgnore
    Boolean isQueryLineRecharge = false;
    @ColumnType(typeHandler = RechargeBillApplyStatusEnumHandler.class)
    RechargeBillApplyStatusEnum rechargeBillApplyStatus;
    @Override
    public Map<String, Object> getRowModelMap() {
        HashMap<String, Object> map = new HashMap<>(8);
        map.put(时间, getCreateTime());
        map.put(操作人, getCreateUserName());
        map.put(线路, getPhoneNumber());
        String accountFare;
        if (getAccountFare() % 1000L == 0) {
            accountFare = String.format("%d", getAccountFare() / 1000L);
        } else {
            accountFare = String.format("%.3f", getAccountFare() / 1000.0);
        }
        map.put(订单号,getOrderId());
        map.put(交易渠道,getRechargeMethod().getDesc());
        map.put(交易状态,getRechargeStatus().getDesc());
        map.put(支付宝交易号,getAliPayReference());
        map.put(账户余额,accountFare);
        if(RechargeStreamHandleTypeEnum.TENANT_LINE_RECHARGE.equals(getHandleType()) ||
                RechargeStreamHandleTypeEnum.TENANT_MESSAGE_RECHARGE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.TENANT_QC_RECHARGE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.QC_ACCOUNT_RECHARGE_ONLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.QC_ACCOUNT_RECHARGE_UNLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.PHONE_NUMBER_ACCOUNT_RECHARGE_ONLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.PHONE_NUMBER_ACCOUNT_RECHARGE_UNLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.MESSAGE_ACCOUNT_RECHARGE_ONLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.MESSAGE_ACCOUNT_RECHARGE_UNLINE.equals(getHandleType())||
                RechargeStreamHandleTypeEnum.TENANT_LINE_GATEWAY_RECHARGE.equals(getHandleType())) {
            map.put(交易类型, "充值");
        } else {
            map.put(交易类型, getHandleType().getDesc());
        }
        map.put(金额, getFare() / 1000.0);
        map.put(操作人, createUserName);
        map.put(备注, getRemark());
	    map.put(发票状态, getRechargeBillApplyStatus() == null ? null : getRechargeBillApplyStatus().getDesc());
        return map;
    }
}
