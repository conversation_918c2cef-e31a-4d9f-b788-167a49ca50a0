package com.yiwise.core.model.vo.distributorcustomer;

import com.yiwise.core.model.enums.RechargePrestoreStreamFunEnum;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerOpenFunctionValidate;
import lombok.Data;
import java.io.Serializable;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;


/**
 * <AUTHOR>   代理商客户开通功能时候 窗口显示信息
 * @date 2019/6/13 17:42
 */
@Data
public class DistributorCustomerOpenFunctionQueryVO implements Serializable {
    private Double objectCount;
    @NotNull(message = "代理商id不能为空", groups = {DistributorCustomerOpenFunctionValidate.class})
    private Long distributorId;
    @NotNull(message = "消费类型不能为空")
    private RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum;
    @NotNull(message = "客户id不能为空")
    private Long tenantId;
    private LocalDate startTime;
    private LocalDate endTime;
}
