package com.yiwise.core.model.vo.openapi.qiyu;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QiyuSendFlashMessageBO implements Serializable {

    /**
     * 客户ID
     */
    private Long tenantId;

    /**
     * 任务ID
     */
    private Long robotCallJobId;

    /**
     * 线路ID
     */
    private Long phoneNumberId;

    /**
     * 被叫号码
     */
    private String calledPhoneNumber;

    /**
     * 客户电话
     */
    private String userNumber;

    /**
     * 通话开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

}
