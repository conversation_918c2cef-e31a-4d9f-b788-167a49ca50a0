package com.yiwise.core.model.vo.callrecord;

import com.yiwise.base.model.bean.vo.AbstractQueryVO;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class CallRecordAsrQueryVO extends AbstractQueryVO implements Serializable {

    /**
     * 通话记录ID
     */
    private Long callRecordId;

    /**
     * 任务ID
     */
    private Long robotCallJobId;

    /**
     * 被叫号码
     */
    private String calledPhoneNumber;

}
