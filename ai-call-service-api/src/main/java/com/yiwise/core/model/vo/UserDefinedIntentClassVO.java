package com.yiwise.core.model.vo;

import lombok.Data;
import lombok.ToString;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ToString
public class UserDefinedIntentClassVO implements Serializable {
    @NotNull(message = "意向标签ID不能为空")
    Long intentLevelTagId;

    @NotNull(message = "用户自定义筛选类列表不能为空")
    List<Integer> intentLevelCodeList;
}
