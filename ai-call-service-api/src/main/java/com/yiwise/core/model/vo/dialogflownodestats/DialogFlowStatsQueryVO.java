package com.yiwise.core.model.vo.dialogflownodestats;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.model.enums.dialogflow.DialogFlowStepTypeEnum;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
public class DialogFlowStatsQueryVO {

    private Long tenantId;

    private Long dialogFlowId;

    private String dialogFlowStepId;

    private DialogFlowStepTypeEnum dialogFlowStepType;

    private Long dialogFlowNodeId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginDateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate localDate;

    private Boolean enableDistinct;

    private List<Long> callJobIdList;

    private String knowledgeId;
}
