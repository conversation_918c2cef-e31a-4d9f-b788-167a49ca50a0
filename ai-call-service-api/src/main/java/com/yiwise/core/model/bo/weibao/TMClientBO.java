package com.yiwise.core.model.bo.weibao;

import lombok.Data;
import java.io.Serializable;
import lombok.ToString;

import java.util.Map;

/**
 * @Author: zqy
 * @Date: 2019-10-16 13:54
 */
@Data
@ToString
public class TMClientBO implements Serializable {
    private String listRecId;                   //名单ID
    private String credentialType;              //证件类型
    private String credentialNo;                //证件号码
    private String name;                        //姓名掩码
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String birthdate;                //出生日期：yyyy-mm-dd
    private String gender;                      //性别：男、女、未知
    private String visitorId;                   //用户唯一标识
    private String wesurePolicyNo;              //微保保单号
    private String policyNo;                    //保单号码
    private String fakePhoneNo;                 //虚拟电话号码
//    @JsonFormat(pattern = "yyyyMMddHHmmssssss", timezone = "GMT+8")
    private String virtualphoneEffecTime;       //虚拟号可用起期
//    @JsonFormat(pattern = "yyyyMMddHHmmssssss", timezone = "GMT+8")
    private String virtualphoneExpireTime;      //虚拟号可用止期
    private String phoneArea;                   //号码归属地
    private String agentId;                     //坐席工号
    private String remark;                      //备注： 本次传保费
    private String externalName;                //客户微信名称
    private String attachInfo;     //附加信息
}
