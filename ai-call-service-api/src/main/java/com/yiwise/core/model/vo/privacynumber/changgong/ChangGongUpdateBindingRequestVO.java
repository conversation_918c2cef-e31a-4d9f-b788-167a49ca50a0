package com.yiwise.core.model.vo.privacynumber.changgong;

import lombok.Data;

/**
 * @author: wux<PERSON>yu<PERSON>@yiwise.com
 * @date: 2024 10 12 18:27
 */
@Data
public class ChangGongUpdateBindingRequestVO {

    /**
     * 绑定Id
     */
    public String bindingId;

    /**
     * 真实号码 不能为空
     */
    private String phoneNumberA;

    /**
     * 对端号码 可为空
     */
    private String phoneNumberB;

    /**
     * 超时时长，此字段为必填项，不允许为空。
     * 单位为秒。从绑定时刻起算，达到设定的expiration秒后会自动解绑；
     * 若设置为0，则表示不设定时间限制，不会自动解绑。
     */
    private Integer expiration;

    /**
     * 录音控制，此字段为必填项，不允许为空。
     * 有效取值如下，默认为0（不开通录音功能）：
     * 0：不开通录音功能
     * 1：开通录音功能
     */
    private String callRecording;

    /**
     * 来显控制，此字段可以为空。
     * 默认值为"0,0"（此默认值仅供参考，具体默认值需根据业务逻辑确定）。
     */
    private String callDisplay;

    /**
     * 呼叫控制，此字段可以为空。
     * 默认值为1。具体含义如下：
     * 1：AXB执行呼叫控制，A和B有权限，其他号码无权限（即现有的AXB模式）
     * 2：AXB的单通控制，A无权限，B有权限，其他号码无权限
     * 3：AXB的单通控制，A有权限，B以及其他号码无权限
     */
    private String callRestrict;

    /**
     * 短信下行控制，此字段可以为空。
     * 有效取值如下，默认为1：
     * 1：采用现有流程
     * 2：通过推送下发
     * 3：采用现有流程下发并推送短信内容
     */
    private String smsMtChannel;

    /**
     * 短信后缀，此字段可以为空。
     */
    private String smsSuffix;


}
