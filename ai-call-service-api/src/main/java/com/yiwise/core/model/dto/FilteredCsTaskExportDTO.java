package com.yiwise.core.model.dto;

import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.model.bo.filteredtask.FilteredCsCallJobTaskBO;
import com.yiwise.core.model.bo.filteredtask.FilteredRobotCallTaskBO;

import java.util.HashMap;
import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * @Author: zqy
 * @Date: 2020/12/21 13:54
 */
public class FilteredCsTaskExportDTO extends FilteredCsCallJobTaskBO implements ExcelRowModel {
    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put(客户名, getCustomerPersonName());
        retMap.put(性别, getGender() == null ? "" : getGender().getDesc());
        retMap.put(联系电话, getCalledPhoneNumber());
        retMap.put(号码归属地, getLocation());
        retMap.put(主叫号码, getTenantPhoneNumber());
        retMap.put(过滤原因, getFilterType() == null ? "" : getFilterType().getDesc());
        retMap.put(过滤时间, getCreateTime());
        return retMap;
    }
}
