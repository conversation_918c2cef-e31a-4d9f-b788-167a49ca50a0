package com.yiwise.core.model.vo.daily;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yiwise.core.batch.common.ExcelRowModel;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * 日报
 * <AUTHOR>
 * @create 2021/09/24
 */
@Data
public class TenantDailyVO implements ExcelRowModel {

	private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	private Long tenantId;

	/**
	 * 本条数据是否为汇总
	 */
	private Boolean isSummary;

	/**
	 * 是否是全部数据的总计
	 */
	private Boolean isTotal = Boolean.FALSE;

	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private LocalDate date;

	private Long robotCallJobId;
	private String robotCallJobName;

	private Long dialogFlowId;
	private String dialogFlowName;

	private String dailyTime;

	/**
	 * 当日导入客户数
	 */
	private Long importCount;

	/**
	 * 当日外部导入名单总量
	 */
	private Long leadInCountValue;


	/**
	 * 外呼总量  外呼客户数
	 */
	private Long callOutCount;

	/**
	 * 名单接通率(不去重)
	 */
	private String answeredRateNotDistinct;

	/**
	 * 名单接通率(去重)
	 */
	private String answeredRateDistinct;

	/**
	 * 过滤客户数
	 */
	private Long filteredCount;

	/**
	 * 过滤比例
	 */
	private String filteredRate;

	/**
	 * 名单过滤比
	 */
	private String filteredRate2;

	/**
	 * 接听客户数
	 */
	private Long answeredCount;

	/**
	 * 接听率
	 */
	private String answeredRate;
	/**
	 * 通话时长
	 */
	private Long realChatTime;
	/**
	 * 平均通话时长
	 */
	private String averageChatDuration;
	/**
	 * 挂机客户数
	 */
	private Long hangupCount;
	/**
	 * 挂机率
	 */
	private String hangupRate;

	private Long totalAnsweredCall;

	/**
	 * A类意向客户数
	 */
	private Long intentACount;
	/**
	 * A类意向客户占比
	 */
	private String intentARate;

	/**
	 * B类意向客户数
	 */
	private Long intentBCount;
	/**
	 * B类意向客户占比
	 */
	private String intentBRate;
	/**
	 * AB类意向客户数
	 */
	private Long intentABCount;
	/**
	 * AB类意向客户占比
	 */
	private String intentABRate;

	/**
	 * 主动加微发起数
	 */
	private Long addWechatSendCount = 0L;

	/**
	 * 主动加微通过数
	 */
	private Long addWechatAcceptCount = 0L;

	/**
	 * 主动加微通过率
	 */
	private String addWechatAcceptRate;

	/**
	 * 被动加微通过客户数
	 */
	private Long passiveAddWechatAcceptCount = 0L;

	/**
	 * 被动加微短链点击数
	 */
	private Long passiveAddWechatLinkClickCount = 0L;
	/**
	 * 被动加微短链点击率
	 */
	private String passiveAddWechatAcceptRate;
	/**
	 * 加微通过客户总数  主动+被动
	 */
	private Long addWechatAcceptAllCount;
	/**
	 * 加微失败客户数
	 */
	private Long addWechatFailCount = 0L;
	/**
	 * 已是好友客户数
	 */
	private Long addWechatIsFriendCount = 0L;
	/**
	 * 名单转化   加微通过客户总数/导入客户数
	 */
	private String intentionConversionRate;
	/**
	 * 接通转化   加微通过客户总数/接听客户数
	 */
	private String answeredConversionRate;
	/**
	 * 推送短信的客户总数
	 */
	private Long smsSendCount;
	/**
	 * 短信发送成功客户数
	 */
	private Long smsSendSuccessCount;
	/**
	 * 短信发送成功率  发送成功的客户/推送短信的客户总数
	 */
	private String smsSendSuccessRate;

	/**
	 * 短信模板接收成功客户数
	 */
	private Long smsTemplateAcceptCount;

	/**
	 * 短信接收成功客户数
	 * 兼容短信补发统计
	 */
	private Long smsAcceptCount;
	/**
	 * 短信接收成功客户率  接收成功/推送短信的客户总数
	 */
	private String smsAcceptRate;
	/**
	 * 任务消耗  外呼+短信 （按分钟/订阅制 也加该字段）
	 */
	private Double jobCost;


	// 冰鉴专属
	/**
	 * D类意向客户数
	 */
	private Long intentDCount;
	/**
	 * D类意向客户占比
	 */
	private String intentDRate;
	/**
	 * AD类意向客户数
	 */
	private Long intentADCount;
	/**
	 * AD类意向客户占比
	 */
	private String intentADRate;
	/**
	 * 计费时长
	 */
	private Long billChatTimeList;

	/**
	 * 过滤客户数(已呼客户去重)
	 */
	private Long filteredTaskNotCalledCount;

	/**
	 * 线路拦截数
	 */
	private Long interceptByLineCount;

	/**
	 * 接听率（剔除线路拦截）
	 * 已接听的客户数/外呼客户数（剔除线路拦截）
	 */
	private String answeredByInterceptByLineRate;


	public void calculateRate() {
		answeredRate = calculateRate(answeredCount, callOutCount);
		filteredRate = calculateRate(filteredCount, callOutCount + filteredCount);
		filteredRate2 = calculateRate(filteredCount, leadInCountValue);
		answeredRateDistinct = calculateRate(answeredCount, importCount);
		answeredRateNotDistinct = calculateRate(answeredCount, leadInCountValue);
		intentARate = calculateRate(intentACount, answeredCount);
		intentBRate = calculateRate(intentBCount, answeredCount);
		intentABCount = intentACount + intentBCount;
		intentABRate = calculateRate(intentABCount, answeredCount);
		addWechatAcceptRate = calculateRate(addWechatAcceptCount, addWechatSendCount);
		addWechatAcceptAllCount = (addWechatAcceptCount != null ? addWechatAcceptCount : 0L) + (passiveAddWechatAcceptCount != null ? passiveAddWechatAcceptCount : 0L);
		intentionConversionRate = calculateRate(addWechatAcceptAllCount, importCount);
		answeredConversionRate = calculateRate(addWechatAcceptAllCount, answeredCount);
		if (totalAnsweredCall != null && totalAnsweredCall > 0) {
			averageChatDuration = BigDecimal.valueOf(realChatTime).divide(BigDecimal.valueOf(totalAnsweredCall), 1, RoundingMode.HALF_UP).toString();
		}else {
			averageChatDuration = "0";
		}
		hangupRate = calculateRate(hangupCount, answeredCount);
		smsSendSuccessRate = calculateRate(smsSendSuccessCount, smsSendCount);
		smsAcceptRate = calculateRate(smsAcceptCount, smsSendCount);
		passiveAddWechatAcceptRate = calculateRate(passiveAddWechatLinkClickCount, smsTemplateAcceptCount);

		intentDRate = calculateRate(intentDCount, answeredCount);
		intentADCount = intentACount + intentDCount;
		intentADRate = calculateRate(intentADCount, answeredCount);
		answeredByInterceptByLineRate = calculateRate(answeredCount, callOutCount - interceptByLineCount);
	}

	/**
	 * 计算百分比
	 */
	private static String calculateRate(Long a, Long b) {
		if (a == null || b == null || a == 0 || b == 0) {
			return "0%";
		} else {
			return BigDecimal.valueOf(a).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(b), 1, RoundingMode.HALF_UP) + "%";
		}
	}

	@Override
	public Map<String, Object> getRowModelMap() {
		Map<String, Object> map = new HashMap<>();
//		map.put(汇总, getIsTotal() ? "总计" : getIsSummary() ? "小计" : 汇总 + "1");
		map.put(外呼日期, Objects.isNull(date) ? "" : formatter.format(date));
		map.put(外呼任务, getIsTotal() ? "总计" : getIsSummary() ? "小计" : getRobotCallJobName());
		map.put(任务ID, isSummary || isTotal ? "" : String.valueOf(robotCallJobId));
		map.put(使用话术, dialogFlowName);
		map.put(外呼时间段, dailyTime);

		map.put(当日导入客户数, importCount);
		map.put(当日外部导入名单数, leadInCountValue);
		map.put(计费分钟数_分钟, billChatTimeList);
		map.put(过滤客户数, filteredCount);
		map.put(过滤比例, filteredRate);
		map.put(名单过滤比, filteredRate2);
		map.put(外呼客户数, callOutCount);
		map.put(接听客户数, answeredCount);
		map.put(日报_接听率, answeredRate);
		map.put(名单接通率_去重, answeredRateDistinct);
		map.put(名单接通率_不去重, answeredRateNotDistinct);
		map.put(日报_平均通话时长, averageChatDuration);
		map.put(挂机客户数, hangupCount);
		map.put(日报_挂机率, hangupRate);

		map.put(日报_A类意向客户数, intentACount);
		map.put(A类意向客户占比, intentARate);
		map.put(日报_B类意向客户数, intentBCount);
		map.put(B类意向客户占比, intentBRate);
		map.put(AB类意向客户数, intentABCount);
		map.put(AB类意向客户占比, intentABRate);

		map.put(主动加微发起数客户数, addWechatSendCount);
		map.put(主动加微通过客户数, addWechatAcceptCount);
		map.put(主动加微通过率, addWechatAcceptRate);
		map.put(被动加微通过客户数, passiveAddWechatAcceptCount);
		map.put(被动加微短链点击数, passiveAddWechatLinkClickCount);
		map.put(被动加微短链点击率, passiveAddWechatAcceptRate);
		map.put(加微通过客户总数, addWechatAcceptAllCount);
		map.put(加微失败客户数, addWechatFailCount);
		map.put(已是好友客户数, addWechatIsFriendCount);
		map.put(名单转化, intentionConversionRate);
		map.put(日报_接通转化, answeredConversionRate);

		map.put(短信发送成功客户数, smsSendSuccessCount);
		map.put(短信发送成功率, smsSendSuccessRate);
		map.put(短信接收成功客户数, smsAcceptCount);
		map.put(短信接收成功率, smsAcceptRate);
		map.put(任务消耗, jobCost);

		map.put(线路拦截数, interceptByLineCount);
		map.put(日报_接听率_剔除线路拦截, answeredByInterceptByLineRate);
		return map;
	}
}
