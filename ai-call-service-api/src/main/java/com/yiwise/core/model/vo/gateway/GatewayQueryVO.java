package com.yiwise.core.model.vo.gateway;

import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询vo
 * @create 2018/11/27
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayQueryVO extends AbstractQueryVO implements Serializable {

    @Nullable
    private String searchWords;

    /**
     * VOIP账号
     */
    private String sipAccount;

    /**
     * 归属者
     */
    private String owner;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 绑定的用户名或联系人
     */
    private String tenantName;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 设备ID
     */
    private List<String> deviceIds;
}
