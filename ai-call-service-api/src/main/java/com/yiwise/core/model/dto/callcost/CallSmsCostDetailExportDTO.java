package com.yiwise.core.model.dto.callcost;

import lombok.Data;
import java.io.Serializable;
import lombok.EqualsAndHashCode;

import java.util.Map;

import static com.yiwise.core.batch.common.BatchConstant.Header.*;

/**
 * <AUTHOR>
 * @description crm-子账号话单-导出话单明细
 * @create 2018/12/13
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CallSmsCostDetailExportDTO extends CallCostDetailExportDTO implements Serializable {

    private Long phoneNumberId;

    private String phoneLocation;

    private String smsTemplate;

    private Integer smsCount;

    private Long smsFare;

    @Override
    public Map<String, Object> getRowModelMap() {
        Map<String, Object> map = super.getRowModelMap();

        // 呼叫时长单位换成分钟
        Long time = getChatDuration();
        if (time != null) {
            map.put(时长, time / 60 + (time % 60 == 0 ? 0 : 1));
        }

        Long callCost = getCallCost();
        map.put(话费, callCost == null ? null : callCost / 1000.0);
        map.put(线路名, getCallingPhoneNumberName());
        map.put(号码归属地, phoneLocation);

        map.put(短信模板, smsTemplate);
        map.put(短信数量, smsCount);
        map.put(短信费, smsFare == null ? null : smsFare / 1000.0);
        return map;
    }
}
