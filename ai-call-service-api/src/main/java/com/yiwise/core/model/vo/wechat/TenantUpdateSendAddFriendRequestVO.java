package com.yiwise.core.model.vo.wechat;

import com.yiwise.core.model.enums.WechatCpAddFriendEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/01/27
 */
@Data
public class TenantUpdateSendAddFriendRequestVO {

    @NotNull
    Long tenantId;

    @NotNull
    WechatCpAddFriendEnum wechatCpAddFriend;

    String wechatCpToken;

    /**
     * 艾客参数
     */
    Map<String, String> aikeProperties;

    /**
     * SCRM参数
     */
	private String addWechatEncryptUrl;
	private String addWechatEncryptAppKey;
	private String addWechatEncryptAppSecret;
	private String addWechatEncryptJson;
}
