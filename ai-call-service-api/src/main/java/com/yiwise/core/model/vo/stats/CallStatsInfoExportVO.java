package com.yiwise.core.model.vo.stats;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yiwise.core.model.enums.StatsDimEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.stats.DimensionEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/02/10
 */
@Data
public class CallStatsInfoExportVO {

	Integer type;
	List<Long> statsId;
	List<Long> robotCallJobIds;
	List<Long> dialogFlowIds;

	StatsDimEnum statsDim;

	@NotNull
	LocalDate startDate;

	@NotNull
	LocalDate endDate;

	Boolean order = true;

	DimensionEnum globalParam = DimensionEnum.CUSTOMER_DIM;

	Long orgId;

	Long callOutPlanId;

	@JsonProperty("userId")
	Long queryUserId;

	SystemEnum systemType;

	List<String> headerList;

	Integer pageNum = 1;

	Integer pageSize = 10000;

	Long tenantId;

	Boolean isGroup = false;

}
