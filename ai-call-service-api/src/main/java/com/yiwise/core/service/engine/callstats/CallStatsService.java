package com.yiwise.core.service.engine.callstats;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.CallRecordLocationAnalysisBO;
import com.yiwise.core.model.bo.callstats.*;
import com.yiwise.core.model.bo.job.JobTaskStatsSimpleInfoBO;
import com.yiwise.core.model.bo.robotcalljob.TaskCallResultBO;
import com.yiwise.core.model.bo.sms.SmsStatsBO;
import com.yiwise.core.model.dto.distributor.BillInfoResultDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallInCostTypeEnum;
import com.yiwise.core.model.enums.stats.DimensionEnum;
import com.yiwise.core.model.request.CustomerAnalysisRequest;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.billing.BillingCalloutContext;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.customer.CustomerConcernVO;
import com.yiwise.core.model.vo.customer.KnowledgeActiveVO;
import com.yiwise.core.model.vo.ope.CallJobMonitorRobotCallJobStatsVO;
import com.yiwise.core.model.vo.ope.CallJobMonitorWechatStatsVO;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountAnalysisResultVO;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> yangdehong
 * @date : 2018/9/5 16:17
 */
public interface CallStatsService {
    /**
     * 在TaskCall完成并且所有的异步工作结束的时候执行任务的统计工作
     */
    void doWhenRobotCallTaskAsyncFinished(CallRecordLocationAnalysisBO analysis);

    void doWhenRobotFilteredTaskAsyncFinished(FilteredRobotCallTaskPO filteredRobotCallTaskPO, LocalDateTime currDateTime, Long intentLevelTagId);

    /**
     * 商机分析
     *
     * @param tenantId  客户ID
     * @param statsId   统计对象id
     * @param statsDim  统计维度(CallJob或DialogFlow)
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    BusinessAnalysisInfoBO getBusinessAnalysisInfo(Long tenantId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,Long userId);

    List<KnowledgeActiveVO> getKnowledgeActiveList(Long tenantId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate);

    List<CustomerConcernVO> getCustomerConcernList(Long tenantId, Long statsId, StatsDimEnum statsDim,
                                                   LocalDate startDate, LocalDate endDate);
    /**
     * 客户维度时段分析
     *
     * @param tenantId  客户ID
     * @param statsId   任务/话术 ID
     * @param statsDim  统计维度(CallJob或DialogFlow)
     * @param userId    操作人id
     * @param startDate 统计开始时间
     * @param endDate   统计结束时间
     * @param analysisInfoType   分析的类型
     * @return 结果
     */
    DuringAnalysisInfoBO getDuringAnalysisInfo(Long tenantId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate, Long userId, AnalysisInfoTypeEnum analysisInfoType);

    /**
     * 接待统计
     *
     * @param tenantId  客户ID
     * @param callInReceptionIdList  接待场景id
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期范围内的统计信息
     */
    CallInReceptionStatusBO getCallInReceptionStats(Long tenantId, List<Long> callInReceptionIdList,LocalDate startDate, LocalDate endDate);


    /**
     * 接待统计  ---当天的接待统计
     *
     * @param tenantId  客户ID
     * @param callInReceptionIdList  接待场景id
     */
    CallInReceptionStatusForDailyBO getCallInReceptionStatsForDaily(Long tenantId, List<Long> callInReceptionIdList);

    /**
     * 导出通话统计
     *
     * @param tenantId  客户id
     * @param callInReceptionIdList    接待场景ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 导出任务的启动信息
     */
    JobStartResultVO exportCallInReception(Long tenantId, Long userId,List<Long> callInReceptionIdList,LocalDate startDate, LocalDate endDate,SystemEnum systemType);

    /**
     * 首页通话统计
     */
    IndexStatsBO getIndexDateCallStatsInfo(Long intentLevelTagId, UserPO userPO,  Long tenantId,Long userId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,SystemEnum systemEnum);
    IndexStatsBO getIndexDateCallStatsInfo(Long intentLevelTagId, UserPO userPO, Long tenantId, Long userId, Long orgId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,SystemEnum systemType);

    /**
     * AICC外呼首页数据改版
     */
    IndexStatsBO getIndexDateCallStatsInfoNew(Long intentLevelTagId, UserPO userPO, Long tenantId, Long userId, Long orgId, List<Long> statsIds, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate);

    /**
     * 某个任务统计数据
     *
     * @param tenantId       客户ID
     * @param robotCallJobId 任务id
     * @return 当天任务的统计信息
     */
    CallJobStatsInfoBO getCallJobStatsInfo(Long tenantId, Long robotCallJobId);

    /**
     * 任务概况按小时统计折线图
     */
    List<IndexCallStatsLinePartBO> callJobStatsLineChart(Long tenantId, LocalDate statsDate, Long robotCallJobId);

	List<CallJobStatsInfoBO> getCallJobStatsInfoList(List<RobotCallJobPO> robotCallJobPOS);

    /**
     * 获取该任务中task的总量和当前执行完成的数量
     *
     * @param tenantId       租户id
     * @param robotCallJobId 任务id
     * @return <Task总量, 完成的Task总量>
     */
    JobTaskStatsSimpleInfoBO getJobTaskCountInfo(Long tenantId, Long robotCallJobId);

    /**
     * 获取任务的统计信息
     */
    Map<Long, JobTaskStatsSimpleInfoBO> getJobTaskCountInfoList(Long tenantId, Collection<Long> robotCallJobIdCollection);

    /**
     * 获取总的任务进度
     */
    Long getTotalTask(Long tenantId, Long statsId);

    /**
     * 获取导入任务数量
     */
    Map<Long, Long> getTotalTaskMap(Collection<Long> robotCallJobIds);

    Long getTotalTaskByDialogFlowId(Long tenantId, Long dialogFlowId);

    /**
     * 获取已完成的任务进度
     */
    Long getCompleteTask(Long tenantId, Long statsId, String collectionName);
    /**
     * 获取总的任务进度列表
     */
    List<Long> getTotalTaskList( List<Long> statsIds, String collectionName);

    /**
     * 获取已完成的任务进度列表
     */
    List<Long> getCompleteTaskList( List<Long> statsIds, String collectionName);

    /**
     * 更新任务中子任务的总量
     *
     * @param tenantId       租户id
     * @param robotCallJobId 任务id
     * @param dialogFlowId   话术id
     * @param taskCount      改变的task的总计
     */
    void updateCallJobTotalTask(Long tenantId, Long robotCallJobId, Long dialogFlowId, int taskCount);

	/**
	 * 任务的导入客户数
	 */
	void incrementJobImportTaskDaily(Long robotCallJobId, Long dialogFlowId, int count);

	/**
	 * 查询日期段内外呼任务导入客户数(按客户去重), 含首日和最后一天
	 */
	Map<Long, Long> getJobImportTaskDailyCount(Integer type, List<Long> robotCallJobId, LocalDate startDate, LocalDate endDate);

    /**
     * 删除任务中的子任务总量
     * @param tenantId 租户id
     * @param robotCallJobId 任务id
     * @param taskCount 删除任务数量
     */
    void deleteCallJobTotalTask(Long tenantId, Long robotCallJobId, Long dialogFlowId, int taskCount);

    /**
     * 导出通话统计
     *
     * @param tenantId  客户id
     * @param userId    用户id
     * @param statsIds   统计对象id
     * @param statsDim  统计维度(CallJob或DialogFlow)
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param currTenantId
     * @return 导出任务的启动信息
     */
    JobStartResultVO exportCallStatsInfo(Long tenantId, Long userId, List<Long> statsIds, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate, SystemEnum systemType,
                                         DimensionEnum globalParam, Boolean order, UserPO userPO, Long queryUserId, Long orgId, Long callOutPlanId, List<String> headerList, Boolean isGroup, Long currTenantId);

    JobStartResultVO exportCallStatsInfoByTask(Integer type, Integer pageNum, Integer pageSize, Long tenantId, Long currUserId,  Long userId, LocalDate startDate, LocalDate endDate, SystemEnum systemType,
                                               DimensionEnum globalParam, Boolean order, UserPO userPO, Long queryUserId, Long orgId, Long callOutPlanId, List<Long> robotCallJobIds, List<String> headerList, Boolean isGroup, Long currTenantId);

    JobStartResultVO exportCustomerConcern(Long tenantId, Long userId, Long statsId, StatsDimEnum statsDim,
                                           LocalDate startDate, LocalDate endDate, Boolean isAll,
                                           SystemEnum systemType, DimensionEnum globalParam, Boolean order,
                                           UserPO userPO, Long queryUserId, Long orgId);

    JobStartResultVO exportKnowledgeActive(Long tenantId, Long userId, Long statsIds, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate, SystemEnum systemType, DimensionEnum globalParam, Boolean order, UserPO userPO, Long queryUserId, Long orgId);

    /**
     * 提供通话统计信息
     *
     * @param tenantId  客户id
     * @param statsIds   统计对象id
     * @param statsDim  统计维度(CallJob或DialogFlow)
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期范围内的统计信息
     */
    CallJobDateRoundBO callBackCallStatsInfo(Long tenantId, List<Long> statsIds, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate, DimensionEnum globalParam, Boolean order, UserPO userPO, Long userId, Long orgId, Long callOutPlanId, SystemEnum systemType, Boolean isGroup);

    PageResultObject<DateStatsTableItemBO> callBackCallStatsInfoByTask(Integer type, Integer pageNum, Integer pageSize, Long tenantId, Long currUserId, LocalDate startDate, LocalDate endDate, DimensionEnum globalParam, Boolean order, UserPO userPO, Long userId, Long orgId, Long callOutPlanId, List<Long> robotCallJobIds, SystemEnum systemType, Boolean isGroup);

    DateStatsTableItemBO callBackCallStatsInfoTotalByTask(Integer type, Long tenantId, LocalDate startDate, LocalDate endDate, DimensionEnum globalParam, UserPO userPO,Long currUserId,  Long userId, Long orgId, Long callOutPlanId, List<Long> robotCallJobIds, SystemEnum systemType, Boolean isGroup);

    /**
     * 提供接待通话统计信息
     *
     * @param tenantId  客户id
     * @param callInReceptionIdList   接待场景ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期范围内的统计信息
     */
    List<DateCallInReceptionStatsTableItemBO> callBackCallInReception(Long tenantId, List<Long> callInReceptionIdList,LocalDate startDate, LocalDate endDate);

    /**
     * 导出话单计费统计-短信费用
     */
    JobStartResultVO exportMessageCostInfo(Long tenantId, Long userId, LocalDate startDate, LocalDate endDate, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId);

    List<OneDaySmsStatsItemBO> callBackMessageCostInfo(Long tenantId, LocalDate startDate, LocalDate endDate, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,Long userId);

    /**
     * 导出话单计费统计（月）-短信费用
     */
    JobStartResultVO exportMessageCostMonthInfo(Long tenantId, Long dialogFlowId, Long userId, Integer monthCount, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,Long queryUserId,SystemEnum systemType);

    List<OneDaySmsStatsItemBO> callBackMessageCostMonthInfo(Long tenantId, Long dialogFlowId, Integer monthCount, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,Long userId);

    CallCostTotalBO getCallCostTotalStatusInfo(CallCostQueryVO callCostQuery);

    /**
     * 用于小程序中需要可执行/可停止任务的统计
     */
    List<SingleCallJobStatsInfoBO> getCallJobStatsInfoByIds(Long tenantId, Set<Long> robotCallJobIds);

    /**
     * 人工外呼话单计费
     */
    CallCostStatsBO getCsCallCostStatsInfo(Long tenantId, LocalDate startDate, LocalDate endDate, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId, Long userId,CallInCostTypeEnum callInCostType);

    /**
     * 子账号通话统计/天
     */
    UserCallJobStatsCostBO getUserCsCallStatInfo(Long userId, Long phoneNumberId, Boolean local, LocalDate startDate, LocalDate endDate);

    /**
     * 子账号通话统计/月
     */
    UserCallJobStatsCostBO getUserCsCallStatMonthInfo(Long userId, Long phoneNumberId, Boolean local,
                                                      Integer monthCount);

    void getLastHourCallStatsCount();

    int resetHourCallStatsCount(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取运营商统计
     */
    RobotCallJobMobileOperatorStatsBO getCallJobOperatorStats(Long robotCallJobId);

    /**
     * 导出人工外呼导出通话费用-月
     */
    JobStartResultVO exportCsCallCostMonthInfo(Long tenantId, Long userId, LocalDate startMonth, LocalDate endMonth, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,SystemEnum systemType);

    /**
     * 导出人工外呼导出通话费用-日
     */
    JobStartResultVO exportCsCallCostInfo(Long tenantId, Long userId, LocalDate startDate, LocalDate endDate, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,Long queryUserId,SystemEnum systemType);

    CallCostStatsBO csCallCostStatsMonthInfo(Long tenantId, LocalDate startMonth, LocalDate endMonth, Boolean local, Long phoneNumberId, List<Long> robotCallTaskId,Long userId,CallInCostTypeEnum callCostType);

    Long allBillChatTime(LocalDate startDate, LocalDate endDate, Long phoneNumberId, Long callStatusId);

    CallJobStatsInfoBO getCallDialogStatsInfo(Long tenantId, Long dialogFlowId);
    /**
     * 通话统计
     *
     * @param tenantId  客户ID
     * @param statsId   统计对象id
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期范围内的统计信息
     */
    CallJobDateRoundBO getCallStatsInfo(Long tenantId, List<Long> statsId, StatsDimEnum statsDim, LocalDate startDate,
                                        LocalDate endDate, DimensionEnum globalParam, Boolean order, UserPO userPO, Long userId,
                                        Long orgId, Long callOutPlanId, SystemEnum systemType, Boolean isGroup);

    PageResultObject<DateStatsTableItemBO> getCallStatsInfoByTask(Integer pageNum, Integer pageSize, Long tenantId, Long currUserId,
                                                                LocalDate startDate, LocalDate endDate, DimensionEnum globalParam,
                                                                Boolean order, List<Long> robotCallJobIds, UserPO userPO, Long userId,
                                                                Long orgId, Long callOutPlanId, SystemEnum systemType, Boolean isGroup);

    PageResultObject<DateStatsTableItemBO> getCallStatsInfoByDialogFlow(Integer pageNum, Integer pageSize, Long tenantId, Long currUserId,
                                                                      LocalDate startDate, LocalDate endDate, DimensionEnum globalParam,
                                                                      Boolean order, List<Long> robotCallJobIds,Long callOutPlanId,
                                                                      UserPO userPO, Long userId, Long orgId, SystemEnum systemType, Boolean isGroup);

    DateStatsTableItemBO getCallStatsInfoTotalByTask(Integer type, Long tenantId, Long currUserId, LocalDate startDate, LocalDate endDate,
                                                     DimensionEnum globalParam, UserPO userPO, Long userId, Long orgId, List<Long> robotCallJobIds,
                                                     Long callOutPlanId, SystemEnum systemType, Boolean isGroup);

    /**
     * 更新外部导入名单数
     * @param tenantId
     * @param userId
     * @param robotCallJobPO
     * @param count
     */
    void updateLeadInCount(Long tenantId, Long userId, RobotCallJobPO robotCallJobPO, int count);

    /**
     * @param robotCallJobId 呼叫任务Id
     * @param dialogFlowId 话术ID
     * @param toBeAdd 导入新客户数
     */
    void updateCustomerNumber(Long robotCallJobId, Long dialogFlowId, Long toBeAdd);

    /**
     * 降低导入客户数字段
     *
     * @param robotCallJobId 要修改的任务id
     * @param dialogFlowId 话术ID
     * @param reduceCount    要减少的
     */
    void reduceCustomerNumber(Long robotCallJobId, Long dialogFlowId, Long reduceCount);

    /**
     * @param robotCallJobId 呼叫任务Id
     * @return 导入客户数
     */
    Long getCustomerNumber(Long robotCallJobId);

    Long getCustomerNumberMultiJob(List<Long> robotCallJobIdList, Long tenantId);

    Long getCustomerNumberDialogFlow(Long dialogFlowId, Long tenantId);
    /**
     * 商机分析-归属地
     *
     * @param tenantId  客户ID
     * @param statsId   统计对象id
     * @param statsDim  统计维度(CallJob或DialogFlow)
     * @param startDate 开始日期
     * @param endDate   结束日期
     */

    BusinessLocationAnalysisBO getBusinessLocationAnalysisInfo(Long tenantId, Long statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,Long userId,Long phoneNumberId,ProvinceEnum provinceEnum);

    List<DateStatsTableItemAPIBO> getCallStatsInfoAPI(Long tenantId, LocalDate startDate, LocalDate endDate, DimensionEnum globalParam, Boolean order);

    /**
     *
     * @param tenantId      客户id
     * @param userId        用户id
     * @param statsId       统计对象id
     * @param statsDim      统计维度(CallJob，DialogFlow或IntentLevel)
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param globalParam   统计维度(CUSTOMER_DIM或CALL_JOB_DIM)
     * @param order         按日期正序或者倒序排列
     * @return              创建导出任务
     */
    JobStartResultVO exportCallStatsDetail(Long tenantId, Long userId, List<Long> statsId, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,
                                           SystemEnum systemType, DimensionEnum globalParam, Boolean order, UserPO user, Long queryUserId, Long orgId, Long callOutPlanId);

    /**
     * 提供通话统计信息
     *
     * @param tenantId  客户id
     * @param statsIds   统计对象id
     * @param statsDim  统计维度(CallJob，DialogFlow或IntentLevel)
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param globalParam 统计维度(CUSTOMER_DIM或CALL_JOB_DIM)
     * @param order     按日期正序或者倒序排列
     * @return 日期范围内的统计信息
     */
    List<CallStatsDetailExportRowModel> callBackCallStatsDetail(Long tenantId, List<Long> statsIds, StatsDimEnum statsDim, LocalDate startDate, LocalDate endDate,
                                                                DimensionEnum globalParam, Boolean order, UserPO user, Long userId, Long orgId, Long callOutPlanId, SystemEnum system);

    /**
     * 外呼首页通话状态折线图
     */
    List<IndexCallStatsLinePartBO> indexCallStatsLineChart(LocalDate startDate, LocalDate endDate, Long tenantId);

    /**
     * 外呼首页客户意向等级饼图
     */
    Map<String, Object> indexIntentLevelPieChart(LocalDate startDate, LocalDate endDate, Long tenantId, Long intentLevelTagId);

    /**
     * 按月查询有外呼任务的日期
     */
    List<LocalDate> getDateListWithValidData(Long tenantId, LocalDate dateMonth, Long robotCallJobId);

	/**
	 * 外呼过滤query
	 */
	Query getCallStatsQuery(Long tenantId, Long distributorId, Long userId, Long callStatsId, Long dialogFlowId, int year, int month, int day, int hour, Long tagId);
    Query getCallStatsQuery(Long tenantId, Long distributorId, Long userId, Long callStatsId, Long callOutPlanId,  Long dialogFlowId, int year, int month, int day, int hour, Long tagId);

	/**
	 * 外呼结束后更新统计信息的query
	 */
	Query getCallStatsQuery(Long tenantId, TenantPayTypeEnum tenantPayType, Long userId, Long callStatsId, Long dialogFlowId, int year, int month, int day,
	                        int hour, boolean local, Long phoneNumberId, Long tagId, Long aiFare, Long comFare, Long extensionFare, Long distributorId, Long callOutPlanId, boolean formal, Integer fareVersion);
	Query getCallStatsQuery(TenantPO tenant, RobotCallJobPO robotCallJob, CallRecordPO callRecord);

    Query getCallStatsQuery(BillingCalloutContext context);

	/**
	 * 外呼结束后更新统计信息的update
	 */
	Update getCallStatsUpdate(long callCost, long smsBillAmount, long smsCost, int smsAmount, Long ringCost, CallRecordPO callRecord, Long billChatDuration,
	                          Long billRingDuration, TaskCallResultBO taskCallResultInfo, Long linePay, Long smsPay, Long factCost, SmsStatsBO smsStats, Boolean notAddTotalAnsweredCall);

	/**
	 * 外呼结束后更新上次通话统计信息的update
	 */
	Update getLastCallStatsUpdate(CallRecordPO preCallRecord);

    /**
     * 根据任务列表获取任务的统计数据
     */
    Map<Long, CallJobMonitorRobotCallJobStatsVO> getRobotCallJobStatsMapByIds(List<Long> robotCallJobIds);
	/**
	 * startDate != null && endDate != null 筛选才生效, 均为inclusive
	 */
	Map<Long, CallJobMonitorRobotCallJobStatsVO> getRobotCallJobStatsMapByIds(List<Long> robotCallJobIds, LocalDate startDate, LocalDate endDate);

    /**
     * 根据任务列表获取加微的统计数据
     */
    Map<Long, CallJobMonitorWechatStatsVO> getWechatStatsMapByIds(List<Long> robotCallJobIdList);
	/**
	 * startDate != null && endDate != null 筛选才生效, 均为inclusive
	 */
    Map<Long, CallJobMonitorWechatStatsVO> getWechatStatsMapByIds(List<Long> robotCallJobIdList, LocalDate startDate, LocalDate endDate);

    /**
     * 查询在指定日期产生了外呼费用或短信费用的客户id集合
     */
    Set<Long> getTenantIdsWithFare(LocalDate localDate);

    void doWithCallOutMonthlyPrice(Long phoneNumberId, Long tenantId, Long callCost, LocalDateTime localDateTime);

    Set<Long> getCalledTenant(LocalDate startDate, LocalDate endDate);

    Long getTenantTodayAnswerCount(Long tenantId, LocalDate today);

    void sendAntCustomerImportMessage();

    void sendAntCalloutStatsMessage();

    void sendAntCalloutStatsMessage(Long tenantId,Long robotCallJobId);

    void sendAntCustomerImportMessageByTenantIds(List<Long> tenantIdList);

    void sendAntCalloutStatsMessageByTenantIds(List<Long> tenantIdList);

    List<BillInfoResultDTO> getBillInfoResult(LocalDate startDate, LocalDate endDate, Long distributorId, Integer pageNum);

    JobStartResultVO doCustomerAnalysis(CustomerAnalysisRequest request);

    AccountAnalysisResultVO getCustomerAnalysis(Long robotCallJobId, Long tenantId, Long userId, boolean newVersion);
}
