package com.yiwise.core.service.file;

import com.yiwise.core.model.enums.CustomerPersonListEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.calloutplan.PlanImportCustomerTypeEnum;
import com.yiwise.middleware.objectstorage.common.WebDirectUploadSignatureVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 上传下载文件
 * @create 2018/09/27
 **/
public interface FileService {

    /**
     * 上传文件到oss，返回文件路径
     *
     * @param tenantId      公司id
     * @param userId        用户id
     * @param expireTime    过期时间（如果为临时保存）
     * @param multipartFile 用户上传的文件
     * @return 文件在oss上的路径
     */
    Map<String, String> upload(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, boolean excelCheck);

    Map<String, String> uploadForNotExpire(Long tenantId, Long userId, String ossKey, MultipartFile multipartFile, boolean excelCheck);
    Map<String, String> uploadAndCheckRequiredField(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, Long robotCallJobId, CustomerPersonListEnum customerPersonListType, SystemEnum systemType);
    Map<String, String> smsUploadAndCheckRequiredField(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, Long smsJobId, SystemEnum systemType);
    Map<String, String> batchJobUploadAndCheckRequiredField(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile,CustomerPersonListEnum customerPersonListType, SystemEnum systemType);
    Map<String, String> uploadAndCheckRequiredFieldWhiteList(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, SystemEnum systemType);
    Map<String, String> uploadQiyu(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, boolean excelCheck);

	/**
	 * 外呼计划导入客户excel
	 */
	Map<String, String> uploadAndCheckRequiredField(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, List<Long> robotCallJobIds,
	                                                CustomerPersonListEnum customerPersonListType, SystemEnum systemType, PlanImportCustomerTypeEnum planImportType);

    /**
     *上传外呼计划模板
     */
    String uploadCallOutPlanTemplate(MultipartFile multipartFile);
    /**
     * 外呼计划导入任务
     */
    Map<String, String> uploadForCallOutPlanJob(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, SystemEnum systemType);
    /**
     *冰鉴 定制化解密服务
     */
    Map<String, String> uploadForBindjianDecrypt(Long tenantId, Long userId, Integer expireTime, MultipartFile multipartFile, SystemEnum systemType);
    String testDecrypt(String original);
    /**
     *
     * @param dirPath
     * @return
     */
    WebDirectUploadSignatureVO getUploadSignature(String dirPath, Integer maxUpload);

    WebDirectUploadSignatureVO getUploadSignatureWithTime(String dirPath, Integer maxUpload, Long time);
    /**
     * 文本客服的文件上传
     * @param dirPath
     * @return
     */
    WebDirectUploadSignatureVO getUploadSignatureForText(String dirPath, Integer maxUpload);

    Map<String, String> uploadVisitorFile(String visitorInfoNum, Integer expireTime, MultipartFile file, Boolean checkExcel);

    /**
     * 上传文件到oss，返回文件路径
     *
     * @param tenantId      公司id
     * @param userId        用户id
     * @param expireTime    过期时间（如果为临时保存）
     * @param fileUrl 用户上传的文件
     * @return 文件在oss上的路径
     */
    Map<String, String> uploadForWeChat(Long tenantId, Long userId, Integer expireTime, String fileUrl, boolean excelCheck,String fileName);

    Map<String, String> uploadPlatform(Long tenantId, Long userId, Integer expireTime, MultipartFile file, Boolean checkExcel);

    Map<String, String> uploadWelcomeMsg(Long tenantId, Long userId, MultipartFile multipartFile);

    Map<String, String> uploadAndCheckRequiredFieldByFixedPhoneConfig(Long userId, MultipartFile multipartFile);

    Map<String, String> uploadAndCheckComplaintRequiredField(Long userId, MultipartFile multipartFile);

    Map<String, String> uploadCompressImgForNotExpire(String ossKey, MultipartFile multipartFile);
}
