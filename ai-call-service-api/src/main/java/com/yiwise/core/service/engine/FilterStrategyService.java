package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.FilterStrategyPO;
import com.yiwise.core.model.bo.filterstrategy.FilterStrategyBO;
import com.yiwise.core.model.bo.filterstrategy.QueryFilterStrategyBO;
import com.yiwise.core.model.vo.filterstrategy.FilterStrategyQueryVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
*
*/
public interface FilterStrategyService extends BasicService<FilterStrategyPO> {

    void saveFilterStrategy(FilterStrategyPO filterStrategyPO, Long userId);

    int deleteFilterStrategy(Long tenantId, Long filterStrategyId, Long userId);

    int updateFilterStrategy(FilterStrategyPO filterStrategyPO, Long userId);

    PageResultObject<FilterStrategyBO> getFilterStrategyList(FilterStrategyQueryVO queryVO);

    Set<Long> selectAvailableIds(Long tenantId, Collection<Long> ids);

    PageResultObject selectFilterStrategyForOpenApi(FilterStrategyQueryVO queryVO);

    void dealFilterStrategyToRcs(Long tenantId);

}
