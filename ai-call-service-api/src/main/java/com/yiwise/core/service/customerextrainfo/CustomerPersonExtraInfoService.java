package com.yiwise.core.service.customerextrainfo;

import com.yiwise.core.dal.mongo.CustomerPersonExtraInfoHistoryPO;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2019/2/15
 **/
public interface CustomerPersonExtraInfoService {

    /**
     * 根据客户id获取自定义字段信息
     * @param customerPersonId
     * @return
     */
    Map<String, String> getExtraInfoByCustomerPersonId(Long tenantId, Long customerPersonId);

    Map<String, String> getExtraInfoByAccountVO(AccountVO accountVO);

    /**
     * 获取欧莱雅品牌
     */
    String getBrandByCustomerPersonIdForLoreal(AccountVO accountVO);

    /**
     * 获取客户自定义字段的值
     */
    String getCustomerAttrByAccount(AccountVO accountVO, String key);

    void saveCustomerPersonExtraInfoHistory(Long callRecordId, Map<Long, List<String>> extraInfo);

    CustomerPersonExtraInfoHistoryPO getCustomerPersonExtraHistoryInfoByCallRecordId(Long callRecordId);

    /**
     * 根据手机号查询客户画像信息
     *
     * @param fieldNames 过滤客户画像字段名
     * @return (phoneNumber, (fieldName, fieldValue))
     */
    Map<String, Map<String, String>> getCustomerPersonExtraInfoByPhoneNumbers(Long tenantId, List<String> phoneNumbers, Set<String> fieldNames);

    Map<Long, CustomerPersonExtraInfoHistoryPO> getCustomerPersonExtraHistoryInfoListByCallRecordIds(Collection<Long> callRecordIds);

	/**
	 * 把account的customerAttr字段转为map
	 */
	Map<String, List<String>> getCustomerAttrAsMap(AccountVO account);
}
