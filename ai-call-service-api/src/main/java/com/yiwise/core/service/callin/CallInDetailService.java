package com.yiwise.core.service.callin;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.CallInDetailPO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/1/18 10:05 AM
 **/
public interface CallInDetailService extends BasicService<CallInDetailPO> {

    /**
     * 查询呼入通话记录详情
     * @param tenantId
     * @param callInRecordId
     * @return
     */
    List<CallInDetailPO> queryByCallInRecordId(Long tenantId, Long callInRecordId);

    /**
     * 批量添加通话记录详情
     * @param list
     */
    void addCallInDetailList(List<CallInDetailPO> list);
}
