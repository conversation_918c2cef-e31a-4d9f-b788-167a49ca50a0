package com.yiwise.core.service.engine.isv;

import com.yiwise.core.model.vo.IdNameVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface YsldCallbackService {

	/**
	 * 查询指定客户在指定时间段内有外呼的任务id列表
	 */
	List<IdNameVO> getCalledRobotCallJobIds(Long tenantId, LocalDate startDate, LocalDate endDate);

	/**
	 * 发送指定客户在指定时间段内的外呼通话记录回调
	 */
	void sendCallRecordCallback(Long tenantId, LocalDate startDate, LocalDate endDate);
}
