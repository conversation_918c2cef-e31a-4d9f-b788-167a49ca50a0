package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.billing.api.bo.response.OverDraftBO;
import com.yiwise.billing.api.dto.TenantAccountDTO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.dal.mongo.tenant.TenantIdentityInfoMongoPO;
import com.yiwise.core.model.bo.sms.TenantSmsPriceBO;
import com.yiwise.core.model.dto.TenantDTO;
import com.yiwise.core.model.dto.TenantIdAndNameDTO;
import com.yiwise.core.model.dto.esign.IdentityInfoDTO;
import com.yiwise.core.model.dto.stats.TenantSimpleInfoDTO;
import com.yiwise.core.model.dto.stats.TenantStatsDTO;
import com.yiwise.core.model.dto.xiaoke.RobotAiSeatTimeToXiaokeDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.AuditStatusEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobOrderTypeEnum;
import com.yiwise.core.model.request.*;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.boss.DistributorCustomerQueryVO;
import com.yiwise.core.model.vo.financestats.FinanceLineStatsVO;
import com.yiwise.core.model.vo.financestats.FinanceTenantPayTypeQueryVO;
import com.yiwise.core.model.vo.ope.QiyuTenantAiDetailVO;
import com.yiwise.core.model.vo.ope.TenantShowAccountFareVO;
import com.yiwise.core.model.vo.openapi.TenantVO;
import com.yiwise.core.model.vo.phonenumber.PhoneNumberQueryVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberConfigVO;
import com.yiwise.core.model.vo.stats.TenantStatsQuery;
import com.yiwise.core.model.vo.tenant.*;
import com.yiwise.core.model.vo.wechat.TenantUpdateSendAddFriendRequestVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalTime;
import java.util.*;

public interface TenantService extends BasicService<TenantPO> {

    List<TenantPO> selectDiractByCsmUserId(List<Long> csmIds);

    String getTenantName(Long tenantId);

    /**
     * 扣短信费,扣短信余额
     */
    void reduceSmsCost(Long tenantId, Long cost);

    /**
     * 扣短信费,调用方决定扣哪个余额
     */
    void reduceSmsCost(Long tenantId, Long cost, CallSmsAccountEnum smsAccount);

    /**
     * 根据tenantId获取tenant
     */
    TenantPO getTenantByTenantId(Long tenantId);

    Map<Long,String> getTenantIdNameMap(List<Long> idList);

    Boolean checkCsDailyCallTime(Long tenantId);

    /**
     * 短信账户是否欠费
     */
    boolean isAccountDebt(Long tenantId, boolean calculateZero);

    /**
     * 根据distributorId获取所有的客户列表, 返回的列表可能很大, 请勿直接拼接至SQL语句中
     */
    List<TenantPO> selectByDistributorId(Long distributorId);

    /**
     * 根据根据distributorId列表获取所有的客户列表
     *
     * @param searchWords       名称关键词
     * @param distributorIdList distributorId列表
     * @param size              大小
     * @return po列表
     */
    List<TenantPO> selectByNameAndDistributorIdList(String searchWords, List<Long> distributorIdList, Integer size);

    /**
     * 根据代理商ID获取租户id列表, 返回的列表可能很大, 请勿直接拼接至SQL语句中
     */
    List<Long> getIdListByDistributorId(Long distributorId);

	/**
	 * 返回的列表可能很大, 请勿直接拼接至SQL语句中
	 */
	List<Long> getIdListByDistributorIdList(List<Long> distributorIdList);


    /**
     * 根据经销商id查询, 返回的列表可能很大, 请勿直接拼接至SQL语句中
     *
     * @param distributorIds 经销商id
     * @return list
     */
    List<TenantDTO> getAllByDistributorIds(List<Long> distributorIds);

    /**
     * 根据tenantIds 查询客户信息
     *
     * @param tenantIds 客户id
     */
    List<TenantDTO> getTenantDTOsByTenantIds(List<Long> tenantIds);

    /**
     * 根据tenantId 查询租户信息
     *
     * @param tenantId 租户id
     */
    TenantDTO getTenantDTOByTenantId(Long tenantId);

    /**
     * 获取代理商列表下审核的客户
     */
    List<TenantPO> getAuditedTenantByDistributorId(List<Long> distributorIds, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager, Long createUserId);

    /**
     * 获取该二级代理商下所有的租户
     */
    List<TenantPO> getAuditingTenantByDistributorId(Long distributorId, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager);

    /**
     * 查询该经销商下面绑定该话术集合的一个租户（用于检测是否存在）
     *
     * @param distributorId   分销商id
     * @param dialogFlowIdSet 话术id集合
     */
    TenantPO getOneBindTenantByDialogFlowIdSet(Long distributorId, Set<Long> dialogFlowIdSet);

    void deleteCacheFromRedis(Number tenantId);

    /**
     * 设置是否可以多并发
     */
    void setEnableConCurrency(Long tenantId, Boolean enableConCurrency);

    /**
     * 设置是否压缩音频
     */
    void setEnableCompressAudio(Long tenantId, Boolean enableCompressAudio);

    /**
     * 设置是否使用一知ASR
     */
    void setUseYiwiseAsr(Long tenantId, Boolean useYiwiseAsr);

    /**
     * 设置是否推送即时统计信息
     */
    void setEnableStatsMsgPush(Long tenantId, Boolean enableStatsMsgPush);

    /**
     * 设置是否可以多并发 并验证distributor权限
     */
    void setEnableConCurrency(Long distributorId, Long tenantId, Boolean enableConCurrency);

//    List<TenantPO> searchTenant(String name, String phone, String linkman);

    List<TenantPO> selectBySearchWords(String searchWords);

    List<TenantPO> selectAllTenant();

    /**
     * 获取所有直销客户id
     */
    List<TenantPO> selectDirectTenant();

    List<TenantPO> selectAllEnabled();
    /**
     * 根据tenantId 删除客户
     *
     * @param tenantId     待删除的客户id
     * @param updateUserId 操作人id
     */
    void deleteByTenantId(Long tenantId, Long updateUserId);

    /**
     * 查询代理商客户
     *
     * @param name      客户名称
     * @param onlyAgent 是否只查询代理商可以，为true只查询代理商客户，为false只查询二级代理商客户
     * @return list
     */
    List<TenantPO> getAllAgentCustomerList(String name, Boolean onlyAgent);

    void setEnableCSSeat(Long tenantId, Boolean enableCSSeat);

    void setCsSeatCount(TenantPO tenantPO);

    void setEnableAiAssistant(Long tenantId, Boolean enableAiAssistant);

    void setEnableAiEngine(Long tenantId, Boolean enableAiEngine);

    void setEnableYiwiseBot(Long tenantId, Boolean enableYiwiseBot);

    void setYiwiseBotViewAuth(Long tenantId, Boolean enableCustomerViewBot, Boolean enableOpeRoleViewBot);

    void setEnableStandardDialogFlow(Long tenantId, Boolean enableStandardDialogFlow);

    void setEnablePrivacyNumber(PrivacyNumberConfigVO config);

    void setEnableCsTransfer(Long tenantId, Boolean enableCsTransfer, Boolean enableCsTransferTel);

    /**
     * 启用呼入功能
     *
     * @param tenantId     客户id
     * @param enableStatus 启用状态 true启用，false禁用
     * @param updateUserId 操作人id
     */
    void enableCallIn(Long tenantId, Boolean enableStatus, Long updateUserId);

    /**
     * 搜索所有的客户信息，只返回前20行数据
     *
     * @param keyWords 搜索关键词
     * @param tenantPayType 类型
     * @return 结果
     */
    List<TenantIdAndNamePairVO> searchAllTenant(String keyWords, Integer count, TenantPayTypeEnum tenantPayType, Integer customerTrackType, Integer customerTrackSubType);
    List<TenantIdAndNamePairVO> searchAllTenant(String keyWords, Collection<Long> tenantIds);

	List<TenantIdAndNamePairVO> listEnabledTenants(String keyWords);

    void insertTenant(TenantPO tenantPO);
    void qiyuInsertTenant(TenantPO tenantPO);

    /**
     * 搜索客户列表
     */
    List<TenantIdAndNamePairVO> searchTenantForSelectList(CompanyEnum companyType, String searchCompanyName, String searchLinkman, String linkmanPhoneNumber, String subAccountPhoneNumber, Integer pageNum, Integer pageSize);


    /**
     * 检查tenant的机器人是否都过期到无法登陆的时间
     */
    Boolean checkAllRobotIsExpireAndForbiddenLogin(Long tenantId);

    /**
     * 设置是否开启查看未识别意向列表
     */
    void setEnabledViewIntent(Long tenantId, Boolean enabledViewIntent);

    /**
     * 开启语音质检
     */
    void setEnableVoiceQualityControl(Long tenantId, Boolean enableVoiceQualityControl, Long userId);

    /**
     * 设置语音质检费用
     */
    void setQcCostUnit(Long tenantId, Long qcCostUnit, Long qcTextCostUnit, Boolean enableQcTagThreshold, Double qcTagThreshold, Long userId);

    /**
     * 扣减质检
     */
    void reduceQcCost(Long tenantId, Long cost);

    boolean checkByDistributorIds(List<Long> distributorIdList);

    /**
     * 查询tenant 外呼等待时间
     */
    Integer queryTenantTaskWaitingTimeout(Long tenantId);

    /**
     * 获取该tenant的外呼通话超时时间
     * @param tenantId 租户id
     * @return 外呼通话超时时间,单位秒
     */
    Integer queryTenantTaskTalkingTimeout(Long tenantId);

    /**
     * 设置任务等待时间
     */
    void updateTenantTaskWaitingTimeout(Long tenantId, Integer taskWaitingTimeout);


    /**
     * 是否启用 自定义任务设置过期
     */
    void updateTenantEnableTaskWaitingTimeout(Long tenantId, Boolean status);

    /**
     * Set the time interval in which a customer can dial.
     */
    void setDialInterval(Long tenantId, LocalTime dialIntervalStart, LocalTime dialIntervalEnd);

	/**
	 * 返回的列表可能很大, 请勿直接拼接至SQL语句中
	 */
    Set<Long> selectByDistributorIds(List<Long> distributorIdList);

    /**
     * 开启ivr
     */
    void setEnableIvr(Long tenantId, Boolean enabledIvr);

    /**
     * 获取租户列表
     */
    List<TenantPO> getTenantListByTenantIds(List<Long> tenantIdList);

    Integer countDistributorTenantListResult(DistributorCustomerQueryVO queryVO);

    Integer countTenantListResult(DirectCustomerQueryVO query);

    Integer getRedialLimit(Long tenantId);

    void updateRedialLimit(Long tenantId, Integer redialLimit);

    List<TenantIdAndNamePairVO> selectAllByDistributorId(Long distributorId, String keyWords, Integer count, TenantPayTypeEnum tenantPayType, Boolean isFilter);

    void openTenantFunction(Long distributorId, Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long userId);

    TenantInfoVO getIntentInfo(Long tenantId, SystemEnum systemType);

    void setTaskWaitingTimeout(Long tenantId, Integer taskWaitingTimeout);

    AvailableServiceVO availableService(Long tenantId, Long userId);

    TenantPO selectTenantByPhoneNumber(String phoneNumber);

    TenantPO selectTenantByCustomerId(String customerId);

    Integer countPhoneLineListResult(PhoneNumberQueryVO queryVO);

    List<TenantPO> selectByTenantIds(List<Long> tenantIds);

    TenantPO selectByTenantIdDb(Long tenantId);

    List<TenantStatsDTO> getTenantOverViewDetail(TenantStatsQuery tenantStatsQuery);

    void updateUsedFreeDialogFlowCount(Long tenantId, Integer differenceValue);

    void updateUsedFreeDialogFlowSentenceCount(Long tenantId, Integer differenceValue);

    void updateAllAccount(Long tenantId, Long autoPayFare);

    void updateAllAccountAndAccount(Long tenantId, Long autoPayFare);

    void updateAllAccountAndQcAccount(Long tenantId, Long autoPayFare);
    /**
     * 增加账户余额
     */
    void increaseTenantAccount(Long tenantId, Long accountFare,Boolean needDelCash);
    //账户体系金额计算
    /**
     * 增加总账户余额
     */
    void increaseTenantAllAccount(Long tenantId, Long accountFare,Boolean needDelCash);

    /**
     * 减少总账户余额
     */
    void reduceTenantAllAccount(Long tenantId, Long accountFare,Boolean needDelCash);
    /**
     * 增加总账户余额减少质检余额
     */
    void increaseTenantAllAccountAndReduceQc(Long tenantId, Long accountFare,Boolean needDelCash);

    /**
     * 减少总账户余额增加质检余额
     */
    void reduceTenantAllAccountAndIncreaseQc(Long tenantId, Long accountFare,Boolean needDelCash);
    /**
     * 增加总账户余额减少短信余额
     */
    void increaseTenantAllAccountAndReduceAccount(Long tenantId, Long accountFare,Boolean needDelCash);

    /**
     * 减少总账户余额增加短信余额
     */
    void reduceTenantAllAccountAndIncreaseAccount(Long tenantId, Long accountFare,Boolean needDelCash);
    /**
     * 增加质检余额
     */
    void increaseTenantQc(Long tenantId, long longValue,Boolean needDelCash);

    List<Long> selectAllTenantIds(TenantPayTypeEnum tenantPayType);

    void resetUserAccountId(Long tenantId);

    void resetOrgAccountId(Long tenantId);

    void setEnableSmsPrice(TenantSmsPriceBO tenantSmsPriceBO, Long userId);

    void setHotWordCount(Long tenantId, Integer hotWordCount, Long userId);

    void setAsrSelfLearningModelCount(Long tenantId, Integer asrSelfLearningModelCount, Long userId);

    void setTenantAccountStatus(Long tenantId, TenantAccountStatusEnum tenantAccountStatus,Long userId);

    List<TenantPO> selectByPayType(TenantPayTypeEnum tenantPayType,String tenantName,Integer pagenum,Integer pageSize);

    /**
     * 该租户是否可以在所有线路余额小于等于0的情况下进行AI外呼任务
     * 异常情况返回true
     */
    boolean lineCanNotOverdue(Long tenantId);

    void updateSendAddFriendStatus(TenantUpdateSendAddFriendRequestVO request, Long userId);

    void setTenantSkipPhoneValidation(Long tenantId, Boolean skipPhoneValidation);

    List<Long> getDirtectTenantByTenantPayType(FinanceTenantPayTypeQueryVO queryVO);

    List<Long> getIdListByPayTypeAndCsmOrManage(FinanceTenantPayTypeQueryVO query);

    void payTypeTenantCostToXiaokeQuartzJob_v3();

    /**
     * 修改了ai坐席扩容将时间同步到逍客
     */
    void robotAiSeatTimeToXiaoke(RobotAiSeatTimeToXiaokeDTO robotAiSeatTimeToXiaokeDTO);

	/**
	 * 返回的列表可能很大, 请勿直接拼接至SQL语句中
	 */
	List<Long> getIdListByDistributorIdAndAccountType(Long distributorId, List<AccountTypeEnum> asList);

    List<TenantPO> getQcTenantIdList(String searchName);

    List<TenantPO> getQcDistributorTenantIdList(String searchName, Long distributorId);

    List<Long> getTenantIdByNameList(List<String> nameList);

    List<TenantPO> getQcSubDistributorTenantIdList(String searchName, Long distributorId);

    void updateCallOutBlackList(Long tenantId, Collection<Long> callOutBlackListIds);

    void setTenantSmsChannel(TenantSmsChannelVO tenantSmsChannelVO);

    Boolean getTenantSkipPhoneValidationById(Long tenantId);

    boolean checkAccountType(Long tenantId);

    PageResultObject selectUnBindingTenant(String searchName, int pageSize, int pageNum);


    /**
     * 通过代理商id分页查询tenantId
     *
     * @param pageNum 页码
     * @param pageSize 一页大小
     * @param distributorId 代理商id
     * @return 结果
     */
    PageResultObject<QiyuTenantAiDetailVO> selectDistributorTenantPageByDistributorId(Long distributorId, Integer pageNum, Integer pageSize);

    /**
     * 通过代理商id分页查询tenantId
     *
     * @param pageNum 页码
     * @param pageSize 一页大小
     * @param distributorId 代理商id
     * @return 结果
     */
    PageResultObject<FinanceLineStatsVO> selectTenantIdPageByDistributorId(Long distributorId, Integer pageNum, Integer pageSize);

    /**
     * 开通账户透支功能
     */
    OverDraftBO setOverdraft(Long tenantId, boolean enableOverdraft, Long creditLine, OverdraftDurationEnum overdraftDuration);

	/**
	 * 查询账户透支时间到期的客户, 关闭透支功能
	 */
	void updateOverdraftStatus();

    /**
     * 校验账户余额是否充足, ture:可以正常充值 false:账户余额不足
     */
    boolean checkTenantAccountFare(TenantPO tenantPO, Long autoPayFare);

    /**
     * 分页查询客户列表
     */
    PageResultObject<TenantIdAndNamePairVO> selectTenantPage(DirectCustomerQueryVO directCustomerQueryVO);

    void updateEnableLoginSecond(Long tenantId, Boolean enableLoginSecond);

    List<TenantSimpleInfoDTO> getTenantSimpleInfoList(TenantStatsQuery condition);

    boolean checkTenantIp(Long tenantId, String ip);

    void updateIpLimit(Long tenantId, Boolean enableIpLimit, Set<String> ipRange);

    void checkIpAndThrowException(Long tenantId, String ip);

    void increaseSmsCost(Long cost, Long tenantId);

    void updateEnableRechargeOnline(Long tenantId, Boolean enableRechargeOnline);

    /**
     * 根据客户赛道查询直销客户列表
     */
    List<TenantIdAndNamePairVO> selectDirectTenantByCustomerTrackType(DirectCustomerQueryVO queryVO);

    /**
     * OpenApi查询客户信息
     */
    TenantVO getTenantForOpenApi(Long tenantId);

	List<TenantPO> getDirectTenantWithCustomerTrackType();

    /**
     * 根据customerId查询tenantId
     */
    Map<String, Long> selectTenantByCustomerIds(List<String> customerIds);

	/**
	 * 记录修改了特定字段的客户
	 */
	void tenantUpdateFields(Collection<Long> tenantId);

	/**
	 * 按指定条件筛选tenantId
	 */
	Set<Long> filterTenantIdByConditions(Collection<Long> tenantIds, Collection<TenantPayTypeEnum> tenantPayTypes, Collection<AccountTypeEnum> accountTypes, Long distributorId);

    void setOrderType(Long tenantId, CallJobOrderTypeEnum orderType, Long userId);

    void setEnableDragOrder(Long tenantId, Boolean enableDragOrder, Long userId);

    void setDailySummer(TenantSetDailySummerVO dailySummerVO);

    /**
     * 自动化营销/会员中心入口控制
     * @param controlDataEntryRequest 请求参数
     */
    Boolean controlDataEntry(ControlDataEntryRequest controlDataEntryRequest);

    /**
     * 设置客户显示短信单价
     */
    void setTenantSmsShowPrice(TenantSmsShowPriceVO tenantSmsShowPriceVO);

    /**
     * 获取客户短信显示单价 单位（厘）
     */
    Long getTenantSmsShowPrice(Long tenantId);

    /**
     * 获取短信任务可发送短信的数量
     */
    Long getSmsJobSendLimit(Long tenantId);

    /**
     * 查询主品牌下的客户下拉列表
     */
    List<TenantSimpleInfoDTO> selectByMainBrandIdLimit(Long tenantId, String companyName);


    /**
     * 搜素客户
     */
    PageResultObject<TenantSimpleInfoVO> getTenantSimpleListByName(String searchName, Integer pageNum, Integer pageSize);

    void controlOrderMaRequest(ControlDataEntryRequest controlOrderMaRequest);

    Map<Long, ControlOrderMaRequest> getOrderMaFunctionMap(List<Long> tenantIdList);

    void controlCrowdConfig(ControlCrowdConfigRequest controlCrowdConfigRequest);

    void batchUpdate(List<TenantPO> updateList);

    List<TenantIdAndNameDTO> getGroupTenantIds(Long tenantId);

    List<TenantPO> getMkyAccountByCondition(Long customerId, String name);

	/**
	 * 租户改为使用新版计费服务
	 */
	void usingNewBillingService(Long tenantId);

	/**
	 * 查询使用新版计费系统的客户的账户余额
	 */
	TenantAccountDTO selectTenantAccount(Long tenantId);

    TenantPO selectCacheByKey(Long key);

    /**
	 * 向计费服务同步单价
	 */
	void updateProductId(TenantPO tenant);

	/**
	 * 更新客户AICC展示账户余额的选项
	 */
	void updateShowAccountFare(TenantShowAccountFareVO tenantShowAccountFare);

	/**
	 * 根据是否有使用新版计费的客户过滤代理商id集合
	 * @param distributorIds 待过滤的代理商id
	 * @return 输入的代理商里, 含使用新版计费的客户的代理商的id
	 */
	Set<Long> filterDistributorIdsByTenantUsingNewBillingService(Collection<Long> distributorIds);

    /**
     * 获取开通隐私号tenant
     */
    List<Long> getEnablePrivacyNumberIds();

    /**
     * 短信模板免审核
     * @param tenantSkipSmsTemplateReviewVO
     */
    void setTenantSkipSmsTemplateReview(TenantSkipSmsTemplateReviewVO tenantSkipSmsTemplateReviewVO);

    /**
     * 更新性别识别开关
     * @param tenantId
     * @param enableGenderRecognition
     */
    void updateEnableGenderRecognition(Long tenantId, Boolean enableGenderRecognition);

    String updateSmsConfig(TenantSmsSendRequest request);

    /**
     * 更新新旧版本外呼
     * @param tenantId
     * @param newVersion
     * @param enable
     */
    void updateEnableTenantCallOutVersion(Long tenantId, Boolean newVersion, Boolean enable);

    /**
     * 开启视频外呼
     * @param tenantId
     * @param enable
     */
    void updateEnableVideoCallOut(Long tenantId, Boolean enable);

    void updateVideoConfig(VideoConfigVO videoConfigVO);

    VideoConfigVO getVideoConfig(Long tenantId);


    /**
     * 是否开启了一知人工坐席
     * @return
     */
    boolean enableYiwseAgent(Long tenantId);

    /**
     * 根据绑定的短信通道查询出直销客户名
     * @param channelId 短信通道id
     * @return 直销客户名
     */
    List<String> selectDirectTenantNameByChannelId(String channelId);

    /**
     * 根据绑定的短信通道查询出代理商客户名
     * @param channelId 短信通道id
     * @param onlyAgentOne true:查询一级代理商 false:查询二级代理商
     * @return
     */
    List<String> selectAgentTenantNameByChannelId(String channelId, boolean onlyAgentOne);

    void updateModelIdentify(IdentifyConfigVO identifyConfigVO);

    IdentifyConfigVO getModelIdentify(Long tenantId);

    List<Long> getIdListUseMd5(String url);

    List<TenantPO> getMd5AndNoDecryptTenant(Set<Long> tenantIds,Set<Long> binJianIds);

    /**
     * 更新客户自定义黑名单配置
     * @param configVO
     * @return
     */
    int updateCustomBlackConfig(CustomBlackConfigVO configVO);

    /**
     * 更新客户外呼超时限制
     */
    void updateTaskTalkingTimeout(Long tenantId, Integer taskTalkingTimeoutMinutes);

    void updateNewMainBrandIdAndMainBrandBlacklist(List<Long> tenant, Long newMainBrandId, Boolean mainBrandBlacklist);

    void saveTenantIdentityInfo(Long tenantId, IdentityInfoDTO identityInfo);

    TenantIdentityInfoMongoPO getTenantIdentityInfo(Long tenantId);

    TenantPO selectByCompanyName(String companyName);
}
