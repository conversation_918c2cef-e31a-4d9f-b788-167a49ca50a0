package com.yiwise.core.service.ope.platform;

import com.yiwise.core.dal.entity.DeploymentUserPO;
import com.yiwise.core.model.vo.ope.DeploymentUserVO;
import com.yiwise.base.service.BasicService;

import java.util.List;
import java.util.Map;

public interface DeploymentUserService extends BasicService<DeploymentUserPO> {
    void batchTenantInsert(Long tenantId, List<Long> deploymentUserIdList);

    void batchDistributorInsert(Long distributorId, List<Long> deploymentUserIdList);

    void deleteByTenantId(Long tenantId);

    void deleteByDistributorId(Long distributorId);

    Map<Long, List<DeploymentUserVO>> selectMapByTenantIdList(List<Long> tenantIdList);

    Map<Long, List<DeploymentUserVO>> selectMapByDistributorIdList(List<Long> distributorIdList);

    List<DeploymentUserPO> selectByDistributorId(Long distributorId);
}
