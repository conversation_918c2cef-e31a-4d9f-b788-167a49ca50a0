package com.yiwise.core.service.callin;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.CallInReceptionPO;
import com.yiwise.core.dal.entity.PhoneNumberPO;
import com.yiwise.core.dal.mongo.CallInAiStaffConfigPO;
import com.yiwise.core.model.bo.callin.SeatBO;
import com.yiwise.core.model.enums.ReceptionTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callin.CallInReceptionStatusEnum;
import com.yiwise.core.model.vo.callin.CallInReceptionSummaryVO;
import com.yiwise.core.model.vo.callin.CallInReceptionVO;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2019/1/11 2:32 PM
 **/
public interface CallInReceptionService extends BasicService<CallInReceptionPO> {

    PageResultObject<CallInReceptionVO> queryList(Long tenantId, Integer pageNumber, Integer pageSize, ReceptionTypeEnum receptionType, SystemEnum systemType);

    List<CallInReceptionPO> queryAllByTenantId(Long tenantId);

    void create(CallInReceptionPO createInfo, Long updateUserId);

    void update(CallInReceptionPO updateInfo, Long updateUserId);

    void updateStatus(Long callInReceptionId, CallInReceptionStatusEnum status, Long updateUserId, Long tenantId);

    void deleteById(Long callInReceptionId, Long updateUserId, Long tenantId);

    /**
     * 获取坐席组信息
     * @param sipAccount
     * @param customerPhone
     * @return
     */
    SeatBO querySeatInfoBySipAccount(String sipAccount, String customerPhone);

    /**
     * 校验线路是否已经被使用
     * @param phoneNumberId
     * @param tenantId
     */
    Boolean checkPhoneNumberAlreadyUse(Long tenantId, Long phoneNumberId);

    CallInReceptionSummaryVO countSumStatus(Long tenantId);

    /**
     * 根据话术id列表更新接待场景到禁用状态
     */
    void updateDisableStatusByDialogFlowIdList(Long tenantId, List<Long> dialogFlowIdList, String message);

    void disableByTenantId(Long tenantId, String message);

    /**
     * 禁用线路不支持呼入的接待场景
     *
     * @return 禁用的接待场景id
     */
    List<Long> banUnsupportedCallIn();

    void updatePhoneNumber(PhoneNumberPO before, PhoneNumberPO after);

    /**
     * 根据接待场景查询坐席id
     */
    List<Long> getCsStaffIdListByReceptionIds(List<Long> receptionIds);

    CallInAiStaffConfigPO updateCallInConfig(Long tenantId, CallInAiStaffConfigPO customerServiceStaffConfigPO);

    CallInAiStaffConfigPO getCallInConfig(Long tenantId);
}
