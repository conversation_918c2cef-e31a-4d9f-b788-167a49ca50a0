package com.yiwise.core.service.engine.csseat;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.CallInRecordPO;
import com.yiwise.aicc.callin.api.model.dto.CallInRecordDTO;
import com.yiwise.core.dal.entity.CsRecordPO;
import com.yiwise.core.dal.entity.CsStaffInfoPO;
import com.yiwise.core.dal.mongo.CustomerServiceStaffConfigPO;
import com.yiwise.core.dal.mongo.IntelligentFieldControlConfigPO;
import com.yiwise.core.dal.mongo.StatisticsRuleConfigPO;
import com.yiwise.core.model.bo.cs.CallInStatusBO;
import com.yiwise.core.model.bo.user.CsPhoneNumberBO;
import com.yiwise.core.model.bo.user.DialInfoBO;
import com.yiwise.core.model.bo.user.SipAccountBO;
import com.yiwise.core.model.dto.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.vo.cs.CsStatusVO;
import com.yiwise.core.model.vo.csseat.CallJobCsStaffTransferMsgVO;
import com.yiwise.core.model.vo.csseat.CsStaffInfoVO;
import com.yiwise.core.model.vo.csseat.StaffGroupQueryVO;

import java.util.List;
import java.util.Map;

/**
 * Created by 昌夜 on 18/12/27.
 */
public interface CsStaffUserService extends BasicService<CsStaffInfoPO> {
    SipAccountBO getCsStaffByUserId(Long userId);

    SipAccountBO getCsStaffSipAccount(Long csStaffId);

    void updateSipAccountStatus(Long phoneNumberId, OnlineStatusEnum onlineStatusEnum);

    List<CsPhoneNumberBO> getCsCallLineList(Long userId);

    DialInfoBO getCsDialInfo(Long tenantId, Long userId, Long phoneNumberId, String customerPhone);

    /**
     * 获取人工坐席在线信息
     */
    CsSeatDTO getStaffOnline(Long csStaffId, Long tenantId);

    void setCsStaffAutoAnswer(Long userId, Boolean answer);

    String getCsStaffAutoAnswer(Long userId);

    CsSeatDTO getOnlineSeatByGroupIdV2(Long tenantId, Long csSeatGroupId);

    /**
     * 获取租户下所有坐席在线情况
     */
    Map<Long, CsSeatDTO> getStaffStatusMap(Long tenantId);

    /**
     * 根据接待场景获取租户下所有坐席在线情况
     */
    Map<Long, CsSeatDTO> getStaffStatusMapForReception(Long tenantId, List<Long> csStaffGroupIdList);

    /**
     * 获取人工坐席在线信息
     */
    CsSeatDTO getStaffStatus(Long csStaffId, Long tenantId);

    /**
     * 获取坐席组成员在线状态
     */
    Map<Long, CsSeatDTO> getGroupStaffStatus(Long csStaffGroupId, Long tenantId);

    List<CsSeatDTO> getGroupStaffOnlineList(Long csStaffGroupId, Long tenantId);

    CsSeatQueueDTO getOnlineSeatByGroupIdWithUserIdV2(Long tenantId, Long csStaffGroupId);

    CsStaffOnlineStatusEnum getUserStaffStatus(Long tenantId, Long userId);

    void setStaffOperationStatus(Long tenantId, Long userId, CsStaffOperationStatusEnum status, boolean remove);

    void setStaffOperationStatusByStaff(Long tenantId, Long csStaffId, CsStaffOperationStatusEnum status, boolean remove, Integer seconds);

    CsSeatQueueDTO getCsByGroupId(Long csStaffGroupId, Boolean queue, Integer count);

    CsSeatQueueDTO getCsByGroupId(Long csStaffGroupId, Boolean queue, Integer count,Boolean filterFlag);

    CustomerServiceStaffConfigPO addCsConfig(Long tenantId);

    CustomerServiceStaffConfigPO getCsConfig(Long tenantId);

    CustomerServiceStaffConfigPO updateCsConfig(Long tenantId, CustomerServiceStaffConfigPO customerServiceStaffConfigPO);

    IntelligentFieldControlConfigPO getIntelligentFieldControlConfig(Long tenantId);

    void updateIntelligentFieldControlConfig(Long tenantId, IntelligentFieldControlConfigDTO intelligentFieldControlConfigDTO);

    StatisticsRuleConfigPO getStatisticsRuleConfig(Long tenantId);

    void updateStatisticsRuleConfig(Long tenantId, StatisticsRuleConfigDTO statisticsRuleConfigDTO);

    CustomerServiceStaffConfigPO resetBeepUrl(Long tenantId, CustomerServiceBeepEnum beepType);

    CallJobCsStaffTransferMsgVO getHumanToCsCallInfo(String identifyId);

    List<CsStatusVO> getCsStatusVOByIdList(List<Long> idList,Long tenantId);

    List<CsStatusVO> getCsStatusVOByIdList(List<Long> idList,boolean filterFlag);

    List<CsStaffOperationDTO> getFreeStaff(List<Long> onlineIdList);

    List<CsStaffOperationDTO> getFreeStaffWithDealTime(List<Long> onlineIdList);

    List<CsStaffInfoPO> getCsOrTextGroupStaffList(Long csStaffGroupId);

    List<CsStaffInfoVO> getCsOrTextGroupListStaffList(StaffGroupQueryVO queryVO);

    CallInStatusBO getCallInStatus(CallInRecordPO callInRecordPO, CsSeatQueueDTO seatQueueDTO, String identifyId);

    DialStatusEnum getCallOutStatus(CsRecordPO csRecordPO, CsSeatQueueDTO seatQueueDTO, String identifyId);

    void setCsHangUp(Long tenantId, Long userId, CallJobHangupEnum hangup, String identifyId);

    void setCsAnswer(Long tenantId, Long userId, String identifyId);

    CsSeatQueueDTO getLastCallStaff(Long tenantId, Long csStaffGroupId, Long customerPersonId);

    List<CsPhoneNumberBO> getStaffPhoneNumberList(Long tenantId, String csMobile);

    int getCsOnlineCount(Map<Long, CsSeatDTO> map);

    int getCsOnlineCountExceptDealCs(Map<Long, CsSeatDTO> map);

    int computeConcurrencyQuota(int onlineCount, int max);

    int computeConcurrentMultiple(int onlineCount, double concurrentMultiple);
}
