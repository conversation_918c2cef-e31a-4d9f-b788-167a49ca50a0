package com.yiwise.core.service.callin;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.CallInRecordPO;
import com.yiwise.core.dal.entity.CallRecordPO;
import com.yiwise.core.dal.entity.CsRecordPO;
import com.yiwise.core.dal.entity.CustomerPersonPO;
import com.yiwise.core.model.bo.callcost.CallCostDetailBO;
import com.yiwise.core.model.bo.callin.SeatBO;
import com.yiwise.core.model.bo.robotcalljob.TaskCallResultBO;
import com.yiwise.core.model.dto.CsSeatQueueDTO;
import com.yiwise.core.model.enums.CsCallCategoryEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.callin.CallInTypeEnum;
import com.yiwise.core.model.enums.callin.CallRecordTransferTypeEnum;
import com.yiwise.core.model.enums.callin.StaffGroupTypeEnum;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.callin.CallInRecordDetailVO;
import com.yiwise.core.model.vo.callin.CallInRecordListVO;
import com.yiwise.core.model.vo.callin.CallInRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.CallRecordInfoVO;
import com.yiwise.core.model.vo.callrecord.CsStaffCallRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.TransferCallVO;
import com.yiwise.core.model.vo.csseat.CsStaffCallRecordVO;
import com.yiwise.core.model.vo.csseat.XSecondMonitorVO;

import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2019/1/14 2:23 PM
 **/
public interface CallInRecordService extends BasicService<CallInRecordPO> {

    PageResultObject<CallInRecordListVO> queryByCondition(Long tenantId,Long userId, CallInRecordQueryVO condition);

    PageResultObject<CallInRecordListVO> callInRecordHistory(Long tenantId,Long userId, CallInRecordQueryVO condition);

    void packageCallInRecordInfo(List<? extends CallInRecordListVO> resultList, Long tenantId);

    /**
     * 根据接待类型查询呼入记录
     * @param callCostQuery
     * @param seatType
     * @return
     */
    PageResultObject<CallCostDetailBO> queryBySeatType(CallCostQueryVO callCostQuery, StaffGroupTypeEnum seatType, CallInTypeEnum callInTypeEnum, Integer pageNum, Integer pageSize);

    /**
     * 设置为已读状态
     * @param tenantId
     * @param callInRecordId
     */
    void updateReadStatus(Long tenantId, Long callInRecordId, SystemEnum system);

    /**
     * 插入通话记录
     * @param seatBO
     * @param callerNumber
     * @param doCallInStats 是否执行数据统计
     */
    Long insertCallInRecord(SeatBO seatBO, String callerNumber, boolean doCallInStats);

    // todo
    Long insertReceptionCallInRecord(SeatBO seatBO, String callerNumber,
                                     Long callRecordId, CsCallCategoryEnum csCallCategory, Long csStaffId,
                                     Long fromCallInRecordId, Long fromFirstCallInRecordId, CallRecordPO callRecordInfo,
                                     CsSeatQueueDTO seatQueueDTO, String identifyId, Boolean isMonitor);

    void addTransferToCsRecordListFromAiCallOut(Long callInRecordId, String callerNumber,
                                                Long callRecordId, CsCallCategoryEnum csCallCategory, CallRecordPO callRecordInfo, List<TransferCallVO> list, CsSeatQueueDTO seatQueueDTO, String identifyId);
    /**
     * 修改通话记录
     * @param seatBO
     * @param callerNumber
     */
    void updateCallInRecord(SeatBO seatBO, String callerNumber, String recordPath, Long chatDuration, Boolean transfer, List<TransferCallVO> transferToCsList);

    List<XSecondMonitorVO>  getCountForXSecond(Long tenantId, List<Long> csStaffIdList, Integer second);
    /**
     * 查询通话记录详情
     * @param tenantId
     * @param callInRecordId
     * @return
     */
    CallInRecordDetailVO findByCallInRecordId(Long tenantId, Long callInRecordId);

    /**
     * ai呼入成功加入的通话记录
     * @param callInRecordPO
     * @param taskCallResultInfo
     */
    void insertCallInRecordByAiSuccess(CallInRecordPO callInRecordPO, TaskCallResultBO taskCallResultInfo);


    /**
     * 保存呼入记录 并更新客户信息
     */
    void saveCallInRecordWithCustomerInfo(CallInRecordPO callInRecordPO);

    /**
     * 根据customerId查询联系历史
     * @param tenantId 租户id
     * @param customerPersonId 客户id
     * @return
     */
    List<CallRecordInfoVO> queryRecordInfoListByCustomerPersonId(Long tenantId, Long customerPersonId);

    /**
     * 更新呼入联系历史为已接听状态
     * @param tenantId
     * @param callInRecordId
     */
    void updateResultStatus2Answered(Long tenantId, Long callInRecordId, Integer hangupBy);

    void updateCallRecordIntentLevel(Long tenantId, Long callRecordId, Integer intentLevel,Long intentLevelTagId);

    /**
     * 分页查询tenant下面所有呼入接待数据
     * @param tenantId
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResultObject<CallInRecordPO> queryAllByTenantId(Long tenantId, Integer pageNum, Integer pageSize);

    PageResultObject<CallInRecordListVO> queryOpeByCondition(CallInRecordQueryVO condition);

    void updateCreateUserId(Long tenantId, Long userId, Collection<Long> customerPersonIds);

    PageResultObject<CsStaffCallRecordVO> callInHistory(CsStaffCallRecordQueryVO csStaffCallRecordQueryVO);

    Query getCallInReceptionStatsQuery(CallInRecordPO callInRecordPO);

    Update getCallInReceptionUpdate(CallInRecordPO callInRecordPO);

    void addTransferToCsRecordListFromAiCallIn(Long callInRecordId, String customerPhoneNumber, CsCallCategoryEnum call, CallInRecordPO callInRecordPO, List<TransferCallVO> transferToCsList);

    void addTransferToCsRecordListFromCsCallOut(Long csRecord, String customerPhoneNumber, CsCallCategoryEnum call, CsRecordPO recordPO, List<TransferCallVO> transferToCsList);

    List<CallInRecordPO> selectByCustomerPersonId(Long tenantId, Long customerPersonId);

    void packageCallInRecord(List<? extends CallInRecordListVO> resultList);

    int countByTransferType(Long tenantId,CallRecordTransferTypeEnum transferType, LocalDate starDate, LocalDate endDate);

    /**
     * 旧数据初始化distributorId字段
     */
    void initCallInRecordDistributorId();
}
