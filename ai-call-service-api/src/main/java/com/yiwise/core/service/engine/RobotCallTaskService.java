package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.model.bo.robotcalltask.*;
import com.yiwise.core.model.dto.CallRecordExportDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callrecord.InterceptStatusEnum;
import com.yiwise.core.model.enums.robotcalltask.RobotCallTaskMapperEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.callrecord.CallRecordQueryVO;
import com.yiwise.core.model.vo.robotcalltask.*;
import com.yiwise.customer.data.platform.rpc.api.service.enums.CreateSourceTypeEnum;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@Validated
@TargetDataSource(value = DataSourceEnum.TASK_POLARDB)
public interface RobotCallTaskService extends BasicService<RobotCallTaskPO> {

    /**
     * 获取任务中还没有开始的子任务列表
     *
     * @param callJobId 任务id
     * @param size      获取list的大小
     * @return 还没有开始的子任务列表
     */
    List<RunTimeRobotCallTaskBO> getRunnableRobotCallTaskList(Long callJobId, Long subCallJobId, int size, boolean skipOrderBy);

    /**
     * 统计任务总可运行的子任务的总数
     * @param callJobId 任务id
     * @return 可运行子任务的总数
     */
    Integer countRunnableRobotCallTaskList(Long callJobId);

    LocalDateTime getRobotCallTaskRedialMinTime(Long callJobId);

    LocalDateTime getRobotCallTaskRedialMinTime(RobotCallJobPO robotCallJobPO);

    /**
     * 更新callTask
     *
     * @param robotTaskCallInfo 待更新对象
     */
    void updateRobotCallTaskById(RobotCallTaskPO robotTaskCallInfo);

    int updateRobotCallTaskNewStatusById(Long tenantId, Long robotCallJobId, Long robotCallTaskId, RobotCallTaskStatusEnum status, RobotCallTaskStatusEnum oldStatus);

    int updateRobotCallTaskNewStatusWithTimeById(Long tenantId, Long robotCallJobId, Long robotCallTaskId, RobotCallTaskStatusEnum status, RobotCallTaskStatusEnum oldStatus, LocalDateTime updateTime);

    /**
     * 统计当前job下，子任务是否完成
     *
     * @param callJobId 任务id
     */
    boolean isAllJobTaskRunFinished(Long callJobId);

    /**
     * 该 job 是否需要等待重拨
     *
     * @param callJobId 任务id
     * @return true都在等
     */
    boolean allLeftTasksWaiting4Redial(Long callJobId);

    LocalDateTime getMinNextCalledTimeByJobId(Long jobId);

    /**
     * 统计正在运行中的子任务
     *
     * @param robotCallJobId 任务id
     * @return 正在运行中的子任务总量
     */
    int countRunningTask(Long robotCallJobId);

    /**
     * 检查超过time分钟没有更新的task为未开始状态
     *
     * @param time 超时时间
     */
    void checkAndUpdateTimeoutCallTaskToNotStart(RobotCallTaskStatusEnum status, int time);

    /**
     * 获取待呼客户名单
     *
     */
    PageResultObject<ToBeCalledTaskVO> getToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery);

    /**
     * 包装客户相关信息
     */
    void resolveCustomerPersonInfo(List<? extends ToBeCalledTaskVO> toBeCalledTaskList);

    /**
     * 删除callTask
     *
     * @param robotTaskId 待删除对象
     * @param tenantId    待删除对象的租户
     * @throws com.yiwise.base.model.exception.ComException 如果
     *                                                     1. 需要删除的task不属于当前租户
     *                                                     2. 不存在robotTaskId的呼叫任务
     *                                                     3. task所属的callJob状态不是USER_PAUSE
     */
     void deleteRobotCallTaskById(Long robotTaskId, Long tenantId, Long userId, boolean useOpenApi);

    /**
     * 导入客户到任务
     *
     * @param intentLevelTagId
     * @param planDuplicateCustomers
     * @param distinctInPlan         本次导入请求是否要求在外呼计划内去重, 如果该任务不属于外呼计划或不关心是否属于外呼计划传null.
     */
    void addCustomerPersonsToRobotCallJob(List<CustomerPersonInfoForRobotCallJobBO> customerPersonInfoList,
                                          RobotCallJobPO robotCallJob,
                                          Long currentTenantId, Long currentUserId,
                                          Long intentLevelTagId, List<CustomerPersonInfoForRobotCallJobBO> duplicateCustomers,
                                          List<CustomerPersonInfoForRobotCallJobBO> planDuplicateCustomers, boolean callRecordDup,
                                          Boolean distinctInPlan);

    /**
     * 添加单个客户到任务 不验证客户信息 更新自定义属性
     */
    void addCustomerPersonToRobotCallJob(CustomerPersonInfoForRobotCallJobBO customerPersonInfo,
                                         Long robotCallJobId,
                                         Long currentTenantId, Long currentUserId,
                                         boolean callRecordDup, String addWechatAccountId, Long addWechatOrgId, Long addWechatScrmUserId);

    Integer addRobotCallTask(RobotCallTaskPO robotCallTaskPO, boolean callRecordDup);

    /**
     * 再次添加到拨打任务
     * @param currentUserId 当前用户
     * @param request 查询条件
     */
    ReAddResultBO reAddCustomerPersonsToRobotCallJob(Long currentUserId,
                                                     ReAddCustomerPersonToRobotCallJobRequestVO request);

    /**
     * 单独抽离此方法  将任务设置为重新拨打状态 并进行必要的校验和更改状态及统计信息
     *
     * @param customerPersonIdList 子任务客户id列表
     * @param robotCallJobId 任务id
     * @param tenantId 租户id
     * @param userId 用户id
     */
    void resetTaskStatusReTry(List<Long> customerPersonIdList, Long robotCallJobId, Long tenantId, Long userId,SystemEnum systemType);

    /**
     * 在有Task导入Job之后，根据Job当前状态重置job状态
     */
    void resetRobotCallJobStatusWhenCallTaskAddToCallJob(Integer addCount, RobotCallJobPO robotCallJobInfo);

    /**
     * 批量删除已呼客户列表
     */
    int deleteRobotCallTaskList(ToBeCalledListQueryVO toBeCalledListQuery);
	/**
	 * 外呼计划批量删除未呼客户
	 */
	int deleteRobotCallTaskList(PlanDeleteTaskQueryVO request);

    /**
     * 导出列表数量
     */
    Integer getCountToBeCalledRobotTaskList(ToBeCalledListQueryVO request);

    /**
     * 通过task查询最后一条通话记录
     */
    List<CallRecordExportDTO> getLastCallRecordByTask(CallRecordQueryVO request);

    Long getLastCallRecordByTaskCount(CallRecordQueryVO condition);

    /**
     * 批量处理的条数
     */
    Integer getCountLastCallRecordReAddByTask(CallRecordQueryVO request);

    /**
     * 删除没有通话记录的子任务
     */
    Integer deleteNotUsedToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery);

    /**
     * 更新有通话记录的子任务未已完成
     */
    Integer updateUsedToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery);

    /**
     * 人工修改意向等级时修改最后一次通话记录的意向等级
     */
    void updateIntentLevel(Long tenantId, Long robotCallTaskId, Long callRecordId, Integer intentLevel);

    /**
     * 更新子任务自定义变量
     */
    void updateProperties(Long tenantId, Long userId, Long robotCallTaskId, Map<String, String> properties, String name, GenderEnum gender, List<CreateSourceTypeEnum> createSources, LocalDate birthday);

    RobotCallTaskStatusEnum selectRobotTaskStatus(Long taskId);

    boolean haveTaskInRunningOrCache(Long callJobId);

    Set<String> getUntriedPhoneNumber(AccountVO accountVO, Long tenantId, Long robotCallTaskId, Long lastTenantPhoneNumberId);

	/**
	 * 更新导入客户数的统计值, 被调用方根据RDS中的duplicated字段判断每个客户是否成功导入并计算准确的导入数量, 用于batch场景下调用方无法获取真实的变更数量
	 *
	 * @param mapper 查询robot_call_task表使用的mapper, 由于数据库读写分离后只保证会话一致性,
	 *                  需要保证上下文对robot_call_task表的操作使用同一个session即同一个mapper
	 * @param customerPersonIds 导入的客户, 含insert失败后更新duplicated值的客户
	 */
	void updateImportedCustomer(RobotCallTaskMapperEnum mapper, Long robotCallJobId, Long dialogFlowId, List<Long> customerPersonIds);

    /**
     * 更新task的callRecordId
     */
    void updateCallRecordId(Long taskId, Long callRecordId);

	/**
	 * 更新加微信息
	 */
	void updateAddWechatFriend(Long robotCallTaskId, WechatCpAddFriendPO wechatCpAddFriendPO);

    void getInfo(List<? extends ToBeCalledTaskVO> toBeCalledTaskList);

    JobStartResultVO deleteRobotTaskList(ToBeCalledListQueryVO toBeCalledListQueryVO);

	/**
	 * 将robot_call_task的last_import_time批量更新为create_time
	 */
	void refreshLastImportTime(Long robotCallTaskIdStart, Long robotCallTaskIdEnd, Integer batchSize);

    List<RobotCallTaskPO> getByIds(List<Long> taskIdList);

    /**
     * 更新人工介入状态
     * @param taskId
     * @param interceptStatusEnum
     */
    void updateInterceptStatus(Long taskId, InterceptStatusEnum interceptStatusEnum);

}
