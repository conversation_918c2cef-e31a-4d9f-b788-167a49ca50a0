package com.yiwise.core.service.ope.platform;

import com.yiwise.core.model.dto.ToyotaISVCallBackReqDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date
 */
public interface IsvOpeService {

    /**
     * @param robotCallJobId
     */
    void doIsvCallBackForJob(Long robotCallJobId);


    void doIsvCallBackForTime(Long tenantId, Long userId, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime callbackTime, LocalDateTime callbackEndTime);

    /**
     * @param tenantId
     * @param robotCallJobIds
     */
    void doIsvCallBackForJobList(Long tenantId, List<Long> robotCallJobIds);


    void doIsvCallBackForBingJianTestList(Long tenantId, Long userId, List<Long> list);

    void doToyotaIsvCallBack(List<ToyotaISVCallBackReqDTO> toyotaISVCallBackReqDTOList, Long tenantId);

    /**
     * 短信接收状态回调重推 -- 冰鉴
     */
    void doIsvSmsReceptResultCallback(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);
}
