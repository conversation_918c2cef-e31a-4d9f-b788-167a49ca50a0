package com.yiwise.core.service.engine;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.SmsJobMessagePO;
import com.yiwise.core.model.vo.sms.SmsMessageDeleteVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SmsJobMessageService extends BasicService<SmsJobMessagePO> {

    /**
     * 批量插入
     */
    int batchInsert(List<SmsJobMessagePO> list);

    List<SmsJobMessagePO> selectAvailableListBySmsJobIdLimit(Long smsJobId);

    Long countAvailableListBySmsJobId(Long smsJobId);

    void deleteSmsJobMessage(SmsMessageDeleteVO smsMessageDeleteVO);

    Long selectIdBySid(String sid);

	/**
	 * 遍历开始使用
	 */
	Long selectSmsJobMessageIdAfterLocalDateTime(LocalDateTime localDateTime);
	Long selectSmsJobMessageIdBeforeLocalDateTime(LocalDateTime localDateTime);

	/**
	 * 遍历使用
	 */
	List<SmsJobMessagePO> selectOrderBySmsJobMessageId(Long smsJobMessageId, Integer batchSize, Long lastSmsJobMessageId);
}
