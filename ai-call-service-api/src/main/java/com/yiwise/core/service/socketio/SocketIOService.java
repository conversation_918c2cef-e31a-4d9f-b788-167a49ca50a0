package com.yiwise.core.service.socketio;

import com.yiwise.core.dal.entity.SpringBatchJobLogPO;
import com.yiwise.core.model.alimessagequeue.websocket.ImportOutputProgressMsg;
import com.yiwise.core.model.bo.websocket.BasicMsg;
import com.yiwise.core.model.vo.customer.ImportOutputProgressVO;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022年11月29日 14:31:00
 */
public interface SocketIOService {

    /**
     * 推送导入导出事件到MQ
     */
    @Deprecated
    void sendSocketIOMsg(Long userId, String event, BasicMsg<ImportOutputProgressVO> message);

    void sendSocketIOMsg(Long userId, String event, BasicMsg<ImportOutputProgressVO> message, Long tenantId);

    void sendSocketIOMsgByNewCallOut(Long userId, String event, BasicMsg message, Long tenantId);
}
