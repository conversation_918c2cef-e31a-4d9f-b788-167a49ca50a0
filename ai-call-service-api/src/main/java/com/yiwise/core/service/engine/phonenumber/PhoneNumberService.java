package com.yiwise.core.service.engine.phonenumber;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.PhoneNumberPO;
import com.yiwise.core.model.bo.boss.PhoneNumberBindStatsQueryBO;
import com.yiwise.core.model.bo.phonenumber.*;
import com.yiwise.core.model.dto.*;
import com.yiwise.core.model.enums.CompanyEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.opensips.OpensipsCarrierQueryVO;
import com.yiwise.core.model.vo.phonenumber.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2018/7/25
 **/
public interface PhoneNumberService extends BasicService<PhoneNumberPO> {
    /**
     * 查询某任务下的线路id列表
     *
     * @param robotCallJobId 任务id
     */
    List<TenantPhoneNumberBO> getJobPhoneNumberListByRobotCallJobId(Long tenantId, Long robotCallJobId);

    List<TenantPhoneNumberBO> getJobPhoneNumberListByPhoneNumberIds(Long tenantId, List<Long> phoneNumberIds);

    Map<Long, List<TenantPhoneNumberBO>> getJobPhoneNumberMapByRobotCallJobIdList (List<Long> calljobIdList);

    List<List<TenantPhoneNumberBO>> getJobPhoneNumberListByRobotCallJobIdList(List<Long> calljobIdList);
    /**
     * 获取添加了呼叫前缀的被叫号码
     *
     * @param sipAccountInfo    主叫号码信息
     * @param calledPhoneNumber 被叫客户号码
     * @return 添加了呼叫前缀的被叫号码
     */
    CalledPhoneNumberBO getPrefixedCalledPhoneNumberInfo(SipAccountInfoBO sipAccountInfo, String calledPhoneNumber, boolean useDynamicDialplan);

    /**
     * 获取添加了呼叫前缀的被叫号码
     *
     * @param sipAccountInfo    主叫号码信息
     * @param calledPhoneNumber 被叫客户号码
     * @return 添加了呼叫前缀的被叫号码
     */
    CalledPhoneNumberBO getPhoneNumberWithPrefixInfo(SipAccountInfoBO sipAccountInfo, String calledPhoneNumber);

    /**
     * 通过sip账号获取线路信息
     */
    PhoneNumberSipAccountVO getPhoneNumberBySipAccount(String sipAccount);

    /**
     * 查询所有电话卡信息
     *
     * @return
     */
    PageResultObject<SimplePhoneNumberVO> selectAllSimplePhoneNumberList();

    /**
     * BOSS
     * 获取用于训练的SIP账号
     */
    PhoneNumberSipAccountVO getVerbalTrickTrainingPhoneNumberBOSS(Long userId);

    /**
     * OPE端
     * 获取用于训练的SIP账号
     */
    PhoneNumberSipAccountVO getVerbalTrickTrainingPhoneNumberOPE(Long userId);

    /**
     * 确保用于训练的SIP账号存在
     */
    PhoneNumberSipAccountVO getVerbalTrickTrainingPhoneNumberCRM(Long userId);

    /**
     * 获取CRM用于训练的主叫的SIP账号
     */
    RobotCallPhoneNumberWithSipInfoBO getCRMVerbalTrickTrainingCallerSipAccountInfo();

    /**
     * 获取OPE用于训练的主叫的SIP账号
     */
    RobotCallPhoneNumberWithSipInfoBO getOPEVerbalTrickTrainingCallerSipAccountInfo();

    /**
     * 获取BOSS用于训练的主叫的SIP账号
     */
    RobotCallPhoneNumberWithSipInfoBO getBOSSVerbalTrickTrainingCallerSipAccountInfo();

    /**
     * 默认线路 即阿里云线路
     * @return
     */
    RobotCallPhoneNumberWithSipInfoBO getDefaultPhoneNumber();

    /**
     * OPE列出所有可以给下级代理商绑定的线路
     */
    List<BindLineDTO> getOpeDistributorCanBindLine(Long beBindDistributorId);

    List<BindLineDTO> getOpeTenantCanBindLine(Long tenantId);

    List<BindLineDTO> getOptTenantCanBindLineMobile(Long tenantId);

    List<BindLineDTO> getOptTenantCanBindLineNotMobile(Long tenantId);

    /**
     * BOSS列出所有可以给下级代理商绑定的线路
     */
    List<BindLineDTO> newGetBossDistributorCanBindLine(Long upDistributorId, Long beBindDistributorId);

    /**
     * 列出所有可以给自己的直销客户绑定的线路, BOSS专用
     * @param beBindTenantId
     * @return
     */
    List<BindLineDTO> newGetTenantCanBindLine(Long upDistributorId, Long beBindTenantId);

    /**
     * 查看已经给代理商绑定的线路(新)
     *
     * @param distributorId
     * @param phoneName
     * @return
     */
    List<BindLineDTO> seeNewLineByDistributor(Long distributorId, Long upDistributorId, String phoneName);

    /**
     * 查看已经给直销客户绑定的线路(新)
     * @param tenantId
     * @return
     */
    List<BindLineDTO> seeNewLineByTenant(Long tenantId, Long upDistributorId);
     /**
     * 查看已经给直销客户绑定的非手机号线路
     * @param tenantId
     * @return
     */
    List<BindLineDTO> seeNotMobileLineByTenant(Long tenantId, Long upDistributorId);


     /**
     * 查看已经给直销客户绑定的手机号线路
     * @param tenantId
     * @return
     */
    List<BindLineDTO> seeMobileLineByTenant(Long tenantId, Long upDistributorId);

    /**
     * 获取代理商能充值的线路 OPE端是没有了
     * @param distributorId
     * @return
     */
    List<BindLineDTO> seeRechargeLineByDistributor(Long distributorId, Long beBindDistributorId);

    void packageListOwner(List<BindLineDTO> bindLineDTOS, List<PhoneNumberPO> phoneNumberPOS, Long upDistributorId);
    /**
     * 获取客户能充值的线路
     * @param tenantId
     * @return
     */
    List<BindLineDTO> seeRechargeLineByTenant(Long distributorId, Long tenantId);

    /**
     * 获取代理商充值线路信息
     * @param ownerDistributorId
     * @param distributorId
     */
    List<BindLineDTO> getRechargeLineByDistributor(Long ownerDistributorId, Long distributorId);

    /**
     * 获取客户充值线路信息
     * @param upDistributorId
     * @param tenantId
     */
    List<BindLineDTO> getRechargeLineByTenantId(Long upDistributorId, Long tenantId);

    /**
     * BOSS端获取客户可充值线路信息
     * @param upDistributorId
     * @param tenantId
     * @return
     */
    List<BindLineDTO> newGetRechargeLineByTenantId(Long upDistributorId, Long tenantId);

    void updatePhoneNumber(Long phoneNumberId, String newPhoneNumber, Long updateUserId);

    void updateRemark(Long phoneNumberId, String remark, Long updateUserId);

    void updateCallInterval(Long phoneNumberId, Integer callInterval, Long updateUserId);

    /**
     * 搜索查询此供应商的所有线路
     * @param distributorId
     * @param searchWords
     * @return
     */
    PageResultObject<SimplePhoneNumberVO> selectBossSimplePhoneNumberListBySearchWords(Long distributorId, String searchWords);

    long addPhoneNumber(PhoneNumberPO phoneNumberPO);

    /**
     * 判断主叫 和 被叫是否为本地
     * 默认返回本地
     * @param calledPhoneNumber 被叫号码
     * @return
     */
    boolean isLocalWithCallerAndCalledPhoneNumber(String calledPhoneNumber, PhoneNumberPO phoneNumberPO);

    /**
     * 查询所有支持呼入功能的线路
     * @return
     */
    List<PhoneNumberPO> selectAllSupportCallInList();

    /**
     * 获取到所有AI呼入的线路
     * @return
     */
    List<PhoneNumberPO> selectAllSupportAiCallInList(String sipAccount, Integer count);

    PhoneNumberPO selectSupportCallInBySipAccount(String callInSipAccount);

    /**
     * 检查支持呼入的线路是否绑定给多个用户，如果是绑定给多个用户，则抛出异常
     * @param phoneNumberId
     */
    void checkSupportCallInOnlyAllowBindToOneSub(Long upDistributorId, Long phoneNumberId, boolean isAdd);

    void checkSupportCallInOnlyAllowBindToOneSub(Long upDistributorId, PhoneNumberPO phoneNumberPO, boolean isAdd);
    /**
     * 校验手机号码是否重复
     * @param oldPhoneNumberId 如果是添加，则传入0即可
     * @param newPhoneNumber 新的电话号码
     */
    void checkPhoneNumberIsDuplicate(Long oldPhoneNumberId, String newPhoneNumber);

    /**
     * 根据设备号（deviceId）列表查询线路列表信息
     * @param deviceIdList
     * @return
     */
    List<PhoneNumberPO> selectByDeviceIdList(List<String> deviceIdList);

    /**
     * 搜索所有的线路信息，只取前 count 个数据， 用于前端线路下啦列表展示
     * @param keyWords
     * @param count
     * @return
     */
    List<SimplePhoneNumberVO> searchAllPhoneNumber(String keyWords, Integer count);

    List<SimplePhoneNumberVO> searchAllPhoneNumberForVos(String keyWords);

    /**
     * 根据线路名模糊搜索
     * @param searchWords
     * @return
     */
    List<PhoneNumberPO> selectByPhoneNumber(String searchWords);

    /**
     * 根据distributorid获取绑定并发的map
     * @param phoneNumberId
     * @param distributorID
     * @return
     */
    Map<Long, Integer> queryPhoneNumberConcurrenceUsedForDistributor(Collection<Long> phoneNumberId, Long distributorID);

    /**
     *
     * @return
     */
    Map<Long, Integer> queryPhoneNumberCurrentConcurrenceForOpe(Collection<Long> phoneNumberIds);

    /**
     *
     * @return
     */
    Map<Long, Integer> queryPhoneNumberCurrentConcurrenceForBoss(Collection<Long> phoneNumberIds, Long upDistributorId);

    PageResultObject queryTenantConcurrencyByPhoneNumber(TenantConcurrencyQueryVO queryVO);

    Integer queryPhoneNumberConcurrenceUsed(Long phoneNumberId, Long upDistributorId);

    List<PhoneNumberNewBossVO> queryBossPhoneNumberListByCondition(PhoneNumberQueryVO queryVO, Collection<Long> phoneNumberIds);

    void updateBossLimitThreshold(Long distributorId, BossPhoneNumberUpdateVO updateVO);

    /**
     * 检查更新绑定的并发是否大于限制，phoneNumberPO是线路信息,如果是更新绑定数量oldConcurrence要管,OPE/apiOpe/customerBindLine/seeNotMobileLineByTenantId里不用管理吧
     * @param phoneNumberPO
     * @param updateConcurrence
     * @return
     */
    Integer checkoutConcurrenceUpdate(Long distributorId,PhoneNumberPO phoneNumberPO, Integer updateConcurrence, Integer oldConcurrence);

    List<PhoneNumberPO> getByDeviceId(String deviceId);

    List<PhoneNumberPO> getPhoneNumberListByIdList(List<Long> phoneNumberIdList);

    List<Long> getPhoneNumberIdListByOwner(CompanyEnum ownerType);

    void addUnLimitPhoneNumberConcurrence(Collection<Long> phoneNumberIds, Collection<PhoneNumberConcurrenceBO> result);

    List<LineMarketResultVO> queryYiwiseLineByCondition(LineMarketQueryVO queryVO);

    void batchInsert(List<PhoneNumberPO> list);

    void batchUpdate(List<PhoneNumberPO> list);

    List<PhoneNumberSupplierStatus> getPhoneNumberSupplierStatusList(List<TenantPhoneNumberDTO> list);

    List<PhoneNumberSupplierStatusBO> getPhoneNumberSupplierStatusListByIds(List<Long> collect);

    List<PhoneNumberPO> getPhoneNumberListByRobotCallJobId(Long tenantId, Long robotCallJobId);

    List<PhoneNumberPO> selectPhoneNumberBySupplierId(Long phoneNumberSupplierId);

    List<PhoneNumberPO> selectAllSupportCallOutAndSetRateList();

    JobStartResultVO batchUpdateBillRate(PhoneNumberBindStatsQueryBO phoneNumberBindStatsQueryBO, Long distributor, Long createUserId);

    PhoneNumberPO selectByPhoneNumberExact(String phoneNumber);

    List<String> selectAreaCodesByProvinceAndCity(String province, String city);

    /**
     * 根据phoneNumberId查询直销客户信息
     * @param phoneNumberId
     * @return
     */
    List<BindLineDTO> selectDirectCustomerPhoneNumberList(PhoneNumberIdQueryVO phoneNumberId);

    /**
     * 根据phoneNumberId查询代理商客户信息
     * @param phoneNumberId
     * @param upDistributorId
     * @return
     */
    List<BindLineDTO> seeNewLineByPhoneNumberId(Long phoneNumberId, Long upDistributorId);

    /**
     * 获取话术体验二维码可绑定线路列表
     * @param name
     * @return
     */
    List<SimplePhoneNumberVO> selectDialogFlowQrcodePhoneNumberList(String name);

    /**
     * 设置体验话术默认线路
     * @param phoneNumberId
     */
    void setTryDialogFlowDefaultPhoneNumber(Long phoneNumberId);

    /**
     * 根据OPENSIPS网关ID查询线路
     * @param queryVO
     * @return
     */
    PageResultObject<SimplePhoneNumberVO> selectPhoneNumberByOpensipsGwId(PhoneNumberOpensipsGwSimpleQueryVO queryVO);

    /**
     * 根据OPENSIPS网关组ID查询线路
     * @param queryVO
     * @return
     */
    PageResultObject<SimplePhoneNumberVO> selectPhoneNumberByOpensipsCarrierId(OpensipsCarrierQueryVO queryVO);

    /**
     * 根据OPENSIPS网关ID导出线路
     * @param queryVO
     * @return
     */
    JobStartResultVO exportPhoneNumberByOpensipsGwId(PhoneNumberOpensipsGwQueryVO queryVO);

    /**
     * 根据OPENSIPS网关组ID导出线路
     * @param queryVO
     * @return
     */
    JobStartResultVO exportPhoneNumberByOpensipsCarrierId(OpensipsCarrierQueryVO queryVO);

    /**
     * 校验网关是否绑定给线路
     *
     * @param gwId
     * @return
     */
    boolean checkPhoneNumberByOpensipsGwId(Integer gwId);

    PageResultObject<PhoneNumberPO> getPhoneNumberPage(int page, int pageSize);

    PhoneNumberPO getByDefaultCallPrefix(String defaultPrefix);

    List<DefaultPrefixAndPhoneNumberIdBO> getOpensispsPhoneNumber();

    List<PhoneNumberPO> selectPhoneNumberPOByOpensipsGwId(Integer id);

    PageResultObject<SimpleCarrierVO> getSimpleCarrierList(PhoneNumberOpensipsGwSimpleQueryVO queryVO);

    LinkedList<Integer> selectCarrierIds(Long key);

    Map<Long, PhoneNumberPO> getPhoneNumberMapToBaseInfo(Set<Long> phoneNumberIdList);

    List<SimplePhoneNumberVO> selectByPhoneNumbers(List<String> searchWords);
}
