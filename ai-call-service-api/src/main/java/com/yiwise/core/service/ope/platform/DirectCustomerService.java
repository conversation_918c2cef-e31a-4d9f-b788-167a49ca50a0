package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.RobotStasPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.dto.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.request.MagicBotRequest;
import com.yiwise.core.model.request.TenantHeaderFieldRequest;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.openapi.qiyu.TenantQiyuResultVO;
import com.yiwise.core.model.vo.openapi.qiyu.TenantQiyuVO;
import com.yiwise.core.model.vo.tenant.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface DirectCustomerService extends BasicService<TenantPO> {

    /**
     * boss与系统获取所有的直销客户
     */
    PageResultObject<DirectAndDistributorCustomerListVO> listAllCustomer(DirectCustomerQueryVO query, boolean isDirect);

    JobStartResultVO opeExportListAllCustomer(DirectCustomerQueryVO query);

    void packageDirectCustomerList(List<? extends DirectAndDistributorCustomerListVO> tenantOpePOS, DirectCustomerQueryVO query);

    /**
     * 坐席列表
     */
    List<RobotExpirationTimeVO> getRobotListByTenantId(Long tenantId);

    List<TenantIdAndNamePairVO> getTenantIdAndNamePairList(Long distributorId, String search);

    /**
     * 添加直销客户
     */
    String addDirectCustomer(CustomerInsertVO customerInsertVO);

    /**
     * 更新直销客户
     */
    void updateDirectCustomer(CustomerUpdateVO tenantPO);

    /**
     * 扩容
     */
    void dilatation(Long tenantId, Integer count, LocalDate startTime, LocalDate endTime, Long userId,TenantAiccPartEnum tenantAiccPartEnum);

    Double  calculateDilatationCost(Long tenantId, Integer count, LocalDate startTime, LocalDate endTime, Long distributorId,TenantAiccPartEnum tenantAiccPartType);

    /**
     * boss系统添加直销/经销商 客户
     */
    String addBossCustomer(CustomerInsertVO customerInsertVO);

    /**
     * 网易七鱼使用新增代理商客户
     */
    TenantQiyuResultVO openApiAddBossCustomer(TenantQiyuVO tenantQiyuVO);
    /**
     * 网易七鱼使用新增代理商客户  +绑定 14967 话术
     */
    TenantQiyuResultVO openApiAddBossCustomerNew(TenantQiyuVO tenantQiyuVO);

    /**
     * 更改状态
     */
    void updateStatus(Long tenantId, TenantStatusEnum status, Long updateUserId);


    /**
     * boss系统的扩容
     */
    void bossDilatation(Long tenantId, Integer count, LocalDate startTime, LocalDate endTime, Long distributorId, Long userId,TenantAiccPartEnum tenantAiccPartEnum);
	void bossDilatationTransactional(Long tenantId, Integer count, LocalDate startTime, LocalDate endTime, Long distributorId, Long userId,TenantAiccPartEnum tenantAiccPartEnum);

    /**
     * 调整服务周期
     */
    void adjustingServiceCycle(Long id, LocalDate newStartTime, LocalDate newEndTime, Long tenantId, Long userId, Long reduceAiAmount,TenantAiccPartEnum tenantAiccPartType);


    //计算调整服务周期费用
    Double calculateAdjustingServiceCycleCost(Long robotId, Long tenantId, Long distributorId,Long userId, LocalDate newStartTime, LocalDate newEndTime, Long reduceAiAmount);
    /**
     * boss系统给直销客户调整服务周期
     */
    void adjustingServiceCycleByBossDirectCustomer(Long id, LocalDate newStartTime, LocalDate newEndTime, Long tenantId, Long userId, Long distributorId, Long reduceAiAmount,TenantAiccPartEnum tenantAiccPartType );


    boolean initConditon(DirectCustomerQueryVO query);


    List<TradeTypeInfoDTO> getAllIndustryOptions();

    /**
     * 修改客户绑定的实施人
     *
     * @param tenantId 客户id
     * @param implementId 实施人id
     * @return 结果
     */
    boolean modifyCustomerImplement(Long tenantId, List<Long> implementId);

    /**
     * 修改姓名与手机号
     *
     * @param tenantId      租户id
     * @param updateUserId  修改人
     * @param linkman       联系人
     *  @param phoneNumber  手机号
     * @return 结果
     */
    boolean modifyCustomerNameAndPhone(Long tenantId, Long updateUserId, String linkman, String phoneNumber);

    /**
     * 修改客户账号类型
     *
     * @param tenantId         客户id
     * @param updateUserId     修改人
     * @param accountTypeEnum  修改的账户类型
     * @return 结果
     */
    boolean modifyCustomerType(Long tenantId, Long updateUserId, AccountTypeEnum accountTypeEnum);

    /**
     * openapi那边修改直销客户基本信息
     *
     * @param customerUpdateVO 参数
     * @return 结果
     */
    boolean modifyOpenApiCustomerInfo(CustomerUpdateVO customerUpdateVO);

    void openApiUpdateOpeDirectCustomerWithOtherInfo(TenantQiyuVO tenantQiyuVO);

    void opeDirectAdjustingServiceCycle(Long id, LocalDate newStartTime, LocalDate newEndTime, Long tenantId, Long userId, Long reduceAiAmount,TenantAiccPartEnum tenantAiccPartType);

    void opeDirectDilatation(Long tenantId, Integer count, LocalDate startTime, LocalDate endTime, Long userId, TenantAiccPartEnum tenantAiccPartType, RobotSeatTypeEnum robotSeatTypeEnum);

    void opeDirectCustomerDelayOld(Long id, LocalDate newEndTime, Long tenantId, Long userId, TenantAiccPartEnum systemType);

    /**
     * 获取所有客户行业信息
     * （客户行业和行业信息有2套，直销客户走客户行业，代理商客户走行业信息）
     */
    List<CustomerIndustryDTO> getAllCustomerIndustry();

    void openAiccPartFunction(TenantAiccPartDTO tenantPO);

    Map<TenantAiccPartEnum, RobotStasPO> queryAiccPartRobots(Long tenantId);

    Map<TenantAiccPartEnum, RobotStasPO> getTenantAiccPartList(List<RobotExpirationTimeVO> robotList);

    void batchUpdateStatus(List<Long> tenantIds, TenantStatusEnum status, Long currentUserId,DirectCustomerQueryVO query,Boolean selectAll);

    void packageDirectCustomerAccountInfo(List<? extends DirectAndDistributorCustomerListVO> tenantOpePOS);

    /**
     * 处理编辑客户的OpenAPI的特殊逻辑
     */
    void processOpenAPIRequest(OpenAPICustomerEditVO request);

    /**
     * 获取代理商的直销客户列表
     */
    PageResultObject<DistributorDirectCustomerOEMVO> listDirectCustomerForOEM(DirectCustomerQueryVO query);

    PageResultObject<DirectAndDistributorCustomerListVO> getDirectTenantList(DirectCustomerQueryVO query);

    void updateMainBrandConfig(MainBrandConfigVO mainBrandConfigVO);

    void updateAiccVersion(TenantVersionVO tenantVersionVO);

    /**
     * 开启/关闭轻量版aicc
     * @param query
     */
    void updateMagicBotStatus(MagicBotRequest query);

    String checkVirtualSmsConfig(Long tenantId);

    void savePreCallOutFilterStatisOpen(RobotCallJobFilterConfigDTO robotCallJobFilterConfigDTO);

    void setHeaderFields(TenantHeaderFieldRequest tenantHeaderFieldRequest);

    List<String> getHeaderFields(TenantHeaderFieldRequest tenantHeaderFieldRequest);
}
