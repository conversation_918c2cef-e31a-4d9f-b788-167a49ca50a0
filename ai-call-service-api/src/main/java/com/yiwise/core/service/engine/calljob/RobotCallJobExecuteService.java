package com.yiwise.core.service.engine.calljob;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.model.bo.robotcalljob.*;
import com.yiwise.core.model.enums.JobOperationEnum;
import com.yiwise.core.model.vo.robotcalljob.CheckJobsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/9/11
 **/
public interface RobotCallJobExecuteService extends BasicService<RobotCallJobPO> {


    /**
     * add by zqy
     * 任务执行
     *
     * @param robotCallJobId 任务id
     * @param operation      start 开始 pause 暂停 stop 终止
     */
    void executeRobotCallJob(Long tenantId, Long robotCallJobId, JobOperationEnum operation);
	/**
	 * @param userId 用户id
	 */
    void executeRobotCallJob(Long tenantId, Long robotCallJobId, JobOperationEnum operation, Long userId);
    void executeRobotCallJob(Long tenantId, Long robotCallJobId, JobOperationEnum operation, Long userId, Boolean isOpenApi);

    /**
     * 执行可执行的任务
     *
     * @param tenantId       租户id
     * @param operation      操作
     * @param userId         用户id
     * @param executableJobs 任务列表
     */
    List<UnexecutableJobBO> executeRobotCallJobs(Long tenantId, JobOperationEnum operation, Long userId, List<ExecutableJobBO> executableJobs);

    /**
     * 检查所有任务是否可执行某个操作
     *
     * @param tenantId  租户id
     * @param checkJobs 操作
     */
    JobCheckResultBO checkAllRobotCallJob(Long tenantId, CheckJobsVO checkJobs);

    /**
     * 检查是否处于系统维护状态
     */
    boolean checkSystemMaintain();

    /**
     * 禁用账号下所有可能运行的任务
     */
    void setJobStatusDisabled(Long tenantId);

    void setSystemMaintain(Long userId);

    void startSystemMaintainJobs(Long userId);
}
