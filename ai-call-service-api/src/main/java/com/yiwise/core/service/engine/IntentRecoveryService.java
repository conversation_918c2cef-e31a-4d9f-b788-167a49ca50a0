package com.yiwise.core.service.engine;

import com.yiwise.core.dal.entity.IntentRecoveryPO;
import com.yiwise.core.model.vo.voicerecovery.IntentRecoveryVO;
import com.yiwise.base.service.BasicService;

import java.util.List;

public interface IntentRecoveryService extends BasicService<IntentRecoveryPO> {

    /**
     * crm获取语音纠错记录历史
     *
     * @param callRecordId
     * @return
     */
    List<IntentRecoveryVO> getIntentRecoveryHistory(Long callRecordId);

    void deleteByCallDetailId(Long callDetailId, Integer intentType);

    List<IntentRecoveryPO> selectByCallDetailId(Long callDetailId, Integer intentType);
}
