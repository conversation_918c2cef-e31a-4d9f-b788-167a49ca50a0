package com.yiwise.core.service.engine;


import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.callcost.CallCostDetailBO;
import com.yiwise.core.model.dto.CallRecordExportDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallRecordReadStatusEnum;
import com.yiwise.core.model.enums.callrecord.InterceptStatusEnum;
import com.yiwise.core.model.vo.callcost.CallCostDetailExportRequestVO;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.calloutplan.CallOutPlanDurationVO;
import com.yiwise.core.model.vo.callrecord.*;
import com.yiwise.core.model.vo.intentleveltag.IntentLevelTagDetailVO;
import com.yiwise.core.model.vo.openapi.ant.GetJobDetailAntRequestVO;
import com.yiwise.core.model.vo.robotcalltask.CallRecordLimitQueryVO;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
public interface CallRecordInfoService extends BasicService<CallRecordPO> {

    /**
     * 根据外呼计划获取最早外呼日期和最迟外呼日期
     */
    CallOutPlanDurationVO getPlanStartAndEndTime(Long callOutPlanId, Long tenantId);
    /**
     * 获取联系历史列表
     *
     * @param condition 查询条件
     */
    PageResultObject<CallRecordExportDTO> getCallRecordInfoList(CallRecordQueryVO condition);

    /**
     * 来自任务的通话记录列表 含数据权限
     *
     * @param condition 查询条件
     */
    PageResultObject<CallRecordExportDTO> getCallRecordInfoListFromJob(CallRecordQueryVO condition, @NotNull UserPO user);
    PageResultObject<CallRecordExportDTO> getCallRecordInfoListFromJobList(CallRecordQueryVO condition, @NotNull UserPO user);
    Long getCallRecordInfoListFromJobCount(CallRecordQueryVO condition, @NotNull UserPO user);
	/**
	 * 查询指定的通话记录对应的外呼任务id集合
	 */
	Set<Long> getRobotCallJobIdsFromJob(CallRecordQueryVO condition);

    /**
     * 来自快速拨打的通话记录列表 含数据权限
     *
     * @param condition 查询条件
     */
    PageResultObject<CallRecordExportDTO> getCallRecordInfoListFromDirectCall(CallRecordBossAndOpeQueryVO condition, @NotNull UserPO user);

    /**
     * 查询话术体验通话记录
     */
    PageResultObject<CallRecordExportDTO> selectCallRecordsFromTryDialogFlowCall(CallRecordBossAndOpeQueryVO queryVO);

    List<CallRecordExportDTO> selectOpeAndBossCallRecordsFromDirectCall(CallRecordBossAndOpeQueryVO condition);

    /**
     * 来自话术训练的通话记录列表 含数据权限
     *
     * @param condition 查询条件
     */
    PageResultObject<CallRecordExportDTO> getCallRecordInfoListFromTraining(CallRecordQueryVO condition, @NotNull UserPO user);

    Integer getCallRecordInfoListFromTrainingCount(CallRecordQueryVO condition, @NotNull UserPO user);

    /**
     * 根据客户id获取所有的通话记录
     */
    List<CallRecordPO> selectAllByCustomerPersonIdAndTenantId(Long customerPersonId, Long tenantId);

    /**
     * 标记callRecord的意向等级
     */
    void updateCallRecordIntentLevel(Long tenantId, Long callRecordId, Integer intentLevel);

    /**
     * 标记callRecord的意向等级
     *
     * @param taskDone 修改时该通话是否已结束: true已结束,false未结束
     */
    void updateCallRecordIntentLevel(Long tenantId, Long callRecordId, Integer intentLevel, boolean taskDone);

    /**
     * 标记callRecord的意向等级
     * OPE话术训练使用 不传入tenantId
     */
    void updateCallRecordIntentLevel(Long callRecordId, Integer intentLevel);

    /**
     * 打包获取任务信息，主叫号码信息，客户信息
     */
    void packageCallRecordListWithJobAndCustomerInfo(List<? extends CallRecordExportDTO> callRecordList, boolean useCache, Integer systemReadStatus,boolean openapi);

    void packageCallRecordListWithJobAndCustomerInfo(List<? extends CallRecordExportDTO> callRecordList, boolean useCache, Integer systemReadStatus);


    void packageCallDetailList(List<? extends CallRecordExportDTO> callRecordList);

    CallRecordPO getCallRecord(Long tenantId, Long callRecordId);

    /**
     * 根据已呼列表查询条件查询客户id集合
     */
    List<Long> getCustomerPersonIdsByCondition(CallRecordQueryVO request);

    int countCallRecordsByIdsOrConditions(CallRecordQueryVO request);

    int countTasksByIdsOrConditions(CallRecordQueryVO request);

	/**
	 * 注意是在call_record表里查task_id字段而不是查task表,对于一些公共的查询字段两者的查询结果会不同
	 */
	Set<Long> getTaskIdsByIdsOrConditions(CallRecordQueryVO request);

    /**
     * 设置消息的阅读状态
     *
     * @param callRecordId 通话记录id
     * @param tenantId     租户id
     * @param system       系统属性
     */
    void setCallRecordStatus(Long callRecordId, Long tenantId, SystemEnum system);

    void encryptCallRecordId(List<CallRecordExportDTO> callRecordExportDTOS);

    /**
     * 阅读状态 二进制表示 0-未读 1-已读 第一位001-BOSS 第二位010-OPE 第三位100-CRM
     */
    CallRecordReadStatusEnum getRealCallRecordReadResult(Integer readStatus, SystemEnum systemEnum);

    /**
     * 根据话术id和任务id设置意向标签分组id
     */
    void setIntentLevelTagId(CallRecordQueryVO condition);

    void updateWechatInfoUsers(Long tenantId, Long callRecordId, Set<Long> userIds, LocalDateTime time);

    /**
     * 根据taskId，查询记录列表
     */
    List<CallRecordPO> getByRobotCallTaskId(Long tenantId, Long robotCallTaskId);

	/**
	 * 只查询了部分字段
	 */
	List<CallRecordPO> getByRobotCallTaskIds(Long tenantId, Collection<Long> robotCallTaskIds);

    int countAnsweredByRobotCallTaskId(Long tenantId, Long robotCallTaskId);

    /**
     * 查询话费信息
     */
    List<CallCostDetailBO> selectCallCostDetail(CallCostQueryVO callCostQuery, List<Long> userIdList);

    Integer selectCountExportCallCostDetail(CallCostDetailExportRequestVO callCostDetailExportRequest);

    void updateRecognize(Long callRecordId, CustomerEmotionEnum emotion, GenderEnum gender);

    /**
     * ope查询通话是否为问卷类型
     *
     * @param callRecordId 通话记录id
     * @return true 是问卷类型
     */
    Boolean hasReturnVisitOpe(Long callRecordId);

    /**
     * boss查询通话是否为问卷类型
     *
     * @param callRecordId 通话记录id
     * @param distributorId     租户id
     * @return true 是问卷类型
     */
    Boolean hasReturnVisitBoss(Long callRecordId, Long distributorId);

    /**
     * crm查询通话是否为问卷类型
     *
     * @param callRecordId 通话记录id
     * @param tenantId     租户id
     * @return true 是问卷类型
     */
    Boolean hasReturnVisitCrm(Long callRecordId, Long tenantId);

    /**
     * 查询问卷类型话术的回访结果
     *
     * @param callRecordId 通话记录id
     * @return 回访结果
     */
    ReturnVisitVO getReturnVisitOpe(Long callRecordId);

    /**
     * 查询问卷类型话术的回访结果
     *
     * @param callRecordId  通话记录id
     * @param distributorId 客户id
     * @return 回访结果
     */
    ReturnVisitVO getReturnVisitBoss(Long callRecordId, Long distributorId);

    /**
     * 查询问卷类型话术的回访结果
     *
     * @param callRecordId 通话记录id
     * @param tenantId     客户id
     * @return 回访结果
     */
    ReturnVisitVO getReturnVisitCrm(Long callRecordId, Long tenantId);

    IntentLevelTagDetailVO getIntentLevelTagByCallRecordId(Long tenantId, Long callRecordId, CustomerFollowRecordFollowTypeEnum followType);

    void syncCallRecordToEs(CallRecordPO callRecordPO);

    void syncCallRecordToEsManually(CallRecordPO callRecordPO);

    void syncCallRecordToEsManuallyByJob(Long tenantId, Long robotCallJobId);

    List<CallRecordExportDTO> selectCallRecordFromES(CallRecordQueryVO callRecordQuery);

     void customerPersonExtraInfoHistory(List<? extends CallRecordExportDTO> callRecordList);

    Long selectCountCallRecordFromES(CallRecordQueryVO callRecordQuery);

    Integer selectRealIntentLevel(Long callRecordId);

    /**
     * 更新加微信息
     */
    void updateAddWechatFriend(WechatCpAddFriendPO wechatCpAddFriendPO);

    /**
     * 获取 任务已呼客户 总数
     */
    Long getCallRecordInfoListCount(CallRecordQueryVO callRecordQuery);

	/**
	 * SCRM调用
	 */
	List<CallRecordPO> selectByCalledPhoneNumber(Long tenantId, List<String> calledPhoneNumber, Integer pageSize);

	/**
	 * 查询该外呼任务在某天中最早的外呼时间和最晚的外呼时间
	 *
	 * @return pair.left最早的外呼时间, pair.right最晚的外呼时间
	 */
	Pair<LocalDateTime, LocalDateTime> selectEarliestAndLatestInDay(Long tenantId, Long robotCallJobId, LocalDate localDate);

    int checkExistByRobotCallTaskId(Long tenantId, Long robotCallTaskId);

    void deleteCallRecordEs(List<Long> callRecordIds);

    List<CallRecordPO> selectCallRecordForAntRequest(GetJobDetailAntRequestVO request);

    Integer selectCountCallRecordForAntRequest(GetJobDetailAntRequestVO request);

	/**
	 * 查询指定客户的指定日期之前的记录
	 */
	List<CallRecordPO> selectByTenantIdStartTime(Long tenantId, Integer day);

	/**
	 * 遍历使用
	 */
	List<Long> selectCallRecordIdsOrderByCallRecordId(Long callRecordId, Integer batchSize, LocalDate localDate);

	/**
	 * 遍历使用
	 */
	List<CallRecordPO> selectOrderByCallRecordId(Long callRecordId, Integer batchSize, Long lastCallRecordId);

	void deleteDeploymentInformationByCallRecordIds(Collection<Long> callRecordIds);

	/**
	 * 遍历开始使用
	 */
	Long selectCallRecordIdAfterLocalDateTime(LocalDateTime localDateTime);
	Long selectCallRecordIdBeforeLocalDateTime(LocalDateTime localDateTime);

    CallRecordListResVO getCallRecordListLimit(CallRecordLimitQueryVO callRecordLimitQueryVO);

    List<CallRecordPO> getListByADB(Long tenantId,LocalDateTime startTime, LocalDateTime endTime,Long startPoint,Integer limit);

    List<CallRecordPO> getByIdListADB(List<Long> oldRecordIdList);

    void updateInterceptStatus(Long callRecordId, InterceptStatusEnum interceptStatusEnum);
}
