package com.yiwise.core.service.privacynumber;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.privacynumber.PrivacyNumberBindingPO;
import com.yiwise.core.dal.entity.privacynumber.PrivacyNumberContactHistoryPO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.IdLongCountPairBO;
import com.yiwise.core.dal.entity.privacynumber.PrivacyNumberOperatorPO;
import com.yiwise.core.dal.entity.privacynumber.PrivacyNumberPO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.privacynumber.aliyun.AliyunCallbackRequestVO;
import com.yiwise.core.model.vo.privacynumber.aliyun.AliyunClickToDialResponseVO;
import com.yiwise.core.model.vo.privacynumber.aliyun.AliyunVoiceReportVO;
import com.yiwise.core.model.vo.privacynumber.changgong.ChangGongCallbackRequestVO;
import com.yiwise.core.model.vo.privacynumber.fengyin.api.FengyinAuthRequestVO;
import com.yiwise.core.model.vo.privacynumber.fengyin.api.FengyinRequestVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberContactHistoryQueryVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberContactHistoryVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberRemoteVO;
import com.yiwise.core.model.vo.privacynumber.yiyun.YiyunCallbackRequestVO;
import com.yiwise.core.model.vo.privacynumber.yiyun.YiyunCallbackResponseVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/08/24
 */
public interface PrivacyNumberContactHistoryService extends BasicService<PrivacyNumberContactHistoryPO> {

	/**
	 * 前端查询, 分页
	 */
	PageResultObject<PrivacyNumberContactHistoryVO> query(PrivacyNumberContactHistoryQueryVO query);

	List<IdLongCountPairBO> selectCostGroupByTenantId(LocalDate localDate);

	JobStartResultVO export(PrivacyNumberContactHistoryQueryVO query);

	void downloadRecordAndUpload();

	void appendFields(PrivacyNumberContactHistoryVO contactHistory);

	/**
	 * 隐私号平台的迪信通/风音回调
	 */
	Map<String, Object> dixintongAxCallback(List<FengyinRequestVO> request);
	Map<String, Object> dixintongAxbCallback(List<FengyinRequestVO> request);

	/**
	 * 隐私号平台的翊云回调
	 */
	YiyunCallbackResponseVO yiyunAxCallback(YiyunCallbackRequestVO request);

	/**
	 * 风音实名认证回调
	 */
    Map<String, Object> fengyinAuthCallback(FengyinAuthRequestVO request);

	/**
	 * 隐私号平台的阿里云AX回调
	 */
	Map<String, Object> aliyunAxCallback(List<AliyunCallbackRequestVO> request);

	/**
	 * 双呼固话话单回调
	 */
	Map<String, Object> aliyunDoubleCallCallback(List<AliyunVoiceReportVO> request);

	void callErrorHistory(Long tenantId, String numberA, String numberX, String numberB, PrivacyNumberPO privacyNumber, PrivacyNumberOperatorPO operator, PrivacyNumberBindingPO binding, String remark, String releaseCause, String axbLog, String doubleCallLog);

	void callSuccessHistory(Long tenantId, String numberA, String numberX, String numberB, String fixedPhone, PrivacyNumberPO privacyNumber, PrivacyNumberOperatorPO operator, PrivacyNumberBindingPO binding, String remark, PrivacyNumberRemoteVO privacyNumberRemoteVO, AliyunClickToDialResponseVO response);

	PageResultObject<PrivacyNumberContactHistoryVO> queryByOpe(PrivacyNumberContactHistoryQueryVO query);

	void checkErrorHistoryData(LocalDateTime now);

	void downloadRecordAndUploadByManual(Long id);

	void downloadRecordByAliyunAxb(LocalDateTime startTime, LocalDateTime endTime);

	Map<String, Object> changGongAxCallback(ChangGongCallbackRequestVO request);

	JobStartResultVO exportByOpe(PrivacyNumberContactHistoryQueryVO query);

	void appendFieldsByOpe(List<? extends PrivacyNumberContactHistoryVO> contactHistory);

	void warnAxbErrorHistoryList(LocalDateTime now);
}
