package com.yiwise.core.service.ope.platform;


import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.DialogFlowDistributorRelationPO;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.mongo.DistributorConfigModelMongoPO;
import com.yiwise.core.model.dialogflow.dto.DialogFlowFilterOptionDTO;
import com.yiwise.core.model.dialogflow.vo.DialogFlowListVO;
import com.yiwise.core.model.dto.distributor.BillInfoQueryDTO;
import com.yiwise.core.model.enums.CompanyEnum;
import com.yiwise.core.model.enums.RechargePrestoreStreamFunEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.dialogflow.DialogFlowTypeEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.*;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.ope.DistributorConfigModelQueryVO;
import com.yiwise.core.model.vo.ope.DistributorConfigModelVO;
import com.yiwise.core.model.vo.ope.DistributorOpeUpdateVO;
import com.yiwise.core.model.vo.ope.DistributorOpeVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.tenant.TenantExportSimpleVO;
import com.yiwise.lcs.api.dto.SmsPlatformChannelDTO;
import com.yiwise.core.model.vo.ope.DistributorMainBrandConfigVO;
import com.yiwise.core.model.vo.ope.DistributorOpeUpdateVO;
import com.yiwise.core.model.vo.ope.DistributorOpeVO;
import com.yiwise.core.model.vo.ope.UpdateDistributorEnableRechargeOnlineVO;
import com.yiwise.core.model.vo.ope.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Wang
 */
public interface DistributorService extends BasicService<DistributorPO> {

    void createDistributor(DistributorPO distributorPO, Long createUserId);

    void updateDistributor(DistributorPO distributorPO);

    void extendDistributor(Long id, BigDecimal robot, Integer dialogFlow, Long userId);

    /**
     * 获取所有的经销商
     *
     * @return
     */
    Set<DistributorPO> getAll();

    /**
     * boss获取所有的二级代理商客户
     *
     * @param distributorId
     * @return
     */
    Set<DistributorPO> getSecondDistributors(Long distributorId);

    List<Long> selectByParentId(Long distributorId);

    /**
     * 根据姓名查询一级代理商, 用于设置电话卡时搜索归属者, 自定义分页大小
     *
     * @param searchWords 姓名关键词
     * @param size        列表大小
     * @return po列表
     */
    List<DistributorPO> selectDistributorByName(String searchWords, Integer size);

    /**
     * 根据姓名查询二级经销商, 用于设置电话卡时搜索归属者, 自定义分页大小
     *
     * @param searchWords 姓名关键词
     * @param size        列表大小
     * @return po列表
     */
    List<DistributorPO> selectSubDistributorByName(String searchWords, Integer size);

    /**
     * boss系统新建经销商逻辑
     *
     * @param distributorVO
     */
    String addBossDistributor(DistributorVO distributorVO);

    /**
     * boss系统前端展示ai坐席总量以及话术总量
     *
     * @param distributorId
     * @return
     */
    DistributorAIVO getAIRobotAndDialogFlow(Long distributorId);

    /**
     * boss 系统更新经销商
     *
     * @param distributorVO
     * @param updateUserId  更新人ID
     */
    void updateBossDistributor(DistributorVO distributorVO, Long updateUserId);

    /**
     * boss系统更新经销商账户的状态
     *
     * @param updateUser
     * @param distributorId
     * @param flag
     */
    void updateStatus(Long updateUser, Long distributorId, boolean flag);

    /**
     * boss系统经销商扩容
     *
     * @param distributorId
     * @param updateUser
     * @param firstDistributorId
     * @param robotCount
     * @param dialogFlowCount
     */
    void dilatation(Long distributorId, Long updateUser, Long firstDistributorId, BigDecimal robotCount, Integer dialogFlowCount);


    /**
     * boss系统列出所有的经销商
     *
     * @return
     */
    PageResultObject<DistributorListVO> listBossDistributor(DistributorQueryVO distributorQueryVO);

    void packageDistributorList(List<? extends DistributorListVO> distributors, SystemEnum systemEnum);


    JobStartResultVO exportListBossDistributor(DistributorQueryVO distributorQueryVO);

    Integer countExport(DistributorQueryVO distributorQueryVO);
    /**
     * boss系统获取该经销商所有已经消耗掉的话术集合
     *
     * @param distributorId
     * @return
     */
    List<DialogFlowListVO> getConsumeDialogFlow(Long distributorId, String name, DialogFlowTypeEnum type,Integer flag,LocalDate startTime,LocalDate endTime);

    /**
     * 根据ID获取代理商信息
     *
     * @param distributorId
     * @return
     */
    DistributorAccountVO getDistributorById(Long distributorId);

    List<Long> selectDistributorIdByType(CompanyEnum ownerType);

    /**
     * 获取二级代理商下拉框
     *
     * @return
     */
    List<DistributorListVO> querySubDistributor();

    /**
     * 获取新建二级代理商一级代理商下拉框
     *
     * @return
     */
    List<DistributorListVO> querySuperDistributor();

    /**
     * 获取新建二级代理商客户时一级代理商下拉框
     *
     * @return
     */
    List<DistributorListVO> queryCustomerSuperDistributor();


    /**
     * boss端创建下级经销商
     *
     * @param distributorPO
     * @param createUserId
     */
    void createSubDistributor(DistributorOpeVO distributorPO, Long createUserId);

    /**
     * 扩容二级代理商
     *
     * @param id
     * @param robot
     * @param dialogFlow
     */
    void extendSubDistributor(Long id, BigDecimal robot, Integer dialogFlow, Long userId);

    /**
     * 更新老代理商库存
     *
     * @param startTime
     * @param endTime
     * @param distributorId 自己的ID
     */
    void updateRepositoryOfDistributor(LocalDate startTime, LocalDate endTime, Long distributorId, Integer dialogFlow, Long concurrencyNumber);

    void update(DistributorPO distributorPO);

    /**
     * 分销商导出总数
     *
     * @param request
     * @return
     */
    Integer getCountBossDistributorExport(DistributorQueryVO request);

    /**
     * 编辑代理商时带出的信息
     *
     * @param distributorId
     * @return
     */
    DistributorListVO getDistributorInfo(Long distributorId);

    /**
     * 二级代理商列表
     *
     * @param queryCondition
     * @return
     */
    PageResultObject<DistributorListVO> listSubDistributor(DistributorQueryVO queryCondition);

    void packageListSubDistributor(List<? extends DistributorListVO> distributors,DistributorQueryVO queryCondition);

    JobStartResultVO exportListSubDistributor(DistributorQueryVO queryCondition);

    void chooseDialogFlow(DialogFlowDistributorRelationPO dialogFlowDistributorRelationPO, Long userId);

    /**
     * 更新经销商话术库存数量
     *
     * @param distributorId 经销商id
     * @param stockNum      要变更的库存数量，负数是减库存，正数是加库存
     */
    void updateStock(Long distributorId, int stockNum);

    /**
     * 在旧的数据基础上更新话术库存
     *
     * @param oldDistributorPO 更新前数据
     * @param stockNum         要变更的库存数量，负数是减库存，正数是加库存
     */
    void updateStock(DistributorPO oldDistributorPO, int stockNum);

    void updateStock(DistributorPO oldDistributorPO, int stockNum,double cost,boolean flag);

    /**
     * 根据id查询经销商信息,并且forUpdate
     *
     * @param distributorId 经销商id
     * @return
     */
    DistributorPO selectByKeyForUpdate(Long distributorId);

    /**
     * 查询该话术分配的二级代理商信息，只查一个
     *
     * @param distributorId   分销商id
     * @param dialogFlowIdSet
     * @return
     */
    DistributorPO getOneSubDistributorByAllocatedDialogFlowIdSet(Long distributorId, Set<Long> dialogFlowIdSet);

    /**
     * 新建OPE流水记录
     *
     * @param distributorOpeVO
     */
    void createStream(DistributorOpeVO distributorOpeVO, Long createUserId, boolean ifSubDistributor);

    /**
     * 新建代理商和用户
     *
     * @param distributorOpeVO
     * @param createUserId
     */
    String createDistributorAndUser(DistributorOpeVO distributorOpeVO, Long createUserId);

    /**
     * 根据distributorIdSet 查询经销商信息handDistributor
     * @param distributorIdSet 经销商id集合
     * @return list
     */
    List<DistributorPO> selectListByDistributorIdSet(Set<Long> distributorIdSet);

    /**
     * 查询所有的代理商信息
     * @param onlyAgent 是否只查询代理商信息，true只查询代理商
     *                  false 只查询二级代理商。
     * @param name 代理商名称
     * @return set
     */
    List<DistributorPO> getAllAgentList(String name, boolean onlyAgent);

    /**
     * 搜索代理商名称和id键值对
     * @param company
     * @param searchName
     * @param searchLinkman
     * @return
     */
    List<DistributorIdAndNamePairVO> searchDistributorByCompanyType(CompanyEnum company, String searchName, String searchLinkman, String searchPhoneNumber, Integer pageNum, Integer pageSize);

    void opeUpdate(DistributorOpeUpdateVO distributorPO);

    void handDistributor(Long distributorId, Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long userId,LocalDate startTime,LocalDate endTime);

    String createDistributorLogic(DistributorOpeVO distributorVO, Long createdUserId);

    String createSubDistributorLogic(DistributorOpeVO distributorVO,Long createdUserId);

    void updateExamineSubDistributorCustomer(Long updateUser, Long distributorId, boolean flag);

    void updateChangeAiCycleStatus(Long updateUser, Long distributorId, boolean flag);

    void hangLiziAccount(Long distributorId, Long clueComboPrice);

    //账户体系

    /**
     * 增加账户余额
     */
    void increaseDistributorAccount(Long distributorId, Long accountFare);

    /**
     * 减少账户余额
     */
    void reduceDistributorAccount(Long distributorId, Long accountFare);
    /**
     * 增加预存款账户余额
     */
    void increaseDistributorPrestoreAccount(Long distributorId, Long accountFare);

    /**
     * 减少预存款账户余额
     */
    void reduceDistributorPrestoreAccount(Long distributorId, Long accountFare);

    JobStartResultVO exportDistributorConsumedDialogFlow(DialogFlowFilterOptionDTO dialogFlowFilterOptionDTO);

    void appendDistributorFares(List<? extends DistributorListVO> items);

    JobStartResultVO exportBillDetailList(BillInfoQueryDTO billInfoQueryDTO);

    void updateEnableRechargeOnline(UpdateDistributorEnableRechargeOnlineVO requestVO);

    void updateMainBrandConfig(DistributorMainBrandConfigVO requestVO);

    void saveDistributorModel(DistributorConfigModelVO distributorConfigModelVO);

    DistributorTenantSimpleInfoVO isSameDistributor(DistributorConfigModelQueryVO distributorConfigModelVO);

    JobStartResultVO exportFailedTenantId(DistributorConfigModelVO req, Long tenantId, Long userId);

    List<TenantExportSimpleVO> batchEditDistributor(DistributorConfigModelVO req, Long userId);

    /**
     * 获取要操作的客户id
     * @param query
     * @return
     */
    List<Long> getTenantId(DirectCustomerQueryVO query);

    List<SmsPlatformChannelDTO> getDistributorSmsConfig(Long distributorId);

    DistributorConfigModelMongoPO getModelConfigMongoPO(Long distributorId);

    DistributorConfigModelVO getModelConfigVO(Long distributorId);

    List<DistributorConfigModelVO> getModelConfigVOList(List<Long> distributorIds);



    void setContractId(Long distributorId, String contractId);

    String getContractIdByDistributor(Long distributorId);

    String initiateContract(DistributorInitiateContractVO req);

    /**
     * 获取一级代理商
     *
     * @return
     */
    List<DistributorSimpleVO> getDistributorList(String searchWords);
}

