package com.yiwise.core.service.ope.platform;


import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.InitiateContractRecordPO;
import com.yiwise.core.model.vo.ope.InitiateContractRecordQueryVO;

public interface InitiateContractRecordService extends BasicService<InitiateContractRecordPO> {

    InitiateContractRecordPO selectBySignFlowId(String signFlowId);

    PageResultObject<InitiateContractRecordPO> selectPageInfo(InitiateContractRecordQueryVO req);

}

