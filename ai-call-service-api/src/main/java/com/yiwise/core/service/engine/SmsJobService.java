package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.sms.SmsJobMessageVO;
import com.yiwise.core.model.bo.sms.SmsJobMobileOperatorStatsBO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.getui.GeTuiFileInfoBO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.customer.CustomerPersonImportSmsJobByS3UrlRequestVO;
import com.yiwise.core.model.vo.sms.*;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.lcs.api.enums.SmsTypeEnum;

import java.time.*;
import java.util.*;

public interface SmsJobService extends BasicService<SmsJobPO> {

    LocalTime SMS_DEFAULT_START_TIME = LocalTime.of(9, 0);
    LocalTime SMS_DEFAULT_END_TIME = LocalTime.of(20, 0);

    /**
     * 创建任务
     */
    void createJob(SmsJobCreateVo smsJobPO);

    /**
     * 更新任务
     */
    void updateJob(SmsJobCreateVo smsJobPO);

    /**
     * 任务列表
     */
    PageResultObject<SmsJobListInfoVO> getSmsJobListInfo(SmsJobQueryVO smsJobQueryVO);

    /**
     * 获取任务详情
     */
    SmsJobDetailInfoVO getSmsJobInfo(Long tenantId, Long smsJobId);

    /**
     * 获取任务的短信发送记录
     */
    PageResultObject<SmsJobMessageVO> getSmsJobMessageByJobId(Long tenantId, Long smsTemplateId, List<Long> smsJobIds, SendMessageStatusEnum sendStatus, String customerPersonName, String phoneNumber, LocalDateTime createBeginTime, LocalDateTime createEndTime, Integer pageNum, Integer pageSize, String reportStatus,Long costCount,SmsTypeEnum smsType);

    int getSmsJobMessageCountByJobId(Long tenantId, Long smsTemplateId, List<Long> smsJobIds, SendMessageStatusEnum sendStatus, String customerPersonName, String phoneNumber, LocalDateTime createBeginTime, LocalDateTime createEndTime, String reportStatus,Long costCount,SmsTypeEnum smsType);

    /**
     * 运行群发任务
     */
    void execute(Long tenantId, Long smsJobId, JobOperationEnum operation, Long userId);

    /**
     * 定时更新可运行的任务
     */
    void checkAndUpdateSmsJobStatus();

    void deleteRobotCallJob(Long smsJobId, Long tenantId, Long userId);

    /**
     * 获取可用的短信任务
     */
    SmsJobPO getSmsJobAvailableAndLock();

    /**
     * 添加用户至短信任务
     */
    Integer addCustomerPersonToSmsJob(AccountVO accountVO, Long smsJobId, Long tenantId, Long currentUserId, Map<String, String> properties);

    void incrementJobStatusAfterSend(Long tenantId, Long smsJobId, SendMessageStatusEnum sendMessageStatus, Long value);

    void incrementJobStatusAfterImport(Long tenantId, Long smsJobId, Long value);

    void incrementJobStatusAfterRecv(Long tenantId, Long smsJobId, SendMessageStatusEnum sendMessageStatus, Long value);

    /**
     * 获取导入的模板
     */
    String generateSmsJobUploadTemplateExcel(Long tenantId, Long smsJobId);

    /**
     * 导入用户
     */
    JobStartResultVO importSmsJobPhoneNumber(Long tenantId, Long currentUserId, String objectName, Long smsJobId, Boolean distinct, SystemEnum systemEnum);

    /**
     * 重置任务状态
     */
    void resetSmsJobStatusWhenCallTaskAddToCallJob(SmsJobPO smsJobPO);

    /**
     * 未发送列表
     */
    PageResultObject<SmsJobTaskPO> getNotSendMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize);

    /**
     * 已发送列表
     */
    PageResultObject<SmsJobMessagePO> getSentMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize);

    void updateSmsJobDateStatsWithMap(Long tenantId, Long distributorId, TenantPayTypeEnum tenantPayType, Long smsJobId, SmsTypeEnum smsType, Long userId, LocalDate localDate, Map<String, Long> map);

    /**
     * 搜索短信任务
     */
    PageResultObject<SmsJobListInfoVO> searchSmsJob(SmsJobQueryVO smsJobQueryVO);

    JobStartResultVO export(SmsRecordQueryVO smsRecordQueryVO);

    void updateSmsOperatorStatsWithMap(Long callJobId, Map<String, Long> map);

    void updateSmsOperatorStats(Long smsJobId, String key, Long value);

    SmsJobMobileOperatorStatsBO getSmsJobOperatorStats(Long smsJobId);

    PageResultObject<SmsJobMessagePO> getAllSentMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize);

    /**
     * 记录短信发送数据
     */
    void addSmsInterceptData(Long tenantId, Long mainBrandId, String phoneNumber);

    /**
     * 获取短信周期发送次数
     */
    Integer getSmsInterceptCount(Long tenantId, Long mainBrandId, String phoneNumber, LocalDate startDate, LocalDate endDate);

    /**
     * 检查是否可以运行
     */
    LocalDateTime checkStartTime(LocalDateTime time, Long tenantId, SmsJobPO smsJob);

    /**
     * 是否跳过风控检查
     */
    Boolean skipFilter(Long tenantId, Long rcsId, Set<Long> brandIds, Set<Long> customerIds);

    /**
     * 获取运行的截止时间
     */
    LocalTime getEndTime(Long tenantId, Long rcsId);

    /**
     * 获取短信任务下拉列表
     */
    List<SmsJobSimpleVO> selectSmsJobLimited(Long tenantId, String name);

    /**
     * 删除未呼列表
     */
    void deleteSmsJobTask(SmsJobTaskDeleteVO smsJobTaskDeleteVO);

    /**
     * 超过90天没有外呼的任务状态变更为"已归档"
     */
    void archive();

    /**
     * 删除已归档任务的task记录
     */
    void cleanTaskByArchivedJob();

    /**
     * 查询风控时间段
     */
    Map<String, LocalTime> getFilterTime(Long tenantId);

    /**
     * 根据id查询数据
     */
    List<SmsJobMessageVO> getListByCondition(List<Long> idList);

    /**
     * 导入到短信任务
     */
    JobStartResultVO importToSmsJob(SmsRecordQueryVO smsRecordQueryVO, Long tenantId, Long userId);

    void addSmsTypeStats(Map<String, Long> map, SmsTypeEnum smsType, Long count, Long cost);

    JobStartResultVO importSmsJobPhoneNumberByS3Url(GeTuiFileInfoBO fileInfo, CustomerPersonImportSmsJobByS3UrlRequestVO requestVO);
}
