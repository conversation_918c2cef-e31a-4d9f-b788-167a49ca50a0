package com.yiwise.core.service.ant;

import com.yiwise.core.dal.entity.IsvInfoPO;
import com.yiwise.core.model.vo.openapi.ant.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * @author: wuxian<PERSON><EMAIL>
 * @date: 2023 10 17 16:53
 */
public interface AntFinancialService {

    GetJobDetailAntResponseVO getJobDetailForAnt(GetJobDetailAntRequestVO requestVO);

    List<GetTaskInfoDetailAntResponseVO> getTaskInfoDetailAnt(GetTaskInfoDetailAntRequestVO requestVO);

    HashMap<String, Object> dealAntSmsApi(SmsAntRequestVO requestVO, IsvInfoPO isvInfoPO, HttpServletRequest request);

    HashMap<String, Object> querySmsTemplatePage(SmsAntRequestVO requestVO, IsvInfoPO isvInfoPO);

    HashMap<String, Object> sendBatchSms(SmsAntRequestVO requestVO, IsvInfoPO isvInfoPO);

    SmsAntBatchResultVO batchQuerySmsTemplate(SmsAntBatchQueryRequestVO requestVO, IsvInfoPO isvInfoPO);

    /**
     * 蚂蚁蚁盾-分页查询短信模板
     * @param requestVO
     * @param isvInfoPO
     * @return
     */
    HashMap<String, Object> querySmsTemplatePageV2(SmsAntRequestV2VO requestVO, IsvInfoPO isvInfoPO);


    /**
     * 蚂蚁蚁盾-短信模板批量查询接口
     * @param requestVO
     * @param isvInfoPO
     * @return
     */
    SmsAntBatchResultVO batchQuerySmsTemplateV2(SmsAntBatchQueryRequestVO requestVO, IsvInfoPO isvInfoPO);

    /**
     * 蚂蚁蚁盾-短信发送
     * @param requestVO
     * @param isvInfoPO
     * @return
     */
    HashMap<String, Object> sendBatchSmsV2(SmsAntRequestVO requestVO, IsvInfoPO isvInfoPO);



}
