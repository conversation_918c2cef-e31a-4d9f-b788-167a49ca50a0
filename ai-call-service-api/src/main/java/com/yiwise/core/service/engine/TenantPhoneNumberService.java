package com.yiwise.core.service.engine;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.phonenumber.*;
import com.yiwise.core.model.dto.*;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.request.MaCallOutResultRequest;
import com.yiwise.core.model.vo.financestats.MaCallCostStatisticsResponseVO;
import com.yiwise.core.model.vo.openapi.PhoneVo;
import com.yiwise.core.model.vo.phonenumber.*;
import com.yiwise.core.model.vo.recharge.LineAccountTransferVO;
import com.yiwise.lcs.api.dto.LineDTO;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2018/7/24
 **/
public interface TenantPhoneNumberService extends BasicService<TenantPhoneNumberPO> {

    TenantPhoneNumberWithPhoneInfoPO selectInfoByKey(Long key);

    TenantPhoneNumberPO selectByKey(Long tenantPhoneNumberId);

    /**
     * @param tenantId 租户id
     * @param searchWords 搜索关键词
     * @return 根据phoneType分类的list
     */
    Map<PhoneTypeEnum, List<TenantPhoneNumberWithPhoneInfoPO>> selectTenantPhoneNumberMapByTenantId(Long tenantId, String searchWords);

    /**
     * 新建任务线路选择（自有线路、线路余额大于0，或者不计费的线路）
     *
     * @param tenantId    租户id
     * @param searchWords 搜索关键词
     * @return map
     */
    Map<PhoneTypeEnum, List<TenantPhoneNumberInfoBO>> selectActiveTenantPhoneNumberMapByTenantId(Long tenantId, String searchWords, Boolean supportCallOut, Boolean supportCallIn, Boolean supportCsSeat, Boolean supportDirectCall);

    /**
     * 获取租户所有线路 带有是否自有线路 包含已解绑
     *
     * @param tenantId    租户id
     * @param searchWords 查询关键词
     * @return list
     */
    List<TenantPhoneNumberInfoBO> selectAllTenantPhoneNumberInfoListByTenantId(Long tenantId, String searchWords, Integer enableStatus);

    /**
     * 获取租户所有线路 带有是否自有线路
     */
    List<TenantPhoneNumberInfoBO> selectTenantPhoneNumberInfoListByTenantId(Long tenantId);

    /**
     * 查询租户所有线路 带有是否自有线路
     *
     * @param tenantId    租户id
     * @param searchWords 搜索关键词
     * @return list
     */
    List<TenantPhoneNumberInfoBO> selectTenantPhoneNumberInfoListByTenantId(Long tenantId, String searchWords, Integer enabledStatus);

    TenantPhoneNumberInfoBO selectByNamePrecise(Long tenantId, String searchWords);

	/**
	 * 用tenantId集合查询po对象, 不含其他条件
	 */
	List<TenantPhoneNumberPO> selectOnlyByTenantIds(Collection<Long> tenantIds);

    /**
     * 获取该租户下的所有线路
     */
    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantId(Long tenantId);

    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantIds(List<Long> tenantIds);

    /**
     * 搜索该租户下的所有线路, 含limit
     *
     * @param tenantId    租户id
     * @param searchWords 搜索的关键词
     * @return 线路列表
     */
    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantId(Long tenantId, String searchWords);

    /**
     * 获取传入phoneNumberIdSet中属于该租户的phoneNumber列表
     *
     * @param tenantId         租户id
     * @param phoneNumberIdSet 传入的phoneNumberId列表
     * @return phoneNumberIdSet中属于该租户的phoneNumber列表
     */
    List<TenantPhoneNumberPO> getRobotCallJobPhoneNumberList(Long tenantId, List<Long> phoneNumberIdSet);

    void updateTenantPhoneNumberConcurrency(Long tenantId, Long phoneNumberId, Integer concurrency);

    /**
     * 更新线路信息
     */
    void modifyTenantPoneNumber(PhoneNumberCrmUpdateVO updateVO, Long tenantId);

    /**
     * 根据PhoneNumberId列表获取对象
     */
    TenantPhoneNumberPO selectOneByPhoneNumberIdList(Long phoneNumberId);

    List<TenantPhoneNumberWithPhoneInfoPO> selectByPhoneNumberIdList(Collection<Long> phoneNumberIdList, Collection<Long> tenantIds);

    List<TenantPhoneNumberWithPhoneInfoPO> selectByPhoneNumberIdListHasOwnerName(Collection<Long> phoneNumberIdList, Collection<Long> tenantIds);

    int selectCountByPhoneNumberIdList(Collection<Long> phoneNumberIdList);

    /**
     * BOSS里给客户tenant绑定一条线路
     */
    void newBindTenantLine(BindOnePhoneNumberVO bindOnePhoneNumberVO, Long upDistributorId);
    /**
     * 客户修改线路
     */
    void updateCustomerPhoneNumber(UpdateBindOnePhoneNumberVO updateBindOnePhoneNumberVO, Long distributorId);

    /**
     * BOSS里更新一条线路tenant的绑定
     */
    void newUpdateCustomerPhoneNumber(UpdateBindOnePhoneNumberVO updateBindOnePhoneNumberVO, Long upDistributorId);

    /**
     * 删除(修改电话卡在使用)
     */
    void deleteTenantPhoneNumberById(Long tenantPhoneNumberId);

    /**
     * 删除
     */
    void deleteBindOneLine(DeleteBindOnePhoneNumberVO deleteBindOnePhoneNumberVO);

    /**
     * 添加网关的绑定信息到tenantPhoneNumber表，会查询是否有旧有的数据，然后直接改旧数据为enable
     */
    void addGateWayLine(List<PhoneNumberPO> phoneNumberPOS);

    void addGateWayOwnerTenant(List<PhoneNumberPO> phoneNumberPOS, Long ownerTenantId);

    void updateMobileOwner(Long oldTenantId, List<Long> phoneNumberIds, Long newTenantId);

    void deleteByTenantAndPhoneNumber(Long tenantId, Collection<Long> phoneNumberIds);
    void disableByPhoneNumberIds(Set<Long> phoneNumberIds);

    Set<Long> selectByTenantPhoneNumberIds(Long tenantId,Collection<Long> phoneNumberIds);

    List<BindLineDTO> selectTenantBindLineList(@Param("tenantId") Long tenantId);

    BindLineDTO getTenantBindLineUpdateInfo(Long tenantPhoneNumberId, Long upDistributorId);

    void addOpeCustomerBindLine(BindOnePhoneNumberVO bindOnePhoneNumberVO);

    void addOpeCustomerBindLineToDistributorCustomer(BindOnePhoneNumberVO bindOnePhoneNumberVO);

    /**
     * 给客户绑定分机号线路
     */
    void doExtensionBindLine(BindOnePhoneNumberVO bindOnePhoneNumberVO);

    void updateOpeCustomerBindLine(UpdateBindOnePhoneNumberVO updateBindOnePhoneNumberVO);

    void updateBindOneLineToDistributorCustomer(UpdateBindOnePhoneNumberVO updateBindOnePhoneNumberVO);

    /**
     * 余额转移
     */
    void transferLineMoney(LineAccountTransferVO lineAccountTransferVO, Long tenantId, Long userId);

    /**
     * ai外呼扣除电话费用
     */
    void reduceCallCostByTenant(TenantPO tenant, RobotCallPhoneNumberWithSipInfoBO sipInfo, Long cost);

    void reduceMaCallCostByTenant(TenantPO tenant, LineDTO lineDTO, Long cost, TenantPhoneNumberPO tenantPhoneNumberPO);

    /**
     * 扣除电话费用
     */
    void reduceCallCost(Long tenantPhoneNumberId, Long cost);

    /**
     * 账户是否欠费
     *
     * @param calculateZero       包含0表示，0也表示欠费
     */
    boolean isAccountDebt(Long tenantPhoneNumberId, boolean calculateZero);
    boolean isAccountDebt(TenantPhoneNumberPO tenantPhoneNumber, boolean calculateZero);

    /**
     * 账户是否欠费
     *
     * @param calculateZero 包含0表示，0也表示欠费
     */
    boolean isAccountDebt(Long tenantId, Long accountFare, Long tenantPhoneNumberId, boolean calculateZero);

    /**
     * 获取归属自己的线路
     *
     * @param tenantId    租户id
     * @param searchWords 查询关键词
     * @return list
     */
    List<TenantPhoneNumberDTO> selectOwnerByTenantId(Long tenantId, String searchWords);

    /**
     * 获取绑定给自己的线路
     */
    List<TenantPhoneNumberDTO> selectBindByTenantId(Long tenantId, String searchWords);


    TenantPhoneNumberPO selectOneByTenantIdAndPhoneNumberId(Long tenantId, Long dtoPhoneNumberId);

    List<TenantPhoneNumberPO> selectTenantPhoneNumberByIds(Collection<Long> tenantPhoneNumberIds);

    /**
     * 功能与 basicService 的 selectMapWithPhoneByKeyCollect 相同，返回对象包括 phoneNumber 表的信息
     *
     * @param tenantPhoneNumberIds 主键列表
     * @return 缓存映射
     */
    Map<? extends Serializable, TenantPhoneNumberWithPhoneInfoPO> selectMapWithPhoneByKeyCollect(Collection<Long> tenantPhoneNumberIds);

    /**
     * 查询手机线路绑定的租户线路信息
     *
     * @param phoneNumberId 线路id
     */
    TenantPhoneNumberPO selectMobileTenantPhoneNumberByPhoneNumberId(Long phoneNumberId);

    /**
     * 查询手机线路绑定的租户线路信息
     *
     * @param phoneNumberIds 线路ids
     */
    List<TenantPhoneNumberPO> selectMobileTenantPhoneNumberByPhoneNumberIds(List<Long> phoneNumberIds);

    /**
     * 获取租户所有线路 带有是否自有线路，并且计算接听总量/外呼总量 ，以客户维度计算 包含已解绑线路
     *
     * @param tenantId    租户id
     * @param searchWords 查询关键词
     * @return list
     */
    List<TenantPhoneNumberInfoBO> getAllTenantPhoneNumberInfoListByTenantId(Long tenantId, String searchWords, Integer enableStatus, Boolean attachSmsAccount, SystemEnum systemType);

    /**
     * 获取租户所有线路 带有是否自有线路，并且计算接听总量/外呼总量 ，以客户维度计算
     *
     * @param tenantId    租户id
     * @param searchWords 搜索关键词
     * @return list
     */
    List<TenantPhoneNumberInfoBO> getTenantPhoneNumberInfoListByTenantId(Long tenantId, String searchWords);


    /**
     * 功能与 basicService 的 selectMapWithPhoneByKeyCollect 相同，返回对象包括 phoneNumber 表的信息, 包括失效，解绑的数据
     *
     * @param tenantPhoneNumberIds 主键列表
     * @return 缓存映射
     */
    Map<? extends Serializable, TenantPhoneNumberWithPhoneInfoPO> selectMapWithPhoneByKeyCollectAll(ArrayList<Long> tenantPhoneNumberIds);

    /**
     * 获取该租户下的所有线路,包含解绑
     */
    List<TenantPhoneNumberWithPhoneInfoPO> selectTenantPhoneNumberAllByTenantId(Long tenantId);

    List<TenantPhoneNumberWithPhoneInfoPO> selectTenantPhoneNumberAllByTenantIds(List<Long> tenantIds);

    List<TenantPhoneNumberWithPhoneInfoPO> selectByPhoneNumberIdListAll(Collection<Long> phoneNumberIdList);

    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantPhoneNumberIdListAll(Collection<Long> phoneNumberIdList, Long tenantId);

    void batchReduceMonthlyBillRage(List<Long> tenantPhoneNumberIds);

    List<TenantPhoneNumberWithPhoneInfoPO> selectSupportCallInListByTenantId(Long tenantId, String searchWord);

    List<TenantPhoneNumberWithPhoneInfoPO> selectSupportCallInListWithoutAlreadyBind2ReceptionByTenantId(Long tenantId, Long callInReceptionId, String searchWord);

    /**
     * 外呼策略组使用的 获取客户线路列表信息
     */
    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantIdAndPhoneLocationForCallPolicyGroup(Long tenantId, String searchPhoneNumber, String province, String city);
    List<TenantPhoneNumberWithPhoneInfoPO> selectByTenantIdAndPhoneLocationNewForCallPolicyGroup(Long tenantId, String searchPhoneNumber, String province, String city,Boolean supportCallIn,Boolean supportCallOut,Boolean supportCsSeat);

    TenantPhoneNumberBO getTenantPhoneNumberInfo(Long tenantPhoneNumberId);

    List<TenantPhoneNumberPO> selectTenantPhoneNumberByCollection(List<Long> phoneNumberIds, List<Long> tenantIds);

    List<TenantPhoneNumberPO> selectTenantPhoneNumberByDistributorId(List<Long> phoneNumberIds, Long distributorId);

    List<TenantPhoneNumberPO> selectTenantPhoneNumberByDistributorIds(List<Long> phoneNumberIds, List<Long> distributorIds);

    List<TenantPhoneNumberPO> selectByPhoneNumberId(Long phoneNumberId);

    /**
     * 根据两个条件计算绑定的并发数量 获取列表中的并发，会忽略逻辑删除的和并发无限制的
     */
    List<PhoneNumberConcurrenceBO> countBindDistributorTenantConcurrence(Collection<Long> phoneNumberIds, Long distributorId);

    int selectCountByPhoneNumberIdAndDistributorId(Collection<Long> phoneNumberIdList, Long distributorId);

    void updateLastHeartBeatTime(Collection<Long> phoneNumberIdList);

    int updateConcurrency(Long tenantPhoneNumberId, Integer diff);

    /**
     * 检查线路并发是否有余
     * @param tenantPhoneNumberId 线路id
     * @param selfExcept 是否正在被当前channel使用
     */
    boolean checkConcurrencyAvailable(Long tenantPhoneNumberId, boolean selfExcept);

    void resetConcurrency();

    /**
     * 修改客户自己线路的信息
     */
    void updatePhoneInfoByTenantPhoneNumberId(Long tenantId, PhoneVo phoneVo);

    /**
     * 修改绑定客户线路的价格
     */
    void updatePhonePriceByTenantPhoneNumberId(PhoneVo phoneVo);

    void batchReduceCallOutMonthlyBillRage(TenantPhoneNumberWithPhoneInfoPO tenantPhoneNumberWithPhoneInfoPO);

    Map<Number, Number> getConcurrencyMap(Collection<Long> phoneNumberIds, List<Long> distributorIds);

    List<TenantConcurrencyBO> getConcurrencyMapByDistributorIds(Long phoneNumberId, Collection<Long> distributorIds);

    void updateAccountFareById(Long tenantPhoneNumberId,Long autoPayFare);

    List<Long> phoneNumberCurrentConcurrenceQuotaIdsForOpe(Integer code);

    boolean checkTenantPhoneNumberStopped(Long tenantPhoneNumberId);
    boolean checkTenantPhoneNumberStopped(TenantPhoneNumberPO tenantPhoneNumberPO);

    void updateStopStatus(Long tenantPhoneNumberId, boolean stopStatus);

    String getAccountFareByTenantId(Long tenantId);

    @Override
    List<TenantPhoneNumberPO> selectListByKeyCollect(Collection<? extends Number> keyCollect);

    Boolean calculateExpensesAndDeduct(MaCallOutResultRequest request);

    void maCallOutStatistics(MaCallOutResultRequest request,Long billRate ,Long callCost, Long period,String payType);
    /**
     * 获取客户绑定的分机号线路
     */
    TenantExtensionPhoneNumberDTO getExtensionPhoneNumberByTenantId(Long tenantId);

    /**
     * 获取客户绑定的分机号线路信息，外呼时使用
     */
    RobotCallPhoneNumberWithSipInfoBO getTenantExtensionPhoneNumber(@Param("tenantId") Long tenantId);

    List<Long> selectByTenantIdCustomerTrackType(Long tenantId, Integer customerTrackType);

    List<Long> selectTenantPhoneNumberIdsByTenantId(Long tenantId);

    List<MaCallCostStatisticsResponseVO> getMaCallCostStatistics(MaCallOutResultRequest request);

    Long computeCallInCost(Long tenantId, Long phoneNumberId, String calledPhoneNumber, Long chatDuration);

    List<MaCallCostStatisticsResponseVO> getBillingChatDuration(MaCallOutResultRequest request);

    /**
     * 获取所有带linePrefix的绑定关系，用于存储快照信息
     */
    List<TenantPhoneNumberSnapshotBO> getAllWithLinePrefix();

    /**
     * 获取所有被绑定一次以上的线路
     */
    List<TenantPhoneNumberCountBO> getTenantPhoneNumberBindCount();

    /**
     * 查询所有需要扣月租的线路
     * @return
     */
    List<TenantPhoneNumberPO> getAllNeedMonthlyCost();

    List<TenantPhoneNumberPO> selectListByTenantPhoneNumberIds(List<Long> tenantPhoneNumberIds);

    List<TenantPhoneNumberBO> getJobPhoneNumberListByTenantPhoneNumberIds(Long tenantId, List<Long> tenantPhoneNumberIds);
}
