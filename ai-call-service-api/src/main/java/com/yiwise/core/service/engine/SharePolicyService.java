package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.dal.entity.SharePolicyPO;
import com.yiwise.core.model.enums.CreateTypeEnum;
import com.yiwise.core.model.enums.PolicyStatusEnum;
import com.yiwise.core.model.enums.WhiteListJoinStatusEnum;
import com.yiwise.base.service.BasicService;

import java.util.Set;


/**
 * 共享黑名单策略
 */
public interface SharePolicyService extends BasicService<SharePolicyPO> {

    /**
     * 获取共享组,distributorId用于区分ope与boss的数据范围     *
     *
     * @param pageNum         页码
     * @param pageSize        一页大小
     * @param sharePolicyName 组名
     * @param tenantId        客户id
     * @param createType      类型
     * @param distributorId   代理商id
     * @param createUserId    操作人id
     * @return 结果
     */
    PageResultObject getPolicyList(Integer pageNum, Integer pageSize, String sharePolicyName, Long tenantId, CreateTypeEnum createType, Long distributorId, Long createUserId);

    PageResultObject selectPolicyList(Integer pageNum, Integer pageSize, String sharePolicyName, CreateTypeEnum createType, WhiteListJoinStatusEnum joinStatus, Long tenantId, Long distributorId,Long createUserId);

    void addSharePolicy(SharePolicyPO sharePolicyPO, CreateTypeEnum createType, Long distributorId, Long createUserId);

    void updateSharePolicy(SharePolicyPO sharePolicyPO);

    void startOrBlock(PolicyStatusEnum policyStatus, Long sharePolicyId);

    void deleteSharePolicy(Long sharePolicyId);

    Set<SharePolicyPO> selectByPolicyIdsAndEnabled(Set<Long> policyIdSet);

    Set<SharePolicyPO> getPolicyListByTenantId(Long tenantId);
}
