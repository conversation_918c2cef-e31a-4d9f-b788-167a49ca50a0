package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.IntentMessagePO;
import com.yiwise.core.model.bo.intentmessage.ReportStatusCountBO;
import com.yiwise.core.model.bo.intentmessage.SendStatusAndCountBO;
import com.yiwise.core.model.dto.IntentMessageStatusCountDTO;
import com.yiwise.core.model.enums.SendMessageStatusEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sms.*;
import com.yiwise.lcs.api.enums.SmsTypeEnum;
import com.yiwise.core.model.vo.sms.DouDianSmsCallBack;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

public interface IntentMessageService extends BasicService<IntentMessagePO> {

    /**
     * 获取意向短信列表
     *
     * @param tenantId           租户id
     * @param customerPersonName 客户姓名
     * @param phoneNumber        客户电话号码
     * @param robotCallJobId     任务Id
     */
    PageResultObject<IntentMessageVO> getIntentMessageList(Long tenantId, Long smsTemplateId, String customerPersonName, String phoneNumber, LocalDate createBeginTime, LocalDate createEndTime, Long robotCallJobId, SendMessageStatusEnum sendMessageStatus, Integer pageNum, Integer pageSize, String reportStatus, List<Long> robotJobIds, Long recordId, LocalDateTime startTime, LocalDateTime endTime, SmsTypeEnum smsType, Long costCount);

    Integer getIntentMessageCount(Long tenantId, Long smsTemplateId, String customerPersonName, String phoneNumber, LocalDate createBeginTime, LocalDate createEndTime, Long robotCallJobId, SendMessageStatusEnum sendMessageStatus, Integer pageNum, Integer pageSize, String reportStatus, List<Long> robotCallJobIds,Long recordId,LocalDateTime startTime,LocalDateTime endTime,SmsTypeEnum smsType,Long costCount);

    void setIntentMessageSendStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String sid, IntentMessagePO intentMessagePO, Boolean virtual);

    void setIntentMessageSendStatusWithMsg(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual);

    /**
     * 默认设置ReportStatus为"PENDING",其他同 setIntentMessageSendStatusWithMsg
     */
    void setIntentMessageSendStatusWithMsgAndReportStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual);

    void setIntentMessageSendStatusWithMsgAndSuccessStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual);

    /**
     * 按callRecordId列表查询 CallRecordId -> List<IntentMessagePO> 的map
     *
     * @param callRecordIdList 通话记录id列表
     * @return CallRecordId -> IntentMessagePO 的map
     */
    Map<Long, List<IntentMessagePO>> selectMapByCallRecordIdList(List<Long> callRecordIdList);

    /**
     * 批量更新发送状态
     * @param intentMessageIdSet 意向短信Id集合
     * @param sendStatus 发送状态
     */
    void batchUpdateSendStatus(Set<Long> intentMessageIdSet, SendMessageStatusEnum sendStatus, String sendErrorMsg);

    /**
     * 批量更新发送时间
     * @param intentMessageIdSet 意向短信Id集合
     * @param createTime 创建时间
     */
    void batchUpdateSendTime(Set<Long> intentMessageIdSet, LocalDateTime createTime);

    /**
     * 单个发送短信是更新统计信息
     */
    void updateIntentMessageStatusCount(Long tenantId, Long robotCallJobId, SendMessageStatusEnum sendStatus,
                                        SendMessageStatusEnum preSendStatus, Boolean virtual,String reportStatus,
                                        LocalDateTime createTime);

    Boolean judgeVirtualTemplateType(Long robotCallJobId,Long tenantId);

    /**
     * 批量发送短信是更统计信息
     */
    void updateIntentMessageStatusCount(Set<Long> intentMessageIdSet, SendMessageStatusEnum sendStatus);

    List<SendStatusAndCountBO> getStatusCount(Collection<Long> intentMessageIdSet);

    List<SendStatusAndCountBO> getMessageStatusCount(Long tenantId, Long robotCallJobId);

    List<SendStatusAndCountBO> getMessageStatusCountCache(Long tenantId, Long robotCallJobId);

	Map<Long, List<SendStatusAndCountBO>> getMessageStatusCountCache(Collection<Long> robotCallJobIds);

    void calcAllIntentMessageStatusCount(Long tenantId, Long robotCallJobId);

    JobStartResultVO export(IntentMessageRecordVO intentMessageRecordVO);

    IntentMessagePO getIntentMessageBySid(String sid);

    List<IntentMessagePO> selectIntentMessageByJobInfo(Long tenantId, Long robotCallJobId, List<Long> callRecordIdList);

    List<IntentMessagePO> selectIntentMessageByJobInfo(Long tenantId, Long robotCallJobId, String phoneNumber);

    /**
     * 短信发送成功客户数
     */
    Long countSendSuccess(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate);

    /**
     * 推送短信的客户总数
     */
    Long countSend(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate);

    /**
     * 短信接收成功客户数
     */
    Long countSmsAcceptSuccess(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate);

    Long countReceiveSuccessByLocalDate(Long tenantId, Long robotCallJobId, Collection<Long> smsTemplateIds, LocalDate localDate);

    /**
     * 根据id列表查询短信历史
     */
    List<IntentMessagePO> getIntentMessageListByIds(List<Long> idList);

    /**
     * 导入到短信任务
     */
    JobStartResultVO importToSmsJob(IntentMessageRecordVO intentMessageRecordVO, Long tenantId, Long userId);


    Long selectCountByJobAndCallRecord(Long tenantId, Long robotCallJobId, Long callRecordId);

    /**
     * 短信重发
     */
    JobStartResultVO reSendSms(JobReSendSmsVO jobReSendSmsVO, Long tenantId, Long userId);

    /**
     * 检查任务是否有正在执行中的补发任务
     */
    Boolean checkReSendProcess(Long robotCallJobId);

    Integer getReportSuccessRecordCountByJob(Long tenantId,Long robotCallJobId, Long callRecordId);

    IntentMessagePO getLastIntentMessageByRecordId(Long recordId);

    List<IntentMessagePO> selectAllIntentMessageByTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime);

	List<ReportStatusCountBO> countByReportStatus(Long tenantId, LocalDate startDate, LocalDate endDate);

	/**
	 * 遍历开始使用
	 */
	Long selectIntentMessageIdAfterLocalDateTime(LocalDateTime localDateTime);
	Long selectIntentMessageIdBeforeLocalDateTime(LocalDateTime localDateTime);

	/**
	 * 遍历使用
	 */
	List<IntentMessagePO> selectOrderByIntentMessageId(Long intentMessageId, Integer batchSize, Long lastIntentMessageId);

    Integer countTemplateIdAndCallRecordId(Long tenantId, Long smsTemplateId, Long callRecordId, Long robotCallJobId);

	/**
	 * 如果不需要按时间维度筛选的话直接查询归档好的IntentMessageStatPO
	 */
	Map<Long, IntentMessageStatusCountDTO> countByJobAndStatus(Collection<Long> robotCallJobIds, @NotNull LocalDateTime startTime, @NotNull LocalDateTime endTime);

    void incDouyinStatisData(DouDianSmsCallBack douDianSmsCallBack);
}
