package com.yiwise.core.service.thirdparty;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FeishuUserService {

	void dailyCheck();

	Collection<Department> departments();

	Collection<User> users(Collection<String> openDepartmentIds);

	@Data
	class Department {
		private String name;

		@JsonAlias("department_id")
		private String departmentId;

		@JsonAlias("open_department_id")
		private String openDepartmentId;
	}

	@Data
	class User {
		@JsonAlias("open_id")
		private String openId;

		@JsonAlias("user_id")
		private String userId;

		private String name;

		private String mobile;

		@JsonAlias("department_ids")
		private List<String> departmentIds;

		@JsonAlias("join_time")
		private Long joinTime;
	}
}
