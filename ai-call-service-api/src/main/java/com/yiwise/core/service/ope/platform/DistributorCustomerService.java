package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.enums.RechargePrestoreStreamFunEnum;
import com.yiwise.core.model.enums.TenantAiccPartEnum;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.AuditStatusEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DistributorCustomerQueryVO;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerFunctionInfo;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.tenant.TenantIdAndNamePairVO;

import java.time.LocalDate;
import java.util.List;

public interface DistributorCustomerService extends BasicService<TenantPO> {

    /**
     * 添加经销商客户
     */
    String addDistributorCustomer(CustomerInsertVO customerInsertVO);

    /**
     * 更新经销商客户
     */
    void updateDistributorCustomer(TenantPO tenantPO);

    List<TenantIdAndNamePairVO> getTenantIdAndNamePairList(Long distributorId, String searchName);

    /**
     * 分销商客户导出 设置分销商名称
     */
    void resolveDistributorName(List<? extends DirectAndDistributorCustomerListVO> tenantPOList);

    String addBossDistributorCustomer(CustomerInsertVO customerInsertVO, Long distributorId);

    /**
     * 二级代理商创建客户时通往审核的service
     */
    void addSubBossDistributorCustomer(TenantPO tenantPO);

    /**
     * 审核客户
     */
    String auditSubDistributorCustomer(Boolean result, String auditNote, Long tenantId, Long userId);


    /**
     * 编辑经销商客户
     */
    void updateSubBossDistributorCustomer(TenantPO tenantPO);

    JobStartResultVO exportListAllDistributorCustomer(DistributorCustomerQueryVO queryVO);

    JobStartResultVO exportListAllSubDistributorCustomer(DistributorCustomerQueryVO queryVO);
    /**
     * 获取待审核的客户列表
     */
    PageResultObject queryCustomerWaitingForAuditing(Integer pageNum, Integer pageSize, Long distributorId, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager, Long createUserId);

    /**
     * 二级代理商看的自己审核的分销商客户列表
     */
    PageResultObject queryForBeingAudited(Integer pageNum, Integer pageSize, Long distributorId, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager);



    /**
     * OPE代理商客户延期
     */
    void opeAndBossDistributorCustomerDelay(Long id, LocalDate newEndTime, Long tenantId, Long userId, TenantAiccPartEnum tenantAiccPartEnum);

    Double calculateDelayCost(Double objectCount, Long distributorId, Long tenantId , LocalDate newEndTime, Long robotId);

    void updateOpeDistributorCustomer(CustomerUpdateVO customerUpdateVO);

    /**
     * 新增代理商用户的时候，计算AI 座席数 费用
     */
    Double calculateAiCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime,Boolean isAddDay);
    /**
     * 代理商用户语音坐席延期 计算费用
     */
    Double calculateCsConsoleCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime,Boolean isAddDay);
    /**
     * 代理商用户开通功能时候计算费用
     */
    Double calculateFunctionCost(Double objectCount, Long distributorId, Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum,Boolean isAddDay,LocalDate startTime,LocalDate endTime);

    List<DistributorCustomerFunctionInfo> getFunctionInfo(Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum);

    void openFunction(Long distributorId, Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum,Long userId,LocalDate startTime,LocalDate endTime);


    double calculateFunctionDelayCost(Double objectCount, Long distributorId, Long tenantId, RechargePrestoreStreamFunEnum funTtsCompose, Long robotId, LocalDate newEndTime,Boolean isAddDay);

    Double calculateAgentAndAiAssistantCost(Double count, Long distributorId, LocalDate startTime, LocalDate endTime, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum);

    int getAllAgentCount(Long tenantId);

    Double calculateMultiConcurrencyCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime,Boolean isAddDay);

    PageResultObject listDistributorTotalCustomerForQiyu(DistributorCustomerQueryVO query);

    /**
     * 代理商ai坐席列表明细
     *
     * @param query tenantId
     * @return 结果
     */
    PageResultObject listDistributorDetailCustomerForQiyu(DistributorCustomerQueryVO query);

    JobStartResultVO exportListAllDistributorCustomerForQiyu(DistributorCustomerQueryVO query);

    void infoResult(List<? extends QiyuTenantAiDetailVO> result);

    void appendListAllDistributorCustomer(List<? extends DirectAndDistributorCustomerListVO> items);
}
