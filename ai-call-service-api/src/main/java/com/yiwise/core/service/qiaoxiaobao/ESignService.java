package com.yiwise.core.service.qiaoxiaobao;

import com.yiwise.core.model.bo.esign.FlowInfoBO;
import com.yiwise.core.model.bo.esign.IdentifyBO;
import com.yiwise.core.model.dto.esign.CreateContractDTO;
import com.yiwise.core.model.dto.esign.IdentityInfoDTO;
import com.yiwise.core.model.dto.esign.SignCallbackDataDTO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: zqy
 * @Date: 2020/8/4 11:09
 */
@Service
public interface ESignService {

    /**
     * 创建个人账号
     * @param tenantId 租户id
     * @param name 姓名
     * @param phoneNumber 手机号码
     * @return accountId
     */
    String createUserAccount(Long tenantId, String name, String phoneNumber);

    boolean deleteUserAccount(String accountId, Long tenantId);

    /**
     * 创建组织账号
     * @param tenantId 租户id
     * @param name 名称
     * @param phoneNumber 手机号
     * @return orgId
     */
    String createOrgAccount(Long tenantId, String name, String phoneNumber);

    boolean deleteOrgAccount(String accountId, Long tenantId);

    /**
     * 获取个人实名认证地址
     * @param accountId 个人账号id
     * @return info
     */
    IdentifyBO getUserIdentifyInfo(Long tenantId, String accountId);

    /**
     * 获取组织机构实名认证地址
     * @param accountId 组织账号id
     * @return info
     */
    IdentifyBO getOrgIdentifyInfo(Long tenantId, String accountId);

    /**
     * 查询认证信息
     * @param flowId 认证流程Id
     * @return info
     */
    FlowInfoBO getFlowInfo(String flowId);

    String createSign(CreateContractDTO createContractDTO);

    void signFlowComplete(SignCallbackDataDTO req, HttpServletRequest request);

    /**
     * 查询流程模板详情
     *
     * @param signTemplateId
     * @return
     */
    Pair<String, String> getSignTemplateDetail(String signTemplateId);

    /**
     * 查询机构信息 统一信用代码方式
     * @return
     */
    String getIdentityInfo(String orgIdCardNum);

    /**
     * 查询机构信息 orgId方式
     *
     * @return
     */
    IdentityInfoDTO getIdentityInfoV2(String orgId);

    /**
     * 合同模板授权
     */
    void orgAuth();

}
