package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.calloutjob.api.vo.CallOutRecordSimpleVO;
import com.yiwise.core.dal.entity.CallRecordPO;
import com.yiwise.core.model.bo.callstats.HourlyTaskQuartzBO;
import com.yiwise.core.model.dto.CallRecordCommonDTO;
import com.yiwise.core.model.dto.DialogFlowForCallRecordDTO;
import com.yiwise.core.model.dto.RobotCallJobForCallRecordDTO;
import com.yiwise.core.model.enums.callrecord.CallRecordSourceEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.enums.ope.CallRecordTrackStatusEnum;
import com.yiwise.core.model.po.CallTaskCountPO;
import com.yiwise.core.model.vo.callrecord.CallRecordBossAndOpeQueryVO;
import com.yiwise.core.model.vo.callrecord.CallRecordUpdateDefectVO;
import com.yiwise.core.model.vo.customer.CallRecordDetailVO;
import com.yiwise.core.model.vo.ope.CallRecordVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Wang
 */
public interface CallRecordOpeService extends BasicService<CallRecordPO> {

    PageResultObject<CallRecordVO> queryCallRecord(CallRecordBossAndOpeQueryVO callRecordBossAndOpeQueryVO);

    /**
     * 封装返回前端数据
     * @param callRecordList
     */
    void packageCallRecordList(List<CallRecordVO> callRecordList);

    /**
     * 外包巡检平台查询通话记录
     */
    PageResultObject<CallRecordVO> outsourcingQueryCallRecord(CallRecordBossAndOpeQueryVO query);

    void updateDefect(CallRecordUpdateDefectVO callRecordUpdateDefectVO);

    void updateTrack(Long callRecordId, CallRecordTrackStatusEnum callRecordTrackStatusEnum);

    PageResultObject<CallRecordCommonDTO> getTenantSet(Integer pageNum, Integer pageSize, String searchWords);

    PageResultObject<DialogFlowForCallRecordDTO> getDialogFlowSet(Integer pageNum, Integer pageSize, String searchWords);

    PageResultObject<RobotCallJobForCallRecordDTO> getRobotCallJobNameSet(Integer pageNum, Integer pageSize, Long dialogFlowId, String searchWords);

    List<CallRecordVO> queryCallRecordByIds(List<Long> callRecordIdList);

    void getDailyCallJobCount();

    List<CallTaskCountPO> getCallTaskCount(LocalDate startTime, LocalDate endTime);

    List<HourlyTaskQuartzBO> getCallTaskHourlyCount(LocalDateTime startTime, LocalDateTime endTime);

    List<CallRecordPO> getAllCallRecordByJob(Long tenantId, Long robotCallJobId);

    PageResultObject<CallRecordCommonDTO> getPhoneNumberSet(Integer pageNum, Integer pageSize, String searchWords);

    List<CallRecordPO> selectLastThreeCallRecord(String phoneNumber, Long tenantId);

    /**
     * 获取通话记录详情
     *
     * @param callRecordId
     * @param userId
     * @param callRecordType
     * @return
     */
    CallRecordVO getCallRecordInfo(Long tenantId, Long callRecordId, Long userId, CallRecordTypeEnum callRecordType);

    List<CallRecordPO> getAllCallRecordByTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime callbackStartTime, LocalDateTime callbackEndTime);

    String selectBingJianId(String id);

    /**
     * 外呼记录汇总
     *
     * @param phoneNumber
     * @param callRecordId
     * @return
     */
    List<CallRecordVO> getRecordSummary(String phoneNumber, String callRecordId);

    /**
     * 通话记录详情汇总
     *
     * @param recordId
     * @param tenantId
     * @param sourceEnum
     * @return
     */
    CallRecordDetailVO getCallDetailFromAll(Long recordId,Long tenantId,CallRecordSourceEnum sourceEnum);

    /**
     * 更新阅读状态
     * @param recordId
     * @param tenantId
     * @param sourceType
     */
    void updateReadStatusSummary(Long recordId, Long tenantId, CallRecordSourceEnum sourceType);

    CallOutRecordSimpleVO getSimpleCallRecord(Long tenantId, Long callRecordId, Long userId);
}
