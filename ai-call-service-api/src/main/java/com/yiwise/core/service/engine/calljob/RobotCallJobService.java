package com.yiwise.core.service.engine.calljob;

import com.alibaba.fastjson.JSONArray;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicService;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.callstats.IndexJobVO;
import com.yiwise.core.model.bo.phonenumber.RobotCallPhoneNumberWithSipInfoBO;
import com.yiwise.core.model.bo.robotcalljob.*;
import com.yiwise.core.model.bo.robotcalljob.timeduration.ActiveTimeBO;
import com.yiwise.core.model.dto.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.calloutplan.PlanImportCustomerTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobOrderTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.RobotCallJobTypeEnum;
import com.yiwise.core.model.po.ConcurrencySumPO;
import com.yiwise.core.model.po.SeatConcurrencySumPO;
import com.yiwise.core.model.vo.IdNamePairVO;
import com.yiwise.core.model.vo.calloutplan.CallOutPlanJobQueryVO;
import com.yiwise.core.model.vo.callstat.CallJobFilterStatisOpenConfigVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.robotcalljob.*;
import com.yiwise.core.model.vo.robotcalljob.concurrency.RobotCallJobConcurrencyInfoVO;
import com.yiwise.lcs.api.enums.OwnerTypeEnum;
import javaslang.Tuple3;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 24/07/2018
 */
public interface RobotCallJobService extends BasicService<RobotCallJobPO> {

    /**
     * 创建机器人任务
     * 新增任务创建日志
     *
     * @param robotCallJobCreateVO 任务对象
     * @param isOpenApi            如果是OpenApi 预警消息推送人可为空
     * @modify zqy 返回任务对象
     */
    RobotCallJobPO createRobotCallJob(RobotCallJobCreateVO robotCallJobCreateVO, boolean isOpenApi);
    /**
     * 外呼计划生成任务并校验
     */
    String validateAndInit(RobotCallJobCompatibleVO robotCallJobPO, List<RobotCallJobPO> saveJob, Long userId, Set<String> checkJobNames);
    /**
     * 更新任务心跳， 并重置任务所在机器， 让其他机器可以抢夺
     */
    void updateRobotCallJobLastHeartBeatTimeAndResetJobIpAddressNotUpdateLastModifyTime(Long callJobId);

    /**
     * 获取正在使用的AI坐席数量
     *
     * @return 正在使用的AI坐席数量
     */
    Integer getUsingRobotCount(Long tenantId,Boolean newVersion);

    /**
     * 获取用户正在使用的AI坐席数量
     */
    Integer getUsingRobotCountByUser(Long tenantId, Long userId,Boolean newVersion);

    /**
     * 获取正在使用的AI坐席数量
     *
     * @param exceptRobotCallJobId 不计算在内的任务id
     */
    Integer getUsingRobotCount(Long tenantId, Long exceptRobotCallJobId);

    Integer getUnFinishedCallJobRobotCount(Long tenantId, Collection<Long> userIds, Long exceptRobotCallJobId,Boolean newVersion);

    Integer getAuthUsingCallJobRobotCount(Long tenantId, UserPO userPO, SystemEnum systemEnum);

    /**
     * 获取某个用户除了当前任务外使用的AI坐席数量
     */
    Integer getUsingRobotCountByUser(Long tenantId, Long userId, Long exceptRobotCallJobId,Boolean newVersion);

    /**
     * 条件查询任务列表 包含统计信息
     */
    PageResultObject<RobotCallJobListInfoVO> getRobotCallJobListInfo(RobotCallJobQueryVO robotCallJobQueryVO);

    PageResultObject<RobotCallJobListInfoVO> getSimpleRobotCallJobListInfo(RobotCallJobQueryVO robotCallJobQueryVO);

    /**
     * crm端条件查询任务列表 包含统计信息 包括数据权限
     */
    PageResultObject<RobotCallJobListInfoVO> getRobotCallJobListInfo(RobotCallJobWithUserQueryVO robotCallJobQueryVO);

    List<SimpleCallJobDTO> selectLimitWithSearchWords(String robotCallJobName);

	RobotCallJobListInfoWithFolderVO getRobotCallJobListInfoWithFolder(RobotCallJobWithUserQueryVO robotCallJobQueryVO);

    /**
     * ope监控面板查询任务列表
     */
    PageResultObject<MonitorCustomerInfoVO> getRobotCallJobListForOpeMonitor(MonitorCustomerQueryVO monitorCustomerQueryVO);

    /**
     * crm 小程序 包含详细统计信息
     */
    PageResultObject<RobotCallJobListInfoVO> getRobotCallJobListStatsInfo(RobotCallJobWithUserQueryVO robotCallJobQueryVO);

    /**
     * 按权限获取任务列表
     */
    List<RobotCallJobListInfoVO> getRobotCallJobListWithAuth(@NotNull RobotCallJobWithUserQueryVO robotCallJobQueryVO);

    /**
     * add by zqy
     * 获取单个任务详情
     *
     * @param robotCallJobId 任务id
     * @return 任务对象
     */
    RobotCallJobPO getRobotCallJobById(Long tenantId, Long robotCallJobId);

    /**
     * add by zqy
     * 任务详情 包含线路详情和统计信息
     *
     * @param robotCallJobId 任务id
     * @return 任务对象json数据
     */
    RobotCallJobDetailInfoVO getRobotCallJobDetailInfo(Long tenantId, Long robotCallJobId);

    /**
     * 获取线路统计信息list
     */
    List<RobotCallJobDetailInfoVO> getRobotCallJobDetailInfoList(List<RobotCallJobPO> robotCallJobPOS);

    /**
     * add by zqy
     * 根据传入对象更新任务
     * 任务为暂停状态 租户id匹配 时可以修改
     * 限制可修改字段
     *
     * @param robotCallJobPO 修改值
     * @return 影响行数
     */
    int updateRobotCallJobByModifiedObject(RobotCallJobModifyVO robotCallJobPO, Boolean useOpenApi) throws ComException;


    /**
     * add by liuxin
     * 检查任务名在当前用户下是否重复
     *
     * @param robotCallJobId 修改任务的id，如为新建任务，则为null
     * @param name 当前检查的任务名
     * @param tenantId 租户id
     * @return 存在重名为TRUE,不存在为FALSE
     */
    Boolean checkNameDuplicate(String name, Long robotCallJobId, Long tenantId);


    int updateByModifiedObject(RobotCallJobPO modifyRobotCallJob);

    /**
     * add by zqy
     * 删除任务 修改任务状态和删除标志位
     *
     * @param robotCallJobId 任务id
     * @param tenantId       租户id
     * @param userId         用户id
     * @return 影响行数
     */
    int deleteRobotCallJob(Long robotCallJobId, Long tenantId, Long userId) throws ComException;

    boolean checkHighPriorityJob(RobotCallJobPO callJobInfo);

    List<SubRobotCallJobPO> sliceUpBigJobToSubJobList(RobotCallJobPO robotCallJob);

    /**
     * 获取一个可执行的任务
     *
     * @return 一个可执行任务的对象
     */
    Optional<Tuple3<RobotCallJobPO, Long, Map<Integer, RobotCallPhoneNumberWithSipInfoBO>>> getAvailableRobotCallJobInfo();

    /**
     * 判断是否所有线路都是欠费的状态
     */
    boolean isAllPhoneNumberDebt(Long jobId, boolean jobInit, List<RobotCallPhoneNumberWithSipInfoBO> chargePhoneNumberList);

    /**
     * 判断是否所有线路都不支持外呼
     */
    boolean isAllPhoneNumberUnsupported(Long jobId, List<RobotCallPhoneNumberWithSipInfoBO> chargePhoneNumberList);

    /**
     * 1. 租户是旧版计费客户and租户是按量付费客户and账户余额不足, 返回true
     * 2. 租户是新版计费客户and租户的指定账户余额不足, 返回true
     *
     * @param tenantAccountsCallNeedCheck 如果租户是新版计费客户, 需要检查的账户余额类型
     */
    boolean totalAccountDebt(Long tenantId, Collection<TenantAccountEnum> tenantAccountsCallNeedCheck);

	/**
	 * 1. 租户是旧版计费客户and租户是按量付费客户and账户余额不足, 返回true
	 * 2. 租户是新版计费客户and租户的指定外呼任务的线路所属指定账户余额不足, 返回true
	 */
	boolean totalAccountDebt(Long tenantId, Long robotCallJobId);

    boolean isAllPhoneNumberBreakDown(Long jobId, List<RobotCallPhoneNumberWithSipInfoBO> phoneNumberList);

    /**
     * 检查job是否任然可以执行，并且更新心跳
     *
     * @return job是否任然可以执行
     */
    boolean isJobStillExecAbleAndUpdateJobLastHeartBeatTime(Long callTaskId, Long subRobotCallJobId);

    /**
     * 更新callTask
     *
     * @param callJobId 待更新任务id
     * @param preStatus 更新状态
     */
    void updateRobotCallJobStatusToSystemHangUp(Long callJobId, RobotCallJobHangUpTypeEnum hangUpType, RobotCallJobStatusEnum preStatus);

    void updateRobotCallJobStatusToLineBreakDownSystemHangUp(Long callJobId, String reason, RobotCallJobStatusEnum preStatus);

    void updateRobotCallJobStatusById(Long callJobId, RobotCallJobStatusEnum status, RobotCallJobStatusEnum preStatus);
    void updateRobotCallJobStatusById(Long callJobId, RobotCallJobStatusEnum status, RobotCallJobStatusEnum preStatus, Boolean updateHeartBeat);

    void updateRobotCallJobStatusToQueue(Long callJobId, InQueueEnum inQueueType, RobotCallJobStatusEnum preStatus);

    void updateRobotCallJobStatusById(Long callJobId, RobotCallJobStatusEnum status, String hangUpReason, RobotCallJobHangUpTypeEnum hangUpType, InQueueEnum inQueueReason, RobotCallJobStatusEnum preStatus);

    /**
     * 获取任务的当前状态
     *
     * @param callJobId 主键id
     * @return 任务状态
     */
    RobotCallJobStatusEnum getRobotCallJobStatus(Long callJobId);

    /**
     * 更新并检查任务状态
     */
    void checkAndUpdateRobotCallJobStatus();

    /**
     * 检查所有排队中的任务，如果资源允许了的话，就运行起来
     */
    void checkInQueueJobs();

    boolean checkTransferHuman(Long dialogFlowId);
    /**
     * 更新可运行任务至可运行或系统暂停 并更新下次开始时间和结束时间
     */
    void setStatusForRunnableJob(RobotCallJobPO robotCallJob);

    /**
     * 检查超过time分钟没有更新的job为可运行状态
     *
     * @param time 超时时间
     */
    void checkAndUpdateTimeoutRobotCallJobToRunnable(int time);

    /**
     * 检查并更新超时的可运行任务 （不在运行时间的更新为系统暂停）
     */
    void checkAndUpdateTimeoutRunnableRobotCallJob();

    /**
     * 检查并更新超时的系统暂停任务
     */
    void checkAndUpdateTimeoutSystemSuspendedRobotCallJob();

    void checkAndUpdateTimeOutAutoRobotCallJob();

    /**
     * 获取任务的简单信息 含数据权限
     */
    List<SimpleRobotCallJobInfoBO> getRobotCallJobVerySimpleInfoList(Long tenantId, UserPO userPO, SimpleRobotCallJobInfoQueryVO callJobInfoQuery);

    /**
     * 获取任务的简单信息 含数据权限
     */
    List<SimpleRobotCallJobInfoBO> getRobotCallJobSimpleInfoList(Long tenantId, UserPO userPO, SimpleRobotCallJobInfoQueryVO callJobInfoQuery);

    /**
     * 根据任务id获取任务名称
     */
    List<SimpleRobotCallJobInfoBO> getNameByRobotCallJobId(Set<Long> robotCallJobIdSet);

    /**
     * 任务所需要的资源是否都具备了，如果不具备，执行排队逻辑
     * 1. 当前并发是否够用
     * 2. 手机线路是否有冲突
     *
     * @param callJobInfo            任务信息
     * @param mobilePhoneNumberIdSet 任务所需要的手机号码的id列表
     */
    InQueueEnum needQueue(RobotCallJobPO callJobInfo, Set<Long> mobilePhoneNumberIdSet, boolean checkTask);

    boolean haveNotEnoughRobot(RobotCallJobPO callJobInfo);

    /**
     * 检查任务配置的短信自动群发任务是否有效
     */
    String checkSmsJobConfig(RobotCallJobPO callJobInfo);

    /**
     * 检查任务配置的短信模板是否有效
     * @return 错误信息
     */
    String checkSmsTemplate(RobotCallJobPO callJobInfo);

    /**
     * 线路总并发数小于任务并发数
     */
    boolean lineConcurrencyNotEnough(RobotCallJobPO callJobInfo);

    /**
     * 线路总并发数小于任务并发数
     */
    boolean lineConcurrencyNotEnough(Long tenantId, PhoneTypeEnum phoneType, List<Long> tenantPhoneNumberIds, Integer robotCount);

    /**
     * 线路当前并发数小于任务并发数
     */
    boolean lineConcurrencyNotEnoughNow(RobotCallJobPO callJobInfo);

    boolean hasFreeRobot(Long tenantId, Long userId, int robotCount, Long robotCallJobId);

    /**
     * 通过id获取任务名称
     */
    String getRobotCallJobName(Long robotCallJobId);

	/**
	 * 获取任务的导入客户时指定加微账号字段信息,现在只有openapi导入时需要获取该信息做校验
	 */
	Set<String> getAddWechatProperties(Long robotCallJobId);

	/**
	 * 获取外呼任务的自定义变量
	 * 1.话术变量 2.短信变量 3.加微欢迎语变量
	 */
    Set<String> getPropertiesFromRobotCallJob(Long robotCallJobId, Long tenantId);

	/**
	 * @param forceGet 如果forceGet为true, 即使skipValidProperties=true的任务也正常查询变量; 否则如果skipValidProperties=true, 返回空set
	 */
    Set<String> getPropertiesFromRobotCallJob(Long robotCallJobId, Long tenantId, Boolean forceGet);

	/**
	 * 获取外呼任务的自定义变量
	 * 1.话术变量 2.短信变量 3.加微欢迎语变量 4.转人工号码 5.加微账号/加微组织/SCRM客户标签/下单时间
	 */
	Set<String> getPropertiesAndExtraInfoFromRobotCallJob(Long robotCallJobId, Long tenantId);

	Set<String> getPropertiesAndExtraInfoFromRobotCallJobs(Collection<Long> robotCallJobIds, Long tenantId, PlanImportCustomerTypeEnum planImportType);
	Map<Long, Set<String>> getPropertiesAndExtraInfoByRobotCallJobs(Collection<Long> robotCallJobIds, Long tenantId, PlanImportCustomerTypeEnum planImportType);

    /**
     * 查询外呼任务对应的dialogFlowId后调用
     * @see RobotCallJobService#deleteCustomerPersonsToRobotCallJob(Long, Long, Long, Integer)
     */
    void deleteCustomerPersonsToRobotCallJob(Long tenantId, Long robotCallJobId, Integer taskCount);

    /**
     * 删除子任务总量 在删除未呼客户列表时调用
     */
    void deleteCustomerPersonsToRobotCallJob(Long tenantId, Long robotCallJobId, Long dialogFlowId, Integer taskCount);

    /**
     * 修改任务并发数
     */
    void modifyRobotCount(Long tenantId, Long robotCallJobId, Integer robotCount);

    /**
     * 更新所有坐席过期任务到可运行状态
     */
    void updateAllNoRobotAvailableJobToInQueue(Long tenantId);

    /**
     * 更新使用了这条线路，并且欠费的任务到排队状态
     */
    void updateDebtJobToInQueueWhoUserThisPhoneNumber(Long tenantId, Long tenantPhoneNumberId);

    /**
     * 获取正在使用这条线路的任务id
     */
    Optional<RobotCallJobPO> getOneJobUsingTenantPhoneNumber(Long tenantPhoneNumberId);

    /**
     * 获取正在使用这条线路的任务
     */
    Optional<RobotCallJobPO> getOneJobUsingTenantPhoneNumberList(Set<Long> tenantPhoneNumberIdList);

    /**
     * 通过关键字搜索
     */
    List<RobotCallJobPO> selectAllBySearchWords(Long dialogFlowId, String searchWords);

    List<RobotCallJobConcurrencyInfoVO> getConcurrencySumByIp();

    List<RobotCallJobConcurrencyInfoVO> getAllConcurrencySumByIp();

    List<RobotCallJobConcurrencyInfoVO> getConcurrencySum();

    List<ConcurrencySumPO> getConcurrencyList(LocalDateTime startTime, LocalDateTime endTime);

    List<SeatConcurrencySumPO> getSeatConcurrencyList(SeatConcurrencyRequestVO seatConcurrencyRequestVO);

    /**
     * 设置系统并发量阈值消息推送
     */
    void setSystemConcurrentWaringSetting(SystemConcurrencyWaringSettingVO systemConcurrencyWaringSettingVO);

    /**
     * 查询系统并发量阈值消息设置
     */
    SystemConcurrencyWaringSettingVO getSystemConcurrentWaringSetting();

    void registerHost();

    void unRegisterHost();

    /**
     * 更新指定job的next_start_time和next_end_time
     *
     * @param jobId  任务id
     * @param timeBO 下次有效时间段
     */
    void updateTimeDurationById(Long jobId, ActiveTimeBO timeBO);

    List<RobotCallJobPO> selectByTenantId(Long tenantId);

    /**
     * 获取客户下运行中的任务ID列表
     */
    List<Long> selectRunningRobotCallJobByTenantId(Long tenantId);

    /**
     * 查询租户下需要监控预警的任务
     * @param tenantId
     * @return
     */
    List<Long> selectNeedWarningRobotCallJobIdByTenantId(Long tenantId);

    /**
     * 筛选客户下的任务下拉列表
     */
    List<SimpleCallJobDTO> selectByTenantIdLimit(Long tenantId, String robotCallJobName, boolean unarchived);

    RobotCallJobActiveTimeDTO getRobotCallJobActiveTime(Long robotCallJobId);

    List<RobotCallJobPO> selectByDialogFlowIdList(Long tenantId, Collection<Long> dialogFlowIdList);

    /**
     * 根据外呼策略组id查询呼叫任务，在删除外呼策略组的时候用到
     * @param tenantId 客户id
     * @param callPolicyGroupId 外呼策略组id
     */
    List<RobotCallJobPO> selectByCallPolicyGroupId(Long tenantId, Long callPolicyGroupId);


    /**
     * 获取待停止的任务列表，这些要停止的任务的startTime一定比startTime早
     *
     * @param tenantId   租户id
     * @param robotCount （待需要的并发数量）
     */
    List<RobotCallJobPO> getToStopJobList(Long tenantId, int robotCount, LocalDateTime startTime);

    /**
     * 获取外呼策略组用到的线路信息
     */
    List<RobotCallPhoneNumberWithSipInfoBO> getPolicyGroupCallerSipInfoByRobotCallJobId(Long robotCallJobId);

    List<RobotCallJobPO> selectByCallPolicyGroupIdList(Long tenantId, Collection<Long> callPolicyGroupIdList);

    PageResultObject<CallRecordCommonDTO> getRobotCallJobList(Integer pageNum, Integer pageSize, String searchWords, Long tenantId);

    /**
     * 获取beginDate到endDate时间段内有外呼的任务列表
     */
    PageResultObject<CallRecordCommonDTO> getRobotCallJobListByDate(Integer pageNum, Integer pageSize, Long robotCallJobId, String searchWords, Long tenantId, LocalDate beginDate, LocalDate endDate);

    JSONArray getStatus(Long[] robotCallJobIds, Long tenantId);

    void updateJobCancelSend(Long robotCallJobId, ConditionCancelSendEnum conditionCancelSend, Long userId);

    /**
     * 挂起该线路下的所有任务，包括检查线路策略组，如果策略组仅剩该条可用线路，则策略组的任务也会被挂起
     */
    void stopJobByPhoneNumber(Long phoneNumberId);

    void stopJobByTenantPhoneNumber(Long tenantPhoneNumberId);

    /**
     * 恢复该线路下的所有任务，包括检查线路策略组，如果策略组存在因线路故障被挂起任务，则恢复
     */
    void resumeJobByPhoneNumber(Long phoneNumberId);

    void resumeJobByTenantPhoneNumber(Long tenantPhoneNumberId);

    void resetRobotCallJobIpAddress(Long robotCallJobId);

    /**
     * 根据计算出的nextDuration重设job的状态
     * 如果还能拨打则为系统暂停, 否则为任务超时
     */
    void computeNextStartAndEndDateTime(RobotCallJobPO job, Optional<ActiveTimeBO> nextActiveDuration);

    RobotCallJobPO getJobByName(Long tenantId, String name);

    RobotCallJobPO copyRobotCallJob(Long robotCallTaskId, RobotCallJobPO robotCallJob, Long userId, Long tenantId);

    void updateRobotCount(Integer robotCount, Long robotCallJobId);

    void updateRobotCount(Integer robotCount, Integer concurrencyQuota, Long robotCallJobId, RobotCallJobTypeEnum robotCallJobType);

    void updateRobotCount(Integer robotCount, Integer concurrencyQuota, Long robotCallJobId, RobotCallJobTypeEnum robotCallJobType, Integer shouldUseRobotCount);

    void updateBaseRobotCountAndBaseConcurrencyQuota(Integer baseRobotCount, Integer baseConcurrencyQuota, Long robotCallJobId);

    void checkTimeRobotAndElastic(RobotCallJobPO robotCallJobPO);

    int getCurrentServerQuota();

    RobotCallJobTypeEnum selectTypeByJobId(Long robotCallJobId);

    void resetSubRobotCallJob(Long robotCallJobId);

    int addMoreSubRobotCallJob(Long robotCallJobId, Integer totalRobotCount, Integer totalConCurrency);

    /**
     * 根据话术id列表更新状态到系统挂起状态
     */
    void updateToSystemHangupStatusByTtsVoiceUnbind(Long tenantId, List<Long> dialogFlowIdList);

    /**
     * 客户未认证 挂起所有任务
     * @param tenantId 租户id
     */
    void setNoAuthentication(Long tenantId);

    List<RobotCallJobPO> selectNoAuthenticationJobs(Long tenantId);

    /**
     * 客户已认证 拉起所有任务
     *
     * @param tenantId 租户id
     */
    void setAuthentication(Long tenantId);

    /**
     * 首页的任务数据列表信息
     */
    PageResultObject<IndexJobVO> indexJobInfo(Long tenantId, UserPO user, List<RobotCallJobStatusEnum> statuses, Integer pageNum, Integer pageSize);

    /**
     * 根据RobotCallJobId和TenantId更新LastEditTime字段
     */
    void updateEditTimeById(Long tenantId, Long robotCallJobId);

    /**
     * 当前任务是否为需发短信且余额不足
     * @param tenantId           tenant信息
     * @param dialogSmsTemplateIds 话术包含的短信模板
     * @return true需发短信且余额不足
     */
    boolean smsDebt(Long tenantId, Collection<Long> dialogSmsTemplateIds, RobotCallJobPO robotCallJob);

	/**
	 * 当前任务是否为需发短信且余额不足
	 * @param ownerTypes 新版计费客户, 提前查出来的短信模板涉及的余额类型
	 * @return true需发短信且余额不足
	 */
    boolean smsDebt(Long tenantId, Collection<Long> dialogSmsTemplateIds, RobotCallJobPO robotCallJob, Set<OwnerTypeEnum> ownerTypes);

    /**
     * 任务收藏状态更新
     * @param robotCallJobId 任务id
     * @param collectionMark 收藏标记
     */
    void collectionMarkUpdate(Long robotCallJobId, Boolean collectionMark,Long tenantId);

	/**
	 * 批量设置任务的folderId
	 */
	void setFolderId(Long tenantId, Long folderId, Collection<Long> jobIds);

	void addJobsFolder(RobotCallJobFolderProcessJobsVO request);

	void removeJobsFolder(RobotCallJobFolderProcessJobsVO request);

    @Transactional
    void increaseSubJob(Long tenantId, Long robotCallJobId, Long userId, int delta);

    DynamicRobotResponseVO increaseSubJobApi(DynamicRobotRequestVO requestVO);

    void increaseRobot(Long robotCallJobId, int incremental);

    /**
     * 更新任务选项ID列表
     */
    void appendOptionIds(Long robotCallJobId, List<Long> optionIdList, CallJobFieldPropertyEnum callJobFieldPropertyEnum);

    /**
     * 从话术和录音师同步选项
     */
    void syncOptionIds(Long robotCallJobId, Collection<Long> optionIdList);

    /**
     * 根据选项ID列表查询任务
     */
    List<RobotCallJobOptionBO> selectRobotCallJobByOptionIdList(List<Long> tenantIdList, Long vOptionId, Collection<Long> cOptionIdList);

    /**
     * 根据选项ID列表查询任务下拉选项
     */
    List<RobotCallJobOptionBO> selectRobotCallJobByOptionLimit(Long tenantId, String name, Collection<Long> vOptionIdList, Collection<Long> cOptionIdList);

    /**
     * 查询该话术下有多少任务
     */
    Integer selectCallJobCountByDialogFlowId(Long dialogFlowId, Long tenantId);

    List<Long> selectRobotCallJobIdListByDialogFlowId(Long dialogFlowId, Long tenantId);

    /**
     * 查询任务列表（标注平台使用）
     */
    List<RobotCallJobMarkSystemVO> selectAllForMarkSystem(RobotCallJobMarkQueryVO vo);

	List<RobotCallJobPO> selectByTenantIdPlanId(Long tenantId, Long callOutPlanId);

    List<RobotCallJobPO> selectByTenantIdPlanIds(Long tenantId, List<Long> callOutPlanIds);

    List<RobotCallJobPO> queryByCallOutPlanCondition(CallOutPlanJobQueryVO query);

    void doWhenJobStatusChange(Long callJobId);

    void doWhenJobStatusChange(RobotCallJobPO robotCallJobPO);

    void doWhenJobStatusChange(RobotCallJobPO robotCallJobPO, UserPO userPO, IsvInfoPO isvInfoPO);

    List<RobotCallJobDTO> selectByAllFolderId(Long tenantId, List<Long> folderIds);

    /**
     * 设置任务的 folderId 和 sortId
     */
    void setFolderIdAndSortId(Long tenantId, Long folderId, Long jobId, Long sortId);

    Integer countTopJob(Long tenantId, Long folderId);

    Long getMaxSortIdByTenantIdAndFolderId(Long tenantId, Long folderId);

    List<Long> selectReorderJobList(Long tenantId, Long folderId, CallJobOrderTypeEnum orderType);

    Map<Long, Integer> getJobsCount(Long tenantId, Collection<Long> folderIds, UserPO user);

    /**
     * 重置任务的预警状态
     */
    void resetWarningStatus();

    /**
     * 根据任务ID列表设置预警状态
     */
    void updateWarningStatusIds(List<Long> robotCallJobIds, JobTypeEnum jobTypeEnum);

    /**
     * 根据id查询名称, 目前话术重构那边用
     */
    List<IdNamePairVO<Long, String>> selectJobIdNamePairByIdList(Collection<Long> jobIds);

	List<WechatWelcomeMsgBO> getWelcomeMsg(Collection<Long> robotCallJobIds);

    String checkJobInfo(RobotCallJobPO robotCallJob, Long userId, Set<String> checkJobNames);

    /**
     * 获取系统并发的统计信息
     */
    SystemRobotStatsVO getSystemRobotStats();

    /**
     * 修改人群包圈选
     */
    void modifyUserGroup(ModifyUserGroupRequestVO requestVO);

    /**
     * 手动拉取人群包数据
     */
    void manualImportCustomerPersonByUserGroup(Long robotCallJobId, Long tenantId, Long currentUserId);

    /**
     * 定时任务自动拉去人群包数据
     */
    void autoImportCustomerPersonByUserGroup();

    /**
     * 根据文件夹id/任务id  查询文件夹下的任务创建人
     */
    List<Long> selectCreateUserIdByFolderIdsOrJobIds(Long tenantId, List<Long> jobIds, List<Long> folderIds);

    Set<String> checkCustomerPersonAndDialogFlowExtraField(Long tenantId, Long robotCallJobId);

    /**
     * 巡检异常 系统挂起任务
     * 丝芙兰定制
     */
    void systemHangUpRobotCallJobBySephora(Long tenantId, Long robotCallJobId, String hangUpReason);


    /**
     * 查询任务列表-蚂蚁金服使用
     */
    List<RobotCallJobAntResponseVO> getRobotCallJobListForAnt(RobotCallJobAntRequestVO request);

	/**
	 * 获取任务配置的短信模板, 不含话术
	 */
	Set<Long> getRobotCallJobSmsTemplateIdSet(RobotCallJobPO robotCallJob);

    /**
     * 闪信配置校验
     */
    void robotCallJobFlashTemplateIdConfigCheck(Long tenantId, RobotCallJobModifyVO modifyVO);

    CallJobFilterStatisOpenConfigVO checkPreCallOutFilterStatisOpen(RobotCallJobSearchDTO robotCallJobSearchDTO);

    void savePreCallOutFilterStatisOpen(RobotCallJobFilterConfigDTO robotCallJobFilterConfigDTO);

	/**
	 * 物理删除
	 */
 	void deleteByTenantIdCreateTime(Long tenantId, Integer day);


    /**
     * 动态修改任务的线路配置信息
     * @param requestVO
     */
     void updateRobotCallJobPhoneNumber(UpdateRobotCallJobPhoneNumberRequestVO requestVO);

    /**
     * 根据租户删除任务
     * @param tenantId
     * @return
     */
    int deleteByTenantId(Long tenantId);

    List<RobotCallJobPO> selectByDialogFlowIds(Collection<Long> dialogFlowIdList);
}
