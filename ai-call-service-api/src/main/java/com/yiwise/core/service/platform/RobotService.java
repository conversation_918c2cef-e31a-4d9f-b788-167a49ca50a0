package com.yiwise.core.service.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.RobotPO;
import com.yiwise.core.model.bo.robot.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.boss.DistributorCustomerQueryVO;
import com.yiwise.core.model.vo.ope.QiyuTenantAiDetailVO;
import com.yiwise.core.model.vo.ope.RobotExpirationTimeVO;
import com.yiwise.core.model.vo.ope.SaveRobotVO;
import com.yiwise.core.model.vo.openapi.RobotPeriodWithCountVO;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @date 28/07/2018
 */
public interface RobotService extends BasicService<RobotPO> {
    /**
     * 根据tenantId查找PO
     *
     * @return RobotPO列表
     */
    PageResultObject getByTenantId(Integer pageNum, Integer pageSize, Long tenantId,Long userId,SystemEnum systemEnum);

    /**
     * 获取租户今天可以的坐席的数量
     *
     * @param tenantId 租户id
     * @return 今天可用坐席数量
     */
    Integer getTenantTodayRobotCount(Long tenantId, SystemEnum systemType);

	/**
	 * 代理商客户今天可用的并发数(代理商客户坐席数和并发数分开买)
	 */
	Integer getDistributorTenantTodayConcurrency(Long tenantId);

    /**
     * 获取租户此刻机器人坐席的使用情况
     */
    RobotUsingInfoBO getUserRobotUsingInfo(Long tenantId, Long userId, SystemEnum systemType);

    RobotInfoBO getAuthUserRobotUsingInfo(Long tenantId, Long userId, SystemEnum systemType);

    /**
     * 获取租户此刻机器人坐席的使用情况
     *
     * @param exceptRobotCallJobId 不把该任务的并发计算在内
     */
    RobotUsingInfoBO getRobotUsingInfo(Long tenantId, Long exceptRobotCallJobId);

    /**
     * 新增客户坐席
     */
    void addRobotNotNull(RobotPO robot);

    /**
     * 通过客户id获取所有的机器坐席
     */
    List<RobotExpirationTimeVO> getAllByTenantId(Long tenantId);

    /**
     * 根据租户id获取该租户机器坐席最大的服务时间
     */
    LocalDate getMaxEndTime(Long tenantId);

    /**
     * 查询客户的最大机器过期时间在时间范围内的tenantID
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     */
    List<RobotTenantMaxEndTimeBO> getTenantMaxEndTimeByExpireDate(LocalDate startDate, LocalDate endDate);

    /**
     * 获取所有可用的且没过期的数量
     */
    Integer getAllAvailableCount(Long tenantId);

    List<RobotExpirationTimeVO> selectByTenantIdList(List<Long> tenantIdList);

    /**
     * 根据时间筛选机器
     */
    List<RobotPO> selectByTime(String startDate, String endDate);

    List<RobotPO> selectByTenantId(Long tenantId);

    void updateFreeAccountToFormal(Long tenantId);

    void deleteRobot(Long tenantId, Long robotId);

    Integer getValidCountByPart(Long tenantId, TenantAiccPartEnum tenantAiccPart);

    List<RobotPO> getTenantMinStartTimeByTenantIds(List<Long> should);

    FreeRobotBO getUserNowFreeRobot(Long tenantId, Long userId);

    /**
     * 结束时间 有一条满就查询出来
     */
    List<RobotPO> selectByTimeAndType(String startDate, String endDate, TenantAiccPartEnum tenantAiccPart);

    List<RobotPO> selectMinStartTimeAndType(String beginStartDate, String beginEndDate, TenantAiccPartEnum tenantAiccPart);

    RobotUsingInfoNewBO getUserRobotUsingInfoNew(Long tenantId, Long userId, SystemEnum systemType);

    List<QiyuTenantAiDetailVO> selectByCondition(DistributorCustomerQueryVO query);

    RobotPO selectByTenantIdAndTime(Long tenantId, LocalDate startTime, LocalDate endTime);

    /**
     * open-api 查询有效AI坐席周期和数量
     */
    PageResultObject<RobotPeriodWithCountVO> selectUnexpiredRobotListByPage(Integer pageNum, Integer pageSize, Long tenantId);

    /**
     * 更新坐席类型
     */
    void updateRobotSeatType(Long robotId, RobotSeatTypeEnum robotSeatTypeEnum);

	/**
	 * 查询在指定日期有可用bot的客户id列表
	 */
	Set<Long> getActiveTenantIds(LocalDate date);

    /**
     * 获取客户米糠云坐席数量
     */
    int getTenantMikangyunRobotCount(Long tenantId);

	/**
	 * 直销客户新增坐席前校验是否小于FS加密设置的历史并发上限
	 *
	 * @return true小于, false大于需要提醒员工
	 */
	Map<String, Long> checkFsEncryptHistoricalPeakConcurrencyLimit(Long tenantId, LocalDate startDate, Integer count);


    /**
     * 添加一知人工坐席
     * @param robotPO
     */
    void addAgent(SaveRobotVO robotPO);

    List<Long> selectTenantIdByType(TenantAiccPartEnum tenantAiccPartEnum);

}
