package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.entity.PhoneLocationPO;
import com.yiwise.core.dal.entity.PhoneNumberPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.dto.LineStatusDTO;
import com.yiwise.core.model.dto.PhoneNumberBindStatsDTO;
import com.yiwise.core.model.enums.CompanyEnum;
import com.yiwise.core.model.enums.CustomerIndustryEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.gateway.GatewayAddVO;
import com.yiwise.core.model.vo.phonenumber.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2018/08/15
 */
public interface PhoneCardService extends BasicService<PhoneNumberPO> {

    /**
     * 获取省份列表
     *
     * @return 省份列表
     */
    List<String> getProvinces();

    List<PhoneLocationPO> getProvinceMap();

    /**
     * 获取省份的城市列表
     *
     * @param province 省份名
     * @return 城市列表
     */
    List<String> getCitiesByProvince(String province);

    List<PhoneLocationPO> getCitiesByProvinceMap(String province);

    /**
     * 获取有业务的省份列表
     *
     * @return 省份列表
     */
    List<String> getCurrentProvinces();

    /**
     * ope查询电话卡信息
     *
     * @param phoneNumberQueryVO 根据voip账号、电话号码、设备编号查询
     * @return 带公司名的电话卡vo
     */
    PageResultObject<PhoneNumberOpeVO> getByCondition(PhoneNumberQueryVO phoneNumberQueryVO, Long upDistributorId, Boolean paging);

    /**
     * 获取代理商或客户的线路费用
     *
     * @param companyEnum 用户类型
     * @param phoneNumberId 线路id
     * @param pageNum 页码
     * @param pageSize 一页大小
     * @param distributorId 代理商id
     * @return 结果
     */
    PageResultObject<PhoneNumberBindStatsDTO> getCustomerOrDistributorBindStats(CompanyEnum companyEnum, Long phoneNumberId, Integer pageNum, Integer pageSize, Long distributorId);

    /**
     * boss查询电话卡信息
     *
     * @param phoneNumberQueryVO 根据voip账号、电话号码、设备编号查询
     * @return 带公司名的电话卡vo
     */
    PageResultObject<PhoneNumberNewBossVO> getByConditionBoss(PhoneNumberQueryVO phoneNumberQueryVO);

    /**
     * 获得一个PhoneNumberPO的归属者信息
     *
     * @param po               信息来源
     * @param tenantPOMap      tenantId -> tenantPO缓存
     * @param distributorPOMap distributorId -> distributorPO缓存
     * @return 归属者信息
     */
    PhoneNumberOwnerVO transformOwnerInfo(PhoneNumberPO po,
                                          Map<? extends Serializable, TenantPO> tenantPOMap,
                                          Map<? extends Serializable, DistributorPO> distributorPOMap);

    /**
     * ope查看归属者列表
     *
     * @param ownerType   归属者类型
     * @param searchWords 要搜索的关键词
     * @return 归属者列表
     */
    List<PhoneNumberOwnerVO> getOwnerList(CompanyEnum ownerType, String searchWords);

    /**
     * 新增线路
     *
     * @param phoneNumberAddVO 新增电话卡vo
     * @param createUserId     更新者的用户id
     */
    void addLine(PhoneNumberAddVO phoneNumberAddVO, Long createUserId);

    /**
     * 新增网关
     *
     * @param gatewayAddVO 新增电话卡vo
     * @param createUserId 更新者的用户id
     */
    void addGateway(GatewayAddVO gatewayAddVO, Long createUserId);

    /**
     * ope修改配置,不包括网关 只用于OPE
     *
     * @param opeUpdateVO  电话卡更新vo
     * @param updateUserId 更新者的用户id
     */
    void update(PhoneNumberOpeUpdateVO opeUpdateVO, Long updateUserId);

    /**
     * boss修改网关配置
     *
     * @param bossUpdateVO 电话卡更新vo
     * @param updateUserId 更新者的用户id
     */
    void update(PhoneNumberBossUpdateVO bossUpdateVO, Long updateUserId);


    /**
     * crm 修改配置
     *
     * @param crmUpdateVO
     * @param tenantId
     * @param updateUserId
     */
    void update(GatewayPhoneNumberCrmUpdateVO crmUpdateVO, Long tenantId, Long updateUserId);

    /**
     * 更换卡号
     *
     * @param phoneNumberId 电话卡id
     * @param phoneNumber   卡号
     * @param updateUserId  更新者的用户id
     */
    void updatePhoneNumber(Long phoneNumberId, String phoneNumber, Long updateUserId);

    /**
     * crm 更换卡号
     *
     * @param phoneNumberId 电话卡id
     * @param phoneNumber   卡号
     * @param updateUserId  更新者的用户id
     */
    void updatePhoneNumberForCrm(Long tenantId, Long phoneNumberId, String phoneNumber, Long updateUserId);

    /**
     * 修改备注
     *
     * @param phoneNumberId 电话卡id
     * @param remark        新备注
     * @param updateUserId  更新者id
     */
    void updateRemark(Long phoneNumberId, String remark, Long updateUserId);

    /**
     * 设置ai自动拨打间隔
     *
     * @param phoneNumberId 电话卡id
     * @param callInterval  自动拨打间隔（单位秒）
     * @param updateUserId  更新者的用户id
     */
    void updateCallInterval(Long phoneNumberId, Integer callInterval, Long updateUserId);

    void updateAllCallInterval(String deviceId, Integer callInterval, Long updateUserId);

    /**
     * 按两个归属者id查找设备编号列表
     *
     * @param ownerDistributorId 归属者代理商id
     * @param ownerTenantId      客户代理商id
     * @return 设备编号列表
     */
    List<String> selectDeviceIdListByTwoOwnerId(Long ownerDistributorId, Long ownerTenantId);


    /**
     * 按ownerTenantId查找设备编号列表
     *
     * @param ownerTenantId 租户id
     * @return
     */
    List<String> selectDeviceIdListByOwnerTenantId(Long ownerTenantId);

    /**
     * 查询该sip账号对应的电话卡个数
     *
     * @param sipAccount sip账号
     * @return 电话卡个数
     */
    Integer countBySipAccount(String sipAccount);

    /**
     * 按设备编号列表查询
     *
     * @param deviceIdList 设备编号列表
     * @return 列表
     */
    List<PhoneNumberPO> selectMobileByDeviceIdList(List<String> deviceIdList);

    /**
     * 按id批量更新电话卡的两个归属者id
     *
     * @param phoneNumberIdList  电话卡id列表
     * @param ownerTenantId      归属者租户id
     * @param ownerDistributorId 归属者代理商id
     * @param updateUserId       更新者的用户id
     */
    void updateOwnerByIdList(List<Long> phoneNumberIdList, Long ownerTenantId, Long ownerDistributorId, Long updateUserId);

    /**
     * 更新电话卡的名称和备注
     *
     * @param phoneNumberId 电话卡id
     * @param phoneName     线路名称
     * @param remark        备注
     */
    void updateNameAndRemarkById(Long phoneNumberId, String phoneName, String remark);

    /**
     * 电话卡拔卡操作,其实是解绑tenant的操作，分销商是可以改的
     *
     * @param phoneNumberId 电话卡id
     */
    void pullOutPhoneCard(Long phoneNumberId);

    /**
     * 把网关所以的电话卡 拔出
     */
    void pullOutAllPhoneCard(String deviceId);

    /**
     * 检查线路是否可用（不包含所属网关检测）
     *
     * @return
     */
    boolean checkPhoneNumberAvailable(Long phoneNumberId);

    /**
     * 检查线路是否可用（包含所属网关检测）
     */
    boolean checkPhoneNumberWithGatewayAvailable(Long phoneNumberId);

    /**
     * 检查线路是否支持外呼
     */
    boolean checkPhoneNumberSupported(Long phoneNumberId);

    Optional<LineStatusDTO> getLineStatus(Long phoneNumberId);

    /**
     * 通过状态从redis找到对应的phonenumberid
     */
    List<Long> getLineStatusByStatus(Boolean isLineAvailable);

    void updateLineStatus(Long phoneNumberId, Boolean isLineAvailable, String hint);

    LineStatusDTO setStatus(Long tenantId, Long phoneNumberId);

    void changeLineMarketShow(Long phoneNumberId, Boolean lineMarketShow);

    String selectLikelyAreaCodeByProvinceAndCity(String province, String city);

    void deleteById(Long phoneNumberId);

    JobStartResultVO opeExportPhoneLine(PhoneNumberQueryVO phoneNumberQueryVO, Long opeDistributorId);

    PhoneNumberPO addLineNew(PhoneNumberAddBO phoneNumberAddVO, Long createUserId);

    boolean addLineByThirdPart(ThirdPhoneNumberAddVO phoneNumberAddVO);

    void billStats(List<? extends PhoneNumberOpeVO> phoneNumberOpeVOList);

    /**
     * 查询最大voip帐号
     */
    MaxSipAccountVO getMaxSipAccount();

    /**
     * 设置最大voip
     */
    void setMaxSipAccount(String sipAccount);

    Long getMaxSipAccountAndIncrement();

    /**
     * 获取客户赛道的最大前缀
     *
     * @param customerTrackType 客户赛道类型
     * @return 结果
     */
    MaxCustomerTrackTypePreVO getMaxCustomerTrackTypePre(Integer customerTrackType);

    /**
     * 获取赛道信息集合
     *
     * @param customerIndustry
     * @param onlyFirstLevel
     * @param customerTrackTypeName
     * @return
     */
    List<PhoneCustomerTrackTypeVO> getCustomerTrackTypeList(CustomerIndustryEnum customerIndustry, Boolean onlyFirstLevel, Integer customerTrackType, String customerTrackTypeName);



    /**
     * 转移线上数据
     */
    void pushTransferData();

    /**
     * 转移线上数据
     */
    void pushTransXiaokeferData();

    String processLineName(String name);

    List<PhoneCustomerTrackTypeVO> getTrackTypeRcsList();

    List<PhoneCustomerTrackTypeVO> getMagicBotTrackTypeList();

    /**
     * 批量修改线路网关
     * @param vo
     */
    void phoneNumberBatchEditGw(PhoneNumberBatchEditGwVO vo);

    /**
     * 网关组替换网关
     * 将A网关组中的a网关替换为b网关
     * @param carrierBatchEditGwVO
     */
    void phoneNumberBatchEditCarrier(CarrierBatchEditGwVO carrierBatchEditGwVO);
}
