package com.yiwise.core.service.engine;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.SmsReceiveRecordPO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sms.SmsReceiveQueryVO;
import com.yiwise.core.model.vo.sms.SmsReceiveRecordVO;

import java.util.List;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/3/2
 * @class <code>SmsReceiveService</code>
 * @see
 * @since JDK1.8
 */
public interface SmsReceiveRecordService extends BasicService<SmsReceiveRecordPO> {

    PageResultObject<SmsReceiveRecordPO> getSmsReceivePageInfo(SmsReceiveQueryVO smsReceiveQueryVO);

    JobStartResultVO export(SmsReceiveRecordVO exportVO, Long tenantId, Long userId);

    List<SmsReceiveRecordPO> getSmsReceiveListByIds(List<Long> idList, SmsReceiveRecordVO smsReceiveRecordVO);

    JobStartResultVO importToSmsJob(SmsReceiveRecordVO smsReceiveRecordVO, Long tenantId, Long userId);

    JobStartResultVO importToBlackList(SmsReceiveRecordVO smsReceiveRecordVO, Long tenantId, Long userId);

    void addSmsReceive(SmsReceiveRecordPO param);

    PageResultObject<SmsReceiveRecordPO> getSmsReceivePageInfoLimit(SmsReceiveQueryVO smsReceiveQueryVO);

    Long getSmsReceivePageInfoCount(SmsReceiveQueryVO smsReceiveQueryVO);
}
