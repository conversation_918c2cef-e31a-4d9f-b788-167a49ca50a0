package com.yiwise.core.service.engine.isv;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.callrecord.IntentChangeMessageBO;
import com.yiwise.core.model.bo.customerwhitelist.AddWhiteListCallBackBO;
import com.yiwise.core.model.bo.isv.ISVCallbackBO;
import com.yiwise.core.model.bo.sms.SmsReceptResultBO;
import com.yiwise.core.model.bo.sms.SmsSendDetailResultBO;
import com.yiwise.core.model.dto.toyota.ToyotaCallbackRecordDTO;
import com.yiwise.core.model.enums.GenderEnum;
import com.yiwise.core.model.enums.isv.BlackListOperationTypeEnum;
import com.yiwise.core.model.enums.isv.ISVDataTypeEnum;
import com.yiwise.core.model.vo.isvcallback.IsvCallBackQuery;
import com.yiwise.core.model.vo.ope.AuthReviewRecordCallbackVO;
import com.yiwise.core.model.vo.openapi.RobotCallJobAsyncImportResultVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberContactHistoryOpenVO;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IsvCallbackService extends BasicService<IsvCallbackPO> {
    /**
     * 中断isv二次回调，在quartz中使用
     */
    void stopHandleSecondaryIsvCallback();

    /**
     * 处理isv二次回调
     */
    void handleSecondaryIsvCallback();

    void handleSecondarySmsIsvCallback();

    /**
     * 回调通话记录
     *
     * @param isvInfo
     * @param callRecord
     */
    void callBackCallRecord(IsvInfoPO isvInfo, CallRecordPO callRecord, List<CallDetailPO> callDetailList, Map<String, String> varName2ValueMap, UserPO userPO, Integer redialTimes, AddWhiteListCallBackBO addWhiteListCallBackBO, RobotCallJobPO robotCallJobPO, Long billChatDuration, String dialogFlowName, Long billRingDuration, Boolean recordSendSms, AccountVO accountVO, String phoneNumber, TenantPO tenantPO, Map<String, Object> customMetricsMap,Boolean allowCallback, Object extendInfo);

    void callBackCallInRecord(IsvInfoPO isvInfo, CallInRecordPO callRecord, List<CallInDetailPO> callDetailList);

    /**
     * 回调任务结果
     *
     * @param isvInfo
     * @param callJob
     */
    void callbackCallJob(IsvInfoPO isvInfo, RobotCallJobPO callJob, UserPO userPO);

    void callbackFilteredTask(IsvInfoPO isvInfo, FilteredRobotCallTaskPO FilteredRobotCallTaskPO, UserPO userPO);

	/**
	 * 隐私号通话回调
	 */
	void callbackPrivacyNumber(IsvInfoPO isvInfo, PrivacyNumberContactHistoryOpenVO contactHistory);

    /**
     * 调用回调接口，用于mq处理
     *
     * @param isvInfo
     * @param obj
     * @param title
     */
    void doCallBackCall(IsvInfoPO isvInfo, ISVCallbackBO obj, String title);

    PageResultObject getIsvCallBackList(IsvCallBackQuery query);

    void checkIsvCallBackCount();

    void callbackBlackList(IsvInfoPO isvInfo, BlackListOperationTypeEnum operation, CustomerWhiteGroupPO customerWhiteGroupPO, List<CustomerWhiteListPO> customerWhiteListPOS);

    void callBackRobotCallGender(IsvInfoPO isvInfo, CallRecordPO callRecordInfo, GenderEnum gender);

    void smsSendRecordCallback(IsvInfoPO isvInfo, ISVDataTypeEnum isvDataTypeEnum, Long refId, SmsSendDetailResultBO sendResultBO, String title);

    void smsReceptRecordCallback(IsvInfoPO isvInfo, ISVDataTypeEnum isvDataTypeEnum, Long refId, SmsReceptResultBO sendResultBO, String title);

    /**
     * API导入任务回调
     * @param isvInfo
     * @param isvDataTypeEnum
     * @param refId
     * @param resultVO
     * @param title
     */
    void apiImportCustomerResultCallback(IsvInfoPO isvInfo, ISVDataTypeEnum isvDataTypeEnum, Long refId, RobotCallJobAsyncImportResultVO resultVO, String title);



    /**
     * 轻量化_资质认证审核状态回调
     */
    void activityQualificationAuthReviewRecordCallback(AuthReviewRecordCallbackVO callbackVO);

    /**
     * 人工意向变更回调
     * @param tenantId
     * @param isvDataTypeEnum
     * @param callRecordId
     * @param intentChangeMessageDTO
     * @param title
     */
    void manualIntentChangeCallBack(IsvInfoPO isvInfo,Long tenantId, ISVDataTypeEnum isvDataTypeEnum, Long callRecordId, IntentChangeMessageBO intentChangeMessageDTO, String title);
}
