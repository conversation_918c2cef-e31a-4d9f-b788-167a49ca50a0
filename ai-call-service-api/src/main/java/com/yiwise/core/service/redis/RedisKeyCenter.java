package com.yiwise.core.service.redis;

import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.base.common.context.EnvEnum;
import com.yiwise.base.common.utils.date.MyDateUtils;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.model.bo.wph.WphMonitorConfigBO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.sms.VirtualPlatformEnum;
import com.yiwise.core.model.enums.stats.DimensionEnum;
import com.yiwise.core.model.enums.train.AlgorithmTrainTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 缓存的key
 *
 * <AUTHOR> yangdehong
 * @date : 2019-07-23 17:38
 */
public class RedisKeyCenter {

    private static final String PRE_FIX="yiwise:";

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    /**
     * 使用微伴的加微任务配置的员工id列表
     */
    public static String getAddWechatWeibanUserIds(Long tenantId, Long robotCallJobId) {
        return String.format("wechat:addFriend2:wb:phoneNumber:tenantId:%s:%s", tenantId, robotCallJobId);
    }

    /**
     * 使用微伴的加微任务手动重发时选择的员工id列表
     */
    public static String getAddWechatWeibanUserIdsManual(Long tenantId, Long robotCallJobId) {
        return String.format("wechat:addFriend2:wb:manual:phoneNumber:tenantId:%s:%s", tenantId, robotCallJobId);
    }

    /**
     * 外呼加微任务的token缓存
     */
    public static String getAddWechatTokens(Long tenantId, Long robotCallJobId) {
        return String.format("wechat:addFriend2:tenantId:%s:%s", tenantId, robotCallJobId);
    }

    public static String getAddWechatTokensManual(Long tenantId, Long robotCallJobId) {
        return String.format("wechat:addFriend2:manual:tenantId:%s:%s", tenantId, robotCallJobId);
    }

    /**
     * 音色克隆授权
     */
    public static String getMiniappTimbreKey(String code) {
        return String.format("miniapp:timbre:%s", code);
    }

    public static String getHelpCenterSearchWords() {
        return "helpCenter:searchWords";
    }


    public static String getAuthenticationKeyPattern() {
        return "tenant:authentication:*";
    }

    /**
     * 资质认证
     */
    public static String getAuthenticationKey(Long tenantId) {
        return String.format("tenant:authentication:%s", tenantId);
    }

    /**
     * 七鱼登录的账号
     */
    public static String isQiyuLogin(SystemEnum system, Long userId) {
        return String.format("user_token:login:qiyu:%s_%s", system, userId);
    }

    /**
     * 七鱼登录保存的token
     */
    public static String getQiyuTokenByUserId(SystemEnum system, Long userId) {
        return String.format("user_token:user_login_token:qiyu:%s_%s", system, userId);
    }

    /**
     * 七鱼登录保存的user
     */
    public static String getQiyuUserByToken(SystemEnum system, String token) {
        return String.format("user_token:user_login_token:qiyu:%s_%s", system, token);
    }

    /**
     * FAQ训练数据
     * {type} - list
     */
    public static String getFaqDataRedisKey() {
        return "TanyiTrain:FAQModel";
    }

    /**
     * Intent训练数据
     * {type} - list
     */
    public static String getIntentDataRedisKey() {
        return "TanyiTrain:IntentModel";
    }

    /**
     * 用户随机密码
     */
    public static String getRandomPasswordRedisKey(String phoneNumber) {
        return String.format("user_token:random_password:%s", phoneNumber);
    }

    /**
     * redis
     */
    public static String getTenantOpenApiTokenRedisKey(String appKey) {
        return String.format("open_api:token:appKey%s", appKey);
    }

    public static String getTenantOpenApiTokenRedisKeyPattern() {
        return "open_api:token:appKey*";
    }

    /**
     * token保存用户信息
     */
    public static String getUserLoginInfoRedisKey(SystemEnum system, String token) {
        return String.format("user_token:user_login_info:%s_%s", system, token);
    }

    /**
     * token的ZSet
     */
    public static String getTokenZsetRedisKey(SystemEnum system) {
        return String.format("user_token:user_login_info:%s", system);
    }

    /**
     * 保存用户登录所在ws的信息
     */
    public static String getUserWsLoginInfo(String name, EnvEnum currEnv, SystemEnum system) {
        return String.format("user_ws_info:%s:%s:%s", system.getDesc(), currEnv.name(), name);
    }

    /**
     * 单点时候用户保存的token
     */
    public static String getUserLoginTokenRedisKey(SystemEnum system, Long userId) {
        return String.format("user_token:user_login_token:%s_%s", system, userId);
    }

    /**
     * 浏览网页中的访客
     */
    public static String getTextConnectionListRedisKey(Long tenantId) {
        return String.format("text_service:visit_connection_list:%s", tenantId);
    }

    /**
     * 浏览网页中的访客session_id
     */
    public static String getVisitSessionRedisKey(String visitorInfoNum) {
        return String.format("text_service:visit_session:%s", visitorInfoNum);
    }

    /**
     * 浏览网页中的访客session_id
     */
    public static String getVisitNumRedisKey(String sessionId) {
        return String.format("text_service:visit_num:%s", sessionId);
    }

    /**
     * 获取今天AI坐席可用总量（在有效期内的AI坐席）的redisKey
     */
    public static String getTenantTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getTenantCallInTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantCallInRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getTenantCustomerServiceTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantCustomerServiceRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getTextServiceTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantTextServiceRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getYiBrainVoiceTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantYiBrainVoiceRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getYiBrainTextTodayRobotCount(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TenantYiBrainTextRobotCount:TenantId%s:%s", tenantId, today);
    }

    public static String getDistributorTenantTodayConcurrency(Long tenantId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("DisTenantConcurrency:tid%s:%s", tenantId, today);
    }

    /**
     * 用户表主键redisKey
     */
    public static String getUserPrimaryRedisKey(Number userId) {
        return String.format("User:userId:%s", userId);
    }

    public static String getUserPrimaryRedisKeyPattern() {
        return "User:userId:*";
    }

    /**
     * 客户表主键redisKey
     */
    public static String getTenantPrimaryRedisKey(Number tenantId) {
        return String.format("Tenant:tenantId:%s", tenantId);
    }

    public static String getWechatCpAddFriendTokenPrimaryRedisKey(Number wechatCpAddFriendTokenId) {
        return String.format("WechatCpAddFriendToken:wechatCpAddFriendTokenId:%s", wechatCpAddFriendTokenId);
    }

    public static String getTenantPrimaryRedisKeyPattern() {
        return "Tenant:tenantId:*";
    }

    public static String getTenantAutoSetRedisKeyPattern() {
        return "TenantAutoSet*";
    }

    /**
     * 供应商表主键redisKey
     */
    public static String getDistributorPrimaryRedisKey(Number distributorId) {
        return String.format("Distributor:distributorId:%s", distributorId);
    }

    /**
     * 供应商表主键redisKey
     */
    public static String getDistributorPrimaryRedisKeyPattern() {
        return "Distributor:distributorId:*";
    }

    /**
     * 电话卡（phoneNumber）主键redisKey
     */
    public static String getPhoneNumberPrimaryRedisKey(Number phoneNumberId) {
        return String.format("PhoneNumber:phoneNumberId:%s", phoneNumberId);
    }

    public static String getPhoneNumberPrimaryRedisKeyPattern() {
        return "PhoneNumber:phoneNumberId:*";
    }

    /**
     * 试用用户每天拨打电话的次数
     */
    public static String getTryFreeUserCall(String openId) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("TryFreeUserCall:openId%s:%s", openId, today);
    }

    /**
     * 阿里asr实时并发量
     */
    public static String getCountAsrConcurrencyKey() {
        return "AsrConcurrency:";
    }

    /**
     * 阿里热词组调用次数
     */
    public static String getAliVocabCountKey() {
        return "AliVocab:";
    }

    /**
     * 阿里自学习模型调用次数
     */
    public static String getAliSelfCountKey() {
        return "AliSelf:";
    }

    /**
     * 腾讯热词组调用次数
     */
    public static String getTencentVocabCountKey() {
        return "TencentVocab:";
    }

    /**
     * 腾讯自学习模型调用次数
     */
    public static String getTencentSelfCountKey() {
        return "TencentSelf:";
    }

    /**
     * 腾讯asr实时并发量
     */
    public static String getTencentAsrConcurrencyKey() {
        return "TencentAsrConcurrency";
    }

    /**
     * 一知asr实时并发量
     */
    public static String getCountYiwiseAsrConcurrencyKey() {
        return "YiwiseAsrConcurrency";
    }

    /**
     * asr总
     */
    public static String getYiwiseAsrServerConcurrencyCountKey() {
        return "AsrYiwiseServerConcurrencyCount";
    }


    /**
     * 短信验证码
     */
    public static String getVerificationCode(String phoneNumber) {
        return String.format("VerificationCode:phoneNumber%s", phoneNumber);
    }

    public static String getWhiteListRedisKeyPatten(Long tenantId) {
        return String.format("WhiteListPhoneNumber:TenantId%s:*", tenantId);
    }

    public static String getWechatLoginStatusKey(String callBackId) {
        return String.format("userLogin_%s", callBackId);
    }

    //BOSS 是否显示 审核
    public static String getBossShowCensorRedisKey(Long dialogFlowId) {
        return String.format("BossShowCensor:DialogFlowId%s", dialogFlowId);
    }

    public static String getIpWhiteListRedisKey() {
        return "WhiteList:IPWhiteList:Mask";
    }

    public static String getUserIsBuiltInSuperAdmin(SystemEnum system, Long userId) {
        return String.format("User:IsBuiltInSuperAdmin:%s:%s", system.getCode(), userId);
    }

    /**
     * 意向标签分组内容列表
     */
    public static String getIntentLevelTagDetailListRedisKey(Long intentLevelTagId) {
        return String.format("IntentLevelTagDetailList:IntentLevelTagId:%s", intentLevelTagId);
    }

    /**
     * 意向标签分组
     */
    public static String getIntentLevelTagRedisKey(Long intentLevelTagId) {
        return String.format("IntentLevelTag:IntentLevelTagId:%s", intentLevelTagId);
    }

    /**
     * 意向标签分组明细
     */
    public static String getIntentLevelTagDetailRedisKey(Long intentLevelTagId, Integer code) {
        return String.format("IntentLevelTagDetail:IntentLevelTagId:%s:%s", intentLevelTagId, code);
    }

    /**
     * 意向标签分组明细
     */
    public static String getIntentLevelTagDetailRedisKeyPatten() {
        return "IntentLevelTagDetail:IntentLevelTagId:*:*";
    }

    public static String getIntentLevelTagDetailListRedisKeyPatten() {
        return "IntentLevelTagDetailList:IntentLevelTagId:*";
    }

    public static String getIntentLevelTagRedisKeyPatten() {
        return "IntentLevelTag:IntentLevelTagId:*";
    }

    /**
     * crm意向标签默认分组
     */
    public static String getTenantDefaultIntentLevelTagRedisKey(Long tenantId) {
        return String.format("TenantDefaultIntentLevelTag:TenantId:%s", tenantId);
    }

    /**
     * boss意向标签默认分组
     */
    public static String getDistributorDefaultIntentLevelTagRedisKey(Long distributorId) {
        return String.format("DistributorDefaultIntentLevelTag:DistributorId:%s", distributorId);
    }

    public static String getTenantDefaultIntentLevelTagRedisKeyPatten() {
        return "TenantDefaultIntentLevelTag:TenantId:*";
    }

    public static String getDistributorDefaultIntentLevelTagRedisKeyPatten() {
        return "DistributorDefaultIntentLevelTag:DistributorId:*";
    }

    public static String getSpringBatchJobIdleCountRedisKey() {
        return "SpringBatchJob:IdleCount";
    }

    public static String getSpringBatchStatusRedisKey() {
        return "SpringBatchJob:EnabledStatus";
    }

    public static String getSpringBatchStopIdSetRedisKey() {
        return "SpringBatchJob:StoppedJobInstanceIdSet";
    }

	/**
     * ope意向标签默认分组
     */
    public static String getOPEDefaultIntentLevelTagRedisKey() {
        return "DistributorDefaultIntentLevelTag:OPE";
    }

    /**
     * 客户是否使用版本缓存
     */
    public static String getUserUsedThisVersionRedisKey(Long userId) {
        return String.format("TenantUsedThisVersion:UserId:%s", userId);
    }

    public static String getUserUsedThisVersionRedisKeyPatten() {
        return "TenantUsedThisVersion:UserId:*";
    }

    public static String getCallInStaffInfoListKeyByStaffGroupId(Long staffGroupId) {
        return String.format("CallIn:StaffInfoList:CsStaffGroupId:%s", staffGroupId);
    }

    /**
     * ai坐席组里面ai接待个数
     */
    public static String getCallInAiReceptionConcurrencyCount(Long csStaffGroupId) {
        return String.format("CallIn:AiReceptionConcurrencyCount:CsStaffGroupId:%s", csStaffGroupId);
    }

    public static String getCallInStaffLastHangUpTime(Long staffId) {
        return String.format("CallIn:StaffLastHangUpTime:CsStaffInfoId:%s", staffId);
    }

    public static String getCallInStaffIsOnline(Long staffId) {
        return String.format("CallIn:StaffIsOnLine:CsStaffInfoId:%s", staffId);
    }

    public static String getCallInStaffIsCalling(Long staffId) {
        return String.format("CallIn:StaffIsCalling:CsStaffInfoId:%s", staffId);
    }

    public static String getIntentMessageStatusCount(Long tenantId, Long robotCallJobId, SendMessageStatusEnum sendStatus) {
        return String.format("IntentMessageStatusCount:TenantId:%s:%s:%s", tenantId, robotCallJobId, sendStatus.name());
    }

    public static String getCallJobCallDetailRedisKey(String identifyId) {
        return String.format("CallDetail:JobId:%s", identifyId);
    }

    public static String getCallJobCsCallTransferMonitorFlagRedisKey(Long robotCallJobId, String identifyId) {
        return String.format("CsSeatMonitor:JobId:%s:%s", robotCallJobId, identifyId);
    }

    public static String getCallJobCsCallTransferHangUpRedisKey(Long robotCallJobId, String identifyId) {
        return String.format("CsSeatHangUp:JobId:%s:%s", robotCallJobId, identifyId);
    }

    public static String getCsStaffDealTime(Long csStaffId) {
        return String.format("CsStaffDealTime:csStaffId:%s", csStaffId);
    }

    public static String getCsStaffReceptionDealTime(Long csStaffId) {
        return String.format("getCsStaffReceptionDealTime:csStaffId:%s", csStaffId);
    }

    public static String getBatchJobCsCount(Long csBatchCallJobId) {
        return String.format("BatchJobCsCount:csBatchCallJobId:%s", csBatchCallJobId);
    }

    public static String getCallOutCsCount() {
        return "CallOutCsCount:all";
    }

    /**
     * 预测试外呼任务总量
     */
    public static String getBatchJobTotalCount(Long csBatchCallJobId) {
        return String.format("BatchJobTotalCount:csBatchCallJobId:%s", csBatchCallJobId);
    }

    /**
     * 预测试外呼任务已完成数量
     */
    public static String getBatchJobCompleteCount(Long csBatchCallJobId) {
        return String.format("BatchJobCompleteCount:csBatchCallJobId:%s", csBatchCallJobId);
    }

    public static String getCsSeatListRedisKey(Long tenantId) {
        return String.format("CsSeatList:TenantId:%s", tenantId);
    }

    public static String getCallPolicyGroupLastModifyTime(Long policyGroupId) {
        return String.format("CallPolicyGroupLastModify:PolicyGroupId:%s", policyGroupId);
    }

    public static String getCustomerPersonExtraFieldListPatten() {
        return "CustomerPersonExtraFieldList:TenantId:*";
    }

    /**
     * 系统维护状态 出现重大bug时使用 任务不可运行
     */
    public static String getSystemMaintain() {
        return "SystemMaintain";
    }

    /**
     * 网关状态
     */
    public static String getGatewayStatusKey(String deviceId) {
        return String.format("GatewayStatus:DID:%s", deviceId);
    }

    /**
     * 线路状态
     */
    public static String getPhoneNumberStatusKey(Long phoneNumberId) {
        return String.format("PhoneNumberStatus:PID:%s", phoneNumberId);
    }

    /**
     * 获取所有线路状态
     */
    public static String getPhoneNumberStatusKeyPatten() {
        return "PhoneNumberStatus:PID:*";
    }

    /**
     * 百度TTS调用数
     */
    public static String getCallBaiDuTTSKey(String time) {
        return String.format("BaiDuTTSCount:%s:", time);
    }

    /**
     * 算法部TTS调用数
     */
    public static String getCallYiwiseTTSKey(String time) {
        return String.format("YiwiseTTSCount:%s", time);
    }

    /**
     * 算法部TTS2调用数
     */
    public static String getCallYiwiseTTS2Key(String time) {
        return String.format("YiwiseTTS2Count:%s", time);
    }

    /**
     * 华为TTS调用数
     */
    public static String getCallHuaweiTTSKey(String time) {
        return String.format("HuaweiTTSCount:%s", time);
    }

    /**
     * aliTTS调用数
     */
    public static String getCallAliyunTTSKey(String time) {
        return String.format("AliyunTTSCount:%s", time);
    }

    /**
     * 统计每秒通过网关要外呼的数量
     */
    public static String getConcurrencyCalledCountByGatewayKey(String prefix, String time) {
        return String.format("concurrencyCalledCount:%s:%s", prefix, time);
    }

    /**
     * 网关是否清空
     */
    public static String getGatewayClearedKey(String deviceId) {
        return String.format("gatewayCleared:DID:%s", deviceId);
    }

	/**
     * 线路状态页面是否展示
     */
    public static String getLineStatsViewKey(Long tenantId) {
        return String.format("LineStats:ViewEnabled:TID:%s", tenantId);
    }

    /**
     * 线路监控
     */
    public static String getLineStatsKey(Long phoneNumberId, int twentyFourHour) {
        return String.format("LineStats:PID:%s:%s", phoneNumberId, twentyFourHour);
    }

	public static String CsEslRecordMap(Long tenantId) {
        return String.format("CsEslRecordMap:%s", tenantId);
    }

    public static String getCsSeatListRedisKeyPattern() {
        return "CsSeatList:TenantId:*";
    }

    public static String getUserWsLoginInfoPattern(EnvEnum currEnv, SystemEnum system) {
        return String.format("user_ws_info:%s:%s:*", system.getDesc(), currEnv.name());
    }

    public static String getPolicyGroupCyclicCount(Long tenantId, Long robotCallJobId) {
        return String.format("policyGroupCyclicCount:%s:%s", tenantId, robotCallJobId);
    }

	public static String getAsrModelRecord(String recordId) {
        return String.format("AsrModelRecord:%s", recordId);
    }

    public static String getAudioMinAppTokenNew(Integer currEnv, String system) {
        return system + "-" + "AudioMinAppTokenKey" + "-" + currEnv;
    }

	public static String getWeiBaoCallStatus(String callSid) {
        return String.format("WeiBaoCallStatus:%s", callSid);
    }

    public static String getWeiBaoCampaignCode(Long robotCallJobId) {
        return String.format("WeiBaoCampaignCode:%s", robotCallJobId);
    }

    /**
     * 短信验证码次数验证
     */
    public static String getVerificationCodeCount(String phoneNumber) {
        return String.format("VerificationCodeCount:phoneNumber%s", phoneNumber);
    }

    /**
     * 短信验证码次数验证
     */
    public static String getCaptchaCode(String key) {
        return String.format("CaptchaCode:PH%s", key);
    }

    public static String getCsStaffAutoAnswer(Long userId) {
        return String.format("CsStaffAutoAnswer:%s", userId);
    }

    public static String getCsStatus(Long csStaffId) {
        return String.format("CsStatus:%s", csStaffId);
    }

    public static String getCsWorkStatus(Long csStaffId) {
        return String.format("CsWorkStatus:%s", csStaffId);
    }

    public static String getCsMonitorInfo(Long csStaffId) {
        return String.format("CsMonitorInfo:%s", csStaffId);
    }

    public static String getCsFirstLoginTime(Long csStaffId) {
        return String.format("CsFirstLoginTime:%s", csStaffId);
    }

    public static String getCsOperationStatus(Long csStaffId) {
        return String.format("CsOperationStatus:%s", csStaffId);
    }

    public static String lastStaffStatus(Long csStaffId) {
        return String.format("lastStaffStatus:staffId:%s", csStaffId);
    }

    public static String groupQueueCount(Long csStaffGroupId) {
        return String.format("StaffGroupQueueCount:groupId:%s", csStaffGroupId);
    }

	public static String getCsConnectKey(Long csStaffId) {
        return String.format("CsConnect:%s", csStaffId);
    }

    public static String getCsConnectKeyRedisKeyPattern() {
        return "CsConnect:*";
    }

    public static String getCsSessionKey(Long userId) {
        return String.format("CsSession:%s", userId);
    }

    public static String getTopMsgKey(Long userId) {
        return String.format("TopMsg:%s", userId);
    }

    public static String HumanToCsCallInfo(String callId) {
        return String.format("HumanToCsCallInfo:%s", callId);
    }

    public static String ReceptionQueueCount(Long callInReceptionId) {
        return String.format("ReceptionQueueCount:%s", callInReceptionId);
    }

    public static String ReceptionCallCount(Long callInReceptionId) {
        return String.format("ReceptionCallCount:%s", callInReceptionId);
    }

    public static String getTransferCallKey(String identifyId) {
        return String.format("TransferCall:%s", identifyId);
    }

    public static String getTransferCallInfoKey(Long tenantId, Long csStaffId) {
        return String.format("TransferCallInfo:%s:%s", tenantId, csStaffId);
    }

    public static String getCsBatchHangUpKey(Long csRecordId) {
        return String.format("CsBatchHangUp:%s", csRecordId);
    }

    /**
     * 质检-训练标识-客户规则
     */
    public static String getQcCustomerRuleTagRedisKey() {
        return "QcTrain:Positive:Customer";
    }

    /**
     * 质检-训练标识-客服规则
     */
    public static String getQcStaffRuleTagRedisKey() {
        return "QcTrain:Positive:Staff";
    }

	public static String getCsHangUpKey(String identifyId) {
        return String.format("CsHangUp:%s", identifyId);
    }

    public static String getCsAnswerKey(String identifyId) {
        return String.format("CsAnswer:%s", identifyId);
    }

    public static String getCallInHeaderEslKey(String identifyId) {
        return String.format("CallInHeaderEsl:%s", identifyId);
    }

    public static String getTextStaffGroupQueueKey(Long csStaffGroupId) {
        return String.format("TextStaffGroupQueue:%s", csStaffGroupId);
    }

    public static String getTextVisitorQueueMap(Long tenantId) {
        return String.format("TextVisitorQueueMap:%s", tenantId);
    }

    public static String getCallJobRegisterHostKey(String hostPattern) {
        return String.format("calljobRegisterHost:%s", hostPattern);
    }

    // 客户最后外呼时间
    public static String getTenantLastCallOutTime(Long tenantId) {
        return String.format("LastCallOutTime:tenantId:%s", tenantId);
    }

    // 客户最后人工外呼时间
    public static String getTenantCsLastCallOutTime(Long tenantId) {
        return String.format("LastCsCallOutTime:tenantId:%s", tenantId);
    }

    public static String getTenantAutoSetLine(Long tenantId, Long phoneNumberId) {
        return String.format("TenantAutoSetLine:%s:%s", tenantId, phoneNumberId);
    }

    public static String getTenantLineStatsAnswerTotalToRedis(Long tenantId, int hour, int minute, Long phoneNumberId) {
        return String.format("TenantLineStatsAnswerTotalToRedis:%s:%s:%s:%s", tenantId, hour, minute, phoneNumberId);
    }

    public static String getTenantLineStatsCallTotalToRedis(Long tenantId, int hour, int minute, Long phoneNumberId) {
        return String.format("TenantLineStatsCallTotalToRedis:%s:%s:%s:%s", tenantId, hour, minute, phoneNumberId);
    }

    //tenant下的所有线路拨打总数
    public static String getTenantLineStatsCallTotalToRedisKeyPattern(Long tenantId, int hour, int minute) {
        return String.format("TenantLineStatsCallTotalToRedis:%s:%s:%s", tenantId, hour, minute);
    }

    //tenant下的所有线路接听总数
    public static String getTenantLineStatsAnswerTotalToRedisKeyPattern(Long tenantId, int hour, int minute) {
        return String.format("TenantLineStatsAnswerTotalToRedis:%s:%s:%s", tenantId, hour, minute);
    }

    public static String getTenantAutoSetMessage(Long tenantId) {
        return String.format("TenantAutoSetMessage:%s", tenantId);
    }

    public static String getTenantAutoSetQc(Long tenantId) {
        return String.format("TenantAutoSetQc:%s", tenantId);
    }

    public static String getTenantAutoSetAllCountFare(Long tenantId) {
        return String.format("TenantAutoSetAllCountFare:%s", tenantId);
    }

    public static String getTenantAutoSetDistributorAccountFare(Long tenantId) {
        return String.format("TenantAutoSetDistributorAccountFare:%s", tenantId);
    }

    public static Object getTenantAutoWxYes(Long tenantId, TenantAutoSetTypeEnum tenantAutoSetTypeEnum, Long phoneNumberId) {
        return String.format("TenantAutoWxYes:%s:%s:%s", tenantId, tenantAutoSetTypeEnum.getDesc(), phoneNumberId);
    }

    public static Object getTenantAutoWarnYes(Long tenantId, TenantAutoSetTypeEnum tenantAutoSetTypeEnum, Long phoneNumberId) {
        return String.format("TenantAutoWarnYes:%s:%s:%s", tenantId, tenantAutoSetTypeEnum.getDesc(), phoneNumberId);
    }

    public static Object getTenantAutoWarnOpeYes(Long tenantId, TenantAutoSetTypeEnum tenantAutoSetTypeEnum, Long phoneNumberId) {
        return String.format("TenantAutoWarnOpeYes:%s:%s:%s", tenantId, tenantAutoSetTypeEnum.getDesc(), phoneNumberId);
    }

    public static Object getTenantLineAnswerRate(Long tenantId, TenantLineAnswerRateEnum tenantLineAnswerRateEnum, Long phoneNumberId) {
        return String.format("TenantLineAnswerRate:%s:%s:%s", tenantId, tenantLineAnswerRateEnum.getDesc(), phoneNumberId);
    }

    public static String getTenantLineAnswerRateQuery(Long tenantId, TenantLineAnswerRateEnum tenantLineAnswerRateEnum, Long phoneNumberId) {
        return String.format("getTenantLineAnswerRateQuery:%s:%s:%s", tenantId, tenantLineAnswerRateEnum.getDesc(), phoneNumberId);
    }

    public static String getTenantTodayForbiddenCount(Long tenantId, LocalDate localDate) {
        return String.format("TenantTodayForbiddenCount:%s:%s", tenantId, localDate);
    }

    public static String callInNoticePrincipal() {
        return "callInNoticePrincipals";
    }

	public static String getTenantAutoSetAllLine(Long tenantId) {
        return String.format("TenantAutoSetAllLine:%s", tenantId);
    }

    public static String getCsBatchCallJobRecordDeal(Long csBatchCallJobRecordId) {
        return String.format("CsBatchCallJobRecordDeal:%s", csBatchCallJobRecordId);
    }

    public static String getESignTokenKey() {
        return "ESign:Token";
    }

    public static String getESignOrgCreatorUser(Long tenantId) {
        return String.format("ESign:Org:Creator:%s", tenantId);
    }

    public static String getCsLoginSecret(String secret) {
        return String.format("cs_secret:%s", secret);
    }

    public static String loginLocked(String phoneNumber, String ip, SystemEnum system) {
        if (StringUtils.isBlank(ip)) {
            ip = "default";
        }
        String systemName = system == null ? "default" : system.name();
        return String.format("loginLocked:system:%s:ip:%s:phoneNumber:%s", systemName, ip, phoneNumber);
    }

    public static String passwordChangeLocked(String phoneNumber) {

        return String.format("passwordChangeLocked:phoneNumber:%s", phoneNumber);
    }

    public static String loginFailedCount(String phoneNumber, String ip, SystemEnum system) {
        if (StringUtils.isBlank(ip)) {
            ip = "default";
        }
        String systemName = system == null ? "default" : system.name();
        return String.format("loginFailedCount:system:%s:ip:%s:phoneNumber:%s", systemName, ip, phoneNumber);
    }

    public static String passwordChangeFailedCount(String phoneNumber) {
        return String.format("loginFailedCount:phoneNumber:%s", phoneNumber);
    }

    public static String skipLoginWaitKey(String phoneNumber) {
        return String.format("noLoginWait:phoneNumber:%s", phoneNumber);
    }

    public static String getSunfengDailyCountKey() {
        return "sunfengDailyCount";
    }

    public static String getSunfengDevDailyCountKey() {
        return "sunfengDevDailyCount";
    }

    public static String getSunfengCallerPhoneMarkKey(String phoneNumber) {
        return String.format("sunfengCallerPhone:%s", phoneNumber);
    }

	public static String getJobWechatPushKey(Long robotCallJobId, int index) {
        return String.format("WechatPush:Job:%s:index:%s", robotCallJobId, index);
    }

    public static String getJobCompleteKey(Long robotCallJobId, Integer version) {
        return String.format("JobComplete:Job:%s:version:%s", robotCallJobId, version);
    }

    public static String getJobTaskQueueKey(Long robotCallJobId) {
        return String.format("JobTaskQueue:%s", robotCallJobId);
    }

    public static String getJobTaskQueueLockKey(Long robotCallJobId) {
        return String.format("JobTaskQueueLock:%s", robotCallJobId);
    }

    /**
     * 线路每日外呼限制
     */
    public static String telDailyCountKey(Long phoneNumberId, LocalDate date, String calledPhoneNumber) {
        return String.format("telLimit:p:%d:d:%s:c:%s", phoneNumberId, date.toString(), calledPhoneNumber);
    }

    public static String getTransferGroupRecord(Long callRecordId) {
        return String.format("TransferGroupRecord:%s", callRecordId);
    }

    public static String getCallInTransferGroupRecord(String callInString) {
        return String.format("TransferCallInGroupRecord:%s", callInString);
    }

    public static String getTrainButtonEnabledRedisKey(Long tenantId, Long botId, AlgorithmTrainTypeEnum trainType) {
        return String.format("TrainButtonEnabled:TID_%s:BID_%s:%s", tenantId, botId, trainType);
    }

    public static String getTrainingStatusRedisKey(Long tenantId, Long botId, AlgorithmTrainTypeEnum trainType) {
        return String.format("TrainingStatus:TID_%s:BID_%s:%s", tenantId, botId, trainType);
    }

    /**
     * Excel导入客户import step的退出状态
     */
    public static String getImportExecutionExitStatusKey(Long jobInstanceId) {
        return "getImportExecutionExitStatus:" + jobInstanceId;
    }

    /**
     * 租户的实时并发数
     */
    public static String tenantConcurrencyNow(Long tenantId) {
        return String.format("tenantConcurrencyNow:tenantId:%d", tenantId);
    }

    public static String getIsvCallBackError(Long tenantId) {
        return "IsvCallBackError:" + tenantId;
    }

    public static String getIsvCallBackErrorNew(Long tenantId) {
        return "IsvCallBackErrorNew:" + tenantId;
    }

    public static String getIsvSmsCallBackError(Long tenantId) {
        return "IsvSmsCallBackError:" + tenantId;
    }

    public static String getIsvSmsCallBackErrorNew(Long tenantId) {
        return "IsvSmsCallBackErrorNew:" + tenantId;
    }

    public static String getIsvCallBackSkip(Long tenantId) {
        return "IsvCallBackSkip:" + tenantId;
    }

    public static String getIsvCallBackSkipNew(Long tenantId) {
        return "IsvCallBackSkipNew:" + tenantId;
    }

    public static String getIsvSmsCallBackSkip(Long tenantId) {
        return "IsvSmsCallBackSkip:" + tenantId;
    }

    public static String getIsvSmsCallBackSkipNew(Long tenantId) {
        return "IsvSmsCallBackSkipNew:" + tenantId;
    }

    public static String getUserConcernIntentLevelRedisKey(Long userId, Long intentLevelTagId) {
        return String.format("UserConcernIntentLevelInfo:%s:%s", userId, intentLevelTagId);
    }

    public static String getXiaokeToken() {
        return "XiaokeCorpAccessToken";
    }

	/**
     * tenant的艾客token, 会过期
     */
    public static String aikeToken(Long tenantId) {
        return String.format("AikeToken:%d", tenantId);
    }

    /**
     * 外呼任务加微的临时计数
     */
    public static String callJobAddFriendCount(Long tenantId, Long robotCallJobId) {
        return String.format("CallJobAddFriend:%s:%s", tenantId, robotCallJobId);
    }

    public static String getCallInServerHeartbeatStatusKey() {
        return "CallInServerHeartbeat";
    }

    public static String getCallInServerOfflineKey(String ip) {
        return String.format("CallInServerOffline_%s", ip);
    }

    /**
     * 小程序access_token
     */
    public static String wechatMiniappAccessToken(String appId) {
        return String.format("WechatMiniappAccessToken:appId:%s", appId);
    }

    /**
     * 欧莱雅的token获取
     */
    public static String getLorealTokenKey(String appId) {
        return String.format("lorealToken:appId:%s", appId);
    }

	/**
     * 一知小号TelX
     */
    public static String huaweiTelX(String telX) {
        return String.format("huawei:telX:%s", telX);
    }

    public static String getCallInTransferCs(String callInRecordingStr) {
        return String.format("CallInTransferCs:%s", callInRecordingStr);
    }

    public static String getCallInTransferCsSeatDTO(String callInRecordingStr) {
        return String.format("getCallInTransferCsSeatDTO:%s", callInRecordingStr);
    }

    /**
     * 纷享客户csmId
     */
    public static String getCsmIdRedisKey(Integer monthValue, String customerId, String objectApiName) {
        return objectApiName + ":" + customerId + ":" + monthValue.toString();
    }


    /**
     * 质检-测试集-客户规则
     */
    public static String getQcCustomerRuleTagTestRedisKey(Long tenantId, Long tagId) {
        return "QcTrain:Test:Customer:" + tenantId + ":" + tagId;
    }

    /**
     * 质检-测试集-客服规则
     */
    public static String getQcStaffRuleTagTestRedisKey(Long tenantId, Long tagId) {
        return "QcTrain:Test:Staff:" + tenantId + ":" + tagId;
    }


    public static String getBlackListTotalCountKey(Long callOutBlackListId) {
        return String.format("BlackListTotalCount:%s", callOutBlackListId);
    }

    public static String getBlackListFilteredCountKey(Long callOutBlackListId) {
        return String.format("BlackListFilteredCount:%s", callOutBlackListId);
    }

    public static String getOpeUserLoginAiccRedisKey(Long opeUserId) {
        return String.format("OpeUserLoginAicc:%s", opeUserId);
    }

    public static String getJobFilteredTaskReadStatusKey(Long robotCallJobId) {
        return String.format("JobFilteredTaskReadStatus:%d", robotCallJobId);
    }

    public static String getShortUrlPVKey(LocalDate date) {
        return String.format("shortUrl:%s", MyDateUtils.formatLocalDateYYYYMMDD(date));
    }

    public static String getShortUrlUVKey(LocalDate date, String shortUrl) {
        return String.format("shortUrl:%s:%s", MyDateUtils.formatLocalDateYYYYMMDD(date), shortUrl);
    }

    /**
     * 语料训练信号
     */
    public static String getTrainDataSignalRedisKey() {
        return String.format("TrainDataSignal:%s", CommonApplicationConstant.ALGORITHM_REGISTER_ENV);
    }

    /**
     * 新日报icon提示
     */
    public static String dailyViewKey() {
        return "DailyView";
    }

    /**
     * 新日报弹窗提示
     */
    public static String dailyViewNoticeKey() {
        return "DailyViewNotice";
    }

    /**
     * 用户已选择不弹窗
     */
    public static String dailyViewNotNoticeKey(Long tenantId, Long userId) {
        return String.format("dailyViewNotNoticeKey:%d:%d", tenantId, userId);
    }

    public static String getSystemConcurrentWaringSettingKey() {
        return "SystemConcurrentWaringSettingPermanent";
    }

    public static String getDialogFlowQrCode(String qrCodeId) {
        return String.format("DialogFlowQrCode:%s", qrCodeId);
    }

    public static String getRobotCallJobQrCode(String qrCodeId) {
        return String.format("RobotCallJobQrCode:%s", qrCodeId);
    }

    public static String getDialogFlowQrCodeLimit(String qrCodeId, String phoneNumber) {
        return String.format("DialogFlowQrCodeLimit:%s:%s", qrCodeId, phoneNumber);
    }

    public static String getTryDialogFlowCallLock(String phoneNumber) {
        return String.format("TryDialogFlowCallLock:%s", phoneNumber);
    }

    public static String getQRCodeImportLock(String phoneNumber) {
        return String.format("QRCodeImportLock:%s", phoneNumber);
    }

    public static String getTryDialogFlowDefaultPhoneNumber() {
        return "DialogFlowDefaultPhoneNumber";
    }

    public static String getSmsTestRedisKey(Long userId) {
        return String.format("SmsTest:UID_%s", userId);
    }

    public static String getAbTestJobKeyLock(Long abTestId, DimensionEnum globalParam) {
        return String.format("AbTestJobKeyLock:%d:%s", abTestId, globalParam);
    }

    public static String getSyncDialogOptionIdKeyLock(Long dialogFlowId) {
        return String.format("SyncDialogOptionIdKeyLock:%d", dialogFlowId);
    }

    public static String openApiImportCustomerToJobLock(Long robotCallJobId, String phoneNumber) {
        return String.format("ImportLock:JobId:phone:%d:%s", robotCallJobId, phoneNumber);
    }

    /**
     * 语气词承接的静默区间
     */
    public static String getModalParticleSilenceInterval() {
        return "ModalParticleSilenceInterval";
    }

    public static String getPhoneNumberThirdPartKey() {
        return "Third_part_variableSet";
    }

	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * tenant统计字段修改, set的key
     */
    public static String tenantStatsFieldsChange(LocalDate date) {
        return String.format("TenantStatsFieldsChange:%s", date.format(DATE_FORMATTER));
    }

    public static String getQuanLiangAddWechatToken() {
        return "quanLiangAddWechatToken";
    }


	/**
     * 实时监控的客户默认分组
     */
    public static String getCallJobWholeMonitorDefaultTenantGroup(Long userId) {
        return "CallJobWholeMonitorDefaultTenantGroup:" + userId;
    }

    /**
     * 5分钟内线路外呼总数
     */
    public static String getPhoneNumberRealTimeTotalCount(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeTotalCount:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内线路外呼接收到sip的通话数
     */
    public static String getPhoneNumberRealTimeRevSip(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeRevSip:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内线路外呼接听数
     */
    public static String getPhoneNumberRealTimeAnswer(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeAnswer:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内线路应收包总数
     */
    public static String getPhoneNumberRealTimeRecRtp(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeRecRtp:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内线路丢包数
     */
    public static String getPhoneNumberRealTimeMissRtp(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeMissRtp:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内通话总时长
     */
    public static String getPhoneNumberRealTimeDuration(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeDuration:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 5分钟内线路通话振铃总时长
     */
    public static String getPhoneNumberRealTimeRing(Long phoneNumberId, String timestampM) {
        return String.format("PhoneNumberRealTimeRing:%d:%s", phoneNumberId, timestampM);
    }

    /**
     * 某日外呼过的线路
     */
    public static String getUsedPhoneNumberByDate(String date) {
        return String.format("UsedPhoneNumberByDate:%s", date);
    }

    public static String getPhoneNumberMoniorTimeRange() {
        return "PhoneNumberMoniorTimeRange";
    }

    /**
     * 精线索签名
     */
    public static String getJxsSign(Long tenantId) {
        return "JxsSign:" + tenantId;
    }

    /**
     * 技术预警是否沉默
     */
    public static String getTechAlertSilence(String topic) {
        return "TechAlertSilence:" + topic;
    }

    /**
     * 客户自有方案加密接口地址
     */
    public static String getTenantCommonDecryptUrlKey(Long tenantId) {
        return "TenantCommonDecryptUrlKey:" + tenantId;
    }

	/**
     * RSA加密客户公钥缓存
     */
    public static String getTenantRsaPublicKey(Long tenantId) {
        return "TenantRsaPublicKey:" + tenantId;
    }

    public static String getTenantRsaPublicKeyPattern() {
        return "TenantRsaPublicKey:*";
    }

    public static String getTenantRsaPrivateKey(Long tenantId) {
        return "TenantRsaPrivateKey:" + tenantId;
    }

    public static String getTenantRsaPrivateKeyPattern() {
        return "TenantRsaPrivateKey:*";
    }

    /**
     * 飞书access_token
     */
    public static String getFeishuAccessToken() {
        return "FeishuAccessToken";
    }

    /**
     * 飞书open_id
     */
    public static String getFeishuOpenId() {
        return "FeishuOpenId";
    }

    public static String getSmsJobImport(Long smsJobId, String phoneNumber) {
        return String.format("SmsJobImport:%d:%s", smsJobId, phoneNumber);
    }

	/**
	 * 外呼计划正在删除计划内去重数据
	 */
	public static String deleteTaskUniqueLockKey(Long callOutPlanId) {
    	return String.format("TaskUniqueDeleting:%d", callOutPlanId);
    }

	/**
	 * 外呼计划正在删除计划内未呼数据
	 */
    public static String deleteTaskPlanLockKey(Long callOutPlanId) {
	    return String.format("TaskPlanDeleting:%d", callOutPlanId);
    }

    /**
     * 客户短信显示单价
     */
    public static String getTenantSmsShowPriceKey(Long tenantId) {
        return String.format("TenantSmsShowPriceKey:%d", tenantId);
    }

	/**
	 * 外呼任务正在删除未呼客户
	 */
	public static String deleteTaskLockKey(Long robotCallJobId) {
		return String.format("TaskDeleting:%d", robotCallJobId);
	}

    /**
     * socket.io的sessionId和user的对应关系
     */
    public static String getSocketIOUserToSessionKey(Long userId) {
        return String.format("SocketIOUserToSessionKey:%s", userId);
    }

    /**
     * 保存socket.io客户端和具体哪个socket对应的关系
     */
    public static String getSocketIOClientInfo(String sessionId) {
        return String.format("SocketIOClient:%s", sessionId);
    }

    /**
     * 用于保存token和登陆用户的信息，仅用于socketio
     */
    public static String getUserLoginInfoForSocketIO(String token) {
        return String.format("UserTokenSocketIO:UserLoginInfo:%s", token);
    }

    public static String getSocketIOIpToSessionKey(String sessionId) {
        return String.format("SocketIO:SocketIOIpToSessionKey:%s", sessionId);
    }

    public static String getUserLoginInfo(String token) {
        return String.format("UserLoginInfo:%s", token);
    }

    /**
     * 短信内部结算单价
     */
    public static String getSmsInternalSettlementFareKey() {
        return "SmsInternalSettlementFare";
    }

    public static String getBatchJobCountKey(Long jobInstanceId){
        return String.format("JobInstanceId:%s", jobInstanceId);
    }

    public static String getCallJobUpdateBeat(Long robotCallJobId) {
        return String.format("CallJobUpdateBeat:%s", robotCallJobId);
    }

    public static String getSubCallJobUpdateBeat(Long robotCallJobId) {
        return String.format("SubCallJobUpdateBeat:%s", robotCallJobId);
    }

    /**
     * 批量手动回调大于1000条 正在执行的客户数量
     */
    public static String getBatchManualCallbackMaxTenantCount(){
        return "BatchManualCallbackTenantCount";
    }

    /**
     * ISV表信息redisKey
     */
    public static String getIsvInfoAdminRedisKey(Long tenantId) {
        return String.format("IsvInfo:tenantId:%s", tenantId);
    }

    /**
     * 早期媒体算法并发redisKey
     */
    public static String getEarlyMediaConcurrencyCountKey() {
        return "AlgorithmConcurrencyCount:EARLY_MEDIA:HASH";
    }

    /**
     * 分机号模型算法并发redisKey
     */
    public static String getExtensionConcurrencyCountKey() {
        return "AlgorithmConcurrencyCount:EXTENSION";
    }

    /**
     * 算法并发预警配置redisKey
     */
    public static String getAlgorithmConcurrentWaringSettingKey(AlgorithmConcurrencyTypeEnum algorithmConcurrencyType) {
        return String.format("AlgorithmConcurrentWaringSettingPermanent:%s", algorithmConcurrencyType);
    }

    public static String getUserNotificationKey(Long userId) {
        return String.format("DisplayUserPwdNotification:%s", userId);
    }

    public static String getUserPwdExpireKey(Long userId) {
        return String.format("UserPwdExpire:%s", userId);
    }

    public static String getMkyBeforeLoginKey(Long userId) {
        return String.format("NewMkyBeforeLogin:%s", userId);
    }

    public static String getAntImportCustomerKey(String requestId) {
        return "Ant:" + requestId;
    }

    /**
     * 外呼任务1分钟的实时数据
     */
    public static String getRobotCallJobRealTimeCall1MinRedisKey(Long robotCallJobId, String timestamp1M) {
        return "Call1Min:" + robotCallJobId + ":"+ timestamp1M;
    }

    /**
     * 外呼任务5分钟的实时数据
     */
    public static String getRobotCallJobRealTimeCall5MinRedisKey(Long robotCallJobId, String timestamp5M) {
        return "Call5Min:" + robotCallJobId + ":"+ timestamp5M;
    }

    /**
     * 话术外呼5分钟的实时数据
     */
    public static String getDialogCallRealTimeCall5MinRedisKey(Long robotCallJobId, String timestamp5M) {
        return "DialogCall5Min:" + robotCallJobId + ":"+ timestamp5M;
    }

    /**
     * 外呼过滤5分钟的实时数据
     */
    public static String getRobotCallJobRealTimeFilter5MinRedisKey(Long robotCallJobId, String timestamp5M) {
        return "Filter5Min:" + robotCallJobId + ":"+ timestamp5M;
    }

    /**
     * 短信退费使用
     */
    public static String getCallRecordStatsQueryForSmsReturn(String sid) {
        return "QuerySms:" + sid;
    }

    public static String getTenantTodayRtpMissRecordCount(LocalDate today, Long tenantId) {
        return String.format("TenantTodayRtpMissRecordCount:%s:%s", MyDateUtils.formatLocalDateYYYYMMDD(today), tenantId);
    }

    public static String getRtpMissPushFeishuKey(Long tenantId) {
        return String.format("RtpMissPushFeishuKey:%s", tenantId);
    }

    public static String getDingDingAccessTokenKey(String key) {
        return String.format("DingDingAccessToken:%s", key);
    }

    /**
     * 短信运营商回调重试次数
     */
    public static String getSmsReportRetryCountRedisKey(String sid) {
        return "smsReportRetryCount:" + sid;
    }

    /**
     * 短信补发
     */
    public static String getJobReSendSmsRedisKey(Long robotCallJobId){
        return String.format("JobReSendSmsKey:%s",robotCallJobId);
    }

    public static String getDouyinToken(VirtualPlatformEnum virtualPlatformEnum, String shopName, Long tenantId){
        return String.format("DouyinTokenInfo:%s:%s:%s", virtualPlatformEnum,shopName,tenantId);
    }

    public static String getPhoneNumberBatchEditGw(Integer id) {
        return String.format("phoneNumberBatchEditGw:%d", id);
    }

    public static String getCarrierBatchEditGw(String carrierId) {
        return String.format("carrierBatchEditGw:%s", carrierId);
    }

    /**
     * 拉取任务QPS限制
     */
    public static String getTaskQpsLimit() {
        return String.format("TaskQps:%s", dateFormatter.format(LocalDateTime.now()));
    }

    /**
     * 线路caps限制
     */
    public static String getPhoneNumberCapsLimit(Long phoneNumberId) {
        return "caps:" + phoneNumberId + ":" + dateFormatter.format(LocalDateTime.now());
    }

	/**
	 * ProductService#selectUnitPriceByProductId缓存key
	 */
	public static String selectUnitPriceByProductIdKey(Long productId) {
    	return String.format("Product:selectUnitPrice:%d", productId);
    }

	/**
	 * ProductService#selectUnitPriceByProductId缓存key
	 */
	public static String isInArrears(Long tenantId, TenantAccountEnum tenantAccount) {
    	return String.format("TenantAccount:isInArrears:%d:%s", tenantId, tenantAccount.getCode());
    }

    public static String getCustomerReqestKey(String idempotentId,Long tenantId) {
        return String.format("importCustomerRequestId:%s:%s",idempotentId,tenantId);
    }

    public static String getJdCacheRandomValue(String randomValue){
        return String.format("jdCacheRandomValue:%s",randomValue);
    }

	public static String deleteCallRecordDeploymentInformation() {
		return "DeleteCallRecordDeploymentInformation";
	}

    public static String getMinuteRequestLimit(LocalDateTime localDateTime) {
        return "minute_req_limit_" + dateFormatter.format(localDateTime);
    }

	public static String getWorkdayKey(LocalDate localDate) {
		return String.format("Workday:%d:%d:%d", localDate.getYear(), localDate.getMonthValue(), localDate.getDayOfMonth());
	}

	public static String getOpensipsGatewaysGwidRedisKey(String gwid) {
		return String.format("OpensipsGatewaysPO:gwid:%s", gwid);
	}

    /**
     * 固话并发数据
     */
    public static String fixedPhoneConcurrencyLimitKey(String fixedPhone) {
        String today = MyDateUtils.formatLocalDateYYYYMMDD();
        return String.format("fixedPhoneConcurrencyLimitKey:%s:%s", today, fixedPhone);
    }

    /**
     * 固话和隐私号绑定key
     */
    public static String fixedPhoneAndPrivacyNumberKey(String fixedPhone, String numberX) {
        return String.format("fixedPhoneAndPrivacyNumberKey:%s:%s", fixedPhone, numberX);
    }

    /**
     * 双呼固话 话单回调
     */
    public static String doubleCallCallBackConsumeKey(String callId) {
        return String.format("doubleCallCallBackConsumeKey:%s", callId);
    }

    /**
     * 阿里云AXB隐私号话单回调
     */
    public static String privacyNumberCallBackConsumeKey(String callId) {
        return String.format("privacyNumberCallBackConsumeKey:%s", callId);
    }

    /**
     * 隐私号校验 绑定id和B号码
     */
    public static String checkBindingIdAndPhoneBKey(String bindingId, String numberB) {
        return String.format("checkBindingIdAndPhoneBKey:%s:%s", bindingId, numberB);
    }

    /**
     * 择时外呼推送
     */
    public static String robotTimeCallTaskPushKey(Long robotCallJobId) {
        return "robotTimeCallTaskPush:" + robotCallJobId;
    }

    /**
     * 风控超时
     */
    public static String rcsTimeOutRandomSleepKey(Long robotCallJobId) {
        return "rcsTimeOutRandomSleep:" + robotCallJobId;
    }

	/**
	 * 租户正在生成日报的标记
	 */
	public static String dailyIsArchiving(Long tenantId) {
		return String.format("DailyIsArchiving:%d", tenantId);
	}

    public static String getAliyunLLMAsrConcurrencyKey() {
        return "AliyunLLMAsrConcurrency";
    }

    public static String getRobotTimeCallTaskCacheBatchIdKey() {
        return "RobotTimeCallTaskCache";
    }

    public static String getDynamicRobotKey(Long tenantId, Long robotCallJobId) {
        return "DynamicRobot:" + tenantId + ":" + robotCallJobId;
    }

    /**
     * 任务最新修改的线路信息
     */
    public static String getRobotCallJobLastTenantPhoneNumberId(Long robotCallJobId) {
        return "JobLastTenantPhoneNumberId:" + robotCallJobId;
    }

	/**
	 * FailureRateRecorder记录成功数量的zset的key
	 */
	public static String failureRateRecorderSuccessKey(String tag) {
		return "FailureRateRecorder:success:" + tag;
	}

	/**
	 * FailureRateRecorder记录失败数量的zset的key
	 */
	public static String failureRateRecorderFailuresKey(String tag) {
		return "FailureRateRecorder:failures:" + tag;
	}

	public static String canNotConnectFeishuWarningIntervalKey(Long robotCallJobId) {
		return "canNotConnectFeishuWarningInterval:JobId:" + robotCallJobId;
	}

    /**
     * 预警时间
     * @param warningConfigurationId
     * @return
     */
    public static String CallJobWholeMonitorLastWarningTime(Long warningConfigurationId) {
        return "LastWarningTime:" + warningConfigurationId;
    }

    /**
     * 新版外呼预警时间
     * @param warningConfigurationId
     * @return
     */
    public static String CallOutJobWholeMonitorLastWarningTime(Long warningConfigurationId) {
        return "CallOutJobLastWarningTime:" + warningConfigurationId;
    }

    /**
     * 唯品会预警
     * @param needMonitorConfig
     * @return
     */
    public static String getWphMonitorRedisKey(WphMonitorConfigBO needMonitorConfig) {
        return "WphMonitor:I" + needMonitorConfig.getImportCount() + ":F" + needMonitorConfig.getFilterCount();
    }

    /**
     * 唯品会预警-任务
     * @param flag
     * @param robotCallJobId
     * @param filterRate
     * @return
     */
    public static String getWphRobotCallJobMonitorRedisKey(String flag, Long robotCallJobId, Double filterRate) {
        return "WphRobotCallJobMonitor:" + flag + ":" + robotCallJobId + ":" + filterRate;
    }

    /**
     * 蚂蚁批量发送短信的batchId
     * @param batchId
     * @return
     */
    public static String getAntSmsBatchId(String batchId) {
        return String.format("AntSms:BatchId:%s", batchId);
    }

    public static Object getTenantNameKey(Long tenantId) {
        return "TenantName:" + tenantId;
    }

    /**
     * 外呼任务人群分析锁
     *
     * @param tenantId 租户id
     * @param robotCallJobId 外呼任务id
     * @param newVersion 是否是新版外呼
     * @return java.lang.String
     * <AUTHOR>
     * date: 2025/1/13 10:27
     */
    public static String getCustomerAnalysisKey(Long tenantId, Long robotCallJobId, boolean newVersion) {
        return "CustomerAnalysis:" + tenantId + ":" + robotCallJobId + ":" + newVersion;
    }

    public static String getHaveSyncHistoryKey(EnvEnum envEnum) {
        return String.format("haveSyncHistory,env:%s",envEnum.getDesc());
    }

    public static String getCurrSyncDateKey(EnvEnum currEnv) {
        return String.format("getCurrSyncDateKey:currEnv:%s",currEnv);
    }

    public static String getBingjianDailyNewKey(){
        return "BingjianDailyKey";
    }

    public static String getImportJobExecingRedisKey(Long importTaskId) {
        return  PRE_FIX + "importJobExecing:" + importTaskId;
    }

    public static String getAntCustomerRequestKey(String requestId) {
        return String.format("importCustomerRequestId:%s",requestId);
    }
    /**
     * e签宝模板授权记录
     * @param orgId
     * @return
     */
    public static String getESignAuth(String orgId) {
        return String.format("ESignAuth:%s", orgId);
    }

}
