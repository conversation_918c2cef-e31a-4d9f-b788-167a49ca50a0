package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.ComplaintHistoryPO;
import com.yiwise.core.model.bo.phonenumber.ComplaintHistoryBO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.phonenumber.ComplainHistoryImportRequestVO;
import com.yiwise.core.model.vo.phonenumber.ComplaintHistoryQueryVO;
import com.yiwise.core.model.vo.phonenumber.ComplaintStatsVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ComplaintHistoryService extends BasicService<ComplaintHistoryPO> {

    /**
     * 查询投诉历史列表
     */
    PageResultObject<ComplaintHistoryBO> queryComplaintHistory(ComplaintHistoryQueryVO queryVO);

    /**
     * 数据处理
     */
    void resovle(List<? extends ComplaintHistoryBO> complaintHistoryBOList);

    /**
     * 导出投诉历史列表
     */
    JobStartResultVO export(ComplaintHistoryQueryVO query);

    /**
     * 新增投诉记录
     */
    void addComplaintHistory(ComplaintHistoryPO complaintHistoryPO, Long userId);

    /**
     * 编辑投诉历史
     */
    void editComplaintHistory(ComplaintHistoryPO complaintHistoryPO, Long userId);

    /**
     * 删除投诉记录
     */
    void deleteComplaintHistory(ComplaintHistoryPO complaintHistoryPO);

    /**
     * 查询租户维度统计数据
     */
    PageResultObject<ComplaintStatsVO> queryTenantStats(ComplaintHistoryQueryVO queryVO);

    /**
     * 查询经销商维度统计数据
     */
    PageResultObject<ComplaintStatsVO> querySupplierStats(ComplaintHistoryQueryVO queryVO);

    /**
     * 查询客户赛道（网关行业）维度统计数据
     */
    PageResultObject<ComplaintStatsVO> queryCustomerTrackStats(ComplaintHistoryQueryVO queryVO);

    /**
     * 查询网关行业维度统计数据
     * @param queryVO
     * @return
     */
    PageResultObject<ComplaintStatsVO> queryGatewayIndustry(ComplaintHistoryQueryVO queryVO);

    /**
     * 导出统计数据
     */
    JobStartResultVO exportStats(ComplaintHistoryQueryVO query);

    /**
     * 投诉率分析统计
     * @param localDate
     */
    void archivingTrackStat (LocalDate localDate);

    /**
     * 投诉率分析统计历史数据处理
     * @param startDate
     * @param endDate
     */
    void archivingTrackStatHistory (LocalDate startDate, LocalDate endDate);

    List<ComplaintHistoryPO> selectByGatewayIndustry(String gateIndustry);

    void updateGatewayIndustry(String old, String last);


    /**
     * 获取投诉记录 key:callRecordId
     * @param recordIdList
     * @return
     */
    Map<Long, ComplaintHistoryPO> getComplaintHistoryMap(List<Long> recordIdList);

    JobStartResultVO importComplaintHistory(ComplainHistoryImportRequestVO importVO);

    ComplaintHistoryPO selectByCallRecordId(Long callRecordId);

}
