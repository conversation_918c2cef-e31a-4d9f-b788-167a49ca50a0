package com.yiwise.core.service.bingjian;

import com.yiwise.core.model.bo.MaCallDetailBO;
import com.yiwise.core.model.vo.BingJianDailyVO;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

/**
 * @author: wuxianyu<PERSON>@yiwise.com
 * @date: 2023 09 22 9:25
 */
public interface BingJianDailyService {

    /**
     * 生成日报
     * @param localDate 昨日
     */
    void statisticalDailyData(Long tenantId, LocalDate localDate);

    /**
     * 生成多个租户
     *
     * @param tenantId
     * @param localDate
     * @return
     */
    Triple<List<BingJianDailyVO>, List<BingJianDailyVO>, List<BingJianDailyVO>> oneTenantStatisticalDailyData(Long tenantId, LocalDate localDate);

    void mergeStatisticalDailyData(List<BingJianDailyVO> yesterday, List<BingJianDailyVO> weekday, List<BingJianDailyVO> month, LocalDate localDate);

    String callDetail(List<MaCallDetailBO> list);

    void manualMergedDaily(LocalDate localDate);

}
