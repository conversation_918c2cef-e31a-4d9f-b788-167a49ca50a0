package com.yiwise.core.service.dialogflow;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.SmsTemplatePO;
import com.yiwise.core.model.bo.NameValueBO;
import com.yiwise.core.model.bo.algorithm.TrainItemBO;
import com.yiwise.core.model.bo.robotcalljob.RobotCallJobOptionBO;
import com.yiwise.core.model.dialogflow.dto.*;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.vo.*;
import com.yiwise.core.model.dto.CallRecordCommonDTO;
import com.yiwise.core.model.enums.EnabledStatusEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.dialogflow.*;
import com.yiwise.core.model.miniapp.vo.RecorderDialogFlowListVO;
import com.yiwise.core.model.vo.dialogflowinfo.CustomerInfoVO;
import com.yiwise.core.model.vo.openapi.RobotVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DialogFlowService extends BasicService<DialogFlowInfoPO> {
    /**
     * 根据话术id获取话术
     * @param dialogFlowId
     * @return
     */
    DialogFlowInfoPO getDialogFlow(Long dialogFlowId);

    List<DialogFlowInfoPO> getDialogFlowBySceneIds(List<Long> sceneIds);

    String getDialogFlowName(Long dialogFlowId);

    Integer countDialogFlowByName(String name);

    List<DialogFlowNameCountVO> countDialogFlowByNameList(List<String> nameList);

    /**
     * 根据话术idList获取话术名List
     * @param dialogFlowId
     * @return
     */
    List<DialogFlowInfoPO> getDialogFlowList(List<Long> dialogFlowId);
    /**
     * 根据话术id获取话术DTO
     * @param dialogFlowId
     * @return
     */
    DialogFlowInfoDTO getDialogFlowInfoDTO(Long dialogFlowId);

    List<DialogFlowInfoPO> findRecorderDialogFlowList(String searchWords, Long distributorId, Long tenantId, Long userId, Boolean nlp);

    List<DialogFlowInfoPO> queryAllProfessionalByTenantId(Long tenantId);

    PageResultObject<DialogFlowInfoPO> findRecorderDialogFlowPageList(String searchWords, Long distributorId, Long tenantId, Long userId, Integer pageNum, Integer pageSize);

    List<DialogFlowInfoPO> findRecorderDialogFlowPageList(String searchWords, Long distributorId, Long tenantId, Long userId, Boolean nlp);

    /**
     * 获取与tenantId绑定的话术列表
     * @param tenantId
     * @return
     */
    List<DialogFlowInfoPO> findDialogFlowByTenant(Long tenantId);

    PageResultObject<RobotVo> findDialogFlowByTenantWithPage(Long tenantId, String name, Integer pageNum, Integer pageSize);

    List<DialogFlowInfoPO> findDialogFlowByDistributor(Long distributorId);

    List<DialogFlowInfoPO> findAllStatusDialogFlowByTenant(Long tenantId);

    List<DialogFlowInfoPO> findDialogFlowByTenant(Long tenantId, String name);

    /**
     * 根据多条件筛选话术
     * @param filterOption
     * @return
     */
    List<DialogFlowInfoPO> filterDialogFlow(DialogFlowFilterOptionDTO filterOption);

    PageResultObject<NameValueBO> getBotPairList(DialogFlowFilterOptionDTO filterOption, int pageNum, int pageSize);

    /**
     * 在全部话术中筛选符合条件的话术
     * @param filterOption
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResultObject<DialogFlowInfoDTO> filterDialogFlow(DialogFlowFilterOptionDTO filterOption, int pageNum, int pageSize);

    PageResultObject<DialogFlowInfoDTO> queryV3DialogFlow(DialogFlowFilterOptionDTO filterOption, int pageNum, int pageSize);

    List<DemoDialogFlowInfoDTO> filterDemoDialogFlow(DialogFlowFilterOptionDTO filterOption);

    List<DialogFlowInfoPO> batchSelectDialogFlowByOptions(DialogFlowBatchSelectOptionDTO dialogFlowBatchSelectOption);

    /**
     * 在特定话术id集合中筛选符合条件的话术
     * @param dialogFlowBatchSelectOption
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResultObject<DialogFlowInfoDTO> batchSelectDialogFlowByOptions(DialogFlowBatchSelectOptionDTO dialogFlowBatchSelectOption, int pageNum, int pageSize, SystemEnum system);

    /**
     * @return
     */
    PageResultObject<DialogFlowSimpleVO> selectSimpleDialogByOptions(DialogFlowBatchSelectOptionDTO dialogFlowBatchSelectOption, int pageNum, int pageSize, SystemEnum system);

    PageResultObject<DialogFlowInfoDTO> batchSelectDialogFlowByOptionsToOpe(DialogFlowBatchSelectOptionDTO dialogFlowBatchSelectOption, int pageNum, int pageSize, SystemEnum system);

    /**
     * 创建话术
     * @param dialogFlowPostDTO
     * @return
     */
    DialogFlowInfoPO createDialogFlow(DialogFlowPostDTO dialogFlowPostDTO, Long recordUserId);

    /**
     * 修改话术
     * @param dialogFlowPostDTO
     * @return
     */
    DialogFlowInfoPO modifyDialogFlow(DialogFlowPostDTO dialogFlowPostDTO);

    /**
     * 删除话术与租户绑定关系
     * @param dialogFlowId
     * @param tenantId
     */
    void deleteDialogFlow(Long dialogFlowId, Long tenantId, Boolean forceDelete);

    /**
     * 删除话术中的mongo信息
     * @param dialogFlowId
     */
    void deleteDialogFlowMongoInfo(Long dialogFlowId);

    /**
     * 标记话术为删除或真实删除
     *
     * @param dialogFlowId
     */
    void deleteDialogFlow(Long dialogFlowId, Boolean forceDelete);

    /**
     * 标记话术为删除或真实删除
     */
    void remoteDeleteDialogFlow(Long dialogFlowId, Boolean forceDelete);

    /**
     * 获取行业信息列表
     *
     * @return
     */
    List<IndustryTypeInfoDTO> getAllIndustryOptions();

    /**
     * 获取已发布话术的全部信息
     * @param dialogFlowId
     * @return
     */
    DialogFlowTotalInfoDTO getDialogFlowTotalInfo(Long dialogFlowId, String collectionName);

    DialogFlowUpdateTimeDTO getDialogFlowUpdateTime(Long dialogFlowId);

    /**
     * 更改话术状态
     *
     * @param dialogFlowId
     * @param status
     */
    void changeDialogFlowStatus(Long dialogFlowId, DialogFlowStatusEnum status);

    /**
     * 远程修改话术状态
     *
     * @param dialogFlowId
     * @param status
     */
    void remoteChangeDialogFlowStatus(Long dialogFlowId, String status);

    /**
     * 更改话术状态
     *
     * @param dialogFlowIdList
     * @param status
     */
    void changeDialogFlowStatus(List<Long> dialogFlowIdList, DialogFlowStatusEnum status);

    /**
     * 更改话术的修改时间，用于标记话术是否在给定的时间内发生更新
     * @param dialogFlowId
     */
    void updateLastModifiedTime(Long dialogFlowId);

    /**
     * 获取话术录音状态
     * @param dialogFlowId
     * @return
     */
    RecordCountDTO getDialogFlowRecordCount(Long dialogFlowId);

    /**
     * 获取话术中存在的占位符
     * @param dialogFlowId
     * @return
     */
    List<String> getDialogFlowPlaceholder(Long dialogFlowId);


    List<String> getDialogFlowPlaceholderUseCache(Long dialogFlowId);

    /**
     * 审核并批准发布话术
     * @param dialogFlowId
     * @param userId
     * @param system
     */
    void approveAndPublishDialogFlow(Long tenantId, Long distributorId, Long dialogFlowId, Long userId, SystemEnum system);

    void submitDialogFlow(Long tenantId, Long distributorId, Long dialogFlowId, SystemEnum system);

    /**
     * 拒绝话术发布请求
     * @param dialogFlowId
     * @param userId
     * @param rejectReason
     */
    void rejectDialogFlow(Long dialogFlowId, Long userId, String rejectReason);

    /**
     * 获取话术呼叫任务相关信息(变量是否存在，转人工是否存在，人工介入是否存在)
     * @param dialogFlowId
     * @return
     */
    DialogFlowCallJobRelatedDTO getDialogFlowCallJobRelatedInfo(Long dialogFlowId);

    /**
     * 通过话术名称模糊搜索
     *
     * @param searchWords
     * @return
     */
    List<RecorderDialogFlowListVO> selectByName(String searchWords);

    DialogFlowInfoPO selectByPreciseName(Long tenantId, String searchWords);

    DialogFlowInfoPO selectByPreciseId(Long tenantId, Long dialogFlowId);


    List<RecorderDialogFlowListVO> selectTop20ByName(String searchWords);



    List<DialogFlowInfoPO> selectAllByName(String searchWords);

    /**
     * 更新录音师时列出该录音师所有已经选择过的话术权限
     *
     * @param userId
     * @return
     */
    List<RecorderDialogFlowListVO> listPersonalAllDialogFlow(Long userId);

    /**
     * 根据录音师的id得到该录音师下的所有话术
     *
     * @param userId
     * @return
     */
    List<DialogFlowInfoPO> selectByRecordUserId(Long userId);

    /**
     * 根据录音师的id得到该录音师下的所有话术
     *
     * @param userId
     * @return
     */
    List<RecorderDialogFlowListVO> selectByRecordUserIdForList(Long userId);

    Map<Long, String> selectDialogFlowNameByDialogFlowIds(Collection<? extends Number> dialogFlowIdList);

    /**
     * 根据话术id获取话术
     *
     * @param dialogFlowId
     * @return
     */
    DialogFlowInfoPO selectByDialogFlowId(Long dialogFlowId);

    /**
     * 根据话术id的集合获取话术集合
     *
     * @param dialogFlowIdList
     * @return
     */
    List<DialogFlowListVO> selectByDialogFlowIds(List<Long> dialogFlowIdList);

    /**
     * 根据话术名称获取行业话术模板的集合，分页
     *
     * @param name
     * @param id
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<RecorderDialogFlowListVO> dialogFlowModelList(String name, Long id, Integer pageNum, Integer pageSize);

    /**
     * 根据话术名称获取行业话术模板的集合，不分页
     *
     * @param name
     * @param id
     * @return
     */
    List<RecorderDialogFlowListVO> dialogFlowModelList(String name, Long id);


    /**
     * 根据话术名称获取客户定制话术的集合
     *
     * @param name
     * @param distributorId
     * @return
     */
    List<DialogFlowListVO> dialogFlowCustomizedList(String name, Long distributorId);

    List<RecorderDialogFlowListVO> dialogFlowCustomizedList(String name, List<Long> idList, Integer pageNum, Integer pageSize);

    void linkAsrModel(Long dialogFlowId, Long asrModelId);

    List<DialogFlowListVO> selectByAsrModelId(Long asrModelId);

    /**
     * 根据用户id列表查询话术信息
     *
     * @param userIds 用户id列表
     * @return list
     */
    List<RecorderDialogFlowListVO> selectByRecordUserIdsForList(List<Long> userIds);

    /**
     * 根据dialogFlowId 查询对应的录音师id。（表中缺少另一个限定条件distributorId）
     *
     * @param dialogFlowId 话术权限id
     * @return
     */
    List<Long> selectUserIdsByDialogFlowId(Long dialogFlowId);

    /**
     * 获取客户所有选择的话术集合
     *
     * @param tenantId
     * @return
     */
    Set<DialogFlowListVO> getAllDialogFlow(Long tenantId);

    /**
     * 清空客户的话术（即将recordUserId改为null）
     *
     * @param oldFlowInfoIds 话术id列表
     */
    void clearRecordUserId(List<Long> oldFlowInfoIds);

    /**
     * 根据分销商id选择分销商客分配的话术
     * @param searchWords 关键字
     * @param distributorId 分销商id
     * @param tenantId 租户id
     * @return
     */
    List<RecorderDialogFlowListVO> selectByNameAndUserId(String searchWords, Long distributorId, Long tenantId);

    /**
     * 根据话术id的集合获取话术的集合
     *
     * @param dialogFlowIds
     * @return
     */
    List<DialogFlowInfoPO> findAllByDialogFlowIds(Set<Long> dialogFlowIds);

    /**
     * 删除boss端话术(已分配的话术提示无法删除)。
     * 暂未考虑录音文件和知识库的删除。
     * @param dialogFlowId 话术id
     * @param distributorId 经销商id
     */
    void deleteBossDialogFlow(Long dialogFlowId, Long distributorId);

    List<RecorderDialogFlowListVO> dialogBossFlowModelList(String name, Long distributorId);

    /**
     * 查询经销商话术的id，包括自己的二级经销商和直销客户创建的话术
     * @param  distributorId 经销商id
     */
    Set<Long> selectIdListByDistributorIdWithSub(Long distributorId);

    void updateNewDialogFlow(DialogFlowPostDTO dialogFlowPostDTO);

    void botConfig(DialogFlowPostDTO dialogFlowPostDTO);

    /**
     * 绑定百家姓模板
     * @param dialogFlowId
     * @param familyNameTemplateId
     */
    void bindFamilyNameTemplate(Long dialogFlowId, Long familyNameTemplateId);

    Set<Long> findDialogFlowInfoIdSetByDistributorIdSet(List<Long> distributorIdList);

    /**
     * 获取话术筛选条件的客户名
     * @param customerInfoVO 客户类型
     * @param pageSize 每页条数
     * @param pageNum  页码
     * @return pageResultObject
     */
    PageResultObject getCustomerInfoByType(CustomerInfoVO customerInfoVO, Integer pageSize, Integer pageNum);

    Long createNewDialogFlow(DialogFlowPostDTO dialogFlowPostDTO);

    /**
     * 替换话术意向分类标签
     * @param dialogFlowId 话术id
     * @param intentLevelTagId 新意向分类标签id
     * @param prevIntentLevelTagId 旧意向分类标签
     */
    void doIntentLevelTagSubstitution(Long dialogFlowId, Long intentLevelTagId, Long prevIntentLevelTagId);

    /**
     * 检查话术是否存在转人工介入
     * @param dialogFlowId
     * @return
     */
    Boolean dialogFlowHumanInterventionExist(Long dialogFlowId);

    /**
     * 查询话术的音频类型
     * @param dialogFlowId 话术id
     * @return
     */
    com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum  selectDialogFlowVoiceType(Long dialogFlowId);

    /**
     * 获取客户的所有绑定话术
     * @param tenantId 租户id
     * @param name 话术名称
     * @param type 话术类型
     * @return 话术列表
     */
    Set<DialogFlowListVO> getAllDialogFlow(Long tenantId, String name, DialogFlowTypeEnum type,Boolean payOrNot);


    PageResultObject<DialogFlowListVO> getAllDialogFlowWithPage(Long tenantId, String name, DialogFlowTypeEnum type,Boolean payOrNot,Integer pageNum,Integer pageSize);



    /**
     * 获取经销商绑定的话术
     * @param distributorId 经销商id
     * @param name 话术名称
     * @param type 话术类型
     * @return
     */
    List<DialogFlowListVO> selectDistributorAllocatedDialogFlow(Long distributorId, String name, DialogFlowTypeEnum type, Integer flag, LocalDate startTime, LocalDate endTime);

    /**
     * 话术上的短信模板列表
     * @param dialogFlowId
     * @return
     */
    List<SmsTemplatePO> dialogFlowSmsTemplateList(Long dialogFlowId);

    Map<Long, List<SmsTemplatePO>> dialogFlowSmsTemplateListByDialogFlowIdList(Collection<Long> dialogFlowIdList);

    Map<Long, Set<Long>> dialogFlowsSmsTemplateList(Collection<Long> dialogFlowId);


    // 查询话术所有版本（包括已发布的和为发布的）依赖的所有短信模板集合
    Set<Long> getAllVersionDependAllSmsTemplateIdSet(Long dialogFlowId);

    /**
     * 检查话术上面的短信模板
     * @param dialogFlowId 话术id
     * @return 短信类型
     */
    String checkDialogFlowSMSTemplate(Long dialogFlowId);

    /**
     * 获取话术的额外信息
     * @param dialogFlowId 话术id
     * @return dialogFlowExtInfoVO
     */
    DialogFlowExtInfoVO getDialogFlowExtInfo(Long dialogFlowId);

    /**
     * 获取话术流程和节点，name-id
     * @param dialogFlowId 话术id
     * @return 话术和节点的名称和id
     */
    List<DialogFlowStepNodeVO> getDialogFlowStepNodes(Long dialogFlowId);

    List<DialogFlowStepNodeVO> getMultiFlowStepNodes(Long dialogFlowId);

    PageResultObject<CallRecordCommonDTO> getDialogFlowSet(Integer pageNum, Integer pageSize, String searchWords, Long tenantId);

    List<RecorderDialogFlowListVO> selectByTenant(String searchWords, Long tenantId);

    PageResultObject getDialogFlowListForAuth(Integer pageNum, Integer pageSize, String searchWords, Long tenantId, Long userId, SystemEnum systemType, BotTypeEnum botType);

    List<String> redisKeyList();

    /**
     * 获取FAQ学习数据
     * @return
     */
    List<TrainItemBO> getDialogFlowFAQList(Long dialogFlowId);
    /**
     * 获取分支学习数据
     * @return
     */
    List<TrainItemBO> getDialogFlowIntentList(Long dialogFlowId);

    List<TrainItemBO> getDialogFlowIntentKeywordList(Long dialogFlowId);

    List<TrainItemBO> getMixedModelIntentList(Long dialogFlowId);

    /**
     * 判断该话术是否需要设置标号
     */
    boolean needLabel(Long dialogFlowId);

    /**
     * 按nlp机器人id查询
     */
    List<DialogFlowInfoPO> getDialogFlowByNlpRobotId(Long nlpRobotId);

    /**
     * 按nlp机器人id批量更新状态
     */
    void updateStatusByNlpRobotId(Long nlpRobotId, DialogFlowStatusEnum status);

    DialogFlowAllVersionList getDialogFlowVersionList(Long tenantId, Long userId, DialogFlowStatusEnum status, DialogFlowTypeEnum type);
    Map<String, Map<Long, List<List<String>>>> copyAllAudio(Long fromDialogFlowId, Long toDialogFlowId, boolean copyAudio, String stepId, Map<String, List<Long>> audioPosition);

    List<DialogFlowInfoPO> queryDialogFlowListByBotType(Long tenantId, BotTypeEnum botType, BotAbilityTypeEnum abilityType);

    PageResultObject<DialogFlowSimpleVO> queryProfessionalRobotIdNamePairList(ProfessionalRobotQueryVO request);

    List<DialogFlowSimpleVO> queryProfessionalRobotIdNamePairByIdList(ProfessionalRobotQueryVO request);

    List<DialogFlowSimpleVO> queryProfessionalRobotIdNamePairByNlpRobotIdList(ProfessionalRobotQueryVO request);

    List<DialogFlowTextSearchResultVO> searchAnswerContent(Long dialogFlowId, String search, Set<String> scopeSet);

    List<DialogFlowTextSearchResultVO> searchQuestionContent(Long dialogFlowId, String search, Set<String> scopeSet);

    List<DialogFlowTextSearchResultVO> searchKeywordContent(Long dialogFlowId, String search, Set<String> scopeSet);

    List<DialogFlowTextSearchResultVO> searchByCondition(DialogFlowTextSearchReplaceRequestVO condition);

    void replaceSearchResult(Long dialogFlowId, List<DialogFlowTextSearchResultVO> contentList, String replacement, Long userId, SystemEnum system);

    void deleteSearchKeywordResult(Long dialogFlowId, List<DialogFlowTextSearchResultVO> contentList, Long userId, SystemEnum system);

    void uploadSearchAnswerAudioUrl(Long dialogFlowId, List<DialogFlowTextSearchResultVO> searchResultList, String audioUrl);

    void train(Long tenantId, Long dialogFlowId, Long userId, String desc, SystemEnum systemType);

    /**
     * 创建文本训练快照
     */
    void createTextTestDialogFlowSnapshot(Long dialogFlowId, SystemEnum system);

    /**
     * 对于直销客户和代理商客户，话术提交审核前需要判断该客户是否具有使用当前话术的音色的权限
     */
    void checkTtsVoiceAuth(Long tenantId, Long distributorId, Long dialogFlowId, SystemEnum system);

    NameValueBO getAsrInfo(Long tenantId, Long distributor, Long botId);

    Set<Long> selectIdListByDistributorId(Long distributorId);

    void robotAutoAudit(Long tenantId, Long distributorId, Long dialogFlowId, Long userId, DialogFlowStatusEnum status, SystemEnum system);

    /**
     * 检查话术是否开启了按键输入
     */
    boolean checkIsEnableDialInput(Long dialogFlowId);

    /**
     * 针对专业版话术自动发布并审核通过
     */
    void submitApprovalAndAutoAudit(Long tenantId,
                                    Long distributorId,
                                    Long userId,
                                    Long dialogFlowId,
                                    SystemEnum system,
                                    DialogFlowStatusEnum status,
                                    String suggestion);

    /**
     * 检查话术是否可以由模板改成客户定制
     * @param dialogFlowId
     * @return
     */
    Boolean canModifyType2Normal(Long dialogFlowId);

    boolean textTrainCheck(Long dialogFlowId, SystemEnum system);

    /**
     * 查询beginDate到endDate时间内外呼任务使用的话术
     * @param tenantId
     * @param dialogFlowId
     * @param dialogFlowName
     * @param beginDate
     * @param endDate
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageResultObject<DialogFlowSimpleVO> getDialogFlowPageByDate(Long tenantId, Long dialogFlowId, String dialogFlowName, LocalDate beginDate, LocalDate endDate, int pageNum, int pageSize);

    /**
     * 保存话术二维码信息
     * @param vo
     * @return
     */
    String createQrCode(CreateDialogFlowQrCodeVO vo);

    /**
     * 获取话术二维码信息
     * @param qrCodeId
     * @return
     */
    DialogFlowQrCodeInfoVO getQrCodeInfo(String qrCodeId);

    /**
     * 获取话术二维码图片
     *
     * @param qrCodeId
     * @param response
     * @return
     */
    void getQrCodeImage(String qrCodeId, HttpServletResponse response);

    /**
     * 编辑话术利益点
     * @param dialogFlowInfoPO
     */
    void editDialogFlowInterestPoint(DialogFlowInfoPO dialogFlowInfoPO);

    /**
     * 同步话术和录音师的选项到任务
     * @param dialogFlowId
     */
    void syncOptionToRobotCallJob(Long dialogFlowId);

    /**
     * 根据话术ID查询字段选项列表（包括录音师字段选项）
     * @param dialogFlowId
     */
    Set<Long> getOptionIdSetByDialogFlowId(Long dialogFlowId);

    /**
     * 根据字段选项列表和其他条件查询话术（包括录音师字段选项）
     */
    List<RobotCallJobOptionBO> selectDialogFlowByOptionLimit(Long tenantId, String name, Collection<Long> vOptionIdList, Collection<Long> cOptionIdList);

    /**
     * 查询所有的话术（标注平台使用）
     * @param queryVO
     * @return
     */
    List<DialogFlowMarkSystemVO> selectAllForMarkSystem(DialogFlowMarkQueryVO queryVO);

    /**
     * 根据话术id更新话术发布状态
     *
     * @param dialogFlowId
     * @param userId
     */
    void updateDialogFlowPublishStatus(Long dialogFlowId, Long userId, String status);

    /**
     * 更新话术可见状态
     *
     * @param dialogFlowId 话术id
     * @param visibleStatus 可见状态
     */
    void updateVisibleStatus(Long dialogFlowId, EnabledStatusEnum visibleStatus);

    /**
     * 获取v3话术id
     *
     * @param dialogFlowIdList
     * @return
     */
    List<Long> getV3DialogFlowIdByIdList(List<Long> dialogFlowIdList);

    void importFromJson(MultipartFile file, Long currentUserId);

    /**
     * 查询指定的话术中曾经审核通过过的话术的id列表
     *
     * @return
     */
    Set<Long> getPublishedDialogFlowIds(Collection<Long> dialogFlowIds);

    Integer countByIdAndEnabledStatus(Long dialogId, Long tenantId);

    PageResultObject<DialogFlowInfoDTO> getDialogFlowPageList(Integer page, Integer pageSize);

    void batchUpdate(List<DialogFlowInfoDTO> updateList);

    void clearPublishedFlagByDialogFlowIdList(List<Long> dialogFlowIdList);

    List<DialogFlowInfoPO> selectByIntentLevelTagIds(Collection<Long> intentLevelTagIds);
}
