package com.yiwise.core.service;

import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.model.enums.dialogflow.RecordTypeEnum;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-09
 */
public class TempFilePathKeyCenter {

    private TempFilePathKeyCenter() {
    }

    /**
     * 获取通用临时文件地址
     */
    public static String getCommonTempFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR;
    }

    /**
     * 获取Excel临时文件地址。
     * 需要删除
     */
    public static String getExcelTempFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/" + fileName;
    }

    /**
     * 获取Csv临时文件地址。
     * 需要删除
     */
    public static String getCsvTempFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "CsvFiles/" + fileName;
    }

    /**
     * 阿里tts文件
     */
    public static String getAliTTSWavFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ALI_TTS/" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "/" + RandomStringUtils.randomAlphabetic(32) + ".wav";
    }

    /**
     * 百家姓临时文件
     */
    public static String getFamilyNameFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "FamilyName/";
    }

    /**
     * 客户导入模板临时文件
     */
    public static String getCustomerPersonTemplateExcelFilePath(Long robotCallJobId, Long tenantId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/customerPersonTemplate/" + tenantId + "/robotCallJob/" + robotCallJobId + "/批量导入客户模板" + new Date().getTime() + ".xlsx";
    }

    /**
     * 客户导入模板临时文件, 外呼计划
     */
    public static String getCustomerPersonPlanTemplateExcelFilePath(Long callOutPlanId, Long tenantId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/customerPersonTemplate/" + tenantId + "/callOutPlan/" + callOutPlanId + "/批量导入客户模板" + new Date().getTime() + ".xlsx";
    }

    public static String getCustomerPersonTemplateExcelFilePath(Long tenantId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/customerPersonTemplate/" + tenantId + "/批量导入成员模版" + new Date().getTime() + ".xlsx";
    }

    /**
     * 获取话术录音临时文件
     */
    public static String getDialogRecordTempFilePath(RecordTypeEnum recordType) {
        if (recordType == null) {
            return CommonApplicationConstant.LOCAL_TMP_DIR;
        }
        return CommonApplicationConstant.LOCAL_TMP_DIR + recordType.toCapitalPrefixHumpStr() + "/";
    }

    /**
     * 获取自定义变量录音临时文件
     */
    public static String getCustomVariableTempFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "CustomVariable/";
    }

    /**
     * 用户上传的临时文件
     */
    public static String getUserUploadTempFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "FileUpload/" + System.currentTimeMillis() + "/" + fileName;
    }

    /**
     * 外呼计划模板
     */
    public static String getCalloutplanFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "excel/callOutPlanTemplate/"+ fileName;
    }

    /**
     * 需要定时删除
     */
    public static String getFSDownloadTempFilePath(String identify) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "fsTemp/" + identify + ".wav";
    }

    /**
     * 话术资源文件的临时路径，会将整个话术的资源打包为.tar.gz
     *
     * @return 临时路径string path
     */
    public static String getDialogResourceTempFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "dialogResourceTemp/";
    }

    public static String getDialogTextFilePath(String dialogFlowName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "dialogText/" + dialogFlowName + ".txt";
    }

    public static String getDialogFlowJsonDirPath(Long dialogFlowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "dialogJson/" + dialogFlowId + "/";
    }

    public static String getDialogFlowXmindFilePath(Long dialogFlowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "xmind/" + dialogFlowId + "/";
    }

    public static String getFsDownloadTxtFilePath(String identify) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "fsTemp/" + identify + ".txt";
    }

    public static String getOSSDownloadTempFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ossTemp/" + fileName;
    }

    /**
     * 短信导入模板临时文件
     */
    public static String getSmsJobTemplateExcelFilePath(Long smsJobId, Long tenantId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/smsJob/" + tenantId + "/smsJobId/" + smsJobId + "/导入模板" + new Date().getTime() + ".xlsx";
    }

    public static String getSelfLearningTrainingTxtFile(Long selfLearningModelId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "SelfLearningTrainingTxt" + (selfLearningModelId == null ? "" : "/" + selfLearningModelId) + "/" + System.currentTimeMillis();
    }

    /**
     * 批量下载录音的临时路径
     */
    public static String getBatchAudioFileParentPath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "BatchAudio/";
    }

    /**
     * 批量下载录音的临时路径
     */
    public static String getBatchAudioFileParentPath(Long dialogFlowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "BatchAudio/" + dialogFlowId + "/";
    }

    public static String getNlpAudioKeyPrefix(String relativePath) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + relativePath;
    }
    /**
     * nlp音频key前缀
     *
     * @param dialogFlowId 话术id
     * @param md5          文本模板的md5
     */
    public static String getNlpAudioKeyPrefix(Long dialogFlowId, String md5) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + dialogFlowId + "/" + md5 + "/";
    }

    /**
     * 短信导入模板临时文件
     */
    public static String getQcRecordExcelFilePath(Long qcJobId, Long tenantId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/qcJob/" + tenantId + "/qcJobId/" + qcJobId + "/质检结果记录" + new Date().getTime() + ".xlsx";
    }

    public static String getESignDownloadTempFilePath(String flowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "esignTemp/" + flowId + ".txt";
    }

    public static String getESignJPGFilePath(String key) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "esignJPG/" + key + ".jpg";
    }

	/**
	 * 隐私号回调通话音频本地临时存储路径
	 */
	public static String getPrivacyNumberAudioPath(String fileName) {
    	return CommonApplicationConstant.LOCAL_TMP_DIR + "privacy_number_audio/" + fileName;
    }

    public static String getExportAsV3ResourceBaseDirPath(Long dialogFlowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "exportAsV3/" + System.currentTimeMillis() + "/" + dialogFlowId + "/";
    }

    public static String getExportAsV3ZipFilePath(Long dialogFlowId) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "exportAsV3Zip/" + System.currentTimeMillis() + "/" + dialogFlowId + "/";
    }

    /**
     * OPE的CSM工作台导出消耗目标统计
     */
    public static String getCsmCostWithTargetOssFileKey() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/csmCostWithTarget/" + System.currentTimeMillis() + ".xlsx";
    }

    public static String getSmsTemplateExcelFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/smsTemplate/" + "/批量导入短信模板" + new Date().getTime() + ".xlsx";
    }

    /**
     * 冰鉴日报导出
     */
    public static String getBingJianDailyOssFileKey() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/bingJianDaily/" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "_" + System.currentTimeMillis() + ".xlsx";
    }

    public static String getBingJianCallDetailOssFileKey() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/bingJianCallDetail/" + DateFormatUtils.format(new Date()
                , "yyyy-MM-dd") + "_" + System.currentTimeMillis() + ".xlsx";
    }

    /**
     * 冰鉴日报导出图片
     */
    public static String getBingJianDailyOssImageKey() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ImageFiles/bingJianDaily/" + DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "_" + System.currentTimeMillis() + ".png";
    }

    /**
     * 通话前 tts 缓存的临时文件
     * @param dialogFlowId 话术 id
     * @param cacheLocalFileName 缓存文件名
     * @return 缓存文件路径
     */
    public static String getTtsCacheFilePath(Long dialogFlowId, String cacheLocalFileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "AudioRecord/dialogFlow/" + dialogFlowId + "/ttsCache/" + cacheLocalFileName;
    }

    public static String getVarValueReplaceFilePath(String fileName) {
        String fileSuffix = fileName.substring(fileName.lastIndexOf("."));
        return CommonApplicationConstant.LOCAL_TMP_DIR + "VarValueReplace/" + System.currentTimeMillis() + "." + fileSuffix;
    }

    /**
     * 双呼固话导入模板临时文件
     */
    public static String getFixedPhoneTemplateExcelFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/fixedPhoneTemplate/" + "/批量导入双呼固话模板" + new Date().getTime() + ".xlsx";
    }

    public static String getComplaintHistoryTemplateExcelFilePath() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/complaintHistoryTemplate/" + "/导入投诉记录模板" + new Date().getTime() + ".xlsx";
    }

    public static String getShareAnswerExportExcel() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/shareAnswer/" + "话术库" + new Date().getTime() + ".xlsx";
    }
    public static String getShareAnswerImportResultExcel() {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "ExcelFiles/shareAnswer/" + "话术库导入结果" + new Date().getTime() + ".xlsx";
    }

    public static String getMergeInterceptAudioTempFilePath(String fileName) {
        return CommonApplicationConstant.LOCAL_TMP_DIR + "TransferMerge" + fileName;
    }
}
