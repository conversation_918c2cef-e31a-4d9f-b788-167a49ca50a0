package com.yiwise.core.service.ope.csmconsole;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.OrderCallOutDataInfoPO;
import com.yiwise.core.model.dto.OrderCallOutDataInfoDTO;
import com.yiwise.core.model.vo.OrderCallOutDataInfoQueryVO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.ope.JobDataUploadQueryVO;
import com.yiwise.core.model.vo.openapi.OrderCallOutDataCallbackVO;

import java.util.List;

public interface OrderCallOutDataInfoService extends BasicService<OrderCallOutDataInfoPO> {


    /**
     * 页面查询接口
     * @param request
     * @return
     */
    List<OrderCallOutDataInfoDTO> selectByCondition(OrderCallOutDataInfoQueryVO request);

    /**
     * 上传订单外呼数据
     * @param jobDataUploadQueryVO
     * @return
     */
    JobStartResultVO uploadCallOutData(JobDataUploadQueryVO jobDataUploadQueryVO);

    /**
     * 外呼数据导入任务回调接口
     * @param jobDataUploadCallbackDTO
     */
    void callback(OrderCallOutDataCallbackVO jobDataUploadCallbackDTO);
}
