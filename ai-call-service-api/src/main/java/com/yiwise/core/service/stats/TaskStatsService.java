package com.yiwise.core.service.stats;

import com.yiwise.core.dal.entity.CallRecordPO;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 单个任务统计
 * <AUTHOR> yangdehong
 * @date : 2018/12/20 10:47
 */
public interface TaskStatsService {

    /**
     * 重新统计某个任务
     */
    void reStatsOneRobotCallJob(Long robotCallJobId);

    /**
     * 重新统计某个用户
     */
    void reStatsOneTenant(Long tenantId, boolean isStatsAll);

    /**
     * 统计快速拨打
     */
    void reStatsQuickCall(Long tenantId);

    void reStatsOneRobotCallJobByTime(Long robotCallJobId, LocalDateTime start, LocalDateTime end);

    /**
     * 重新统计单个话术
     */
    void reStatsOneDialogFlow(Long tenantId, Long dialogFlowId, boolean validDialogFlowId);

    /**
     * 重新统计客户下面的所有话术
     */
    void reStatsAllDialogFlowByTenant(Long tenantId);

    /**
     * 重新统计客户的呼入接待数据
     */
    void reStatsCallInByTenantId(Long tenantId);

    /**
     * 重新统计一个客户的所有问答知识触发次数
     */
    void reStatsKnowledgeByTenantId(Long tenantId);

    /**
     * 重新统计客户的某个任务的问答知识的触发次数
     */
    void reStatsKnowledgeByRobotCallJobId(Long tenantId, Long robotCallJobIf);

	/**
	 * 从allCallRecords中获取callRecords的每个记录的"相同taskId的上一次外呼记录"
	 */
	List<Pair<CallRecordPO, CallRecordPO>> getPreCallRecord(Collection<CallRecordPO> callRecords, List<CallRecordPO> allCallRecords);

    /**
     * 任务统计集合历史数据设置dialogFlowId字段
     */
    void setDialogFlowIdForCallStatsRobotJob();

	/**
	 * 维护历史客户数据（正式或非正式）
	 */
	void setFormalTenantForCallStatsRobotJob();

	/**
	 * 外呼短信赠送改为不赠送
	 */
	void costFreeSms(Long tenantId, LocalDate startDate, LocalDate endDate);

	/**
	 * 将指定的外呼任务费用更新为单价*(通数or时长)
	 */
	void fixNewBilling(Integer year, Integer month, Integer day, Long tenantId);

	/**
	 * 比较指定租户在指定日期里AICC的外呼任务统计费用和计费服务的总消耗, 如果AICC消耗大于计费服务消耗则生成扣费记录
	 */
	void compareAiccAndBillingCost(LocalDate startDate, LocalDate endDate, Long tenantId, Boolean mockAddBill);
}
