package com.yiwise.core.service.engine.calloutplan;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.calloutplan.CallOutPlanJobPO;
import com.yiwise.core.dal.entity.calloutplan.CallOutPlanPO;
import com.yiwise.core.model.dialogflow.dto.DialogFlowInfoDTO;
import com.yiwise.core.model.enums.JobOperationEnum;
import com.yiwise.core.model.vo.CallOutPlanModifyVO;
import com.yiwise.core.model.vo.IdNameVO;
import com.yiwise.core.model.vo.calloutplan.*;
import com.yiwise.core.model.vo.robotcalljob.RobotCallJobModifyVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CallOutPlanService extends BasicService<CallOutPlanPO> {

    Set<String> getProperties(Long dialogFlowId);

    Boolean checkDialogFlowProperiesFit(CallOutPlanDialogFlowQueryVO callOutPlanDialogFlowQueryVO);

    RobotCallJobStatusInfo queryJobStatusByIds(List<Long> robotCallJobIds);

    void updateLastHeartBeat(Long callOutPlanId);

    Boolean callOutPlanExists(Long tenantId);

    void createCallOutPlan(CallOutPlanCreateVO callOutPlanCreateVO, Long tenantId, Long userId);

    GenerateResultVO createCallOutPlanAndImportCallJob(CallOutPlanJobImportRequestVO callOutPlanJobImportRequestVO);

    GenerateResultVO importCallJob(CallOutPlanJobImportRequestVO callOutPlanJobImportRequestVO);

    GenerateResultVO checkImportDup(CallOutPlanJobImportRequestVO callOutPlanJobImportRequestVO);

    PageResultObject<CallOutPlanInfoVO> getCallOutPlanByTenantId(Long tenantId, Long userId, String callOutPlanName,
                                                               LocalDate startTime, LocalDate endTime,
                                                               LocalDate startCreateTime, LocalDate endCreateTime,
                                                               String note, Integer pageNum, Integer pageSize);

    PageResultObject<IdNameVO> simpleGet(Long tenantId, Integer pageNum, Integer pageSize, String searchWords);

    CallOutPlanVO getById(Long callOutPlanId, Long tenantId);

    void deleteById(Long callOutPlanId, Long tenantId, Long userId);

    void modify(CallOutPlanModifyVO callOutPlanModifyVO, Long tenantId, Long userId);
    void updatePlanStartTime(Long callOutPlanId,  LocalDate startTime);

    void addCallJobIntoPlan(RobotCallJobModifyVO robotCallJobModifyVO, Long userId, Long tenantId);

    List<CallOutPlanJobVO> getCallJobByPlan(Long tenantId, Long callOutPlanId);

    void updateDialogFlow(Long callOutPlanId, Long tenantId, Long userId);

    JobStatusCheckResultVO checkJobStatus(BatchModifyVO batchModifyVO, Long tenantId, Long userId);

    TagInfoVO queryIntentTagSingle(List<Long> robotcallJobIds);

    PageResultObject<DialogFlowInfoDTO> getDialogFlowList(CallOutPlanDialogFlowQueryVO filterOption);

    CallOutPlanSmsInfoVO getSmsList(CallOutPlanSmsQueryVO query);

    PhoneNumberSimpleInfoVO getPhoneName(PhoneNumberSimpleQueryVO query);

    List<CallOutPlanJobVO> queryJobByPlan(CallOutPlanJobQueryVO callOutPlanJobQueryVO);

    void checkAccess(Long userId);

    GenerateResultVO batchModifyJob(RobotCallJobModifyVO robotCallJobModifyVO, List<Long> robotCallJobids, Long userId);

    CallOutPlanResultVO generateCallJob(JobGenerateVO jobGenerateVO, Long tenantId, Long userId);

    CallOutPlanResultVO deleteCallJob(JobGenerateVO jobGenerateVO, Long tenantId, Long userId);

    GenerateResultVO batchExecuteCallJob(JobGenerateVO jobGenerateVO, Long tenantId, Long userId, JobOperationEnum operation);

    void duplicatePlan(CallOutPlanModifyVO callOutPlanModifyVO, Long tenantId, Long userId);

    Boolean checkNameDup(String name, Long callOutPlanId, Long tenantId);

    void duplicateCallJob(Long robotCallJobId, Integer Count, Long tenantId, Long userId);
    void duplicateCallJob(Long robotCallJobId, Integer Count, Long tenantId, Long userId, CallOutPlanJobPO callOutPlanJobPO);
    void duplicateCallJob(Long robotCallJobId, Integer Count, Long tenantId, Long userId, Long callOutPlanId, CallOutPlanJobPO callOutPlanJobPO);

	List<PlanJobImportInfoVO> queryImportInfo(PlanImportJobsInfoRequestVO request);

    CallOutPlanResultVO checkGenerateCallJob(JobGenerateVO jobGenerateVO, Long tenantId, Long userId);
}
