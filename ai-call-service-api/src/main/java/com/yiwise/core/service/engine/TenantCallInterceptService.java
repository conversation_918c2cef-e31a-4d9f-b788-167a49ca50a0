package com.yiwise.core.service.engine;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.dal.entity.TenantCallInterceptPO;
import com.yiwise.core.dal.mongo.CallOutInterceptPO;
import com.yiwise.core.model.enums.TenantCallInterceptSourceEnum;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

public interface TenantCallInterceptService extends BasicService<TenantCallInterceptPO> {

    List<TenantCallInterceptPO> selectSystemInterceptsByTenantId(Long tenantId);

    /**
     * 查询任务的外呼拦截规则,包括ope指定的和aicc指定的
     */
    List<TenantCallInterceptPO> selectByRobotCallJobId(Long robotCallJobId);
    List<TenantCallInterceptPO> selectByRobotCallJob(RobotCallJobPO robotCallJob);

    boolean tenantEnableIntercept(Long tenantId);

    void updateTenantIntercept(TenantCallInterceptPO tenantCallInterceptPO);

    List<TenantCallInterceptPO> getTenantCallInterceptList(Long tenantId, String name, TenantCallInterceptSourceEnum source);

    /**
     * ope/boss删除规则
     */
    void deleteTenantCallIntercept(Long tenantId, Long tenantCallInterceptId);

    /**
     * aicc删除规则
     */
    void deleteTenantCallIntercept(Long tenantId, Long tenantCallInterceptId, boolean fromTenant);
    /**
     * 检查外呼次数是否超出限制是否
     */
    boolean checkCallOutDays(TenantCallInterceptPO interceptPO, String phoneNumber);
    /**
     * 检查接通次数是否超出限制
     */
    boolean checkGetThroughDays(TenantCallInterceptPO interceptPO, String phoneNumber);
    /**
     * 检查是否空号拦截
     */
    boolean checkNotExistDays(TenantCallInterceptPO interceptPO, String phoneNumber);
    /**
     * 检查是否停机拦截
     */
    boolean checkNotServiceDays(TenantCallInterceptPO interceptPO, String phoneNumber);

    int getCustomCount(Long tenantId);

    void setTenantDefaultStatus(Long tenantId, Long tenantCallInterceptId, Long userId);

    /**
     * 查询外呼拦截数据
     * @param beginDate
     * @param endDate
     * @param tenantId
     * @param calledPhoneNumber
     * @return
     */
    CallOutInterceptPO getCallOutInterceptTotal(LocalDate beginDate, LocalDate endDate, Long tenantId, String calledPhoneNumber);

    List<TenantCallInterceptPO> selectTenantCallInterceptListByTenant(Collection<Long> list, Long tenantId);

    void dealTenantCallInterceptToRcs(Long tenantId);
}
