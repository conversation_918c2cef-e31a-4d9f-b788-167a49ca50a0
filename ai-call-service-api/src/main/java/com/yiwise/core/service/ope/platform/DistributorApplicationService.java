package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.DistributorApplicationPO;
import com.yiwise.core.model.enums.ope.ApplicationStateEnum;
import com.yiwise.core.model.enums.ope.ApplicationTypeEnum;
import com.yiwise.core.model.vo.distributorapplication.DistributorApplicationVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 代理合作
 * @date 2018/08/14
 */
public interface DistributorApplicationService extends BasicService<DistributorApplicationPO> {

    /**
     * 获取有代理申请的省份列表
     *
     * @return 省份字符串列表
     */
    List<String> getProvincesWithApplication();

    /**
     * 获取有代理申请的市列表
     *
     * @param province 要查询的省
     * @return 市字符串列表
     */
    List<String> getCitiesWithApplication(String province);

    /**
     * 插入代理合作申请
     *
     * @param distributorApplicationPO
     */
    void insert(DistributorApplicationPO distributorApplicationPO);

    /**
     * 根据公司名/联系人名/电话号码模糊匹配查询代理申请
     * @param condition    公司名/联系人名/电话号码
     * @param province     代理省份
     * @param city         代理市
     * @param processState 处理状态
     */
    PageResultObject<DistributorApplicationVO> getByCondition(
            Integer pageNum, Integer pageSize, ApplicationTypeEnum type,
            String condition, String province, String city,
            ApplicationStateEnum processState,
            LocalDate startDate, LocalDate endDate);

    /**
     * 处理代理申请
     *
     * @param applicationId 申请id
     * @param userId        用户id
     * @param processInfo   处理详情
     */
    void updateProcess(Long applicationId, Long userId, String processInfo);

    void createDistributorApplication(DistributorApplicationPO distributorApplicationPO);

    void updateSyncToXiaoKe(DistributorApplicationPO df);
}
