package com.yiwise.core.service.financial;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.financial.CallFinancialPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CallFinancialService extends BasicService<CallFinancialPO>, FinancialService {

	/**
	 * @return true更新成功, false未更新数据
	 */
	Boolean updateCost(Long tenantId, Long callRecordId, Long cost);

	/**
	 * 刷数据使用
	 */
	CallFinancialPO selectByCallRecordId(Long callRecordId);

	/**
	 * 刷数据使用
	 */
	void deleteByCallRecordId(Long callRecordId);

    List<CallFinancialPO> getInfoByCallRecordIdList(Set<Long> recordIdList);
}
