package com.yiwise.core.service.uservariable;

import com.yiwise.base.service.BasicService;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.dal.entity.uservariable.UserVariableItemPO;
import com.yiwise.core.model.dto.UserVariableItemCountDTO;
import com.yiwise.core.model.miniapp.vo.UserVariableItemVO;
import com.yiwise.core.model.vo.uservariable.UserVariableItemQueryVO;
import com.yiwise.core.model.dto.UserVariableItemTotalDTO;
import com.yiwise.core.model.miniapp.vo.UserVariableItemVO;
import com.yiwise.core.model.vo.uservariable.UserVariableItemQueryVO;
import com.yiwise.core.model.vo.uservariable.UserVariableItemUpdateVO;
import com.yiwise.core.model.vo.uservariable.UserVariableRecordingVO;
import com.yiwise.base.service.BasicService;

import java.util.List;
import java.util.Set;

/**
 * Created on 2019-04-10.
 *
 * <AUTHOR>
 * email <EMAIL>
 * description 自定义变量项接口
 */
public interface UserVariableItemService extends BasicService<UserVariableItemPO> {
    /**
     * 批量插入变量内容
     * @param userVariableItemPOList 变量内容列表
     * @return boolean res
     */
    boolean addBatch(List<UserVariableItemPO> userVariableItemPOList);

    /**
     *  逻辑删除变量值，用于清空
     * @param userVariableId
     * @return
     */
    int deleteByVariableId(Long userVariableId);

    /**
     * 查询所有的变量值
     * @param userVariableId
     * @return
     */
    List<UserVariableItemPO> findAllByUserVariableId(Long userVariableId);

    List<UserVariableItemPO> findAllByUserVariableIdList(List<Long> userVariableIdList);

    List<UserVariableItemPO> findAllByUserVariableIdWithPage(Long userVariableId, Integer pageNum, Integer pageSize);

    /**
     * 通过变量值删除
     * @param userVariableId 用户变量id
     * @param oldItemValues 需要删除的值
     */
    void deleteByVariableValues(Long userVariableId, Set<String> oldItemValues);

    /**
     * 根据变量值查询变量信息
     * @param userVariableId 自定义变量id
     * @param value 变量内容
     * @return null
     */
    UserVariableItemPO selectByValue(Long userVariableId, String value);

    /**
     * 根据关键词查询变量id
     * @param keywords
     * @return
     */
    Set<Long> getUserVariableIdSetByKeywords(String keywords);

    Integer countByVariableId(Long userVariableId);

    /**
     * 批量统计变量内容数量
     * @param userVariableIdList
     * @return
     */
    List<UserVariableItemCountDTO> countByVariableIds(List<Long> userVariableIdList);

    List<UserVariableRecordingVO> selectListByVariableId(Long userVariableId, Long recordUserId);

    PageResultObject<UserVariableRecordingVO> pageListUserVariableRecordingVO(UserVariableItemQueryVO queryVO);

    List<UserVariableRecordingVO> selectListByRecordUserId(Long recordUserId);

    UserVariableItemPO selectByUserVariableItemId(Long userVariableItemId);

    UserVariableItemPO updateContentByValue(Long userVariableId, String value, String content);

    /**
     * 批量更新变量内容
     *
     * @param list 更新表单列表
     */
    void batchUpdateContent(List<UserVariableItemUpdateVO> list);

    UserVariableItemTotalDTO selectTotalInfoByUserVariableId(Long userVariableId);

    /**
     * 获取变量分页列表
     *
     * @param userVariableId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<UserVariableItemVO> getUserVariableItemList(Long userVariableId, String keyword, Integer pageNum, Integer pageSize, Long userId);

    List<UserVariableItemVO> findAllByUserVariableIdAndKeyword(Long userVariableId, String keyword, Long userId);
}
