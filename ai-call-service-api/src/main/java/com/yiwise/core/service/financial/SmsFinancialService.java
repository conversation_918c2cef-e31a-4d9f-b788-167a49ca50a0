package com.yiwise.core.service.financial;

import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.SmsJobMessagePO;
import com.yiwise.core.dal.entity.financial.SmsFinancialPO;
import com.yiwise.core.model.enums.sms.SmsBusinessTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SmsFinancialService extends BasicService<SmsFinancialPO>, FinancialService {

	SmsFinancialPO selectByRefId(SmsBusinessTypeEnum businessType, Long refId);

	void batchInsert(Long tenantId, SmsBusinessTypeEnum businessType, Collection<SmsJobMessagePO> smsJobMessages, Long smsChannelId, Long chengbenFare);

	void initUpdate(SmsBusinessTypeEnum businessType, Long refId, Long smsChannelId, Long chengbenFare, Integer gatewayCostCount, Long chengbenCost);

	void updateCost(SmsBusinessTypeEnum businessType, Long refId, Long smsPrice, Integer billCount, Long smsCost, Long freeCost);

	void updateRefund(SmsBusinessTypeEnum businessType, Long refId);

	void updateChannel(SmsBusinessTypeEnum businessType, Long refId, String channelNo, Long chengbenFare, Integer gatewayCostCount);

	List<Long> selectByBusinessTypeRefIds(SmsBusinessTypeEnum businessType, Collection<Long> refIds);
}
