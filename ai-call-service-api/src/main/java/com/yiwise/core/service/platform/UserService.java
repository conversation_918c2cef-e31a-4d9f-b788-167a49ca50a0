package com.yiwise.core.service.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.model.bo.user.UserOpenOrBlockUpBatchBO;
import com.yiwise.core.model.dto.SimpleUserInfoDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.IdNamePairVO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.customer.CustomerPersonImportRequestVO;
import com.yiwise.core.model.vo.ope.DistributorOpeVO;
import com.yiwise.core.model.vo.user.*;
import javaslang.Tuple2;

import java.util.*;

/**
 * <AUTHOR>
 * @date 27/07/2018
 */
public interface UserService extends BasicService<UserPO> {

    /**
     * 通过id查询用户名
     * 只用id查询效率太低 建议使用 tenantId 和userId作为条件查询
     *
     * @param id 用户id
     * @return 用户对象
     */
    UserPO getUserById(Long id);

    /**
     * 获取tenant的用户
     *
     * @param tenantId 客户id
     * @param useId    用户id
     * @return 用户信息
     */
    UserPO getTenantUser(Long tenantId, Long useId);

    /**
     * 查询租户下的用户
     *
     * @param tenantId 租户id
     * @return 用户对象列表
     */
    List<UserPO> getUserByTenantId(Long tenantId);

    Integer getUserCountByTenantId(Long tenantId);

    /**
     * 登录逻辑
     *
     * @param phoneNumber 电话号码
     * @param password    密码
     * @param system      要登录的系统
     * @param ip          用户ip
     * @return 登陆成功后返回该用户的信息
     */
    UserPasswordVO login(String phoneNumber, String password, SystemEnum system, String ip);

    void loginWithoutPassword(UserPO user, SystemEnum system, String phoneNumber, String ip);

    /**
     * 手机验证码登录
     *
     * @param phoneNumber 电话号码
     * @param code        手机验证码
     * @param system      要登录的系统
     * @return 登陆成功后返回该用户的信息
     */
    UserPO loginBySmsVerificationCode(String phoneNumber, String code, SystemEnum system, String ip);

    /**
     * 微信扫码登录
     *
     * @param code   微信返回的code
     * @param system 要登录的系统
     * @return 登陆成功后返回该用户的信息
     */
    UserPO miniAppLoginByWechatCode(String code, SystemEnum system, String ip);

    /**
     * 绑定微信CRM小程序openid
     *
     * @param userId 用户id
     * @param code   微信返回的code
     */
    void bindMiniAppOpenId(Long userId, String code);

    /**
     * 发送手机验证码
     *
     * @param phoneNumber 电话号码
     */
    void sendSmsVerificationCode(String phoneNumber, String captchaKey, String captchaCode);

    /**
     * 添加用户
     *
     * @param user    用户对象
     * @param systems 该用户能登录的系统
     */
    void addUser(UserPO user, String password, SystemEnum systems, Long organizationId, Long roleId);

    /**
     * 各系统更新用户信息
     *
     * @param user       用户对象
     * @param systemType 修改的系统
     * @return 更新情况
     */
    String updateUser(UserPO user, SystemEnum systemType, Long organizationId, Long roleId, String password,boolean checkPassword);


    /**
     * 启用用户
     *
     * @param userId     用户id
     * @param updateUser 更新用户id
     */
    void enable(Long userId, Long updateUser, SystemEnum systemEnum);

    /**
     * 停用用户
     */
    void blockUp(Long userId, Long updateUser, SystemEnum systemEnum);

    /**
     * 停用crm用户
     * 因为crm用户停用之后，需要把其所属的客户移动到客户公海
     *
     * @param tenantId     客户id
     * @param userId       用户id
     * @param updateUserId 操作人id
     */
    void blockUpCrmUser(Long tenantId, Long userId, Long updateUserId,SystemEnum systemType);

    /**
     * 根据组织的id以及子组织的id获取用户的集合
     *
     * @param tenantId       客户id
     * @param organizationId 组织id
     * @param name           姓名
     * @param nickName       用户名
     * @param phoneNumber    手机号
     * @param roleId         角色id
     */
    PageResultObject<UserListVO> selectUserByOrganizationAndChildrenByIdByAndStatus(Integer pageNum, Integer pageSize, Long tenantId, Long organizationId, String name, String nickName, String phoneNumber, Long roleId, UserStatusEnum status, SystemEnum systemType);

    UserPO getUserByOpenId(String openId);

    /**
     * 根据用户id获取用户
     */
    List<UserPO> getUsersByIds(Collection<Long> userIds);

    /**
     * ope新建录音师
     *
     * @param recorderVO 录音师用户对象
     * @param createUser 创建用户id
     */
    void addRecorder(RecorderVO recorderVO, Long createUser);

    /**
     * boss新建录音师
     *
     * @param recorderVO    录音师用户对象
     * @param createUser    创建用户id
     * @param distributorId 经销商id
     */
    void addRecorder(RecorderVO recorderVO, Long createUser, Long distributorId);

    /**
     * boss查询录音师列表
     *
     * @param pageNum         页码
     * @param pageSize        每页条数
     * @param name            姓名
     * @param phoneNumber     手机号
     * @param dialogFlowId    话术Id
     * @param status          状态
     * @param distributorId   经销商id
     * @param canModifySpeech 能否修改话术
     */
    PageResultObject<RecorderListVO> getRecordistList(Integer pageNum, Integer pageSize, String name, String phoneNumber, Long dialogFlowId, UserStatusEnum status, Long distributorId, Boolean canModifySpeech,SystemEnum systemType,Long bindDistributorId);

    List<IdNamePairVO<Long,String>> listAllRecordUser(String name, SystemEnum systemType);

    /**
     * 编辑录音师
     */
    void updateRecorder(RecorderVO recorderVO, Long updateUser);

    /**
     * 用户重置密码
     */
    String resetPassword(Long userId, Long updateUser);

    /**
     * 为客户重置密码
     */
    String resetTenantPassword(Long tenantId, Long updateUserId);

    /**
     * 为代理商重置密码
     */
    String resetDistributorPassword(Long distributorId, Long updateUserId);


    /**
     * 用户修改密码
     */
    void modifyPassword(Long userId, String newPassword, Long updateUserId);

    /**
     * 用户修改密码（校验旧密码）
     */
    void modifyPassword(Long userId, String oldPassword, String newPassword, Long updateUserId);


    /**
     * 登出
     */
    void logout(String token, Long userId, SystemEnum system);

    /**
     * 获取当前用户的名字
     */
    String getUser(Long userId);

    String getUserPhoneNumber(Long userId);

    String getName(Long userId);

    /**
     * 获取当前登录用户的信息
     *
     * @param userId 用户id
     * @param system 系统类型
     * @return 用户信息vo
     */
    UserInfoVO getUserInfo(Long userId, SystemEnum system);

    UserInfoVO getOpeLoginAiccUserInfo(UserPO aiccUser);

    /**
     * OPE登录AICC的用户获取AICC管理员的userId
     */
    Long changeAiccAdminUserId(Long opeUserId, Long aiccTenantId);

    /**
     * 获取CRM用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 用户信息vo
     */
    UserInfoVO getCRMUserInfo(Long userId, Long tenantId);

    /**
     * 获取MiniApp用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 用户信息vo
     */
    UserInfoVO getMiniAppUserInfo(Long userId, Long tenantId);

    /**
     * 通过用户的id和系统类型获取用户信息
     */
    UpdateUserInfoListVO getUserInfoList(Long userId, SystemEnum system);

    /**
     * 获取所有用户
     */
    List<OrganizationManagerListVO> getAllUser(Long tenantId);

    /**
     * 获取所有用户
     */
    List<UserListVO> getAllUserPO(Long tenantId);


    /**
     * 获取绑定微信的用户集合
     */
    List<UserByWechatListVO> getUserByWechat(Long tenantId);


    List<UserGuideListVO> getWechatUserForGuide(UserPO userPO, String searchWords, List<Long> editIds);

    /**
     * 绑定微信
     */
    Map<String, String> bindWechat(Long userId) throws Exception;

    boolean unbindWechat(Long userId, String bindType);

    boolean unbindWechatWeb(UserPO user, Long targetId);

    Map<String, String> generateLoginWechatCode() throws Exception;

    /**
     * 根据tenantId获取超级管理员的用户
     */
    UserPO selectAdminByTenantId(Long tenantId);


    /**
     * 获取OPE登录AICC新增客户的负责人
     */
    UserPO selectOpeLoginAIccUserByTenantId(Long tenantId);

    List<UserByWechatListVO> getUserByName(Long tenantId, String name);

    /**
     * boss系统编辑代理商时改变超管的账号状态
     */
    UserPO selectAdminByDistributorId(Long distributorId);

    List<UserPO> getByRoleId(Long roleId);

    UserPO selectByPhoneNumber(String phoneNumber);
    List<UserPO> selectByPhoneNumberList(List<String> phoneNumberList);

    String createUserForDistributor(DistributorOpeVO distributorOpeVO, Long createUserId);

    /**
     * ope查询用户列表
     *
     * @param pageNum     当前页数
     * @param pageSize    每页大小
     * @param name        姓名
     * @param nickname    用户名
     * @param phoneNumber 电话号码
     * @param roleId      角色id
     * @param userStatus  用户状态
     * @return 用户列表
     */
    PageResultObject<UserListVO> queryOpeUser(Integer pageNum, Integer pageSize, String name, String nickname, String phoneNumber, Long roleId, Long organizationId, UserStatusEnum userStatus);

    /**
     * boss查询用户列表
     *
     * @param pageNum       当前页数
     * @param pageSize      每页大小
     * @param name          姓名
     * @param nickname      用户名
     * @param phoneNumber   电话号码
     * @param roleId        角色id
     * @param userStatus    用户状态
     * @param distributorId 经销商id
     * @return 用户列表
     */
    PageResultObject<UserListVO> queryBossUser(Integer pageNum, Integer pageSize, String name, String nickname, String phoneNumber, Long roleId, UserStatusEnum userStatus, Long distributorId);

    /**
     * 根据distributorId查找id最小的user（临时用的接口）
     */
    UserPO selectMinIdByDistributorId(Long distributorId);

    /**
     * 根据tenantId查找id最小的user（临时用的接口）
     */
    UserPO selectMinIdByTenantId(Long tenantId);

    /**
     * 根据user id列表搜索user
     */
    List<UserPO> selectAllByIdList(List<Long> userIds);

    /**
     * 根据user id列表搜索user
     */
    List<UserPO> selectAllByIdList(List<Long> userIds, String userName);

    /**
     * 根据user id列表搜索user
     */
    List<UserPO> getAllByIdListRemote(List<Long> userIds);

    /**
     * 根据user id列表搜索user
     */
    List<UserPO> selectAllRecordUserByIdList(Collection<Long> userIds, RecordUserTypeEnum recordUserType, String userName);

    /**
     * 根据租户id查找用户简要信息列表
     *
     * @param tenantId 租户id
     * @return 用户简要信息列表
     */
    List<UserBriefVO> selectByTenantId(Long tenantId);

    /**
     * 判断用户是否有录音权限用于小程序端登录校验
     */
    boolean canRecordByLogin(UserPO userPO,SystemEnum systemEnum);

    boolean needCreateFamilyNameTemplate(UserPO userPO,SystemEnum systemEnum);

    void updateStatusByTenantId(Long tenantId, UserStatusEnum status, SystemEnum system, Long updateUserId);

    String update(UserPO user, SystemEnum type, Long organizationId, Long roleId, UserStatusEnum status);

    /**
     * ope/boss 个人中心获取个人信息
     */
    UserInfoVO getPersonInformation(Long userId, SystemEnum type);

    /**
     * ope/boss 个人中心编辑个人信息
     */
    String updatePersonInfo(UserPO user, SystemEnum type);

    /**
     * 根据id修改公众号信息
     */
    void updateWxPublicAccountOpenId(String wxPublicAccountOpenId, Long id, String wxNickName, String wxHeadImgUrl);


    /**
     * 是否允许创建新用户
     */
    Boolean isAllowCreateUser(String phone, SystemEnum system);

    /**
     * 删除crm端用户
     */
    void deleteUser(Long distributorId, Long tenantId, Long userId, Long updateUserId,SystemEnum systemType);

    /**
     * 查询用户状态
     */
    UserStatusEnum getUserStatus(UserPO user, SystemEnum system);

    /**
     * 获取客户代理信息
     *
     * @param distributorId 代理商id
     * @param phoneNumber   电话号码
     */
    UserPO selectByDistributorIdAndPhoneNumber(Long distributorId, String phoneNumber);


    UserPO selectByTenantIdAndPhoneNumber(Long tenantId, String phoneNumber);

    List<UserPO> selectByOrganization(Long organizationId);

    List<UserPO> selectUserBaseInfoListByIdSet(Set<Long> idSet);

    PageResultObject<SimpleUserInfoDTO> getUserByTenant(Integer pageNum, Integer pageSize, UserPO user, String username, String phoneNumber,SystemEnum systemType);

    UserPO selectUserFromDB(Long userId);

    void fillWithPassword(UserPO userPO, String password);

    /**
     * 查询所有子组织下的用户ID
     */
    List<Long> queryUserIdByParentOrganizationId(Long tenantId, Long organizationId, SystemEnum system);

    String generateDefaultPassword(String name, String phoneNumber);

    /**
     * 根据客户id，删除该客户下面的所有用户
     *
     * @param tenantId     客户id
     * @param updateUserId 操作人id
     */
    void deleteUserByTenant(Long tenantId, Long updateUserId);

    /**
     * 检查用户是否仍然是默认密码
     */
    Boolean checkUserIsDefaultPassword(Long userId);

    /**
     * 获取实施负责人的集合
     */
    PageResultObject<UserByWechatListVO> getDeploymentUser(Integer pageNum, Integer pageSize, String userName);

    /**
     * 获取tenant的用户
     *
     * @param tenantId 客户id
     * @param useId    用户id
     * @return 用户信息
     */
    UserPO getTenantUserWithNull(Long tenantId, Long useId);

    Boolean checkOpeUserHasViewDirectCustomPhoneNumberAuth(Long userId);

    Boolean checkOpeUserHasViewDistributorContactNumberAuth(Long userId);

    Boolean checkOpeUserHasViewDistributorCustomerPhoneNumberAuth(Long userId);

    Boolean checkOpeUserHasViewSubDistributorContactNumberAuth(Long userId);

    Boolean checkOpeUserHasViewSubDistributorCustomerPhoneNumberAuth(Long userId);

    /**
     * 检查当前登录ope用户是否只有查看自己的tenat和distributor的数据权限
     * @param userId 当前登录用户id
     */
    Boolean checkOpeUserIsOnlyReadSelfTenantData(Long userId);

    /**
     * 根据用户id和系统，获取用户的组织机构id
     */
    Long queryUserOrganizationId(Long userId, SystemEnum system);

    /**
     * 根据用户id和系统，获取用户的组织机构id
     */
    Map<Long, Long> queryUserOrganizationId(Collection<Long> userIdList, SystemEnum system);

    List<Long> queryUserIdByParentOrganizationId(Long organizationId, SystemEnum system);

    Set<Long> getOpeChildUserIdByParentUserId (Long parentId);
    /**
     * 查询用户
     */
    UserPO selectUserByTenantIdAndPhoneNumber(Long tenantId, String phoneNumber);

    PageResultObject<SimpleUserInfoDTO> getUserAllByTenant(Integer pageNum, Integer pageSize, UserPO user, String username, String phoneNumber);

    /**
     * 模糊查询所有用户信息，并支持分页
     */
    PageResultObject<UserByWechatListVO> getUserList(Integer pageNum, Integer pageSize, String searchWords, Long tenantId);

    List<UserGuideListVO> getUserSelect(Long tenantId, String searchWords, List<Long> editIds);

    String getAudioMinAppToken();

    Map<String,String> createWxQRCode(String scene, String page, Integer width, Boolean autoColor, Object lineColor, Boolean isHyaline,Integer currEnv,String System);

    PageResultObject<InsertRecorderVO> listUserByRecord(Integer pageNum, Integer pageSize, String searchWords);

    void selectDialogFlow(AddRecordVO addRecordVO);

    PageResultObject<RecorderListVO> getCrmRecorderList(Integer pageNum, Integer pageSize, String name, String phoneNumber, Long dialogFlowId, UserStatusEnum status, Long tenantId, Boolean canModifySpeech);

    List<AccountManagerNamePairVO> queryCSMAccountManagerNamePairList(String name);

    List<OrganizationManagerListVO> getAllCRMUser(Long tenantId, UserStatusEnum status);

    void forgetPassword(String phoneNumber,String password);

    UserInfoVO getOpeUserInfo(Long userId, SystemEnum ope);

    UserInfoVO getBossUserInfo(Long userId, SystemEnum boss);

    List<UserTitleVO> getUserTitleVOByUserIdList(List<Long> userIds, Boolean onlyWechatCpUser);

    Boolean checkOpeUserIsOnlyReadAnalyseSelfTenantData(Long userId);

    Boolean checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(Long userId);

    Boolean checkOpeCustomerAnalyseIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(Long userId);

    void batchUpdateRobotLimit(List<Long> userIds);

    List<Long> getUserIdsByOrganizationId(Long tenantId, Long organizationId, SystemEnum systemEnum);

    void unBindDialogFlow(AddRecordVO addRecordVO);

    List<UserPO> selectQcUser(Long tenantId);

    void updateRobotLimitNullById(Long userId);

    List<AccountManagerNamePairVO> queryFollowRecordNamePairList();

    void packageOrganizationUserInfo(List<? extends UserListVO> userListVOList,SystemEnum systemType);

    JobStartResultVO export(Long tenantId, Long userId, SystemEnum crm);

    Integer openOrBlockUpBatch(UserOpenOrBlockUpBatchBO userOpenOrBlockUpBatchBO);

    Integer deleteBatch(UserOpenOrBlockUpBatchBO userOpenOrBlockUpBatchBO);

    /**
     * 开启或关闭外呼监控
     *
     * @param enable true开启,false关闭
     */
    void setMonitor(Long userId, boolean enable);

    void setUserRole(Long userId, RoleTypeEnum roleType, Long updateUserId, SystemEnum systemEnum);

    PageResultObject<UserByWechatListVO> getUserListWithAuth(Integer pageNum, Integer pageSize, String searchWords, Long tenantId, Long userId);

    List<UserPO> selectEarlyWarningUserByTenantId(Long tenantId, String name);

    void updateGuideStatus(Long userId, List<String> guideStatus);

    /**
     * 查询客户经理列表
     */
    List<AccountManagerNamePairVO> queryAccountManagerNamePairList();

    void doWhenWsConnected(Long tenantId, Long userId);

	List<UserPO> selectByTenantIds(List<Long> tenantIds);

    List<UserByWechatListVO> queryOpeUserForSelect(String name);

    /**
     * 查询用户和角色信息
     */
    List<UserListVO> queryUserWithRole(SystemEnum system, List<Long> userIdList);


    void setUserJobToFolder(Boolean jobToFolder, Long userId);

    /**
     * 根据 system 和 authResourceUri 查询对应权限
     * @param userId userId
     * @param system SystemEnum
     * @param authResourceUriEnum AuthResourceUriEnum
     */
    Boolean checkAuthorityBySystemAndAuthResourceUri(Long userId, SystemEnum system, AuthResourceUriEnum authResourceUriEnum);

    JobStartResultVO importPlatformCustomerPerson(CustomerPersonImportRequestVO customerPersonImportRequestVO);

    /**
     * 查询所有用户，录音师排在前面
     * @param system 系统类型
     * @param name 用户名模糊搜索
     * @param tenantId 租户id
     * @param status 启用/停用
     * @param pageNum 页码
     * @param pageSize 每页数据条数
     * @return 用户id, name列表
     */
    PageResultObject<IdNamePairVO<Long, String>> listUserOrderByRecorderRole(SystemEnum system, String name, Long tenantId, UserStatusEnum status, int pageNum, int pageSize);

    Tuple2<Boolean, String> checkUserPwd(String password, Long userId);

    UserPwdExpireResultVO checkUserPwdExpire(String token, Long userId);

    void closeNotification(Long userId);

    void clearWphUserPwdNotification();

    void updUserPwdExpire(Long userId, String date);

	/**
	 * 查询OPE某个组织及其下级组的userId列表, 因为OPE的用户不能用user.tenant_id区分所以单独写接口
	 */
	Set<Long> selectOpeUserIdsByOrgId(Long organizationId);

    /**
     * 更新米糠云邮箱
     */
    int updateEmailByTenantIdForMiKangYun(Long tenantId);

    /**
     * 更新米糠云邮箱
     */
    int updateEmailByUserIdForMiKangYun(Long userId);

    /**
     * 校验天润账号绑定的唯一性
     * @param tenantId
     * @param tianRunUserName
     * @param userId
     * @return
     */
    boolean checkTianRunUserNameUnique(Long tenantId, String tianRunUserName, Long userId);

    /**
     * 绑定天润账号
     * @param userId
     * @param tianRunUserName
     */
    void updateTianRunUserName(Long userId, String tianRunUserName);
}
