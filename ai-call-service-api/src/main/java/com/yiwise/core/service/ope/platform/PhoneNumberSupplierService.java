package com.yiwise.core.service.ope.platform;

import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.callstats.CallPhoneCostStatsBO;
import com.yiwise.core.model.enums.phonenumber.FileTypeEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.phonenumber.*;
import com.yiwise.lcs.api.dto.SmsSupplierGatewayShowDTO;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface PhoneNumberSupplierService extends BasicService<PhoneNumberSupplierPO> {

    PageResultObject<PhoneNumberSupplierVO> getByCondition(PhoneNumberSupplierQueryVO phoneNumberSupplierQueryVO);

    List<PhoneNumberSupplierPO> querySupplierNameList(PhoneNumberSupplierQueryVO phoneNumberSupplierQueryVO);

    //给个数+1
    void countAddOne(Long phoneNumberSupplierId);

    //给个数-1
    void countSubtractOne(Long phoneNumberSupplierId);

    PhoneNumberSupplierPO selectById(Long phoneNumberSupplierId);

    List<PhoneNumberSupplierPO> selectByIdList(List<Long> phoneNumberSupplierIdList);

    /**
     * 查询线路供应商统计信息
     *
     * @return
     */
    List<CallPhoneCostStatsBO> queryStats(PhoneNumberSupplierStatsQueryVO queryVO);

    JobStartResultVO exportStats(PhoneNumberSupplierStatsQueryVO queryVO);

    PageResultObject<PhoneNumberSupplierVO> getList(PhoneNumberSupplierQueryVO phoneNumberSupplierQueryVO);

    SupplierAccountDetailVO getAccountDetail(Long supplierId);

    List<RechargeDetailVO> getRechargeDetail(Long supplierId, LocalDateTime startTime, LocalDateTime endTime);

    List<PhoneNumberConsumeVO> getSpendDetail(Long supplierId, LocalDate startDate, LocalDate endDate);

    List<PhoneNumberSupplierRechargeRecordPO> getRecordList(Long supplierId, LocalDate startDate, LocalDate endDate);

    List<PhoneNumberInvoiceRecordVO> getBillDetail(Long supplierId, LocalDateTime startTime, LocalDateTime endTime);

    List<PhoneNumberSupplierPO> getAllList();

    String createRechargeApproval(RechargeApprovalVO param, UserPO userPO);

    FileCodeVO uploadFile(MultipartFile file, FileTypeEnum type);

    String upload(MultipartFile file, Long supplierId);

    PhoneNumberInvoiceRecordPO addInvoice(PhoneNumberInvoiceVO invoiceVO, Long userId);

    PhoneNumberSupplierConsumePO saveConsume(PhoneNumberConsumeVO consume);

    ApprovalRecordVO getApprovalRecord(Long approvalId);

    JobStartResultVO exportRecharge(StatementExportVO exportVO);

    JobStartResultVO exportInvoice(InvoiceExportVO exportVO);

    JobStartResultVO exportConsume(ConsumeExportVO exportVO);

    JobStartResultVO exportRechargeRecord(RechargeRecordExportVO exportVO);

    void syncToBillStatisticService(PhoneNumberSupplierPO phoneNumberSupplierPO);

    SmsSupplierGatewayShowDTO selectGatewayInfoList(Long supplierId);

    void checkExpireSupplier();

    void cleanExpirationDate(Long phoneNumberSupplierId);

    List<PhoneNumberSupplierPO> selectByOrderId(Long orderId);
}
