package com.yiwise.core.config;

import com.yiwise.base.common.context.EnvEnum;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.bill.statistic.model.enums.StatisticServerEnvEnum;
import com.yiwise.core.model.enums.*;
import javaslang.control.Try;

import java.util.HashSet;
import java.util.Set;

/**
 * @Author: zqy
 * @Date: 2022/11/4 17:04
 */
public class CommonApplicationConstant {
    /**
     *  Customer Service console config
     */
    public static final String CONSISTENT_DIALOG_BEEP_URL = PropertyLoaderUtils.getProperty("consistent.dialog.beep.url");
    public static final String FORCASTING_CALLOUT_BEEP_URL = PropertyLoaderUtils.getProperty("forcasting.callout.beep.url");

    public static final String QUEUE_BEEP_URL = PropertyLoaderUtils.getProperty("queue.beep.url");
    public static final String UNCOMMON_HANGUP_BEEP_URL = PropertyLoaderUtils.getProperty("uncommon.hangup.beep.url");

    /**
     * cmcc-中国移动手机号码规则
     */
    public static final String cmccRegex = "^[1]{1}(([3]{1}[4-9]{1})|([4]{1}[78]{1})|([5]{1}[012789]{1})|([7]{1}[28])|([8]{1}[23478]{1})|([9]{1}[8]{1}))[0-9]{8}$|^[1]{1}[7]{1}[0]{1}[356]{1}[0-9]{7}$";
    /**
     * cucc-中国联通手机号码规则
     */
    public static final String cuccRegex = "^[1]{1}(([3]{1}[0-2]{1})|([4]{1}[56]{1})|([5]{1}[56]{1})|([6]{1}[6]{1})|([7]{1}[56]{1})|([8]{1}[56]{1}))[0-9]{8}$|^[1]{1}[7]{1}(([0]{1}[4789]{1})|([1]{1}[12356789]{1}))[0-9]{7}$";
    /**
     * cnc--中国电信手机号码规则
     */
    public static final String cncRegex = "^[1]{1}(([3]{1}[3]{1})|([4]{1}[9]{1})|([5]{1}[3]{1})|([7]{1}[347]{1})|([8]{1}[019]{1})|([9]{1}[9]{1}))[0-9]{8}$|^[1]{1}[7]{1}[0]{1}[012]{1}[0-9]{7}$";

    /**
     * gdcc-中国广电
     */
    public static final String gdccRegex = "^192\\d{8}$";


    // 算法训练
    public static final String ALGORITHM_REGISTER_ENV = PropertyLoaderUtils.getProperty("algorithm.register.env");
    public static final String ALGORITHM_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.predict.url");
    public static final String ALGORITHM_VERBAL_TRAINING_PREDICT_URL = PropertyLoaderUtils.getProperty("algorithm.verbal.training.url");


    public static final String STANDARD_NOTIFY_MODEL_PREDICT_URL = PropertyLoaderUtils.getProperty("ai.call.platform.back.standardNotifyModel.predict.url");
    /**
     * 导出文件配置
     */
    public static final String EXPORT_FILE_PREFIX = PropertyLoaderUtils.getProperty("export.filePrefix");
    public static final int JOB_QUEUE_SIZE = PropertyLoaderUtils.getIntProperty("batch.queue.size");
    public static final Integer EXPORT_CHUNK_SIZE = PropertyLoaderUtils.getIntProperty("export.chunkSize");
    public static final String OSS_EXCEL_ROOT = PropertyLoaderUtils.getProperty("oss.excel.root");
    /**
     * 默认ope超级管理员的几个id
     */
    public static final Long OPE_TENANT_ID = 0L;
    public static final Long OPE_DISTRIBUTOR_ID = 0L;
    public static final MQTypeEnum MQ_TYPE = Try.of(()-> CodeDescEnum.getFromDescOrThrow(MQTypeEnum.class, PropertyLoaderUtils.getProperty("rocketmq.type"))).getOrElseGet(e->null);
    public static final PublishModuleEnum MODULE_TYPE = Try.of(()->CodeDescEnum.getFromDescOrThrow(PublishModuleEnum.class, PropertyLoaderUtils.getProperty("publish.module.type"))).getOrElseGet(e->null);
    public static final ProductTypeEnum PRODUCT_TYPE = Try.of(()->CodeDescEnum.getFromDescOrThrow(ProductTypeEnum.class, PropertyLoaderUtils.getProperty("product.type"))).getOrElseGet(e->null);
    public static final NlsServerTypeEnum NLS_SERVER_TYPE = CodeDescEnum.getFromDescOrThrow(NlsServerTypeEnum.class, PropertyLoaderUtils.getProperty("nls.server.type"));
    public static final Boolean esEnable = PropertyLoaderUtils.getBooleanProperty("es.enable");
    // 获取启动环境
    public static final EnvEnum CURR_ENV = CodeDescEnum.getFromDescOrThrow(EnvEnum.class, PropertyLoaderUtils.getProperty("spring.profiles.active"));
    /**
     * 微服务名称
     */
    public static final String SERVICE_NAME = PropertyLoaderUtils.getProperty("service.name");

    public static String SCRM_GET_TEMPLATE_URL;

    public static class AlertPrefix {
        public static final String AX = "AX";
        public static final String ASR_DELAY_ALERT = "AsrDelayAlert";
        public static final String COMMON_ADD_WECHAT_ALERT = "commonAddWechat";
        public static final String JUZI_ADD_WECHAT_ALERT = "juziAddWechat";
        public static final String SCRM_ADD_WECHAT_ALERT = "scrmAddWechat";
        public static final String INTENT_RECOGNITION = "intent_recognition";
        public static final String ASR_CORRECTION = "asr_correction";
        public static final String GENDER_RECOGNITION = "gender_recognition";
        public static final String INTENT_LEVEL = "intent_level";
        public static final String ENTITY_EXTRACTION = "entity_extraction";
        public static final String ASR_RECONNECT = "asr_reconnect";
    }

    public static final Long CHANG_HE_TENANT_ID = PropertyLoaderUtils.getLongProperty("changhe.tenant.id");

    public static final int CUSTOMER_TRANSFER_MAX = 1000;
    public static final int CUSTOMER_ADD_ROBOT_CALL_JOB_MAX = 10;

    public static final String LOCAL_TMP_DIR = "/tmp/";

    // ope查询角色id
    public static Set<Long> OPE_IMPLEMENT_ROLE_IDS;

	/**
	 * 导出表头使用-自定义变量后缀
	 */
	public static final String propertiesPostfix = "-自定义";
	/**
	 * 导出表头使用-动态变量后缀
	 */
	public static final String dynamicVariablePostfix = "-动态";
	/**
	 * 导出表头使用-客户属性后缀
	 */
	public static final String customerPersonFieldPostfix = "-客户属性";

    static {
        reload();
        PropertyLoaderUtils.addPropertyChangeListener(CommonApplicationConstant::reload);
    }
    // apollo配置项发生变化时，重新加载
    public static void reload() {
        if (PropertyLoaderUtils.containsProperty("implement.role")) {
            OPE_IMPLEMENT_ROLE_IDS = PropertyLoaderUtils.getSetLongProperty("implement.role");
        } else {
            OPE_IMPLEMENT_ROLE_IDS = new HashSet<>();
        }

        // 获取scrm一客一链地址
        if (PropertyLoaderUtils.containsProperty("scrm.get.template.url")) {
            SCRM_GET_TEMPLATE_URL = PropertyLoaderUtils.getProperty("scrm.get.template.url");
        }else {
            SCRM_GET_TEMPLATE_URL = "";
        }

		// CURR_STATISTIC_ENV初始化
	    if (CommonApplicationConstant.CURR_ENV.isAliyun()) {
		    CURR_STATISTIC_ENV = StatisticServerEnvEnum.CONSUME;
	    } else if (CommonApplicationConstant.CURR_ENV.isFinance()) {
		    CURR_STATISTIC_ENV = StatisticServerEnvEnum.FINANCE;
	    } else if (CommonApplicationConstant.CURR_ENV.isHuoShan()) {
		    CURR_STATISTIC_ENV = StatisticServerEnvEnum.DOUDIAN;
	    }
    }

    /**
     * 系统类型
     */
    public static final String SCRM = "SCRM";
    /**
     * 系统类型
     */
    public static final String AICC = "AICC";

    /**
     * url参数
     */
    public static final String URL_PARAMETER = "?msg_signature=%s&timestamp=%s&nonce=%s";

    public static final Integer MAX_CALLBACK_COUNT = 1000;

	public static StatisticServerEnvEnum CURR_STATISTIC_ENV;
}
