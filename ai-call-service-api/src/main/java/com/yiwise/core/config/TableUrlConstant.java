package com.yiwise.core.config;

public class TableUrlConstant {
    public static final String APIOPE_RECHARGESTREAM_ALLLINERECHARGESTREAM = "/apiOpe/rechargeStream/allLineRechargeStream";
    public static final String APIOPE_FINANCE_STATS_TENANTLINE = "/apiOpe/finance/stats/tenantLine";
    public static final String APIOPE_FINANCE_STATS_TENANTLINEFORQIYU = "/apiOpe/finance/stats/tenantLineForQiyu";
    public static final String APIOPE_FINANCE_STATS_LINETEST = "/apiOpe/finance/stats/lineTest";
    public static final String APIOPE_FINANCE_STATS_DISTRIBUTORPRESTORESTATS = "/apiOpe/finance/stats/distributorPrestoreStats";
    public static final String APIOPE_DIRECTCUSTOMER_EXPORTLISTALLDIRECTCUSTOMER = "/apiOpe/directCustomer/exportListAllDirectCustomerNew";
    public static final String APIOPE_PHONECARD_EXPORT = "/apiOpe/phoneCard/export";
    public static final String APIOPE_DIALOGFLOWPHONENUMBERINDUSTRYRELATION_GETLIST = "/apiOpe/dialogFlowPhoneNumberIndustryRelation/getList";
    public static final String APIOPE_DISTRIBUTOR_EXPORTLISTDISTRIBUTOR = "/apiOpe/distributor/exportListDistributor";
    public static final String APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTDISTRIBUTORCUSTOMER = "/apiOpe/distributorCustomer/exportListDistributorCustomer";
    public static final String APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTDISTRIBUTORCUSTOMERFORQIYU = "/apiOpe/distributorCustomer/exportListDistributorCustomerForQiyu";
    public static final String APIOPE_SUBDISTRIBUTOR_EXPORTLISTSUBDISTRIBUTOR = "/apiOpe/subDistributor/exportListSubDistributor";
    public static final String APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTSUBDISTRIBUTORCUSTOMER = "/apiOpe/distributorCustomer/exportListSubDistributorCustomer";
    public static final String APIOPE_HUAWEIFREETRY_EXPORT = "/apiOpe/huaweifreetry/export";
    public static final String APIOPE_QCCOST_EXPORTDIRECTCUSTOMERQCCOST = "/apiOpe/qcCost/getCustomerQcCost";
    public static final String APIOPE_QCCOST_GETQCCOSTDETAIL = "/apiOpe/qcCost/getQcCostDetail";
    public static final String APIOPE_CALLRECORDINSPECT_STATS = "/apiOpe/callRecordInspect/stats";
    public static final String APIOPE_CALLRECORDINSPECT_LIST = "/apiOpe/callRecordInspect/list";
    public static final String APIOPE_VOSREPORT_LIST = "/apiOpe/vosReport/list";
    public static final String APIOPE_COMPLAINTHISTORY_LIST = "/apiOpe/complaintHistory/list";
    public static final String APIOPE_COMPLAINTHISTORY_STATS_TENANT = "/apiOpe/complaintHistory/stats/tenant";
    public static final String APIOPE_COMPLAINTHISTORY_STATS_TRACK = "/apiOpe/complaintHistory/stats/track";
    public static final String APIOPE_COMPLAINTHISTORY_STATS_SUPPLIER = "/apiOpe/complaintHistory/stats/supplier";
    public static final String APIOPE_RECOMMEND_DIALOGFLOWRECOMMEND_EXPORTDIALOGFLOWRECOMMEND = "/apiOpe/recommend/dialogFlowRecommend/exportDialogFlowRecommend";
    public static final String APIOPE_FINANCE_STATS2_SMS_PIECE = "/apiOpe/finance/stats2/sms/piece";
    public static final String APIOPE_FINANCE_STATS2_SMS_SUBSCRIBE = "/apiOpe/finance/stats2/sms/subscribe";
    public static final String APIOPE_FINANCE_STATS2_SMS_DISTRIBUTOR = "/apiOpe/finance/stats2/sms/distributor";
    public static final String APIENGINE_RECHARGESTREAM_GETTENANTLINESTREAM = "/apiEngine/rechargeStream/getTenantLineStream";
    public static final String APIOPE_FINANCE_STATS_EXPORT_TENANTLINE_CALLIN = "/apiOpe/finance/stats/export/tenantLine/callin";
    public static final String APIOPE_FINANCE_STATS_EXPORT_TENANTLINE_CALLOUT = "/apiOpe/finance/stats/export/tenantLine/callout";
    public static final String APIOPE_OPENSIPSACC_LIST = "/apiOpe/opensipsAcc/list";
    public static final String APIOPE_MAINBRAND_LIST = "/apiOpe/mainBrand/list";
    public static final String APIOPE_DIALOGFLOW_EXPORTINTENTBRANCH_CORPUS = "/apiOpe/dialogFlow/exportIntentBranch/corpus";
    public static final String APIOPE_DIALOGFLOW_EXPORTINTENTBRANCH_PROPERTY = "/apiOpe/dialogFlow/exportIntentBranch/property";
    public static final String APIOPE_DIALOGFLOWBILLING_LIST = "/apiOpe/dialogFlowBilling/list";
    public static final String APIOPE_PRIVACYNUMBER_QUERY = "/apiOpe/privacyNumber/query";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORTCALLCOST = "/apiEngine/springBatchJob/exportCallCost";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORTCALLRECORD = "/apiEngine/springBatchJob/exportCallRecord";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORTCUSTOMERWHITELIST = "/apiEngine/springBatchJob/exportCustomerWhiteList";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORTCALLINRECORDHISTORY = "/apiEngine/springBatchJob/exportCallInRecordHistory";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORTCALLINRECORDHISTORY_HUAYUN = "/apiEngine/springBatchJob/exportCallInRecordHistory/huayun";
    public static final String APIOPE_SPRINGBATCHJOB_EXPORT_PHONENUMBER_STATEMENT = "/apiOpe/phoneNumberSupplier/account/rechargeDetail";
    public static final String APIOPE_SPRINGBATCHJOB_EXPORT_PHONENUMBER_CONSUME = "/apiOpe/phoneNumberSupplier/account/spendDetail";
    public static final String APIOPE_SPRINGBATCHJOB_EXPORT_PHONENUMBER_RECHARGE_RECORD = "/apiOpe/phoneNumberSupplier/account/record";
    public static final String APIOPE_SPRINGBATCHJOB_EXPORT_PHONENUMBER_INVOICE = "/apiOpe/phoneNumberSupplier/account/invoiceDetail";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_RECEIVE_RECORD = "/apiEngine/smsReceiveRecord/export";
    public static final String APIENGINE_CALLBACKRECORD_EXPORT = "/apiEngine/callbackRecord/export";

    public static final String APIENGINE_SPRINGBATCHJOB_EXPORT_INTENTMESSAGE = "/apiEngine/intentMessage/export";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORT_SINGLESMS = "/apiEngine/customerSms/export";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORT_GROUPSMS = "/apiEngine/smsJob/export";
    public static final String APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_JOB_TASK = "/apiEngine/smsJobTask/export";

    public static final String APIOPE_BILL_EXPORT_ALIPAYRECHARGELIST = "/apiOpe/bill/exportAlipayRechargeList";
    public static final String APIOPE_BILL_EXPORT_OFFLINERECHARGELIST = "/apiOpe/bill/exportOfflineRechargeList";
    public static final String APIENGINE_BILLING_EXPORT_RECHARGEREFUNDLIST = "/apiEngine/billing/exportRechargeRefundList";
	public static final String APIBOSS_RECHARGESTREAM_EXPORT_OFFLINERECHARGELIST = "/apiBoss/rechargeStream/exportOfflineRechargeList";
	public static final String APIBOSS_RECHARGESTREAM_EXPORT_ACCOUNTRECHARGE = "/apiBoss/rechargeStream/exportAccountRecharge";
	public static final String APIBOSS_STOCK_EXPORT_ACCOUNTRECHARGE = "/apiBoss/stock/exportAllAccountRecharge";

    public static final String APIENGINE_EXPORT_SMS_TEMPLATE = "/apiEngine/smsTemplate/import";

    public static final String APIENGINE_EXPORT_PRIVACY_NUMBER_CALL_COST = "/apiEngine/privacynumber/exportPrivacyNumberCost";
    public static final String APIENGINE_EXPORT_PRIVACY_NUMBER_MONTH_COST = "/apiEngine/privacynumber/exportPrivacyNumberMonthCost";
    public static final String APIOPE_EXPORT_PRIVACY_NUMBER_CALL_COST = "/apiOpe/privacynumber/exportPrivacyNumberCost";
    public static final String APIOPE_EXPORT_PRIVACY_NUMBER_MONTH_COST = "/apiOpe/privacynumber/exportPrivacyNumberMonthCost";

}
