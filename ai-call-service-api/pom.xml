<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-call-back</artifactId>
        <groupId>com.yiwise</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ai-call-service-api</artifactId>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yiwise.common</groupId>
            <artifactId>base-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.11</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise.agent</groupId>
            <artifactId>yiwise-agent-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.reception</groupId>
            <artifactId>ai-call-reception-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.dialogflow</groupId>
            <artifactId>dialogflow-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-ObjectStorage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.middleware</groupId>
            <artifactId>Middleware-Tts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-lcs-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>

        <!-- elasticsearch-->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-spring-batch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dyplsapi20170525</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

        <!--huawei-->
        <dependency>
            <groupId>com.huawei.sis</groupId>
            <artifactId>java-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.sis</groupId>
            <artifactId>huaweicloud-java-sdk-custom_tts</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.sis</groupId>
            <artifactId>java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-tts</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nls</groupId>
            <artifactId>nls-sdk-long-asr</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>asr-client-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.monitorjbl</groupId>
            <artifactId>xlsx-streamer</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch</groupId>
                    <artifactId>elasticsearch</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-circuitbreaker</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-rcs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>customer-data-platform-rpc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>ai-call-batch-mq-api</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-isv-callback-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-billing-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-bill-statistic-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ti-net</groupId>
            <artifactId>clink-serversdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise.aicc</groupId>
            <artifactId>callin-service-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>aicc-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>callout-job-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yiwise</groupId>
            <artifactId>yiwise-blacklist-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.vip</groupId>
            <artifactId>osp-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.vip</groupId>
            <artifactId>vop-sdk-idlc</artifactId>
        </dependency>

    </dependencies>



    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-javadoc-plugin</artifactId>-->
<!--                <version>3.1.1</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>attach-javadocs</id>-->
<!--                        <goals>-->
<!--                            <goal>jar</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <additionalJOption>-Xdoclint:none</additionalJOption>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <encoding>UTF-8</encoding>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-gpg-plugin</artifactId>-->
<!--                <version>1.6</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>sign-artifacts</id>-->
<!--                        <phase>verify</phase>-->
<!--                        <goals>-->
<!--                            <goal>sign</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <gpgArguments>-->
<!--                                <arg>&#45;&#45;pinentry-mode</arg>-->
<!--                                <arg>loopback</arg>-->
<!--                            </gpgArguments>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>${repository.id}</id>
            <name>${repository.name}</name>
            <url>${repository.url}</url>
        </repository>
        <snapshotRepository>
            <id>${snapshotRepository.id}</id>
            <name>${snapshotRepository.name}</name>
            <url>${snapshotRepository.url}</url>
        </snapshotRepository>
    </distributionManagement>
</project>