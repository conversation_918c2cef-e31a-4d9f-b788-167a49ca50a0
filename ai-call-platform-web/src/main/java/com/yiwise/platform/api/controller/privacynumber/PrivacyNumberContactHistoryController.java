package com.yiwise.platform.api.controller.privacynumber;

import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberContactHistoryQueryVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberContactHistoryVO;
import com.yiwise.core.service.privacynumber.PrivacyNumberContactHistoryService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2021/08/27
 */
@Validated
@RestController
@RequestMapping("/apiPlatform/privacyNumberContactHistory")
public class PrivacyNumberContactHistoryController {

	@Resource
	private PrivacyNumberContactHistoryService privacyNumberContactHistoryService;

	@PostMapping("list")
	public ResultObject<PageResultObject<PrivacyNumberContactHistoryVO>> list(@RequestBody PrivacyNumberContactHistoryQueryVO query) {
		query.setTenantId(SecurityUtils.getTenantId());
		return ResultObject.success(privacyNumberContactHistoryService.query(query), "查询成功");
	}

	@PostMapping("export")
	public ResultObject<JobStartResultVO> export(@RequestBody PrivacyNumberContactHistoryQueryVO query) {
		query.setTenantId(SecurityUtils.getTenantId());
		query.setQueryUserId(SecurityUtils.getUserId());
		return ResultObject.success(privacyNumberContactHistoryService.export(query), "开始导出");
	}
}
