<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.core.dal.dao.SmsJobMessagePOMapper">
    <resultMap id="BaseResultMap" type="com.yiwise.core.dal.entity.SmsJobMessagePO">
        <id column="sms_job_message_id" jdbcType="BIGINT" property="smsJobMessageId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="sms_job_id" jdbcType="BIGINT" property="smsJobId"/>
        <result column="customer_person_id" jdbcType="BIGINT" property="customerPersonId"/>
        <result column="customer_person_name" jdbcType="VARCHAR" property="customerPersonName"/>
        <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber"/>
        <result column="send_status" jdbcType="TINYINT" property="sendStatus"
                typeHandler="com.yiwise.core.model.enums.handler.SendMessageStatusEnumHandler"/>
        <result column="sms_template_id" jdbcType="BIGINT" property="smsTemplateId"/>
        <result column="sms_template_name" jdbcType="VARCHAR" property="smsTemplateName"/>
        <result column="signature_name" jdbcType="VARCHAR" property="signatureName"/>
        <result column="cost_count" jdbcType="BIGINT" property="costCount"/>
        <result column="fee" jdbcType="BIGINT" property="fee"/>
        <result column="create_user_id" jdbcType="TIMESTAMP" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="message_detail" jdbcType="LONGVARCHAR" property="messageDetail"/>
        <result column="send_error_msg" jdbcType="VARCHAR" property="sendErrorMsg"/>
        <result column="report_status" jdbcType="VARCHAR" property="reportStatus"/>
        <result column="report_error_detail" jdbcType="VARCHAR" property="reportErrorDetail"/>
        <result column="user_receive_time" jdbcType="TIMESTAMP" property="userReceiveTime"/>
        <result column="mobile_operator" jdbcType="TINYINT" property="mobileOperator"
                typeHandler="com.yiwise.core.model.enums.handler.MobileOperatorEnumHandler"/>
        <result column="sid" jdbcType="VARCHAR" property="sid"/>
        <result column="properties" jdbcType="VARCHAR" property="properties"
                typeHandler="com.yiwise.core.model.enums.handler.base.MapToJsonTypeHandler"/>
        <result column="send_time" jdbcType="TIMESTAMP" property="sendTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        sms_job_message_id, tenant_id, customer_person_id, customer_person_name, phone_number, send_status,
        sms_template_id, sms_template_name, signature_name, fee,create_user_id, send_time,
        cost_count, create_time, message_detail, send_error_msg, report_status, report_error_detail, user_receive_time, sms_job_id, update_time, mobile_operator,sid, properties
    </sql>

    <sql id="Base_Column_List_Join">
        sjm
        .
        sms_job_message_id
        , sjm.tenant_id, sjm.customer_person_id, sjm.customer_person_name, sjm.phone_number, sjm.send_status,
        sjm.sms_template_id, sjm.sms_template_name, sjm.signature_name, sjm.fee, sjm.send_time,
        sjm.cost_count, sjm.create_time, sjm.message_detail, sjm.send_error_msg, sjm.report_status,
        sjm.report_error_detail, sjm.user_receive_time, sjm.sms_job_id, sjm.update_time, sjm.mobile_operator,sjm.sid
    </sql>

    <resultMap id="VO_ResultMap" type="com.yiwise.core.model.bo.sms.SmsJobMessageVO" extends="BaseResultMap">
        <result column="sms_job_name" jdbcType="VARCHAR" property="smsJobName"/>
    </resultMap>

    <resultMap id="DTO_ResultMap" type="com.yiwise.core.model.dto.SmsJobMessageDTO" extends="BaseResultMap">

    </resultMap>

    <select id="getSmsJobMessageListBySmsJob" resultMap="VO_ResultMap">
<!--        /*FORCE_IMCI_NODES*/ select /*+ SET_VAR(cost_threshold_for_imci=0) */-->
        select
        <include refid="Base_Column_List_Join"/>
        from sms_job_message sjm
        <include refid="condition"></include>
        ORDER BY sjm.sms_job_message_id DESC
        limit #{start}, #{end}
    </select>

    <select id="getSmsJobMessageCountBySmsJob" resultType="int">
<!--        /*FORCE_IMCI_NODES*/ select /*+ SET_VAR(cost_threshold_for_imci=0) */-->
        select
        count(1)
        from sms_job_message sjm
        <include refid="condition"></include>
    </select>
    <sql id="forceIndexSql">
        <if test="list == null and sendStatus == null and (reportStatus == null or reportStatus == '' ) and createBeginTime == null and createEndTime == null and (phoneNumber == null or phoneNumber =='') and (customerPersonName == null or customerPersonName == '') and (smsTemplateIds == null or smsTemplateIds.size == 0)">
            FORCE INDEX (idx_tenantid_id)
        </if>
        <if test="list == null and sendStatus != null and (reportStatus == null or reportStatus == '' ) and createBeginTime == null and createEndTime == null and (phoneNumber == null or phoneNumber =='') and (customerPersonName == null or customerPersonName == '') and (smsTemplateIds == null or smsTemplateIds.size == 0)">
            FORCE INDEX (idx_tenantid_sendstatus_id)
        </if>
        <if test="list == null and sendStatus != null and (reportStatus != null and reportStatus != '' ) and createBeginTime == null and createEndTime == null and (phoneNumber == null or phoneNumber =='') and (customerPersonName == null or customerPersonName == '') and (smsTemplateIds == null or smsTemplateIds.size == 0)">
            FORCE INDEX (idx_tenantid_sendstatus_reportstatus_id)
        </if>
    </sql>
    <sql id="condition">
        <include refid="forceIndexSql"/>
        <where>
            sjm.tenant_id = #{tenantId}
            <if test="costCount != null">
                and sjm.cost_count = #{costCount}
            </if>
            <if test="smsTemplateId != null">
                and sjm.sms_template_id = #{smsTemplateId}
            </if>
            <if test="smsTemplateIds != null and smsTemplateIds.size() > 0">
                and sjm.sms_template_id in
                <foreach collection="smsTemplateIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list != null and list.size > 0">
                and sjm.sms_job_id in
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sendStatus != null">
                and sjm.send_status =
                #{sendStatus, typeHandler=com.yiwise.core.model.enums.handler.SendMessageStatusEnumHandler}
            </if>
            <if test="customerPersonName != null and customerPersonName != ''">
                and customer_person_name like concat('%', #{customerPersonName}, '%')
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and sjm.phone_number = #{phoneNumber}
            </if>
            <if test="reportStatus != null and reportStatus!=''">
                and sjm.report_status = #{reportStatus}
            </if>
            <if test="createBeginTime != null and createEndTime != null">
                and sjm.send_time between #{createBeginTime} AND #{createEndTime}
            </if>
        </where>
    </sql>

    <select id="selectAvailableListBySmsJobIdLimit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        where sms_job_id = #{smsJobId}
        and send_status in (3, 4, 5) limit 20
    </select>

    <select id="countAvailableListBySmsJobId" resultType="java.lang.Long">
        select count(1)
        from sms_job_message
        where sms_job_id = #{smsJobId}
          and send_status in (3, 4, 5)
    </select>

    <select id="getSentMessageList" resultMap="BaseResultMap">
<!--        /*FORCE_IMCI_NODES*/ select /*+ SET_VAR(cost_threshold_for_imci=0) */-->
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        where sms_job_id = #{smsJobId}
        and tenant_id = #{tenantId}
        and send_status in (0, 2)
    </select>

    <select id="getSmsJobMessageListBySmsJobDTO" resultMap="DTO_ResultMap">
<!--        /*FORCE_IMCI_NODES*/ select /*+ SET_VAR(cost_threshold_for_imci=0) */-->
        select
        s.customer_person_name, s.phone_number, s.send_status, s.report_status,s.sms_template_id,
        s.sms_template_name, s.signature_name, s.fee, s.sms_job_id, s.create_user_id,
        s.cost_count, s.create_time, s.message_detail, s.send_error_msg, s.update_time, s.mobile_operator,s.tenant_id
        from sms_job_message s
        <where>
            s.tenant_id = #{tenantId}
            <if test="smsJobId != null">
                and s.sms_job_id = #{smsJobId}
            </if>
            <if test="costCount != null">
                and s.cost_count = #{costCount}
            </if>
            <if test="smsTemplateIds != null and smsTemplateIds.size() > 0">
                and s.sms_template_id in
                <foreach collection="smsTemplateIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sendStatus != null">
                and s.send_status = #{sendStatus}
            </if>
            <if test="customerPersonName != null and customerPersonName != ''">
                and s.customer_person_name like concat('%', #{customerPersonName}, '%')
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and s.phone_number = #{phoneNumber}
            </if>
            <if test="createBeginTime != null and createEndTime != null">
                and s.create_time between #{createBeginTime} AND #{createEndTime}
            </if>
            <if test="reportStatus != null and reportStatus != ''">
                and report_status = #{reportStatus}
            </if>
            <if test="list != null and list.size > 0">
                and sms_job_id in
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY s.sms_job_message_id DESC
    </select>

    <select id="selectSmsJobMessageBySid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        where sid in
        <if test="sids != null">
            <foreach collection="sids" item="sid" index="i" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>
        <if test="sids.size==0">
            ('')
        </if>

    </select>

    <select id="getSmsJobMessageBySid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        where sid = #{sid} and report_status IS NOT NULL
        LIMIT 1
    </select>

    <select id="getAllSentMessageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        where sms_job_id = #{smsJobId}
        and tenant_id = #{tenantId}
        order by sms_job_message_id desc
    </select>
    <select id="getListByIds" resultMap="VO_ResultMap">
        select
        <include refid="Base_Column_List"/>
        from sms_job_message
        <where>
            and sms_job_message_id in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="selectIdBySid" resultType="java.lang.Long">
        select sms_job_message_id
        from sms_job_message
        where sid = #{sid}
          and report_status IS NOT NULL LIMIT 1
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="smsJobMessageId">
        INSERT INTO sms_job_message (`tenant_id`, `sms_job_id`, `phone_number`, `customer_person_name`, `send_status`,
        `fee`, `send_time`, `cost_count`, `sms_template_id`, `message_detail`, `properties`, `send_error_msg`,
        `report_status`, `sid`, `sms_template_name`, `signature_name`, `create_user_id`, `update_user_id`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.smsJobId} , #{item.phoneNumber}, #{item.customerPersonName},
            #{item.sendStatus, typeHandler=com.yiwise.core.model.enums.handler.SendMessageStatusEnumHandler},
            #{item.fee}, #{item.sendTime}, #{item.costCount}, #{item.smsTemplateId}, #{item.messageDetail},
            #{item.properties}, #{item.sendErrorMsg}, #{item.reportStatus}, #{item.sid}, #{item.smsTemplateName},
            #{item.signatureName}, #{item.createUserId}, #{item.updateUserId})
        </foreach>
    </insert>

    <select id="selectSmsJobMessageIdAfterLocalDateTime" resultType="java.lang.Long">
        SELECT sms_job_message_id
        FROM sms_job_message
        WHERE send_time > #{localDateTime}
        ORDER BY send_time LIMIT 1
    </select>
    <select id="selectSmsJobMessageIdAfterEqualLocalDateTime" resultType="java.lang.Long">
        SELECT sms_job_message_id
        FROM sms_job_message
        WHERE send_time <![CDATA[ >= ]]> #{localDateTime}
        ORDER BY send_time LIMIT 1
    </select>

    <select id="selectOrderBySmsJobMessageId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sms_job_message
        WHERE sms_job_message_id > #{smsJobMessageId} AND sms_job_message_id <![CDATA[ <= ]]> #{lastSmsJobMessageId}
        ORDER BY sms_job_message_id
        LIMIT #{batchSize}
    </select>
</mapper>