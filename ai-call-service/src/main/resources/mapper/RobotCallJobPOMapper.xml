<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiwise.core.dal.dao.RobotCallJobPOMapper">
    <resultMap id="RobotTaskDTOBaseResultMap" type="com.yiwise.core.model.dto.RobotCallJobDTO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
        <result column="status" jdbcType="TINYINT" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
        <result column="phone_type" jdbcType="TINYINT" property="phoneType"
                typeHandler="com.yiwise.core.model.enums.handler.PhoneTypeEnumHandler"/>
        <result column="phone_number_id" jdbcType="BIGINT" property="phoneNumberId"/>
    </resultMap>

    <resultMap id="RobotTaskSimpleInfoMap" type="com.yiwise.core.model.dto.RobotCallJobSimpleInfoDTO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="name" jdbcType="VARCHAR" property="robotCallJobName"/>
    </resultMap>

    <resultMap id="RobotTaskPOBaseResultMap" type="com.yiwise.core.dal.entity.RobotCallJobPO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
        <result column="call_out_plan_id" jdbcType="BIGINT" property="callOutPlanId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
        <result column="status" jdbcType="TINYINT" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="sms_alert_level_code" jdbcType="VARCHAR" property="smsAlertLevelCode"
                typeHandler="com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler"/>
        <result column="sms_template_id" jdbcType="INTEGER" property="smsTemplateId"/>
        <result column="ip_address" jdbcType="VARCHAR" property="ipAddress"/>
        <result column="hostname" jdbcType="VARCHAR" property="hostname"/>
        <result column="version_number" jdbcType="INTEGER" property="versionNumber"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="early_warning_alert_users" jdbcType="VARCHAR" property="earlyWarningAlertUsers"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="inactive_time_list" jdbcType="VARCHAR" property="inactiveTimeList"
                typeHandler="com.yiwise.core.model.enums.handler.base.InactiveTimeListToJsonTypeHandler"/>
        <result column="inactive_date_list" jdbcType="VARCHAR" property="inactiveDateList"
                typeHandler="com.yiwise.core.model.enums.handler.base.DateListToJsonTypeHandler"/>
        <result column="on_working_days" jdbcType="BOOLEAN" property="onWorkingDays"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="days_of_week" jdbcType="VARCHAR" property="daysOfWeek"
                typeHandler="com.yiwise.core.model.enums.handler.DayOfWeekSetTypeHandler"/>
        <result column="daily_start_time" jdbcType="TIMESTAMP" property="dailyStartTime"/>
        <result column="daily_end_time" jdbcType="TIMESTAMP" property="dailyEndTime"/>
        <result column="redial_condition" jdbcType="VARCHAR" property="redialCondition"
                typeHandler="com.yiwise.core.model.enums.handler.DialStatusEnumSetTypeHandler"/>
        <result column="connect_redial_condition" jdbcType="VARCHAR" property="connectRedialCondition"
                typeHandler="com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler"/>
        <result column="customer_white_group_ids" jdbcType="VARCHAR" property="customerWhiteGroupIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="redial_interval" jdbcType="INTEGER" property="redialInterval"/>
        <result column="redial_times" jdbcType="TINYINT" property="redialTimes"/>
        <result column="concurrency_quota" jdbcType="INTEGER" property="concurrencyQuota"/>
        <result column="cs_staff_group_id" jdbcType="BIGINT" property="csStaffGroupId"/>
        <result column="phone_type" jdbcType="TINYINT" property="phoneType"
                typeHandler="com.yiwise.core.model.enums.handler.PhoneTypeEnumHandler"/>
        <result column="condition_cancel_send" jdbcType="TINYINT" property="conditionCancelSend" typeHandler="com.yiwise.core.model.enums.handler.ConditionCancelSendEnumHandler"/>
        <result column="robot_call_job_type" jdbcType="INTEGER" property="robotCallJobType" typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobTypeEnumHandler"/>
        <result column="cs_distribution_method" jdbcType="TINYINT" property="csDistributionMethod" typeHandler="com.yiwise.core.model.enums.handler.CsDistributionMethodEnumHandler"/>
        <result column="wechat_push_condition_list" jdbcType="VARCHAR" property="wechatPushConditionList"
                typeHandler="com.yiwise.core.model.enums.WechatPushConditionListTypeHandler"/>
        <result column="compress_audio" property="compressAudio"/>
        <result column="use_yiwise_asr" property="useYiwiseAsr" />
        <result column="next_start_datetime" jdbcType="TIMESTAMP" property="nextStartDatetime"/>
        <result column="next_end_datetime" jdbcType="TIMESTAMP" property="nextEndDatetime"/>
        <result column="priority" property="priority"/>
        <result column="use_free_ai" property="useFreeAi" />
        <result column="robot_call_job_type" property="robotCallJobType" typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobTypeEnumHandler"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="folder_id" jdbcType="VARCHAR" property="folderId"/>
        <result column="tenant_call_intercept_id" jdbcType="BIGINT" property="tenantCallInterceptId"/>
        <result column="time_robot_count_list" property="timeRobotCountList" typeHandler="com.yiwise.core.model.enums.handler.base.TimeRobotCountListToJsonTypeHandler"/>
        <result column="base_robot_count" jdbcType="INTEGER" property="baseRobotCount"/>
        <result column="should_use_robot_count" jdbcType="INTEGER" property="shouldUseRobotCount"/>
        <result column="enable_time_robot" property="enableTimeRobot" />
        <result column="enable_elastic_robot" property="enableElasticRobot" />
        <result column="base_concurrency_quota" jdbcType="INTEGER" property="baseConcurrencyQuota"/>
        <result column="sms_template_intent_list" jdbcType="VARCHAR" property="smsTemplateIntentList"
                typeHandler="com.yiwise.core.model.enums.handler.base.SmsTemplateIntentListTypeHandler"/>
        <result column="redial_setting_list" jdbcType="VARCHAR" property="redialSettingList"
                typeHandler="com.yiwise.core.model.enums.handler.base.RedialSettingListTypeHandler" />
        <result column="sms_push_type" jdbcType="VARCHAR" property="smsPushType"
                typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.SmsPushEnumHandler" />
        <result column="add_friend_status" property="addFriendStatus"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobAddFriendStatusEnumHandler"/>
        <result column="add_friend_level_code" jdbcType="VARCHAR" property="addFriendLevelCode"
                typeHandler="com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler"/>
        <result column="wechat_cp_add_friend_token_id" jdbcType="VARCHAR" property="wechatCpAddFriendTokenId"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="wechat_cp_add_friend_accounts" jdbcType="VARCHAR" property="wechatCpAddFriendAccounts"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="wechat_cp_add_friend_message" jdbcType="VARCHAR" property="wechatCpAddFriendMessage"/>
        <result column="wechat_cp_add_friend_delay_time" jdbcType="INTEGER" property="wechatCpAddFriendDelayTime"/>
        <result column="last_heart_beat_time" jdbcType="TIMESTAMP" property="lastHeartBeatTime"/>
        <result column="aike_wechat_list" jdbcType="LONGVARCHAR" property="aikeWechatList"
                typeHandler="com.yiwise.core.model.enums.handler.AikeWechatListToJsonTypeHandler"/>
        <result column="add_wechat_config" jdbcType="LONGVARCHAR" property="addWechatConfig"
                typeHandler="com.yiwise.core.model.enums.handler.CallJobAddWechatConfigToJsonTypeHandler"/>
        <result column="sms_job_config" jdbcType="LONGVARCHAR" property="smsJobConfig"
                typeHandler="com.yiwise.core.model.enums.handler.CallJobSmsJobConfigToJsonTypeHandler"/>
        <result column="wechat_cp_add_friend" jdbcType="INTEGER" property="wechatCpAddFriend"
                typeHandler="com.yiwise.core.model.enums.handler.WechatCpAddFriendEnumHandler"/>
        <result column="flash_message_template_id" jdbcType="BIGINT" property="flashMessageTemplateId"/>
        <result column="function_version" jdbcType="INTEGER" property="functionVersion"
                typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobVersionEnumHandler"/>
        <result column="redial_type" jdbcType="INTEGER" property="redialType"
                typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobRedialTypeEnumHandler"/>
        <result column="sms_push_answered" jdbcType="TINYINT" property="smsPushAnswered" />
        <result column="filter_strategy_ids" jdbcType="VARCHAR" property="filterStrategyIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="add_wechat_type" jdbcType="INTEGER" property="addWechatType"/>
        <result column="option_ids" jdbcType="VARCHAR" property="optionIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="rule_id" jdbcType="VARCHAR" property="ruleId"/>
        <result column="sort_id" jdbcType="BIGINT" property="sortId"/>
        <result column="enable_top" jdbcType="TINYINT" property="enableTop"/>
        <result column="user_group_ids" jdbcType="VARCHAR" property="userGroupIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongListToJsonTypeHandler"/>
        <result column="auto_import" jdbcType="TINYINT" property="autoImport"/>
        <result column="wph_rule_ids" jdbcType="VARCHAR" property="wphRuleIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="wph_coupon_pool_id" jdbcType="BIGINT" property="wphCouponPoolId"/>
        <result column="wph_coupon_pool_switch" jdbcType="BIGINT" property="wphCouponPoolSwitch"/>
        <result column="brand_black_group_ids" jdbcType="VARCHAR" property="brandBlackGroupIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
        <result column="risk_control_strategy_id" jdbcType="BIGINT" property="riskControlStrategyId"/>
        <result column="enable_remote_filter" jdbcType="TINYINT" property="enableRemoteFilter"/>
        <result column="qr_code_id" jdbcType="VARCHAR" property="qrCodeId"/>
        <result column="qr_code_title" jdbcType="VARCHAR" property="qrCodeTitle"/>
        <result column="qr_code_content" jdbcType="VARCHAR" property="qrCodeContent"/>
        <result column="qr_code_background" jdbcType="VARCHAR" property="qrCodeBackground"/>
        <result column="qr_code_expire_time" jdbcType="TIMESTAMP" property="qrCodeExpireTime"/>
        <result column="skip_valid_properties" jdbcType="TINYINT" property="skipValidProperties"/>
        <result column="virtual_sms_template_id" jdbcType="BIGINT" property="virtualSmsTemplateId"/>
        <result column="auto_import_begin" jdbcType="TIMESTAMP" property="autoImportBegin"/>
        <result column="auto_import_end" jdbcType="TIMESTAMP" property="autoImportEnd"/>
        <result column="wph_production_push_code" jdbcType="VARCHAR" property="wphProductionPushCode"/>
        <result column="wph_push_type" jdbcType="TINYINT" property="wphPushType"
                typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.WphPushTypeEnumHandler"/>
        <result column="wph_coupon_sms_template_id" jdbcType="BIGINT" property="wphCouponSmsTemplateId"/>
        <result column="wph_product_sms_template_id" jdbcType="BIGINT" property="wphProductSmsTemplateId"/>
        <result column="wph_coupon_out_call_info" jdbcType="VARCHAR" property="wphCouponOutCallInfo"/>
        <result column="wph_product_out_call_info" jdbcType="VARCHAR" property="wphProductOutCallInfo"/>
        <result column="wph_fx_id" jdbcType="BIGINT" property="wphFxId"/>
        <result column="re_send_sms_template_ids" jdbcType="VARCHAR" property="reSendSmsTemplateIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongListToJsonTypeHandler"/>
        <result column="enable_re_send_sms" jdbcType="TINYINT" property="enableReSendSms"/>
        <result column="bot_sms_template_id" jdbcType="BIGINT" property="botSmsTemplateId"/>
        <result property="enableWphSendSms" column="enable_wph_send_sms" jdbcType="TINYINT"/>
    </resultMap>
    
    <resultMap id="robotCallJobConcurrenceResultMap" type="com.yiwise.core.dal.entity.RobotCallJobPO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="status" jdbcType="TINYINT" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="concurrency_quota" property="concurrencyQuota"/>
    </resultMap>

    <resultMap id="ConcurrencyInfoResultMap" type="com.yiwise.core.model.vo.robotcalljob.ConcurrencyInfoVO">
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="concurrency_count" jdbcType="BIGINT" property="concurrencyCount"/>
    </resultMap>

    <sql id="base_column">
        robot_call_job_id
        , tenant_id, name,call_out_plan_id, dialog_flow_id, sms_alert_level_code, sms_template_id, concurrency_quota,
        ip_address, hostname, version_number, status, robot_count, create_user_id,phone_type,
        early_warning_alert_users, create_time,
        redial_condition, redial_interval, redial_times, daily_start_time, daily_end_time, inactive_time_list, condition_cancel_send, robot_call_job_type,
        cs_distribution_method, next_start_datetime, customer_white_group_ids,filter_strategy_ids,tenant_call_intercept_id  next_end_datetime, priority, use_free_ai, tenant_call_intercept_id, inactive_date_list,
        on_working_days, time_robot_count_list, base_robot_count, enable_time_robot, enable_elastic_robot, should_use_robot_count, base_concurrency_quota,sms_template_intent_list,redial_setting_list,
        sms_push_type,add_friend_status,add_friend_level_code,wechat_cp_add_friend_token_id,wechat_cp_add_friend_message,wechat_cp_add_friend_accounts,wechat_cp_add_friend_delay_time,
        aike_wechat_list, add_wechat_config, sms_job_config, wechat_cp_add_friend, redial_type, sms_push_answered, filter_strategy_ids, add_wechat_type, option_ids, rule_id, sort_id, enable_top, flash_message_template_id, user_group_ids, auto_import,
        brand_black_group_ids, risk_control_strategy_id,enable_remote_filter, qr_code_id, qr_code_title, qr_code_content, qr_code_background, qr_code_expire_time, skip_valid_properties, virtual_sms_template_id, auto_import_begin, auto_import_end,
        re_send_sms_template_ids, enable_re_send_sms, bot_sms_template_id,enable_wph_send_sms
    </sql>

    <resultMap id="RobotCallJobNameResultMap" type="com.yiwise.core.model.bo.robotcalljob.SimpleRobotCallJobInfoBO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="first_start_date" jdbcType="TIMESTAMP" property="firstStartDate"/>
        <result column="last_heart_beat_time" jdbcType="TIMESTAMP" property="lastHeartBeatDate"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
        <result column="transfer_phone_choose_type" jdbcType="INTEGER" property="transferPhoneChooseType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
    </resultMap>

    <sql id="time_column">
        robot_call_job_id, tenant_id, name, last_heart_beat_time, daily_start_time, daily_end_time,
        inactive_time_list, end_time, days_of_week, inactive_date_list, status, on_working_days
    </sql>
    <resultMap id="TimeOutRobotCallJobResultMap" type="com.yiwise.core.model.bo.robotcalljob.TimeOutRobotCallJobInfoBO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="daily_start_time" jdbcType="TIMESTAMP" property="dailyStartTime"/>
        <result column="daily_end_time" jdbcType="TIMESTAMP" property="dailyEndTime"/>
        <result column="last_heart_beat_time" jdbcType="TIMESTAMP" property="lastHeartBeatTime"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="inactive_time_list" jdbcType="VARCHAR" property="inactiveTimeList"
                typeHandler="com.yiwise.core.model.enums.handler.base.InactiveTimeListToJsonTypeHandler"/>
        <result column="inactive_date_list" jdbcType="VARCHAR" property="inactiveDateList"
                typeHandler="com.yiwise.core.model.enums.handler.base.DateListToJsonTypeHandler"/>
        <result column="on_working_days" jdbcType="BOOLEAN" property="onWorkingDays"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="days_of_week" jdbcType="VARCHAR" property="daysOfWeek"
                typeHandler="com.yiwise.core.model.enums.handler.DayOfWeekSetTypeHandler"/>
        <result column="status" jdbcType="VARCHAR" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
    </resultMap>


    <resultMap id="RobotJobAlertResultMap" type="com.yiwise.core.model.alertmessage.AbnormalRobotCallJobAlertMsg">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="mode" jdbcType="TINYINT" property="mode"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="robot_count" jdbcType="TINYINT" property="robotCount"/>
        <result column="concurrency_quota" jdbcType="TINYINT" property="concurrencyQuota"/>
        <result column="ip_address" jdbcType="VARCHAR" property="ipAddress"/>
    </resultMap>

    <resultMap id="RobotCallJobListInfoVOResultMap" type="com.yiwise.core.model.vo.robotcalljob.RobotCallJobListInfoVO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="status" jdbcType="TINYINT" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
        <result column="hang_up_type" jdbcType="TINYINT" property="hangUpType"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobHangUpTypeEnumHandler"/>
        <result column="hang_up_reason" jdbcType="VARCHAR" property="hangUpReason"/>
        <result column="next_start_datetime" jdbcType="TIMESTAMP" property="nextStartDatetime"/>
        <result column="priority" jdbcType="BIGINT" property="priority"/>
        <result column="use_free_ai" jdbcType="TINYINT" property="useFreeAi"/>
        <result column="use_yiwise_asr" jdbcType="TINYINT" property="useYiwiseAsr"/>
        <result column="in_queue_reason" jdbcType="VARCHAR" property="inQueueReason"/>
        <result column="in_queue_type" jdbcType="TINYINT" property="inQueueType"
                typeHandler="com.yiwise.core.model.enums.robotcalljob.handler.InQueueEnumHandler"/>
        <result column="last_edit_time" jdbcType="TIMESTAMP" property="lastEditTime"/>
        <result column="add_friend_status" jdbcType="TINYINT" property="addFriendStatus"/>
        <result column="collection_mark" jdbcType="INTEGER" property="collectionMark"/>
        <result column="option_ids" jdbcType="VARCHAR" property="optionIds"
                typeHandler="com.yiwise.base.common.handler.LongSetToJsonTypeHandler"/>
        <result column="last_heart_beat_time" jdbcType="TIMESTAMP" property="lastHeartBeatTime"/>
        <result column="folder_id" jdbcType="BIGINT" property="folderId"/>
        <result column="robot_count" jdbcType="INTEGER" property="robotCount"/>
    </resultMap>


    <resultMap id="MarkSystemResultMap" type="com.yiwise.core.model.vo.robotcalljob.RobotCallJobMarkSystemVO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="name" jdbcType="VARCHAR" property="robotCallJobName"/>
    </resultMap>

    <resultMap id="AntResultMap" type="com.yiwise.core.model.vo.robotcalljob.RobotCallJobAntResponseVO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="taskId"/>
        <result column="name" jdbcType="VARCHAR" property="taskName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="concurrency_quota" jdbcType="INTEGER" property="maxConcurrency"/>
    </resultMap>

    <select id="selectRobotCallJob" resultType="com.yiwise.core.model.vo.robotcalljob.RobotCallJobListInfoVO">
        select
        `robot_call_job_id` AS robotCallJobId, `name`, `status`, `create_user_id` AS createUserId,
        `create_time` AS createTime, dialog_flow_id AS dialogFlowId, priority
        from `robot_call_job`
        <where>
            tenant_id = #{tenantId}
            <if test="status != null">
                and `status` = #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
            </if>
            <if test="robotCallJobId != null">
                and robot_call_job_id = #{robotCallJobId}
            </if>
            <if test="name != null and name !=''">
                and `name` like concat('%', #{name},'%')
            </if>
            <if test="beginCreateDate != null and endCreateDate != null">
                and create_time between #{beginCreateDate} AND date_add(#{endCreateDate}, INTERVAL 1 DAY)
            </if>
            <if test="robotCallJobId != null">
                and robot_call_job_id = #{robotCallJobId}
            </if>
        <if test="didiStatus != null and didiStatus ==1">
            and status in (0,1,2,3)
        </if>
            and enabled_status = 0
            and `status` != 15
        </where>
        order by robot_call_job_id desc
    </select>

    <select id="getRobotCallJobListForAnt" resultMap="AntResultMap">
        SELECT robot_call_job_id, `name`, `status`, concurrency_quota
        FROM robot_call_job
        WHERE tenant_id = #{request.tenantId} AND enabled_status = 0 AND `status` NOT IN (6, 15, 16)
        <if test="request.taskId != null">
            AND robot_call_job_id = #{request.taskId}
        </if>
        <if test="request.lastCallTime != null">
            AND last_end_run_time <![CDATA[ >= ]]> #{request.lastCallTime}
        </if>
    </select>

    <select id="selectSimpleRobotCallJob" resultType="com.yiwise.core.model.vo.robotcalljob.RobotCallJobListInfoVO">
        select
        `robot_call_job_id` AS robotCallJobId, `name`, `status`, `create_user_id` AS createUserId,
        `create_time` AS createTime
        from `robot_call_job`
        <where>
            tenant_id = #{tenantId}
            <if test="name != null and name !=''">
                and `name` like concat('%', #{name},'%')
            </if>
            <if test="robotCallJobIds != null and robotCallJobIds.size() > 0">
                and robot_call_job_id in
                <foreach collection="robotCallJobIds" open="(" close=")" separator=", " item="id">
                    #{id}
                </foreach>
            </if>
            and enabled_status = 0
            and `status` != 15
        </where>
        order by robot_call_job_id desc
    </select>


    <resultMap id="SimpleCallJob" type="com.yiwise.core.model.dto.SimpleCallJobDTO">
        <result column="robot_call_job_id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="dialog_flow_id" jdbcType="BIGINT" property="dialogFlowId"/>
    </resultMap>
    <update id="updateRobotCallJobStatusById" parameterType="com.yiwise.core.dal.entity.RobotCallJobPO">
        UPDATE `robot_call_job` SET
        <if test="hangUpReason!=null and hangUpReason!=''">
            hang_up_reason = #{hangUpReason},
        </if>
        <if test="type!=null">
            hang_up_type = #{type},
        </if>
        <if test="inQueueType != null">
            in_queue_type = #{inQueueType, typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.InQueueEnumHandler},
        </if>
        <if test="inQueueReason != null">
            in_queue_reason = #{inQueueReason},
        </if>
        `status` = #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler},
        <if test="updateHeartBeat == true">
            `last_heart_beat_time` = now(),
        </if>
        update_time = update_time
        WHERE `robot_call_job_id` = #{callJobId} AND `status` != 15
        <if test="preStatus != null">
            and `status` = #{preStatus, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
        </if>;
    </update>

    <select id="getRobotCallJobStatus" resultType="java.lang.Integer">
        SELECT `status` FROM `robot_call_job` WHERE `robot_call_job_id` = #{callJobId};
    </select>

    <update id="updateRobotCallJobLastHeartBeatTime">
        <![CDATA[
        UPDATE `robot_call_job` SET `last_heart_beat_time` = now() WHERE `robot_call_job_id` = #{callJobId} and `last_heart_beat_time` < date_sub(now(), INTERVAL 10 SECOND);
        ]]>
    </update>

    <update id="updateRobotCallJobLastHeartBeatTimeAndResetJobIpAddressNotUpdateLastModifyTime">
        UPDATE `robot_call_job` SET `last_heart_beat_time` = now(), ip_address = NULL, update_time = update_time WHERE `robot_call_job_id` = #{callJobId};
    </update>

    <select id="selectOrderedRobotCallJobLimited" resultMap="RobotCallJobListInfoVOResultMap">
        select
        `robot_call_job_id`, `name`, `status`, `create_user_id`, `create_time`, `hang_up_type`, `hang_up_reason`,
        `dialog_flow_id`, `next_start_datetime`, cs_distribution_method, priority, `use_free_ai`, `use_yiwise_asr`, inactive_date_list,
        `in_queue_reason`, `in_queue_type`, `last_edit_time`, add_friend_status, `collection_mark`, option_ids, last_heart_beat_time, `folder_id`, robot_count
        from `robot_call_job`
        <where>
            tenant_id = #{tenantId}
            <if test="status != null and status.code == 5">
                and (`status` = 5 or `status` = 11)
            </if>
            <if test="status != null and status.code != 5">
                and `status` = #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
            </if>
            <if test="statusSet != null and statusSet.size() > 0">
                and `status` in
                <foreach collection="statusSet" open="(" close=")" separator=", " item="status">
                    #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
                </foreach>
            </if>
            <if test="name != null and name !=''">
                and ( `name` like concat('%', #{name},'%') OR robot_call_job_id = #{name} )
            </if>
            <if test="beginCreateDate != null and endCreateDate != null">
                and create_time between #{beginCreateDate} AND date_add(#{endCreateDate}, INTERVAL 1 DAY)
            </if>
            <if test="userIdList != null and userIdList.size > 0">
                and create_user_id in
                <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="collectionMark != null">
                and `collection_mark` = #{collectionMark}
            </if>
            <if test="folderId != null">
                <choose>
                    <when  test="name != null and name !='' and subFolderIds != null and subFolderIds.size() > 0 " >
                        and folder_id in
                        <foreach collection="subFolderIds" open="(" close=")" separator=", " item="id">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and folder_id = #{folderId}
                    </otherwise>
                </choose>
            </if>
            <if test="exceptFolderId != null">
                and (folder_id != #{exceptFolderId} or folder_id is null)
            </if>
            <if test="queryAllJobs == false">
                and folder_id is null
            </if>
            <if test="enableTop != null">
                and enable_top = #{enableTop}
            </if>
            <if test="jobIds != null and jobIds.size > 0">
                and robot_call_job_id in
                <foreach collection="jobIds" open="(" close=")" separator=", " item="jobId">
                    #{jobId}
                </foreach>
            </if>
            and enabled_status = 0
            and `status` != 15
        </where>
        <if test="orderBy == null or orderBy.getCode() == 0">
            order by robot_call_job_id desc
        </if>
        <if test="orderBy != null and orderBy.getCode() == 1">
            order by last_edit_time desc
        </if>
        <if test="orderBy != null and orderBy.getCode() == 2">
            order by sort_id desc, robot_call_job_id desc
        </if>
    </select>

    <update id="updateLastEditTimeById" parameterType="long">
        update robot_call_job SET `last_edit_time` = CURRENT_TIMESTAMP
        where `robot_call_job_id` = #{robotCallJobId} and `tenant_id` = #{tenantId} and enabled_status = 0
    </update>

    <update id="updateByModifiedObject" parameterType="com.yiwise.core.dal.entity.RobotCallJobPO">
        update robot_call_job
        <set>
            <if test="name!=null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="mode!=null">
                `mode` = #{mode,jdbcType=TINYINT},
            </if>
            <if test="robotCount!=null">
                robot_count = #{robotCount,jdbcType=INTEGER},
            </if>
            <if test="phoneType != null">
                phone_type = #{phoneType},
            </if>
            <if test="dailyStartTime!=null">
                daily_start_time = #{dailyStartTime,jdbcType=TIME},
            </if>
            <if test="dailyEndTime!=null">
                daily_end_time = #{dailyEndTime,jdbcType=TIME},
            </if>
            <if test="daysOfWeek!=null">
                days_of_week = #{daysOfWeek, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.DayOfWeekSetTypeHandler},
            </if>
            <if test="inactiveTimeList!=null">
                inactive_time_list =
                #{inactiveTimeList,typeHandler=com.yiwise.core.model.enums.handler.base.InactiveTimeListToJsonTypeHandler},
            </if>
            <if test="description!=null">
                description = #{description},
            </if>
            <if test="smsTemplateId!=null">
                sms_template_id = #{smsTemplateId},
            </if>
            <if test="virtualSmsTemplateId != null">
                virtual_sms_template_id = #{virtualSmsTemplateId},
            </if>
            <if test="wphCouponSmsTemplateId != null">
                wph_coupon_sms_template_id = #{wphCouponSmsTemplateId},
            </if>
            <if test="wphProductSmsTemplateId != null">
                wph_product_sms_template_id = #{wphProductSmsTemplateId},
            </if>
            <if test="smsAlertLevelCode!=null">
                sms_alert_level_code = #{smsAlertLevelCode,jdbcType=VARCHAR,typeHandler=com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler},
            </if>
            <if test="nextStartDatetime!=null">
                next_start_datetime = #{nextStartDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="nextEndDatetime!=null">
                next_end_datetime = #{nextEndDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId!=null">
                update_user_id = #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="startTime!=null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            end_time = #{endTime,jdbcType=TIMESTAMP},
            <if test="earlyWarningAlertUsers!=null">
                early_warning_alert_users = #{earlyWarningAlertUsers,jdbcType=VARCHAR,typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="redialCondition != null">
                redial_condition =
                #{redialCondition, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.DialStatusEnumSetTypeHandler},
            </if>
            <if test="connectRedialCondition != null">
                connect_redial_condition =
                #{connectRedialCondition, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler},
            </if>
            <if test="redialInterval != null">
                redial_interval = #{redialInterval, jdbcType=INTEGER},
            </if>
            <if test="redialType != null">
                redial_type = #{redialType, jdbcType=INTEGER, typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobRedialTypeEnumHandler},
            </if>
            <if test="redialTimes != null">
                redial_times = #{redialTimes, jdbcType=TINYINT},
            </if>
            <if test="concurrencyQuota != null">
                `concurrency_quota` = #{concurrencyQuota, jdbcType=INTEGER},
            </if>
            <if test="robotCallJobType != null">
                `robot_call_job_type` = #{robotCallJobType, typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobTypeEnumHandler},
            </if>
            <if test="transferTenantPhoneNumberId!=null">
                transfer_tenant_phone_number_id = #{transferTenantPhoneNumberId},
            </if>
            <if test="wechatPushConditionList!=null">
                wechat_push_condition_list = #{wechatPushConditionList, typeHandler=com.yiwise.core.model.enums.WechatPushConditionListTypeHandler},
            </if>
            <if test="priority!=null">
                priority = #{priority},
            </if>
            <if test="useFreeAi!=null">
                use_free_ai = #{useFreeAi},
            </if>
            <if test="tenantCallInterceptId!=null">
                tenant_call_intercept_id = #{tenantCallInterceptId},
            </if>
            <if test="inactiveDateList">
                inactive_date_list = #{inactiveDateList, typeHandler=com.yiwise.core.model.enums.handler.base.DateListToJsonTypeHandler},
            </if>
            <if test="onWorkingDays != null">
                on_working_days = #{onWorkingDays},
            </if>
            <if test="timeRobotCountList!=null">
                time_robot_count_list =
                #{timeRobotCountList,typeHandler=com.yiwise.core.model.enums.handler.base.TimeRobotCountListToJsonTypeHandler},
            </if>
            <if test="baseRobotCount!=null">
                base_robot_count = #{baseRobotCount,jdbcType=INTEGER},
            </if>
            <if test="enableElasticRobot!=null">
                enable_elastic_robot = #{enableElasticRobot},
            </if>
            <if test="enableTimeRobot!=null">
                enable_time_robot = #{enableTimeRobot},
            </if>
            <if test="shouldUseRobotCount!=null">
                should_use_robot_count = #{shouldUseRobotCount,jdbcType=INTEGER},
            </if>
            <if test="baseConcurrencyQuota!=null">
                base_concurrency_quota = #{baseConcurrencyQuota,jdbcType=INTEGER},
            </if>
            <if test="smsTemplateIntentList!=null">
                sms_template_intent_list = #{smsTemplateIntentList,typeHandler=com.yiwise.core.model.enums.handler.base.SmsTemplateIntentListTypeHandler},
            </if>
            <if test="redialSettingList!=null">
                redial_setting_list = #{redialSettingList,typeHandler=com.yiwise.core.model.enums.handler.base.RedialSettingListTypeHandler},
            </if>
            <if test="customerWhiteGroupIds != null">
                customer_white_group_ids = #{customerWhiteGroupIds, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="csStaffGroupId != null">
                cs_staff_group_id = #{csStaffGroupId},
            </if>
            <if test="smsPushType != null">
                sms_push_type = #{smsPushType, jdbcType=TINYINT,
                typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.SmsPushEnumHandler},
            </if>
            <if test="addFriendStatus != null">
                add_friend_status = #{addFriendStatus, jdbcType=TINYINT,
                typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobAddFriendStatusEnumHandler},
            </if>
            <if test="addFriendLevelCode!=null">
                add_friend_level_code = #{addFriendLevelCode, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.IntegerSetToJsonTypeHandler},
            </if>
            <if test="wechatCpAddFriendTokenId!=null">
                wechat_cp_add_friend_token_id = #{wechatCpAddFriendTokenId, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="wechatCpAddFriendMessage!=null">
                wechat_cp_add_friend_message = #{wechatCpAddFriendMessage},
            </if>
            <if test="wechatCpAddFriendAccounts!=null">
                wechat_cp_add_friend_accounts = #{wechatCpAddFriendAccounts, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="wechatCpAddFriendDelayTime!=null">
                wechat_cp_add_friend_delay_time = #{wechatCpAddFriendDelayTime},
            </if>
            <if test="aikeWechatList!=null">
                aike_wechat_list = #{aikeWechatList, jdbcType=LONGVARCHAR, typeHandler=com.yiwise.core.model.enums.handler.AikeWechatListToJsonTypeHandler},
            </if>
            <if test="addWechatConfig!=null">
                add_wechat_config = #{addWechatConfig, jdbcType=LONGVARCHAR, typeHandler=com.yiwise.core.model.enums.handler.CallJobAddWechatConfigToJsonTypeHandler},
            </if>
            sms_job_config = #{smsJobConfig, jdbcType=LONGVARCHAR, typeHandler=com.yiwise.core.model.enums.handler.CallJobSmsJobConfigToJsonTypeHandler},
            <if test="wechatCpAddFriend!=null">
                wechat_cp_add_friend = #{wechatCpAddFriend, jdbcType=INTEGER, typeHandler=com.yiwise.core.model.enums.handler.WechatCpAddFriendEnumHandler},
            </if>
            <if test="smsPushAnswered != null ">
                sms_push_answered = #{smsPushAnswered},
            </if>
            <if test="optionIds != null">
                option_ids = #{optionIds, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="filterStrategyIds != null ">
                filter_strategy_ids = #{filterStrategyIds, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="addWechatType != null">
                add_wechat_type = #{addWechatType},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId},
            </if>
            <if test="wphRuleIds != null ">
                wph_rule_ids = #{wphRuleIds, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="wphCouponPoolId != null">
                wph_coupon_pool_id = #{wphCouponPoolId},
            </if>
            <if test="wphCouponPoolSwitch != null">
                wph_coupon_pool_switch = #{wphCouponPoolSwitch},
            </if>
            <if test="wphProductionPushCode != null">
                wph_production_push_code = #{wphProductionPushCode},
            </if>
            wph_fx_id = #{wphFxId},
            <if test="wphCouponOutCallInfo != null">
                wph_coupon_out_call_info = #{wphCouponOutCallInfo},
            </if>
            <if test="wphProductOutCallInfo != null">
                wph_product_out_call_info = #{wphProductOutCallInfo},
            </if>
            <if test="wphPushType != null">
                wph_push_type = #{wphPushType, typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.WphPushTypeEnumHandler},
            </if>
            <if test="brandBlackGroupIds != null">
                brand_black_group_ids =
                #{brandBlackGroupIds, jdbcType=VARCHAR, typeHandler=com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler},
            </if>
            <if test="enableRemoteFilter != null">
                enable_remote_filter =#{enableRemoteFilter},
            </if>
            <if test="skipValidProperties != null">
                skip_valid_properties =#{skipValidProperties},
            </if>
            bot_sms_template_id = #{botSmsTemplateId},
            <if test="enableReSendSms != null">
                enable_re_send_sms = #{enableReSendSms},
            </if>
            <if test="reSendSmsTemplateIds != null">
                re_send_sms_template_ids = #{reSendSmsTemplateIds,typeHandler=com.yiwise.core.model.enums.handler.base.LongListToJsonTypeHandler},
            </if>
            risk_control_strategy_id = #{riskControlStrategyId},
            flash_message_template_id = #{flashMessageTemplateId}

        </set>
        where robot_call_job_id = #{robotCallJobId,jdbcType=BIGINT} and enabled_status = 0
    </update>

    <update id="setDeleteState" parameterType="long">
        update robot_call_job set status = 6, enabled_status = 1 where robot_call_job_id = #{robotCallJobId,jdbcType=BIGINT}
    </update>

    <select id="getAvailableRobotCallJob" resultMap="RobotTaskPOBaseResultMap">
        SELECT job.robot_call_job_id, job.tenant_id, job.name, job.dialog_flow_id,
        job.sms_alert_level_code, job.sms_template_id, job.priority, job.use_free_ai, job.start_time,
        job.ip_address, job.hostname, job.version_number, job.status, job.robot_count,
        job.concurrency_quota, job.create_user_id, job.early_warning_alert_users, job.create_time,
        job.redial_condition, job.redial_interval, job.redial_times, job.daily_start_time, job.daily_end_time,
        job.inactive_time_list, job.cs_staff_group_id, job.phone_type, job.condition_cancel_send,
        job.cs_distribution_method, job.wechat_push_condition_list, job.compress_audio, job.use_yiwise_asr,
        job.description, job.robot_call_job_type, job.end_time, job.days_of_week, job.connect_redial_condition,
        job.inactive_date_list, job.time_robot_count_list, job.base_robot_count, job.should_use_robot_count,
        job.enable_elastic_robot, job.enable_time_robot, job.base_concurrency_quota,
        job.sms_template_intent_list, job.redial_setting_list, job.sms_push_type,
        job.add_friend_status,job.customer_white_group_ids,
        job.add_friend_level_code,job.wechat_cp_add_friend_token_id,job.wechat_cp_add_friend_message,job.wechat_cp_add_friend_delay_time,
        job.tenant_call_intercept_id,job.aike_wechat_list,job.add_wechat_config,job.sms_job_config,job.wechat_cp_add_friend,job.flash_message_template_id,
        job.redial_type, job.sms_push_answered, job.filter_strategy_ids, job.call_out_plan_id, job.add_wechat_type,
        job.wph_rule_ids, job.wph_coupon_pool_id, job.skip_valid_properties,
        job.wph_coupon_pool_switch, job.brand_black_group_ids, job.risk_control_strategy_id,job.enable_remote_filter,job.virtual_sms_template_id,
        job.wph_production_push_code, job.wph_push_type, job.wph_coupon_sms_template_id, job.wph_product_sms_template_id, job.wph_coupon_out_call_info, job.wph_product_out_call_info, job.wph_fx_id,
        job.re_send_sms_template_ids, job.enable_re_send_sms, job.bot_sms_template_id,job.enable_wph_send_sms
        FROM `robot_call_job` job
        WHERE job.status IN (3, 5) and job.enabled_status = 0 AND job.start_time &lt;= now()
        AND robot_call_job_type = #{robotCallJobType}
        <choose>
<!--            <when test="isGreedy">-->
<!--                AND `ip_address` = #{ipAddress}-->
<!--            </when>-->
            <!-- 如果是切片后的任务，调度的时候不必考虑单机并发啊上限，并且不受IP限制 -->
            <when test="@<EMAIL>(robotCallJobType)">
                AND (`ip_address` = #{ipAddress} OR `ip_address` IS NULL) AND robot_count &lt;= #{remainingQuota}
            </when>
        <!-- 获取线上大任务，排除pre-->
        <otherwise>
            AND (hostname not like '%pre%' or hostname is null)
        </otherwise>
      </choose>
      AND (curtime() >=`daily_start_time` AND curtime() &lt;= `daily_end_time` AND curtime() >=`next_start_datetime` AND
      curtime() &lt;= `next_end_datetime`)
      ORDER BY RAND() LIMIT 1;
  </select>

  <update id="updateRobotCallJobToRunningByVersion">
      UPDATE `robot_call_job`
      SET `status` = 1, `ip_address` = #{ipAddress}, `hostname` = #{hostname}, `version_number` = #{versionNumber} + 1, `last_heart_beat_time` = now(),
          hang_up_type = NULL, hang_up_reason = NULL
      WHERE `robot_call_job_id` = #{callJobId} AND `version_number` = #{versionNumber} AND enabled_status = 0 and `status` != 15;
  </update>

    <select id="getCheckAndUpdateAllExecAbleRobotCallJobToRunnable" resultMap="RobotTaskPOBaseResultMap">
        SELECT <include refid="base_column"/> from `robot_call_job`
        <![CDATA[
            WHERE
                (`status` = 5
                OR `status` = 11
                OR `status` = 14
                OR (`status` = 0 AND `mode` = 0)
                )
            AND `start_time` <= now() and enabled_status = 0 and `status` != 15
            AND current_timestamp() BETWEEN `next_start_datetime` AND `next_end_datetime` limit 50;
        ]]>
    </select>

  <select id="selectTimeOutRobotCallJob" resultMap="TimeOutRobotCallJobResultMap">
      SELECT <include refid="time_column"/>
      FROM `robot_call_job`
      <![CDATA[
      WHERE `status` = 1 AND `last_heart_beat_time` < TIMESTAMPADD(MINUTE,  -#{time}, now()) and enabled_status = 0 and `status` != 15
      ]]>
  </select>

  <select id="selectTimeOutRunnableRobotCallJob" resultMap="TimeOutRobotCallJobResultMap">
      SELECT <include refid="time_column"/>
      FROM `robot_call_job`
      <![CDATA[
      WHERE `status` = 3 AND now() > `next_end_datetime` and enabled_status = 0 and `status` != 15
      ]]>
  </select>

  <select id="selectTimeOutSystemSuspendedRobotCallJob" resultMap="TimeOutRobotCallJobResultMap">
      SELECT <include refid="time_column"/>
      FROM `robot_call_job`
      <![CDATA[
      WHERE `status` = 5 AND now() between `daily_start_time` and  `daily_end_time` and now() > `next_start_datetime` AND `start_time` <= now() and enabled_status = 0
      ]]>
  </select>

    <select id="selectTimeOutAutoRobotCallJob" resultMap="TimeOutRobotCallJobResultMap">
        SELECT <include refid="time_column"/>
        FROM `robot_call_job`
        <![CDATA[
        WHERE `status` = 0 AND mode = 0 AND now() between `daily_start_time` and  `daily_end_time` and now() > `next_end_datetime` AND `start_time` <= now() and enabled_status = 0
        ]]>
    </select>


  <update id="updateTimeOutRobotCallJobStatus">
      <![CDATA[

      UPDATE `robot_call_job` SET `ip_address` = NULL, `status` = #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}, next_start_datetime = #{nextStartDatetime}, next_end_datetime = #{nextEndDatetime}
      WHERE robot_call_job_id = #{robotCallJobId} AND `status` = 1 AND `last_heart_beat_time` < TIMESTAMPADD(MINUTE,  -#{time}, now()) and enabled_status = 0;

      ]]>
  </update>

  <select id="selectRobotCallJobSimpleInfoList" resultMap="RobotCallJobNameResultMap">
      select `robot_call_job_id`, `name`, `status`, `first_start_date`, `last_heart_beat_time`, `dialog_flow_id`,
      `transfer_phone_choose_type`, `create_time`, `robot_count`
      from robot_call_job
      where
      tenant_id = #{tenantId} and enabled_status = 0 and `status` != 15
      <if test="dialogFlowId != null">
          and dialog_flow_id = #{dialogFlowId}
      </if>
      <if test="null != callJobStatusSet and callJobStatusSet.size > 0">
          and `status` in
          <foreach collection="callJobStatusSet" item="item" index="i" open="(" separator="," close=")">
              #{item, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
          </foreach>
      </if>
      <if test="searchWords != null and searchWords != ''">
          and name like concat('%', #{searchWords}, '%')
      </if>
      <if test="beginCreateDate != null and endCreateDate != null">
          and create_time between #{beginCreateDate} AND date_add(#{endCreateDate}, INTERVAL 1 DAY)
      </if>
      <if test="userIdList != null and userIdList.size > 0">
          and create_user_id in
          <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
              #{userId}
          </foreach>
      </if>
      <if test="queryImport == true">
          and status != 16
      </if>
      ORDER BY robot_call_job_id DESC
  </select>

  <select id="getUnFinishedCallJobRobotCount" resultType="java.lang.Integer">
      SELECT IFNULL(SUM(robot_count), 0) FROM `robot_call_job` WHERE tenant_id = #{tenantId}
      <!--        <if test="userId != null">-->
        <!--            and create_user_id = #{userId}-->
        <!--        </if>-->
        <if test="userIds != null and userIds.size()>0">
            and create_user_id in
            <foreach collection="userIds" open="(" close=")" separator=", " item="userId">
                #{userId}
            </foreach>
        </if>
        AND `status` IN (1, 3) and enabled_status = 0
        <if test="exceptRobotCallJobId != null">
            AND robot_call_job_id != #{exceptRobotCallJobId};
        </if>
    </select>

    <select id="getAuthUsingCallJobRobotCount" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(robot_count), 0) FROM `robot_call_job` WHERE tenant_id = #{tenantId}
        <if test="userIdList != null and userIdList.size > 0">
            and create_user_id in
            <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
                #{userId}
            </foreach>
        </if>
        AND `status` IN (1, 3) and enabled_status = 0
    </select>

    <select id="selectNameByRobotCallJobId" parameterType="long" resultMap="RobotCallJobNameResultMap">
        select `robot_call_job_id`, `name`, `status`, `first_start_date`, `last_heart_beat_time`, `create_time` from robot_call_job
        where robot_call_job_id in
        <foreach collection="robotCallJobIdSet" item="id" index="i" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectNameById" parameterType="java.lang.Long" resultType="java.lang.String">
        select name
        from robot_call_job
        where robot_call_job_id = #{robotCallJobId}
    </select>

    <select id="selectByStatusAndDuration" resultMap="RobotJobAlertResultMap">
        <![CDATA[
        select robot_call_job_id, name, tenant_id, mode, update_time, concurrency_quota, robot_count, ip_address
        from robot_call_job
        where status = #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
          and update_time < TIMESTAMPADD(SECOND, -#{duration}, now())
        ]]>
    </select>

    <select id="selectInQueueJobList" resultMap="RobotTaskPOBaseResultMap">
        SELECT robot_call_job_id , tenant_id , robot_count , daily_start_time, daily_end_time, inactive_time_list, create_user_id,
               `priority`, `use_free_ai`, start_time, end_time, days_of_week, base_robot_count, time_robot_count_list, last_heart_beat_time,
               phone_type, status
        FROM `robot_call_job`
        WHERE `status` = 7 and last_heart_beat_time <![CDATA[ < ]]> #{time}
        ORDER BY priority ASC, start_time ASC, robot_count DESC limit 10
    </select>

    <!-- 根据任务名和创建任务的用户id获取任务列表 -->
    <select id="selectRobotCallJobIdByTenantIdAndName" resultType="java.lang.Long">
        SELECT robot_call_job_id FROM robot_call_job WHERE name = #{name} and tenant_id = #{tenantId} and enabled_status = 0
    </select>

    <select id="selectExistRobotCallJobIdByTenantId" resultType="java.lang.Long">
        SELECT robot_call_job_id FROM robot_call_job WHERE tenant_id = #{tenantId} and enabled_status = 0 and `status` != 15
        <if test="null != statusSet and statusSet.size > 0">
            and `status` in
            <foreach collection="statusSet" item="item" index="i" open="(" separator="," close=")">
                #{item, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
            </foreach>
        </if>
        <if test="name != null and name !=''">
            and `name` like concat('%',#{name},'%')
        </if>
        <if test="beginCreateDate != null and endCreateDate != null">
            and create_time between #{beginCreateDate} AND date_add(#{endCreateDate}, INTERVAL 1 DAY)
        </if>
        <if test="userIdList != null and userIdList.size > 0">
            and create_user_id in
            <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
                #{userId}
            </foreach>
        </if>
        ORDER BY robot_call_job_id DESC
        limit 100
    </select>

    <!-- 根据客户ID获取任务列表 -->
    <select id="selectRobotCallJobByTenantId" resultMap="RobotTaskPOBaseResultMap">
        SELECT robot_call_job_id as robotCallJobId, name, status FROM robot_call_job WHERE tenant_id = #{tenantId}
    </select>

    <select id="selectRobotCallJobByTenantIdList" resultMap= "RobotTaskPOBaseResultMap">
        select * from robot_call_job where tenant_id in
        <foreach collection="tenantIdList" open="(" close=")" separator=", " item="tenantId">
            #{tenantId}
        </foreach>
        and status in
        <foreach collection="statusList" open="(" close=")" separator=", " item="status">
            #{status}
        </foreach>
        and status != 0
        and status != 2
        and status != 6
        ORDER BY field(tenant_id,
        <foreach collection="tenantIdList" separator=", " item="tenantId">
        #{tenantId}
        </foreach>
            )

    </select>

    <select id="selectRobotCallJobForCallOutPlan" resultMap= "RobotTaskPOBaseResultMap">
        select *
        from robot_call_job rj left join call_out_plan_job pj on rj.robot_call_job_id = pj.robot_call_job_id
        <where>
         rj.tenant_id = #{query.tenantId} and
         rj.call_out_plan_id = #{query.callOutPlanId}
            <if test="query.startTime != null">
                and rj.create_time <![CDATA[ >= ]]> #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and rj.create_time <![CDATA[ < ]]> #{query.endTime}
            </if>
            <if test="query.jobName != null and query.jobName.length() > 0 ">
                and rj.name like concat('%', #{query.jobName}, '%')
            </if>
            <if test="query.crowdPack != null and query.crowdPack.length() > 0 ">
                and pj.crowd_pack like concat('%', #{query.crowdPack}, '%')
            </if>
            <if test="query.note != null and query.note.length() > 0 ">
                and rj.description like concat('%', #{query.note}, '%')
            </if>
            <if test="query.robotCallJobStatuses != null and query.robotCallJobStatuses.size > 0">
                and rj.status in
                <foreach collection="query.robotCallJobStatuses" open="(" close=")" separator=", " item="status">
                    #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
                </foreach>
            </if>
            and rj.enabled_status = 0
        </where>
        <choose>
            <when  test="query.callOutPlanVision != null and query.callOutPlanVision.getCode() == 0" >
                order by pj.crowd_pack, rj.create_time desc, rj.robot_call_job_id desc
            </when>
            <when  test="query.callOutPlanVision != null and query.callOutPlanVision.getCode() == 1" >
                order by pj.planed_start_time desc
            </when>
            <otherwise>
                order by rj.create_time desc, rj.robot_call_job_id desc
            </otherwise>
        </choose>
    </select>


    <select id="selectAllHangUpJob" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include> from robot_call_job  WHERE status = 10 and hang_up_type = #{preStatus} AND tenant_id = #{tenantId} AND enabled_status = 0 and `status` != 15 limit 20
    </select>

    <select id="selectDebtJobToInQueueWhoUserThisPhoneNumber" resultMap="RobotTaskPOBaseResultMap">
        select job.robot_call_job_id, job.tenant_id, job.create_user_id, job.status from robot_call_job job JOIN robot_call_job_phone_number phone ON job.robot_call_job_id = phone.robot_call_job_id
        WHERE phone.tenant_id = #{tenantId} AND phone.tenant_phone_number_id = #{tenantPhoneNumberId} AND job.status = 10 AND job.hang_up_type= 0 AND job.enabled_status = 0
          and `status` != 15 limit 20;
    </select>

    <select id="selectAllBySearchWords" resultMap="RobotTaskPOBaseResultMap">
        SELECT robot_call_job_id, tenant_id, name, dialog_flow_id, sms_alert_level_code,
        sms_template_id, ip_address, hostname, version_number, status, robot_count, create_user_id,
        early_warning_alert_users
        FROM `robot_call_job`
        <where>
            <if test="searchWords != null and searchWords != ''">
                `name` like concat('%',#{searchWords},'%')
            </if>
            <if test="dialogFlowId != null">
                and dialog_flow_id = #{dialogFlowId}
            </if>
        </where>
    </select>

    <select id="selectAllByTenantIdListAndSearchWords" resultType="com.yiwise.core.model.dto.CallRecordCommonDTO">
        select `name`, robot_call_job_id as id
        from robot_call_job
        <where>
            <if test="tenantIds != null and tenantIds.size > 0">
                tenant_id in
                <foreach collection="tenantIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="searchWords != null and searchWords != ''">
                and `name` like concat('%', #{searchWords}, '%')
            </if>
            <if test="dialogFlowId != null">
                and dialog_flow_id = #{dialogFlowId}
            </if>
        </where>
    </select>

    <select id="getOneJobUsingTenantPhoneNumber" resultMap="RobotTaskPOBaseResultMap">
        SELECT job.* FROM robot_call_job_phone_number phone
                              LEFT JOIN robot_call_job job ON phone.robot_call_job_id = job.robot_call_job_id
        WHERE phone.tenant_phone_number_id = #{tenantPhoneNumberId} AND job.status IN (1, 5) LIMIT 1;
    </select>

    <select id="getOneJobUsingTenantPhoneNumberList" resultMap="RobotTaskPOBaseResultMap">
        SELECT job.* FROM robot_call_job_phone_number phone
        LEFT JOIN robot_call_job job ON phone.robot_call_job_id = job.robot_call_job_id
        WHERE phone.tenant_phone_number_id in
        <foreach collection="tenantPhoneNumberIdList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND job.status IN (1, 5) LIMIT 1;
    </select>

    <select id="getSuspendableJobsByPhoneNumberList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.phone_number_id in
        <foreach collection="phoneNumberIdList" item="phoneNumberId" index="index" open="(" separator="," close=")">
            #{phoneNumberId}
        </foreach>
        and rcj.status in (1, 5, 7, 11)
    </select>

    <select id="getSuspendableJobsByTenantPhoneNumberList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.tenant_phone_number_id in
        <foreach collection="tenantPhoneNumberIdList" item="tenantPhoneNumberId" index="index" open="(" separator="," close=")">
            #{tenantPhoneNumberId}
        </foreach>
        and rcj.status in (1, 5, 7, 11)
    </select>

    <select id="getSuspendableJobsByCallPolicyGroupList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.call_policy_group_id in
        <foreach collection="callPolicyGroupIdList" item="callPolicyGroupId" index="index" open="(" separator="," close=")">
            #{callPolicyGroupId}
        </foreach>
        and rcj.status in (1, 5, 7, 11)
    </select>

    <select id="getResumableJobsByPhoneNumberList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.phone_number_id in
        <foreach collection="phoneNumberIdList" item="phoneNumberId" index="index" open="(" separator="," close=")">
            #{phoneNumberId}
        </foreach>
        and rcj.status = 10
        and rcj.hang_up_type = 3
    </select>

    <select id="getResumableJobsByTenantPhoneNumberList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.tenant_phone_number_id in
        <foreach collection="tenantPhoneNumberIdList" item="tenantPhoneNumberId" index="index" open="(" separator="," close=")">
            #{tenantPhoneNumberId}
        </foreach>
        and rcj.status = 10
        and rcj.hang_up_type = 3
    </select>

    <select id="getResumableJobsByCallPolicyGroupList" resultMap="RobotTaskPOBaseResultMap">
        select rcj.*
        from robot_call_job rcj
        left join robot_call_job_phone_number rcjpn
        on rcj.robot_call_job_id = rcjpn.robot_call_job_id
        where rcjpn.call_policy_group_id in
        <foreach collection="callPolicyGroupIdList" item="callPolicyGroupId" index="index" open="(" separator="," close=")">
            #{callPolicyGroupId}
        </foreach>
        and rcj.status = 10
        and rcj.hang_up_type = 3
    </select>

    <select id="selectUsingJobListWithCreateUser" resultMap="RobotTaskDTOBaseResultMap">
        SELECT
        robot_call_job_id, tenant_id, name, ip_address, hostname, version_number, status, robot_count,
        phone_type
        FROM
        robot_call_job
        WHERE
        enabled_status = 0
        <!--<if test="statusList.size()>0">-->
        <!--AND-->
        <!--<foreach collection="statusList" item="item" open="(" close=")" separator="or">-->
        <!--`status` = #{item}-->
        <!--</foreach>-->
        <!--</if>-->
        AND `status` IN (1, 3)
        AND tenant_id = #{tenantId}
        <if test="userIdList != null and userIdList.size > 0">
            and create_user_id in
            <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="selectRobotCallJobIfNotEnd" resultMap="RobotTaskDTOBaseResultMap">
        select `robot_call_job_id`, status from robot_call_job
        where robot_call_job_id = #{robotCallJobId} and curtime() &lt;= `next_end_datetime`
    </select>

    <select id="countRobotCallJobInSystemSuspendedWithTheSameTime" resultType="java.lang.Integer">
        SELECT COUNT(0) FROM robot_call_job WHERE tenant_id = #{tenantId} AND status = 5 AND robot_call_job_id != #{exceptRobotCallJobId}
                                              AND daily_start_time = #{dailyStartTime} AND daily_end_time = #{dailyEndTime}
                                              AND time(next_start_datetime) = time(#{nextStartDatetime}) AND time(next_end_datetime) = time(#{nextEndDatetime});
    </select>

    <update id="updateTimeDurationById">
        update robot_call_job
        set
            next_start_datetime = #{startDatetime},
            next_end_datetime = #{endDatetime}
        where robot_call_job_id = #{jobId}
    </update>

    <resultMap id="cuncurrencySumByIp"
               type="com.yiwise.core.model.vo.robotcalljob.concurrency.RobotCallJobConcurrencyInfoVO">
        <result column="ip_address" property="ipAddress"/>
        <result column="hostname" property="hostname"/>
        <result column="value" property="robotCount"/>
        <result column="concurrencyQuota" property="concurrencyQuotaSum"/>
        <result column="jobCount" property="jobSum"/>
    </resultMap>

    <select id="getConcurrencySumByIp" resultMap="cuncurrencySumByIp">
        select  `ip_address` , `hostname`, SUM(`robot_count`) as `value`, SUM(`concurrency_quota`) as concurrencyQuota, count(0) as `jobCount`
        from `robot_call_job` WHERE `status` = 1 and `robot_call_job_type` = 0 GROUP BY `ip_address`, `hostname`;
    </select>

    <resultMap id="robotCallJobGroupByStatus" type="com.yiwise.core.model.vo.robotcalljob.RobotCallJobGroupByStatus">
        <result column="status" jdbcType="TINYINT" property="robotCallJobStatus"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="robotCount" property="robotCount"/>
        <result column="jobCount" property="jobCount"/>
        <result column="concurrencyQuota" property="concurrencyQuota"/>
    </resultMap>

    <select id="selectByTenantId" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job
        where tenant_id=#{tenantId}
    </select>

    <select id="selectRunningRobotCallJobByTenantId" resultType="java.lang.Long">
        SELECT robot_call_job_id
        FROM robot_call_job
        WHERE tenant_id = #{tenantId} AND status = 1
    </select>

    <select id="selectNeedWarningRobotCallJobIdByTenantId" resultType="java.lang.Long">
        SELECT robot_call_job_id
        FROM robot_call_job
        WHERE tenant_id = #{tenantId} AND status IN (0, 1, 4, 5) AND enabled_status = 0
    </select>

    <select id="selectByTenantIdLimit" resultMap="SimpleCallJob">
        SELECT robot_call_job_id, name, dialog_flow_id
        FROM robot_call_job
        WHERE tenant_id = #{tenantId} AND enabled_status = 0
        <if test="robotCallJobName != null and robotCallJobName != '' ">
            AND `name` LIKE concat("%",#{robotCallJobName},"%")
        </if>
        <if test="unarchived == true">
            AND `status` != 16 AND `status` != 6
        </if>
        ORDER BY robot_call_job_id DESC
        LIMIT 99
    </select>

    <select id="selectByDialogFlowIdList" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job
        where enabled_status = 0
        and `status` != 15
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        and dialog_flow_id in
        <foreach collection="dialogFlowIdList" open="(" close=")" separator=", " item="dialogFlowId">
            #{dialogFlowId}
        </foreach>
    </select>

    <select id="selectJobToDisabled" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include> from robot_call_job where tenant_id=#{tenantId} and `status` in (0, 1, 3, 5, 7, 10, 11, 13, 14) and enabled_status = 0 limit 20
    </select>

    <select id="selectByCsStaffGroupId" resultMap="RobotTaskDTOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job
        where tenant_id = #{tenantId} and cs_staff_group_id = #{staffGroupId}
        and enabled_status = 0 and `status` != 15
    </select>
    <select id="selectByCallPolicyGroupId" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job job
        where job.phone_type = 13
        and robot_call_job_id in (
        select robot_call_job_id from robot_call_job_phone_number where call_policy_group_id = #{callPolicyGroupId}
        )
        and enabled_status = 0
        and `status` != 15
    </select>

    <select id="selectByCallPolicyGroupIdList" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job job
        where job.phone_type = 13
        and `status` in (1,5,7)
        and robot_call_job_id in (
        select robot_call_job_id from robot_call_job_phone_number
        <where>
            <if test="callPolicyGroupIdList != null and callPolicyGroupIdList.size > 0">
                and call_policy_group_id in
                <foreach collection="callPolicyGroupIdList" open="(" close=")" separator=", " item="callPolicyGroupId">
                    #{callPolicyGroupId}
                </foreach>
            </if>
        </where>
        )
        and enabled_status = 0
        and `status` != 15
    </select>

    <select id="selectToSystemMaintain" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include> from robot_call_job where `status` in (1, 3) and enabled_status = 0
    </select>

    <select id="selectSystemMaintainJobs" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job
        where `status` = 13 and enabled_status = 0
    </select>

    <select id="selectAllByTenantIdAndSearchWords" resultMap="SimpleCallJob">
        select robot_call_job_id,`name`,dialog_flow_id
        from robot_call_job
        <where>
            tenant_id = #{tenantId}
            <if test="searchWords != null and searchWords != ''">
                and `name` like concat("%",#{searchWords},"%")
            </if>
        </where>
    </select>

    <select id="selectLimitWithSearchWords" resultMap="SimpleCallJob">
        select robot_call_job_id,`name`,dialog_flow_id
        from robot_call_job
        <where>
            enabled_status = 0
            <if test="searchWords != null and searchWords != ''">
                and `name` like concat("%",#{searchWords},"%")
            </if>
        </where>
        LIMIT 20
    </select>

    <select id="selectAllByTenantIdAndSearchWordsLimit" resultMap="SimpleCallJob">
        select robot_call_job_id,`name`,dialog_flow_id
        from robot_call_job
        <where>
            tenant_id = #{tenantId}
            <if test="searchWords != null and searchWords != ''">
                and `name` like concat("%",#{searchWords},"%")
            </if>
            <if test="robotCallJobIdList != null and robotCallJobIdList.size > 0">
                and robot_call_job_id in
                <foreach collection="robotCallJobIdList" open="(" close=")" separator=", " item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY robot_call_job_id DESC
        <if test="searchWords != null and searchWords != ''">
            LIMIT 99
        </if>

    </select>

    <select id="selectRunningAndRunnableRobotCallJobList" resultMap="RobotTaskPOBaseResultMap">
        SELECT * FROM robot_call_job WHERE tenant_id = #{tenantId} AND status in (1, 3) AND `priority` != 0 AND start_time <![CDATA[ < ]]> #{startTime};
    </select>

    <select id="selectRunningServerList" resultType="com.yiwise.core.model.po.RobotCallJobServerBO">
        SELECT `hostname`, `ip_address` AS ipAddress, SUM(`concurrency_quota`) as concurrencyQuota FROM robot_call_job
        WHERE `status` = 1 AND `hostname` NOT LIKE '%pre%' AND `ip_address` IS NOT NULL and robot_call_job_type = 0
        GROUP BY `ip_address`;
    </select>

    <select id="selectAllRunningJobs" resultMap="RobotTaskPOBaseResultMap">
        <![CDATA[
        SELECT robot_call_job_id, tenant_id, name, dialog_flow_id, early_warning_alert_users from robot_call_job where status = 1
        ]]>
    </select>

    <select id="selectRunnableRobotCount" resultType="java.lang.Integer">
        SELECT COUNT(0) FROM ${tableName} WHERE `status` = 3;
    </select>

    <update id="resetRobotCallJobIpAddress">
        UPDATE `robot_call_job` SET `ip_address` = NULL WHERE `robot_call_job_id` = #{robotCallJobId};
    </update>

    <update id="resetSubRobotCallJobIpAddress">
        UPDATE `sub_robot_call_job` SET `ip_address` = NULL, `hostname` = NULL, `status` = 0 WHERE `robot_call_job_id` = #{robotCallJobId};
    </update>

    <update id="resetOverRemainingQuotaRunnableJobById">
        UPDATE `robot_call_job` SET `ip_address` = NULL WHERE `ip_address` = #{ipAddress} AND `concurrency_quota` > #{remainingQuota} AND `status` = 3 and robot_call_job_id=#{robotCallJobId};
    </update>

    <select id="selectOverRemainingQuotaRunnableJob" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>  from `robot_call_job`  WHERE `ip_address` = #{ipAddress} AND `concurrency_quota` > #{remainingQuota} AND `status` = 3 limit 20;
    </select>

    <select id="selectCurrServerQuota" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(`concurrency_quota`), 0) FROM
        <choose>
            <when test="isPartition==true">
                `sub_robot_call_job`
            </when>
            <otherwise>
                `robot_call_job`
            </otherwise>
        </choose>
        WHERE `status` = 1 AND `ip_address` = #{ipAddress}
        <if test="isPartition==false">
            and `robot_call_job_type` = 0
        </if>
    </select>

    <select id="selectJobByName" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and `name` like concat('%',#{name},'%') and enabled_status = 0 and `status` != 15  order by robot_call_job_id desc  limit 1
    </select>

    <update id="updateRobotCount">
        update robot_call_job set robot_count = #{robotCount} ,  concurrency_quota=#{concurrencyQuota}
        <if test="robotCallJobType != null">
            , robot_call_job_type = #{robotCallJobType, typeHandler=com.yiwise.core.model.enums.robotcalljob.handler.RobotCallJobTypeEnumHandler}
        </if>
        <if test="shouldUseRobotCount != null">
            , should_use_robot_count = #{shouldUseRobotCount}
        </if>
        where robot_call_job_id=#{robotCallJobId}
    </update>

    <update id="updateBaseRobotCountAndBaseConcurrencyQuota">
        update robot_call_job set should_use_robot_count = #{shouldUseRobotCount}, robot_count=#{shouldUseRobotCount}, concurrency_quota=#{concurrencyQuota}
        where robot_call_job_id=#{robotCallJobId}
    </update>

    <select id="selectTenantByChangableJob" resultType="java.lang.Long">
        select tenant_id from robot_call_job where (status = 1 or status = 7) and (enable_time_robot = 1 or enable_elastic_robot = 1) group by tenant_id
    </select>

    <select id="selectTimeRobotJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and (status = 1 or status = 7) and enable_time_robot = 1 order by status desc, start_time
    </select>

    <select id="selectNotElasticRobotQueueJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and status = 7 and enable_elastic_robot = 0 order by status desc, start_time
    </select>

    <select id="selectElasticRobotQueueJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and status = 7 and enable_elastic_robot = 1 order by status desc, start_time
    </select>

    <select id="selectOverLoadRobotJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and status = 1  and enable_elastic_robot = 1  and robot_count > should_use_robot_count order by start_time
    </select>

    <select id="selectUnderLoadRobotJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and status = 1  and enable_elastic_robot = 1  and robot_count <![CDATA[< ]]> should_use_robot_count order by start_time
    </select>

    <select id="selectFullAndOverLoadRobotJobByTenant" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId} and status = 1  and enable_elastic_robot = 1  and robot_count >= should_use_robot_count order by start_time
    </select>

    <select id="selectTypeByJobId" resultType="com.yiwise.core.model.enums.robotcalljob.RobotCallJobTypeEnum">
        select robot_call_job_type from robot_call_job where robot_call_job_id = #{robotCallJobId}
    </select>

    <select id="getJobListByDialogFlowIdList" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include>
        from robot_call_job
        where tenant_id = #{tenantId}
        and dialog_flow_id in
        <foreach collection="dialogFlowIdList" open="(" close=")" separator=", " item="dialogFlowId">
            #{dialogFlowId}
        </foreach>
    </select>

    <select id="selectJobToNoAuthentication" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include> from robot_call_job where tenant_id = #{tenantId} and (status in (1, 3, 5, 7, 11, 12, 13) or (status = 10 and hang_up_type in (0, 1, 2, 3, 4, 5, 6, 7)))
    </select>

    <select id="selectNoAuthenticationJobs" resultMap="RobotTaskPOBaseResultMap">
        select <include refid="base_column"></include> from robot_call_job where tenant_id = #{tenantId} and status = 10 and hang_up_type = 8 order by start_time desc
    </select>

    <update id="updateRobotCallJobIP">
        update robot_call_job set ip_address = #{newIpAddress} where robot_call_job_id = #{robotCallJobId} and
            (ip_address is null or ip_address = #{ipAddress})
    </update>

    <resultMap id="indexJobInfoMap" type="com.yiwise.core.model.bo.callstats.IndexJobVO">
        <result column="robot_call_job_id" jdbcType="BIGINT" property="jobId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="robot_count" jdbcType="INTEGER" property="concurrency"/>
        <result column="status" jdbcType="TINYINT" property="status"
                typeHandler="com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
    </resultMap>
    <select id="indexJobInfo" resultMap="indexJobInfoMap">
        select robot_call_job_id, `name`, robot_count, status, create_user_id
        from robot_call_job
        where tenant_id = #{tenantId} and enabled_status = 0 and `status` != 15
        <if test="statusSet != null and statusSet.size() > 0">
            and status in
            <foreach collection="statusSet" open="(" close=")" separator=", " item="status">
                #{status, typeHandler=com.yiwise.core.model.enums.handler.RobotCallJobStatusEnumHandler}
            </foreach>
        </if>
        <if test="userIdList != null and userIdList.size > 0">
            and create_user_id in
            <foreach collection="userIdList" open="(" close=")" separator=", " item="userId">
                #{userId}
            </foreach>
        </if>
        ORDER BY status
    </select>

    <update id="collectionMarkUpdate">
        update robot_call_job
        set `collection_mark` = #{collectionMark}
        where tenant_id = #{tenantId}
        and robot_call_job_id = #{robotCallJobId}
    </update>

    <update id="setFolderId">
        update robot_call_job
        set folder_id = #{folderId}
        where tenant_id = #{tenantId} and robot_call_job_id in
        <foreach collection="jobIds" open="(" separator=", " close=")" item="jobId">#{jobId}</foreach>
    </update>
    <resultMap id="IdCountPairBO" type="com.yiwise.core.model.vo.IdCountPairBO">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="c" jdbcType="BIGINT" property="count"/>
    </resultMap>
    <select id="getFolderJobsCount" resultMap="IdCountPairBO">
        select folder_id as id, count(folder_id) as c
        from robot_call_job
        where tenant_id = #{tenantId} and enabled_status = 0 and `status` != 15 and folder_id in <foreach collection="folderIds" open="(" close=")" separator=", " item="folderId">#{folderId}</foreach>
        <if test="authUserIdList != null and authUserIdList.size > 0">
            and create_user_id in <foreach collection="authUserIdList" open="(" close=")" separator=", " item="userId">#{userId}</foreach>
        </if>
        group by folder_id;
    </select>

    <resultMap id="RobotCallJobOptionBOMap" type="com.yiwise.core.model.bo.robotcalljob.RobotCallJobOptionBO">
        <id column="robot_call_job_id" jdbcType="BIGINT" property="robotCallJobId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="option_ids" jdbcType="VARCHAR" property="optionIds"
                typeHandler="com.yiwise.core.model.enums.handler.base.LongSetToJsonTypeHandler"/>
    </resultMap>

    <select id="selectRobotCallJobByOptionIdList" resultMap="RobotCallJobOptionBOMap">
        SELECT robot_call_job_id, `name`, option_ids, tenant_id
        FROM robot_call_job
        WHERE enabled_status = 0
        and `status` != 15
        <if test="vOptionId != null ">
            AND JSON_CONTAINS(option_ids, concat(#{vOptionId}, ''))
        </if>
        <!--包含cOptionIdList所有的选项-->
        <if test="cOptionIdList != null and cOptionIdList.size() > 0 ">
            AND JSON_CONTAINS(option_ids, JSON_ARRAY
            <foreach collection="cOptionIdList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="tenantIdList != null and tenantIdList.size() > 0 ">
            AND tenant_id in
            <foreach collection="tenantIdList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectRobotCallJobByOptionLimit" resultMap="RobotCallJobOptionBOMap">
        SELECT robot_call_job_id, `name`, option_ids
        FROM robot_call_job
        WHERE enabled_status = 0 and `status` != 15 AND tenant_id = #{tenantId}
        <if test="vOptionIdList != null and vOptionIdList.size() > 0 ">
            AND
            <foreach collection="vOptionIdList" open="(" separator="or" close=")" item="item">
                JSON_CONTAINS(option_ids, concat(#{item}, ''))
            </foreach>
        </if>
        <!--包含cOptionIdList所有的选项-->
        <if test="cOptionIdList != null and cOptionIdList.size() > 0 ">
            AND JSON_CONTAINS(option_ids, JSON_ARRAY
            <foreach collection="cOptionIdList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="name != null and name !=''">
            AND `name` like concat('%', #{name},'%')
        </if>
        ORDER BY robot_call_job_id DESC
        LIMIT 20
    </select>
    <select id="selectCallJobCountByDialogFlowId" resultType="java.lang.Integer">
        SELECT COUNT(*) from robot_call_job
            where dialog_flow_id = #{dialogFlowId}
              AND tenant_id = #{tenantId}
    </select>
    <select id="selectRobotCallJobIdListByDialogFlowId" resultType="java.lang.Long">
        SELECT robot_call_job_id from robot_call_job
        WHERE dialog_flow_id = #{dialogFlowId}
          AND tenant_id = #{tenantId}
    </select>


    <update id="increaseRobot">
        update robot_call_job set robot_count = robot_count + #{increasement}, base_robot_count = base_robot_count + #{increasement},
                                  should_use_robot_count = should_use_robot_count + #{increasement},concurrency_quota= concurrency_quota + #{increasement},
                                  base_concurrency_quota = base_concurrency_quota + #{increasement}
        where robot_call_job_id = #{robotCallJobId}
    </update>

    <select id="selectAllForMarkSystem" resultMap="MarkSystemResultMap">
        SELECT robot_call_job_id, `name`
        FROM robot_call_job
        <where>
            <if test="robotCallJobId != null ">
                AND robot_call_job_id LIKE concat('%',#{robotCallJobId},'%')
            </if>
        </where>
        LIMIT 10
    </select>

    <select id="selectByIdListForMarkSystem" resultMap="MarkSystemResultMap">
        SELECT robot_call_job_id, `name`
        FROM robot_call_job
        WHERE robot_call_job_id in
        <foreach collection="robotCallJobIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllWithResultDialogFlow" resultMap="RobotCallJobNameResultMap">
        SELECT robot_call_job_id, dialog_flow_id
        FROM robot_call_job
    </select>
    <select id="selectByCallOutPlanId" resultMap="RobotTaskPOBaseResultMap">
        SELECT <include refid="base_column"/> from `robot_call_job`
        where tenant_id = #{tenantId}
        and call_out_plan_id = #{callOutPlanId} and enabled_status = 0
    </select>
    <select id="selectByCallOutPlanIds" resultMap="RobotTaskPOBaseResultMap">
        SELECT <include refid="base_column"/> from `robot_call_job`
        where tenant_id = #{tenantId}
        and call_out_plan_id in
        <foreach collection="callOutPlanIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and enabled_status = 0
    </select>

    <update id="resetWarningStatus">
        UPDATE robot_call_job SET warning = 0
        WHERE warning > 0 OR warning IS NULL
        LIMIT 100
    </update>

    <select id="updateWarningStatusIds">
        UPDATE robot_call_job SET warning = #{warningNum} | warning
        WHERE robot_call_job_id IN
        <foreach collection="robotCallJobIds" item="robotCallJobId" open="(" separator="," close=")">
            #{robotCallJobId}
        </foreach>
    </select>

    <select id="selectByAllFoldersId" resultMap="RobotTaskDTOBaseResultMap">
        select robot_call_job_id, tenant_id, name
        from robot_call_job
        where tenant_id = #{tenantId}
        and folder_id in
        <foreach collection="folderIds" open="(" close=")" separator=", " item="folderId">#{folderId}</foreach>
        and enabled_status = 0
        and `status` != 15
    </select>

    <update id="setFolderIdAndSortId">
        update robot_call_job
        set folder_id = #{folderId},
            sort_id = #{sortId},
            enable_top = false
        where tenant_id = #{tenantId}
          and robot_call_job_id = #{jobId}
    </update>

    <select id="countTopJob" resultType="java.lang.Integer">
        select count(*)
        from robot_call_job
        where tenant_id = #{tenantId}
        <include refid="checkFolderId"/>
          and enable_top = 1
          and enabled_status = 0
          and `status` != 15
    </select>

    <select id="countByTenantId" resultType="java.lang.Integer">
        select count(*) from robot_call_job
        where tenant_id = #{tenantId}  and enabled_status = 0  and `status` != 15
    </select>

    <select id="getMaxSortIdByTenantIdAndFolderId" resultType="java.lang.Long">
        select max(sort_id)
        from robot_call_job
        where tenant_id = #{tenantId}
        <include refid="checkFolderId"/>
        and enabled_status = 0
        and `status` != 15
    </select>

    <sql id="checkFolderId">
        <if test="folderId == null">
            and folder_id is null
        </if>
        <if test="folderId != null">
            and folder_id = #{folderId}
        </if>
    </sql>

    <sql id="orderType">
        <if test="orderType == null or orderType.getCode() == 0">
            order by robot_call_job_id desc
        </if>
        <if test="orderType != null and orderType.getCode() == 1">
            order by last_edit_time desc
        </if>
        <if test="orderType != null and orderType.getCode() == 2">
            order by sort_id desc, robot_call_job_id desc
        </if>
    </sql>

    <select id="selectReorderJobList" resultType="java.lang.Long">
        select robot_call_job_id
        from robot_call_job
        where tenant_id = #{tenantId}
        <include refid="checkFolderId"/>
        and enabled_status = 0
        and `status` != 15
        <include refid="reorderOrderType"/>
    </select>

    <select id="getSimpleInfoByName" resultMap="RobotTaskSimpleInfoMap">
        select robot_call_job_id, name
        from robot_call_job
        where tenant_id = #{tenantId}
        <if test="searchWord != null and searchWord !=''">
            and `name` like concat('%', #{searchWord},'%')
        </if>
        and enabled_status = 0
        and `status` != 15
        order by robot_call_job_id desc
        limit 20
    </select>

    <select id="getSimpleInfoByIdList" resultMap="RobotTaskSimpleInfoMap">
        select robot_call_job_id, name
        from robot_call_job
        where robot_call_job_id in
        <foreach collection="idList" open="(" close=")" separator=", " item="id">#{id}</foreach>
    </select>

    <sql id="reorderOrderType">
        <if test="orderType == null or orderType.getCode() == 0">
            order by robot_call_job_id
        </if>
        <if test="orderType != null and orderType.getCode() == 1">
            order by last_edit_time
        </if>
        <if test="orderType != null and orderType.getCode() == 2">
            order by sort_id , robot_call_job_id
        </if>
    </sql>

    <update id="updateAll">
        update robot_call_job
        set sort_id = CASE  robot_call_job_id
        when 0 then 0
        <foreach collection="jobPOList" item="jobPO">
            when #{jobPO.robotCallJobId}  then #{jobPO.sortId}
        </foreach>
        else 0 end
        where robot_call_job_id in
        <foreach collection="jobIds" open="(" separator=", " close=")" item="jobId">
            #{jobId}
        </foreach>
    </update>

    <select id="getConcurrencyRequiredRobotCount" resultType="java.lang.Integer">
        SELECT IFNULL(sum(robot_count),0)
        FROM robot_call_job
        WHERE enabled_status = 0 AND status IN (1, 3)
    </select>


    <update id="updateLastRunEndTime">
        UPDATE robot_call_job
        SET last_end_run_time = now()
        WHERE robot_call_job_id = #{robotCallJobId}
    </update>

    <select id="getConcurrencyRobotCount" resultType="java.lang.Integer">
        select sum(robot_count) from robot_call_job where status=1 and robot_call_job_type=0
    </select>

    <select id="selectNeedAutoImportUserGroupJob" resultMap="RobotTaskPOBaseResultMap">
        SELECT <include refid="base_column"/>
        FROM robot_call_job
        WHERE enabled_status = 0 AND auto_import = 1 AND #{currentDate} between auto_import_begin and auto_import_end
        ORDER BY auto_import_begin DESC
    </select>

    <select id="selectUnarchivedJobIdByIntervalDay" resultMap="robotCallJobConcurrenceResultMap">
        SELECT robot_call_job_id, status
        FROM robot_call_job
        WHERE status not in (15, 16) AND last_heart_beat_time <![CDATA[ < ]]> date_sub(now(), interval #{day} day)
        LIMIT #{limit}
    </select>

    <select id="selectNotCleanedArchivedJobId" resultMap="RobotTaskDTOBaseResultMap">
        SELECT robot_call_job_id, tenant_id
        FROM robot_call_job
        WHERE (status = 16 OR (status = 15 AND last_heart_beat_time <![CDATA[ < ]]> date_sub(now(), interval #{day} day)))
          AND task_cleaned = false
        LIMIT 1
    </select>

    <select id="selectFilteredTaskNotCleanedArchivedJob" resultMap="RobotTaskDTOBaseResultMap">
        SELECT robot_call_job_id, tenant_id
        FROM robot_call_job
        WHERE status = 16 AND last_heart_beat_time <![CDATA[ < ]]> date_sub(now(), interval #{day} day) AND filtered_task_cleaned = false
        LIMIT 1
    </select>

    <update id="updateRcsByFilterStrategyIds">
        UPDATE robot_call_job
        SET risk_control_strategy_id = #{riskControlStrategyId}
        WHERE tenant_id = #{tenantId} AND JSON_CONTAINS(filter_strategy_ids, concat(#{filterStrategyId}, ''))
    </update>

    <update id="updateRcsByTenantCallInterceptId">
        UPDATE robot_call_job
        SET risk_control_strategy_id = #{riskControlStrategyId}
        WHERE tenant_id = #{tenantId} AND tenant_call_intercept_id = #{tenantCallInterceptId}
    </update>


    <select id="selectCreateUserIdByFolderIdsOrJobIds" resultType="java.lang.Long">
        SELECT distinct create_user_id
        FROM robot_call_job
        WHERE
          tenant_id = #{tenantId}
        <if test="jobIds != null and jobIds.size() > 0 ">
            and robot_call_job_id in
            <foreach collection="jobIds" item="id" open="(" separator=", " close=")">
                #{id}
            </foreach>
        </if>
        <if test="folderIds != null and folderIds.size() > 0 ">
            and folder_id in
            <foreach collection="folderIds" item="id" open="(" separator=", " close=")">
                #{id}
            </foreach>
        </if>
          and enabled_status = 0
          and `status` != 15
    </select>

    <select id="selectAllRobotCallJob" resultMap="RobotTaskPOBaseResultMap">
        SELECT <include refid="base_column"/>
        FROM robot_call_job
        WHERE `enabled_status` = 0 and `status` in (0,1,2,3,4,5,6,7,10,11,12,13,14)
    </select>
    <select id="generateConcurrencyInfo" resultMap="ConcurrencyInfoResultMap">
        select tenant_id, sum(robot_count) as concurrency_count from robot_call_job where status = 1 group by tenant_id
    </select>
    <select id="selectByDialogFlowIds" resultMap="RobotTaskPOBaseResultMap">
        select
        <include refid="base_column"/>
        from robot_call_job
        where enabled_status = 0
        and `status` != 15
        and dialog_flow_id in
        <foreach collection="dialogFlowIdList" open="(" close=")" separator=", " item="dialogFlowId">
            #{dialogFlowId}
        </foreach>
    </select>

    <delete id="deleteByTenantIdCreateTime">
        DELETE FROM robot_call_job
        WHERE tenant_id = #{tenantId} AND create_time <![CDATA[ < ]]> DATE_SUB(now(), INTERVAL #{day} DAY)
    </delete>
    <delete id="deleteByTenantId">
        DELETE FROM `robot_call_job` WHERE tenant_id = #{tenantId} limit 1000
    </delete>
</mapper>