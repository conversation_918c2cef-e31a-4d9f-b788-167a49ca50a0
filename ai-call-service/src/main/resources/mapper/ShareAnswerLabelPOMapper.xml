<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yiwise.core.dal.dao.ShareAnswerLabelPOMapper" >
    <resultMap id="BaseResultMap" type="com.yiwise.core.model.dialogflow.entity.ShareAnswerLabelPO" >
        <id column="share_answer_label_id" property="shareAnswerLabelId" jdbcType="BIGINT" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="enabled_status" jdbcType="TINYINT" property="enabledStatus"
                typeHandler="com.yiwise.core.model.enums.handler.EnabledStatusEnumHandler"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <sql id="Table_Name">
        share_answer_label
    </sql>

    <sql id="Base_Column_List" >
        share_answer_label_id, name, enabled_status, create_user_id, update_user_id, create_time, update_time
    </sql>

    <select id="selectByIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where enabled_status != 2
        and share_answer_label_id in
        <foreach collection="shareAnswerLabelIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where enabled_status != 2
    </select>
    <select id="getByName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where enabled_status != 2
        and name = #{name}
    </select>


</mapper>