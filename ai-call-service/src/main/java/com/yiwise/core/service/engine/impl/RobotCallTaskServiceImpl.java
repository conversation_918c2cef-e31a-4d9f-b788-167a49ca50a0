package com.yiwise.core.service.engine.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.HandleByPageUtils;
import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.aop.annotation.Batched;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.batch.mq.BatchMqProcessHelper;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.batchdao.ImportCustomerPersonsMapper;
import com.yiwise.core.dal.dao.RobotCallTaskPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.entity.calloutplan.CallOutPlanPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.feignclient.rcs.AccountClient;
import com.yiwise.core.helper.*;
import com.yiwise.core.helper.publish.RobotCallTaskFilterEventPublisher;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.phonenumber.PhoneHomeLocationBO;
import com.yiwise.core.model.bo.robotcalljob.RedialSettingBO;
import com.yiwise.core.model.bo.robotcalljob.timeduration.ActiveTimeBO;
import com.yiwise.core.model.bo.robotcalltask.*;
import com.yiwise.core.model.bo.wexin.*;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dto.CallRecordExportDTO;
import com.yiwise.core.model.dto.ToBeCalledTaskExportDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.isv.ISVDataTypeEnum;
import com.yiwise.core.model.enums.callrecord.InterceptStatusEnum;
import com.yiwise.core.model.enums.robotcalljob.RobotCallJobRedialTypeEnum;
import com.yiwise.core.model.enums.robotcalljob.RobotCallJobTypeEnum;
import com.yiwise.core.model.enums.robotcalltask.RobotCallTaskMapperEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.callrecord.CallRecordQueryVO;
import com.yiwise.core.model.vo.robotcalltask.*;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.calljob.*;
import com.yiwise.core.service.engine.calloutplan.*;
import com.yiwise.core.service.engine.callstats.CallStatsService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonService;
import com.yiwise.core.service.engine.isv.IsvCallbackService;
import com.yiwise.core.service.mongo.impl.MongoOperationService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.scrm.TenantScrmService;
import com.yiwise.core.service.wechat.ScrmManualAddWechatFriendService;
import com.yiwise.core.util.JobUtils;
import com.yiwise.core.utils.CdpEnumUtil;
import com.yiwise.customer.data.platform.rpc.api.service.enums.CreateSourceTypeEnum;
import com.yiwise.customer.data.platform.rpc.api.service.request.PhoneNumberListRequest;
import com.yiwise.customer.data.platform.rpc.api.service.request.UpdateAccountInfoRequest;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.core.batch.common.BatchConstant.EXPORT_TO_BE_CALLED_TASK;
import static com.yiwise.core.config.ApplicationConstant.*;

/**
 * <AUTHOR>
 */
@Service
public class RobotCallTaskServiceImpl extends BasicServiceImpl<RobotCallTaskPO> implements RobotCallTaskService {

    private static final Logger logger = LoggerFactory.getLogger(RobotCallTaskServiceImpl.class);
    @Lazy
    @Resource
    private CallStatsService callStatsService;
    @Lazy
    @Resource
    private CustomerPersonService customerPersonService;
    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private SubRobotCallJobService subRobotCallJobService;
    @Lazy
    @Resource
    private CallRecordInfoService callRecordInfoService;
    @Resource
    private RobotCallTaskPOMapper robotCallTaskPOMapper;
    @Resource
    private ImportCustomerPersonsMapper importCustomerPersonsMapper;
    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private PhoneLocationService phoneLocationService;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private TenantScrmService tenantScrmService;
    @Resource
    private ScrmManualAddWechatFriendService scrmManualAddWechatFriendService;
    @Resource
    private CallOutPlanTaskUniqueService callOutPlanTaskUniqueService;
    @Resource
    private BatchJobInQueueService batchJobInQueueService;
    @Resource
    private RobotCallJobClearService robotCallJobClearService;
    @Resource
    private CallOutPlanService callOutPlanService;
    @Resource
    private PlanImportCustomerCommonService planImportCustomerCommonService;
    @Autowired
    @Qualifier("robotCallTaskDeleteJob")
    private Job robotCallTaskDeleteJob;
    @Resource
    private AccountClient accountClient;
    @Resource
    private RobotCallTaskFilterEventPublisher robotCallTaskFilterEventPublisher;
    @Resource
    private MongoOperationService mongoOperationService;
    @Resource
    private IsvCallbackService isvCallbackService;

    @Resource
    private BatchMqProcessHelper batchMqProcessHelper;

    @Override
    public List<RunTimeRobotCallTaskBO> getRunnableRobotCallTaskList(Long callJobId, Long subCallJobId, int size, boolean skipOrderBy) {
        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(callJobId);

        // 根据重拨间隔和当前时间计算出上次拨打时间的阈值
        Boolean noRedial = CollectionUtils.isEmpty(robotCallJobPO.getRedialSettingList());
        List<RunTimeRobotCallTaskBO> robotCallTaskList = new ArrayList<>();
        if (RobotCallJobTypeEnum.COMMON.equals(robotCallJobPO.getRobotCallJobType())) {
            List<Long> robotCallTaskListIds = new ArrayList<>();
            List<Long> robotCallTaskListIdsTemp = new ArrayList<>();
            // 首次外呼优先的自动重播 先查询status=0的数据
            if (RobotCallJobRedialTypeEnum.FIRST_CALL.equals(robotCallJobPO.getRedialType())) {
                robotCallTaskListIdsTemp = robotCallTaskPOMapper.getRunnableRobotCallTaskIdListFirst(callJobId, size, skipOrderBy);
                if (CollectionUtils.isNotEmpty(robotCallTaskListIdsTemp)) {
                    robotCallTaskListIds.addAll(robotCallTaskListIdsTemp);
                }
            }
            if (robotCallTaskListIds.size() < size) {
                int fetchSize1 = size - robotCallTaskListIds.size();
                if (skipOrderBy) {
                    // 对于号码数量超过50万的任务，自动重拨的号码，优先级高于非自动重拨的号码，分两次获取
                    robotCallTaskListIdsTemp = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList1(callJobId, fetchSize1);
                    robotCallTaskListIds.addAll(robotCallTaskListIdsTemp);
                    if (robotCallTaskListIds.size() < size) {
                        int fetchSize = size - robotCallTaskListIds.size();
                        robotCallTaskListIdsTemp = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList2(callJobId, fetchSize, noRedial);
                        robotCallTaskListIds.addAll(robotCallTaskListIdsTemp);
                    }
                } else {
                    robotCallTaskListIdsTemp = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList(callJobId, fetchSize1, noRedial, skipOrderBy);
                    robotCallTaskListIds.addAll(robotCallTaskListIdsTemp);
                }
            }
            // id转对象
            if (CollectionUtils.isNotEmpty(robotCallTaskListIds)) {
                robotCallTaskList = robotCallTaskPOMapper.getRunnableRobotCallTaskByIds(robotCallTaskListIds, callJobId, noRedial);
            }
        } else {
            robotCallTaskList = getPartitionRunnableRobotCallTaskList(
                    callJobId, subCallJobId, size, noRedial, robotCallJobPO.getConcurrencyQuota(), skipOrderBy, robotCallJobPO.getRedialType());
        }

        return robotCallTaskList;
    }

    @SuppressWarnings("unchecked")
    private List<RunTimeRobotCallTaskBO> getPartitionRunnableRobotCallTaskList(
            Long callJobId, Long subCallJobId, int size, Boolean noRedial, Integer concurrencyQuota, boolean skipOrderBy, RobotCallJobRedialTypeEnum redialType) {
        // 大任务，先从redis缓存中获取
        String jobTaskQueueKey = RedisKeyCenter.getJobTaskQueueKey(callJobId);
        String taskListStr = (String)redisOpsService.getRedisTemplate().opsForList().leftPop(jobTaskQueueKey);
        if (StringUtils.isNotBlank(taskListStr)) {
            return convert(taskListStr, callJobId, size, noRedial);
        }

        logger.info("redis缓存内容为空，重新加载");
        // redis上没有获取到，需要补充
        // 获取redis的全局锁
        String jobTaskQueueLockKey = RedisKeyCenter.getJobTaskQueueLockKey(callJobId);
        boolean locked = redisOpsService.setIfAbsent(jobTaskQueueLockKey, ServerInfoConstants.SERVER_HOSTNAME, 100);
        int tryCount = 0;
        while (!locked && tryCount++ < 2) {
            // 其他线程可能已获取数据 可以直接读取
            logger.info("获取全局锁失败，直接查询redis");
            taskListStr = (String)redisOpsService.getRedisTemplate().opsForList().leftPop(jobTaskQueueKey);
            if (StringUtils.isNotBlank(taskListStr)) {
                return convert(taskListStr, callJobId, size, noRedial);
            }
            // 剩余task为空 不再执行 直接返回
            Integer count = countRunnableRobotCallTaskList(callJobId);
            if (count == null || count == 0) {
                logger.info("不存在task，退出");
                return new ArrayList<>(0);
            }
            // 随机等待
            long waitSeconds = RandomUtils.nextLong(2, 5);
            logger.info("未获取到全局锁，{}秒后重试", waitSeconds);
            MyThreadUtils.sleepSeconds(waitSeconds);
            locked = redisOpsService.setIfAbsent(jobTaskQueueLockKey, ServerInfoConstants.SERVER_HOSTNAME, 100);
        }
        if (!locked) {
            logger.info("获取锁失败，退出");
            return new ArrayList<>(0);
        }
        long startTime = System.currentTimeMillis();
        int refreshRedisCount = 0;
        try {
            // 再尝试一次
            taskListStr = (String)redisOpsService.getRedisTemplate().opsForList().leftPop(jobTaskQueueKey);
            if (StringUtils.isNotBlank(taskListStr)) {
                return convert(taskListStr, callJobId, size, noRedial);
            }
            int redisCacheCapacity = concurrencyQuota * PARTITION_CHANNEL_TASK_CACHE_SIZE;
            List<Long> robotCallTaskIdList = new ArrayList<>();
            List<Long> robotCallTaskIdList1 = Collections.emptyList();
            // 首次外呼优先的自动重播 先查询status=0的数据
            if (RobotCallJobRedialTypeEnum.FIRST_CALL.equals(redialType)) {
                robotCallTaskIdList1 = robotCallTaskPOMapper.getRunnableRobotCallTaskIdListFirst(callJobId, redisCacheCapacity, skipOrderBy);
                robotCallTaskIdList.addAll(robotCallTaskIdList1);
            }
            if (robotCallTaskIdList1.size() < redisCacheCapacity) {
                int fetchSize1 = redisCacheCapacity - robotCallTaskIdList1.size();
                if (skipOrderBy) {
                    // 对于号码数量超过50万的任务，自动重拨的号码，优先级高于非自动重拨的号码，分两次获取
                    robotCallTaskIdList1 = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList1(callJobId, fetchSize1);
                    robotCallTaskIdList.addAll(robotCallTaskIdList1);
                    if (robotCallTaskIdList1.size() < redisCacheCapacity) {
                        int fetchSize = redisCacheCapacity - robotCallTaskIdList1.size();
                        List<Long> robotCallTaskIdList2 = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList2(callJobId, fetchSize, noRedial);
                        robotCallTaskIdList.addAll(robotCallTaskIdList2);
                    }
                } else {
                    robotCallTaskIdList1 = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList(callJobId, fetchSize1, noRedial, skipOrderBy);
                    robotCallTaskIdList.addAll(robotCallTaskIdList1);
                }
            }

            List<SubRobotCallJobPO> subJobList = subRobotCallJobService.selectSubRobotCallJobs(callJobId);
            List<String> taskIdStrList = JobUtils.buildRedisTaskCache(subJobList, robotCallTaskIdList);
            for (String eachTaskIdStr : taskIdStrList) {
                redisOpsService.getRedisTemplate().opsForList().rightPush(jobTaskQueueKey, eachTaskIdStr);
                ++refreshRedisCount;
            }
            redisOpsService.expire(jobTaskQueueKey, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            redisOpsService.delete(jobTaskQueueLockKey);
            if(refreshRedisCount > 0) {
                logger.info("刷新redis任务缓存，jobId={}, subJobId={}, size={}, 耗时 {} ms.", callJobId, subCallJobId, refreshRedisCount, System.currentTimeMillis() - startTime);
            }
        }

        taskListStr = (String)redisOpsService.getRedisTemplate().opsForList().leftPop(jobTaskQueueKey);
        return convert(taskListStr, callJobId, size, noRedial);
    }

    private List<RunTimeRobotCallTaskBO> convert(String taskIdListStr, Long callJobId, int size, Boolean noRedial) {
        if(StringUtils.isBlank(taskIdListStr)) {
            return new ArrayList<>(0);
        }

        String[] taskIdArr = StringUtils.split(taskIdListStr, ",");
        List<Long> robotCallTaskIds = new ArrayList<>(taskIdArr.length);
	    for (String s : taskIdArr) {
		    robotCallTaskIds.add(Long.valueOf(s));
	    }
        if (robotCallTaskIds.size() > size) {
            List<Long> discardTaskIds = robotCallTaskIds.subList(size, robotCallTaskIds.size());
            String jobTaskQueueKey = RedisKeyCenter.getJobTaskQueueKey(callJobId);
            String discardTaskIdsStr = StringUtils.join(discardTaskIds, ",");
            redisOpsService.getRedisTemplate().opsForList().leftPush(jobTaskQueueKey, discardTaskIdsStr);
            robotCallTaskIds = robotCallTaskIds.subList(0, size);
        }

        List<RunTimeRobotCallTaskBO> robotCallTaskList = new ArrayList<>(0);
        if (!robotCallTaskIds.isEmpty()) {
            robotCallTaskList = robotCallTaskPOMapper.getRunnableRobotCallTaskByIds(robotCallTaskIds, callJobId, noRedial);
        }
        return robotCallTaskList;
    }

    @Override
    public Integer countRunnableRobotCallTaskList(Long callJobId) {
        Boolean noRedial = getIsRedialById(callJobId);
        return robotCallTaskPOMapper.countRunnableRobotCallTaskList(callJobId, noRedial);
    }

    private Boolean getIsRedialById(Long callJobId) {
        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(callJobId);
        List<RedialSettingBO> redialSettingBOList = robotCallJobPO.getRedialSettingList();
        return CollectionUtils.isEmpty(redialSettingBOList);
    }

    @Override
    public LocalDateTime getRobotCallTaskRedialMinTime(Long callJobId) {
        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(callJobId);
        return getRobotCallTaskRedialMinTime(robotCallJobPO);
    }

    @Override
    public LocalDateTime getRobotCallTaskRedialMinTime(RobotCallJobPO robotCallJobPO) {
        // 设置了自动重拨
        // 预约重拨不在任务里设置 所有的都按自动重拨查找
        Integer redialInterval = robotCallJobPO.getRedialInterval();
        if (redialInterval == null) {
            redialInterval = 0;
        }
        return LocalDateTime.now().minusMinutes(redialInterval);
    }


    @Override
    public void updateRobotCallTaskById(RobotCallTaskPO robotCallTaskInfo) {
        Assert.notNull(robotCallTaskInfo.getRobotCallTaskId(), "RobotCallTaskPO的主键id不能为空");
        robotCallTaskPOMapper.updateRobotCallTaskById(robotCallTaskInfo);
    }

    @Override
    public int updateRobotCallTaskNewStatusById(Long tenantId, Long robotCallJobId, Long robotCallTaskId, RobotCallTaskStatusEnum status, RobotCallTaskStatusEnum oldStatus) {
	    int updateCount = robotCallTaskPOMapper.updateRobotCallTaskNewStatusById(robotCallTaskId, status, oldStatus);
	    // 更新外呼监控(悬浮窗)实时并发数
    	if (RobotCallTaskStatusEnum.IN_PROCESS.equals(oldStatus) && !RobotCallTaskStatusEnum.IN_PROCESS.equals(status)) {
    		decrementTenantConcurrency(tenantId, robotCallJobId, updateCount);
	    } else if (!RobotCallTaskStatusEnum.IN_PROCESS.equals(oldStatus) && RobotCallTaskStatusEnum.IN_PROCESS.equals(status)) {
		    incrementRobotConcurrencyCache(tenantId, robotCallJobId, updateCount);
	    }
    	return updateCount;
    }

    @Override
    public int updateRobotCallTaskNewStatusWithTimeById(Long tenantId, Long robotCallJobId, Long robotCallTaskId, RobotCallTaskStatusEnum status, RobotCallTaskStatusEnum oldStatus, LocalDateTime lastCalledTime) {
	    int updateCount = robotCallTaskPOMapper.updateRobotCallTaskNewStatusWithTimeById(robotCallTaskId, status, oldStatus, lastCalledTime);
	    // 更新外呼监控(悬浮窗)实时并发数
	    if (RobotCallTaskStatusEnum.IN_PROCESS.equals(oldStatus) && !RobotCallTaskStatusEnum.IN_PROCESS.equals(status)) {
		    decrementTenantConcurrency(tenantId, robotCallJobId, updateCount);
	    } else if (!RobotCallTaskStatusEnum.IN_PROCESS.equals(oldStatus) && RobotCallTaskStatusEnum.IN_PROCESS.equals(status)) {
		    incrementRobotConcurrencyCache(tenantId, robotCallJobId, updateCount);
	    }
	    return updateCount;
    }

    @Override
    public boolean isAllJobTaskRunFinished(Long callJobId) {
        return robotCallTaskPOMapper.isAllJobTaskRunFinished(callJobId) == 0;
    }

    @Override
    public boolean allLeftTasksWaiting4Redial(Long callJobId) {
        // 是否有可运行的子任务
        List<Long> idList = robotCallTaskPOMapper.getRunnableRobotCallTaskIdList(callJobId, 1, false, true);
        return CollectionUtils.isEmpty(idList);
    }

    @Override
    public LocalDateTime getMinNextCalledTimeByJobId(Long jobId) {
        return robotCallTaskPOMapper.getMinNextCalledTimeByJobId(jobId);
    }

    @Override
    public int countRunningTask(Long robotCallJobId) {
        Integer count = robotCallTaskPOMapper.countRunningTask(robotCallJobId);
        if (count == null) {
            count = 0;
        }
        return count;
    }

    @Override
    public void checkAndUpdateTimeoutCallTaskToNotStart(RobotCallTaskStatusEnum status, int time) {
        // 检查task在运行中的并且超过time分钟没有更新的，将任务置为未开始状态
	    List<RobotCallTaskPO> robotCallTasks;
        do {
	        // 以分钟为单位且并发量越高查询范围越大
	        robotCallTasks = robotCallTaskPOMapper.selectTimeoutCallTaskToNotStart(status, time);
	        if (CollectionUtils.isNotEmpty(robotCallTasks)) {
                for (RobotCallTaskPO task : robotCallTasks) {
                    // 进行中重置为已完成，避免重复拨打 其他重置为未开始
                    RobotCallTaskStatusEnum resetStatus = RobotCallTaskStatusEnum.COMPLETED;
                    int count = updateRobotCallTaskNewStatusById(task.getTenantId(), task.getRobotCallJobId(), task.getRobotCallTaskId(), resetStatus, status);
                    if (count > 0) {
                        if (task.getRobotCallJobId() != null) {
                            logger.warn("[LogHub_Warn] task={}超时重置", task.getRobotCallTaskId());
                        } else {
                            logger.warn("快速拨打task={}超时重置", task.getRobotCallTaskId());
                        }
                    }
                }
            }
        } while (CollectionUtils.isNotEmpty(robotCallTasks));
    }

    @Override
    @Batched(batchedArgName = "customerPersonInfoList", executeMaxLimit = 100000, executeStepSize = 100)
    public void addCustomerPersonsToRobotCallJob(List<CustomerPersonInfoForRobotCallJobBO> customerPersonInfoList,
                                                 RobotCallJobPO robotCallJobInfo, Long currentTenantId, Long currentUserId, Long intentLevelTagId,
                                                 List<CustomerPersonInfoForRobotCallJobBO> duplicateCustomers,
                                                 List<CustomerPersonInfoForRobotCallJobBO> planDuplicateCustomers,
                                                 boolean callRecordDup, Boolean distinctInPlan) {
        Assert.notNull(robotCallJobInfo, "呼叫任务id不可以是null");
        Assert.notNull(currentTenantId, "租户id不可以是null");
        Assert.notNull(currentUserId, "用户id不可以是null");
        if (CollectionUtils.isEmpty(customerPersonInfoList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "添加到任务的客户不可以为空");
        }

        Long dialogFlowId = robotCallJobInfo.getDialogFlowId();
        Long createUserId = robotCallJobInfo.getCreateUserId();
        // update customer person with new properties
        // 已经在insert的时候更新了 此处更新有可能出现刚insert的数据查不到的情况
        logger.debug("开始将{}加入呼叫任务{}，此时的租户id是{}，用户id是{}，话术id是{}，任务创建用户id是{}", customerPersonInfoList, robotCallJobInfo, currentTenantId, currentUserId, dialogFlowId, createUserId);
        int addCount = getNumber(customerPersonInfoList, robotCallJobInfo, dialogFlowId, currentTenantId, createUserId, currentUserId, intentLevelTagId, duplicateCustomers, planDuplicateCustomers, callRecordDup, distinctInPlan);

        // 更新统计信息
        callStatsService.updateCallJobTotalTask(currentTenantId, robotCallJobInfo.getRobotCallJobId(), dialogFlowId, addCount);

        try {
            resetRobotCallJobStatusWhenCallTaskAddToCallJob(addCount, robotCallJobInfo);
        } catch (Exception e) {
            logger.error("resetRobotCallJobStatusWhenCallTaskAddToCallJob error", e);
        }

        //增加导入客户数
        callStatsService.updateLeadInCount(currentTenantId, currentUserId, robotCallJobInfo, addCount);
    }

	private Integer getNumber(List<CustomerPersonInfoForRobotCallJobBO> customerPersonInfoList, RobotCallJobPO robotCallJob,
	                          Long dialogFlowId, Long currentTenantId, Long createUserId, Long currentUserId, Long intentLevelTagId,
	                          List<CustomerPersonInfoForRobotCallJobBO> duplicateCustomers, List<CustomerPersonInfoForRobotCallJobBO> planDuplicateCustomers,
	                          boolean callRecordDup, Boolean distinctInPlan) {
		List<String> lockKeys = new ArrayList<>(customerPersonInfoList.size());
		List<CustomerPersonInfoForRobotCallJobBO> getLockFailedCustomers = new ArrayList<>(2);
		Long robotCallJobId = robotCallJob.getRobotCallJobId();
		try {
			boolean checkDistinctInPlan = checkDistinctInPlan(robotCallJob, distinctInPlan);
			List<CustomerPersonInfoForRobotCallJobBO> getLockCustomers = new ArrayList<>(customerPersonInfoList.size());
			Set<CustomerPersonInfoForRobotCallJobBO> duplicateCustomerSet = new HashSet<>(customerPersonInfoList.size());
			
			List<RobotCallTaskPO>robotCallTaskPOList=new ArrayList<>();
			
            Boolean filterOpenState = mongoOperationService.getFilterOpenState(robotCallJobId, currentTenantId);

//            setCustomerPersonNameIfEmpty(currentTenantId, customerPersonInfoList);

            for (CustomerPersonInfoForRobotCallJobBO customerPerson : customerPersonInfoList) {
				// 外呼计划内去重
				if (checkDistinctInPlan && !callOutPlanTaskUniqueService.insert(currentTenantId,
						robotCallJobId, robotCallJob.getCallOutPlanId(), customerPerson.getCustomerPersonId())) {
					planDuplicateCustomers.add(customerPerson);
					logger.info("customerPersonId={}被外呼计划内去重", customerPerson.getCustomerPersonId());
					continue;
				}
				// 1. 更新duplicate 2. 根据duplicate查询重复导入 3. 重置duplicate状态 流程加锁
				String lockKey = RedisKeyCenter.openApiImportCustomerToJobLock(robotCallJobId, customerPerson.getPhoneNumber());
				// 获取不到锁立即返回, setNX失败不阻塞等待
				if (redisOpsService.setIfAbsent(lockKey, 1, 3)) {
					lockKeys.add(lockKey);
					
					robotCallTaskPOMapper.addCustomerPersonsToRobotCallJob(customerPerson, robotCallJobId, dialogFlowId, currentTenantId, createUserId, currentUserId, intentLevelTagId, null, callRecordDup);
					
					if(filterOpenState){
                        RobotCallTaskPO robotCallTaskPO=new RobotCallTaskPO();
                        robotCallTaskPO.setTenantId(currentTenantId);
                        robotCallTaskPO.setRobotCallJobId(robotCallJobId);
                        robotCallTaskPO.setCustomerPersonId(customerPerson.getCustomerPersonId());
                        robotCallTaskPO.setCalledPhoneNumber(customerPerson.getPhoneNumber());
                        robotCallTaskPO.setProperties(customerPerson.getProperties());
                        robotCallTaskPO.setCreateTime(LocalDateTime.now());
                        robotCallTaskPO.setCreateUserId(currentUserId);
                        robotCallTaskPO.setUpdateUserId(currentUserId);
                        robotCallTaskPO.setCustomerPersonName(customerPerson.getCustomerPersonName());
                        robotCallTaskPOList.add(robotCallTaskPO);
                    }
					
                    
					getLockCustomers.add(customerPerson);
				} else {
					getLockFailedCustomers.add(customerPerson);
				}
			}
            
			if(filterOpenState){
                robotCallTaskFilterEventPublisher.cacheTaskInfo(robotCallTaskPOList);
            }
			
			int insertCount = customerPersonInfoList.size();

			// 计算重复客户
			if (CollectionUtils.isNotEmpty(getLockCustomers)) {
				List<Long> customerPersonIds = getLockCustomers.stream().map(CustomerPersonInfoForRobotCallJobBO::getCustomerPersonId).collect(Collectors.toList());
				updateImportedCustomer(RobotCallTaskMapperEnum.ROBOT_CALL_TASK_PO_MAPPER, robotCallJobId, dialogFlowId, customerPersonIds);
				// 校验客户在任务中是否已经存在，如果存在则加入duplicateCustomers里面, 获取锁成功且duplicate被更新为1的记录
				duplicateCustomerSet.addAll(getDuplicateCustomers(robotCallJobId, getLockCustomers));
			}
			// 获取锁失败的记录
			duplicateCustomerSet.addAll(getLockFailedCustomers);
			// set去重后加入duplicateCustomers
			duplicateCustomers.addAll(duplicateCustomerSet);
			int duplicateCount = duplicateCustomers.size();
			int planDuplicateCount = planDuplicateCustomers.size();
			logger.debug("本次批量导入到任务{}，成功{}个，失败{}个", robotCallJobId, insertCount, duplicateCount + planDuplicateCount);
			return insertCount - duplicateCount - planDuplicateCount;
		} finally {
    		lockKeys.forEach(redisOpsService::delete);
	    }
    }



    /**
     * 导入客户如果不包含客户名称，从会员中心获取
     * @param tenantId
     * @param customerPersonImportDTOS
     */
    public void setCustomerPersonNameIfEmpty(Long tenantId, List<CustomerPersonInfoForRobotCallJobBO> customerPersonImportDTOS) {
        if (CollectionUtils.isNotEmpty(customerPersonImportDTOS)) {
            List<String> phoneNumberList = customerPersonImportDTOS.stream()
                    .filter(person -> org.apache.commons.lang3.StringUtils.isEmpty(person.getCustomerPersonName()))
                    .map(CustomerPersonInfoForRobotCallJobBO::getPhoneNumber)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(phoneNumberList)) return;

            PhoneNumberListRequest phoneNumberListRequest = new PhoneNumberListRequest();
            phoneNumberListRequest.setTenantId(tenantId);
            phoneNumberListRequest.setPhoneList(phoneNumberList);

            List<AccountVO> accountInfoListByPhone = accountClient.getAccountInfoListByPhone(phoneNumberListRequest);

            if (CollectionUtils.isNotEmpty(accountInfoListByPhone)) {
                Map<String, AccountVO> numberAccountMap = com.yiwise.core.utils.MyCollectionUtils.listToMap(accountInfoListByPhone, AccountVO::getMainPhoneNumber);

                customerPersonImportDTOS.forEach(person -> {
                    if (org.apache.commons.lang3.StringUtils.isEmpty(person.getCustomerPersonName()) && numberAccountMap.containsKey(person.getPhoneNumber())) {
                        AccountVO accountVO = numberAccountMap.get(person.getPhoneNumber());
                        person.setCustomerPersonName(accountVO.getName());
                    }
                });
            }
        }
    }

    @Override
    public void addCustomerPersonToRobotCallJob(CustomerPersonInfoForRobotCallJobBO customerPersonInfo, Long robotCallJobId, Long currentTenantId,
                                                Long currentUserId, boolean callRecordDup, String addWechatAccountId, Long addWechatOrgId, Long addWechatScrmUserId) {
        Assert.notNull(robotCallJobId, "呼叫任务id不可以是null");
        Assert.notNull(currentTenantId, "租户id不可以是null");
        Assert.notNull(currentUserId, "用户id不可以是null");
        if (customerPersonInfo == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "添加到任务的客户不可以为空");
        }
        // 校验要加入的任务id是否属于该租户
        RobotCallJobPO robotCallJobInfo = robotCallJobService.getRobotCallJobById(currentTenantId, robotCallJobId);
	    if (robotCallJobClearService.isClearing(robotCallJobId, robotCallJobInfo.getCallOutPlanId())) {
		    throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选外呼任务数据删除中，无法导入客户");
	    }
	    if (RobotCallJobStatusEnum.canNotImportCustomer(robotCallJobInfo.getStatus())) {
		    throw new ComException(ComErrorCode.VALIDATE_ERROR, "已归档任务不支持导入客户");
	    }

        Long dialogFlowId = robotCallJobInfo.getDialogFlowId();
        Long createUserId = robotCallJobInfo.getCreateUserId();
        DialogFlowInfoPO dialogFlowInfoPO = dialogFlowService.selectByKey(dialogFlowId);
        Long intentLevelTagId = dialogFlowInfoPO.getIntentLevelTagId();

        logger.debug("开始将{}加入呼叫任务{}，此时的租户id是{}，用户id是{}，话术id是{}，任务创建用户id是{}",
                customerPersonInfo, robotCallJobId, currentTenantId, currentUserId, dialogFlowId, createUserId);



        // 新增单个
        RobotCallTaskPO robotCallTaskPO = new RobotCallTaskPO();
        robotCallTaskPO.setRobotCallJobId(robotCallJobId);
        robotCallTaskPO.setDialogFlowId(dialogFlowId);
        robotCallTaskPO.setTenantId(currentTenantId);
        robotCallTaskPO.setCreateUserId(createUserId);
        robotCallTaskPO.setUpdateUserId(currentUserId);
        robotCallTaskPO.setIntentLevelTagId(intentLevelTagId);
        robotCallTaskPO.setCustomerPersonId(customerPersonInfo.getCustomerPersonId());
        robotCallTaskPO.setCalledPhoneNumber(customerPersonInfo.getPhoneNumber());
        robotCallTaskPO.setCustomerPersonName(customerPersonInfo.getCustomerPersonName());
        robotCallTaskPO.setProperties(customerPersonInfo.getProperties());
        if (addWechatAccountId != null) {
            robotCallTaskPO.setAddWechatAccountId(addWechatAccountId);
        }
        if (addWechatOrgId != null) {
            robotCallTaskPO.setAddWechatOrgId(addWechatOrgId);
        }
        if (addWechatScrmUserId != null) {
	        robotCallTaskPO.setAddWechatOrgId(addWechatScrmUserId);
        }

        if (StringUtils.isEmpty(customerPersonInfo.getCustomerPersonName()) && StringUtils.isNotEmpty(customerPersonInfo.getPhoneNumber())) {
            AccountVO accountInfoByTenantIdAndPhoneNumber = accountClient.getAccountInfoByTenantIdAndPhoneNumber(currentTenantId, customerPersonInfo.getPhoneNumber());
            if (Objects.nonNull(accountInfoByTenantIdAndPhoneNumber)) {
                robotCallTaskPO.setCustomerPersonName(accountInfoByTenantIdAndPhoneNumber.getName());
            }
        }

	    addRobotCallTask(robotCallTaskPO, callRecordDup);

	    updateImportedCustomer(RobotCallTaskMapperEnum.ROBOT_CALL_TASK_PO_MAPPER, robotCallJobId, dialogFlowId, Collections.singletonList(customerPersonInfo.getCustomerPersonId()));
	    List<CustomerPersonInfoForRobotCallJobBO> duplicateCustomers = getDuplicateCustomers(robotCallJobId, Collections.singletonList(customerPersonInfo));
	    if (duplicateCustomers.size() > 0) {
		    throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "客户" + customerPersonInfo.getPhoneNumber() + "已在该任务中");
	    }

        // 更新统计信息
        callStatsService.updateCallJobTotalTask(currentTenantId, robotCallJobId, robotCallJobInfo.getDialogFlowId(), 1);
        //重置任务状态
        resetRobotCallJobStatusWhenCallTaskAddToCallJob(1, robotCallJobInfo);
        //增加导入客户数
        callStatsService.updateLeadInCount(currentTenantId, currentUserId, robotCallJobInfo, 1);
        
        //增加过滤客户数统计
        Boolean filterOpenState = mongoOperationService.getFilterOpenState(robotCallJobId, currentTenantId);
        if(filterOpenState){
            robotCallTaskFilterEventPublisher.cacheTaskInfo(Lists.newArrayList(robotCallTaskPO));
        }
    }
   
    @Override
    public Integer addRobotCallTask(RobotCallTaskPO robotCallTaskPO, boolean callRecordDup) {
        return robotCallTaskPOMapper.addOneCustomerPersonToRobotCallJob(robotCallTaskPO, callRecordDup);
    }

    // todo
    @Override
    public ReAddResultBO reAddCustomerPersonsToRobotCallJob(Long currentUserId,
                                                            ReAddCustomerPersonToRobotCallJobRequestVO request) {
    	if (robotCallJobClearService.isClearing(request.getRobotCallJobId())) {
		    throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选外呼任务数据删除中，无法导入客户");
	    }
	    RobotCallJobPO robotCallJob = robotCallJobService.selectByKey(request.getRobotCallJobId());
	    if (RobotCallJobStatusEnum.canNotImportCustomer(robotCallJob.getStatus())) {
		    throw new ComException(ComErrorCode.VALIDATE_ERROR, "已归档任务不支持导入客户");
	    }
        AtomicBoolean empty = new AtomicBoolean(true);
        Set<Long> customerPersonIds = request.getCustomerPersonIds();
        AtomicInteger whiteCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger interceptCount = new AtomicInteger(0);
        AtomicInteger otherFollowerCount = new AtomicInteger(0);
        int count;
        if (request.isLastCallRecord()) {
            count = getCountLastCallRecordReAddByTask(request);
        } else {
            count = callRecordInfoService.countCallRecordsByIdsOrConditions(request);
        }
        if (count > MAX_BATCH_SIZE) {
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "每次操作数据不能超过" + MAX_BATCH_SIZE + "条");
        }
        // 重新添加 通过条件筛选
        if (CollectionUtils.isEmpty(customerPersonIds)) {
            // 最近一次通话记录 用task表查询
            if (request.isLastCallRecord()) {
                HandleByPageUtils.handlePage(300, () -> robotCallTaskPOMapper.getLastCallRecordCustomerPersonIdByTask(request), (ids) -> {
                    if (reAddPartIds(currentUserId, request, ids, successCount, request.getSystemType()) && empty.get()) {
                        empty.set(false);
                    }
                }, true);
            } else {
                HandleByPageUtils.handlePage(300, () -> callRecordInfoService.getCustomerPersonIdsByCondition(request), (ids) -> {
                    if (reAddPartIds(currentUserId, request, ids, successCount, request.getSystemType()) && empty.get()) {
                        empty.set(false);
                    }
                }, true);
            }
        } else {
            if (reAddPartIds(currentUserId, request, new ArrayList<>(customerPersonIds), successCount, request.getSystemType()) && empty.get()) {
                empty.set(false);
            }
        }
        return new ReAddResultBO(successCount.get(), whiteCount.get(), interceptCount.get(), otherFollowerCount.get());
    }

    /**
     * @return 是否有添加成功的 false表示添加的为空
     */
    private boolean reAddPartIds(Long currentUserId, ReAddCustomerPersonToRobotCallJobRequestVO request, List<Long> customerPersonIdList,
                                 AtomicInteger successCount, SystemEnum systemType) {
        if (CollectionUtils.isEmpty(customerPersonIdList)) {
            return false;
        }
        resetTaskStatusReTry(customerPersonIdList, request.getRobotCallJobId(), request.getTenantId(), currentUserId, systemType);
        successCount.set(successCount.addAndGet(customerPersonIdList.size()));
        return CollectionUtils.isNotEmpty(customerPersonIdList);
    }

    @Override
    public void resetTaskStatusReTry(List<Long> customerPersonIdList, Long robotCallJobId, Long tenantId, Long userId, SystemEnum systemType) {
        // 校验要加入的任务id是否属于该租户
        RobotCallJobPO robotCallJobInfo = robotCallJobService.getRobotCallJobById(tenantId, robotCallJobId);
        // 设置重拨次数
        // 用基本类型的话，下面会报一个npe的warning
        Integer addCount = robotCallTaskPOMapper.reAddCustomerPersonsToRobotCallJob(customerPersonIdList,
                robotCallJobId, tenantId, userId);
        // 更新统计信息
        callStatsService.updateCallJobTotalTask(tenantId, robotCallJobId, robotCallJobInfo.getDialogFlowId(), addCount);
	    logger.info("incrementJobImportTaskDaily 3");
        callStatsService.incrementJobImportTaskDaily(robotCallJobId, robotCallJobInfo.getDialogFlowId(), addCount);
        // 更新任务状态
        resetRobotCallJobStatusWhenCallTaskAddToCallJob(addCount, robotCallJobInfo);
    }

    @Override
    public void resetRobotCallJobStatusWhenCallTaskAddToCallJob(Integer addCount, RobotCallJobPO robotCallJobInfo) {
        Long robotCallJobId = robotCallJobInfo.getRobotCallJobId();

        // 如果加入的个数大于0，并且任务现在处于已完成
        if (!Objects.equals(addCount, 0)) {
            if (RobotCallJobStatusEnum.COMPLETED.equals(robotCallJobService.getRobotCallJobStatus(robotCallJobId))) {
                // 手动任务未开始
                if (RobotCallJobModeEnum.MANUAL.equals(robotCallJobInfo.getMode())) {
                    robotCallJobService.updateRobotCallJobStatusById(robotCallJobId, RobotCallJobStatusEnum.NOT_STARTED, RobotCallJobStatusEnum.COMPLETED);
                }
                // 自动任务 排队中 TODO 根据可运行时段设置状态
                else {
                    Optional<ActiveTimeBO> nearestActiveTime = NewRobotCallJobActiveTimeHelper.getNearestActiveTime(robotCallJobInfo, LocalDateTime.now());
                    RobotCallJobPO updateRobotCallJob = new RobotCallJobPO();
                    updateRobotCallJob.setRobotCallJobId(robotCallJobId);
                    if (robotCallJobInfo.getStartTime() != null) {
                        // 自动任务的下次开始时间需要重新计算
                        LocalDateTime startTime = LocalDateTime.now().isAfter(robotCallJobInfo.getStartTime()) ? LocalDateTime.now() : robotCallJobInfo.getStartTime();
                        nearestActiveTime = NewRobotCallJobActiveTimeHelper.getNearestActiveTime(robotCallJobInfo, startTime);
                        updateRobotCallJob.setStatus(RobotCallJobStatusEnum.NOT_STARTED);
                    } else {
                        updateRobotCallJob.setStatus(RobotCallJobStatusEnum.IN_QUEUE);
                        updateRobotCallJob.setInQueueType(InQueueEnum.LESS_ROBOT_COUNT);
                        updateRobotCallJob.setInQueueReason(InQueueEnum.LESS_ROBOT_COUNT.getDesc());
                    }
                    robotCallJobService.computeNextStartAndEndDateTime(updateRobotCallJob, nearestActiveTime);
                    robotCallJobService.updateNotNull(updateRobotCallJob);
                }
            } else if (RobotCallJobStatusEnum.WAITING_FOR_REDIAL.equals(robotCallJobService.getRobotCallJobStatus(robotCallJobId))) {
                // 等待重播状态的任务在导入客户时改为排队中
                robotCallJobService.updateRobotCallJobStatusById(robotCallJobId, RobotCallJobStatusEnum.IN_QUEUE, null, null, InQueueEnum.LESS_ROBOT_COUNT, RobotCallJobStatusEnum.WAITING_FOR_REDIAL);
            }
            robotCallJobService.doWhenJobStatusChange(robotCallJobId);
        }
    }

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public PageResultObject<ToBeCalledTaskVO> getToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery) {
        Long robotCallJobId = toBeCalledListQuery.getRobotCallJobId();

        if (StringUtils.isEmpty(toBeCalledListQuery.getSearchWords())) {
            toBeCalledListQuery.setSearchWords(toBeCalledListQuery.getCalledPhoneNumber());
        }

        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);

        LocalDateTime taskRedialMinTime = getRobotCallTaskRedialMinTime(robotCallJobPO);
        toBeCalledListQuery.setTaskRedialMinTime(taskRedialMinTime);

//        if (StringUtils.isNotEmpty(toBeCalledListQuery.getCustomerPersonName())) {
//            List<Long> accountIds = accountClient.queryAccountIdsByName(toBeCalledListQuery.getTenantId(), toBeCalledListQuery.getCustomerPersonName());
//            if (CollectionUtils.isEmpty(accountIds)) {
//                return PageResultObject.of(Lists.newArrayList());
//            }
//            toBeCalledListQuery.setAccountIds(accountIds);
//        }

        PageHelper.startPage(toBeCalledListQuery.getPageNum(), toBeCalledListQuery.getPageSize());
        List<ToBeCalledTaskVO> toBeCalledTaskList;

        toBeCalledTaskList = robotCallTaskPOMapper.selectToBeCalledRobotTaskList(toBeCalledListQuery);

        // 如果是不可拨打的任务，计算出可拨打的时间
        LocalDateTime now = LocalDateTime.now();
        toBeCalledTaskList.forEach(item -> {
            if (RobotCallTaskStatusEnum.IN_PROCESS.equals(item.getStatus())) {
                item.setRunnableStatus(RobotCallTaskStatusEnum.IN_PROCESS);
            } else if (RobotCallTaskStatusEnum.mayCanNotStart(item.getStatus()) && item.getNextCalledTime() != null && item.getNextCalledTime().isAfter(now)) {
                item.setRunnableStatus(RobotCallTaskStatusEnum.CAN_NOT_START);
            } else {
                item.setRunnableStatus(RobotCallTaskStatusEnum.CAN_START);
            }
        });

        // 加微账号
	    Map<String, String> addWechatAccountIdNameMap = Collections.emptyMap();
	    Set<String> addWechatAccountIds = toBeCalledTaskList.stream().map(ToBeCalledTaskVO::getAddWechatAccountId).filter(Objects::nonNull).collect(Collectors.toSet());
	    if (!addWechatAccountIds.isEmpty()) {
		    List<ScrmWechatBO> wechats = tenantScrmService.getWechatByIdNames(toBeCalledListQuery.getTenantId(), null, null, addWechatAccountIds);
		    addWechatAccountIdNameMap = MyCollectionUtils.listToMap(wechats, ScrmWechatBO::getWxworkUserId, ScrmWechatBO::getUserName);
	    }
	    // 加微组织(SCRM自动)/加微账号(SCRM手动)
	    Map<Long, String> addWechatOrgIdNameMap = Collections.emptyMap();
	    Set<Long> addWechatOrgIds = toBeCalledTaskList.stream().map(ToBeCalledTaskVO::getAddWechatOrgId).filter(Objects::nonNull).collect(Collectors.toSet());
	    if (!addWechatOrgIds.isEmpty()) {
		    WechatCpAddFriendEnum addFriendType = robotCallJobPO.getWechatCpAddFriend();
		    if (WechatCpAddFriendEnum.YIWISE_SCRM_AUTO.equals(addFriendType)) {
			    List<ScrmOrgBO> wechats = tenantScrmService.getOrgByIdNames(toBeCalledListQuery.getTenantId(), addWechatOrgIds, null);
			    addWechatOrgIdNameMap = MyCollectionUtils.listToMap(wechats, ScrmOrgBO::getOrganizationId, ScrmOrgBO::getName);
		    } else if (WechatCpAddFriendEnum.YIWISE_SCRM_MANUAL.equals(addFriendType)) {
			    List<ScrmManualWechatBO> wechats = scrmManualAddWechatFriendService.queryWechatList(toBeCalledListQuery.getTenantId(), null, addWechatOrgIds);
			    addWechatOrgIdNameMap = MyCollectionUtils.listToMap(wechats, ScrmManualWechatBO::getUserId, ScrmManualWechatBO::getName);
		    }
	    }

        Map<String, PhoneHomeLocationBO> locationMap = phoneLocationService.getPhoneLocationMap(toBeCalledTaskList.stream().map(ToBeCalledTaskVO::getCalledPhoneNumber).collect(Collectors.toList()));
	    for (ToBeCalledTaskVO item : toBeCalledTaskList) {
            String phoneNumber = item.getCalledPhoneNumber();
            if (!StringUtils.isEmpty(phoneNumber) && phoneNumber.length() >= 7 && !PhoneNumberHelper.isExtensionPhone(phoneNumber)) {
                PhoneHomeLocationBO locationBO = locationMap.get(phoneNumber.substring(0, 7));
                if (Objects.nonNull(locationBO)) {
                    if (org.apache.commons.lang3.StringUtils.equals(locationBO.getProv(), locationBO.getCity())) {
                        item.setLocation(locationBO.getCity());
                    } else {
                        item.setLocation(locationBO.getProv() + locationBO.getCity());
                    }
                }
            }
	        if (StringUtils.isNotBlank(item.getAddWechatAccountId())) {
		        item.setAddWechatAccountName(addWechatAccountIdNameMap.get(item.getAddWechatAccountId()));
	        }
	        if (item.getAddWechatOrgId() != null) {
		        item.setAddWechatOrgName(addWechatOrgIdNameMap.get(item.getAddWechatOrgId()));
	        }
        }

        resolveCustomerPersonInfo(toBeCalledTaskList);
        return PageResultObject.of(toBeCalledTaskList);
    }

    @Override
    public void resolveCustomerPersonInfo(List<? extends ToBeCalledTaskVO> toBeCalledTaskList) {
        Set<Long> customerPersonIdSet = toBeCalledTaskList.stream().map(ToBeCalledTaskVO::getCustomerPersonId).filter(item -> item != null && item > 0).collect(Collectors.toSet());
        Map<Long, AccountVO> accountVOMap = customerPersonService.getCustomerPersonByIds(customerPersonIdSet);
        toBeCalledTaskList.forEach(item -> {
            AccountVO accountVO = accountVOMap.get(item.getCustomerPersonId());
            if (accountVO != null) {
                //客户姓名
//                item.setCustomerPersonName(accountVO.getName());
//                if (StringUtils.isEmpty(item.getCustomerPersonName())) {
//                    item.setCustomerPersonName(accountVO.getName());
//                }

                item.setAlternatePhoneNumbers(accountVO.getMobile());
                item.setCreateSources(accountVO.getCreateSources());
                item.setGender(CdpEnumUtil.changeGenderToAicc(accountVO.getGender()));
                item.setBirthday(accountVO.getBirthday());
            } else {
                item.setInWhiteList(false);
                item.setInShareWhiteList(false);
            }

            if (RobotCallTaskStatusEnum.RETRY.equals(item.getStatus())) {
                item.setStatus(RobotCallTaskStatusEnum.NOT_STARTED);
            }
        });
    }

	/**
	 * 从customersForImport里面找到与呼叫任务里面重复的客户
	 *
	 * @param robotCallJobId     呼叫任务id
	 * @param customersForImport 本次导入的客户
	 * @return 重复的客户
	 */
	private List<CustomerPersonInfoForRobotCallJobBO> getDuplicateCustomers(Long robotCallJobId, List<CustomerPersonInfoForRobotCallJobBO> customersForImport) {
		List<CustomerPersonInfoForRobotCallJobBO> duplicateCustomers = new ArrayList<>();
		Set<Long> customerPersonIds = customersForImport.stream().map(CustomerPersonInfoForRobotCallJobBO::getCustomerPersonId).collect(Collectors.toSet());
		Set<Long> existedCustomerIdsInRobotCallJob = robotCallTaskPOMapper.getDuplicateCustomersInRobotCallJob(robotCallJobId, customerPersonIds);
		if (!CollectionUtils.isEmpty(existedCustomerIdsInRobotCallJob)) {
			for (CustomerPersonInfoForRobotCallJobBO customerPerson : customersForImport) {
				Long customerPersonId = customerPerson.getCustomerPersonId();
				if (existedCustomerIdsInRobotCallJob.contains(customerPersonId)) {
					duplicateCustomers.add(customerPerson);
				}
			}
			logger.debug("加入到任务{}时，{}出现重复", robotCallJobId, duplicateCustomers);
			robotCallTaskPOMapper.resetDuplicateField(robotCallJobId, existedCustomerIdsInRobotCallJob);
		}
		return duplicateCustomers;
	}

    /**
     * 删除callTask
     */
    @Override
    public void deleteRobotCallTaskById(Long robotTaskId, Long tenantId, Long userId, boolean isOpenApi) {
        RobotCallTaskPO robotTask = selectByKeyOrThrow(robotTaskId);

        if (!robotTask.getTenantId().equals(tenantId)) {
            String msg = "该租户不是任务的拥有者";
            String format = MessageFormat.format("租户:{0}不是Task:{1}的拥有者", tenantId, robotTaskId);
            throw new ComException(ComErrorCode.VALIDATE_ERROR, msg, format);
        }

        RobotCallTaskStatusEnum taskStatus = robotTask.getStatus();
        if (!taskStatus.isDeletable()) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前子任务处于" + taskStatus.getDesc() + "不能删除", "当前子任务处于" + taskStatus.getDesc() + "不能删除，TaskId=" + robotTaskId);
        }

        Long robotCallJobId = robotTask.getRobotCallJobId();

        ToBeCalledListQueryVO toBeCalledListQuery = new ToBeCalledListQueryVO();
        toBeCalledListQuery.setSkipStatus(true);
        toBeCalledListQuery.setTenantId(tenantId);
        toBeCalledListQuery.setRobotCallJobId(robotCallJobId);
        toBeCalledListQuery.setRobotCallTaskIds(Collections.singletonList(robotTaskId));

        int delete = deleteNotUsedToBeCalledRobotTaskList(toBeCalledListQuery);
        int update = updateUsedToBeCalledRobotTaskList(toBeCalledListQuery);
	    deleteCallOutPlanTaskUnique(tenantId, robotCallJobId, robotTask.getCustomerPersonId());
        if (delete > 0) {
            callStatsService.reduceCustomerNumber(robotCallJobId, robotTask.getDialogFlowId(), (long) delete);
        }
        if (delete > 0 || update > 0 ) {
            robotCallJobService.deleteCustomerPersonsToRobotCallJob(tenantId, robotCallJobId, robotTask.getDialogFlowId(), -1);
        }
        operationLogService.onRobotCallTaskDelete(robotCallJobId, tenantId, userId,isOpenApi);
    }

    @Override
    public int deleteRobotCallTaskList(ToBeCalledListQueryVO toBeCalledListQuery) {
        Long robotCallJobId = toBeCalledListQuery.getRobotCallJobId();
	    Long dialogFlowId = null;
	    RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
	    if (robotCallJobPO != null) {
		    dialogFlowId = robotCallJobPO.getDialogFlowId();
	    }

	    toBeCalledListQuery.setSkipStatus(true);

	    AccountVO accountVO = null;
	    if (StringUtils.isNotBlank(toBeCalledListQuery.getSearchWords())) {
		    // robotCallTask没有phoneNumber的索引, phoneNumber -> customerPerson -> robotCallTaskId
		    accountVO = customerPersonService.getCustomerPersonByPhoneNumber(toBeCalledListQuery.getTenantId(), toBeCalledListQuery.getSearchWords());
		    if (accountVO == null) {
			    return 0;
		    }
		    toBeCalledListQuery.setCustomerPersonId(accountVO.getAccountId());
	    }

	    // 外呼计划去重表
	    if (robotCallJobPO != null && robotCallJobPO.getCallOutPlanId() != null) {
		    Set<Long> customerPersonIds = null;
		    if (StringUtils.isNotBlank(toBeCalledListQuery.getSearchWords())) {
			    if (accountVO != null) {
				    customerPersonIds = Collections.singleton(accountVO.getAccountId());
			    }
		    } else if (CollectionUtils.isNotEmpty(toBeCalledListQuery.getRobotCallTaskIds())) {
			    List<RobotCallTaskPO> robotCallTasks = selectListByKeyCollect(toBeCalledListQuery.getRobotCallTaskIds());
			    customerPersonIds = robotCallTasks.stream().map(RobotCallTaskPO::getCustomerPersonId).collect(Collectors.toSet());
		    } else {
		    	// 指定索引
			    List<ToBeCalledTaskVO> toBeCalledTasks = robotCallTaskPOMapper.selectToBeCalledRobotTaskList(toBeCalledListQuery);
			    customerPersonIds = toBeCalledTasks.stream().map(ToBeCalledTaskVO::getCustomerPersonId).collect(Collectors.toSet());
		    }
		    callOutPlanTaskUniqueService.deleteByJobIdCustomerIds(toBeCalledListQuery.getTenantId(), robotCallJobId, customerPersonIds);
	    }

	    int delete = deleteNotUsedToBeCalledRobotTaskList(toBeCalledListQuery);
        int update = updateUsedToBeCalledRobotTaskList(toBeCalledListQuery);
        if (delete > 0) {
            callStatsService.reduceCustomerNumber(robotCallJobId, dialogFlowId, (long) delete);
        }
        robotCallJobService.deleteCustomerPersonsToRobotCallJob(toBeCalledListQuery.getTenantId(), robotCallJobId, dialogFlowId, -(delete + update));
        return delete + update;
    }

	/**
	 * request.getRobotCallTaskIds()中的task可能属于不同的job
	 */
	@Override
	public int deleteRobotCallTaskList(PlanDeleteTaskQueryVO request) {
		Map<Long, List<ToBeCalledTaskExportDTO>> taskMap = groupByJobId(request.getTasks());
		AtomicInteger totalDelete = new AtomicInteger(0);
		AtomicInteger totalUpdate = new AtomicInteger(0);
		taskMap.forEach((robotCallJobId, tasks) -> {
			// 删除多job中的task属于外呼计划操作, 此处不删除task_unique表数据
			int delete = deleteNotUsedToBeCalledRobotTaskList2(request, robotCallJobId);
			int update = updateUsedToBeCalledRobotTaskList2(request, robotCallJobId);
			if (delete > 0) {
				Long dialogFlowId = null;
				RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
				if (Objects.nonNull(robotCallJobPO)) {
					dialogFlowId = robotCallJobPO.getDialogFlowId();
				}
				callStatsService.reduceCustomerNumber(robotCallJobId, dialogFlowId, (long) delete);
			}
			robotCallJobService.deleteCustomerPersonsToRobotCallJob(request.getTenantId(), robotCallJobId, -(delete + update));
			totalDelete.addAndGet(delete);
			totalUpdate.addAndGet(update);
		});
		return totalDelete.get() + totalUpdate.get();
	}

	@Override
    public Integer getCountToBeCalledRobotTaskList(ToBeCalledListQueryVO request) {
        return robotCallTaskPOMapper.selectCountToBeCalledRobotTaskList(request);
    }

    @Override
    public List<CallRecordExportDTO> getLastCallRecordByTask(CallRecordQueryVO request) {
        Integer start = (request.getPageNum() - 1) * request.getPageSize();
        Integer end = request.getPageSize();
        return robotCallTaskPOMapper.getLastCallRecordByTask(request, start, end);
    }

    @Override
    public Long getLastCallRecordByTaskCount(CallRecordQueryVO request){
        return robotCallTaskPOMapper.getLastCallRecordByTaskCount(request);
    }

    @Override
    public Integer getCountLastCallRecordReAddByTask(CallRecordQueryVO request) {
        Integer count;
        count = robotCallTaskPOMapper.getCountLastCallRecordReAddByTask(request);
        return count != null ? count : 0;
    }

    @Override
    public Integer deleteNotUsedToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery) {
        Integer delete = robotCallTaskPOMapper.deleteNotUsedToBeCalledRobotTaskList(toBeCalledListQuery);
        return delete != null ? delete : 0;
    }

    private Integer deleteNotUsedToBeCalledRobotTaskList2(PlanDeleteTaskQueryVO request, Long robotCallJobId) {
		if (CollectionUtils.isEmpty(request.getTasks())) {
			return 0;
		}
	    Integer delete = robotCallTaskPOMapper.deleteNotUsedToBeCalledRobotTaskList2(request, robotCallJobId);
	    return delete != null ? delete : 0;
    }

    @Override
    public Integer updateUsedToBeCalledRobotTaskList(ToBeCalledListQueryVO toBeCalledListQuery) {
        Integer delete = robotCallTaskPOMapper.updateUsedToBeCalledRobotTaskList(toBeCalledListQuery);
        return delete != null ? delete : 0;
    }

    private Integer updateUsedToBeCalledRobotTaskList2(PlanDeleteTaskQueryVO request, Long robotCallJobId) {
	    if (CollectionUtils.isEmpty(request.getTasks())) {
		    return 0;
	    }
        Integer delete = robotCallTaskPOMapper.updateUsedToBeCalledRobotTaskList2(request, robotCallJobId);
        return delete != null ? delete : 0;
    }

    @Override
    public void updateIntentLevel(Long tenantId, Long robotCallTaskId, Long callRecordId, Integer intentLevel) {
        robotCallTaskPOMapper.updateIntentLevel(tenantId, robotCallTaskId, callRecordId, intentLevel);
    }

    @Override
    public void updateProperties(Long tenantId, Long userId, Long robotCallTaskId, Map<String, String> properties, String name, GenderEnum gender, List<CreateSourceTypeEnum> createSources, LocalDate birthday) {
        // 更新子任务
        RobotCallTaskPO robotCallTaskPO = selectByKey(robotCallTaskId);
        robotCallTaskPO.getProperties().putAll(properties);
//        robotCallTaskPOMapper.updateProperties(tenantId, robotCallTaskId, JsonUtils.object2String(robotCallTaskPO.getProperties()));

        robotCallTaskPOMapper.updateTaskInfo(tenantId, robotCallTaskId, JsonUtils.object2String(robotCallTaskPO.getProperties()), name);

        // 更新客户
        if (tenantId.equals(robotCallTaskPO.getTenantId())) {
            UpdateAccountInfoRequest request = new UpdateAccountInfoRequest();
            request.setTenantId(tenantId);
            request.setAccountId(robotCallTaskPO.getCustomerPersonId());
            request.setName(name);
            if (Objects.nonNull(gender)) {
                request.setGender(com.yiwise.customer.data.platform.rpc.api.service.enums.GenderEnum.valueOf(gender.name()));
            }
            if (CollectionUtils.isNotEmpty(createSources)) {
                request.setCreateSources(createSources);
            }
            if (Objects.nonNull(birthday)) {
                request.setBirthday(birthday);
            }
            accountClient.updateAccountInfo(request);
        }
    }

    @Override
    public RobotCallTaskStatusEnum selectRobotTaskStatus(Long taskId) {
        Integer taskStatus = robotCallTaskPOMapper.selectRobotTaskStatus(taskId);
        return CodeDescEnum.getFromCodeOrThrow(RobotCallTaskStatusEnum.class, taskStatus);
    }

    @Override
    public boolean haveTaskInRunningOrCache(Long callJobId) {
        Integer integer = robotCallTaskPOMapper.countTaskInRunningOrCache(callJobId);
        return integer != null && integer > 0;
    }

    @Override
    public Set<String> getUntriedPhoneNumber(AccountVO accountVO, Long tenantId, Long robotCallTaskId, Long lastTenantPhoneNumberId) {
        // 客户有备用号码
        if (accountVO != null && CollectionUtils.isNotEmpty(accountVO.getMobile())) {
            List<CallRecordPO> callRecordPOList = callRecordInfoService.getByRobotCallTaskId(tenantId, robotCallTaskId);
            Set<String> alternatePhoneNumbersCopy = new LinkedHashSet<>(accountVO.getMobile());
            // 获取客户所有号码
            String phoneNumber = accountVO.getMainPhoneNumber();
            alternatePhoneNumbersCopy.add(phoneNumber);
            // 此方法在生成callRecord的异步方法执行之前 所以callRecord可能为空
            if (CollectionUtils.isNotEmpty(callRecordPOList)) {

                // 正在使用的线路是否重试过所有号码
                // 最近使用的线路需要计算
                Set<String> calledPhoneNumbers = callRecordPOList.stream().
                        filter(item -> item.getTenantPhoneNumberId().equals(lastTenantPhoneNumberId))
                        .map(CallRecordPO::getCalledPhoneNumber).collect(Collectors.toSet());
                alternatePhoneNumbersCopy.removeAll(calledPhoneNumbers);
            }
            // 还有用这条线路未试过的号码 进行备用号码重播
            return alternatePhoneNumbersCopy;
        }
        return null;
    }

	@Override
    public void updateImportedCustomer(RobotCallTaskMapperEnum mapper, Long robotCallJobId, Long dialogFlowId, List<Long> customerPersonIds) {
        try {
            if (robotCallJobId != null && customerPersonIds != null && customerPersonIds.size() > 0) {
                Long toBeAdd = 0L;
                int length = customerPersonIds.size();
                int index = 0;
                while (length > 100) {
                    Integer customers = getImportCustomersInRobotCallJob(mapper, robotCallJobId, customerPersonIds.subList(index * 100, (index + 1) * 100));
                    toBeAdd += Long.valueOf(customers);
                    index++;
                    length -= 100;
                }
                Integer customers = getImportCustomersInRobotCallJob(mapper, robotCallJobId, customerPersonIds.subList(index * 100, customerPersonIds.size()));
                toBeAdd += Long.valueOf(customers);
                if (toBeAdd > 0) {
                    callStatsService.updateCustomerNumber(robotCallJobId, dialogFlowId, toBeAdd);
                }
            }
            logger.info("更新导入客户数（去重）成功 robotCallJobId={}, customerPersonIds={}", robotCallJobId, customerPersonIds);
        } catch (Exception e) {
            logger.error("更新导入客户数（去重）失败 robotCallJobId={}, customerPersonIds={}", robotCallJobId, customerPersonIds, e);
        }
    }

    @Override
    public void updateCallRecordId(Long taskId, Long callRecordId) {
        robotCallTaskPOMapper.updateCallRecordId(taskId, callRecordId);
    }

	@Override
	public void updateAddWechatFriend(Long robotCallTaskId, WechatCpAddFriendPO wechatCpAddFriendPO) {
		robotCallTaskPOMapper.updateAddWechatFriend(robotCallTaskId, wechatCpAddFriendPO.getFromwxName(),
				wechatCpAddFriendPO.getStatus(), wechatCpAddFriendPO.getResult(), wechatCpAddFriendPO.getCreateTime(), wechatCpAddFriendPO.getAdoptTime());
	}

	private Integer getImportCustomersInRobotCallJob(RobotCallTaskMapperEnum mapper, Long robotCallJobId, List<Long> customerPersonIds) {
		if (mapper == null) {
			return 0;
		}
		if (robotCallJobId == null) {
			return 0;
		}
		if (CollectionUtils.isEmpty(customerPersonIds)) {
			return 0;
		}
		switch (mapper) {
			case IMPORT_CUSTOMER_PERSONS_MAPPER:
				return importCustomerPersonsMapper.getImportCustomersInRobotCallJob(robotCallJobId, customerPersonIds);
			case ROBOT_CALL_TASK_PO_MAPPER:
			default:
				return robotCallTaskPOMapper.getImportCustomersInRobotCallJob(robotCallJobId, customerPersonIds);
		}
    }
    @Override
    public void refreshLastImportTime(Long robotCallTaskIdStart, Long robotCallTaskIdEnd, Integer batchSize) {
        int loopTime = 0;
        long index = robotCallTaskIdStart;
        do {
            robotCallTaskPOMapper.refreshLastImportTime(index, Math.min(index + batchSize, robotCallTaskIdEnd));
            loopTime++;
            if (loopTime % 100 == 0) {
                logger.info("refreshLastImportTime, index={}", index);
            }
            index += batchSize;
        } while (index <= robotCallTaskIdEnd);
    }

    @Override
    public void getInfo(List<? extends ToBeCalledTaskVO> toBeCalledTaskList) {
        if (CollectionUtils.isNotEmpty(toBeCalledTaskList)) {
            Set<Long> collect = toBeCalledTaskList.stream().map(ToBeCalledTaskVO::getRobotCallTaskId).collect(Collectors.toSet());
            Map<? extends Serializable, RobotCallTaskPO> robotCallTaskPOMap = selectMapByKeyCollect(collect);
            toBeCalledTaskList.forEach(item -> {
                RobotCallTaskPO robotCallTaskPO = robotCallTaskPOMap.get(item.getRobotCallTaskId());
                if (robotCallTaskPO != null) {
                    BeanUtils.copyProperties(robotCallTaskPO, item);
                }
            });
        }
    }

    private void deleteCallOutPlanTaskUnique(Long tenantId, Long robotCallJobId, Long customerPersonId) {
	    callOutPlanTaskUniqueService.deleteByJobIdCustomerId(tenantId, robotCallJobId, customerPersonId);
    }

    @Override
    public JobStartResultVO deleteRobotTaskList(ToBeCalledListQueryVO toBeCalledListQueryVO) {
        toBeCalledListQueryVO.setSearchWords(toBeCalledListQueryVO.getCalledPhoneNumber());

        toBeCalledListQueryVO.setSkipStatus(true);
        Long robotCallJobId = toBeCalledListQueryVO.getRobotCallJobId();
        Long tenantId = toBeCalledListQueryVO.getTenantId();

        Assert.notNull(robotCallJobId, "任务id不能为空");
        Assert.notNull(tenantId, "租户id不能为空");

        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
        LocalDateTime robotCallTaskRedialMinTime = getRobotCallTaskRedialMinTime(robotCallJobId);
        toBeCalledListQueryVO.setTaskRedialMinTime(robotCallTaskRedialMinTime);

        List<String> headerList = EXPORT_TO_BE_CALLED_TASK;
        SystemEnum systemEnum;
        if(null != toBeCalledListQueryVO.getSystemType()){
            systemEnum = toBeCalledListQueryVO.getSystemType();
        } else {
            systemEnum = SystemEnum.CRM;
        }

        Long now = System.currentTimeMillis();
        String resultFileOssKey = OssKeyCenter.getExcelJobNameOssFileKey(ROBOT_CALL_TASK_DELETE_FILE, tenantId, robotCallJobPO.getName(), now.toString());

        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerList));
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", toBeCalledListQueryVO.getUserId());
        jobParametersBuilder.addString("SYSTEM_TYPE", systemEnum.name());
        jobParametersBuilder.addString("OSS_FILE_KEY", resultFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(resultFileOssKey));
        String toBeCalledListQueryVOString = JsonUtils.object2String(toBeCalledListQueryVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", toBeCalledListQueryVOString);
        jobParametersBuilder.addString("IS_WPH", WeiPinHuiHelper.isWeipinhuiTenant(tenantId) + "");

        JobParameters jobParameters = jobParametersBuilder.toJobParameters();
        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.DELETE_ROBOT_CALL_TASK;
        String jobName = ROBOT_CALL_TASK_DELETE_FILE + robotCallJobPO.getName();
        int totalCount = toBeCalledListQueryVO.getRobotCallTaskIds() != null ? toBeCalledListQueryVO.getRobotCallTaskIds().size() : 0;
        if (totalCount == 0){
            totalCount = robotCallTaskPOMapper.countTaskNotStart(robotCallJobId);
        }
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        robotCallTaskDeleteJob,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        toBeCalledListQueryVO.getUserId(),
                        jobType,
                        systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

	public void incrementRobotConcurrencyCache(Long tenantId, Long robotCallJobId, int delta) {
		if (tenantId == null || robotCallJobId == null || delta == 0) {
			return;
		}
		try {
			String key = RedisKeyCenter.tenantConcurrencyNow(tenantId);
			// noinspection unchecked
			redisOpsService.getRedisTemplate().opsForHash().increment(key, robotCallJobId, delta);
			redisOpsService.expire(key, 5, TimeUnit.MINUTES);
		} catch (Exception e) {
			logger.error("增加外呼任务实时并发数出错", e);
		}
	}

    @Override
    public void updateInterceptStatus(Long taskId, InterceptStatusEnum interceptStatusEnum) {

        robotCallTaskPOMapper.updateInterceptStatus(taskId, interceptStatusEnum);
    }
    @Override
    public List<RobotCallTaskPO> getByIds(List<Long> taskIdList) {
        if(CollectionUtils.isEmpty(taskIdList)){
            return new ArrayList<>();
        }
        return robotCallTaskPOMapper.getByIds(taskIdList);
    }

	private void decrementTenantConcurrency(Long tenantId, Long robotCallJobId, int delta) {
		if (tenantId == null || robotCallJobId == null || delta == 0) {
			return;
		}
		try {
			String key = RedisKeyCenter.tenantConcurrencyNow(tenantId);
			// noinspection unchecked
			redisOpsService.getRedisTemplate().opsForHash().increment(key, robotCallJobId, -delta);
			redisOpsService.expire(key, 5, TimeUnit.MINUTES);
		} catch (Exception e) {
			logger.error("降低外呼任务实时并发数出错", e);
		}
	}

	private Map<Long, List<ToBeCalledTaskExportDTO>> groupByJobId(Collection<? extends ToBeCalledTaskExportDTO> tasks) {
		Map<Long, List<ToBeCalledTaskExportDTO>> map = new HashMap<>();
	    for (ToBeCalledTaskExportDTO task : tasks) {
		    List<ToBeCalledTaskExportDTO> list = map.computeIfAbsent(task.getRobotCallJobId(), k -> new ArrayList<>());
		    list.add(task);
	    }
	    return map;
	}

	/**
	 * 查询外呼任务是否属于外呼计划, 所属外呼计划是否开启计划内去重, 根据传入的去重条件进行处理
	 */
	private boolean checkDistinctInPlan(RobotCallJobPO robotCallJob, Boolean requestDistinctInPlan) {
		if (robotCallJob.getCallOutPlanId() == null) {
			logger.debug("该外呼任务不属于外呼计划, 不进行外呼计划去重");
			return false;
		}
		CallOutPlanPO callOutPlan = callOutPlanService.selectByKey(robotCallJob.getCallOutPlanId());
		if (callOutPlan == null) {
			logger.debug("[LogHub_Warn]该外呼任务所属外呼计划未找到, 不进行外呼计划去重, callOutPlanId={}", robotCallJob.getCallOutPlanId());
			return false;
		}
		if (requestDistinctInPlan == null) {
			logger.info("导入请求未指定是否在外呼计划内去重, 返回{}", callOutPlan.getDistinctInPlan());
			return callOutPlan.getDistinctInPlan();
		}
		if (requestDistinctInPlan) {
			if (callOutPlan.getDistinctInPlan()) {
				logger.info("导入请求在外呼计划内去重");
				return true;
			} else {
				throw new ComException(ComErrorCode.NOT_SUPPORT, "不允许计划内去重");
			}
		} else {
			if (callOutPlan.getDistinctInPlan()) {
				logger.info("导入请求不在外呼计划内去重, 关闭该外呼计划的计划内去重功能");
				planImportCustomerCommonService.disableDistinctInPlan(robotCallJob.getTenantId(), robotCallJob.getCallOutPlanId());
			} else {
				logger.info("导入请求不在外呼计划内去重, 且该外呼计划已不支持计划内去重");
			}
			return false;
		}
	}
}
