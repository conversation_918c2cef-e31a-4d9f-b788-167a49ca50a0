package com.yiwise.core.service.assistant;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.PhoneNumberUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.dal.entity.QcJobRecordV2PO;
import com.yiwise.core.dal.entity.QcJobV2PO;
import com.yiwise.core.dal.entity.QcRuleTagPO;
import com.yiwise.core.helper.YiwiseAskHelper;
import com.yiwise.core.model.bo.Rule;
import com.yiwise.core.model.bo.algorithm.PredictRequest;
import com.yiwise.core.model.bo.algorithm.PredictResponse;
import com.yiwise.core.model.bo.algorithm.IntentBO;
import com.yiwise.core.model.bo.qc.QcRuleGroupBO;
import com.yiwise.core.model.dto.qc.QcJobRecordV2DTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.qc.QcTypeEnum;
import com.yiwise.core.model.enums.train.AlgorithmTrainTypeEnum;
import com.yiwise.core.model.enums.train.SnapshotTypeEnum;
import com.yiwise.core.model.vo.qc.v2.ItemTriggerV2VO;
import com.yiwise.core.model.vo.qc.v2.QcJobRecordDetailV2VO;
import com.yiwise.core.model.vo.qc.v2.QcRuleTagVO;
import com.yiwise.core.model.vo.qc.v2.QcRuleV2VO;
import com.yiwise.core.service.engine.qc.QcRuleTagService;
import com.yiwise.core.service.engine.qc.QcRuleTagServiceImpl;
import com.yiwise.core.service.engine.qc.QcRuleV2Service;
import com.yiwise.core.util.CustomerInfoRecognitionUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: wangguomin
 * @Date: 2019-11-24 14:24
 */
public class QcMatchV2 {

    private static final Logger logger = LoggerFactory.getLogger(QcMatchV2.class);

    /**
     * 数据库存储的任务规则
     */
    protected List<QcRuleV2VO> ruleVOList;

    protected QcJobV2PO qcJobPO;

    public Integer sumScore = 0;

    protected List<QcJobRecordDetailV2VO> detailList;

    protected QcJobRecordStatusEnum qcJobRecordStatusEnum = QcJobRecordStatusEnum.FAIL;

    protected String failReason;

    protected List<ItemTriggerV2VO> triggerList = new ArrayList<>();

    protected Long chatDuration = 0L;

    private Map<Long, QcRuleV2VO> ruleMap;

    protected Integer speed = 0;

    protected Integer muteRate = 0;

    protected Integer muteDuration = 0;

    protected Long voiceVolume = 0L;

    protected CustomerEmotionEnum csEmotion = CustomerEmotionEnum.UNKNOWN;

    protected CustomerEmotionEnum personEmotion = CustomerEmotionEnum.UNKNOWN;

    protected Integer chatRound = 0;

    protected Long csVolume = 0L;

    protected Long personVolume = 0L;

    protected Map<Long, Long> tagMatchCount = new HashMap<>();

    private static final QcRuleTagService qcRuleTagService = AppContextUtils.getBean(QcRuleTagServiceImpl.class);
    private static final QcRuleV2Service qcRuleV2Service = AppContextUtils.getBean(QcRuleV2Service.class);

    Map<Long, List<ItemTriggerV2VO>> ruleTriggerMap = new HashMap<>();

    private TagMatchResult tagMatchResult = null;

    protected Double qcTagThreshold;

    protected Boolean enableQcTagThreshold = false;

    protected Integer shouxiangyingshichang = 0;

    protected Boolean dontCheckShouxiangyingshichang = false;

    protected Map<String, Pattern> tagPatternMap = new HashMap<>();

    //这个分数作为备份，如果所有规则匹配后，发现不算分那就在重置回去
    private Integer backSumSore = 0;

    public QcJobRecordV2PO qcJobRecordV2PO;

    private static final Pattern pattern = Pattern.compile("(<.+?>)|(</.+?>)");

    public QcMatchV2(QcJobV2PO qcJobPO, List<QcRuleV2VO> ruleVOList, List<QcJobRecordDetailV2VO> detailList) {
        this.qcJobPO = qcJobPO;
        this.ruleVOList = ruleVOList;
        this.detailList = detailList;
        generateRuleMap();
    }

    private void buildDetailInfo(List<QcJobRecordDetailV2VO> detailList) {
        if (this.detailList != null) {
            int count = 0;
            for (QcJobRecordDetailV2VO item : detailList) {
                item.setIndex(count);
                count++;
            }
        }
    }

    public void doMatch() {
        buildDetailInfo(this.detailList);
        if (qcJobPO.getQcType() == QcTypeEnum.VOICE) {
            preBuild();
        } else {

            Integer csFirstOffset = null;
            Integer customerOffSet = null;
            for (int i = 0; i < detailList.size(); i++) {
                QcJobRecordDetailV2VO detailVO = detailList.get(i);
                if (detailVO.getType() == CharacterEnum.CUSTOMER_SERVICE) {
                    chatRound++;
                    if (csFirstOffset == null) {
                        //设置客服首次说话的节点时间
                        csFirstOffset = detailVO.getStartOffset();
                        if (i == 0) {
                            //说明是第一句话,不需要检测首响应时长
                            dontCheckShouxiangyingshichang = true;
                        }
                    }
                    //判断前一句话是否是客户说的
                    detailVO.setMuteDuration(0);
                    detailVO.setResDuration(0);
                    if (i > 0 && detailList.get(i - 1).getType() == CharacterEnum.PERSON) {
                        QcJobRecordDetailV2VO person = detailList.get(i - 1);
                        detailVO.setResDuration(Long.valueOf(detailVO.getStartOffset() - person.getEndOffset()).intValue());
                    }
                } else {
                    if (customerOffSet == null) {
                        //设置客服首次说话的节点时间
                        customerOffSet = detailVO.getEndOffset();
                    }
                }
            }
            //如果小于零，那就说明是客服先说的
            if (dontCheckShouxiangyingshichang) {
                this.shouxiangyingshichang = -99;
            } else {
                this.shouxiangyingshichang = ((csFirstOffset == null ? 0 : csFirstOffset) - (customerOffSet == null ? 0 : customerOffSet)) / 1000;
            }
        }
        logger.info("detail pre :" + JSONObject.toJSONString(detailList));
        for (QcRuleV2VO ruleV2VO : ruleVOList) {
            //Rule rule = buildRule(ruleV2VO);
            //备份每个规则处理后的分数
            logger.info("分数计算backSumScore:" + this.backSumSore + ";sumScore:" + this.sumScore);
            backSumSore = this.sumScore;
            List<QcRuleGroupBO> groupListBO = buildRuleGroup(ruleV2VO);
            Boolean result = null;
            for (QcRuleGroupBO groupBO : groupListBO) {
                Rule rule = groupBO.getContent();
                logger.info("ruleInfo:" + JSONObject.toJSONString(rule));
                Boolean ruleMatch = eachMatch(rule);
                logger.info("ruleMatch:" + ruleMatch);
                if ("true".equalsIgnoreCase(groupBO.getNot())) {
                    //false 符合，true不符合
                    ruleMatch = !ruleMatch;
                }
                if (result == null) {
                    result = ruleMatch;
                } else {
                    if ("or".equalsIgnoreCase(groupBO.getOption())) {
                        //或
                        result = (result == null) ? ruleMatch : (result || ruleMatch);
                    } else {
                        //且
                        result = (result == null) ? ruleMatch : (result && ruleMatch);
                    }
                }
            }
            if (result != null && !result) {
                //说明上面的规则组合匹配结果为未命中，则还原分数
                this.sumScore = backSumSore;
                ruleTriggerMap.remove(ruleV2VO.getQcRuleId());
                logger.info("没匹配到:" + this.sumScore + "：" + ruleV2VO.getQcRuleId());
            } else {
                //这里需要注意的是eachMatch 里面进行了分数计算，所以如果是每通的话，还是会进行了多次计算分
                //要重新计算分数
                if (QcRuleRateEnum.UNIT_CALL.equals(ruleV2VO.getRuleRate())) {
                    logger.info("分数前:" + this.sumScore);
                    this.sumScore = backSumSore + ruleV2VO.getRuleScore() * ruleV2VO.getRuleWeight();
                    logger.info("分数后:" + this.sumScore);
                    if (!ruleTriggerMap.containsKey(ruleV2VO.getQcRuleId())) {
                        List<ItemTriggerV2VO> triggerV2VOS = new ArrayList<>();
                        Rule rule = buildRule(ruleV2VO);
                        ItemTriggerV2VO vo = new ItemTriggerV2VO();
                        BeanUtils.copyProperties(rule, vo);
                        vo.setRuleId(vo.getRuleId());
                        vo.setRuleName(vo.getRuleName());
                        vo.setTriggerCount(1);
                        vo.setScore(ruleV2VO.getRuleScore() * ruleV2VO.getRuleWeight());
                        triggerV2VOS.add(vo);
                        ruleTriggerMap.put(ruleV2VO.getQcRuleId(), triggerV2VOS);
                    }
                }
            }
            logger.info("triggerList:" + JSONObject.toJSON(ruleTriggerMap.get(ruleV2VO.getQcRuleId())));
        }
        this.ruleTriggerMap.values().forEach(item -> {
            this.triggerList.addAll(item);
        });
        logger.info("AlltriggerList:" + JSONObject.toJSON(this.triggerList));
        logger.info("score: " + this.sumScore);
        QcJobRecordV2DTO qcJobRecordV2DTO = QcJobRecordV2DTO.parseQcJob(detailList, ruleMap, qcJobPO);
        String jobRecordEntity = JsonUtils.object2String(qcJobRecordV2DTO);
        logger.info("jobRecordEntity:" + jobRecordEntity);
        try {
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(ApplicationConstant.QC_WORD_FREQUENCY_UPDATE);
            StringEntity entity = new StringEntity(jobRecordEntity, "UTF-8");
            httpPost.setEntity(entity);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-type", "application/json");
            CloseableHttpResponse response = client.execute(httpPost);
            client.close();
        } catch (Exception e) {
            logger.error("传输质检详情失败");
        }
        if (qcJobPO.getQcType() == QcTypeEnum.VOICE) {
            int count = 0;
            List<String> csList = new ArrayList<>();
            List<String> personList = new ArrayList<>();
            for (QcJobRecordDetailV2VO item : detailList) {
                if (CharacterEnum.CUSTOMER_SERVICE.equals(item.getType())) {
                    chatRound++;
                    csList.add(item.getText());
                    this.voiceVolume += item.getVoiceValue();
                    count++;
                    if (item.getEmotion() != null) {
                        if (CustomerEmotionEnum.ANGRY.equals(item.getEmotion())) {
                            csEmotion = CustomerEmotionEnum.ANGRY;
                        }
                        if (!CustomerEmotionEnum.ANGRY.equals(csEmotion)) {
                            csEmotion = item.getEmotion();
                        }
                    }
                }
                if (CharacterEnum.PERSON.equals(item.getType())) {
                    personList.add(item.getText());
                    if (item.getEmotion() != null) {
                        if (CustomerEmotionEnum.ANGRY.equals(item.getEmotion())) {
                            personEmotion = CustomerEmotionEnum.ANGRY;
                        }
                        if (!CustomerEmotionEnum.ANGRY.equals(personEmotion)) {
                            personEmotion = item.getEmotion();
                        }
                    }
                }
            }
            if (CustomerEmotionEnum.UNKNOWN.equals(csEmotion)) {
                csEmotion = CustomerInfoRecognitionUtil.getEmotion(csList);
            }
            if (!CustomerEmotionEnum.UNKNOWN.equals(personEmotion)) {
                csEmotion = CustomerInfoRecognitionUtil.getEmotion(personList);
            }
            this.voiceVolume = count == 0 ? 0L : this.voiceVolume / count;
        }
        this.qcJobRecordStatusEnum = QcJobRecordStatusEnum.COMPLETED;
    }

    private void addRuleTriggerMap(Rule rule, ItemTriggerV2VO triggerV2VO) {
        triggerV2VO.setAddress(rule.getAddress());
        triggerV2VO.setMethod(rule.getMethod());
        triggerV2VO.setSession_address(rule.getSession_address());
        triggerV2VO.setSession_role(rule.getSession_role());
        triggerV2VO.setSentence_sequence(rule.getSentence_sequence());
        triggerV2VO.setRole(rule.getRole());
        triggerV2VO.setNot(rule.getNot());
        triggerV2VO.setParams(triggerV2VO.getParams());
        List<ItemTriggerV2VO> list = ruleTriggerMap.get(rule.getRuleId());
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(triggerV2VO);
        ruleTriggerMap.put(rule.getRuleId(), list);
    }

    private void subDetailList(Rule item) {
        if ("appoint".equalsIgnoreCase(item.getAddress()) && ("start".equalsIgnoreCase(item.getSession_address()) || "end".equalsIgnoreCase(item.getSession_address()))) {
            //指定位置的话需要重新裁剪一部分
            Integer csCount = 0;
            Integer customerCount = 0;
            for (QcJobRecordDetailV2VO detailV2VO : detailList) {
                if (CharacterEnum.CUSTOMER_SERVICE.equals(detailV2VO.getType())) {
                    csCount++;
                } else {
                    customerCount++;
                }
            }
            Integer from = 1;
            Integer to = 1;

            Integer index = 0;
            if ("start".equalsIgnoreCase(item.getSession_address())) {
                from = 1;
                for (QcJobRecordDetailV2VO detailV2VO : detailList) {
                    if ("PERSON".equalsIgnoreCase(item.getSession_role())) {
                        //客户
                        if (CharacterEnum.PERSON.equals(detailV2VO.getType())) {
                            index++;
                            if (index >= item.getSentence_sequence()) {
                                break;
                            }
                        }
                    } else {
                        //客服
                        if (CharacterEnum.CUSTOMER_SERVICE.equals(detailV2VO.getType())) {
                            index++;
                            if (index >= item.getSentence_sequence()) {
                                break;
                            }
                        }
                    }
                    to++;
                }
            } else {
                to = csCount + customerCount;
                for (QcJobRecordDetailV2VO detailV2VO : detailList) {
                    if ("PERSON".equalsIgnoreCase(item.getSession_role())) {
                        //客户
                        if (CharacterEnum.PERSON.equals(detailV2VO.getType())) {
                            index++;
                            if (index > (customerCount - item.getSentence_sequence())) {
                                break;
                            }
                        }
                    } else {
                        //客服
                        if (CharacterEnum.CUSTOMER_SERVICE.equals(detailV2VO.getType())) {
                            index++;
                            if (index > (csCount - item.getSentence_sequence())) {
                                break;
                            }
                        }
                    }
                    from++;
                }
            }

            logger.info("from:" + from + ";to:" + to);
            item.setFromIndex((from - 1) > detailList.size() ? (detailList.size() - 1) : (from - 1));
            item.setToIndex(to - 1);
        } else if ("appoint".equalsIgnoreCase(item.getAddress()) && "center".equalsIgnoreCase(item.getSession_address())) {
            //会话中从第几句到第几句
            Integer from = 1;
            Integer to = 1;
            Integer index = 1;
            Integer itemIndex = 1;
            for (QcJobRecordDetailV2VO detailV2VO : detailList) {
                itemIndex++;
                if ("PERSON".equalsIgnoreCase(item.getSession_role())) {
                    //客户
                    if (CharacterEnum.PERSON.equals(detailV2VO.getType())) {
                        index++;
                        if (index < item.getSentence_sequence() && index > item.getSentence_sequence_end()) {
                            break;
                        }
                        if (index.equals(item.getSentence_sequence())) {
                            from = itemIndex;
                        }
                        if (index.equals(item.getSentence_sequence_end())) {
                            to = itemIndex;
                        }
                    }
                } else {
                    //客服
                    if (CharacterEnum.CUSTOMER_SERVICE.equals(detailV2VO.getType())) {
                        index++;
                        if (index < item.getSentence_sequence() && index > item.getSentence_sequence_end()) {
                            break;
                        }
                        if (index.equals(item.getSentence_sequence())) {
                            from = itemIndex;
                        }
                        if (index.equals(item.getSentence_sequence_end())) {
                            to = itemIndex;
                        }
                    }
                }
            }
            logger.info("from:" + from + ";to:" + to);
            item.setFromIndex((from - 1) > detailList.size() ? (detailList.size() - 1) : (from - 1));
            item.setToIndex(to - 1);
        }

    }

    private void resetRuleInex(Rule rule) {
        rule.setFromIndex(null);
        rule.setToIndex(null);
    }

    private Boolean eachMatch(Rule rule) {
        Boolean ruleMatch = false;
        if (QcRuleRateEnum.ONCE.equals(rule.getRuleRate())) {
            //每次，任一
            if ("or".equalsIgnoreCase(rule.getType())) {
                Boolean matchFlag = false;
                for (Rule item : rule.getRuleList()) {
                    subDetailList(item);
                    switch (item.getMethod()) {
                        case "keywordsMatch":
                            List<String> keywordsList = item.getParams();
                            ItemTriggerV2VO triggerV2VO1 = keywordsMatchAllDetail(item, rule.getRuleId(), item.getRole(), keywordsList, rule.getRuleName());
                            if (triggerV2VO1 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO1);
                                triggerV2VO1.setRuleScore(rule.getRuleScore());
                                triggerV2VO1.setRuleName(rule.getRuleName());
                                triggerV2VO1.setRuleRate(rule.getRuleRate());
                                triggerV2VO1.setRuleType(rule.getType());
                                triggerV2VO1.setScore(rule.getRuleScore() * triggerV2VO1.getTriggerCount());
                                addRuleTriggerMap(item, triggerV2VO1);
                                logger.info("keyWordsMatch分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore() + ";triggerCount:" + triggerV2VO1.getTriggerCount());
                                this.sumScore += rule.getRuleScore() * triggerV2VO1.getTriggerCount();
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "businessMatch":
                            List<String> businessList = item.getParams();
                            ItemTriggerV2VO triggerV2VO2 = businessMatchAllDetail(item, rule.getRuleId(), item.getRole(), businessList, rule.getRuleName());
                            if (triggerV2VO2 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO2);
                                triggerV2VO2.setRuleScore(rule.getRuleScore());
                                triggerV2VO2.setRuleName(rule.getRuleName());
                                triggerV2VO2.setRuleRate(rule.getRuleRate());
                                triggerV2VO2.setRuleType(rule.getType());
                                triggerV2VO2.setScore(rule.getRuleScore() * triggerV2VO2.getTriggerCount());
                                addRuleTriggerMap(item, triggerV2VO2);
                                logger.info("business分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore() + ";triggerCount:" + triggerV2VO2.getTriggerCount());
                                this.sumScore += rule.getRuleScore() * triggerV2VO2.getTriggerCount();
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "tagMatch":
                            List<String> tagList = item.getParams();
                            ItemTriggerV2VO triggerV2VO3 = tagMatchAllDetail(item, rule.getRuleId(), item.getRole(), true, tagList, rule.getRuleName());
                            if (triggerV2VO3 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO3);
                                triggerV2VO3.setRuleScore(rule.getRuleScore());
                                triggerV2VO3.setRuleName(rule.getRuleName());
                                triggerV2VO3.setRuleRate(rule.getRuleRate());
                                triggerV2VO3.setRuleType(rule.getType());
                                triggerV2VO3.setScore(rule.getRuleScore() * triggerV2VO3.getTriggerCount());
                                addRuleTriggerMap(item, triggerV2VO3);
                                logger.info("tagMatch分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore() + ";triggerCount:" + triggerV2VO3.getTriggerCount());
                                this.sumScore += rule.getRuleScore() * triggerV2VO3.getTriggerCount();
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "features":
                            //会话特性
                            item.setRuleId(item.getRuleId());
                            ItemTriggerV2VO triggerV2VO4 = featuresMatch(item);
                            if (triggerV2VO4 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO4);
                                triggerV2VO4.setRuleScore(rule.getRuleScore());
                                triggerV2VO4.setRuleName(rule.getRuleName());
                                triggerV2VO4.setRuleRate(rule.getRuleRate());
                                triggerV2VO4.setRuleType(rule.getType());
                                triggerV2VO4.setScore(rule.getRuleScore() * triggerV2VO4.getTriggerCount());
                                addRuleTriggerMap(item, triggerV2VO4);
                                logger.info("features分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore() + ";triggerCount:" + triggerV2VO4.getTriggerCount());
                                this.sumScore += rule.getRuleScore() * triggerV2VO4.getTriggerCount();
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        default:
                            break;
                    }
                    resetRuleInex(rule);
                }
                if (matchFlag) {
                    //此规则匹配
                }
            }
        }
        if (QcRuleRateEnum.UNIT_CALL.equals(rule.getRuleRate())) {
            //每通，任一/全部
            if ("or".equalsIgnoreCase(rule.getType())) {
                //每通，任一
                Boolean matchFlag = false;
                ItemTriggerV2VO triggerV2VO = null;
                for (Rule item : rule.getRuleList()) {
                    subDetailList(item);
                    switch (item.getMethod()) {
                        case "keywordsMatch":
                            List<String> keywordsList = item.getParams();
                            triggerV2VO = keywordsMatchAllDetailAndAllDialog(item, rule.getRuleId(), item.getRole(), item.getNot(), keywordsList, rule.getRuleName());
                            if (triggerV2VO != null) {
                                BeanUtils.copyProperties(item, triggerV2VO);
                                triggerV2VO.setScore(rule.getRuleScore());
                                triggerV2VO.setRuleScore(rule.getRuleScore());
                                triggerV2VO.setRuleName(rule.getRuleName());
                                triggerV2VO.setRuleRate(rule.getRuleRate());
                                triggerV2VO.setRuleType(rule.getType());
                                addRuleTriggerMap(item, triggerV2VO);
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "businessMatch":
                            List<String> businessList = item.getParams();
                            triggerV2VO = businessMatchAllDetailWithCount(item, rule.getRuleId(), item.getRole(), item.getNot(), businessList, rule.getRuleName());
                            if (triggerV2VO != null) {
                                BeanUtils.copyProperties(item, triggerV2VO);
                                triggerV2VO.setScore(rule.getRuleScore());
                                triggerV2VO.setRuleScore(rule.getRuleScore());
                                triggerV2VO.setRuleName(rule.getRuleName());
                                triggerV2VO.setRuleRate(rule.getRuleRate());
                                triggerV2VO.setRuleType(rule.getType());
                                addRuleTriggerMap(item, triggerV2VO);
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "tagMatch":
                            List<String> tagList = item.getParams();
                            ItemTriggerV2VO triggerV2VO3 = tagMatchAllDetail(item, rule.getRuleId(), item.getRole(), item.getNot(), tagList, rule.getRuleName());
                            if (triggerV2VO3 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO3);
                                triggerV2VO3.setScore(rule.getRuleScore());
                                triggerV2VO3.setRuleScore(rule.getRuleScore());
                                triggerV2VO3.setRuleName(rule.getRuleName());
                                triggerV2VO3.setRuleRate(rule.getRuleRate());
                                triggerV2VO3.setRuleType(rule.getType());
                                addRuleTriggerMap(item, triggerV2VO3);
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        case "features":
                            //会话特性
                            item.setRuleId(item.getRuleId());
                            ItemTriggerV2VO triggerV2VO4 = featuresMatch(item);
                            if (triggerV2VO4 != null) {
                                BeanUtils.copyProperties(item, triggerV2VO4);
                                triggerV2VO4.setRuleScore(rule.getRuleScore());
                                triggerV2VO4.setRuleName(rule.getRuleName());
                                triggerV2VO4.setRuleRate(rule.getRuleRate());
                                triggerV2VO4.setRuleType(rule.getType());
                                triggerV2VO4.setScore(rule.getRuleScore());
                                addRuleTriggerMap(item, triggerV2VO4);
                                matchFlag = true;
                                ruleMatch = true;
                            }
                            break;
                        default:
                            break;
                    }
                    resetRuleInex(rule);
                }
                if (matchFlag) {
                    //每通，任一则加分
                    logger.info("每通分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore());
                    this.sumScore += rule.getRuleScore();
                }
            }

            if ("and".equalsIgnoreCase(rule.getType())) {
                //每通，全部
                List<Rule> ruleList = rule.getRuleList();
                Map<String, List<Rule>> addressMap = new HashMap<>();
                for (Rule item : ruleList) {
                    List<Rule> itemList = addressMap.get(item.getAddress());
                    if (itemList != null) {
                        itemList.add(item);
                    } else {
                        itemList = new ArrayList<>();
                        itemList.add(item);
                    }
                    addressMap.put(item.getAddress(), itemList);
                }
                List<ItemTriggerV2VO> allTrigger = new ArrayList<>();
                Map<Integer, ItemTriggerV2VO> map = new HashMap<>();
                Boolean dialogMatchFlag = true;
                if (addressMap.get("features") != null) {
                    //整个对话过程中
                    ItemTriggerV2VO triggerV2VO = null;
                    for (Rule item : addressMap.get("features")) {
                        switch (item.getMethod()) {
                            case "features":
                                //会话特性
                                item.setRuleId(item.getRuleId());
                                triggerV2VO = featuresMatch(item);
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                        }
                    }
                }

                if (addressMap.get("all") != null) {
                    //整个对话过程中
                    ItemTriggerV2VO triggerV2VO = null;
                    for (Rule item : addressMap.get("all")) {
                        subDetailList(item);
                        switch (item.getMethod()) {
                            case "keywordsMatch":
                                List<String> keywordsList = item.getParams();
                                triggerV2VO = keywordsMatchAllDetailAndAllDialog(item, rule.getRuleId(), item.getRole(), item.getNot(), keywordsList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            case "businessMatch":
                                List<String> businessList = item.getParams();
                                triggerV2VO = businessMatchAllDetailWithCount(item, rule.getRuleId(), item.getRole(), item.getNot(), businessList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            case "tagMatch":
                                List<String> tagList = item.getParams();
                                triggerV2VO = tagMatchAllDetailWithCount(item, rule.getRuleId(), item.getRole(), item.getNot(), tagList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            default:
                                break;
                        }
                        resetRuleInex(rule);
                    }
                }

                if (addressMap.get("appoint") != null) {
                    //指定对话过程
                    ItemTriggerV2VO triggerV2VO = null;
                    for (Rule item : addressMap.get("appoint")) {
                        subDetailList(item);
                        switch (item.getMethod()) {
                            case "keywordsMatch":
                                List<String> keywordsList = item.getParams();
                                triggerV2VO = keywordsMatchAllDetailAndAllDialog(item, rule.getRuleId(), item.getRole(), item.getNot(), keywordsList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            case "businessMatch":
                                List<String> businessList = item.getParams();
                                triggerV2VO = businessMatchAllDetailWithCount(item, rule.getRuleId(), item.getRole(), item.getNot(), businessList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            case "tagMatch":
                                List<String> tagList = item.getParams();
                                triggerV2VO = tagMatchAllDetailWithCount(item, rule.getRuleId(), item.getRole(), item.getNot(), tagList, rule.getRuleName());
                                if (triggerV2VO != null) {
                                    BeanUtils.copyProperties(item, triggerV2VO);
                                    triggerV2VO.setScore(rule.getRuleScore());
                                    allTrigger.add(triggerV2VO);
                                    map.put(item.getRuleItemId(), triggerV2VO);
                                } else {
                                    dialogMatchFlag = false;
                                }
                                break;
                            default:
                                break;
                        }
                        resetRuleInex(rule);
                    }
                }

                Integer curItemId = null;
                Boolean addressMatchFlag = false;
                List<ItemTriggerV2VO> curList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(addressMap.get("current"))) {
                    //找到当前位置
                    Rule currentRule = addressMap.get("current").get(0);
                    //寻找位置
                    List<Long> addressIdList = new ArrayList<>();
                    List<Integer> indexList = new ArrayList<>();
                    Map<Integer, ItemTriggerV2VO> curTrrMap = new HashMap<>();
                    for (int i = 0; i < detailList.size(); i++) {
                        QcJobRecordDetailV2VO item = detailList.get(i);
                        if (!item.getType().name().equalsIgnoreCase(currentRule.getRole())) {
                            continue;
                        }
                        ItemTriggerV2VO itemTriggerV2VO = checkMatch(rule.getRuleId(), currentRule, item);
                        if (itemTriggerV2VO != null) {
                            curTrrMap.put(i, itemTriggerV2VO);
                            addressIdList.add(item.getQcJobRecordDetailId());
                            indexList.add(i);
                            logger.info("current:itemTriggerV2VO:" + JSONObject.toJSONString(itemTriggerV2VO));
                        }
                    }
                    if (indexList.size() > 0) {
                        curItemId = currentRule.getRuleItemId();
                    }
                    for (Integer index : indexList) {
                        List<Rule> onList = addressMap.get("on");
                        if (CollectionUtils.isNotEmpty(onList)) {
                            if (index == 0) {
                                continue;
                            }
                            List<QcJobRecordDetailV2VO> preList = detailList.subList(index, index + 1);
                            if (CollectionUtils.isNotEmpty(preList)) {
                                addressMatchFlag = false;
                                for (Rule onRule : onList) {
                                    ItemTriggerV2VO item = checkWithDeatilList(rule.getRuleId(), onRule, preList);
                                    if (item != null) {
                                        curList.add(curTrrMap.get(index));
                                        map.put(onRule.getRuleItemId(), item);
                                        addressMatchFlag = addressMatchFlag || true;
                                        logger.info("on:itemTriggerV2VO:" + JSONObject.toJSONString(item));
                                    } else {
                                        addressMatchFlag = addressMatchFlag || false;
                                    }
                                }
                            }
                        }

                        //sequence 代表位置之前的前几句话，或者位置之后的几句后
                        List<Rule> beforeList = addressMap.get("before");
                        if (CollectionUtils.isNotEmpty(beforeList)) {
                            if (index == 0) {
                                continue;
                            }

                            addressMatchFlag = false;
                            for (Rule beforeRule : beforeList) {
                                Integer sequence = beforeRule.getSentence_sequence();
                                List<QcJobRecordDetailV2VO> preList = detailList.subList(countBeforeSequence(beforeRule.getRole(), sequence, index), index);
                                if (CollectionUtils.isNotEmpty(preList)) {
                                    ItemTriggerV2VO item = checkWithDeatilList(rule.getRuleId(), beforeRule, preList);
                                    if (item != null) {
                                        curList.add(curTrrMap.get(index));
                                        map.put(beforeRule.getRuleItemId(), item);
                                        addressMatchFlag = addressMatchFlag || true;
                                        logger.info("before:itemTriggerV2VO:" + JSONObject.toJSONString(item));
                                    } else {
                                        addressMatchFlag = addressMatchFlag || false;
                                    }
                                }
                            }
                        }

                        List<Rule> afterList = addressMap.get("after");
                        if (CollectionUtils.isNotEmpty(afterList)) {
                            if (index == detailList.size() - 1) {
                                continue;
                            }

                            addressMatchFlag = false;
                            for (Rule afterRule : afterList) {
                                Integer sequence = afterRule.getSentence_sequence();
                                List<QcJobRecordDetailV2VO> sufList = detailList.subList(index + 1, countAfterSequence(afterRule.getRole(), sequence, index, detailList.size()));
                                if (CollectionUtils.isNotEmpty(sufList)) {
                                    ItemTriggerV2VO item = checkWithDeatilList(rule.getRuleId(), afterRule, sufList);
                                    if (item != null) {
                                        curList.add(curTrrMap.get(index));
                                        map.put(afterRule.getRuleItemId(), item);
                                        addressMatchFlag = addressMatchFlag || true;
                                        logger.info("after:itemTriggerV2VO:" + JSONObject.toJSONString(item));
                                    } else {
                                        addressMatchFlag = addressMatchFlag || false;
                                    }
                                }
                            }
                        }
                        if (addressMatchFlag) {
                            break;
                        }
                    }
                } else {
                    addressMatchFlag = true;
                }
                if (dialogMatchFlag && addressMatchFlag) {
                    //全部匹配
                    logger.info("全部匹配分数计算:" + this.sumScore + ";ruleScore:" + rule.getRuleScore());
                    this.sumScore += rule.getRuleScore();
                    ruleMatch = true;
                    for (Rule rule1 : rule.getRuleList()) {
                        ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                        BeanUtils.copyProperties(rule, trigger);
                        trigger.setRuleScore(rule.getRuleScore());
                        trigger.setParams(rule1.getParams());
                        trigger.setRuleName(rule.getRuleName());
                        trigger.setRuleRate(rule.getRuleRate());
                        trigger.setRuleType(rule.getType());
                        if (curItemId != null && curItemId.equals(rule1.getRuleItemId())) {
                            Set<String> val = new HashSet<>();
                            for (ItemTriggerV2VO str : curList) {
                                val.add(str.getValue());
                            }
                            trigger.setValue(String.join(",", val));
                        } else {
                            trigger.setValue(map.get(rule1.getRuleItemId()) == null ? "" : map.get(rule1.getRuleItemId()).getValue());
                        }
                        addRuleTriggerMap(rule1, trigger);
                    }
                }
            }
        }
        return ruleMatch;
    }

    private Integer countBeforeSequence(String role, Integer sequence, Integer index) {
        if (sequence == null || sequence == -1) {
            return 0;
        }
        int roleCount = 0;
        //找到 role 的前几句
        int count = detailList.size() - 1;
        for (; count >= 0; count--) {
            QcJobRecordDetailV2VO item = detailList.get(count);
            if (count < index) {
                if (item.getType().name().equalsIgnoreCase(role)) {
                    roleCount++;
                    if (roleCount == sequence) {
                        break;
                    }
                }
            }
        }
        return count < 0 ? 0 : count;
    }

    //找到index 后的 角色为role的第几句话位置
    private Integer countAfterSequence(String role, Integer sequence, Integer index, Integer size) {
        if (sequence == null || sequence == -1) {
            return size;
        }
        int roleCount = 0;
        //找到 role 的后几句
        int count = 0;
        for (; count < detailList.size(); count++) {
            QcJobRecordDetailV2VO item = detailList.get(count);
            if (count > index) {
                if (item.getType().name().equalsIgnoreCase(role)) {
                    roleCount++;
                    if (roleCount == sequence) {
                        break;
                    }
                }
            }
        }
        return (count + 1) > detailList.size() ? size : (count + 1);
    }

    private ItemTriggerV2VO checkWithDeatilList(Long ruleId, Rule rule, List<QcJobRecordDetailV2VO> list) {
        ItemTriggerV2VO triggerV2VO = null;
        switch (rule.getMethod()) {
            case "keywordsMatch":
                List<String> keywordsList = rule.getParams();
                triggerV2VO = keywordsMatchAllDetailAndAllDialogWithDetailList(rule, ruleId, rule.getRole(), rule.getNot(), keywordsList, rule.getRuleName(), list);
                if (triggerV2VO != null) {
                    BeanUtils.copyProperties(rule, triggerV2VO);
                    triggerV2VO.setScore(rule.getRuleScore());
                }
                break;
            case "businessMatch":
                List<String> businessList = rule.getParams();
                triggerV2VO = businessMatchAllDetailWithCountAndDetailList(rule, ruleId, rule.getRole(), rule.getNot(), businessList, rule.getRuleName(), list);
                if (triggerV2VO != null) {
                    BeanUtils.copyProperties(rule, triggerV2VO);
                    triggerV2VO.setScore(rule.getRuleScore());
                }
                break;
            case "tagMatch":
                List<String> tagList = rule.getParams();
                triggerV2VO = tagMatchAllDetailWithCountAndDetailList(rule, rule.getRuleId(), rule.getRole(), rule.getNot(), tagList, rule.getRuleName(), list);
                if (triggerV2VO != null) {
                    BeanUtils.copyProperties(rule, triggerV2VO);
                    triggerV2VO.setScore(rule.getRuleScore());
                }
                break;
            default:
                break;
        }

        return triggerV2VO;
    }

    public ItemTriggerV2VO checkMatch(Long ruleId, Rule rule, QcJobRecordDetailV2VO item) {
        ItemTriggerV2VO result = null;
        switch (rule.getMethod()) {
            case "keywordsMatch":
                List<String> keywordsList = rule.getParams();
                result = keywordsMatch(ruleId, rule.getNot(), keywordsList, item.getText(), item, rule.getRuleName());
                break;
            case "businessMatch":
                List<String> businessList = rule.getParams();
                result = businessMatch(ruleId, rule.getNot(), businessList, item, rule.getRuleName());
                break;
            case "tagMatch":
                List<String> tagList = rule.getParams();
                result = tagMatcOneText(ruleId, rule.getRole(), rule.getNot(), tagList, item.getText(), item);
                if (result != null) {
                    buildRuleMap(item, result, rule.getRuleName(), rule.getNot());
                }
                break;
            default:
                break;
        }
        return result;
    }

    private void generateRuleMap() {
        if (Objects.isNull(ruleMap)) {
            ruleMap = new HashMap<>();
        }
        for (QcRuleV2VO qcRuleV2VO : ruleVOList) {
            ruleMap.put(qcRuleV2VO.getQcRuleId(), qcRuleV2VO);
            try {
                List<Rule> list = qcRuleV2Service.getRuleContent(qcRuleV2VO);
                for (Rule item : list) {
                    if ("tagMatch".equalsIgnoreCase(item.getMethod())) {
                        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(Long.valueOf(item.getParams().get(0)));
                        if (StringUtils.isNotEmpty(qcRuleTagPO.getTagKeywords())) {
                            String[] wordslist = qcRuleTagPO.getTagKeywords().split("\n");
                            for (String words : wordslist) {
                                Pattern pattern = Pattern.compile(".*" + words + ".*");
                                tagPatternMap.put(words, pattern);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("build tag tagPatternMap", e);
            }
        }
    }

    private void preBuild() {
        int speed_count = 0;
        int speed_avg = 0;
        int sumDuration = 0;
        int resDuration = 0;
        int intDuration = 0;
        int csCount = 0;
        int personCount = 0;

        Integer csFirstOffset = 0;
        Integer customerOffSet = 0;
        for (int i = 0; i < detailList.size(); i++) {
            QcJobRecordDetailV2VO detailVO = detailList.get(i);
            if (detailVO.getType() == CharacterEnum.CUSTOMER_SERVICE) {
                csCount++;
                csVolume += detailVO.getVoiceValue() == null ? 0L : detailVO.getVoiceValue();
                if (StringUtils.isNotBlank(detailVO.getText()) && detailVO.getText().length() >= 5) {
                    speed_count++;
                    long time = detailVO.getEndOffset() - detailVO.getStartOffset();
                    detailVO.setSpeakSpeed(Long.valueOf(60 * 1000 * detailVO.getText().length() / (time <= 0 ? 1 : time)).intValue());
                    speed_avg += detailVO.getSpeakSpeed();
                }
                if (csFirstOffset == 0) {
                    //设置客服首次说话的节点时间
                    csFirstOffset = detailVO.getStartOffset();
                    if (i == 0) {
                        //说明是第一句话,不需要检测首响应时长
                        dontCheckShouxiangyingshichang = true;
                    }
                }
                sumDuration += detailVO.getEndOffset() - detailVO.getStartOffset();
                //判断前一句话是否是客户说的
                detailVO.setMuteDuration(0);
                detailVO.setResDuration(0);
                if (i > 0 && detailList.get(i - 1).getType() == CharacterEnum.PERSON) {
                    QcJobRecordDetailV2VO person = detailList.get(i - 1);
                    if (person.getEndOffset() < detailVO.getStartOffset()) {
                        detailVO.setMuteDuration(detailVO.getStartOffset() - person.getEndOffset());
                        this.muteDuration += detailVO.getMuteDuration();
                    } else {
                        detailVO.setMuteDuration(0);
                    }
                    if (detailVO.getStartOffset() - person.getEndOffset() > 0) {
                        detailVO.setResDuration(detailVO.getStartOffset() - person.getEndOffset());
                        detailVO.setIntDuration(0);
                    } else {
                        detailVO.setResDuration(0);
                        if (detailVO.getStartOffset() > person.getStartOffset()) {
                            if (detailVO.getEndOffset() < person.getEndOffset()) {
                                detailVO.setIntDuration(detailVO.getEndOffset() - detailVO.getStartOffset());
                            } else {
                                detailVO.setIntDuration(person.getEndOffset() - detailVO.getStartOffset());
                            }
                        }
                    }
                }
                if (detailVO.getResDuration() > 0) {
                    resDuration += detailVO.getResDuration();
                }
                if (detailVO.getIntDuration() > 0) {
                    intDuration += detailVO.getIntDuration();
                }
            } else {
                personCount++;
                personVolume += detailVO.getVoiceValue() == null ? 0L : detailVO.getVoiceValue();
                if (customerOffSet == 0) {
                    //设置客服首次说话的节点时间
                    customerOffSet = detailVO.getEndOffset();
                }
            }
        }
        this.csVolume = csCount == 0L ? 0L : this.csVolume / csCount;
        this.personVolume = personCount == 0L ? 0L : this.personVolume / personCount;
        //语速
        this.speed = (speed_count == 0) ? 0 : (speed_avg / speed_count);
        this.muteDuration = this.muteDuration == null ? 0 : this.muteDuration;
        //静音比
        this.muteRate = (sumDuration == 0) ? 0 : (int) ((this.muteDuration * 1.0 / sumDuration) * 100);

        //如果小于零，那就说明是客服先说的
        if (dontCheckShouxiangyingshichang) {
            this.shouxiangyingshichang = -99;
        } else {
            this.shouxiangyingshichang = (csFirstOffset - customerOffSet) / 1000;
        }

    }

    public List<QcRuleGroupBO> buildRuleGroup(QcRuleV2VO ruleV2VO) {
        JSONObject jsonObject = JSONObject.parseObject(ruleV2VO.getRuleContent());
        JSONArray jsonArray = jsonObject.getJSONArray("group");
        List<QcRuleGroupBO> res = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject ruleItem = jsonArray.getJSONObject(i);
            QcRuleGroupBO groupBO = new QcRuleGroupBO();
            groupBO.setOption(ruleItem.getString("option"));
            groupBO.setNot(ruleItem.getString("not"));
            groupBO.setType(ruleItem.getString("type"));
            Rule rule = new Rule();
            rule.setRuleId(ruleV2VO.getQcRuleId());
            rule.setRuleName(ruleV2VO.getRuleName());
            rule.setFatalFlag(ruleV2VO.getFatalFlag() == 1 ? true : false);
            rule.setRuleRate(ruleV2VO.getRuleRate());
            rule.setRuleScore(ruleV2VO.getRuleScore() * ruleV2VO.getRuleWeight());
            rule.setType(groupBO.getType());
            List<Rule> list = JSONArray.parseArray(ruleItem.getString("content"), Rule.class);
            int index = 0;
            for (Rule item : list) {
                item.setRuleItemId(++index);
                item.setRuleScore(rule.getRuleScore());
                item.setRuleName(ruleV2VO.getRuleName());
                item.setRuleId(rule.getRuleId());
            }
            rule.setRuleList(list);
            groupBO.setContent(rule);
            res.add(groupBO);
        }
        return res;
    }

    public Rule buildRule(QcRuleV2VO ruleV2VO) {
        Rule rule = new Rule();
        rule.setRuleId(ruleV2VO.getQcRuleId());
        rule.setRuleName(ruleV2VO.getRuleName());
        rule.setFatalFlag(ruleV2VO.getFatalFlag() == 1 ? true : false);
        rule.setRuleRate(ruleV2VO.getRuleRate());
        rule.setRuleScore(ruleV2VO.getRuleScore() * ruleV2VO.getRuleWeight());
        rule.setType(ruleV2VO.getRuleRequire() == 1 ? "and" : "or");
        List<Rule> list = JSONArray.parseArray(JSONObject.parseObject(ruleV2VO.getRuleContent()).getString("content"), Rule.class);
        int index = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (Rule item : list) {
                item.setRuleItemId(++index);
                item.setRuleScore(rule.getRuleScore());
                item.setRuleName(ruleV2VO.getRuleName());
                item.setRuleId(rule.getRuleId());
            }
            rule.setRuleList(list);
        }
        logger.info("ruleInfo : " + JSONObject.toJSONString(rule));
        return rule;
    }

    private Integer getMinListCount(List<String> list) {
        HashMap<String, Integer> hs = new HashMap<>();
        for (String string : list) {
            Integer count = 1;
            if (hs.get(string) != null) {
                count = hs.get(string) + 1;
            }
            hs.put(string, count);
        }
        Integer count = 1000;
        for (Map.Entry<String, Integer> entry : hs.entrySet()) {
            if (entry.getValue() < count) {
                count = entry.getValue();
            }
        }
        if (hs.size() == 0) {
            count = 0;
        }
        return count;
    }

    /**
     * 关键词匹配
     *
     * @param params ["key1", "key2", "all"]
     * @return
     */
    public ItemTriggerV2VO keywordsMatchAllDetail(Rule item, Long ruleId, String role, List<String> params, String ruleName) {
        if (CollectionUtils.isNotEmpty(params) && params.size() > 1) {
            String flag = params.get(params.size() - 1);
            List<String> list = new ArrayList<>();
            List<String> keyList = subListWithLength(params, params.size() - 1);
            Integer index = -1;
            for (QcJobRecordDetailV2VO detailV2VO : detailList) {
                index++;
                if (item.getFromIndex() != null && index < item.getFromIndex()) {
                    continue;
                }
                if (item.getToIndex() != null && item.getToIndex() < index) {
                    continue;
                }
                List<String> veryList = new ArrayList<>();
                if (!detailV2VO.getType().name().equalsIgnoreCase(role)) {
                    continue;
                }
                Boolean oneTrue = false;
                Boolean oneFalse = false;
                for (String str : keyList) {
                    Pattern pattern = Pattern.compile(".*" + str + ".*");
                    String dealText = textQcWithRichText(detailV2VO);
                    if (textQcWithType(detailV2VO) && pattern.matcher(dealText).matches()) {
                        veryList.add(str);
                        list.add(str);
                        oneTrue = true;
                    } else {
                        oneFalse = true;
                    }
                }
                Boolean matchFlag = false;
                if ("one".equalsIgnoreCase(flag) && oneTrue) {
                    matchFlag = true;
                }

                if ("all".equalsIgnoreCase(flag) && !oneFalse) {
                    matchFlag = true;
                }
                if (matchFlag) {
                    String keys = Joiner.on(",").join(veryList);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    buildRuleMap(detailV2VO, trigger, ruleName);
                }
            }

            if (list.size() > 0) {
                Set<String> set = new HashSet<>(list);
                if ("one".equalsIgnoreCase(flag)) {
                    String keys = Joiner.on(",").join(set);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    trigger.setTriggerCount(list.size());
                    return trigger;
                }

                if ("all".equalsIgnoreCase(flag) && set.size() == keyList.size()) {
                    String keys = Joiner.on(",").join(set);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    trigger.setTriggerCount(getMinListCount(list));
                    return trigger;
                }
            }
            return null;
        } else {
            return null;
        }
    }

    /**
     * 关键词匹配
     *
     * @param params ["key1", "key2", "all"]
     * @return
     */
    public ItemTriggerV2VO keywordsMatchAllDetailAndAllDialog(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName) {
        return keywordsMatchAllDetailAndAllDialogWithDetailList(ruleItem, ruleId, role, not, params, ruleName, detailList);
    }


    /**
     * 关键词匹配
     *
     * @param params ["key1", "key2", "all"]
     * @return
     */
    public ItemTriggerV2VO keywordsMatchAllDetailAndAllDialogWithDetailList(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName, List<QcJobRecordDetailV2VO> detailV2VOS) {
        if (CollectionUtils.isNotEmpty(params) && params.size() > 1) {
            String flag = params.get(params.size() - 1);
            List<String> list = new ArrayList<>();
            List<String> keyList = subListWithLength(params, params.size() - 1);
            Boolean wholeOnceTrue = false;
            List<String> matchList = new ArrayList<>();
            Integer index = -1;
            for (QcJobRecordDetailV2VO detailV2VO : detailV2VOS) {
                index++;
                if (ruleItem.getFromIndex() != null && index < ruleItem.getFromIndex()) {
                    continue;
                }
                if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                    continue;
                }
                List<String> veryList = new ArrayList<>();
                if (!detailV2VO.getType().name().equalsIgnoreCase(role)) {
                    continue;
                }
                Boolean oneTrue = false;
                Boolean oneFalse = false;
                for (String str : keyList) {
                    Pattern pattern = Pattern.compile(".*" + str + ".*");
                    String dealText = textQcWithRichText(detailV2VO);
                    if (textQcWithType(detailV2VO) && pattern.matcher(dealText).matches()) {
                        veryList.add(str);
                        matchList.add(str);
                        oneTrue = true;
                        wholeOnceTrue = true;
                    } else {
                        oneFalse = true;
                    }
                }

                Set<String> set = new HashSet<>(veryList);
                if (not && "one".equalsIgnoreCase(flag) && oneTrue) {
                    //包含，任一
                    list.addAll(veryList);
                    String keys = Joiner.on(",").join(veryList);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    buildRuleMap(detailV2VO, trigger, ruleName);
                }

                if (not && "all".equalsIgnoreCase(flag) && set.size() == keyList.size()) {
                    //包含，全部
                    list.addAll(veryList);
                    String keys = Joiner.on(",").join(veryList);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    buildRuleMap(detailV2VO, trigger, ruleName);
                }


                if (!not && "one".equalsIgnoreCase(flag) && veryList.size() == 0) {
                    //不包含，任一
                    String keys = Joiner.on(",").join(keyList);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    buildRuleMap(detailV2VO, trigger, ruleName, not);
                }

                if (!not && "all".equalsIgnoreCase(flag) && veryList.size() < keyList.size()) {
                    //不包含，全部
                    list.addAll(veryList);
                    String keys = Joiner.on(",").join(subList(keyList, veryList));
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    buildRuleMap(detailV2VO, trigger, ruleName, not);
                }
            }
            if (matchList.size() > 0 && not) {
                //整个对话过程中，包含
                if ("one".equalsIgnoreCase(flag)) {
                    //任一
                    Set<String> paramSet = new HashSet<>(list);
                    String keys = Joiner.on(",").join(paramSet);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    trigger.setTriggerCount(1);
                    return trigger;
                }
                if ("all".equalsIgnoreCase(flag)) {
                    //全部
                    Set<String> paramSet = new HashSet<>(matchList);
                    if (paramSet.size() == keyList.size()) {
                        String keys = Joiner.on(",").join(keyList);
                        ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                        trigger.setRuleId(ruleId);
                        trigger.setType("keywords");
                        trigger.setValue(keys);
                        trigger.setParams(params);
                        trigger.setTriggerCount(1);
                        return trigger;
                    }
                }
            }
            if (!not) {
                //整个对话过程中，不包含
                Set<String> paramSet = new HashSet<>(list);
                if ("one".equalsIgnoreCase(flag) && !wholeOnceTrue) {
                    //任一
                    String keys = Joiner.on(",").join(keyList);
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    trigger.setTriggerCount(1);
                    return trigger;
                }

                if ("all".equalsIgnoreCase(flag) && paramSet.size() < keyList.size()) {
                    //全部
                    String keys = Joiner.on(",").join(subList(keyList, new ArrayList<>(paramSet)));
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType("keywords");
                    trigger.setValue(keys);
                    trigger.setParams(params);
                    trigger.setTriggerCount(1);
                    return trigger;
                }
            }
            return null;
        } else {
            return null;
        }
    }

    private List<String> subListWithLength(List<String> source, Integer index) {
        List<String> subList = new ArrayList<>();
        for (int i = 0; i < index; i++) {
            subList.add(source.get(i));
        }
        return subList;
    }

    private List<String> subList(List<String> source, List<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return source;
        } else {
            List<String> subList = new ArrayList<>();
            for (String item : source) {
                if (!list.contains(item)) {
                    subList.add(item);
                }
            }
            return subList;
        }
    }

    /**
     * 关键词匹配
     *
     * @param params ["key1", "key2", "all"]
     * @param text
     * @return
     */
    public ItemTriggerV2VO keywordsMatch(Long ruleId, Boolean not, List<String> params, String text, QcJobRecordDetailV2VO item, String ruleName) {
        if (CollectionUtils.isNotEmpty(params) && params.size() > 1) {
            List<String> keyList = subListWithLength(params, params.size() - 1);
            String flag = params.get(params.size() - 1);
            Boolean oneTrue = false;
            List<String> matchList = new ArrayList<>();
            for (String str : keyList) {
                Pattern pattern = Pattern.compile(".*" + str + ".*");
                String dealText = textQcWithRichText(item);
                if (textQcWithType(item) && pattern.matcher(dealText).matches()) {
                    matchList.add(str);
                    oneTrue = true;
                }
            }

            Set<String> set = new HashSet<>(matchList);
            if (not && "one".equalsIgnoreCase(flag) && oneTrue) {
                //包含，任一
                String keys = Joiner.on(",").join(set);
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("keywords");
                trigger.setValue(keys);
                trigger.setParams(params);
                buildRuleMap(item, trigger, ruleName, not);
                return trigger;
            }

            if (not && "all".equalsIgnoreCase(flag) && set.size() == keyList.size()) {
                //包含，全部
                String keys = Joiner.on(",").join(keyList);
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("keywords");
                trigger.setValue(keys);
                trigger.setParams(params);
                buildRuleMap(item, trigger, ruleName, not);
                return trigger;
            }


            if (!not && "one".equalsIgnoreCase(flag) && matchList.size() == 0) {
                //不包含，任一
                String keys = Joiner.on(",").join(keyList);
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("keywords");
                trigger.setValue(keys);
                trigger.setParams(params);
                buildRuleMap(item, trigger, ruleName, not);
                return trigger;
            }

            if (!not && "all".equalsIgnoreCase(flag) && matchList.size() < keyList.size()) {
                //不包含，全部
                String keys = Joiner.on(",").join(subList(keyList, matchList));
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("keywords");
                trigger.setValue(keys);
                trigger.setParams(params);
                buildRuleMap(item, trigger, ruleName, not);
                return trigger;
            }
            return null;
        } else {
            return null;
        }
    }

    private void buildRuleMap(QcJobRecordDetailV2VO item, ItemTriggerV2VO trigger, String ruleName) {
        buildRuleMap(item, trigger, ruleName, true);
    }

    private void buildRuleMap(QcJobRecordDetailV2VO itemDetail, ItemTriggerV2VO trigger, String ruleName, Boolean not) {
        //这里需要考虑通过截取过来的detail, 引用修改不会变更，所以，需要找到原始的detail
        QcJobRecordDetailV2VO item = null;
        for (QcJobRecordDetailV2VO detailV2VO : detailList) {
            if (detailV2VO.getText().equals(itemDetail.getText()) && detailV2VO.getIndex().equals(itemDetail.getIndex())) {
                item = detailV2VO;
                break;
            }
        }
        if (item == null) {
            item = itemDetail;
        }
        Map<Long, List<ItemTriggerV2VO>> map = item.getMap();
        List<ItemTriggerV2VO> list = map.get(trigger.getRuleId());
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(trigger);
        map.put(trigger.getRuleId(), list);
        item.setMap(map);
        List<String> logList = item.getDebugLogList();
        if (logList == null) {
            logList = new ArrayList<>();
        }
        List<ItemTriggerV2VO> triggerV2VOList = item.getTriggerList();
        if (triggerV2VOList == null) {
            triggerV2VOList = new ArrayList<>();
        }
        triggerV2VOList.add(trigger);
        String logText = null;
        switch (trigger.getType()) {
            case "keywords":
                logText = (not ? "" : "未") + "检测到关键词:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "tag":
                if (tagMatchResult == null) {
                    logText = (not ? "" : "未") + "触发语句标签:" + trigger.getValue() + "，相关规则:" + ruleName;
                } else {
                    if (StringUtils.isNoneEmpty(tagMatchResult.getKeyWords())) {
                        logText = "命中关键词:" + tagMatchResult.getKeyWords() + "，触发语句标签" + trigger.getValue() + "，相关规则:" + ruleName;
                    } else if (tagMatchResult.getConfidence() != null) {
                        logText = "命中语料置信度:" + tagMatchResult.getConfidence() + "，触发语句标签" + trigger.getValue() + "，相关规则:" + ruleName;
                    } else {
                        logText = (not ? "" : "未") + "触发语句标签:" + trigger.getValue() + "，相关规则:" + ruleName;
                    }
                }
                break;
            case "mei_ju_yu_su":
                //["mei_ju_yu_su", "10", "20", ">=", 10]
                logText = "每句语速:" + trigger.getValue() + "字/分钟，相关规则:" + ruleName;
                break;
            case "zheng_ti_yu_su":
                //["zheng_ti_yu_su", "10", "20"]
                logText = "整体语速:" + trigger.getValue() + "字/分钟，相关规则:" + ruleName;
                break;
            case "jing_yin_bi":
                //todo 全局
                logText = "静音比:" + trigger.getValue() + "%，相关规则:" + ruleName;
                break;
            case "jing_yin_zong_shi_chang":
                //todo 全局
                logText = "静音总时长:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "fu_wu_qiang_duan_shi_chang":
                //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                logText = "服务抢断时长:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "fu_wu_xiang_ying_shi_chang":
                //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                logText = "服务响应时长:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "mei_ju_yin_liang":
                //["mei_ju_yin_liang", "10", "20", ">=", "10"]
                logText = "每句音量:" + trigger.getValue() + "分贝，相关规则:" + ruleName;
                break;
            case "zheng_ti_yin_liang":
                //todo 全局
                //["zheng_ti_yin_liang", "10", "20", ">=", "10"]
                logText = "整体音量:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "chu_xian_xiao_sheng":
                //["chu_xian_xiao_sheng", ">=", "10"]
                logText = (not ? "" : "未") + "检测到笑声，相关规则:" + ruleName;
                break;
            case "chu_xian_ke_sou_sheng":
                //["chu_xian_ke_sou_sheng", ">=", "10"]
                logText = (not ? "" : "未") + "检测到咳嗽声，相关规则:" + ruleName;
                break;
            case "dui_hua_lun_ci":
                //todo 全局
                //["dui_hua_lun_ci", ">=", "10"]
                logText = "对话轮次:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "ke_fu_qing_xu":
                //["ke_fu_qing_xu", "平静", ">=", "10"]
                logText = (not ? "" : "未") + "检测到客服情绪:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "ke_hu_qing_xu":
                //["ke_hu_qing_xu", "平静", ">=", "10"]
                logText = (not ? "" : "未") + "检测到客户情绪:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "ti_ji_shou_ji_hao":
                //["ti_ji_shou_ji_hao"]
                logText = "提及手机号:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;
            case "shou_xiang_ying_shi_chang":
                logText = "首响应时长:" + (shouxiangyingshichang < 0 ? 0 : shouxiangyingshichang) + "秒，相关规则:" + ruleName;
                break;
            case "chong_fu_hua_shu":
                logText = "重复话术:" + trigger.getValue() + "，相关规则:" + ruleName;
                break;

        }
        if (StringUtils.isNoneEmpty(logText)) {
            logList.add(logText);
        }
        item.setDebugLogList(logList);
        item.setDebugLog(logList);
    }

    public ItemTriggerV2VO tagMatchAllDetail(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName) {
        //todo id获取语料信息
        //todo 算法接口
        int count = 0;
        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(Long.valueOf(params.get(0)));
        String keys = qcRuleTagPO == null ? "" : qcRuleTagPO.getTagName();
        Integer index = -1;
        for (QcJobRecordDetailV2VO item : detailList) {
            index++;
            if (ruleItem.getFromIndex() != null && index < ruleItem.getFromIndex()) {
                continue;
            }
            if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                continue;
            }
            if (!item.getType().name().equalsIgnoreCase(role)) {
                continue;
            }
            //算法接口
            if (notAndContion(not, tagMatch(role, params, item.getText(), qcRuleTagPO.getTagKeywords(), item))) {
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("tag");
                trigger.setValue(keys);
                trigger.setParams(params);
                trigger.setTriggerCount(1);
                buildRuleMap(item, trigger, ruleName, not);
                count++;
            }
        }
        if (compareTag(count, params)) {
            ItemTriggerV2VO trigger1 = new ItemTriggerV2VO();
            trigger1.setRuleId(ruleId);
            trigger1.setType("tag");
            trigger1.setValue(keys);
            trigger1.setParams(params);
            trigger1.setTriggerCount(count);
            return trigger1;
        } else {
            return null;
        }
    }

    public ItemTriggerV2VO tagMatcOneText(Long ruleId, String role, Boolean not, List<String> params, String text, QcJobRecordDetailV2VO item) {
        //todo id获取语料信息
        //todo 算法接口
        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(Long.valueOf(params.get(0)));
        String keys = qcRuleTagPO == null ? "" : qcRuleTagPO.getTagName();
        if (notAndContion(not, tagMatch(role, params, text, qcRuleTagPO.getTagKeywords(), item))) {
            ItemTriggerV2VO trigger = new ItemTriggerV2VO();
            trigger.setRuleId(ruleId);
            trigger.setType("tag");
            trigger.setValue(keys);
            trigger.setParams(params);
            trigger.setTriggerCount(1);
            return trigger;
        }
        return null;
    }

    public ItemTriggerV2VO tagMatchAllDetailWithCount(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName) {
        return tagMatchAllDetailWithCountAndDetailList(ruleItem, ruleId, role, not, params, ruleName, detailList);
    }


    public ItemTriggerV2VO tagMatchAllDetailWithCountAndDetailList(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName, List<QcJobRecordDetailV2VO> list) {
        //todo id获取语料信息
        //todo 算法接口
        int count = 0;
        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(Long.valueOf(params.get(0)));
        String keys = qcRuleTagPO == null ? "" : qcRuleTagPO.getTagName();
        Integer index = -1;
        for (QcJobRecordDetailV2VO item : list) {
            index++;
            if (ruleItem.getFromIndex() != null && index < ruleItem.getFromIndex()) {
                continue;
            }
            if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                continue;
            }
            if (!item.getType().name().equalsIgnoreCase(role)) {
                continue;
            }
            //算法接口
            if (notAndContion(not, tagMatch(role, params, item.getText(), qcRuleTagPO.getTagKeywords(), item))) {
                //匹配成功。
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType("tag");
                trigger.setValue(keys);
                trigger.setParams(params);
                trigger.setTriggerCount(1);
                buildRuleMap(item, trigger, ruleName, not);
                count++;
            }
        }
        if (comareOne(count, params)) {
            ItemTriggerV2VO trigger1 = new ItemTriggerV2VO();
            trigger1.setRuleId(ruleId);
            trigger1.setType("tag");
            trigger1.setValue(keys);
            trigger1.setParams(params);
            trigger1.setTriggerCount(count);
            return trigger1;
        } else {
            return null;
        }
    }

    private boolean tagMatchKeywords(String keywords, String text) {
        if (StringUtils.isNoneEmpty(keywords)) {
            String[] list = keywords.split("\n");
            //替换掉换行符
            text = text.replaceAll("\\n", "");
            for (String words : list) {
                Pattern pattern = tagPatternMap.get(words);
                if (pattern == null) {
                    pattern = Pattern.compile(".*" + words + ".*");
                }
                if (pattern.matcher(text).matches()) {
                    if (tagMatchResult == null) {
                        tagMatchResult = new TagMatchResult();
                    }
                    tagMatchResult.setKeyWords(words);
                    return true;
                }
            }
        }
        return false;
    }

    private boolean tagMatchSentence(Long tagId, String text) {
        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(tagId);
        if (qcRuleTagPO != null) {
            QcRuleTagVO tagVO = qcRuleTagPO.convertToVO();
            if (tagVO != null) {
                List<String> list = tagVO.getTagSentence();
                if (CollectionUtils.isNotEmpty(list)) {
                    try {
                        String textReplace = text.replaceAll("[\\pP‘’“”]", "").trim();
                        if (list.contains(textReplace)) {
                            logger.info("全匹配:" + text);
                            return true;
                        }
                    } catch (Exception e) {
                        logger.error("tagMatchSentence", e);
                    }
                }
            }
        }
        return false;
    }

    private Boolean tagMatchHaveSentence(Long tagId) {
        QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(tagId);
        if (qcRuleTagPO != null) {
            QcRuleTagVO tagVO = qcRuleTagPO.convertToVO();
            if (tagVO != null) {
                List<String> list = tagVO.getTagSentence();
                if (CollectionUtils.isNotEmpty(list)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean tagMatch(String role, List<String> params, String text, String keywords, QcJobRecordDetailV2VO qcJobRecordDetailV2VO) {
        try {
            if (!textQcWithType(qcJobRecordDetailV2VO)) {
                return false;
            }
            text = textQcWithRichText(qcJobRecordDetailV2VO);
            tagMatchResult = new TagMatchResult();

            if (!qcJobPO.getTenantId().equals(7376L)) {
                Long tagId = Long.valueOf(params.get(0));
                if (tagMatchHaveSentence(tagId)) {
                    if (tagMatchSentence(tagId, text)) {
                        Long count = tagMatchCount.get(tagId);
                        tagMatchCount.put(tagId, count == null ? 1 : 1 + count);
                        if (tagMatchResult == null) {
                            tagMatchResult = new TagMatchResult();
                        }
                        tagMatchResult.setConfidence(1.0);
                        return true;
                    }
                    AlgorithmTrainTypeEnum trainType = AlgorithmTrainTypeEnum.QC_TAG_CUSTOMER;
                    if (CharacterEnum.CUSTOMER_SERVICE.name().equals(role)) {
                        trainType = AlgorithmTrainTypeEnum.QC_TAG_STAFF;
                    }
                    // 质检将robotId设置为0，统一通过tenantId来区分，将inputMinLength设为null，不在后端进行文本过滤
                    PredictRequest predictRequest = PredictRequest.builder()
                            .tenantId(qcJobPO.getTenantId())
                            .robotId(0L)
                            .userInput(text)
                            .trainType(trainType)
                            .snapshotType(SnapshotTypeEnum.PUBLISHED)
                            .isVerbalTraining(false)
                            .build();
                    PredictResponse predictResponse = YiwiseAskHelper.predict(predictRequest);
                    if (Objects.isNull(predictResponse.getIntent()) || org.apache.commons.collections.CollectionUtils.isEmpty(predictResponse.getIntent().getIntent_ranking())) {
                        logger.info("质检标签预测，算法返回为空");
                    } else {
                        List<IntentBO> intentRanking = predictResponse.getIntent().getIntent_ranking();
                        //取出正常文本以上的
                        List<IntentBO> arr = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(intentRanking)) {
                            for (IntentBO item : intentRanking) {
                                if ("正常文本".equals(item.getName())) {
                                    break;
                                } else {
                                    arr.add(item);
                                }
                            }
                        }
                        for (IntentBO intentBO : arr) {
                            if (org.apache.commons.lang.StringUtils.isNotEmpty(intentBO.getName())) {
                                //匹配
                                QcRuleTagPO qcRuleTagPO = qcRuleTagService.selectByKey(tagId);
                                if (qcRuleTagPO != null && intentBO.getName().equals(qcRuleTagPO.getTagName())) {
                                    Double threshold = null;
                                    if (!enableQcTagThreshold) {
                                        threshold = (qcRuleTagPO.getQcTagThreshold() != null && qcRuleTagPO.getQcTagThreshold() > 0) ? qcRuleTagPO.getQcTagThreshold() : qcTagThreshold;
                                    } else {
                                        threshold = qcTagThreshold;
                                    }
                                    if (threshold != null && threshold > 0 && intentBO.getConfidence() != null && Double.parseDouble(intentBO.getConfidence()) < threshold) {
                                        logger.info("语句标签阈值和置信度不匹配threshold={}, Confidence={}", threshold, intentBO.getConfidence());
                                    } else {
                                        Long count = tagMatchCount.get(tagId);
                                        tagMatchCount.put(tagId, count == null ? 1 : 1 + count);
                                        if (tagMatchResult == null) {
                                            tagMatchResult = new TagMatchResult();
                                        }
                                        tagMatchResult.setConfidence(Double.parseDouble(intentBO.getConfidence()));
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (tagMatchKeywords(keywords, text)) {
                logger.info("匹配到关键词，不进行标签匹配，keywords={}, text={}", keywords, text);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("tagMatch error, ", e);
        }
        return false;
    }

    /**
     * @param not     true:包含， false:不包含
     * @param contion 条件
     * @return
     */
    private boolean notAndContion(Boolean not, Boolean contion) {
        if (not) {
            //包含
            return contion;
        } else {
            return !contion;
        }
    }

    /**
     * 业务指标匹配
     *
     * @param params ["mei_ju_yu_su", "10", "20", ">=", 10]
     * @return
     */
    public ItemTriggerV2VO businessMatchAllDetailWithCount(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName) {
        return businessMatchAllDetailWithCountAndDetailList(ruleItem, ruleId, role, not, params, ruleName, detailList);
    }

    public ItemTriggerV2VO businessMatchAllDetailWithCountAndDetailList(Rule ruleItem, Long ruleId, String role, Boolean not, List<String> params, String ruleName, List<QcJobRecordDetailV2VO> detailV2VOS) {
        ItemTriggerV2VO returnTrigger = new ItemTriggerV2VO();
        returnTrigger.setRuleId(ruleId);
        returnTrigger.setParams(params);
        Integer count = 0;
        String business = params.get(0);
        Integer duihualunciCount = 0;

        Integer index = -1;
        for (QcJobRecordDetailV2VO item : detailV2VOS) {
            index++;
            if (ruleItem.getFromIndex() != null && ruleItem.getFromIndex() > index) {
                continue;
            }
            if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                continue;
            }
            if (CharacterEnum.PERSON.equals(item.getType())) {
                duihualunciCount++;
            }
        }

        index = -1;
        //存储没有标点的文本
        List<String> detailStrList = new ArrayList<>();
        for (QcJobRecordDetailV2VO item : detailV2VOS) {
            index++;
            if (ruleItem.getFromIndex() != null && ruleItem.getFromIndex() > index) {
                continue;
            }
            if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                continue;
            }
            if (!item.getType().name().equalsIgnoreCase(role)) {
                continue;
            }
            if (StringUtils.isNoneEmpty(item.getText())) {
                //去除标点
                detailStrList.add(item.getText().replaceAll("[\\pP‘’“”]", "").trim());
            }
            if (CollectionUtils.isNotEmpty(params)) {
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType(business);
                trigger.setParams(params);
                returnTrigger.setType(business);
                switch (business) {
                    case "mei_ju_yu_su":
                        //["mei_ju_yu_su", "10", "20", ">=", 10]
                        long time = item.getEndOffset() - item.getStartOffset();
                        long speed_avg = 60 * item.getText().length() * 1000 / (time <= 0 ? 1 : time);
                        if (notAndContion(not, Long.valueOf(params.get(1)) <= speed_avg && speed_avg <= Long.valueOf(params.get(2)))) {
                            trigger.setValue(String.valueOf(speed_avg));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "zheng_ti_yu_su":
                        //todo 全局
                        //["zheng_ti_yu_su", "10", "20"]
                        break;
                    case "jing_yin_bi":
                        //todo 全局
                        break;
                    case "jing_yin_zong_shi_chang":
                        //todo 全局
                        break;
                    case "fu_wu_qiang_duan_shi_chang":
                        //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                        long timeInt = item.getIntDuration() / 1000;
                        if (notAndContion(not, timeInt >= Long.valueOf(params.get(1)) && timeInt <= Long.valueOf(params.get(2)))) {
                            trigger.setValue(String.valueOf(timeInt));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "fu_wu_xiang_ying_shi_chang":
                        //["fu_wu_xiang_ying_shi_chang", "10", "20", ">=", "10"]
                        long timeRes = item.getResDuration() / 1000;
                        if (notAndContion(not, timeRes >= Long.valueOf(params.get(1)) && timeRes <= Long.valueOf(params.get(2)))) {
                            trigger.setValue(String.valueOf(timeRes));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "mei_ju_yin_liang":
                        //["mei_ju_yin_liang", "10", "20", ">=", "10"]
                        if (notAndContion(not, item.getVoiceValue() >= Long.valueOf(params.get(1)) && item.getVoiceValue() <= Long.valueOf(params.get(2)))) {
                            trigger.setValue(String.valueOf(item.getVoiceValue()));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "zheng_ti_yin_liang":
                        //todo 全局
                        //["zheng_ti_yin_liang", "10", "20", ">=", "10"]
                        break;
                    case "chu_xian_xiao_sheng":
                        //["chu_xian_xiao_sheng", ">=", "10"]
                        break;
                    case "chu_xian_ke_sou_sheng":
                        //["chu_xian_ke_sou_sheng", ">=", "10"]
                        break;
                    case "dui_hua_lun_ci":
                        //todo 全局
                        //["dui_hua_lun_ci", ">=", "10"]
                        break;
                    case "ke_fu_qing_xu":
                        //["ke_fu_qing_xu", "平静", ">=", "10"]
                        item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                        if (notAndContion(not, compareEmotion(item.getEmotion().getDesc(), params.get(1)))) {
                            trigger.setValue(item.getEmotion().getDesc());
                            buildRuleMap(item, trigger, ruleName, not);
                            count++;
                        }
                        break;
                    case "ke_hu_qing_xu":
                        //["ke_hu_qing_xu", "平静", ">=", "10"]
                        item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                        if (notAndContion(not, compareEmotion(item.getEmotion().getDesc(), params.get(1)))) {
                            trigger.setValue(item.getEmotion().getDesc());
                            buildRuleMap(item, trigger, ruleName, not);
                            count++;
                        }
                    case "ti_ji_shou_ji_hao":
                        //["ti_ji_shou_ji_hao"]
                        String result = PhoneNumberUtils.getPhoneNumberFromText(item.getText());
                        if (StringUtils.isNotEmpty(result)) {
                            trigger.setValue(result);
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                }
            }
        }
        Boolean matchFlag = false;
        if (count > 0) {
            matchFlag = true;
            switch (business) {
                case "mei_ju_yu_su":
                    //["mei_ju_yu_su", "10", "20", ">=", 10]
                    matchFlag = compare(count, params);
                    break;
                case "fu_wu_qiang_duan_shi_chang":
                    //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                    matchFlag = compare(count, params);
                    break;
                case "fu_wu_xiang_ying_shi_chang":
                    //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                    matchFlag = compare(count, params);
                    break;
                case "mei_ju_yin_liang":
                    //["mei_ju_yin_liang", "10", "20", ">=", "10"]
                    matchFlag = compare(count, params);
                    break;
                case "chu_xian_xiao_sheng":
                    //["chu_xian_xiao_sheng", ">=", "10"]
                    break;
                case "chu_xian_ke_sou_sheng":
                    //["chu_xian_ke_sou_sheng", ">=", "10"]
                    break;
                case "ke_fu_qing_xu":
                    //["ke_fu_qing_xu", "平静", ">=", "10"]
                    matchFlag = compareQingxu(count, params);
                    break;
                case "ke_hu_qing_xu":
                    //["ke_hu_qing_xu", "平静", ">=", "10"]
                    matchFlag = compareQingxu(count, params);
                    break;
                case "ti_ji_shou_ji_hao":
                    //["ti_ji_shou_ji_hao"]
                    break;
            }
        }
        switch (business) {
            case "dui_hua_lun_ci":
                //todo 全局
                //["dui_hua_lun_ci", ">=", "10"]
                matchFlag = comareOne(duihualunciCount, params);
                matchFlag = notAndContion(not, matchFlag);
                break;
            case "jing_yin_bi":
                //todo 全局
                if (this.muteRate.compareTo(Integer.valueOf(params.get(1))) >= 0 && this.muteRate.compareTo(Integer.valueOf(params.get(2))) <= 0) {
                    matchFlag = true;
                }
                matchFlag = notAndContion(not, matchFlag);
                break;
            case "zheng_ti_yu_su":
                //todo 全局
                //["zheng_ti_yu_su", "10", "20"]
                if (this.speed.compareTo(Integer.valueOf(params.get(1))) >= 0 && this.speed.compareTo(Integer.valueOf(params.get(2))) <= 0) {
                    matchFlag = true;
                }
                matchFlag = notAndContion(not, matchFlag);
                break;
            case "jing_yin_zong_shi_chang":
                //todo 全局
                Integer m = this.muteDuration / 1000;
                if (m.compareTo(Integer.valueOf(params.get(1))) >= 0 && m.compareTo(Integer.valueOf(params.get(2))) <= 0) {
                    matchFlag = true;
                }
                matchFlag = notAndContion(not, matchFlag);
                break;
            case "zheng_ti_yin_liang":
                //todo 全局
                if (CharacterEnum.PERSON.name().equalsIgnoreCase(role)) {
                    if (this.personVolume.compareTo(Long.valueOf(params.get(1))) >= 0 && this.personVolume.compareTo(Long.valueOf(params.get(2))) <= 0) {
                        matchFlag = true;
                    }
                }
                if (CharacterEnum.CUSTOMER_SERVICE.name().equalsIgnoreCase(role)) {
                    if (this.csVolume.compareTo(Long.valueOf(params.get(1))) >= 0 && this.csVolume.compareTo(Long.valueOf(params.get(2))) <= 0) {
                        matchFlag = true;
                    }
                }
                matchFlag = notAndContion(not, matchFlag);
                break;
            case "shou_xiang_ying_shi_chang":
                //全局 首响应时长
                logger.info(this.shouxiangyingshichang + "---" + params.get(1) + "---" + params.get(2));
                if (dontCheckShouxiangyingshichang) {
                    //如果是第一句则不校验首响应时长了
                    break;
                }
                if (this.shouxiangyingshichang.compareTo(Integer.valueOf(params.get(1))) >= 0 && this.shouxiangyingshichang.compareTo(Integer.valueOf(params.get(2))) <= 0) {
                    matchFlag = true;
                }
                matchFlag = notAndContion(not, matchFlag);
                if (matchFlag) {
                    ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                    trigger.setRuleId(ruleId);
                    trigger.setType(business);
                    trigger.setParams(params);
                    doCsFirst(trigger, ruleName);
                }
                break;
            case "chong_fu_hua_shu":
                //["chong_fu_hua_shu", 3, 2, "true", "你好\n真的\n"]
                //连续3句出现2次，是否使用特定的重复话术,后两个参数可能是没有的
                String flag = "false";
                String[] arr = null;
                if (params.size() > 4) {
                    flag = params.get(3);
                    String cStr = params.get(4);
                    arr = cStr == null ? null : cStr.split("\\n");
                }
                Integer cCount = 0;
                List<String> matchList = new ArrayList<>();
                Integer subFrom = null;
                Integer subTo = null;
                for (int i = 0; i < detailStrList.size(); i++) {
                    List<String> subList = detailStrList.subList(i, (i + Integer.valueOf(params.get(1))) > detailStrList.size() ? detailStrList.size() : (i + Integer.valueOf(params.get(1))));
                    if (CollectionUtils.isNotEmpty(subList)) {
                        Map<String, Integer> map = new HashMap<>();
                        for (String item : subList) {
                            Integer mapCount = map.get(item);
                            if (mapCount == null) {
                                map.put(item, 1);
                            } else {
                                map.put(item, mapCount + 1);
                            }
                        }
                        if ("true".equalsIgnoreCase(flag)) {
                            if (arr != null) {
                                for (String item : subList) {
                                    for (String paramItem : arr) {
                                        String paramItemReplace = paramItem.replaceAll("[\\pP‘’“”]", "").trim();
                                        if (item.equalsIgnoreCase(paramItemReplace)) {
                                            cCount++;
                                            subFrom = i;
                                            subTo = (i + Integer.valueOf(params.get(1))) > detailStrList.size() ? detailStrList.size() : (i + Integer.valueOf(params.get(1)));
                                            matchList.add(paramItemReplace);
                                        }
                                    }
                                }
                            }
                        } else {
                            for (String item : subList) {
                                if (map.get(item) != null) {
                                    Integer mapCount = map.get(item);
                                    if (mapCount > cCount && mapCount >= Integer.valueOf(params.get(2))) {
                                        subFrom = i;
                                        subTo = (i + Integer.valueOf(params.get(1))) > detailStrList.size() ? detailStrList.size() : (i + Integer.valueOf(params.get(1)));
                                        cCount = mapCount;
                                        matchList.add(item);
                                    }
                                }
                            }
                        }
                    }
                }
                if (cCount >= Integer.valueOf(params.get(2))) {
                    matchFlag = true;
                }
                matchFlag = notAndContion(not, matchFlag);
                index = -1;
                Integer subIndex = -1;
                if (matchFlag) {
                    for (QcJobRecordDetailV2VO item : detailV2VOS) {
                        index++;
                        if (ruleItem.getFromIndex() != null && ruleItem.getFromIndex() > index) {
                            continue;
                        }
                        if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                            continue;
                        }
                        if (!item.getType().name().equalsIgnoreCase(role)) {
                            continue;
                        }
                        if (StringUtils.isNoneEmpty(item.getText())) {
                            //去除标点
                            //这里校验下是不是在 subFrom subTO 返回内
                            subIndex++;
                            String replaceStr = item.getText().replaceAll("[\\pP‘’“”]", "").trim();
                            if (subFrom == null || subTo == null) {
                                continue;
                            }
                            if (matchList.contains(replaceStr) && subIndex >= subFrom && subIndex < subTo) {
                                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                                trigger.setRuleId(ruleId);
                                trigger.setType(business);
                                trigger.setParams(params);
                                returnTrigger.setType(business);
                                trigger.setValue(replaceStr);
                                buildRuleMap(item, trigger, ruleName, not);
                            }
                        }
                    }
                }
                break;

        }
        if (matchFlag) {
            returnTrigger.setTriggerCount(1);
            return returnTrigger;
        }
        return null;
    }

    private void doCsFirst(ItemTriggerV2VO trigger, String ruleName) {
        for (QcJobRecordDetailV2VO item : detailList) {
            if (CharacterEnum.CUSTOMER_SERVICE.equals(item.getType())) {
                buildRuleMap(item, trigger, ruleName);
                break;
            }
        }
    }

    public boolean compareEmotion(String emotion, String param) {
        String transfer = "平静";
        if ("生气".equalsIgnoreCase(emotion)) {
            transfer = "生气";
        }
        return transfer.equalsIgnoreCase(param);
    }

    public boolean compareQingxu(Integer count, List<String> params) {
        if (params == null || params.size() < 4) {
            return true;
        }
        String operator = params.get(2);
        switch (operator) {
            case ">=":
                if (count >= Integer.valueOf(params.get(3))) {
                    return true;
                }
                break;
            case "<=":
                if (count <= Integer.valueOf(params.get(3))) {
                    return true;
                }
                break;
        }
        return false;
    }

    public boolean compareTag(Integer count, List<String> params) {
        if (params == null || params.size() < 3) {
            return count > 0;
        }
        String operator = params.get(1);
        switch (operator) {
            case ">=":
                if (count >= Integer.valueOf(params.get(2))) {
                    return true;
                }
                break;
            case "<=":
                if (count <= Integer.valueOf(params.get(2))) {
                    return true;
                }
                break;
        }
        return false;
    }

    public boolean comareOne(Integer count, List<String> params) {
        if (params == null || params.size() < 3) {
            return true;
        }
        String operator = params.get(1);
        switch (operator) {
            case ">=":
                if (count >= Integer.valueOf(params.get(2))) {
                    return true;
                }
                break;
            case "<=":
                if (count <= Integer.valueOf(params.get(2))) {
                    return true;
                }
                break;
        }
        return false;
    }

    public boolean compare(Integer count, List<String> params) {
        if (params == null || params.size() < 5) {
            return true;
        }
        String operator = params.get(3);
        switch (operator) {
            case ">=":
                if (count >= Integer.valueOf(params.get(4))) {
                    return true;
                }
                break;
            case "<=":
                if (count <= Integer.valueOf(params.get(4))) {
                    return true;
                }
                break;
        }
        return false;
    }

    /**
     * 业务指标匹配
     *
     * @param params ["mei_ju_yu_su", "10", "20", ">=", 10]
     * @return
     */
    public ItemTriggerV2VO businessMatchAllDetail(Rule ruleItem, Long ruleId, String rule, List<String> params, String ruleName) {
        ItemTriggerV2VO returnTrigger = new ItemTriggerV2VO();
        returnTrigger.setRuleId(ruleId);
        returnTrigger.setParams(params);
        Integer count = 0;
        Integer index = -1;
        for (QcJobRecordDetailV2VO item : detailList) {
            index++;
            if (ruleItem.getFromIndex() != null && index < ruleItem.getFromIndex()) {
                continue;
            }
            if (ruleItem.getToIndex() != null && ruleItem.getToIndex() < index) {
                continue;
            }
            if (!item.getType().name().equalsIgnoreCase(rule)) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(params)) {
                String business = params.get(0);
                ItemTriggerV2VO trigger = new ItemTriggerV2VO();
                trigger.setRuleId(ruleId);
                trigger.setType(business);
                trigger.setParams(params);
                returnTrigger.setType(business);
                switch (business) {
                    case "mei_ju_yu_su":
                        //["mei_ju_yu_su", "10", "20", ">=", 10]
                        long time = item.getEndOffset() - item.getStartOffset();
                        long speed_avg = 60 * item.getText().length() * 1000 / (time <= 0 ? 1 : time);
                        if (speed_avg >= Long.valueOf(params.get(1)) && speed_avg <= Long.valueOf(params.get(2))) {
                            trigger.setValue(String.valueOf(speed_avg));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "zheng_ti_yu_su":
                        //todo 全局
                        //["zheng_ti_yu_su", "10", "20"]
                        break;
                    case "jing_yin_bi":
                        //todo 全局
                        break;
                    case "jing_yin_zong_shi_chang":
                        //todo 全局
                        break;
                    case "fu_wu_qiang_duan_shi_chang":
                        //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                        long timeInt = item.getIntDuration() / 1000;
                        if (timeInt >= Long.valueOf(params.get(1)) && timeInt <= Long.valueOf(params.get(2))) {
                            trigger.setValue(String.valueOf(timeInt));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "fu_wu_xiang_ying_shi_chang":
                        //["fu_wu_xiang_ying_shi_chang", "10", "20", ">=", "10"]
                        long timeRes = item.getResDuration() / 1000;
                        if (timeRes >= Long.valueOf(params.get(1)) && timeRes <= Long.valueOf(params.get(2))) {
                            trigger.setValue(String.valueOf(timeRes));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "mei_ju_yin_liang":
                        //["mei_ju_yin_liang", "10", "20", ">=", "10"]
                        if (item.getVoiceValue() >= Long.valueOf(params.get(1)) && item.getVoiceValue() <= Long.valueOf(params.get(2))) {
                            trigger.setValue(String.valueOf(item.getVoiceValue()));
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "zheng_ti_yin_liang":
                        //todo 全局
                        //["zheng_ti_yin_liang", "10", "20", ">=", "10"]
                        break;
                    case "chu_xian_xiao_sheng":
                        //["chu_xian_xiao_sheng", ">=", "10"]
                        break;
                    case "chu_xian_ke_sou_sheng":
                        //["chu_xian_ke_sou_sheng", ">=", "10"]
                        break;
                    case "dui_hua_lun_ci":
                        //todo 全局
                        //["dui_hua_lun_ci", ">=", "10"]
                        break;
                    case "ke_fu_qing_xu":
                        //["ke_fu_qing_xu", "平静", ">=", "10"]
                        item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                        if (compareEmotion(item.getEmotion().getDesc(), params.get(1))) {
                            trigger.setValue(item.getEmotion().getDesc());
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "ke_hu_qing_xu":
                        //["ke_hu_qing_xu", "平静", ">=", "10"]
                        item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                        if (compareEmotion(item.getEmotion().getDesc(), params.get(1))) {
                            trigger.setValue(item.getEmotion().getDesc());
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                    case "ti_ji_shou_ji_hao":
                        //["ti_ji_shou_ji_hao"]
                        String result = PhoneNumberUtils.getPhoneNumberFromText(item.getText());
                        if (StringUtils.isNotEmpty(result)) {
                            trigger.setValue(result);
                            buildRuleMap(item, trigger, ruleName);
                            count++;
                        }
                        break;
                }
            }
        }
        if (count > 0) {
            returnTrigger.setTriggerCount(count);
            return returnTrigger;
        } else {
            return null;
        }
    }

    /**
     * 业务指标匹配
     *
     * @param params ["mei_ju_yu_su", "10", "20", ">=", 10]
     * @param item
     * @return
     */
    public ItemTriggerV2VO businessMatch(Long ruleId, Boolean not, List<String> params, QcJobRecordDetailV2VO item, String ruleName) {
        if (CollectionUtils.isNotEmpty(params) && params.size() > 1) {
            String business = params.get(0);
            ItemTriggerV2VO trigger = new ItemTriggerV2VO();
            trigger.setRuleId(ruleId);
            trigger.setType(business);
            trigger.setParams(params);
            switch (business) {
                case "mei_ju_yu_su":
                    //["mei_ju_yu_su", "10", "20", ">=", 10]
                    long time = item.getEndOffset() - item.getStartOffset();
                    long speed_avg = 60 * item.getText().length() * 1000 / (time <= 0 ? 1 : time);
                    if (notAndContion(not, speed_avg >= Long.valueOf(params.get(1)) && speed_avg <= Long.valueOf(params.get(2)))) {
                        trigger.setValue(String.valueOf(speed_avg));
                        buildRuleMap(item, trigger, ruleName);
                        return trigger;
                    }
                    break;
                case "zheng_ti_yu_su":
                    //todo 全局
                    //["zheng_ti_yu_su", "10", "20"]
                    break;
                case "jing_yin_bi":
                    //todo 全局
                    break;
                case "jing_yin_zong_shi_chang":
                    //todo 全局
                    break;
                case "fu_wu_qiang_duan_shi_chang":
                    //["fu_wu_qiang_duan_shi_chang", "10", "20", ">=", "10"]
                    long timeInt = item.getIntDuration() / 1000;
                    if (notAndContion(not, timeInt >= Long.valueOf(params.get(1)) && timeInt <= Long.valueOf(params.get(2)))) {
                        trigger.setValue(String.valueOf(timeInt));
                        buildRuleMap(item, trigger, ruleName);
                        return trigger;
                    }
                    break;
                case "fu_wu_xiang_ying_shi_chang":
                    //["fu_wu_xiang_ying_shi_chang", "10", "20", ">=", "10"]
                    long timeRes = item.getResDuration() / 1000;
                    if (notAndContion(not, timeRes >= Long.valueOf(params.get(1)) && timeRes <= Long.valueOf(params.get(2)))) {
                        trigger.setValue(String.valueOf(timeRes));
                        buildRuleMap(item, trigger, ruleName);
                        return trigger;
                    }
                    break;
                case "mei_ju_yin_liang":
                    //["mei_ju_yin_liang", "10", "20", ">=", "10"]
                    if (notAndContion(not, item.getVoiceValue() >= Long.valueOf(params.get(1)) && item.getVoiceValue() <= Long.valueOf(params.get(2)))) {
                        trigger.setValue(String.valueOf(item.getVoiceValue()));
                        buildRuleMap(item, trigger, ruleName);
                        return trigger;
                    }
                    break;
                case "zheng_ti_yin_liang":
                    //todo 全局
                    //["zheng_ti_yin_liang", "10", "20", ">=", "10"]
                    break;
                case "chu_xian_xiao_sheng":
                    //["chu_xian_xiao_sheng", ">=", "10"]
                    break;
                case "chu_xian_ke_sou_sheng":
                    //["chu_xian_ke_sou_sheng", ">=", "10"]
                    break;
                case "dui_hua_lun_ci":
                    //todo 全局
                    //["dui_hua_lun_ci", ">=", "10"]
                    break;
                case "ke_fu_qing_xu":
                    //["ke_fu_qing_xu", "平静", ">=", "10"]
                    item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                    if (notAndContion(not, compareEmotion(item.getEmotion().getDesc(), params.get(1)))) {
                        trigger.setValue(item.getEmotion().getDesc());
                        buildRuleMap(item, trigger, ruleName, not);
                        return trigger;
                    }
                    break;
                case "ke_hu_qing_xu":
                    //["ke_hu_qing_xu", "平静", ">=", "10"]
                    item.setEmotion(CustomerInfoRecognitionUtil.getEmotion(Arrays.asList(item.getText())));
                    if (notAndContion(not, compareEmotion(item.getEmotion().getDesc(), params.get(1)))) {
                        trigger.setValue(item.getEmotion().getDesc());
                        buildRuleMap(item, trigger, ruleName, not);
                        return trigger;
                    }
                    break;
                case "ti_ji_shou_ji_hao":
                    //["ti_ji_shou_ji_hao"]
                    String result = PhoneNumberUtils.getPhoneNumberFromText(item.getText());
                    if (StringUtils.isNotEmpty(result)) {
                        trigger.setValue(result);
                        buildRuleMap(item, trigger, ruleName);
                        return trigger;
                    }
                    break;
            }
            return null;
        } else {
            return null;
        }
    }

    /**
     * #hui_hua_zhong_zhi_fan：会话终止方 hui_hua_fa_qi_fang:会话发起方  zhuan_jie_qing_kuang:转接情况 shou_ci_lai_fang: 首次来访
     *
     * @param rule
     * @return
     */
    private ItemTriggerV2VO featuresMatch(Rule rule) {
        List<String> params = rule.getParams();
        if (params == null || params.size() <= 0 || qcJobRecordV2PO == null) {
            return null;
        }
        String features = params.get(0);
        String value = params.get(1);
        String transfer = null;
        if (params.size() > 2) {
            transfer = params.get(1);
            value = params.get(2);
        }
        ItemTriggerV2VO trigger = new ItemTriggerV2VO();
        trigger.setRuleId(rule.getRuleId());
        trigger.setType(features);
        trigger.setParams(params);
        trigger.setTriggerCount(1);
        switch (features) {
            case "zuo_xi_zhong_zhi":
                if (QcSoundChannelEnum.CS.equals(qcJobRecordV2PO.getRefHangUp())) {
                    return trigger;
                }
                break;
            case "ke_hu_zhong_zhi":
                if (QcSoundChannelEnum.CUSTOMER.equals(qcJobRecordV2PO.getRefHangUp())) {
                    return trigger;
                }
                break;
            case "wu_zhuan_jie":
                if (QcTransferTypeEnum.NONE.equals(qcJobRecordV2PO.getTransfer())) {
                    return trigger;
                }
                break;
            case "jin_zhuan_jie":
                if (QcTransferTypeEnum.TRANSFER_OUT.equals(qcJobRecordV2PO.getTransfer())) {
                    return trigger;
                }
                break;
            case "jin_bei_zhuan_jie":
                if (QcTransferTypeEnum.TRANSFER_IN.equals(qcJobRecordV2PO.getTransfer())) {
                    return trigger;
                }
                break;
            case "bei_zhuan_jie_qie_zhuan_jie":
                if (QcTransferTypeEnum.TRANSFER_IN_OUT.equals(qcJobRecordV2PO.getTransfer())) {
                    return trigger;
                }
                break;
            case "chong_fu_lai_fang":
                if (qcJobRecordV2PO.getVisitCount() != null && qcJobRecordV2PO.getVisitCount() > 0) {
                    return trigger;
                }
                break;
            case "sou_ci_lai_fang":
                if (qcJobRecordV2PO.getVisitCount() != null && qcJobRecordV2PO.getVisitCount() == 0) {
                    return trigger;
                }
                break;
            case "hui_hua_zhong_zhi_fang":
                if (QcSoundChannelEnum.CS.equals(qcJobRecordV2PO.getRefHangUp()) && "zuo_xi".equalsIgnoreCase(value)) {
                    return trigger;
                }
                if (QcSoundChannelEnum.CUSTOMER.equals(qcJobRecordV2PO.getRefHangUp()) && "ke_hu".equalsIgnoreCase(value)) {
                    return trigger;
                }
                break;
            case "hui_hua_fa_qi_fang":
                if (QcSoundChannelEnum.CS.equals(qcJobRecordV2PO.getRefBeginer()) && "zuo_xi".equalsIgnoreCase(value)) {
                    return trigger;
                }
                if (QcSoundChannelEnum.CUSTOMER.equals(qcJobRecordV2PO.getRefBeginer()) && "ke_hu".equalsIgnoreCase(value)) {
                    return trigger;
                }
                break;
            case "zhuan_jie_qing_kuang":
                //主动转接
                if ("zhu_dong_zhuan_jie".equalsIgnoreCase(transfer)) {
                    if (zhuDongZhuanJie() && "shi".equalsIgnoreCase(value)) {
                        return trigger;
                    }
                    if (!zhuDongZhuanJie() && "fou".equalsIgnoreCase(value)) {
                        return trigger;
                    }
                }
                //被转接
                if ("bei_zhuan_jie".equalsIgnoreCase(transfer)) {
                    if (beiZhuanJie() && "shi".equalsIgnoreCase(value)) {
                        return trigger;
                    }
                    if (!beiZhuanJie() && "fou".equalsIgnoreCase(value)) {
                        return trigger;
                    }
                }
                break;
            case "shou_ci_lai_fang":
                if (qcJobRecordV2PO.getVisitCount() != null && qcJobRecordV2PO.getVisitCount() == 0 && "shi".equalsIgnoreCase(value)) {
                    return trigger;
                }
                if (qcJobRecordV2PO.getVisitCount() != null && qcJobRecordV2PO.getVisitCount() > 0 && "fou".equalsIgnoreCase(value)) {
                    return trigger;
                }
                break;
        }
        return null;
    }

    private boolean zhuDongZhuanJie() {
        if (QcTransferTypeEnum.TRANSFER_OUT.equals(qcJobRecordV2PO.getTransfer())) {
            return true;
        }
        if (QcTransferTypeEnum.TRANSFER_IN_OUT.equals(qcJobRecordV2PO.getTransfer())) {
            return true;
        }
        return false;
    }

    private boolean beiZhuanJie() {
        if (QcTransferTypeEnum.TRANSFER_IN.equals(qcJobRecordV2PO.getTransfer())) {
            return true;
        }
        if (QcTransferTypeEnum.TRANSFER_IN_OUT.equals(qcJobRecordV2PO.getTransfer())) {
            return true;
        }
        return false;
    }

    @Data
    public static class Matches {
        private TagMatchBO intent;
        private List<TagMatchBO> intent_ranking;
        private List<TagMatchBO> matches;
    }


    @Data
    public static class TagMatchBO {
        private String name;
        private String title;
        private Double confidence;
        private String simSentence;
    }

    @Data
    public static class TagMatchResult {
        private String keyWords;
        private Double confidence;
    }

    //textType  1:纯文本；2:图片；3:语音；4:文件；5:视频；100/118:工单；115:富文本
    private boolean textQcWithType(QcJobRecordDetailV2VO detailV2VO) {
        if (detailV2VO == null) {
            return true;
        }
        if (detailV2VO.getTextType() == null) {
            return true;
        }
        //2:图片；3:语音；4:文件；5:视频 不进行文本质检, 115:富文本 只质检其中文本部分
        if (detailV2VO.getTextType() == 2 || detailV2VO.getTextType() == 3 || detailV2VO.getTextType() == 4
                || detailV2VO.getTextType() == 5) {
            logger.info("当前文本类型为:" + detailV2VO.getTextType());
            return false;
        }
        return true;
    }

    private String textQcWithRichText(QcJobRecordDetailV2VO detailV2VO) {
        if (detailV2VO.getTextType() != null && detailV2VO.getTextType() == 115) {
            //富文本处理
            try {
                if (StringUtils.isNoneEmpty(detailV2VO.getText())) {
                    JSONObject jsonObject = JSONObject.parseObject(detailV2VO.getText());
                    String context = jsonObject.getString("content");
                    String res = dealContent(context);
                    logger.info("富文本提取：" + res);
                    res = res.replaceAll("\\n", "");
                    return res;
                }
            } catch (Exception e) {
                logger.error("textQcWithRichText:", e);
            }
        }
        //替换掉换行符
        String text = detailV2VO.getText();
        text = text.replaceAll("\\n", "");
        return text;
    }

    public static String dealContent(String content) {
        // 匹配标签
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            // 替换图片
            content = matcher.replaceAll("").replace(" ", "");
        }

        return content;
    }

    public Integer getChatRound() {
        return chatRound;
    }

    public Map<Long, List<ItemTriggerV2VO>> getRuleTriggerMap() {
        return ruleTriggerMap;
    }

    public List<QcJobRecordDetailV2VO> getDetailList() {
        return detailList;
    }

    public Integer getSumScore() {
        return sumScore;
    }

    public QcJobRecordStatusEnum getQcJobRecordStatusEnum() {
        return qcJobRecordStatusEnum;
    }

    public String getFailReason() {
        return failReason;
    }

    public List<ItemTriggerV2VO> getTriggerList() {
        return triggerList;
    }

    public Long getChatDuration() {
        return chatDuration;
    }

    public Integer getSpeed() {
        return speed;
    }

    public Integer getMuteRate() {
        return muteRate;
    }

    public Integer getMuteDuration() {
        return muteDuration;
    }

    public Long getVoiceVolume() {
        return voiceVolume;
    }

    public CustomerEmotionEnum getCsEmotion() {
        return csEmotion;
    }

    public CustomerEmotionEnum getPersonEmotion() {
        return personEmotion;
    }

    public Map<Long, Long> getTagMatchCount() {
        return tagMatchCount;
    }

    public void setQcTagThreshold(Double qcTagThreshold) {
        this.qcTagThreshold = qcTagThreshold;
    }

    public void setEnableQcTagThreshold(Boolean enableQcTagThreshold) {
        this.enableQcTagThreshold = enableQcTagThreshold;
    }
}
