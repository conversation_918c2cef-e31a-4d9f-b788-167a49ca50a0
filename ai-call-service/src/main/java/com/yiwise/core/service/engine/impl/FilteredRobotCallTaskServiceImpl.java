package com.yiwise.core.service.engine.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.yiwise.base.common.helper.ServerInfoConstants;
import com.yiwise.base.common.utils.HandleByPageUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.blacklist.api.dto.CheckBlackListRuleRequestDTO;
import com.yiwise.blacklist.api.dto.CheckBlackListRuleResponseDTO;
import com.yiwise.blacklist.api.enums.ComplaintTypeEnum;
import com.yiwise.blacklist.api.enums.YiwiseBlackListRuleCallerEnum;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.config.BizOrikaConvert;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.dao.FilteredRobotCallTaskPOMapper;
import com.yiwise.core.dal.entity.AreaPO;
import com.yiwise.core.dal.dao.RobotCallJobPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.feignclient.blacklist.YiwiseBlackListClient;
import com.yiwise.core.feignclient.rcs.CustomerRealWhiteApiClient;
import com.yiwise.core.feignclient.rcs.DouDianCrowdClient;
import com.yiwise.core.feignclient.rcs.RcsApiClient;
import com.yiwise.core.helper.*;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.filteredtask.FilteredRobotCallTaskBO;
import com.yiwise.core.model.bo.phonenumber.PhoneHomeLocationBO;
import com.yiwise.core.model.bo.robotcalltask.PreFilterTaskBO;
import com.yiwise.core.model.bo.robotcalltask.RunTimeRobotCallTaskBO;
import com.yiwise.core.model.bo.wexin.CallJobAddWechatConfigBO;
import com.yiwise.core.model.bo.wexin.ScrmWechatBO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dto.CommonDecryptResultDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.robotcalljob.WphPushTypeEnum;
import com.yiwise.core.model.enums.wechat.ScrmAddWechatTypeEnum;
import com.yiwise.core.model.request.FilterRecordRequest;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.filteredtask.FilterRecordVO;
import com.yiwise.core.model.vo.filteredtask.FilteredRobotCallTaskOpenApiQueryVO;
import com.yiwise.core.model.vo.filteredtask.FilteredRobotCallTaskQueryVO;
import com.yiwise.core.model.vo.openapi.FilteredRobotCallTaskApiVO;
import com.yiwise.core.model.vo.phonenumber.TenantPhoneNumberWithPhoneInfoPO;
import com.yiwise.core.model.vo.qf.*;
import com.yiwise.core.model.vo.wph.AiCallRiskCheckResultVO;
import com.yiwise.core.model.vo.wph.CallOutInterceptResultDTO;
import com.yiwise.core.model.vo.wph.WphBlackCheckResultVO;
import com.yiwise.core.model.vo.wph.WphCustomInterceptConfigVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.customerextrainfo.CustomerPersonExtraInfoService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.TenantEncryptConfigureService;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.scrm.TenantScrmService;
import com.yiwise.core.service.thirdparty.FeishuSendMsgService;
import com.yiwise.core.util.AiccStringUtils;
import com.yiwise.core.util.IllegalNameReplaceUtil;
import com.yiwise.core.util.ObjUtil;
import com.yiwise.core.utils.CdpEnumUtil;
import com.yiwise.customer.data.platform.rpc.api.service.dto.DouDianOutboundDTO;
import com.yiwise.customer.data.platform.rpc.api.service.enums.ChannelTypeEnum;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.customer.data.platform.rpc.api.service.vo.DoudianOutboundRequestVO;
import com.yiwise.rcs.api.dto.*;
import com.yiwise.rcs.api.enums.CustomizeAccountTypeEnum;
import com.yiwise.rcs.api.enums.ModelTypeEnum;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import com.yiwise.core.util.IllegalNameReplaceUtil;
import com.yiwise.rcs.api.dto.PhoneNumberLocationDTO;
import com.yiwise.rcs.api.dto.RcsCheckReqDTO;
import com.yiwise.rcs.api.dto.RcsCheckResDTO;
import org.apache.avro.data.Json;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.core.batch.common.BatchConstant.FILTERED_TASK_EXPORT_LIST;
import static com.yiwise.core.config.ApplicationConstant.RCS_RUNTIME_EXCEPTION_WARN_FEI_SHU_WEBHOOK;
import static com.yiwise.core.config.CommonApplicationConstant.EXPORT_FILE_PREFIX;

/**
 * @Author: zqy
 * @Date: 2020/11/30 17:55
 */
@Service
public class FilteredRobotCallTaskServiceImpl extends BasicServiceImpl<FilteredRobotCallTaskPO> implements FilteredRobotCallTaskService {
    private static final Logger logger = LoggerFactory.getLogger(FilteredRobotCallTaskServiceImpl.class);
    @Resource
    private FilteredRobotCallTaskPOMapper filteredRobotCallTaskPOMapper;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private PhoneLocationService phoneLocationService;
    @Resource
    private BatchJobInQueueService batchJobInQueueService;
    @Resource
    private TenantPhoneNumberService tenantPhoneNumberService;
    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private UserService userService;
    @Lazy
    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private CustomerPersonService customerPersonService;
    @Resource
    private SmsJobService smsJobService;
    @Lazy
    @Resource
    private CallRecordInfoService callRecordInfoService;
    @Resource
    private TenantScrmService tenantScrmService;
    @Resource
    private CustomerPersonExtraInfoService customerPersonExtraInfoService;
    @Resource
    private RcsApiClient rcsApiClient;

    @Autowired
    @Qualifier("filteredTaskExportExcelJob")
    private Job filteredTaskExportExcelJob;

    @Autowired
    @Qualifier("filteredTaskImportToSmsJob")
    private Job filteredTaskImportToSmsJob;

    @Resource
    private TenantEncryptConfigureService tenantEncryptConfigureService;

    @Resource
    private RobotCallJobPOMapper robotCallJobPOMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private DouDianCrowdClient douDianCrowdClient;
    @Resource
    private BizOrikaConvert bizOrikaConvert;

    @Resource
    private WeiPinHuiNewHelper weiPinHuiNewHelper;
    @Resource
    private YiwiseBlackListClient yiwiseBlackListClient;
    @Resource
    private CustomerRealWhiteApiClient customerRealWhiteClient;
    @Resource
    private FeishuSendMsgService feishuSendMsgService;

    // save
    // select by job
    @Override
    public PageResultObject<FilteredRobotCallTaskBO> selectFilteredTaskListByJobId(FilteredRobotCallTaskQueryVO query) {
        Optional<List<Long>> authUserIds = dataAccessControlService.getAuthUserIdListWithTenantId(query.getUserId(), query.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_organization);
        authUserIds.ifPresent(query::setUserIdList);
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<FilteredRobotCallTaskBO> filteredRobotCallTaskBOS = filteredRobotCallTaskPOMapper.selectFilteredTaskByJobId(query);
        resolveFilteredTaskInfo(filteredRobotCallTaskBOS);
        return PageResultObject.of(filteredRobotCallTaskBOS);
    }

    @Override
    public void resolveFilteredTaskInfo(List<? extends FilteredRobotCallTaskBO> filteredRobotCallTaskBOS) {
        if (CollectionUtils.isEmpty(filteredRobotCallTaskBOS)) {
            return;
        }
        // location
        Set<String> areaCodeSet = filteredRobotCallTaskBOS.stream().map(FilteredRobotCallTaskBO::getAreaCode).collect(Collectors.toSet());
        List<PhoneLocationPO> phoneLocationList = phoneLocationService.selectPhoneLocationByAreaCodeList(new ArrayList<>(areaCodeSet));
        Map<String, PhoneLocationPO> stringPhoneLocationPOMap = MyCollectionUtils.listToMap(phoneLocationList, PhoneLocationPO::getAreaCode);
        // tenantPhoneNumber
        List<Long> tenantPhoneNumberIds = filteredRobotCallTaskBOS.stream().map(FilteredRobotCallTaskBO::getTenantPhoneNumberId).collect(Collectors.toList());
        Map<? extends Serializable, TenantPhoneNumberWithPhoneInfoPO> tenantPhoneNumberMap = tenantPhoneNumberService.selectMapWithPhoneByKeyCollect(new ArrayList<>(tenantPhoneNumberIds));
        // robotCallJobName
        List<Long> robotCallJobId = filteredRobotCallTaskBOS.stream().map(FilteredRobotCallTaskBO::getRobotCallJobId).collect(Collectors.toList());
        Map<? extends Serializable, RobotCallJobPO> robotCallJobPOMap = robotCallJobService.selectMapByKeyCollect(robotCallJobId);
        // dialogFlowName
        List<Long> dialogFlowId = filteredRobotCallTaskBOS.stream().map(FilteredRobotCallTaskBO::getDialogFlowId).collect(Collectors.toList());
        Map<? extends Serializable, DialogFlowInfoPO> dialogFlowInfoPOMap = dialogFlowService.selectMapByKeyCollect(dialogFlowId);

        Set<Long> customerPersonIdSet = filteredRobotCallTaskBOS.stream().map(FilteredRobotCallTaskBO::getCustomerPersonId).filter(item -> item != null && item > 0).collect(Collectors.toSet());
        Map<Long, AccountVO> accountVOMap = customerPersonService.getCustomerPersonByIds(customerPersonIdSet);

        // 赋值
        for (FilteredRobotCallTaskBO item : filteredRobotCallTaskBOS) {
            if (FilterTypeEnum.BLACK_LIST.equals(item.getFilterType()) || FilterTypeEnum.BLACK_LIST_SMS.equals(item.getFilterType())) {
                item.setFilterReason(null);
            }
            AccountVO accountVO = accountVOMap.get(item.getCustomerPersonId());
            if (Objects.nonNull(accountVO)) {
                item.setCreateSources(accountVO.getCreateSources());
                item.setAlternatePhoneNumbers(accountVO.getMobile());
            }
            // location
            PhoneLocationPO phoneLocationPO = stringPhoneLocationPOMap.get(item.getAreaCode());
            if (phoneLocationPO != null) {
                if (Objects.equals(phoneLocationPO.getProvince(), phoneLocationPO.getCity())) {
                    item.setLocation(phoneLocationPO.getCity());
                } else {
                    item.setLocation(phoneLocationPO.getProvince() + phoneLocationPO.getCity());
                }
            }
            // tenantPhoneNumberName
            TenantPhoneNumberWithPhoneInfoPO tenantPhoneNumberPO = tenantPhoneNumberMap.get(item.getTenantPhoneNumberId());
            if (tenantPhoneNumberPO != null) {
                item.setTenantPhoneNumber(tenantPhoneNumberPO.getPhoneNumber());
                item.setPhoneName(tenantPhoneNumberPO.getPhoneName());
                item.setPhoneType(tenantPhoneNumberPO.getPhoneType());
            }
            // robotCallJobName
            RobotCallJobPO robotCallJobPO = robotCallJobPOMap.get(item.getRobotCallJobId());
            if (robotCallJobPO != null) {
                item.setRobotCallJobName(robotCallJobPO.getName());
            }
            // dialogFlowName
            DialogFlowInfoPO dialogFlowInfoPO = dialogFlowInfoPOMap.get(item.getDialogFlowId());
            if (dialogFlowInfoPO != null) {
                item.setDialogFlowName(dialogFlowInfoPO.getName());
            }

            // 添加过滤原因 给导出用
            item.setExportFilterReason(item.getFilterReason());
        }
    }


    public FilteredRobotCallTaskPO addFilteredTask(RobotCallTaskPO robotCallTaskPO, AccountVO accountVO, FilterTypeEnum filterType, String filterReason, Long tenantPhoneNumberId, PhoneHomeLocationBO phoneHomeLocationBO) {
        return addFilteredTask(robotCallTaskPO, accountVO, filterType, null, filterReason, tenantPhoneNumberId, phoneHomeLocationBO);
    }

    private FilteredRobotCallTaskPO addFilteredTask(RobotCallTaskPO robotCallTaskPO, AccountVO accountVO, FilterTypeEnum filterType, FilterInterceptTypeEnum interceptType,
                                                    String filterReason, Long tenantPhoneNumberId, PhoneHomeLocationBO phoneHomeLocationBO) {
        FilteredRobotCallTaskPO filteredRobotCallTaskPO = new FilteredRobotCallTaskPO();
        filteredRobotCallTaskPO.setTenantId(robotCallTaskPO.getTenantId());
        filteredRobotCallTaskPO.setRobotCallJobId(robotCallTaskPO.getRobotCallJobId());
        filteredRobotCallTaskPO.setRobotCallTaskId(robotCallTaskPO.getRobotCallTaskId());
        filteredRobotCallTaskPO.setDialogFlowId(robotCallTaskPO.getDialogFlowId());
        filteredRobotCallTaskPO.setCreateUserId(robotCallTaskPO.getCreateUserId());
        filteredRobotCallTaskPO.setUpdateUserId(robotCallTaskPO.getUpdateUserId());
        filteredRobotCallTaskPO.setCalledPhoneNumber(robotCallTaskPO.getCalledPhoneNumber());
        filteredRobotCallTaskPO.setFilterType(filterType);
        filteredRobotCallTaskPO.setInterceptType(interceptType);
        filteredRobotCallTaskPO.setFilterReason(AiccStringUtils.checkAndSubString(filterReason, 100));
        filteredRobotCallTaskPO.setTenantPhoneNumberId(tenantPhoneNumberId);
        // 内置变量不算在内
        filteredRobotCallTaskPO.setProperties(robotCallTaskPO.getProperties());
        Long customerPersonId = robotCallTaskPO.getCustomerPersonId();
        filteredRobotCallTaskPO.setCustomerPersonId(customerPersonId);
        if (accountVO != null) {
            filteredRobotCallTaskPO.setAreaCode(accountVO.getAreaCode());
            filteredRobotCallTaskPO.setGender(CdpEnumUtil.changeGenderToAicc(accountVO.getGender()));
            filteredRobotCallTaskPO.setCustomerPersonName(AccountHelper.getAccountName(accountVO, 100));
        }
        // 如果号码归属地不为空 则以号码归属地为准
        if (phoneHomeLocationBO != null) {
            filteredRobotCallTaskPO.setAreaCode(phoneHomeLocationBO.getAreaCode());
        }
        FilteredRobotCallTaskPO.DeploymentInformation deploymentInformation = new FilteredRobotCallTaskPO.DeploymentInformation();
        deploymentInformation.setLogId(MDC.get("MDC_LOG_ID"));
        deploymentInformation.setServerName(ServerInfoConstants.SERVER_HOSTNAME);
        deploymentInformation.setIpAddress(ServerInfoConstants.SERVER_IP_ADDRESS);
        filteredRobotCallTaskPO.setDeploymentInformation(deploymentInformation);
        FilteredRobotCallTaskPO lastFilter = checkFilteredTaskByTaskId(robotCallTaskPO.getTenantId(), robotCallTaskPO.getRobotCallJobId(), robotCallTaskPO.getRobotCallTaskId());
        if (lastFilter != null) {
            filteredRobotCallTaskPO.setFilterCount(lastFilter.getFilterCount() == null ? 2 : lastFilter.getFilterCount() + 1);
            // 是否外呼状态沿用上一次
            filteredRobotCallTaskPO.setCalled(lastFilter.getCalled());
        } else {
            filteredRobotCallTaskPO.setCalled(callRecordInfoService.checkExistByRobotCallTaskId(robotCallTaskPO.getTenantId(), robotCallTaskPO.getRobotCallTaskId()));
            filteredRobotCallTaskPO.setFilterCount(1L);
        }
        // 最后一次过滤
        filteredRobotCallTaskPO.setLastFilter(true);
        saveNotNull(filteredRobotCallTaskPO);
        if (lastFilter != null) {
            // 将其他过滤设置为非最后一次
            setNotLastFilter(filteredRobotCallTaskPO.getTenantId(), filteredRobotCallTaskPO.getRobotCallTaskId(), filteredRobotCallTaskPO.getRobotCallJobId(), filteredRobotCallTaskPO.getFilteredRobotCallTaskId());
        }

        read(filteredRobotCallTaskPO.getRobotCallJobId(), false);
        filteredRobotCallTaskPO.setCreateTime(LocalDateTime.now());
        return filteredRobotCallTaskPO;
    }

    @Override
    public FilteredRobotCallTaskPO rcsFilterTask(RunTimeRobotCallTaskBO robotCallTaskPO,
                                                 RobotCallJobPO robotCallJobPO,
                                                 AccountVO accountVO,
                                                 Set<String> needProperties,
                                                 TenantPO tenantInfo,
                                                 RiskControlStrategyDTO systemRcs) {
        //前置批量过滤
        if (Objects.nonNull(robotCallTaskPO.getCallPreFilterTaskBO()) && BooleanUtils.isTrue(robotCallTaskPO.getCallPreFilterTaskBO().getPreFilter())) {
            return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, robotCallTaskPO.getCallPreFilterTaskBO().getPreFilterReason(), null, null);
        }
        //客户自有方案加密
        FilteredRobotCallTaskPO tenantEncryptFiltered = doTenantEncryptConfigure(robotCallTaskPO, accountVO, tenantInfo);
        if (Objects.nonNull(tenantEncryptFiltered)) {
            return tenantEncryptFiltered;
        }
        PhoneHomeLocationBO phoneHomeLocationBO = null;
        try {
            String calledPhoneNumberTmp = robotCallTaskPO.getCalledPhoneNumber();
            if (RsaHelper.isRsaTenantPO(tenantInfo) || Objects.equals(tenantInfo.getEncryptType(), TenantEncryptTypeEnum.CUSTOMER_ENCRYPT) || Objects.equals(tenantInfo.getEncryptType(),TenantEncryptTypeEnum.MD5)) {
               calledPhoneNumberTmp = robotCallTaskPO.getRealCalledPhoneNumber();
            }
            phoneHomeLocationBO = phoneLocationService.getLocationOrNewByPhoneNumber(calledPhoneNumberTmp);
        } catch (Exception e) {
            logger.error("getLocationOrNewByPhoneNumber error", e);
        }
        //判断是否是分机号外呼
        robotCallTaskPO.setExtensionMode(ExtensionHelper.isExtensionCall(tenantInfo, StringUtils.isNotBlank(robotCallTaskPO.getRealCalledPhoneNumber()) ? robotCallTaskPO.getRealCalledPhoneNumber() : robotCallTaskPO.getCalledPhoneNumber()));
        boolean weipinhuiTenant = WeiPinHuiHelper.isWeipinhuiTenantWithPO(tenantInfo);
        List<String> ttsFilterValues = new ArrayList<>();
        //处理欧莱雅唯品会等客户定制化拦截
        FilteredRobotCallTaskPO customFiltered = doCustomInterceptCheck(robotCallTaskPO, accountVO, phoneHomeLocationBO, robotCallJobPO, weipinhuiTenant, needProperties, ttsFilterValues);
        if (Objects.nonNull(customFiltered)) {
            return customFiltered;
        }

        //自定义变量校验
        FilteredRobotCallTaskPO checkPropertiesResult = checkProperties(robotCallTaskPO, accountVO, needProperties, phoneHomeLocationBO);
        if (Objects.nonNull(checkPropertiesResult)) {
            return checkPropertiesResult;
        }

        //rcsCheck
        FilteredRobotCallTaskPO rcsFiltered = rcsCheck(robotCallTaskPO, robotCallJobPO, accountVO, phoneHomeLocationBO, weipinhuiTenant, ttsFilterValues,tenantInfo);
        if (Objects.nonNull(rcsFiltered)) {
            return rcsFiltered;
        }
        //大数据风控
        FilteredRobotCallTaskPO checkedDataRcs = checkBlackListRule(robotCallTaskPO, robotCallJobPO, accountVO, phoneHomeLocationBO, tenantInfo, systemRcs);
        if (Objects.nonNull(checkedDataRcs)) {
            return checkedDataRcs;
        }

        // 加微账号 使用第一象限自动加微 指定账号 校验
        String filterReason = checkWechatAccount(robotCallJobPO, robotCallTaskPO);
        if (filterReason != null) {
            logger.info("第一象限自动加微指定账号过滤 filterReason={}", filterReason);
            return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.INVALID_ADD_WECHAT_ACCOUNT, filterReason, null, phoneHomeLocationBO);
        }

        //todo 唯品会共享黑名单和呼频数据过滤
        if (weipinhuiTenant && Objects.nonNull(robotCallJobPO.getEnableRemoteFilter())
                && Objects.equals(Boolean.TRUE, robotCallJobPO.getEnableRemoteFilter())) {
            String usedPhoneNumber = robotCallTaskPO.getCalledPhoneNumber();
            String uid = getCustomizeEntity(true, robotCallTaskPO).getCustomizeValue();
            //共享黑名单过滤
            AiCallRiskCheckResultVO resultVO = WeiPinHuiHelper.checkLocalBlackList(usedPhoneNumber);
            if (!resultVO.getIsPass()) {
                if (StringUtils.isNotBlank(resultVO.getMsg())) {
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, "接口异常拦截", null, phoneHomeLocationBO);
                } else {
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, "本地共享黑名单拦截", null, phoneHomeLocationBO);
                }
            }

            //获取风控策略配置
            Long riskControlStrategyId = robotCallJobPO.getRiskControlStrategyId();
            if (robotCallJobPO.getRiskControlStrategyId() == null) {
                RiskControlStrategyDTO strategyDTO = rcsApiClient.getSystemRcs(robotCallTaskPO.getTenantId(), ModelTypeEnum.CALL);
                if (Objects.isNull(strategyDTO)) {
                    logger.error("系统风控策略为null");
                    return null;
                } else {
                    riskControlStrategyId = strategyDTO.getRiskControlStrategyId();
                }
            }
            RiskControlStrategyDTO riskControlStrategy = rcsApiClient.getRcsDetail(riskControlStrategyId);
            if (Objects.nonNull(riskControlStrategy) && CollectionUtils.isNotEmpty(riskControlStrategy.getIntercept())) {
                for (InterceptDTO interceptDTO : riskControlStrategy.getIntercept()) {
                    Map<Integer, Tuple2<CallOutInterceptDTO, CallOutInterceptResultDTO>> cacheMap = new HashMap<>();
                    //获取callout次数
                    if (Objects.nonNull(interceptDTO.getInterceptCallOutSwitch())
                            && interceptDTO.getInterceptCallOutSwitch()
                            && interceptDTO.getInterceptCallOutDays() > 0) {
                        int days = interceptDTO.getInterceptCallOutDays();
                        int count = interceptDTO.getInterceptCallOutCount();
                        Tuple2<FilterTypeEnum, String> result = riskCheck(cacheMap, days, count, robotCallTaskPO.getTenantId(), usedPhoneNumber, FilterInterceptTypeEnum.CALL_OUT, uid);
                        if (Objects.nonNull(result)) {
                            if (robotCallJobPO.getRiskControlStrategyId() == null) {
                                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_INTERCEPT, FilterInterceptTypeEnum.CALL_OUT, result._2, null, phoneHomeLocationBO);
                            } else {
                                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.INTERCEPT, FilterInterceptTypeEnum.CALL_OUT, result._2, null, phoneHomeLocationBO);
                            }
                        }
                    }
                    //获取接通次数
                    if (Objects.nonNull(interceptDTO.getInterceptGetThroughSwitch())
                            && interceptDTO.getInterceptGetThroughSwitch()
                            && interceptDTO.getInterceptGetThroughCount() > 0) {
                        int days = interceptDTO.getInterceptGetThroughDays();
                        int count = interceptDTO.getInterceptGetThroughCount();
                        Tuple2<FilterTypeEnum, String> result = riskCheck(cacheMap, days, count, robotCallTaskPO.getTenantId(), usedPhoneNumber, FilterInterceptTypeEnum.GET_THROUGH, uid);
                        if (Objects.nonNull(result)) {
                            if (robotCallJobPO.getRiskControlStrategyId() == null) {
                                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_INTERCEPT, FilterInterceptTypeEnum.CALL_OUT, result._2, null, phoneHomeLocationBO);
                            } else {
                                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.INTERCEPT, FilterInterceptTypeEnum.CALL_OUT, result._2, null, phoneHomeLocationBO);
                            }
                        }
                    }
                }
            }

        }

        FilteredRobotCallTaskPO wphCustomIntercept = doWphCustomIntercept(weipinhuiTenant, robotCallJobPO, robotCallTaskPO, tenantInfo, accountVO, phoneHomeLocationBO);
        if (Objects.nonNull(wphCustomIntercept)) {
            return wphCustomIntercept;
        }
        return null;
    }

    @Override
    public void preBatchFilterTask(Long tenantId, List<RunTimeRobotCallTaskBO> robotCallTasks) {
        if (CollectionUtils.isEmpty(robotCallTasks)) {
            return;
        }
        try {
            if (QiFuHelper.isQiFuTenant(tenantId)) {
                try {
                    List<RunTimeRobotCallTaskBO> tempRobotCallTasks = new ArrayList<>();
                    robotCallTasks.forEach(task -> {
                        if (MapUtils.isEmpty(task.getProperties()) || StringUtils.isBlank(task.getProperties().get(QiFuHelper.BATCH_NO))) {
                            task.setCallPreFilterTaskBO(PreFilterTaskBO.builder()
                                    .preFilter(true)
                                    .preFilterReason("未获取到批次ID")
                                    .build());
                        } else {
                            tempRobotCallTasks.add(task);
                        }
                    });
                    if (CollectionUtils.isEmpty(tempRobotCallTasks)) {
                        return;
                    }
                    //奇富科技批量过滤
                    Map<String, PreFilterTaskBO> ivrResultMap = new HashMap<>(robotCallTasks.size());
                    Map<String, PreFilterTaskBO> smsResultMap = new HashMap<>(robotCallTasks.size());
                    tempRobotCallTasks.stream().collect(Collectors.groupingBy(task -> task.getProperties().get(QiFuHelper.BATCH_NO))).forEach((batchNo, tasks) -> {
                        Lists.partition(tasks, QiFuHelper.BATCH_SIZE).forEach(subTasks -> {
                            List<QiFuRiskRequestDTO> requestDTOS = subTasks.stream().map(task -> QiFuRiskRequestDTO.builder()
                                    .mobileMd5(task.getCalledPhoneNumber())
                                    .properties(task.getProperties())
                                    .build())
                                    .collect(Collectors.toList());
                            try {
                                QiFuIvrRiskBatchResponseDTO responseDTO = QiFuHelper.ivrRisk(QiFuRiskBatchRequestDTO.builder()
                                        .requestDTOS(requestDTOS)
                                        .build());
                                if (responseDTO.isSuccess()) {
                                    Map<String, Boolean> responseMap = MyCollectionUtils.listToMap(responseDTO.getIvrCntDetails(), QiFuIvrCountResponseDataItemDTO::getMobileMd5, QiFuIvrCountResponseDataItemDTO::getIsLimit);
                                    subTasks.forEach(item -> {
                                        if (BooleanUtils.isNotFalse(responseMap.get(item.getCalledPhoneNumber()))) {
                                            ivrResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                                    .preFilter(true)
                                                    .preFilterReason("呼叫超限")
                                                    .build());
                                        }
                                    });
                                } else {
                                    subTasks.forEach(item -> ivrResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                            .preFilter(true)
                                            .preFilterReason(responseDTO.getMsg())
                                            .build()));
                                }
                            } catch (Exception e) {
                                logger.error("FilteredRobotCallTaskServiceImpl preBatchFilterTask ivr error", e);
                                subTasks.forEach(item -> ivrResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                        .preFilter(true)
                                        .preFilterReason("查询频控异常")
                                        .build()));
                            }
                            try {
                                QiFuSmsRiskBatchResponseDTO responseDTO = QiFuHelper.smsRisk(QiFuRiskBatchRequestDTO.builder()
                                        .requestDTOS(requestDTOS)
                                        .build());
                                if (responseDTO.isSuccess()) {
                                    Map<String, Boolean> responseMap = MyCollectionUtils.listToMap(responseDTO.getSmsCntDetails(), QiFuSmsCountResponseDataItemDTO::getMobileMd5, QiFuSmsCountResponseDataItemDTO::getIsLimit);
                                    subTasks.forEach(item -> {
                                        if (BooleanUtils.isNotFalse(responseMap.get(item.getCalledPhoneNumber()))) {
                                            smsResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                                    .preFilter(true)
                                                    .preFilterReason("发送超限")
                                                    .build());
                                        }
                                    });
                                } else {
                                    subTasks.forEach(item -> smsResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                            .preFilter(true)
                                            .preFilterReason(responseDTO.getMsg())
                                            .build()));
                                }
                            } catch (Exception e) {
                                logger.error("FilteredRobotCallTaskServiceImpl preBatchFilterTask sms error", e);
                                subTasks.forEach(item -> smsResultMap.put(item.getCalledPhoneNumber(), PreFilterTaskBO.builder()
                                        .preFilter(true)
                                        .preFilterReason("查询频控异常")
                                        .build()));
                            }
                        });
                    });
                    robotCallTasks.forEach(task -> {
                        task.setCallPreFilterTaskBO(ivrResultMap.get(task.getCalledPhoneNumber()));
                        task.setSmsPreFilterTaskBO(smsResultMap.get(task.getCalledPhoneNumber()));
                    });
                } catch (Exception e) {
                    logger.error("FilteredRobotCallTaskServiceImpl preBatchFilterTask error", e);
                    robotCallTasks.forEach(item -> item.setCallPreFilterTaskBO(PreFilterTaskBO.builder()
                            .preFilter(true)
                            .preFilterReason("查询频控异常")
                            .build()));
                }
            }
        } catch (Exception e) {
            logger.error("FilteredRobotCallTaskServiceImpl preBatchFilterTask error", e);
        }
    }

    /**
     * 获取客户定制化风控实体
     *
     * @param weipinhuiTenant
     * @param robotCallTaskPO
     * @return
     */
    private CustomizeEntityDTO getCustomizeEntity(boolean weipinhuiTenant, RunTimeRobotCallTaskBO robotCallTaskPO) {
        if (MapUtils.isNotEmpty(robotCallTaskPO.getProperties()) && weipinhuiTenant) {
            return CustomizeEntityDTO.builder()
                    .customizeAccountType(CustomizeAccountTypeEnum.WPH)
                    .customizeValue(robotCallTaskPO.getProperties().getOrDefault("hashUid", null))
                    .build();
        }
        return null;
    }

    @Override
    public Tuple2<FilterTypeEnum, String> riskCheck(Map<Integer, Tuple2<CallOutInterceptDTO, CallOutInterceptResultDTO>> cacheMap,
                                                    int days, int count, Long tenantId, String phoneNumber, FilterInterceptTypeEnum typeEnum, String uid) {
        CallOutInterceptDTO callOutInterceptDTO;
        CallOutInterceptResultDTO localCalloutInterceptDTO;
        int limitNum = count;
        int num = 0;
        int localNum = 0;
        if (cacheMap.containsKey(days)) {
            callOutInterceptDTO = cacheMap.get(days)._1;
            localCalloutInterceptDTO = cacheMap.get(days)._2;
        } else {
            callOutInterceptDTO = rcsApiClient.getCalloutIntercept(days, uid, tenantId);
            localCalloutInterceptDTO = WeiPinHuiHelper.getCalloutInterceptLocal(phoneNumber, days);
        }

        if (FilterInterceptTypeEnum.CALL_OUT.equals(typeEnum)) {
            //外呼次数判断
            if (Objects.nonNull(callOutInterceptDTO) && Objects.nonNull(callOutInterceptDTO.getDayCallOut())) {
                num = callOutInterceptDTO.getDayCallOut();
                count = count - num;
            }
            if (Objects.nonNull(localCalloutInterceptDTO) && Objects.nonNull(localCalloutInterceptDTO.getDayCallOut())) {
                if (StringUtils.isNotBlank(localCalloutInterceptDTO.getMsg())) {
                    return Tuple.of(FilterTypeEnum.INTERCEPT, "接口异常拦截");
                }
                localNum = localCalloutInterceptDTO.getDayCallOut();
                count = count - localNum;
            }
        } else {
            //接听次数判断
            if (Objects.nonNull(callOutInterceptDTO) && Objects.nonNull(callOutInterceptDTO.getDayCallAnswered())) {
                num = callOutInterceptDTO.getDayCallAnswered();
                count = count - num;
            }
            if (Objects.nonNull(localCalloutInterceptDTO) && Objects.nonNull(localCalloutInterceptDTO.getDayCallAnswered())) {
                if (StringUtils.isNotBlank(localCalloutInterceptDTO.getMsg())) {
                    return Tuple.of(FilterTypeEnum.INTERCEPT, "接口异常拦截");
                }
                localNum = localCalloutInterceptDTO.getDayCallAnswered();
                count = count - localNum;
            }
        }
        if (count <= 0) {
            switch (typeEnum) {
                case CALL_OUT:
                    return Tuple.of(FilterTypeEnum.INTERCEPT, String.format("外呼上限超过%s天%s次(混合云外呼%s次,本地外呼%s次)", days, limitNum, num, localNum));
                case GET_THROUGH:
                    return Tuple.of(FilterTypeEnum.INTERCEPT, String.format("接通上限超过%s天%s次(混合云接通%s次,本地接通%s次)", days, limitNum, num, localNum));
            }
        }
        cacheMap.put(days, Tuple.of(callOutInterceptDTO, localCalloutInterceptDTO));
        return null;
    }

    /**
     * 处理欧莱雅唯品会等客户定制化拦截
     */
    private FilteredRobotCallTaskPO doCustomInterceptCheck(RunTimeRobotCallTaskBO robotCallTaskPO, AccountVO accountVO, PhoneHomeLocationBO phoneHomeLocationBO, RobotCallJobPO robotCallJobPO, boolean weipinhuiTenant, Set<String> needProperties, List<String> ttsFilterValues) {
        //欧莱雅外呼自定义变量查询+黑名单校验
        try {
            if (LorealHelper.isLorealTenant(robotCallTaskPO.getTenantId()) && Objects.nonNull(accountVO)) {
                String brand = customerPersonExtraInfoService.getBrandByCustomerPersonIdForLoreal(accountVO);
                String filterReason;
                if (StringUtils.isNotBlank(brand)) {
                    if (Boolean.TRUE.equals(robotCallJobPO.getSkipValidProperties())) {
                        Map<String, String> properties = LorealHelper.getProperties(brand, robotCallTaskPO.getCalledPhoneNumber());
                        robotCallTaskPO.getProperties().putAll(properties);
                        robotCallTaskPO.getRunTimeProperties().putAll(properties);
                    }
                    filterReason = LorealHelper.checkBlackList(brand, robotCallTaskPO.getCalledPhoneNumber());
                } else {
                    filterReason = LorealHelper.BRAND_FILED + "变量缺失";
                }
                if (StringUtils.isNotEmpty(filterReason)) {
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, filterReason, null, phoneHomeLocationBO);
                }
            }
        } catch (Exception e) {
            logger.error("filterTask LorealHelper checkBlackList error", e);
        }
        //唯品会风险数据校验
        try {
            if (weipinhuiTenant) {
                //唯品会黑名单校验
                if (ApplicationConstant.wphBlackCheckSwitch) {
                    WphBlackCheckResultVO wphBlack;
                    if(WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())){
                        String importTaskId = robotCallTaskPO.getRunTimeProperties().get(WeiPinHuiNewHelper.IMPORT_TASK_ID);
                        wphBlack = weiPinHuiNewHelper.wphBlackCheckV2(robotCallTaskPO.getCalledPhoneNumber(), importTaskId);
                    }else {
                        wphBlack = WeiPinHuiHelper.isWphBlack(robotCallTaskPO.getCalledPhoneNumber());
                    }
                    if (BooleanUtils.isNotTrue(wphBlack.getIsPass())) {
                        return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, AiccStringUtils.checkAndSubString(wphBlack.getMsg(), 100), null, phoneHomeLocationBO);
                    }
                }
                //唯品会优惠券池校验
                Long wphCouponPoolId = BooleanUtils.isTrue(robotCallJobPO.getWphCouponPoolSwitch()) ? robotCallJobPO.getWphCouponPoolId() : null;
                String wphProductionPushCode = WphPushTypeEnum.PRODUCTION.equals(robotCallJobPO.getWphPushType()) ? robotCallJobPO.getWphProductionPushCode() : "";
                Long wphFxId = WphPushTypeEnum.PRODUCTION.equals(robotCallJobPO.getWphPushType()) ? robotCallJobPO.getWphFxId() : null;
                List<String> productionPushParam;
                if(!WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())) {
                    productionPushParam = WphPushTypeEnum.PRODUCTION.equals(robotCallJobPO.getWphPushType()) && CollectionUtils.isNotEmpty(needProperties) ? needProperties.stream().filter(x -> ApplicationConstant.WPH_PRODUCTION_PUSH_KEY.contains(x)).collect(Collectors.toList()) : Collections.emptyList();
                }else{
                    productionPushParam = needProperties.stream().filter(x -> ApplicationConstant.WPH_COUPON_PUSH_KEY.contains(x)).collect(Collectors.toList());
                }
                Set<Integer> wphRuleIds = CollectionUtils.isNotEmpty(robotCallJobPO.getWphRuleIds()) ? robotCallJobPO.getWphRuleIds() : Sets.newHashSet();
                if (BooleanUtils.isTrue(robotCallJobPO.getWphCouponPoolSwitch())) {
                    if (WphPushTypeEnum.PRODUCTION.equals(robotCallJobPO.getWphPushType())) {
                        wphRuleIds.add(WeiPinHuiHelper.PRODUCTION_PUSH_RULE_ID);
                    } else if (WphPushTypeEnum.COUPON.equals(robotCallJobPO.getWphPushType())) {
                        wphRuleIds.add(WeiPinHuiHelper.COUPON_POOL_RULE_ID);
                    }
                }
                AiCallRiskCheckResultVO resultVO;
                if(WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())){
                    String importTaskId = robotCallTaskPO.getRunTimeProperties().get(WeiPinHuiNewHelper.IMPORT_TASK_ID);
                    if(BooleanUtils.isTrue(robotCallJobPO.getEnableWphSendSms())) {
                        productionPushParam.add(WeiPinHuiNewHelper.SMS_KEY);
                    }
                    resultVO = weiPinHuiNewHelper.getCustomVariable(importTaskId,robotCallTaskPO.getCalledPhoneNumber(),productionPushParam);
                }else {
                    resultVO = WeiPinHuiHelper.aiCallRiskCheck(robotCallTaskPO.getCalledPhoneNumber(), wphRuleIds, wphCouponPoolId, wphProductionPushCode, productionPushParam, wphFxId);
                    if (Objects.nonNull(resultVO.getHashUid())) {
                        Map<String, String> runTimeProperties = MapUtils.isNotEmpty(robotCallTaskPO.getRunTimeProperties()) ? robotCallTaskPO.getRunTimeProperties() : new HashMap<>(4);
                        Map<String, String> properties = MapUtils.isNotEmpty(robotCallTaskPO.getProperties()) ? robotCallTaskPO.getProperties() : new HashMap<>(4);
                        runTimeProperties.put("hashUid", resultVO.getHashUid() + "");
                        robotCallTaskPO.setRunTimeProperties(runTimeProperties);
                        properties.put("hashUid", resultVO.getHashUid() + "");
                        robotCallTaskPO.setProperties(properties);
                    }
                    if (BooleanUtils.isNotTrue(resultVO.getIsPass())) {
                        String filterReason = resultVO.getMsg();
                        if (StringUtils.isNotBlank(filterReason) && filterReason.length() > 100) {
                            filterReason = filterReason.substring(0, 100);
                        }
                        return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LOREAL_INTERCEPT, filterReason, null, phoneHomeLocationBO);
                    }
                }
                if (BooleanUtils.isTrue(robotCallJobPO.getWphCouponPoolSwitch())) {
                    Map<String, String> runTimeProperties = MapUtils.isNotEmpty(robotCallTaskPO.getRunTimeProperties()) ? robotCallTaskPO.getRunTimeProperties() : new HashMap<>(16);
                    Map<String, String> properties = MapUtils.isNotEmpty(robotCallTaskPO.getProperties()) ? robotCallTaskPO.getProperties() : new HashMap<>(16);
                    if (WphPushTypeEnum.PRODUCTION.equals(robotCallJobPO.getWphPushType())) {
                        Map<String, String> productionParamMap = MapUtils.isNotEmpty(resultVO.getProductionParams()) ? resultVO.getProductionParams() : new HashMap<>(16);
                        //处理之前先清空变量
                        WeiPinHuiHelper.dealPropertiesPre(runTimeProperties, properties);
                        productionPushParam.forEach(key -> {
                            String value = productionParamMap.get(key);
                            if (StringUtils.isNotBlank(value)) {
                                properties.put(key, value);
                                runTimeProperties.put(key, value);
                            }else {
                                if(!WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())) {
                                    properties.put(key, WeiPinHuiHelper.PRODUCTION_PUSH_DEFAULT_SINGLE);
                                    runTimeProperties.put(key, WeiPinHuiHelper.PRODUCTION_PUSH_DEFAULT_SINGLE);
                                }
                            }
                        });
                        if(!WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())) {
                            properties.put(WeiPinHuiHelper.PRODUCTION_PUSH_FLAG, resultVO.getCuponFlag() + "");
                            runTimeProperties.put(WeiPinHuiHelper.PRODUCTION_PUSH_FLAG, resultVO.getCuponFlag() + "");
                        }
                    }else{
                        Map<String, String> productionParamMap = MapUtils.isNotEmpty(resultVO.getProductionParams()) ? resultVO.getProductionParams() : new HashMap<>(16);
                        if(productionParamMap.containsKey(WeiPinHuiNewHelper.SMS_KEY)){
                            properties.put(WeiPinHuiNewHelper.SMS_KEY, productionParamMap.get(WeiPinHuiNewHelper.SMS_KEY));
                        }
                    }
                    boolean personalizedProduct = WeiPinHuiHelper.personalizedProduct(robotCallJobPO, runTimeProperties);
                        if (personalizedProduct && productionPushParam.contains(WeiPinHuiHelper.三级品类名称) && productionPushParam.contains(WeiPinHuiHelper.商品名称)) {
                            String originalClassify = runTimeProperties.get(WeiPinHuiHelper.三级品类名称);
                            if (StringUtils.isNotBlank(originalClassify) && originalClassify.contains(WeiPinHuiHelper.SPLIT)) {
                                String classify = WeiPinHuiHelper.getClassify(runTimeProperties);
                                if (StringUtils.isNotBlank(classify)) {
                                    runTimeProperties.put(WeiPinHuiHelper.三级品类名称, classify);
                                    properties.put(WeiPinHuiHelper.三级品类名称, classify);
                                } else {
                                    if(!WeiPinHuiNewHelper.belongWphMarketing(robotCallTaskPO.getTenantId())) {
                                        runTimeProperties.put(WeiPinHuiHelper.三级品类名称, WeiPinHuiHelper.PRODUCTION_PUSH_DEFAULT_SINGLE);
                                        properties.put(WeiPinHuiHelper.三级品类名称, WeiPinHuiHelper.PRODUCTION_PUSH_DEFAULT_SINGLE);
                                        personalizedProduct = false;
                                    }
                                }
                            }
                        }
                    if (Objects.nonNull(resultVO.getCouponBuy())) {
                        runTimeProperties.put("门槛", resultVO.getCouponBuy().longValue() + "");
                        properties.put("门槛", resultVO.getCouponBuy().longValue() + "");
                    } else if (personalizedProduct) {
                        runTimeProperties.put("门槛", "");
                    }
                    if (Objects.nonNull(resultVO.getCouponFav())) {
                        runTimeProperties.put("面额", resultVO.getCouponFav().longValue() + "");
                        properties.put("面额", resultVO.getCouponFav().longValue() + "");
                    } else if (personalizedProduct) {
                        runTimeProperties.put("面额", "");
                    }
                    if (personalizedProduct) {
                        //处理需要过滤的tts值
                        ApplicationConstant.WPH_PRODUCTION_TTS_KEY.forEach(key -> {
                            String ttsFilterValue = runTimeProperties.get(key);
                            if (StringUtils.isNotBlank(ttsFilterValue)) {
                                ttsFilterValues.add(ttsFilterValue);
                            }
                        });
                    }
                    robotCallTaskPO.setRunTimeProperties(runTimeProperties);
                    robotCallTaskPO.setProperties(properties);

                }else{
                    Map<String, String> properties = MapUtils.isNotEmpty(robotCallTaskPO.getProperties()) ? robotCallTaskPO.getProperties() : new HashMap<>(16);
                    Map<String, String> productionParamMap = MapUtils.isNotEmpty(resultVO.getProductionParams()) ? resultVO.getProductionParams() : new HashMap<>(16);
                    productionPushParam.forEach(key -> {
                        String value = productionParamMap.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            properties.put(key, value);
                        }
                    });
                    robotCallTaskPO.setProperties(properties);
                }
            }
        } catch (Exception e) {
            logger.error("filterTask WeiPinHuiHelper aiCallRiskCheck error", e);
        }
        return null;
    }

    /**
     * 客户自定义加密
     *
     * @param robotCallTaskPO
     * @param tenantInfo
     * @return
     */
    private FilteredRobotCallTaskPO doTenantEncryptConfigure(RunTimeRobotCallTaskBO robotCallTaskPO, AccountVO accountVO, TenantPO tenantInfo) {
        try {
            if (Objects.isNull(tenantInfo) || !TenantEncryptTypeEnum.customerCommonEncrypt(tenantInfo.getEncryptType())) {
                return null;
            }
            //抖音特殊处理
            if (TenantEncryptTypeEnum.DOU_YIN_ENCRYPT.equals(tenantInfo.getEncryptType())
                    && StringUtils.isBlank(robotCallTaskPO.getCalledPhoneNumber())) {
                robotCallTaskPO.setCalledPhoneNumber(accountVO.getChannelId());
            }

            CommonDecryptResultDTO resultDTO = null;
            if (TenantEncryptTypeEnum.DOU_YIN_ENCRYPT.equals(tenantInfo.getEncryptType()) && accountVO.getChannelName() == ChannelTypeEnum.DOU_DIAN_RULE_SELECT) {
                resultDTO = decryptDouDianCrowd(robotCallTaskPO, accountVO, tenantInfo);
            } else {
                resultDTO = tenantEncryptConfigureService.commonDecrypt(robotCallTaskPO.getTenantId(), tenantInfo.getEncryptType(), robotCallTaskPO.getCalledPhoneNumber());
            }

            if (tenantEncryptConfigureService.commonDecryptSuccess(resultDTO)) {
                //解密成功
                robotCallTaskPO.setRealCalledPhoneNumber(resultDTO.getMobile());
                if (ApplicationConstant.ANT_MD5_PHONE_END_TENANTS.contains(tenantInfo.getTenantId())) {
                    Map<String, String> runTimeProperties = MapUtils.isNotEmpty(robotCallTaskPO.getRunTimeProperties()) ? robotCallTaskPO.getRunTimeProperties() : new HashMap<>(4);
                    Map<String, String> properties = MapUtils.isNotEmpty(robotCallTaskPO.getProperties()) ? robotCallTaskPO.getProperties() : new HashMap<>(4);
                    String end4 = resultDTO.getMobile().substring(resultDTO.getMobile().length() - 4);
                    runTimeProperties.put(AntHelper.PHONE_END, end4);
                    robotCallTaskPO.setRunTimeProperties(runTimeProperties);
                    properties.put(AntHelper.PHONE_END, end4);
                    robotCallTaskPO.setProperties(properties);
                }
                return null;
            } else {
                if (TenantEncryptTypeEnum.DOU_YIN_ENCRYPT.equals(tenantInfo.getEncryptType())) {
                    //电商拦截异常过滤
                    //解密失败
                    String filterReason = "号码解密失败-" + resultDTO.getErrMsg();
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.ELECTRONIC_BUSINESS, AiccStringUtils.checkAndSubString(filterReason, 100), null, null);
                }else{
                    //解密失败
                    String filterReason = "号码解密失败-" + resultDTO.getErrMsg();
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.DECRYPT_FAIL, AiccStringUtils.checkAndSubString(filterReason, 100), null, null);
                }

            }
        } catch (Exception e) {
            logger.error("doTenantEncryptConfigure error", e);
            return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.DECRYPT_FAIL, "号码解密失败", null, null);
        }


    }

    private CommonDecryptResultDTO decryptDouDianCrowd(RunTimeRobotCallTaskBO robotCallTaskPO, AccountVO accountVO, TenantPO tenantInfo) {

        Map<String, String> properties = robotCallTaskPO.getProperties();

        String crowdDouDianId = properties.get("crowdDouDianId");
        DoudianOutboundRequestVO requestVO = new DoudianOutboundRequestVO();
        requestVO.setEncryptUid(accountVO.getChannelId());
        requestVO.setTenantId(tenantInfo.getTenantId());
        requestVO.setCrowdDouDianId(Long.parseLong(crowdDouDianId));

        logger.info("requestVO -> {}", JsonUtils.object2String(requestVO));
        DouDianOutboundDTO outboundId = douDianCrowdClient.getOutboundId(requestVO);

        logger.info("outboundId -> {}", JsonUtils.object2String(outboundId));
        return BeanUtil.toBean(outboundId, CommonDecryptResultDTO.class);
    }

    /**
     * 加微推送 使用第一象限自动加微，且加微账号选择“指定账号” 外呼时候进行校验
     */
    @Override
    public String checkWechatAccount(RobotCallJobPO robotCallJobPO, RunTimeRobotCallTaskBO robotCallTaskPO) {
        try {
            if (robotCallJobPO.getAddFriendStatus() != null && RobotCallJobAddFriendStatusEnum.SEND.equals(robotCallJobPO.getAddFriendStatus())
                    && robotCallJobPO.getWechatCpAddFriend() != null && WechatCpAddFriendEnum.YIWISE_SCRM_AUTO.equals(robotCallJobPO.getWechatCpAddFriend())) {
                CallJobAddWechatConfigBO wechatConfig = robotCallJobPO.getAddWechatConfig();
                if (wechatConfig != null && wechatConfig.getScrmType() != null && ScrmAddWechatTypeEnum.ACCOUNT.equals(wechatConfig.getScrmType())) {
                    String wechatAccount = robotCallTaskPO.getAddWechatAccountId();
                    List<ScrmWechatBO> wechats = tenantScrmService.getWechatByIdNames(robotCallTaskPO.getTenantId(), null, null, Collections.singletonList(wechatAccount));
                    logger.info("加微账号信息查询 ScrmWechatBO={}", wechats);
                    if (wechats.size() > 0) {
                        ScrmWechatBO wechat = wechats.get(0);
                        if (wechat.getOnline() != null && !wechat.getOnline()) {
                            return "加微账号" + wechat.getUserName() + "离线";
                        } else if (wechat.getIsDayLimit() != null && wechat.getIsDayLimit()) {
                            return "加微账号" + wechat.getUserName() + "达到每日加微上限";
                        } else if (wechat.getIsNotCertified() != null && wechat.getIsNotCertified()) {
                            return "加微账号" + wechat.getUserName() + "未实名认证";
                        } else if (wechat.getIsLimit() != null && wechat.getIsLimit()) {
                            return "加微账号" + wechat.getUserName() + "限流";
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("过滤加微账号出错", e);
        }
        return null;
    }

    @Override
    public JobStartResultVO importToSmsJob(FilteredRobotCallTaskQueryVO query) {
        Long tenantId = query.getTenantId();
        Long userId = query.getUserId();
        SystemEnum system = query.getSystemType();
        if (system == null) {
            system = SystemEnum.RISK_CONTROL;
        }
        //OPE登录AICC的操作用户followUserId修改为AICC管理员的userId
        Long followUserId = userService.changeAiccAdminUserId(userId, tenantId);

        SmsJobPO smsJob = smsJobService.getSmsJobInfo(tenantId, query.getTargetRobotCallJobId()).getSmsJob();
        Optional<List<Long>> authUserIds = dataAccessControlService.getAuthUserIdListWithTenantId(userId, tenantId, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_organization);
        authUserIds.ifPresent(query::setUserIdList);
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addString("SYSTEM_TYPE", system.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(FILTERED_TASK_EXPORT_LIST));

        String baseName = SpringBatchJobTypeEnum.FILTERED_TASK_EXPORT_EXCEL.getDesc() + "-" + now;
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(EXPORT_FILE_PREFIX, tenantId, userId, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey));
        jobParametersBuilder.addLong("FOLLOW_USER_ID", followUserId);
        jobParametersBuilder.addLong("SMS_JOB_ID", query.getTargetRobotCallJobId());
        //jobParametersBuilder.addLong("DIALOG_FLOW_ID", robotCallJob.getDialogFlowId());
        jobParametersBuilder.addLong("JOB_CREATE_USER_ID", smsJob.getCreateUserId());

        String queryString = JsonUtils.object2String(query);
        jobParametersBuilder.addString("EXPORT_REQUEST", queryString);

        String jobNamePrefix = "";
        if (CollectionUtils.isNotEmpty(query.getRobotCallJobIdList())) {
            RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(query.getRobotCallJobIdList().get(0));
            if (Objects.nonNull(robotCallJobPO)) {
                jobNamePrefix = "「" + robotCallJobPO.getName() + "」";
            }
        }
        String jobName = jobNamePrefix + "导入过滤客户到短信任务" + "「" + smsJob.getName() + "」";
        int totalCount = countFilteredTaskList(query);
        //取消导出数据80晚的限制
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        filteredTaskImportToSmsJob,
                        jobParametersBuilder.toJobParameters(),
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        SpringBatchJobTypeEnum.IMPORT_FILTERED_CUSTOMER_TO_SMS_JOB,
                        system,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public JobStartResultVO exportFilteredTask(FilteredRobotCallTaskQueryVO query) {
        Long tenantId = query.getTenantId();
        Long userId = query.getUserId();
        SystemEnum system = query.getSystemType();
        if (system == null) {
            system = SystemEnum.RISK_CONTROL;
        }
        Optional<List<Long>> authUserIds = dataAccessControlService.getAuthUserIdListWithTenantId(userId, tenantId, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_organization);
        authUserIds.ifPresent(query::setUserIdList);
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addString("SYSTEM_TYPE", system.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(FILTERED_TASK_EXPORT_LIST));
        jobParametersBuilder.addLong("JOB_TYPE", Long.valueOf(SpringBatchJobTypeEnum.FILTERED_TASK_EXPORT_EXCEL.getFileType()));
        String baseName, jobName, jobEnumDesc = SpringBatchJobTypeEnum.FILTERED_TASK_EXPORT_EXCEL.getDesc();
        if (Boolean.TRUE.equals(query.getExportWithJobName())) {
            // 任务模块导出
            List<Long> robotCallJobIds = query.getRobotCallJobIdList();
            Assert.notNull(robotCallJobIds, "选择任务为空");
            Assert.isTrue(robotCallJobIds.size() == 1, "选择任务数量不合法");
            RobotCallJobPO robotCallJob = robotCallJobService.selectByKeyOrThrow(robotCallJobIds.get(0));
            baseName = jobEnumDesc + "-" + IllegalNameReplaceUtil.replaceJobName(robotCallJob.getName()) + "-" + now;
            jobName = jobEnumDesc + "-" + robotCallJob.getName();
        } else {
            // 过滤客户模块导出
            baseName = jobEnumDesc + "-" + now;
            jobName = jobEnumDesc;
        }
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(EXPORT_FILE_PREFIX, tenantId, userId, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        String customerPersonExportRequestVOString = JsonUtils.object2String(query);
        jobParametersBuilder.addString("EXPORT_REQUEST", customerPersonExportRequestVOString);

        int totalCount = countFilteredTaskList(query);

        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.FILTERED_TASK_EXPORT_EXCEL;
        //删除导出客户上限为80万的限制
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        filteredTaskExportExcelJob,
                        jobParametersBuilder.toJobParameters(),
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        system,
                        false);
        springBatchJobBO.setDownloadStatus(0);
        JobStartResultVO jobStartResultVO = batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
        return jobStartResultVO;
    }

    @Override
    public int countFilteredTaskList(FilteredRobotCallTaskQueryVO query) {
        Optional<List<Long>> authUserIds = dataAccessControlService.getAuthUserIdListWithTenantId(query.getUserPO().getUserId(), query.getUserPO().getTenantId(), AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_call_task_data_permission_organization);
        authUserIds.ifPresent(query::setUserIdList);
        Integer integer = filteredRobotCallTaskPOMapper.selectCountFilteredTaskExportByJobId(query);
        return integer == null ? 0 : integer;
    }

    @Override
    public Long countFilteredTaskListNoLogin(FilteredRobotCallTaskQueryVO query) {
        return Long.valueOf(filteredRobotCallTaskPOMapper.selectCountFilteredTaskExportByJobId(query));
    }

    @Override
    public FilteredRobotCallTaskPO checkFilteredTaskByTaskId(Long tenantId, Long robotCallJobId, Long robotCallTaskId) {
        return filteredRobotCallTaskPOMapper.checkFilteredTaskByTaskId(tenantId, robotCallJobId, robotCallTaskId);
    }

    @Override
    public List<FilteredRobotCallTaskPO> selectFilteredTaskByTaskId(Long tenantId, Long robotCallJobId, Long robotCallTaskId) {
        return filteredRobotCallTaskPOMapper.selectFilteredTaskByTaskId(tenantId, robotCallJobId, robotCallTaskId);
    }

    @Override
    public List<FilteredRobotCallTaskPO> selectNoLastFilter(Long tenantId, Long robotCallTaskId, Long robotCallJobId, Long excludeFilteredRobotCallTaskId) {
        return filteredRobotCallTaskPOMapper.selectNoLastFilter(tenantId, robotCallTaskId, robotCallJobId, excludeFilteredRobotCallTaskId);
    }

    @Override
    public boolean readStatus(Long robotCallJobId) {
        String key = RedisKeyCenter.getJobFilteredTaskReadStatusKey(robotCallJobId);
        // null, true -> true
        // false -> false
        return BooleanUtils.isNotFalse(redisOpsService.get(key, Boolean.class));
    }

    @Override
    public void read(Long robotCallJobId, Boolean status) {
        String key = RedisKeyCenter.getJobFilteredTaskReadStatusKey(robotCallJobId);
        redisOpsService.set(key, status);
        redisOpsService.expire(key, 3, TimeUnit.DAYS);
    }

    @Override
    public void setNotLastFilter(Long tenantId, Long robotCallTaskId, Long robotCallJobId, Long excludeFilteredRobotCallTaskId) {
        filteredRobotCallTaskPOMapper.setNotLastFilter(tenantId, robotCallTaskId, robotCallJobId, excludeFilteredRobotCallTaskId);
    }

    @Override
    public void setFilteredTaskCalled(Long tenantId, Long robotCallRobotId, Long robotCallTaskId, Integer called) {
        if (Objects.isNull(called) || Objects.isNull(robotCallRobotId) || Objects.isNull(robotCallTaskId)) {
            return;
        }
        filteredRobotCallTaskPOMapper.setFilteredTaskCalled(tenantId, robotCallRobotId, robotCallTaskId, called);
    }

    @Override
    public PageResultObject selectByJobIdForOpenApi(FilteredRobotCallTaskOpenApiQueryVO queryVO) {
        Assert.notNull(queryVO.getRobotCallJobId(), "任务ID不能为空");
        if (queryVO.getPageSize() > 100) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "pageSize允许最大值为100");
        }
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<FilteredRobotCallTaskApiVO> filteredRobotCallTaskApiVOS = filteredRobotCallTaskPOMapper.selectByJobIdForOpenApi(queryVO.getTenantId(), queryVO.getRobotCallJobId());
        return PageResultObject.of(filteredRobotCallTaskApiVOS);
    }

    @Override
    public void dealNotCalledFilterHistory() {
        AtomicInteger successCount = new AtomicInteger();
        HandleByPageUtils.handleItem(() -> robotCallJobPOMapper.selectAllRobotCallJob(), (index, item) -> {
            try {
                FilteredRobotCallTaskQueryVO queryVO = new FilteredRobotCallTaskQueryVO();
                queryVO.setTenantId(item.getTenantId());
                queryVO.setRobotCallJobIdList(Lists.newArrayList(item.getRobotCallJobId()));
                queryVO.setCalled(false);
                queryVO.setLastFilter(true);
                Long count = countFilteredTaskListNoLogin(queryVO);
                logger.info("dealNotCalledFilterHistory robotCallJobId={} count={}", item.getRobotCallJobId(), count);
                if (count > 0) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("tenantId").is(item.getTenantId()));
                    query.addCriteria(Criteria.where("callStatsId").is(item.getRobotCallJobId()));
                    query.addCriteria(Criteria.where("dialogFlowId").is(item.getDialogFlowId()));
                    Update update = new Update();
                    update.inc("filteredTaskNotCalledCount", count);
                    mongoTemplate.upsert(query, update, MongoCollectionNameCenter.CALL_STATS_ROBOT_JOB);
                }
                successCount.getAndIncrement();
                if (successCount.get() % 1000 == 0) {
                    logger.info("dealNotCalledFilterHistory successCount={}", successCount.get());
                    Thread.sleep(1000);
                }
            } catch (Exception e) {
                logger.error("dealNotCalledFilterHistory error", e);
            }
        });
    }

    /**
     * 自定义变量校验
     */
    private FilteredRobotCallTaskPO checkProperties(RunTimeRobotCallTaskBO robotCallTaskPO,
                                                    AccountVO accountVO,
                                                    Set<String> needProperties,
                                                    PhoneHomeLocationBO phoneHomeLocationBO) {
        if (CollectionUtils.isNotEmpty(needProperties) && MapUtils.isNotEmpty(robotCallTaskPO.getRunTimeProperties())
                && !robotCallTaskPO.getRunTimeProperties().keySet().containsAll(needProperties)) {
            Set<String> copyProperties = new HashSet<>(needProperties);
            copyProperties.removeAll(robotCallTaskPO.getRunTimeProperties().keySet());
            logger.info("自定义变量缺失 taskId={} 缺失变量={}", robotCallTaskPO.getRobotCallTaskId(), copyProperties);
            // 缺失变量明细
            return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.LACK_PROPERTIES, JsonUtils.object2PrettyString(copyProperties), null, phoneHomeLocationBO);
        }
        return null;
    }

    @Override
    public int selectFilteredTaskCountByJobId(Long tenantId, Long robotCallJobId) {
        return filteredRobotCallTaskPOMapper.countFilteredTaskByJobIdOnDistinctNumber(tenantId, robotCallJobId);
    }

    @Override
    public FilteredRobotCallTaskPO rcsCallingAtSameTime(RunTimeRobotCallTaskBO robotCallTaskPO, RobotCallJobPO robotCallJobPO, AccountVO accountVO, Set<String> needProperties, TenantPO tenantInfo) {

        if(!BooleanUtils.toBooleanDefaultIfNull(tenantInfo.getCallAtSameTimeLimit(),false)){
            return null;
        }
        try {
            RcsCheckReqDTO reqDTO = new RcsCheckReqDTO();
            reqDTO.setTenantId(robotCallTaskPO.getTenantId());
            boolean weipinhuiTenant = WeiPinHuiHelper.isWeipinhuiTenantWithPO(tenantInfo);
            reqDTO.setCustomizeEntity(getCustomizeEntity(weipinhuiTenant, robotCallTaskPO));
            reqDTO.setCalledPhoneNumber(robotCallTaskPO.getCalledPhoneNumber());
            reqDTO.setExtensionMode(robotCallTaskPO.getExtensionMode());
            if (null != robotCallTaskPO.getTicktokMode() && Boolean.TRUE.equals(robotCallTaskPO.getTicktokMode())) {
                reqDTO.setExtensionMode(robotCallTaskPO.getTicktokMode());
            }
            reqDTO.setAccountId(accountVO.getAccountId());
            RcsCheckResDTO rcsCheckResDTO = rcsApiClient.tagCalling(reqDTO);
            if(Objects.nonNull(rcsCheckResDTO)){
                FilterInterceptTypeEnum filterInterceptType = Objects.nonNull(rcsCheckResDTO.getInterceptType()) ? FilterInterceptTypeEnum.valueOf(rcsCheckResDTO.getInterceptType().name()) : null;
                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.valueOf(rcsCheckResDTO.getFilterType().name()), filterInterceptType, rcsCheckResDTO.getFilterReason(), null, null);
            }
        } catch (Exception e) {
            logger.error("rcsFilterTask rcsApiClient tagCalling error", e);
            return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.ILLEGAL_TEL, "风控系统不可用", null, null);
        }
        return null;
    }

    @Override
    public List<FilteredRobotCallTaskPO> getListByADB(Long tenantId, LocalDateTime startTime, LocalDateTime endTime,Long startPoint,Integer limit) {
        List<Long> ids = filteredRobotCallTaskPOMapper.getIdListByADB(tenantId,startTime,endTime,startPoint,limit);
        if(CollectionUtils.isNotEmpty(ids)){
            return filteredRobotCallTaskPOMapper.getListByIdsADB(ids);
        }
        return new ArrayList<>();
    }

    @Override
    public List<FilterRecordVO> getFilterRecordByRobotCallJobId(FilterRecordRequest filterRecordRequest) {
        if (ObjUtil.anyNull(filterRecordRequest.getRobotCallJobId(), filterRecordRequest.getCalledPhoneNumber())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "必填项为空");
        }
        if (StringUtils.isEmpty(filterRecordRequest.getCalledPhoneNumber())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "必填项为空");
        }
        List<FilteredRobotCallTaskPO> filterRecord = filteredRobotCallTaskPOMapper.getFilterRecordByRobotCallJobId(filterRecordRequest);
        if(CollectionUtils.isEmpty(filterRecord)){
            return Collections.emptyList();
        }
        List<FilterRecordVO> filterRecordVOS = bizOrikaConvert.convertList(filterRecord, FilterRecordVO.class);
        for (FilterRecordVO filterRecordVO : filterRecordVOS) {
            if (filterRecordVO.getFilterType().equals(FilterTypeEnum.BLACK_LIST)) {
                filterRecordVO.setFilterReason(null);
            }
        }
        return filterRecordVOS;
    }

    /**
     * rcs
     * @param robotCallTaskPO
     * @param robotCallJobPO
     * @param accountVO
     * @param phoneHomeLocationBO
     * @param weipinhuiTenant
     * @param ttsFilterValues
     * @return
     */
    private FilteredRobotCallTaskPO rcsCheck(RunTimeRobotCallTaskBO robotCallTaskPO, RobotCallJobPO robotCallJobPO, AccountVO accountVO, PhoneHomeLocationBO phoneHomeLocationBO, boolean weipinhuiTenant, List<String> ttsFilterValues,TenantPO tenantInfo) {
        int tryCount = 0;
        while (true) {
            try {
                RcsCheckResDTO rcsCheckResDTO = doRcsCheck(robotCallTaskPO, robotCallJobPO, accountVO, phoneHomeLocationBO, weipinhuiTenant, ttsFilterValues,tenantInfo.getEncryptType());
                if (Objects.nonNull(rcsCheckResDTO)) {
                    FilterInterceptTypeEnum filterInterceptType = Objects.nonNull(rcsCheckResDTO.getInterceptType()) ? FilterInterceptTypeEnum.valueOf(rcsCheckResDTO.getInterceptType().name()) : null;
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.valueOf(rcsCheckResDTO.getFilterType().name()), filterInterceptType, rcsCheckResDTO.getFilterReason(), null, phoneHomeLocationBO);
                }
                break;
            } catch (HystrixRuntimeException hystrixRuntimeException) {
                logger.error("[LogHub_Warn] 风控系统超时 tryCount={}", tryCount, hystrixRuntimeException);
                RcsHelper.setRcsTimeOut(robotCallJobPO.getRobotCallJobId());
                if (tryCount++ >= 3) {
                    feishuSendMsgService.sendFeishuWarn(Collections.emptyList(),"风控系统超时告警", "调用风控系统连续失败三次", RCS_RUNTIME_EXCEPTION_WARN_FEI_SHU_WEBHOOK);
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_EXCEPTION, "风控系统不可用", null, phoneHomeLocationBO);
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn] 风控系统不可用", e);
                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_EXCEPTION, "风控系统不可用", null, phoneHomeLocationBO);
            }
        }
        return null;
    }

    /**
     * rcsCheck
     * @param robotCallTaskPO
     * @param robotCallJobPO
     * @param accountVO
     * @param phoneHomeLocationBO
     * @param weipinhuiTenant
     * @param ttsFilterValues
     * @return
     */
    private RcsCheckResDTO doRcsCheck(RunTimeRobotCallTaskBO robotCallTaskPO, RobotCallJobPO robotCallJobPO, AccountVO accountVO, PhoneHomeLocationBO phoneHomeLocationBO, boolean weipinhuiTenant, List<String> ttsFilterValues,TenantEncryptTypeEnum encryptType) {
        //风控系统过滤
        RcsCheckReqDTO reqDTO = new RcsCheckReqDTO();
        reqDTO.setTenantId(robotCallTaskPO.getTenantId());
        reqDTO.setRcsId(robotCallJobPO.getRiskControlStrategyId());
        reqDTO.setCustomerBlackGroupIds(robotCallJobPO.getCustomerWhiteGroupIds());
        reqDTO.setBrandBlackGroupIds(robotCallJobPO.getBrandBlackGroupIds());
        reqDTO.setCalledPhoneNumber(robotCallTaskPO.getCalledPhoneNumber());
        reqDTO.setCustomizeEntity(getCustomizeEntity(weipinhuiTenant, robotCallTaskPO));
        reqDTO.setRealCalledPhoneNumber(robotCallTaskPO.getRealCalledPhoneNumber() != null ? robotCallTaskPO.getRealCalledPhoneNumber() : robotCallTaskPO.getCalledPhoneNumber());
        reqDTO.setExtensionMode(robotCallTaskPO.getExtensionMode());
        if (null != robotCallTaskPO.getTicktokMode() && Boolean.TRUE.equals(robotCallTaskPO.getTicktokMode())) {
            reqDTO.setExtensionMode(robotCallTaskPO.getTicktokMode());
        }
        reqDTO.setAccountId(accountVO.getAccountId());
        reqDTO.setPhoneNumberLocationDTO(PhoneNumberLocationDTO.builder()
                .prov(Objects.nonNull(phoneHomeLocationBO) ? phoneHomeLocationBO.getProv() : null)
                .city(Objects.nonNull(phoneHomeLocationBO) ? phoneHomeLocationBO.getCity() : null)
                .build());
        reqDTO.setCompany(Objects.nonNull(phoneHomeLocationBO) ? phoneHomeLocationBO.getCompany():null);
        reqDTO.setTiktokEncrypt(TenantEncryptTypeEnum.DOU_YIN_ENCRYPT.equals(encryptType));
        reqDTO.setTtsFilterValues(ttsFilterValues);
        RcsHelper.rcsTimeOutSleepSeconds(robotCallJobPO.getRobotCallJobId());
        return rcsApiClient.rcsCheckV2(reqDTO);
    }

    /**
     * 唯品会定制化呼频规则
     * @param weipinhuiTenant
     * @param robotCallJobPO
     * @param robotCallTaskPO
     * @param tenantInfo
     * @param accountVO
     * @param phoneHomeLocationBO
     * @return
     */
    private FilteredRobotCallTaskPO doWphCustomIntercept(boolean weipinhuiTenant, RobotCallJobPO robotCallJobPO, RunTimeRobotCallTaskBO robotCallTaskPO, TenantPO tenantInfo, AccountVO accountVO, PhoneHomeLocationBO phoneHomeLocationBO) {
        try {
            if (!weipinhuiTenant) {
                return null;
            }
            WphCustomInterceptConfigVO wphCustomInterceptConfig = ApplicationConstant.wphCustomInterceptConfig;
            if (BooleanUtils.isNotTrue(wphCustomInterceptConfig.getEnable())) {
                return null;
            }
            if (Objects.isNull(wphCustomInterceptConfig.getDays()) || wphCustomInterceptConfig.getDays() <= 0) {
                return null;
            }
            if (Objects.isNull(wphCustomInterceptConfig.getCount()) ||  wphCustomInterceptConfig.getCount() <= 0) {
                return null;
            }
            String uid = getCustomizeEntity(true, robotCallTaskPO).getCustomizeValue();
            if (StringUtils.isBlank(uid)) {
                logger.info("doWphCustomIntercept uid is empty");
                return null;
            }
            CallOutInterceptDTO calloutIntercept = rcsApiClient.getCalloutIntercept(wphCustomInterceptConfig.getDays(), uid, tenantInfo.getTenantId());
            int dayCallOut = 0;
            int dayCallAnswered = 0;
            if (Objects.nonNull(calloutIntercept)) {
                dayCallOut = Objects.nonNull(calloutIntercept.getDayCallOut()) ? calloutIntercept.getDayCallOut() : 0;
                dayCallAnswered = Objects.nonNull(calloutIntercept.getDayCallAnswered()) ? calloutIntercept.getDayCallAnswered() : 0;
            }
            if (dayCallAnswered > 0) {
                logger.info("doWphCustomIntercept dayCallAnswered={}", dayCallAnswered);
                return null;
            }
            int dayCallOutLocal = 0;
            int dayCallAnsweredLocal = 0;
            if (Objects.nonNull(robotCallJobPO.getEnableRemoteFilter())
                    && Objects.equals(Boolean.TRUE, robotCallJobPO.getEnableRemoteFilter())) {
                CallOutInterceptResultDTO calloutInterceptLocal = WeiPinHuiHelper.getCalloutInterceptLocal(robotCallTaskPO.getCalledPhoneNumber(), wphCustomInterceptConfig.getDays());
                if (Objects.nonNull(calloutInterceptLocal)) {
                    dayCallOutLocal = Objects.nonNull(calloutInterceptLocal.getDayCallOut()) ? calloutInterceptLocal.getDayCallOut() : 0;
                    dayCallAnsweredLocal = Objects.nonNull(calloutInterceptLocal.getDayCallAnswered()) ? calloutInterceptLocal.getDayCallAnswered() : 0;
                }
            }
            if (dayCallAnsweredLocal > 0) {
                logger.info("doWphCustomIntercept dayCallAnsweredLocal={}", dayCallAnsweredLocal);
                return null;
            }
            if ((dayCallOut + dayCallOutLocal) >= wphCustomInterceptConfig.getCount()) {
                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.INTERCEPT, FilterInterceptTypeEnum.CALL_OUT, String.format("近%d天外呼次数>=%d(混合云外呼%d次,本地外呼%d次)，且接通次数为0", wphCustomInterceptConfig.getDays(), wphCustomInterceptConfig.getCount(), dayCallOut, dayCallOutLocal), null, phoneHomeLocationBO);
            }
        } catch (Exception e) {
            logger.error("[LogHub_Warn] doWphCustomIntercept error", e);
        }
        return null;
    }


    private FilteredRobotCallTaskPO checkBlackListRule(RunTimeRobotCallTaskBO robotCallTaskPO, RobotCallJobPO robotCallJobPO, AccountVO accountVO, PhoneHomeLocationBO phoneHomeLocationBO, TenantPO tenantInfo, RiskControlStrategyDTO systemRcs) {
        int tryCount = 0;
        while (true) {
            try {
                RcsCheckResDTO rcsCheckResDTO = doCheckBlackListRule(robotCallTaskPO, robotCallJobPO, tenantInfo, systemRcs);
                if (Objects.nonNull(rcsCheckResDTO)) {
                    FilterInterceptTypeEnum filterInterceptType = Objects.nonNull(rcsCheckResDTO.getInterceptType()) ? FilterInterceptTypeEnum.valueOf(rcsCheckResDTO.getInterceptType().name()) : null;
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.valueOf(rcsCheckResDTO.getFilterType().name()), filterInterceptType, rcsCheckResDTO.getFilterReason(), null, phoneHomeLocationBO);
                }
                break;
            } catch (HystrixRuntimeException hystrixRuntimeException) {
                logger.error("[LogHub_Warn] 大数据黑名单系统超时 tryCount={}", tryCount, hystrixRuntimeException);
                RcsHelper.setRcsTimeOut(robotCallJobPO.getRobotCallJobId());
                if (tryCount++ >= 3) {
                    feishuSendMsgService.sendFeishuWarn(Collections.emptyList(),"大数据黑名单系统超时告警", "调用大数据黑名单系统连续失败三次", RCS_RUNTIME_EXCEPTION_WARN_FEI_SHU_WEBHOOK);
                    return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_EXCEPTION, "大数据黑名单服务异常", null, phoneHomeLocationBO);
                }
            } catch (Exception e) {
                logger.error("[LogHub_Warn] 大数据黑名单系统不可用", e);
                return addFilteredTask(robotCallTaskPO, accountVO, FilterTypeEnum.SYSTEM_EXCEPTION, "大数据黑名单服务异常", null, phoneHomeLocationBO);
            }
        }
        return null;
    }

    private RcsCheckResDTO doCheckBlackListRule(RunTimeRobotCallTaskBO robotCallTaskPO, RobotCallJobPO robotCallJobPO, TenantPO tenantInfo, RiskControlStrategyDTO systemRcs) {

        if (!needYiwiseBlackListRuleFilter(systemRcs)){
            return null;
        }
        String mobile = StringUtils.isNotBlank(robotCallTaskPO.getRealCalledPhoneNumber()) ? robotCallTaskPO.getRealCalledPhoneNumber() : robotCallTaskPO.getCalledPhoneNumber();
        if (!PhoneNumberHelper.isCellPhoneOrFixedPhone(mobile)) {
            //只有真实号码才调用大数据黑名单
            return null;
        }
        RcsHelper.rcsTimeOutSleepSeconds(robotCallJobPO.getRobotCallJobId());

        // 白名单校验
        CheckWhiteListDTO customerRealWhite = new CheckWhiteListDTO();
        customerRealWhite.setPhoneNumber(mobile);
        customerRealWhite.setTenantId(tenantInfo.getTenantId());
        customerRealWhite.setNewMainBrandId(tenantInfo.getNewMainBrandId());
        Boolean inWhite = customerRealWhiteClient.checkPhoneNumberInRealCustomerWhiteList(customerRealWhite);
        if (BooleanUtils.isTrue(inWhite)) {
            return null;
        }
        // 大数据黑名单过滤
        CheckBlackListRuleRequestDTO requestDTO = new CheckBlackListRuleRequestDTO();
        requestDTO.setMobile(mobile);
        requestDTO.setYiwiseBlackListRuleId(systemRcs.getYiwiseBlackListRuleId());
        requestDTO.setCaller(getCaller());
        long currentTimeMillis = System.currentTimeMillis();
        CheckBlackListRuleResponseDTO responseDTO = yiwiseBlackListClient.checkBlackListRule(requestDTO);
        long cost = System.currentTimeMillis() - currentTimeMillis;
        logger.info("[LogHub] YiwiseBlacklistRuleFilter mobile={} cost={} response={}", mobile, cost, responseDTO);
        if (BooleanUtils.isTrue(responseDTO.getBlack())) {
            com.yiwise.rcs.api.enums.FilterTypeEnum filterTypeEnum = ComplaintTypeEnum.SMS.equals(responseDTO.getComplaintType())  ? com.yiwise.rcs.api.enums.FilterTypeEnum.BLACK_LIST_SMS : com.yiwise.rcs.api.enums.FilterTypeEnum.BLACK_LIST;
            return RcsCheckResDTO.builder()
                    .filterType(filterTypeEnum)
                    .filterReason(responseDTO.getMsg())
                    .build();
        }
        return null;
    }

    private boolean needYiwiseBlackListRuleFilter(RiskControlStrategyDTO systemRcs) {
        if (Objects.isNull(systemRcs)) {
            logger.warn("[LogHub_Warn] 系统风控策略为空");
            return false;
        }
        if (BooleanUtils.isNotTrue(systemRcs.getCallOutBlackSwitch())) {
            return false;
        }
        if (Objects.isNull(systemRcs.getYiwiseBlackListRuleId())) {
            return false;
        }
        if (Objects.nonNull(systemRcs.getBlackListType()) && 1 == systemRcs.getBlackListType()) {
            return true;
        }
        return false;
    }

    /**
     * 获取系统调用方
     */
    private String getCaller() {
        if (CommonApplicationConstant.CURR_ENV.isAliyun()) {
            return YiwiseBlackListRuleCallerEnum.AICC.name();
        } else if (CommonApplicationConstant.CURR_ENV.isFinance()) {
            return YiwiseBlackListRuleCallerEnum.AICC_FINANCE.name();
        }
        return "";
    }


}
