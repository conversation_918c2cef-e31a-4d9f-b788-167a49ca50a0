package com.yiwise.core.service.callin.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.dal.dao.CallInReceptionPOMapper;
import com.yiwise.core.dal.dao.CallInRecordPOMapper;
import com.yiwise.core.dal.dao.CsStaffGroupTransferPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.callcost.CallCostDetailBO;
import com.yiwise.core.model.bo.callin.SeatBO;
import com.yiwise.core.model.bo.cs.CallInStatusBO;
import com.yiwise.core.model.bo.robotcalljob.TaskCallResultBO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.vo.DialogFlowListVO;
import com.yiwise.core.model.dto.CsSeatQueueDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.*;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.callin.CallInRecordDetailVO;
import com.yiwise.core.model.vo.callin.CallInRecordListVO;
import com.yiwise.core.model.vo.callin.CallInRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.CallRecordInfoVO;
import com.yiwise.core.model.vo.callrecord.CsStaffCallRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.TransferCallVO;
import com.yiwise.core.model.vo.csseat.CsStaffCallRecordVO;
import com.yiwise.core.model.vo.csseat.XSecondMonitorVO;
import com.yiwise.core.model.vo.phonenumber.TenantPhoneNumberWithPhoneInfoPO;
import com.yiwise.core.service.assistant.AssistantRecordService;
import com.yiwise.core.service.callin.CallInCostService;
import com.yiwise.core.service.callin.CallInDetailService;
import com.yiwise.core.service.callin.CallInRecordService;
import com.yiwise.core.service.callin.CallInStatsService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.csseat.CsStaffGroupService;
import com.yiwise.core.service.engine.csseat.CsStaffInfoService;
import com.yiwise.core.service.engine.csseat.CsStaffStatService;
import com.yiwise.core.service.engine.csseat.CsStaffUserService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.AuthDialogService;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.service.platform.RoleService;
import com.yiwise.core.service.platform.UserRoleService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.control.Try;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2019/1/14 2:57 PM
 **/
@Service
public class CallInRecordServiceImpl extends BasicServiceImpl<CallInRecordPO> implements CallInRecordService {
    private static final Logger logger = LoggerFactory.getLogger(CallInRecordServiceImpl.class);

    @Resource
    private CallInRecordPOMapper callInRecordPOMapper;

    @Resource
    private DialogFlowService dialogFlowService;

    @Resource
    private TenantPhoneNumberService tenantPhoneNumberService;

    @Resource
    private CsStaffInfoService csStaffInfoService;

    @Resource
    private CallInDetailService callInDetailService;

    @Resource
    private IntentLevelTagService intentLevelTagService;

    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;

    @Resource
    private CallInStatsService callInStatsService;

    @Resource
    private CallInCostService callInCostService;

    @Resource
    private PhoneNumberService phoneNumberService;

    @Resource
    private CostListService costListService;

    @Resource
    private CsStaffGroupService csStaffGroupService;

    @Resource
    private TenantService tenantService;
    @Resource
    private UserService userService;

    @Resource
    private DistributorService distributorService;

    @Resource
    private CsStaffGroupTransferPOMapper csStaffGroupTransferPOMapper;

    @Resource
    private CallInReceptionPOMapper callInReceptionPOMapper;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private DataAccessControlService dataAccessControlService;

    @Resource
    private CsStaffStatService csStaffStatService;

    @Resource
    private CsStaffUserService csStaffUserService;

    @Resource
    private CsDetailService csDetailService;

    @Resource
    private AssistantRecordService assistantRecordService;
    @Resource
    private RoleService roleService;
    @Resource
    private AuthDialogService authDialogService;
    @Resource
    private UserRoleService userRoleService;

    @Override
    public PageResultObject<CallInRecordListVO> queryByCondition(Long tenantId,Long userId, CallInRecordQueryVO condition) {

        Integer readParameter = 1;
        condition.setReadParameter(readParameter);
        if (Objects.nonNull(condition.getEndTime())) {
            condition.setEndTime(condition.getEndTime().plusDays(1));
        }
        List<Long> csStaffInfoIdList = null;
        if (StringUtils.isNotBlank(condition.getSearchReceptionSeatName())) {
            List<CsStaffInfoPO> csStaffInfoList = csStaffInfoService.queryByStaffName(tenantId, condition.getSearchReceptionSeatName());
            csStaffInfoIdList = csStaffInfoList.stream().map(CsStaffInfoPO::getCsStaffId).collect(Collectors.toList());
        }
        UserPO userPO=userService.selectByKey(userId);
        Optional<List<Long>> authUserIdList =Optional.empty();
        if(Objects.isNull(condition.getSystemType()) || Objects.equals(SystemEnum.CALL_OUT,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CALL_IN,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_company, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        }else if(Objects.equals(SystemEnum.CUSTOMER_CENTER,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.customer_contact_records_company, AuthResourceUriEnum.customer_contact_records_groups);
        }
        if (StringUtils.isNotBlank(condition.getSearchReceptionSeatName())) {
            List<CsStaffInfoPO> csStaffInfoList = csStaffInfoService.queryByStaffNameAndUserId(tenantId, condition.getSearchReceptionSeatName(),authUserIdList.orElse(null));
            csStaffInfoIdList = csStaffInfoList.stream().map(CsStaffInfoPO::getCsStaffId).collect(Collectors.toList());
        }

        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        condition.setFilterStaffInfoIdList(csStaffInfoIdList);

        List<CallInRecordListVO> resultList = new ArrayList<>();
        //兼容旧crm
        if(CollectionUtils.isNotEmpty(condition.getSeatTypeList())){
            resultList = callInRecordPOMapper.getCallInRecordHistory(tenantId, condition);
        } else {
            resultList = callInRecordPOMapper.queryByCondition(tenantId, condition);
        }

        packageCallInRecord(resultList);

        // 如果是ai接待，则需要设置话术信息
        if (condition.getSeatType() == StaffGroupTypeEnum.AI && CollectionUtils.isNotEmpty(resultList)) {
            Set<Long> receptionIdSet = resultList.stream().filter( x -> (x.getCallInReceptionId() != null && x.getCallInReceptionId() > 0 )).map(CallInRecordListVO::getCallInReceptionId).collect(Collectors.toSet());
            Map<? extends Serializable, CsStaffGroupPO> csStaffGroupPOMap = new HashMap<>();
            Map<Long, Long> receptionMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(receptionIdSet)){
                List<CallInReceptionPO> receptionPOS = callInReceptionPOMapper.selectByCallInreceotionIdList(new ArrayList<>(receptionIdSet));
                if(CollectionUtils.isNotEmpty(receptionPOS)){
                    Set<Long> csStaffGroupIds = receptionPOS.stream().map(CallInReceptionPO::getCsStaffGroupId).collect(Collectors.toSet());
                    receptionMap = receptionPOS.stream().collect(Collectors.toMap(CallInReceptionPO::getCallInReceptionId, CallInReceptionPO::getCsStaffGroupId));
                    csStaffGroupPOMap = csStaffGroupService.selectMapByKeyCollect(csStaffGroupIds);
                }
            }
            List<DialogFlowInfoPO> dialogFlowInfoList = dialogFlowService.findDialogFlowByTenant(tenantId);
            Map<Long, String> dialogFlowMap = dialogFlowInfoList.stream().collect(Collectors.toMap(DialogFlowInfoPO::getId, DialogFlowInfoPO::getName));
            resultList.forEach(callInRecord -> {
                callInRecord.setDialogFlowName(dialogFlowMap.get(callInRecord.getDialogFlowId()));
            });
            for(CallInRecordListVO item : resultList){
                Set<String> aiCsStaffGroupNames = new HashSet<>();
                if(receptionMap.get(item.getCallInReceptionId()) != null) {
                    CsStaffGroupPO groupPO = csStaffGroupPOMap.get(receptionMap.get(item.getCallInReceptionId()));
                    if(groupPO != null) {
                        aiCsStaffGroupNames.add(groupPO.getGroupName());
                        item.setAiCsStaffGroupNames(aiCsStaffGroupNames);
                    }
                }
            }
            List<Long> staffIdList = resultList.stream().map(CallInRecordListVO::getStaffId).distinct().filter(staffId -> staffId > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(staffIdList)) {
                List<CsStaffInfoPO> staffInfoList = csStaffInfoService.selectListByKeyCollect(staffIdList);
                Map<Long, CsStaffInfoPO> csStaffMap = staffInfoList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, x -> x));
                resultList.forEach(callInRecord -> {
                    if(callInRecord.getStaffId() != null && csStaffMap.get(callInRecord.getStaffId()) != null) {
                        callInRecord.setCsStaffInfo(csStaffMap.get(callInRecord.getStaffId()));
                        callInRecord.setCsStaffName(callInRecord.getCsStaffInfo().getCsName());
                    }
                });
            }
        }

        // 如果是人工接待，需要设置人工客服信息
        if (condition.getSeatType() == StaffGroupTypeEnum.CS) {
            List<Long> staffIdList = resultList.stream().map(CallInRecordListVO::getCsStaffId).distinct().filter(staffId -> staffId > 0).collect(Collectors.toList());
            List<Long> staffGrouopIdList = resultList.stream().map(CallInRecordListVO::getStaffGroupId).distinct().filter(groupId -> (null != groupId && groupId > 0)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(staffIdList)) {
                List<CsStaffInfoPO> staffInfoList = csStaffInfoService.selectListByKeyCollect(staffIdList);
                Map<Long, CsStaffInfoPO> csStaffMap = staffInfoList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, x -> x));
                resultList.forEach(callInRecord -> {
                    callInRecord.setCsStaffInfo(csStaffMap.get(callInRecord.getCsStaffId()));
                    callInRecord.setCsStaffName(callInRecord.getCsStaffInfo() != null ? callInRecord.getCsStaffInfo().getCsName() : null);
                });
            }
            if(CollectionUtils.isNotEmpty(staffGrouopIdList)){
                Map<? extends Serializable, CsStaffGroupPO> csStaffGroupPOMap = csStaffGroupService.selectMapByKeyCollect(staffGrouopIdList);
                resultList.forEach(callInRecord -> {
                    CsStaffGroupPO groupPO = csStaffGroupPOMap.get(callInRecord.getStaffGroupId());
                    if(groupPO != null) {
                        callInRecord.setCsStaffGroupName(groupPO.getGroupName());
                    }
                });
            }

        }


        if (CollectionUtils.isNotEmpty(resultList)) {
            // 设置线路信息
            List<Long> phoneNumberIds = resultList.stream().map(CallInRecordListVO::getPhoneNumberId).distinct().collect(Collectors.toList());
            Set<Long> customerPersonIds = resultList.stream().map(CallInRecordListVO::getCustomerPersonId).collect(Collectors.toSet());

            List<TenantPhoneNumberWithPhoneInfoPO> phoneNumberPOList = tenantPhoneNumberService.selectSupportCallInListByTenantId(tenantId, null);
            Map<Long, TenantPhoneNumberWithPhoneInfoPO> phoneInfoPOMap = phoneNumberPOList.stream().collect(Collectors.toMap(TenantPhoneNumberWithPhoneInfoPO::getPhoneNumberId, x -> x));
            resultList.forEach(callInRecord -> {
                callInRecord.setPhoneNumberInfo(phoneInfoPOMap.get(callInRecord.getPhoneNumberId()));
            });
        }

        // 设置是否已读的状态
        resultList.forEach(this::fillCallInRecordReadStatus);

        return PageResultObject.of(resultList);
    }

    private void buildIVRDetail(List<? extends CallInRecordListVO> resultList){
        if(resultList == null){
            return;
        }
        Set<Long> csStaffGroupIds = new HashSet<>();
        Set<Long> dialogFlowIds = new HashSet<>();
        resultList.forEach(item -> {
            csStaffGroupIds.addAll(item.getAiCsStaffGroupIds());
            csStaffGroupIds.addAll(item.getHumanCsStaffGroupIds());
            dialogFlowIds.addAll(item.getDialogFlowIds());
        });
        Map<? extends Serializable, CsStaffGroupPO> csStaffGroupPOMap = csStaffGroupService.selectMapByKeyCollect(csStaffGroupIds);
        Map<? extends Serializable, DialogFlowInfoPO> dialogFlowInfoPOMap = dialogFlowService.selectMapByKeyCollect(dialogFlowIds);

        resultList.forEach(item -> {
            // ai坐席组
            Set<Long> aiCsStaffGroupIds = item.getAiCsStaffGroupIds();
            if (CollectionUtils.isNotEmpty(aiCsStaffGroupIds)) {
                Set<String> aiCsStaffGroupNames = new HashSet<>();
                aiCsStaffGroupIds.forEach(id -> {
                    CsStaffGroupPO csStaffGroupPO = csStaffGroupPOMap.get(id);
                    if (csStaffGroupPO != null) {
                        aiCsStaffGroupNames.add(csStaffGroupPO.getGroupName());
                    }
                });
                item.setAiCsStaffGroupNames(aiCsStaffGroupNames);
                //再将列表转字符串显示
                if(CollectionUtils.isNotEmpty(aiCsStaffGroupNames)) {
                    item.setCsStaffGroupName(String.join(";", aiCsStaffGroupNames));
                }
            }
            // 人工坐席组
            Set<Long> humanCsGroupIds = item.getHumanCsStaffGroupIds();
            if (CollectionUtils.isNotEmpty(humanCsGroupIds)) {
                Set<String> humanCsStaffGroupNames = new HashSet<>();
                humanCsGroupIds.forEach(id -> {
                    CsStaffGroupPO csStaffGroupPO = csStaffGroupPOMap.get(id);
                    if (csStaffGroupPO != null) {
                        humanCsStaffGroupNames.add(csStaffGroupPO.getGroupName());
                    }
                });
                item.setHumanCsStaffGroupNames(humanCsStaffGroupNames);
                //再将列表转字符串显示
                if(CollectionUtils.isNotEmpty(humanCsStaffGroupNames)) {
                    if(StringUtils.isNoneEmpty(item.getCsStaffGroupName())) {
                        humanCsStaffGroupNames.add(item.getCsStaffGroupName());
                    }
                    item.setCsStaffGroupName(String.join(";", humanCsStaffGroupNames));
                }
            }

            //话术
            Set<Long> ivrDialogFlowIds = item.getDialogFlowIds();
            if (CollectionUtils.isNotEmpty(ivrDialogFlowIds)) {
                Set<String> dialogFlowNames = new HashSet<>();
                ivrDialogFlowIds.forEach(id -> {
                    DialogFlowInfoPO dialogFlowInfoPO = dialogFlowInfoPOMap.get(id);
                    if (dialogFlowInfoPO != null) {
                        dialogFlowNames.add(dialogFlowInfoPO.getName());
                    }
                });
                item.setDialogFlowNames(dialogFlowNames);
            }
        });
    }

    @Override
    public PageResultObject<CallInRecordListVO> callInRecordHistory(Long tenantId,Long userId, CallInRecordQueryVO condition) {
        Integer readParameter = 1;
        condition.setReadParameter(readParameter);
        List<Long> csStaffInfoIdList = null;
        UserPO userPO = userService.selectByKey(userId);
        Optional<List<Long>> authUserIdList = Optional.empty();
        if(Objects.isNull(condition.getSystemType()) || Objects.equals(SystemEnum.CALL_OUT,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CALL_IN,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListInAiccWithPart(userPO, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_company, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_organization, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_partDialog);
        }else if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        }else if(Objects.equals(SystemEnum.CUSTOMER_CENTER,condition.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.customer_contact_records_company, AuthResourceUriEnum.customer_contact_records_groups);
        }
        if (StringUtils.isNotBlank(condition.getSearchReceptionSeatName())) {
            List<CsStaffInfoPO> csStaffInfoList = csStaffInfoService.queryByStaffNameAndUserId(tenantId, condition.getSearchReceptionSeatName(),authUserIdList.orElse(null));
            csStaffInfoIdList = csStaffInfoList.stream().map(CsStaffInfoPO::getCsStaffId).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(authUserIdList.orElse(null))&&CollectionUtils.isNotEmpty(csStaffInfoIdList)){
            condition.setFilterStaffInfoIdList(csStaffInfoIdList);
        }
        if(roleService.hasAuthResource(userId, SystemEnum.AICC, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_partDialog)){
            List<Long> receptionIdList = condition.getReceptionIdList();
            Long roleId=getRoleId(userId,condition.getSystemType());
            if(Objects.isNull(roleId)){
                logger.info("指定了特殊场景,但是当前角色未找到");
                return PageResultObject.of(Lists.newArrayList());
            }
            AuthDialogPO authDialogPO = authDialogService.selectByTenantIdAndRoleId(tenantId,roleId);
            Set<Long> bindCallInReceptionDialogFlowList = authDialogPO.getBindCallInReceptionDialogFlowList();
            if(CollectionUtils.isEmpty(receptionIdList)){
                receptionIdList=Lists.newArrayList();
            }
            if(CollectionUtils.isEmpty(bindCallInReceptionDialogFlowList)){
                bindCallInReceptionDialogFlowList=new HashSet<>();
            }
            List<Long> re= null;
            if(CollectionUtils.isEmpty(receptionIdList)&&CollectionUtils.isNotEmpty(bindCallInReceptionDialogFlowList)){
                condition.setReceptionIdList(new ArrayList<>(bindCallInReceptionDialogFlowList));
            }else if(CollectionUtils.isNotEmpty(receptionIdList)&&CollectionUtils.isNotEmpty(bindCallInReceptionDialogFlowList)){
                re= (List<Long>) CollectionUtils.intersection(receptionIdList,bindCallInReceptionDialogFlowList);
                if(CollectionUtils.isEmpty(re)){
                    return PageResultObject.of(Lists.newArrayList());
                }
                condition.setReceptionIdList(re);
            }
        }
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());

        List<CallInRecordListVO> resultList = callInRecordPOMapper.getCallInRecordHistory(tenantId, condition);

        packageCallInRecordInfo(resultList, tenantId);

        return PageResultObject.of(resultList);

    }

    private Long getRoleId(Long userId, SystemEnum system) {
        UserRolePO userRolePO = userRoleService.selectByUserIdAndSystemType(userId, SystemEnum.CALL_IN.equals(system)? SystemEnum.CRM : system);
        if (userRolePO == null) {
            return null;
        }
        RolePO role = roleService.selectByKey(userRolePO.getRoleId());
        if (role == null) {
            return null;
        }
        return role.getRoleId();
    }


    @Override
    public void packageCallInRecordInfo(List<? extends CallInRecordListVO> resultList, Long tenantId) {
        if (CollectionUtils.isNotEmpty(resultList)) {
            // 设置线路信息
            List<Long> phoneNumberIds = resultList.stream().map(CallInRecordListVO::getPhoneNumberId).distinct().collect(Collectors.toList());
            Set<Long> customerPersonIds = resultList.stream().map(CallInRecordListVO::getCustomerPersonId).collect(Collectors.toSet());

            List<TenantPhoneNumberWithPhoneInfoPO> phoneNumberPOList = tenantPhoneNumberService.selectSupportCallInListByTenantId(tenantId, null);
            Map<Long, TenantPhoneNumberWithPhoneInfoPO> phoneInfoPOMap = phoneNumberPOList.stream().collect(Collectors.toMap(TenantPhoneNumberWithPhoneInfoPO::getPhoneNumberId, x -> x));
            Set<Integer> intentLevelSet = resultList.stream().map(CallInRecordListVO::getIntentLevel).collect(Collectors.toSet());
            intentLevelSet.addAll(resultList.stream().filter(item -> item.getRealIntentLevel() != null).map(CallInRecordListVO::getRealIntentLevel).collect(Collectors.toSet()));
            Map<? extends Serializable, IntentLevelTagDetailPO> intentLevelTagDetailPOMap = intentLevelTagDetailService.selectMapByKeyCollect(intentLevelSet);
            resultList.forEach(callInRecord -> {
                TenantPhoneNumberWithPhoneInfoPO phoneNumberInfo = phoneInfoPOMap.get(callInRecord.getPhoneNumberId());
                if (phoneNumberInfo != null) {
                    callInRecord.setPhoneNumberInfo(phoneNumberInfo);
                    String prov = phoneNumberInfo.getProvince();
                    String city = phoneNumberInfo.getCity();
                    if (!org.springframework.util.StringUtils.isEmpty(prov) && !org.springframework.util.StringUtils.isEmpty(city)) {
                        prov.replaceAll("省", "");
                        prov.replaceAll("市", "");
                        prov.replaceAll("自治区", "");
                        city.replaceAll("市", "");
                    }
                    if (StringUtils.equals(prov, city)) {
                        callInRecord.setLocation(city);
                    } else {
                        callInRecord.setLocation(prov + city);
                    }
                }
                IntentLevelTagDetailPO intentLevelTagDetailPO = intentLevelTagDetailPOMap.get(callInRecord.getIntentLevel());
                if (intentLevelTagDetailPO != null) {
                    callInRecord.setIntentLevelName(intentLevelTagDetailPO.getName());
                }
                IntentLevelTagDetailPO intentLevelTagDetailPO1 = intentLevelTagDetailPOMap.get(callInRecord.getRealIntentLevel());
                if (intentLevelTagDetailPO1 != null) {
                    callInRecord.setRealIntentLevelName(intentLevelTagDetailPO1.getName());
                }
            });

            Set<Long> receptionIdSet = resultList.stream().map(CallInRecordListVO::getCallInReceptionId).filter(x -> x != null).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(receptionIdSet)){
                List<CallInReceptionPO> callInReceptionPOList = callInReceptionPOMapper.selectByCallInreceotionIdList(new ArrayList<>(receptionIdSet));
                if(CollectionUtils.isNotEmpty(callInReceptionPOList)){
                    Map<Long, CallInReceptionPO> map = callInReceptionPOList.stream().collect(Collectors.toMap(CallInReceptionPO::getCallInReceptionId, x -> x));

                    resultList.forEach(callInRecord -> {
                        if(callInRecord.getCallInReceptionId() != null && map.get(callInRecord.getCallInReceptionId()) != null) {
                            callInRecord.setReceptionName(map.get(callInRecord.getCallInReceptionId()).getRemark());
                        }
                    });
                }
            }
        }

        List<Long> staffIdList = resultList.stream().map(CallInRecordListVO::getStaffId).distinct().filter(staffId -> staffId != null && staffId > 0).collect(Collectors.toList());
        List<Long> csStaffIdList = resultList.stream().map(CallInRecordListVO::getCsStaffId).distinct().filter(staffId -> staffId != null && staffId > 0).collect(Collectors.toList());
        Set<Long> staffIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(staffIdList)){
            staffIdSet.addAll(staffIdList);
        }
        if(CollectionUtils.isNotEmpty(csStaffIdList)){
            staffIdSet.addAll(csStaffIdList);
        }
        if (CollectionUtils.isNotEmpty(staffIdSet)) {
            List<CsStaffInfoPO> staffInfoList = csStaffInfoService.selectListByKeyCollect(staffIdSet);
            Map<Long, CsStaffInfoPO> csStaffMap = staffInfoList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, x -> x));
            resultList.forEach(callInRecord -> {
                if(callInRecord.getStaffId() != null && csStaffMap.get(callInRecord.getStaffId()) != null) {
                    callInRecord.setCsStaffInfo(csStaffMap.get(callInRecord.getStaffId()));
                    callInRecord.setCsStaffName(callInRecord.getCsStaffInfo().getCsName());
                }
                if(callInRecord.getCsStaffId() != null && csStaffMap.get(callInRecord.getCsStaffId()) != null){
                    callInRecord.setCsStaffInfo(csStaffMap.get(callInRecord.getCsStaffId()));
                    callInRecord.setCsStaffName(callInRecord.getCsStaffInfo().getCsName());
                }
            });
        }

        buildIVRDetail(resultList);

        List<Long> staffGroupIdList = resultList.stream().map(CallInRecordListVO::getStaffGroupId).distinct().filter(groupId -> groupId != null && groupId > 0).collect(Collectors.toList());
        List<Long> csStaffGroupIdList = resultList.stream().map(CallInRecordListVO::getCsStaffGroupId).distinct().filter(groupId -> groupId != null && groupId > 0).collect(Collectors.toList());
        Set<Long> staffGroupIdSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(staffGroupIdList)){
            staffGroupIdSet.addAll(staffGroupIdList);
        }
        if(CollectionUtils.isNotEmpty(csStaffGroupIdList)){
            staffGroupIdSet.addAll(csStaffGroupIdList);
        }

        if (CollectionUtils.isNotEmpty(staffGroupIdSet)) {
            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectListByKeyCollect(staffGroupIdSet);
            Map<Long, CsStaffGroupPO> groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, x -> x));
            resultList.forEach(callInRecord -> {
                if(callInRecord.getStaffGroupId() != null && groupMap.get(callInRecord.getStaffGroupId()) != null) {
                    callInRecord.setCsStaffGroupName(groupMap.get(callInRecord.getStaffGroupId()).getGroupName());
                }
                if(callInRecord.getCsStaffGroupId() != null && groupMap.get(callInRecord.getCsStaffGroupId()) != null){
                    callInRecord.setCsStaffGroupName(groupMap.get(callInRecord.getCsStaffGroupId()).getGroupName());
                }
            });
        }

        // 设置是否已读的状态
        resultList.forEach(this::fillCallInRecordReadStatus);
    }

    /**
     * 根据接待类型查询呼入记录
     * @param callCostQuery
     * @param seatType
     * @return
     */
    @Override
    public PageResultObject<CallCostDetailBO> queryBySeatType(CallCostQueryVO callCostQuery, StaffGroupTypeEnum seatType,CallInTypeEnum callInTypeEnum, Integer pageNum, Integer pageSize) {
        CallInRecordQueryVO condition = new CallInRecordQueryVO();
        if(Objects.nonNull(callCostQuery.getBeginDate())){
            condition.setStartTime(callCostQuery.getBeginDate().atStartOfDay());
        }
        if (Objects.nonNull(callCostQuery.getEndDate())) {
            condition.setEndTime(callCostQuery.getEndDate().plusDays(1).atStartOfDay());
        }
        condition.setSeatType(seatType);
        condition.setSearchCallInRecordId(callCostQuery.getCallRecordId());
        condition.setSearchCustomerPhone(callCostQuery.getCalledPhoneNumber());
        condition.setChatDuration(callCostQuery.getChatDuration());
        if(callCostQuery.getTenantPhoneNumberId() != null) {
            TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectByKey(callCostQuery.getTenantPhoneNumberId());
            if(tenantPhoneNumberPO != null) {
                condition.setPhoneNumberId(tenantPhoneNumberPO.getPhoneNumberId());
            }
        }
        if (Objects.nonNull(callCostQuery.getResultStatus())) {
            condition.setResultStatus(CallInResultStatusEnum.getByName(callCostQuery.getResultStatus().name()));
        }
        PageHelper.startPage(pageNum, pageSize);
        List<CallInRecordListVO> resultList;
        if(CallInTypeEnum.ALL.equals(callInTypeEnum.getCode())){
            condition.setSeatTypeList(Arrays.asList(StaffGroupTypeEnum.CS,StaffGroupTypeEnum.AI));
            resultList = callInRecordPOMapper.getCallInRecordHistory(callCostQuery.getTenantId(), condition);
        } else {
            resultList = callInRecordPOMapper.queryByCondition(callCostQuery.getTenantId(), condition);
        }
        packageCallInRecord(resultList);
        // 结果转换
        List<CallCostDetailBO> list = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        resultList.forEach(item -> {
            ids.add(item.getPhoneNumberId());
        });
        Map<? extends Serializable, PhoneNumberPO> phoneNumberPOMap = phoneNumberService.selectMapByKeyCollect(ids);
        resultList.forEach(item -> {
            CallCostDetailBO callCostDetailBO = new CallCostDetailBO();
            callCostDetailBO.setCallRecordId(item.getCallInRecordId());
            callCostDetailBO.setCustomerPersonId(item.getCustomerPersonId());
            callCostDetailBO.setCalledPhoneNumber(item.getCustomerPhoneNumber());
            callCostDetailBO.setRobotCallJobName(null);
            callCostDetailBO.setStartTime(item.getStartTime());
            TenantPhoneNumberPO tenantPhoneNumberPO=tenantPhoneNumberService.selectOneByTenantIdAndPhoneNumberId(item.getTenantId(),item.getPhoneNumberId());
            callCostDetailBO.setTenantPhoneNumberStatus(Objects.nonNull(tenantPhoneNumberPO)?tenantPhoneNumberPO.getEnabledStatus():0);
            PhoneNumberPO phoneNumberInfo = phoneNumberPOMap.get(item.getPhoneNumberId());
            if (Objects.nonNull(phoneNumberInfo)) {
                callCostDetailBO.setCallingPhoneNumberName(StringUtils.isBlank(phoneNumberInfo.getPhoneName()) ? phoneNumberInfo.getPhoneNumber() : phoneNumberInfo.getPhoneName());
                callCostDetailBO.setResultStatus(CallInResultStatusEnum.ANSWERED.equals(item.getResultStatus())?DialStatusEnum.ANSWERED:DialStatusEnum.NO_ANSWER);
                callCostDetailBO.setCalledPhoneNumberLocationProv(phoneNumberInfo.getProvince());
                callCostDetailBO.setCalledPhoneNumberLocationCity(phoneNumberInfo.getCity());
            }
            callCostDetailBO.setChatDuration(item.getChatDuration());
            callCostDetailBO.setCallCost(item.getCallCost());

            callCostDetailBO.setSeatType(item.getSeatType());


            list.add(callCostDetailBO);
        });
        return PageResultObject.of(list);
    }

    @Override
    public void updateReadStatus(Long tenantId, Long callInRecordId, SystemEnum system) {
        int parameter = 0;
        switch (system) {
            case BOSS:
                parameter = 4;
                break;
            case OPE:
                parameter = 2;
                break;
            case CRM:
            case MINIAPP:
                parameter = 1;
                break;
        }
        callInRecordPOMapper.updateReadStatus(tenantId, callInRecordId, parameter);
    }

    private void fillCallInRecordReadStatus(CallInRecordListVO callInRecord) {
        Integer crmReadStatusMask = 1;
        Integer bossReadStatusMask = 4;
        Integer opeReadStatusMask = 2;
        callInRecord.setCrmRead((callInRecord.getReadStatus() & crmReadStatusMask) > 0);
        callInRecord.setOpeRead((callInRecord.getReadStatus() & opeReadStatusMask) > 0);
        callInRecord.setBossRead((callInRecord.getReadStatus() & bossReadStatusMask) > 0);
    }

    private CallRecordReadStatusEnum fillCallInRecordReadStatus(Integer read, SystemEnum systemEnum) {
        CallRecordReadStatusEnum readStatus = CallRecordReadStatusEnum.NOT_READ;
        if(read == null){
            return readStatus;
        }
        Integer crmReadStatusMask = 1;
        Integer bossReadStatusMask = 4;
        Integer opeReadStatusMask = 2;
        switch (systemEnum) {
            case BOSS:
                readStatus = (read & bossReadStatusMask) > 0 ? CallRecordReadStatusEnum.HAS_READ : CallRecordReadStatusEnum.NOT_READ;
                break;
            case OPE:
                readStatus = (read & opeReadStatusMask) > 0 ? CallRecordReadStatusEnum.HAS_READ : CallRecordReadStatusEnum.NOT_READ;
                break;
            case CRM:
                readStatus = (read & crmReadStatusMask) > 0 ? CallRecordReadStatusEnum.HAS_READ : CallRecordReadStatusEnum.NOT_READ;
                break;
        }
        return readStatus;
    }
    /**
     * 插入通话记录
     *
     * @param seatBO
     * @param callerNumber
     */
    @Override
    public Long insertCallInRecord(SeatBO seatBO, String callerNumber, boolean isDoCallStats) {
        Long chatDuration = 0L;
        Integer chatRound = 0;
        Integer intentLevel = 3;
        Long intentLevelTagId = Objects.isNull(seatBO.getIntentLevelTagId()) ? 0 : seatBO.getIntentLevelTagId();

        CallInRecordPO callInRecordPO = new CallInRecordPO();
        callInRecordPO.setStaffGroupId(seatBO.getStaffGroupId());
        callInRecordPO.setStaffId(seatBO.getStaffId());
	    Long tenantId = seatBO.getTenantId();
	    callInRecordPO.setTenantId(tenantId);
	    if (tenantId != null) {
		    TenantPO tenant = tenantService.selectByKey(tenantId);
		    if (tenant != null) {
			    callInRecordPO.setDistributorId(tenant.getDistributorId());
		    }
	    }
        Long userId = seatBO.getCallInReceptionLastUpdateUserId();
        UserPO userPO = userService.selectByKey(userId == null ? 1L : userId);
        callInRecordPO.setDistributorId(userPO.getDistributorId());
        callInRecordPO.setDialogFlowId(seatBO.getDialogFlowId());
        callInRecordPO.setPhoneNumberId(seatBO.getPhoneNumberPO().getPhoneNumberId());
        callInRecordPO.setSeatType(seatBO.getSeatTypeEnum());
        callInRecordPO.setCustomerPhoneNumber(callerNumber);
        callInRecordPO.setIntentLevelTagId(intentLevelTagId);
        callInRecordPO.setManualMarked(false);
        if(!seatBO.isDispatchSuccess()){
            callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_CS);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_CS);
        }else {
            callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_ANSWER);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_ANSWER);
        }
        callInRecordPO.setStartTime(LocalDateTime.now());
        if(seatBO.getQueueDuration() != null){
            callInRecordPO.setEndTime(callInRecordPO.getStartTime().plusSeconds(seatBO.getQueueDuration()));
        }
        callInRecordPO.setIntentLevel(intentLevel);
        callInRecordPO.setChatDuration(chatDuration);
        callInRecordPO.setChatRound(chatRound);
        callInRecordPO.setCustomerConcern(new HashSet<>());
        callInRecordPO.setAttributes(new HashSet<>());
        callInRecordPO.setProperties(new HashMap<>());
        callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
        callInRecordPO.setHangupBy(CallJobHangupEnum.INITIAL_HANGUP);
        callInRecordPO.setCreateUserId(seatBO.getCallInReceptionLastUpdateUserId());
        callInRecordPO.setUpdateUserId(seatBO.getCallInReceptionLastUpdateUserId());
        callInRecordPO.setCallCost(0L);
        callInRecordPO.setCallInReceptionId(seatBO.getCallInReceptionId());
        callInRecordPO.setQueueDuration(seatBO.getQueueDuration());
        callInRecordPO.setRingDuration(seatBO.getRingDuration());
        if(!seatBO.isDispatchSuccess() && seatBO.isHasOnline()){
            callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_CS);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_CS);
        }
        if(!seatBO.isDispatchSuccess() && seatBO.isQueueOvertime()){
            callInRecordPO.setResultStatus(CallInResultStatusEnum.QUEUE_OVER_TIME);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.QUEUE_OVER_TIME);
        }
        if(!seatBO.isDispatchSuccess() && seatBO.isQueueGiveUp()){
            callInRecordPO.setResultStatus(CallInResultStatusEnum.QUEUE_GIVE_UP);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.QUEUE_GIVE_UP);
        }
        callInRecordPO.setCsProcess(seatBO.getCsProcess());
        callInRecordPO.setCustomerPersonId(-1L); // TODO
        saveNotNull(callInRecordPO);

        Long callInRedordId = callInRecordPO.getCallInRecordId();
        if (isDoCallStats) {
            callInStatsService.doWhenAfterCallInHangup(callInRedordId);
        }

        return callInRedordId;
    }

    /**
     * 插入通话记录
     *  @param seatBO
     * @param callerNumber
     * @param isMonitor
     */
    @Override
    public Long insertReceptionCallInRecord(SeatBO seatBO, String callerNumber,
                                            Long callRecordId, CsCallCategoryEnum csCallCategory, Long csStaffId,
                                            Long fromCallInRecordId, Long fromFirstCallInRecordId,
                                            CallRecordPO callRecordInfo, CsSeatQueueDTO seatQueueDTO, String identifyId,
                                            Boolean isMonitor) {

        CallInRecordPO callInRecordPO = new CallInRecordPO();
        callInRecordPO.setStaffGroupId(seatBO.getStaffGroupId());
        callInRecordPO.setStaffId(seatBO.getStaffId());
	    Long tenantId = seatBO.getTenantId();
	    callInRecordPO.setTenantId(tenantId);
	    if (tenantId != null) {
		    TenantPO tenant = tenantService.selectByKey(tenantId);
		    if (tenant != null) {
			    callInRecordPO.setDistributorId(tenant.getDistributorId());
		    }
	    }
        callInRecordPO.setDialogFlowId(seatBO.getDialogFlowId());
        callInRecordPO.setSeatType(seatBO.getSeatTypeEnum());
        callInRecordPO.setCustomerPhoneNumber(callerNumber);
        callInRecordPO.setManualMarked(false);

        callInRecordPO.setIntentLevel(callRecordInfo.getIntentLevel());
        callInRecordPO.setChatRound(callRecordInfo.getChatRound());
        callInRecordPO.setCustomerConcern(callRecordInfo.getCustomerConcern());
        callInRecordPO.setAttributes(callRecordInfo.getAttributes());
        callInRecordPO.setProperties(callRecordInfo.getProperties());
        callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
        callInRecordPO.setHangupBy(CallJobHangupEnum.INITIAL_HANGUP);
        callInRecordPO.setCreateUserId(seatBO.getCallInReceptionLastUpdateUserId());
        callInRecordPO.setUpdateUserId(seatBO.getCallInReceptionLastUpdateUserId());
        callInRecordPO.setCallCost(0L);
        callInRecordPO.setCallInReceptionId(seatBO.getCallInReceptionId());
        callInRecordPO.setRelevantId(callRecordId);
        callInRecordPO.setCsCallCategory(csCallCategory);
        callInRecordPO.setFromCsStaffId(csStaffId);
        callInRecordPO.setFromCallInRecordId(fromCallInRecordId);
        callInRecordPO.setFromFirstCallInRecordId(fromFirstCallInRecordId);
        callInRecordPO.setChatDuration(callRecordInfo.getChatDuration());
        callInRecordPO.setChatRound(callRecordInfo.getChatRound());
        callInRecordPO.setAnalysisBasis(callRecordInfo.getAnalysisBasis());
        callInRecordPO.setAttributes(callRecordInfo.getAttributes());
        callInRecordPO.setCustomerAudioUrl(callRecordInfo.getCustomerAudioUrl());
        callInRecordPO.setFullAudioUrl(callRecordInfo.getFullAudioUrl());
        callInRecordPO.setCustomerConcern(callRecordInfo.getCustomerConcern());
        callInRecordPO.setStartTime(callRecordInfo.getStartTime());
        callInRecordPO.setEndTime(callRecordInfo.getStartTime().plusSeconds(callInRecordPO.getChatDuration()));
        callInRecordPO.setCsMonitorFlag(BooleanUtils.isTrue(isMonitor) ? 1 : 0);
        if (callRecordInfo.getCsMonitorFlag() != null && callRecordInfo.getCsMonitorFlag() == 1 &&
            callRecordInfo.getCsTransferAccept() != null && callRecordInfo.getCsTransferAccept() == 0) {
            //如果监听但是没有介入
            callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_ANSWER);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_ANSWER);
        } else {
            callInRecordPO.setTransferStartTime(seatBO.getTransferStartTime());
            if (seatBO.getTransferStartTime() != null) {
                callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
                callInRecordPO.setSeatStatus(CallInResultStatusEnum.ANSWERED);
            } else {
                if (!seatQueueDTO.isHasOnline()) {
                    //人工坐席不在线
                    callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_CS);
                    callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_CS);
                } else if (seatQueueDTO.isQueueOvertime()) {
                    //人工坐席没有空闲坐席
                    callInRecordPO.setResultStatus(CallInResultStatusEnum.QUEUE_OVER_TIME);
                    callInRecordPO.setSeatStatus(CallInResultStatusEnum.QUEUE_OVER_TIME);
                } else if (seatQueueDTO.isQueueGiveUp()) {
                    callInRecordPO.setResultStatus(CallInResultStatusEnum.QUEUE_GIVE_UP);
                    callInRecordPO.setSeatStatus(CallInResultStatusEnum.QUEUE_GIVE_UP);
                } else {
                    //这里去获取振铃放弃的，如果get identifyId  这个有值，说明是客服自己放弃了, 人工介入的应该就是未接听
                    String hangUp = redisOpsService.get(RedisKeyCenter.getCsHangUpKey(identifyId));
                    if ((StringUtils.isNoneEmpty(hangUp) && CallJobHangupEnum.CS_HANGUP.name().equalsIgnoreCase(hangUp)) || CsTransferNotifyEnum.NOTIFY_SUCCESS.equals(callRecordInfo.getCsTransferNotify())) {
                        callInRecordPO.setResultStatus(CallInResultStatusEnum.NO_ANSWER);
                        callInRecordPO.setSeatStatus(CallInResultStatusEnum.NO_ANSWER);
                    } else {
                        callInRecordPO.setResultStatus(CallInResultStatusEnum.RING_GIVE_UP);
                        callInRecordPO.setSeatStatus(CallInResultStatusEnum.RING_GIVE_UP);
                    }
                }
            }
        }
        callInRecordPO.setQueueDuration(seatBO.getQueueDuration());
        callInRecordPO.setCsProcess(CsProcessEnum.CS);
        saveNotNull(callInRecordPO);

        Long callInRedordId = callInRecordPO.getCallInRecordId();

        return callInRedordId;
    }

    @Override
    public void addTransferToCsRecordListFromAiCallOut(Long callInRecordId, String callerNumber, Long callRecordId, CsCallCategoryEnum csCallCategory, CallRecordPO callRecordInfo, List<TransferCallVO> list, CsSeatQueueDTO csSeatQueueDTO, String identifyId) {
        if(list == null){
            return;
        }
	    Long tenantId = callRecordInfo.getTenantId();
	    Long distributorId = null;
	    if (tenantId != null) {
		    TenantPO tenant = tenantService.selectByKey(tenantId);
		    if (tenant != null) {
			    distributorId = tenant.getDistributorId();
		    }
	    }
	    List<CallInDetailPO> detailList = callInDetailService.queryByCallInRecordId(tenantId, callInRecordId);
        Long lastCallInRecordId = callInRecordId;
        for(TransferCallVO vo : list){
            CallInRecordPO callInRecordPO = new CallInRecordPO();
            callInRecordPO.setStaffGroupId(null);
            callInRecordPO.setStaffId(vo.getCurrentCsStaffId());
            callInRecordPO.setTenantId(tenantId);
            callInRecordPO.setDistributorId(distributorId);
            callInRecordPO.setDialogFlowId(callRecordInfo.getDialogFlowId());
            callInRecordPO.setSeatType(StaffGroupTypeEnum.CS);
            callInRecordPO.setCustomerPhoneNumber(callerNumber);
            callInRecordPO.setManualMarked(false);
            callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setStartTime(callRecordInfo.getStartTime());

            callInRecordPO.setIntentLevel(callRecordInfo.getIntentLevel());
            callInRecordPO.setChatDuration(callRecordInfo.getChatDuration());
            callInRecordPO.setChatRound(callRecordInfo.getChatRound());
            callInRecordPO.setCustomerConcern(callRecordInfo.getCustomerConcern());
            callInRecordPO.setAttributes(callRecordInfo.getAttributes());
            callInRecordPO.setProperties(callRecordInfo.getProperties());
            callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
            callInRecordPO.setHangupBy(CallJobHangupEnum.CS_HANGUP);
            callInRecordPO.setCreateUserId(vo.getCurrentUserId());
            callInRecordPO.setUpdateUserId(vo.getCurrentUserId());
            callInRecordPO.setCallCost(0L);
            callInRecordPO.setCallInReceptionId(0L);
            callInRecordPO.setRelevantId(callRecordId);
            callInRecordPO.setCsCallCategory(csCallCategory);
            callInRecordPO.setFromCsStaffId(vo.getFromCsStaffId());
            callInRecordPO.setFromCallInRecordId(lastCallInRecordId);
            callInRecordPO.setFromFirstCallInRecordId(callInRecordId);
            callInRecordPO.setChatDuration(callRecordInfo.getChatDuration());
            callInRecordPO.setChatRound(callRecordInfo.getChatRound());
            callInRecordPO.setAnalysisBasis(callRecordInfo.getAnalysisBasis());
            callInRecordPO.setAttributes(callRecordInfo.getAttributes());
            callInRecordPO.setCustomerAudioUrl(callRecordInfo.getCustomerAudioUrl());
            callInRecordPO.setFullAudioUrl(callRecordInfo.getFullAudioUrl());
            callInRecordPO.setCustomerConcern(callRecordInfo.getCustomerConcern());
            callInRecordPO.setStartTime(callRecordInfo.getStartTime());
            callInRecordPO.setEndTime(callRecordInfo.getStartTime().plusSeconds(callInRecordPO.getChatDuration()));
            callInRecordPO.setTransferStartTime(callRecordInfo.getTransferTime());
            callInRecordPO.setCsProcess(CsProcessEnum.CS);
            saveNotNull(callInRecordPO);

            if(detailList != null) {
                List<CallInDetailPO> callInDetailList = new ArrayList<>();
                detailList.forEach(item -> {
                    CallInDetailPO po = new CallInDetailPO();
                    po.setCallInRecordId(callInRecordPO.getCallInRecordId());
                    po.setDebugLog(item.getDebugLog());
                    po.setEmotion(item.getEmotion());
                    po.setEndOffset(item.getEndOffset());
                    po.setStartOffset(item.getStartOffset());
                    po.setTenantId(item.getTenantId());
                    po.setText(item.getText());
                    po.setType(item.getType());
                    callInDetailList.add(po);
                });
                if (!detailList.isEmpty()) {
                    callInDetailService.addCallInDetailList(callInDetailList);
                }
            }

            lastCallInRecordId = callInRecordPO.getCallInRecordId();
        }
    }

    @Override
    public void addTransferToCsRecordListFromAiCallIn(Long callInRecordId, String customerPhoneNumber, CsCallCategoryEnum call, CallInRecordPO recordPO, List<TransferCallVO> transferToCsList) {
        if(transferToCsList == null){
            return;
        }
	    Long tenantId = recordPO.getTenantId();
        Long distributorId = null;
	    if (tenantId != null) {
		    TenantPO tenant = tenantService.selectByKey(tenantId);
		    if (tenant != null) {
			    distributorId = tenant.getDistributorId();
		    }
	    }
	    List<CallInDetailPO> callInDetailList = callInDetailService.queryByCallInRecordId(tenantId, callInRecordId);
        Long lastCallInRecordId = callInRecordId;
        for(TransferCallVO vo : transferToCsList){

            CallInRecordPO callInRecordPO = new CallInRecordPO();
            callInRecordPO.setStaffGroupId(null);
            callInRecordPO.setStaffId(vo.getCurrentCsStaffId());
            callInRecordPO.setTenantId(tenantId);
            callInRecordPO.setDistributorId(distributorId);
            callInRecordPO.setDialogFlowId(recordPO.getDialogFlowId());
            callInRecordPO.setSeatType(StaffGroupTypeEnum.CS);
            callInRecordPO.setCustomerPhoneNumber(customerPhoneNumber);
            callInRecordPO.setManualMarked(false);
            callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setStartTime(recordPO.getStartTime());

            callInRecordPO.setIntentLevel(recordPO.getIntentLevel());
            callInRecordPO.setChatDuration(recordPO.getChatDuration());
            callInRecordPO.setChatRound(recordPO.getChatRound());
            callInRecordPO.setCustomerConcern(recordPO.getCustomerConcern());
            callInRecordPO.setAttributes(recordPO.getAttributes());
            callInRecordPO.setProperties(recordPO.getProperties());
            callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
            callInRecordPO.setHangupBy(CallJobHangupEnum.CS_HANGUP);
            callInRecordPO.setCreateUserId(vo.getCurrentUserId());
            callInRecordPO.setUpdateUserId(vo.getCurrentUserId());
            callInRecordPO.setCallCost(0L);
            callInRecordPO.setCallInReceptionId(recordPO.getCallInReceptionId());
            callInRecordPO.setRelevantId(callInRecordId);
            callInRecordPO.setCsCallCategory(call);
            callInRecordPO.setFromCsStaffId(vo.getFromCsStaffId());
            callInRecordPO.setFromCallInRecordId(lastCallInRecordId);
            callInRecordPO.setFromFirstCallInRecordId(callInRecordId);
            callInRecordPO.setChatDuration(recordPO.getChatDuration());
            callInRecordPO.setChatRound(recordPO.getChatRound());
            callInRecordPO.setAnalysisBasis(recordPO.getAnalysisBasis());
            callInRecordPO.setAttributes(recordPO.getAttributes());
            callInRecordPO.setCustomerAudioUrl(recordPO.getCustomerAudioUrl());
            callInRecordPO.setFullAudioUrl(recordPO.getFullAudioUrl());
            callInRecordPO.setCustomerConcern(recordPO.getCustomerConcern());
            callInRecordPO.setStartTime(recordPO.getStartTime());
            callInRecordPO.setEndTime(recordPO.getEndTime());
            callInRecordPO.setTransferStartTime(recordPO.getTransferStartTime());
            callInRecordPO.setCsProcess(CsProcessEnum.CS);
            saveNotNull(callInRecordPO);

            List<CallInDetailPO> detailList = new ArrayList<>();
            callInDetailList.forEach(item -> {
                CallInDetailPO po = new CallInDetailPO();
                po.setCallInRecordId(callInRecordPO.getCallInRecordId());
                po.setDebugLog(item.getDebugLog());
                po.setEmotion(item.getEmotion());
                po.setEndOffset(item.getEndOffset());
                po.setStartOffset(item.getStartOffset());
                po.setTenantId(item.getTenantId());
                po.setText(item.getText());
                po.setType(item.getType());
                detailList.add(po);
            });
            if(!detailList.isEmpty()) {
                callInDetailService.addCallInDetailList(detailList);
            }

            lastCallInRecordId = callInRecordPO.getCallInRecordId();
        }
    }

    @Override
    public void addTransferToCsRecordListFromCsCallOut(Long csRecord, String customerPhoneNumber, CsCallCategoryEnum call, CsRecordPO recordPO, List<TransferCallVO> transferToCsList) {
        if(transferToCsList == null){
            return;
        }
        Long lastCallInRecordId = csRecord;
        List<CsDetailPO> csDetailList = csDetailService.selectByCsCallId(csRecord);
	    Long tenantId = recordPO.getTenantId();
	    Long distributorId = null;
	    if (tenantId != null) {
		    TenantPO tenant = tenantService.selectByKey(tenantId);
		    if (tenant != null) {
			    distributorId = tenant.getDistributorId();
		    }
	    }
        for(TransferCallVO vo : transferToCsList){

            CallInRecordPO callInRecordPO = new CallInRecordPO();
            callInRecordPO.setStaffGroupId(null);
            callInRecordPO.setStaffId(vo.getCurrentCsStaffId());
	        callInRecordPO.setTenantId(tenantId);
	        callInRecordPO.setDistributorId(distributorId);
            callInRecordPO.setDialogFlowId(recordPO.getDialogFlowId());
            callInRecordPO.setSeatType(StaffGroupTypeEnum.CS);
            callInRecordPO.setCustomerPhoneNumber(customerPhoneNumber);
            callInRecordPO.setManualMarked(false);
            callInRecordPO.setCustomerPersonId(recordPO.getCustomerPersonId());
            callInRecordPO.setCustomerPersonName(recordPO.getCustomerPersonName());
            callInRecordPO.setResultStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setSeatStatus(CallInResultStatusEnum.ANSWERED);
            callInRecordPO.setStartTime(recordPO.getStartTime());

            callInRecordPO.setIntentLevel(recordPO.getIntentLevel());
            callInRecordPO.setChatDuration(recordPO.getChatDuration());
            callInRecordPO.setChatRound(recordPO.getChatRound());
            callInRecordPO.setAttributes(recordPO.getAttributes());
            callInRecordPO.setProperties(recordPO.getProperties());
            callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
            callInRecordPO.setHangupBy(CallJobHangupEnum.CS_HANGUP);
            callInRecordPO.setCreateUserId(vo.getCurrentUserId());
            callInRecordPO.setUpdateUserId(vo.getCurrentUserId());
            callInRecordPO.setCallCost(0L);
            callInRecordPO.setRelevantId(csRecord);
            callInRecordPO.setCsCallCategory(call);
            callInRecordPO.setFromCsStaffId(vo.getFromCsStaffId());
            callInRecordPO.setFromCallInRecordId(lastCallInRecordId);
            callInRecordPO.setFromFirstCallInRecordId(csRecord);
            callInRecordPO.setChatDuration(recordPO.getChatDuration());
            callInRecordPO.setChatRound(recordPO.getChatRound());
            callInRecordPO.setAttributes(recordPO.getAttributes());
            callInRecordPO.setFullAudioUrl(recordPO.getFullAudioUrl());
            callInRecordPO.setStartTime(recordPO.getStartTime());
            callInRecordPO.setEndTime(recordPO.getEndTime());
            callInRecordPO.setCsProcess(CsProcessEnum.CS);
            saveNotNull(callInRecordPO);

            if(csDetailList != null){
                csDetailList.forEach(item -> {
                    CallInDetailPO csDetailPO = new CallInDetailPO();
                    BeanUtils.copyProperties(item, csDetailPO);
                    csDetailPO.setCallInDetailId(null);
                    csDetailPO.setCallInRecordId(callInRecordPO.getCallInRecordId());
                    callInDetailService.saveNotNull(csDetailPO);
                });
            }

            lastCallInRecordId = callInRecordPO.getCallInRecordId();
        }
    }

    /**
     * 修改通话记录
     *
     * @param seatBO
     * @param callerNumber
     */
    @Override
    public void updateCallInRecord(SeatBO seatBO, String callerNumber, String recordPath, Long chatDuration, Boolean transfer, List<TransferCallVO> transferToCsList) {
        if(StaffGroupTypeEnum.CS.equals(seatBO.getSeatTypeEnum()) && seatBO.getStaffId() != null && seatBO.getStaffId()> 0){
            //设置呼入化后处理
            csStaffStatService.setCallInDealTime(seatBO.getTenantId(),seatBO.getStaffId());
        }

        Boolean dispatchSuccess = seatBO.isDispatchSuccess();
        // 预发的通话记录
        Long callInRecordId = seatBO.getCallInRecordId();
        CallInRecordPO callInRecordPO = selectByKey(callInRecordId);

        CallInResultStatusEnum resultStatus = (dispatchSuccess && chatDuration > 0) ? CallInResultStatusEnum.ANSWERED : CallInResultStatusEnum.NO_ANSWER;
        Integer chatRound = 0;
        Integer intentLevel = 3;
        Long intentLevelTagId = Objects.isNull(seatBO.getIntentLevelTagId()) ? 0 : seatBO.getIntentLevelTagId();

        callInRecordPO.setCsStaffGroupId(seatBO.getStaffGroupId());
        callInRecordPO.setTenantId(seatBO.getTenantId());
        callInRecordPO.setDialogFlowId(seatBO.getDialogFlowId());
        callInRecordPO.setPhoneNumberId(seatBO.getPhoneNumberPO().getPhoneNumberId());
        callInRecordPO.setSeatType(seatBO.getSeatTypeEnum());
        callInRecordPO.setIntentLevelTagId(intentLevelTagId);
        callInRecordPO.setManualMarked(false);
        callInRecordPO.setCsStaffId(seatBO.getStaffId());
        callInRecordPO.setResultStatus(resultStatus);
        callInRecordPO.setFullAudioUrl(recordPath);
        callInRecordPO.setEndTime(LocalDateTime.now());

        callInRecordPO.setIntentLevel(intentLevel);
        callInRecordPO.setChatDuration(chatDuration);
        callInRecordPO.setChatRound(chatRound);
        callInRecordPO.setCustomerConcern(new HashSet<>());
        callInRecordPO.setAttributes(new HashSet<>());
        callInRecordPO.setProperties(new HashMap<>());
        callInRecordPO.setTransferType(CallRecordTransferTypeEnum.NO_TRANSFER);
        callInRecordPO.setHangupBy(CallJobHangupEnum.INITIAL_HANGUP);
        // 计算通话费用
        callInRecordPO.setCallCost(calculateCallInCost(callInRecordPO));
        callInRecordPO.setRingDuration(seatBO.getRingDuration());
        if(seatBO.isRingGiveUp()){
            callInRecordPO.setResultStatus(CallInResultStatusEnum.RING_GIVE_UP);
        }
        CsSeatQueueDTO csSeatQueueDTO = new CsSeatQueueDTO();
        BeanUtils.copyProperties(seatBO, csSeatQueueDTO);
        CallInStatusBO callInStatusBO = csStaffUserService.getCallInStatus(callInRecordPO, csSeatQueueDTO, seatBO.getCallInRecordings());
        callInRecordPO.setResultStatus(callInStatusBO.getCallInStatus());
        callInRecordPO.setSeatStatus(callInStatusBO.getSeatStatus());
        callInRecordPO.setCsCallCategory(CsCallCategoryEnum.CALL_IN_CS);
        updateNotNull(callInRecordPO);

        try{
            //辅助&转译
            assistantRecordService.relevantRecord(seatBO.getCallInRecordId(), CallInstanceTypeEnum.CALL_IN_RECORD, seatBO.getTenantId(), seatBO.getStaffId()
                    , seatBO.getCallInRecordings());
            if(CollectionUtils.isNotEmpty(transferToCsList) && transfer) {
                addTransferToCsRecordListFromAiCallIn(callInRecordPO.getCallInRecordId(), callInRecordPO.getCustomerPhoneNumber(), CsCallCategoryEnum.CALL_IN_TRANSFER, callInRecordPO, transferToCsList);
            }
        }catch (Exception e){
            logger.error("初始化转接的数据", e);
        }

        // 添加流水信息
        addCallCostList(callInRecordPO);
        // 统计通话记录
        callInStatsService.doWhenAfterCallInHangup(callInRecordPO.getCallInRecordId());
        //统计接待统计
        logger.info("插入mongoDb--------开始");
        Query query = this.getCallInReceptionStatsQuery(callInRecordPO);
        Update update = this.getCallInReceptionUpdate(callInRecordPO);
        callStatsMongoService.updateMongoData(MongoCollectionNameCenter.CALL_IN_RECEPTION_STATS, query, update);
        if(StaffGroupTypeEnum.CS.equals(seatBO.getSeatTypeEnum()) && seatBO.getStaffId() != null && seatBO.getStaffId()> 0){
            CsStaffInfoPO csStaffInfoPO = csStaffInfoService.selectByKey(seatBO.getStaffId());
            if(csStaffInfoPO != null) {
                Long answeredReceptionCount = Objects.equals(callInRecordPO.getSeatStatus(),CallInResultStatusEnum.ANSWERED) ? 1L : 0L;
                Long effectiveReceptionDuration = Objects.equals(callInRecordPO.getSeatStatus(),CallInResultStatusEnum.ANSWERED) ? callInRecordPO.getChatDuration() : 0L;
                Integer xSecondCount = csStaffStatService.getCallInXSecondCount(callInRecordPO.getTenantId(),callInRecordPO.getRingDuration());
                csStaffStatService.staffStatCallIn(csStaffInfoPO.getUserId(), callInRecordPO.getTenantId(), csStaffInfoPO.getCsStaffId(),answeredReceptionCount,effectiveReceptionDuration,xSecondCount,callInRecordPO.getRingDuration());
            }
        }
        logger.info("插入mongoDb--------结束");
        //删除redis里的排队量
        redisOpsService.delete(RedisKeyCenter.ReceptionQueueCount(callInRecordPO.getCallInReceptionId()));
    }

    @Override
    public List<XSecondMonitorVO>  getCountForXSecond(Long tenantId, List<Long> csStaffIdList, Integer second) {
        return callInRecordPOMapper.getCountForXSecond(tenantId,csStaffIdList,second);
    }

    @Override
    public Query getCallInReceptionStatsQuery(CallInRecordPO callInRecordPO){
        LocalDateTime createTime = callInRecordPO.getCreateTime();
        if(createTime == null){
            createTime = LocalDateTime.now();
        }
        int year = createTime.getYear();
        int month = createTime.getMonthValue();
        int day = createTime.getDayOfMonth();
        int hour = createTime.getHour();

        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(callInRecordPO.getTenantId()));
        query.addCriteria(Criteria.where("userId").is(callInRecordPO.getCreateUserId()));
        query.addCriteria(Criteria.where("phoneNumberId").is(callInRecordPO.getPhoneNumberId()));
        query.addCriteria(Criteria.where("year").is(year));
        query.addCriteria(Criteria.where("month").is(month));
        query.addCriteria(Criteria.where("day").is(day));
        query.addCriteria(Criteria.where("hour").is(hour));
        query.addCriteria(Criteria.where("seatType").is(callInRecordPO.getSeatType().getCode()));
        query.addCriteria(Criteria.where("staffInfoId").is(callInRecordPO.getStaffId()));
        query.addCriteria(Criteria.where("staffGroupId").is(callInRecordPO.getCsStaffGroupId()));
        if(null != callInRecordPO.getCallInReceptionId()){
            query.addCriteria(Criteria.where("callInReceptionId").is(callInRecordPO.getCallInReceptionId()));
        }
        query.addCriteria(Criteria.where("localDateTime").is(LocalDateTime.of(year, month, day, hour, 0)));
        return query;
    }

    @Override
    public Update getCallInReceptionUpdate(CallInRecordPO callInRecordPO){
        Update update = new Update();
        update.inc("chatRound", callInRecordPO.getChatRound());
        update.inc("chatTime", callInRecordPO.getChatDuration());
        update.inc("taskTotalCompleted", 1);
        if (CallInResultStatusEnum.ANSWERED.equals(callInRecordPO.getResultStatus())) {
            update.inc("answeredCall", 1);
        }
        if (CallInResultStatusEnum.NO_ANSWER.equals(callInRecordPO.getResultStatus())
                ||CallInResultStatusEnum.IVR_GIVE_UP.equals(callInRecordPO.getResultStatus())
                ||CallInResultStatusEnum.QUEUE_GIVE_UP.equals(callInRecordPO.getResultStatus())
                ||CallInResultStatusEnum.QUEUE_OVER_TIME.equals(callInRecordPO.getResultStatus())
                ||CallInResultStatusEnum.NO_CS.equals(callInRecordPO.getResultStatus())
                ||CallInResultStatusEnum.RING_GIVE_UP.equals(callInRecordPO.getResultStatus())) {
            update.inc("callLoss", 1);
        }
        if (StaffGroupTypeEnum.AI.equals(callInRecordPO.getSeatType())) {
            update.inc("aiAnsweredCall", 1);
        }
        if (StaffGroupTypeEnum.CS.equals(callInRecordPO.getSeatType())
                ||StaffGroupTypeEnum.TEXT.equals(callInRecordPO.getSeatType())) {
            update.inc("cloudAnsweredCall", 1);
        }
        if (StaffGroupTypeEnum.TEL.equals(callInRecordPO.getSeatType())) {
            update.inc("telAnsweredCall", 1);
        }

        //排队量
        if(null != callInRecordPO.getCallInReceptionId()){
            String count = redisOpsService.get(RedisKeyCenter.ReceptionQueueCount(callInRecordPO.getCallInReceptionId()));
            if(StringUtils.isNotEmpty(count)){
                update.inc("queueTask", Long.parseLong(count));
            }
        }
        //排队时长
        update.inc("queueTime", null == callInRecordPO.getQueueDuration()?0:callInRecordPO.getQueueDuration());

        update.inc("resultStatus." + callInRecordPO.getResultStatus().name(), 1);

        Long callCost = callInRecordPO.getCallCost();
        if (Objects.isNull(callCost)) {
            callCost = 0L;
        }
        update.inc("callCost", callCost);
        return update;
    }

    @Override
    public CallInRecordDetailVO findByCallInRecordId(Long tenantId, Long callInRecordId) {
        CallInRecordPO callInRecord = callInRecordPOMapper.findByCallInRecordId(tenantId, callInRecordId);
        if (Objects.isNull(callInRecord)) {
            throw new ComException(ComErrorCode.NOT_EXIST, "联系历史不存在");
        }
        CallInRecordDetailVO result = new CallInRecordDetailVO();
        BeanUtils.copyProperties(callInRecord, result);
        result.setCalledPhoneNumber(callInRecord.getCustomerPhoneNumber());
        List<CallInDetailPO> callInDetailList = callInDetailService.queryByCallInRecordId(tenantId, callInRecordId);
        result.setCallInDetailList(callInDetailList);
        IntentLevelTagDetailPO intentLevelDetailByCode = intentLevelTagDetailService.getIntentLevelDetailByCode(result.getIntentLevelTagId(), result.getIntentLevel(), true);
        result.setIntentLevelName(intentLevelDetailByCode == null ? "" : intentLevelDetailByCode.getName());
        IntentLevelTagDetailPO intentLevelDetailByCode1 = intentLevelTagDetailService.getIntentLevelDetailByCode(result.getIntentLevelTagId(), result.getRealIntentLevel(), true);
        if (intentLevelDetailByCode1 != null) {
            result.setRealIntentLevelName(intentLevelDetailByCode1.getName());
        }
        return result;
    }

    /**
     * ai呼入成功加入的通话记录
     *
     * @param callInRecordPO
     * @param taskCallResultInfo
     */
    @Override
    public void insertCallInRecordByAiSuccess(CallInRecordPO callInRecordPO, TaskCallResultBO taskCallResultInfo) {
        Long chatDuration = taskCallResultInfo.getChatDuration();
        Integer chatRound = taskCallResultInfo.getChatRound();
        Integer intentLevel = taskCallResultInfo.getIntentLevel();
        // 意向登记默认为3
        if (Objects.isNull(intentLevel)) {
            intentLevel = 3;
        }
        if(callInRecordPO.getTenantId() != null) {
            TenantPO tenantPO = tenantService.getTenantByTenantId(callInRecordPO.getTenantId());
            callInRecordPO.setDistributorId(Objects.isNull(tenantPO) ? 0 : tenantPO.getDistributorId());
        }
        callInRecordPO.setManualMarked(false);
        callInRecordPO.setIntentLevel(intentLevel);
        callInRecordPO.setChatDuration(chatDuration);
        callInRecordPO.setChatRound(chatRound);
        callInRecordPO.setCustomerConcern(taskCallResultInfo.getCustomerConcern());
        callInRecordPO.setAttributes(taskCallResultInfo.getCustomerAttribute());
        callInRecordPO.setFullAudioUrl(taskCallResultInfo.getFullAudioUrl());
        callInRecordPO.setAnalysisBasis(taskCallResultInfo.getAnalysisBasis());
        callInRecordPO.setCustomerAudioUrl(taskCallResultInfo.getCustomerAudioUrl());
        callInRecordPO.setTransferType(taskCallResultInfo.getTransferType());
        callInRecordPO.setEndTime(LocalDateTime.now());
        callInRecordPO.setTrackStatus(TrackStatusEnum.CLUE);

        callInRecordPO.setCallCost(calculateCallInCost(callInRecordPO));
        saveCallInRecordWithCustomerInfo(callInRecordPO);
        // 添加流水信息
        addCallCostList(callInRecordPO);
        // 统计通话记录
        callInStatsService.doWhenAfterCallInHangup(callInRecordPO.getCallInRecordId());
    }

    @Override
    public void saveCallInRecordWithCustomerInfo(CallInRecordPO callInRecordPO) {
        saveNotNull(callInRecordPO);
    }

    @Override
    public List<CallRecordInfoVO> queryRecordInfoListByCustomerPersonId(Long tenantId, Long customerPersonId) {
        List<CallInRecordPO> poList = callInRecordPOMapper.queryByCustomerPersonId(tenantId, customerPersonId);

        List<DialogFlowInfoPO> dialogFlowInfoList = dialogFlowService.findDialogFlowByTenant(tenantId);
        Map<Long, String> dialogFlowMap = dialogFlowInfoList.stream().collect(Collectors.toMap(DialogFlowInfoPO::getId, DialogFlowInfoPO::getName));

        List<IntentLevelTagPO> intentLevelTagList = intentLevelTagService.getIntentLevelTagListByTenantId(tenantId);
        Map<Long, String> intentLevelNameMap = intentLevelTagList.stream().collect(Collectors.toMap(IntentLevelTagPO::getIntentLevelTagId, IntentLevelTagPO::getName));
        Set<Tuple2<Long, Integer>> intentLevelSet = poList.stream()
                .filter(item -> item.getIntentLevelTagId() != null)
                .filter(item -> Objects.nonNull(item.getIntentLevel()) || Objects.nonNull(item.getRealIntentLevel()))
                .map((item) -> Tuple.of(item.getIntentLevelTagId(), getShowIntentLevelCode(item)))
                .collect(Collectors.toSet());

        Map<Tuple2<Long, Integer>, IntentLevelTagDetailPO> intentLevelMap = intentLevelTagDetailService.getDetails(intentLevelSet, false);

        List<Long> csStaffInfoIdList = poList.stream().map(CallInRecordPO::getCsStaffId).filter(Objects::nonNull).distinct().filter(x -> x > 0).collect(Collectors.toList());
        Set<Long> csGroupIdSet = poList.stream().map(CallInRecordPO::getCsStaffGroupId).filter(Objects::nonNull).distinct().filter(x -> x > 0).collect(Collectors.toSet());
        Set<Long> staffIdList = poList.stream().map(CallInRecordPO::getStaffId).filter(Objects::nonNull).distinct().filter(x -> x > 0).collect(Collectors.toSet());
        final Map<Long, CsStaffGroupPO> groupPOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(csGroupIdSet)) {
            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(csGroupIdSet));
            groupPOList.forEach(item ->{
                groupPOMap.put(item.getCsStaffGroupId(), item);
            });
        }
        final Map<Long, String> csStaffInfoNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(csStaffInfoIdList)) {
            List<CsStaffInfoPO> csStaffInfoList = csStaffInfoService.selectListByKeyCollect(csStaffInfoIdList);
            csStaffInfoList.forEach(x -> {
                csStaffInfoNameMap.put(x.getCsStaffId(), x.getCsName());
            });
        }
        final Map<Long, String> staffInfoNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(staffIdList)) {
            List<CsStaffInfoPO> csStaffInfoList = csStaffInfoService.selectListByKeyCollect(staffIdList);
            csStaffInfoList.forEach(x -> {
                staffInfoNameMap.put(x.getCsStaffId(), x.getCsName());
            });
        }

        List<CallRecordInfoVO> list = poList.stream().map(callInRecord -> {
            CallRecordInfoVO result = new CallRecordInfoVO();
            BeanUtils.copyProperties(callInRecord, result);
            result.setCallRecordId(callInRecord.getCallInRecordId());
            result.setChatDuration(callInRecord.getChatDuration());
            result.setCustomerAudioUrl(callInRecord.getCustomerAudioUrl());
            result.setFullAudioUrl(callInRecord.getFullAudioUrl());
            result.setRead(fillCallInRecordReadStatus(callInRecord.getReadStatus(), SystemEnum.CRM));
            result.setRealIntentLevel(callInRecord.getRealIntentLevel());
            result.setRobotCallJobId(0L);
            result.setStartTime(callInRecord.getStartTime());
            result.setCallInCsName(csStaffInfoNameMap.getOrDefault(callInRecord.getCsStaffId(), ""));
            result.setCsStaffName(staffInfoNameMap.getOrDefault(callInRecord.getStaffId(), null));
            result.setCsStaffGroupName(groupPOMap.get(callInRecord.getCsStaffGroupId()) != null ? groupPOMap.get(callInRecord.getCsStaffGroupId()).getGroupName() : null);
            DialStatusEnum resultStatus = DialStatusEnum.ANSWERED;
            if (CallInResultStatusEnum.NO_ANSWER.equals(callInRecord)) {
                resultStatus = DialStatusEnum.NO_ANSWER;
            }
            result.setResultStatus(resultStatus);
            result.setRobotCallJobName("");
            result.setDialogFlowName(dialogFlowMap.get(callInRecord.getDialogFlowId()));
            result.setIntentLevelTagName(intentLevelNameMap.get(callInRecord.getIntentLevelTagId()));
            int type = StaffGroupTypeEnum.AI.equals(callInRecord.getSeatType()) ? 2 : 3;
            result.setType(type);
            IntentLevelTagDetailPO intentLevelTagDetailPO = intentLevelMap.get(Tuple.of(callInRecord.getIntentLevelTagId(), getShowIntentLevelCode(callInRecord)));
            if (Objects.nonNull(intentLevelTagDetailPO)) {
                result.setRealIntentLevelName(intentLevelTagDetailPO.getName());
            }
            result.setEmotion(callInRecord.getEmotion());
            if(callInRecord.getTransferPhoneNumber() != null){
                result.setCsType(2);
            }
            if(callInRecord.getCsStaffGroupId() != null && callInRecord.getTransferPhoneNumber() != null){
                CsStaffGroupPO groupPO = groupPOMap.get(callInRecord.getCsStaffGroupId());
                if(groupPO != null && StaffGroupTypeEnum.TEL.equals(groupPO.getGroupType())){
                    result.setCsType(3);
                    CsStaffGroupTransferPO transferPO= csStaffGroupTransferPOMapper.selectByGroupIdAndPhoneNumber(groupPO.getCsStaffGroupId(), callInRecord.getTransferPhoneNumber());
                    if(transferPO != null){
                        if(transferPO.getUserId() != null) {
                            CsStaffInfoPO csStaffInfoPO = csStaffInfoService.queryVoiceByUserId(groupPO.getTenantId(), transferPO.getUserId());
                            result.setCsStaffName(csStaffInfoPO != null ? csStaffInfoPO.getCsName() +"-" + callInRecord.getTransferPhoneNumber() : callInRecord.getTransferPhoneNumber());
                        }else{
                            result.setCsStaffName(callInRecord.getTransferPhoneNumber());
                        }
                    }
                }
            }
            return result;
        }).collect(Collectors.toList());

        return list;
    }

    @Override
    public void updateResultStatus2Answered(Long tenantId, Long callInRecordId, Integer hangupBy) {
        callInRecordPOMapper.updateResultStatus2Answered(tenantId, callInRecordId, hangupBy);
    }

    @Override
    public void updateCallRecordIntentLevel(Long tenantId, Long callInRecordId, Integer intentLevel,Long intentLevelTagId) {
        CallInRecordPO callInRecord = selectByKeyOrThrow(callInRecordId);
        if (!callInRecord.getTenantId().equals(tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权访问此通话记录", "访问callInRecordId:" + callInRecordId + "，所属当前用户tenantId:" + callInRecord.getTenantId() + ", 当前用户tenantId:" + tenantId);
        }
        // 更新数据库的意向
        callInRecordPOMapper.updateCallInRecordIntentLevel(tenantId, callInRecordId, intentLevel,intentLevelTagId);
    }

    @Override
    public PageResultObject<CallInRecordPO> queryAllByTenantId(Long tenantId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<CallInRecordPO> resultList = callInRecordPOMapper.queryAllByTenantId(tenantId);
        return PageResultObject.of(resultList);
    }

    private Integer getShowIntentLevelCode(CallInRecordPO callInRecord) {
        Integer intentLevel = Objects.nonNull(callInRecord.getRealIntentLevel()) ? callInRecord.getRealIntentLevel() : callInRecord.getIntentLevel();
        return intentLevel;
    }

    /**
     * 计算呼入通话费用
     * 在添加新记录的时候计算
     */
    private Long calculateCallInCost(CallInRecordPO callInRecordPO) {
        if (Objects.isNull(callInRecordPO.getChatDuration()) || callInRecordPO.getChatDuration() <= 0) {
            return 0L;
        }
        Long tenantId = callInRecordPO.getTenantId();
        Long phoneNumberId = callInRecordPO.getPhoneNumberId();
        return callInCostService.computeCallInCost(tenantId, phoneNumberId, callInRecordPO.getCustomerPhoneNumber(), callInRecordPO.getChatDuration());
    }

    // 添加线路流水
    private void addCallCostList(CallInRecordPO callInRecordPO) {
        // 如果是按月租收费，则不用添加流水信息
        if (!callInCostService.needComputeCallInChatTimeBill(callInRecordPO.getTenantId(), callInRecordPO.getPhoneNumberId())) {
            return;
        }

        PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKey(callInRecordPO.getPhoneNumberId());
        if (Objects.isNull(phoneNumberPO)) {
            logger.error("[LogHub_Warn] 添加呼入费用流水，线路信息异常");
        }
        CallInCostPO callInCostPO = new CallInCostPO();
        Boolean isLocal = phoneNumberService.isLocalWithCallerAndCalledPhoneNumber(callInRecordPO.getCustomerPhoneNumber(), phoneNumberPO);

        Try.run(() -> {
            callInCostPO.setCallInRecordId(callInRecordPO.getCallInRecordId());
            callInCostPO.setTenantId(callInRecordPO.getTenantId());
            callInCostPO.setPhoneNumberId(callInRecordPO.getPhoneNumberId());
            callInCostPO.setChatDuration(callInRecordPO.getChatDuration());
            callInCostPO.setCallCost(callInRecordPO.getCallCost());
            callInCostPO.setTotalCost(callInRecordPO.getCallCost());
            callInCostPO.setCalledPhoneNumber(callInRecordPO.getCustomerPhoneNumber());
            callInCostPO.setStartTime(callInRecordPO.getStartTime());
            callInCostPO.setCallCostType(isLocal ? CallInCostTypeEnum.LOCAL_CALL_BILL : CallInCostTypeEnum.OTHER_CALL_BILL);
            callInCostService.addCallInCost(callInCostPO);
        }).onFailure(e -> logger.error("添加呼入通话费用记录错误", e));

        Try.run(() -> {

            CostListPO costListPO = new CostListPO();

            costListPO.setCallRecordId(callInCostPO.getCallInRecordId());
            costListPO.setCallCostId(callInCostPO.getCallInCostId());
            costListPO.setCount(callInCostPO.getChatDuration());
            costListPO.setTenantId(callInCostPO.getTenantId());
            costListPO.setPhoneNumberId(callInCostPO.getPhoneNumberId());
            costListPO.setFare(callInCostPO.getTotalCost());
            costListPO.setCreateUserId(callInRecordPO.getCreateUserId());
            costListPO.setUpdateUserId(callInRecordPO.getUpdateUserId());
            costListPO.setCostType(isLocal ? CostTypeEnum.CALL_IN_LOCAL_CALL : CostTypeEnum.CALL_IN_OTHER_CALL);
            costListPO.setCostListType(CostListTypeEnum.CALL_IN);
            costListService.saveNotNull(costListPO);
        }).onFailure(e -> logger.error("添加呼入费用流水错误", e));

        Try.run(() -> {
            TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectOneByTenantIdAndPhoneNumberId(callInRecordPO.getTenantId(), callInRecordPO.getPhoneNumberId());
            tenantPhoneNumberService.reduceCallCost(tenantPhoneNumberPO.getTenantPhoneNumberId(), callInRecordPO.getCallCost());
        }).onFailure(e -> logger.error("[LogHub_Warn]呼入接待扣除用户电话费用失败 call_in_record_id={}", callInRecordPO.getCallInRecordId()));

    }

    @Override
    public PageResultObject<CallInRecordListVO> queryOpeByCondition(CallInRecordQueryVO condition) {
        Integer readParameter = 1;
        condition.setReadParameter(readParameter);
        if (Objects.nonNull(condition.getEndTime())) {
            condition.setEndTime(condition.getEndTime().plusDays(1));
        }
        //boss系统查询通话记录
        if (getFilterDistributorIdList(condition)) {
            return PageResultObject.of(Collections.emptyList());
        }
        PageHelper.startPage(condition.getPageNum(), condition.getPageSize());
        List<CallInRecordListVO> resultList = callInRecordPOMapper.queryOpeByCondition(condition);

        if (resultList != null && !resultList.isEmpty()) {
            Set<Long> tenantIdList = resultList.stream().map(CallInRecordPO::getTenantId).collect(Collectors.toSet());
            Set<Long> customerPersonIds = resultList.stream().map(CallInRecordListVO::getCustomerPersonId).collect(Collectors.toSet());
            List<TenantPO> tenantPOList = tenantService.getTenantListByTenantIds(new ArrayList<>(tenantIdList));
            Map<Long, String> tenantMap = tenantPOList.stream().collect(Collectors.toMap(TenantPO::getTenantId, TenantPO::getCompanyName));
            resultList.forEach(callInRecordListVO -> {
                callInRecordListVO.setCompanyName(tenantMap.get(callInRecordListVO.getTenantId()));
            });
        }

        // 如果是ai接待，则需要设置话术信息
        if (condition.getSeatType() == StaffGroupTypeEnum.AI && CollectionUtils.isNotEmpty(resultList)) {
            List<Long> dialogFlowIds = resultList.stream().map(CallInRecordListVO::getDialogFlowId).distinct().collect(Collectors.toList());
            List<DialogFlowListVO> dialogFlowInfoList = dialogFlowService.selectByDialogFlowIds(dialogFlowIds);
            Map<Long, String> dialogFlowMap = dialogFlowInfoList.stream().collect(Collectors.toMap(DialogFlowListVO::getDialogFlowId, DialogFlowListVO::getDialogFlowName));
            resultList.forEach(callInRecord -> {
                callInRecord.setDialogFlowName(dialogFlowMap.get(callInRecord.getDialogFlowId()));
            });
        }

        // 如果是人工接待，需要设置人工客服信息
        if (condition.getSeatType() == StaffGroupTypeEnum.CS) {
            List<Long> staffIdList = resultList.stream().map(CallInRecordListVO::getCsStaffId).distinct().filter(staffId -> staffId > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(staffIdList)) {
                List<CsStaffInfoPO> staffInfoList = csStaffInfoService.selectListByKeyCollect(staffIdList);
                Map<Long, CsStaffInfoPO> csStaffMap = staffInfoList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, x -> x));
                resultList.forEach(callInRecord -> {
                    callInRecord.setCsStaffInfo(csStaffMap.get(callInRecord.getCsStaffId()));
                });
            }
        }

        List<Long> phoneNumberIds = resultList.stream().map(CallInRecordListVO::getPhoneNumberId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(phoneNumberIds)) {
            List<PhoneNumberPO> phoneNumberPOList = phoneNumberService.getPhoneNumberListByIdList(phoneNumberIds);
            Map<Long, PhoneNumberPO> phoneNumberMap = phoneNumberPOList.stream().collect(Collectors.toMap(PhoneNumberPO::getPhoneNumberId, d -> d));
            resultList.forEach(callInRecord -> {
                TenantPhoneNumberWithPhoneInfoPO phoneNumberInfo = new TenantPhoneNumberWithPhoneInfoPO();
                PhoneNumberPO phoneNumberPO = phoneNumberMap.get(callInRecord.getPhoneNumberId());
                if (Objects.nonNull(phoneNumberPO)) {
                    BeanUtils.copyProperties(phoneNumberPO, phoneNumberInfo);
                }
                callInRecord.setPhoneNumberInfo(phoneNumberInfo);
            });
        }

        // 设置是否已读的状态
        resultList.forEach(this::fillCallInRecordReadStatus);

        return PageResultObject.of(resultList);
    }

    private boolean getFilterDistributorIdList(CallInRecordQueryVO condition) {
        Long distributorId = condition.getDistributorId();
        if (Objects.nonNull(distributorId)) {
            List<Long> distributorIdList;
            DistributorPO distributorPO = distributorService.selectByKey(distributorId);
            if (distributorPO.getParentId() == 0L) {
                //一级代理商
                distributorIdList = distributorService.selectByParentId(distributorId);
                distributorIdList.add(distributorId);
            } else {
	            distributorIdList = Collections.singletonList(distributorId);
            }
            if (CollectionUtils.isEmpty(distributorIdList)) {
                return true;
            } else {
                condition.setFilterDistributorIdList(distributorIdList);
            }
        }
        return false;
    }

    @Override
    public void updateCreateUserId(Long tenantId, Long userId, Collection<Long> customerPersonIds) {
        callInRecordPOMapper.updateCreateUser(tenantId, userId, customerPersonIds);
    }

    @Override
    public PageResultObject<CsStaffCallRecordVO> callInHistory(CsStaffCallRecordQueryVO csStaffCallRecordQueryVO) {
        CsStaffInfoPO csStaffInfoPO = csStaffInfoService.queryByUserIdAndType(csStaffCallRecordQueryVO.getTenantId(), csStaffCallRecordQueryVO.getUserId(), StaffTypeEnum.VOICE);
        if(csStaffInfoPO == null){
            return PageResultObject.of(null);
        }
        csStaffCallRecordQueryVO.setCsStaffId(csStaffInfoPO.getCsStaffId());
        CallInRecordPO callInRecordPO = callInRecordPOMapper.getLastCallInRecord(csStaffCallRecordQueryVO.getTenantId());
        PageHelper.startPage(csStaffCallRecordQueryVO.getPageNum(), csStaffCallRecordQueryVO.getPageSize());
        List<CsStaffCallRecordVO> list = new ArrayList<>();
        if(callInRecordPO == null) {
            list = callInRecordPOMapper.queryCallInHistoryOnlyCs(csStaffCallRecordQueryVO);
        }else {
            list = callInRecordPOMapper.queryCallInHistory(csStaffCallRecordQueryVO);
        }

        for(CsStaffCallRecordVO vo : list){
            if(vo.getResult() == 0){
                vo.setResultStatus(DialStatusEnum.ANSWERED);
            } else {
                vo.setResultStatus(DialStatusEnum.NO_ANSWER);
            }
        }
        return PageResultObject.of(list);
    }

    @Override
    public List<CallInRecordPO> selectByCustomerPersonId(Long tenantId, Long customerPersonId) {
        return callInRecordPOMapper.selectByCustomerPersonId(tenantId, customerPersonId);
    }

    /* left join cs_staff_group csg ON cir.staff_group_id = csg.cs_staff_group_id
       left join cs_staff_info csi ON cir.staff_id = csi.cs_staff_id
       left join call_in_reception cire ON cir.call_in_reception_id = cire.call_in_reception_id
       left join ivr_navigation_info inf ON cire.ivr_navigation_info_id = inf.ivr_navigation_info_id*/
    @Override
    public void packageCallInRecord(List<? extends CallInRecordListVO> list){
        if(CollectionUtils.isNotEmpty(list)){
            Set<Long> groupSet = list.stream().map(CallInRecordListVO::getStaffGroupId).collect(Collectors.toSet());
            Set<Long> staffSet = list.stream().map(CallInRecordListVO::getStaffId).collect(Collectors.toSet());
            Set<Long> receptionSet = list.stream().map(CallInRecordListVO::getCallInReceptionId).collect(Collectors.toSet());

            Map<Long, String> groupMap = new HashMap<>();
            Map<Long, String> staffMap = new HashMap<>();
            Map<Long, String> receptionMap = new HashMap<>();
            Map<Long, String> receptionIvrMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(groupSet)) {
                List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(groupSet));
                if(CollectionUtils.isNotEmpty(groupPOList)){
                    groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
                }
            }

            if(CollectionUtils.isNotEmpty(staffSet)) {
                List<CsStaffInfoPO> staffPOList = csStaffInfoService.selectByIdList(new ArrayList<>(staffSet));
                if(CollectionUtils.isNotEmpty(staffPOList)){
                    staffMap = staffPOList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, CsStaffInfoPO::getCsName));
                }
            }

            if(CollectionUtils.isNotEmpty(receptionSet)) {
                List<CallInReceptionPO> receptionPOList = callInReceptionPOMapper.selectByCallInreceotionIdList(new ArrayList<>(receptionSet));
                if(CollectionUtils.isNotEmpty(receptionPOList)){
                    receptionMap = receptionPOList.stream().filter( x -> StringUtils.isNotEmpty(x.getRemark())).collect(Collectors.toMap(CallInReceptionPO::getCallInReceptionId, CallInReceptionPO::getRemark));

                }
            }

            for(CallInRecordListVO item : list){
                item.setCsStaffGroupName(groupMap.get(item.getStaffGroupId()));
                item.setCsStaffName(staffMap.get(item.getStaffId()));
                item.setReceptionName(receptionMap.get(item.getCallInReceptionId()));
            }
        }
    }

    @Override
    public int countByTransferType(Long tenantId,CallRecordTransferTypeEnum transferType, LocalDate startDate, LocalDate endDate) {
        if(startDate == null || endDate == null){
            startDate = LocalDate.now();
            endDate = LocalDate.now().plusDays(1);
        }
        return callInRecordPOMapper.countByTransferType(tenantId,transferType.getCode(),startDate,endDate);
    }

	@Override
	public void initCallInRecordDistributorId() {
		List<Long> tenantIds = callInRecordPOMapper.selectDistinctTenantId();
		Map<? extends Serializable, Long> idMap = tenantService.selectMapByKeyCollect(tenantIds, TenantPO::getDistributorId);
		idMap.forEach((key, value) -> {
			if (value != 0) {
				callInRecordPOMapper.updateDistributorIdByTenantId((Long) key, value);
			}
		});
	}
}
