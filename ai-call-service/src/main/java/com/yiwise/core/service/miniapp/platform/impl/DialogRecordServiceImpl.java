package com.yiwise.core.service.miniapp.platform.impl;

import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.helper.objectstorage.AddOssPrefixSerializer;
import com.yiwise.core.manager.miniapp.DialogRecordManager;
import com.yiwise.core.manager.miniapp.TextAudioManager;
import com.yiwise.core.model.dialogflow.dto.RecordCountDTO;
import com.yiwise.core.model.dialogflow.dto.RecordUploadDTO;
import com.yiwise.core.model.dialogflow.entity.*;
import com.yiwise.core.model.dialogflow.tree.HierarchyInfo;
import com.yiwise.core.model.dialogflow.vo.DialogAudioCopyResultVO;
import com.yiwise.core.model.dialogflow.vo.DialogFlowAudioCopyLogVO;
import com.yiwise.core.model.enums.OperationLogOperationTypeEnum;
import com.yiwise.core.model.enums.YesOrNoEnum;
import com.yiwise.core.model.enums.dialogflow.*;
import com.yiwise.core.model.miniapp.dto.RecordContentDTO;
import com.yiwise.core.model.miniapp.dto.RecordMetaDTO;
import com.yiwise.core.model.miniapp.dto.RecordNodeDTO;
import com.yiwise.core.model.miniapp.vo.*;
import com.yiwise.core.model.vo.dialogrecord.RobotKnowledgeModifyVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.dialogflow.*;
import com.yiwise.core.service.dialogflow.impl.DialogFlowUploadServiceImpl;
import com.yiwise.core.thread.DynamicDataSourceApplicationExecutorHolder;
import com.yiwise.core.validate.miniapp.dialogrecord.DialogRecordQueryValidate;
import com.yiwise.core.validate.miniapp.dialogrecord.DialogRecordResetValidate;
import com.yiwise.core.validate.miniapp.dialogrecord.DialogRecordUploadValidate;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.WAV_TO_PCM_HEAD_LEN;

/**
 * <AUTHOR>
 * @date 2018-11-22 10:36
 */
@Service
public class DialogRecordServiceImpl implements DialogRecordService {

    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private DialogFlowConfigService dialogFlowConfigService;
    @Resource
    private DialogFlowStepService dialogFlowStepService;
    @Resource
    private DialogFlowRobotKnowledgeService dialogFlowRobotKnowledgeService;
    @Resource
    private DialogRecordManager dialogRecordManager;
    @Resource
    private DialogRecordQueryValidate dialogRecordQueryValidate;
    @Resource
    private DialogRecordResetValidate dialogRecordResetValidate;
    @Resource
    private DialogRecordUploadValidate dialogRecordUploadValidate;
    @Resource
    private DialogOperateLogGenerateService dialogOperateLogGenerateService;
    @Resource
    private ObjectStorageHelper objectStorageHelper;
    @Resource
    private TextAudioManager textAudioManager;
    @Resource
    private DialogAudioRecordLogService dialogAudioRecordLogService;

    private static final Pattern DOT_FILE_PATTERN = Pattern.compile(".+\\..+");

    private final static Logger logger = LoggerFactory.getLogger(DialogRecordServiceImpl.class);

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public PageResultObject<MiniappRecordDialogInfoVO> getDialogFlowInfoPageList(String searchWords, Long distributorId, Long tenantId, Long userId, Integer pageNum, Integer pageSize, Boolean nlp) {
        if (BooleanUtils.isTrue(nlp)) {
            // db中查出所有符合条件的话术, 过滤掉不含人工录音的话术, 手动分页
            List<DialogFlowInfoPO> dialogFlows = dialogFlowService.findRecorderDialogFlowPageList(searchWords, distributorId, tenantId, userId, true);
            List<MiniappRecordDialogInfoVO> ret = buildMiniappRecordDialogInfoPageList(dialogFlows, true);
            return PageResultObject.of(ret, pageNum, pageSize);
        } else {
            PageResultObject<MiniappRecordDialogInfoVO> pageInfo = PageResultObject.of(new ArrayList<>());

            // 获取录音师所拥有的话术(分页)
            PageResultObject<DialogFlowInfoPO> dialogFlowPOs = dialogFlowService.findRecorderDialogFlowPageList(searchWords, distributorId, tenantId, userId, pageNum, pageSize);
            pageInfo.setNumber(dialogFlowPOs.getNumber());
            pageInfo.setPages(dialogFlowPOs.getPages());
            pageInfo.setPageSize(dialogFlowPOs.getPageSize());
            pageInfo.setTotalElements(dialogFlowPOs.getTotalElements());
            if (dialogFlowPOs.getTotalElements() == 0) {
                return pageInfo;
            }

            List<DialogFlowInfoPO> list = dialogFlowPOs.getContent();
            if (list == null || list.isEmpty()) {
                return pageInfo;
            }
            pageInfo.setContent(buildMiniappRecordDialogInfoPageList(list, false));
            return pageInfo;
        }
    }

    private List<MiniappRecordDialogInfoVO> buildMiniappRecordDialogInfoPageList(List<DialogFlowInfoPO> list, boolean nlp){
        List<MiniappRecordDialogInfoVO> dialogFlowRecordInfoVOS = new ArrayList<>();

        if(list == null){
            return dialogFlowRecordInfoVOS;
        }

        Map<Long, RecordCountDTO> map;
        if (BooleanUtils.isTrue(nlp)) {
            map = dialogRecordManager.statNlpDialogFlowListRecordCountMap(list);
        } else {
            List<Long> dialogFlowIdList = list.stream().map(DialogFlowInfoPO::getId).collect(Collectors.toList());
            List<RecordCountDTO> recordCountList = dialogRecordManager.calculateDialogFlowRecordStats(dialogFlowIdList);
            map = MyCollectionUtils.listToMap(recordCountList, RecordCountDTO::getDialogFlowId);
        }

        // 统计每个话术的信息
        list.forEach(dialogFlow -> {
            MiniappRecordDialogInfoVO dialogFlowRecordInfo = new MiniappRecordDialogInfoVO();
            // 基本信息
            dialogFlowRecordInfo.setId(dialogFlow.getId());
            dialogFlowRecordInfo.setTitle(dialogFlow.getName());
            dialogFlowRecordInfo.setStatusDesc(dialogFlow.getStatus().getDesc());
            dialogFlowRecordInfo.setLastModifiedTime(dialogFlow.getUpdateTime());
            // 录音数量统计
            RecordCountDTO recordCountDTO = map.get(dialogFlow.getId());
            dialogFlowRecordInfo.setTotalRecordCount(recordCountDTO.getTotalRecordCount());
            dialogFlowRecordInfo.setNoneRecordCount(recordCountDTO.getNoneRecordCount());
            dialogFlowRecordInfo.setRecordPercent(recordCountDTO.countRecordPercent());
            dialogFlowRecordInfo.setDistinctRecordCount(recordCountDTO.getDistinctRecordCount());
            dialogFlowRecordInfo.setDistinctWordCount(recordCountDTO.getDistinctWordCount());
            dialogFlowRecordInfo.setDistinctHasRecordCount(recordCountDTO.getDistinctHasRecordCount());
            dialogFlowRecordInfo.setDistinctHasWordCount(recordCountDTO.getDistinctHasWordCount());

            dialogFlowRecordInfoVOS.add(dialogFlowRecordInfo);
        });
        return dialogFlowRecordInfoVOS;
    }

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public MiniappRecordDialogDetailVO getDialogFlowDetail(Long tenantId, Long dialogFlowId) {
        return handleDialogFlowDetail(tenantId, dialogFlowId, null);
    }

    private MiniappRecordDialogDetailVO handleDialogFlowDetail(Long tenantId, Long dialogFlowId, String keyword) {
        MiniappRecordDialogDetailVO dialogFlowRecordDetail = new MiniappRecordDialogDetailVO();

        List<RecordNodeDTO> recordNodeDTOS = dialogRecordManager.getDialogFlowRecordNodeList(dialogFlowId);
        if (keyword != null) {
            recordNodeDTOS = recordNodeDTOS.stream()
                    .filter(r -> r.getOriginTextAudio() != null
                            && r.getOriginTextAudio().getText() != null
                            && r.getOriginTextAudio().getText().contains(keyword)).collect(Collectors.toList());
        }
        List<MiniappRecordDialogItemVO> recordDialogItemVOS = dialogRecordManager.unionDialogDetail(recordNodeDTOS);

        // 获取主动话术信息
        dialogFlowRecordDetail.setDialogFlowSteps(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.DIALOG_FLOW_STEP.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取挂机话术详情信息
        dialogFlowRecordDetail.setHangUps(recordDialogItemVOS.stream()
                .filter(x -> (RecordTypeEnum.isHangUp(x.getType())))
                .collect(Collectors.toList()));

        // 获取知识库话术信息
        dialogFlowRecordDetail.setRobotKnowledges(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.ROBOT_KNOWLEDGE.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取多轮对话话术信息
        dialogFlowRecordDetail.setKnowledgeSteps(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.KNOWLEDGE_STEP.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取所有未录音信息
        dialogFlowRecordDetail.setNoneRecords(dialogRecordManager.unionNoneRecordNodeList(recordNodeDTOS));

        // 是否可以重置全部录音
        dialogFlowRecordDetail.setResetAll(dialogRecordManager.isDialogFlowResetAll(dialogFlowId));

        return dialogFlowRecordDetail;
    }

    @Override
    public MiniappRecordDialogDetailVO getDialogFlowDetailByKeyword(Long tenantId, Long dialogFlowId, String keyword) {
        return handleDialogFlowDetail(tenantId, dialogFlowId, keyword);
    }

    public MiniappRecordDialogDetailVO getDialogFlowRecordDetail(Long tenantId, Long dialogFlowId) {
        MiniappRecordDialogDetailVO dialogFlowRecordDetail = new MiniappRecordDialogDetailVO();

        List<RecordNodeDTO> recordNodeDTOS = dialogRecordManager.getDialogFlowRecordNodeList(dialogFlowId);
        List<MiniappRecordDialogItemVO> recordDialogItemVOS = dialogRecordManager.unionDialogDetail(recordNodeDTOS);

        // 获取主动话术信息
        dialogFlowRecordDetail.setDialogFlowSteps(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.DIALOG_FLOW_STEP.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取挂机话术详情信息
        dialogFlowRecordDetail.setHangUps(recordDialogItemVOS.stream()
                .filter(x -> (RecordTypeEnum.isHangUp(x.getType())))
                .collect(Collectors.toList()));

        // 获取知识库话术信息
        dialogFlowRecordDetail.setRobotKnowledges(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.ROBOT_KNOWLEDGE.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取多轮对话话术信息
        dialogFlowRecordDetail.setKnowledgeSteps(recordDialogItemVOS.stream()
                .filter(x -> RecordTypeEnum.KNOWLEDGE_STEP.equals(x.getType()))
                .collect(Collectors.toList()));

        // 获取所有未录音信息
        dialogFlowRecordDetail.setNoneRecords(dialogRecordManager.unionNoneRecordNodeList(recordNodeDTOS));

        // 是否可以重置全部录音
        dialogFlowRecordDetail.setResetAll(dialogRecordManager.isDialogFlowResetAll(dialogFlowId));

        return dialogFlowRecordDetail;
    }

    @Override
    public List<MiniappRecordDialogItemDetailVO> getDialogStepRecords(String stepId) {
        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(stepId);
        if (dialogFlowStep == null) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "请求的流程已不存在");
        }

        List<RecordNodeDTO> recordNodeDTOS = dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep);

        return dialogRecordManager.unionRecordItemList(recordNodeDTOS, dialogFlowStep);
    }

    @Override
    public List<MiniappRecordDialogItemDetailVO> getHangUpRecords(Long dialogFlowId, RecordTypeEnum recordType) {
        // 聚合数据信息
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
        List<RecordNodeDTO> recordNodeDTOS = dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig, recordType);

        return dialogRecordManager.unionRecordItemList(recordNodeDTOS);
    }

    @Override
    public List<MiniappRecordDialogItemDetailVO> getRobotKnowledgeRecords(String robotKnowledgeId) {
        RobotKnowledgePO robotKnowledge = dialogFlowRobotKnowledgeService.getRobotKnowledge(robotKnowledgeId);
        if (robotKnowledge == null) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "请求的知识库已不存在");
        }

        List<RecordNodeDTO> recordNodeDTOS = dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge);

        return dialogRecordManager.unionRecordItemList(recordNodeDTOS);
    }

    @Override
    public MiniappRecordDialogRecordDetailVO getDialogStepRecordDetail(MiniappRecordNodeRequestVO request) {
        // 校验查询参数
        dialogRecordQueryValidate.validateDialogStepQuery(request);

        // 获取话术流程
        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(request.getParentId());

        // 查询出符合条件的录音节点
        List<RecordNodeDTO> queryRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep), request);
        if (queryRecordNodeDTOS.size() == 0) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "没有查询出录音详情");
        } else if (queryRecordNodeDTOS.size() > 1) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "查询出多个录音详情");
        }

        return dialogRecordManager.unionRecordDetail(queryRecordNodeDTOS.get(0));
    }

    @Override
    public MiniappRecordDialogRecordDetailVO getHangUpRecordDetail(MiniappRecordNodeRequestVO request) {
        // 校验查询参数
        dialogRecordQueryValidate.validateDialogConfigQuery(request);

        // 查询出符合条件的录音节点
        Long dialogFlowId = Long.valueOf(request.getParentId());
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
        List<RecordNodeDTO> queryRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig), request);
        if (queryRecordNodeDTOS.size() == 0) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "没有查询出录音详情");
        } else if (queryRecordNodeDTOS.size() > 1) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "查询出多个录音详情");
        }

        return dialogRecordManager.unionRecordDetail(queryRecordNodeDTOS.get(0));
    }

    @Override
    public MiniappRecordDialogRecordDetailVO getRobotKnowledgeRecordDetail(MiniappRecordNodeRequestVO request) {
        // 校验查询参数
        dialogRecordQueryValidate.validateRobotKnowledgeQuery(request);

        // 所在知识库是否存在
        RobotKnowledgePO robotKnowledge = dialogFlowRobotKnowledgeService.getRobotKnowledge(request.getParentId());

        // 查询出符合条件的录音节点
        List<RecordNodeDTO> queryRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge), request);
        if (queryRecordNodeDTOS.size() == 0) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "没有查询出录音详情");
        } else if (queryRecordNodeDTOS.size() > 1) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "查询出多个录音详情");
        }

        return dialogRecordManager.unionRecordDetail(queryRecordNodeDTOS.get(0));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetDialogFlowRecord(Long dialogFlowId, Long userId) {
        // 校验信息
        dialogRecordResetValidate.validateDialogFlowStatus(dialogFlowId);

        // 获取需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.getDialogFlowRecordNodeList(dialogFlowId);

        // 重置话术录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        dialogOperateLogGenerateService.simpleLog(dialogFlowId, userId,"重置全部录音", OperationLogOperationTypeEnum.DIALOG_AUDIO, DialogLogResourceTypeEnum.AUDIO, DialogFlowAuditActionEnum.UPDATE);

        logger.info("清除话术所有录音，userId={} dialogFlowId={}", userId, dialogFlowId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetDialogStepRecords(Long userId, List<MiniappRecordNodeRequestVO> requestVOS) {
        // 校验请求信息
        dialogRecordResetValidate.validateDialogStepReset(requestVOS);

        // 获取话术流程
        String parentId = dialogRecordResetValidate.getAndValidateParentId(requestVOS);
        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(parentId);

        // 获取需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep), requestVOS);

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        // 记录日志
        resetRecordNodeDTOS.forEach(recordNode -> {
            if (RecordTypeEnum.DIALOG_FLOW_STEP.equals(dialogFlowStep.getType().toRecordTypeEnum())) {
                logger.info("清除主动话术录音，{}", recordNode.toString());
            } else {
                logger.info("清除多轮对话录音，{}", recordNode.toString());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetHangUpRecords(Long userId, List<MiniappRecordNodeRequestVO> requestVOS) {
        // 验证请求信息
        dialogRecordResetValidate.validateDialogConfigReset(requestVOS);

        // 获取话术信息
        String dialogFlowIdStr = dialogRecordResetValidate.getAndValidateParentId(requestVOS);
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(Long.valueOf(dialogFlowIdStr));

        // 获取需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig), requestVOS);

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        resetRecordNodeDTOS.forEach(recordNode -> logger.info("清除挂机话术录音，{}", recordNode.toString()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetRobotKnowledgeRecords(Long userId, List<MiniappRecordNodeRequestVO> requestVOS) {
        // 校验输入的信息
        dialogRecordResetValidate.validateRobotKnowledgeReset(requestVOS);

        // 获取知识库信息
        String robotKnowledgeId = dialogRecordResetValidate.getAndValidateParentId(requestVOS);
        RobotKnowledgePO robotKnowledge = dialogFlowRobotKnowledgeService.getRobotKnowledge(robotKnowledgeId);

        // 获取需要重置的录音节点
        List<RecordNodeDTO> resetRecordDetailVOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge), requestVOS);

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordDetailVOS, userId);

        resetRecordDetailVOS.forEach(recordNode -> logger.info("清除知识库问答录音，{}", recordNode.toString()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetDialogStepRecord(Long userId, MiniappRecordNodeRequestVO request) {
        // 验证信息
        dialogRecordResetValidate.validateDialogStepReset(request);

        // 获取流程信息
        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(request.getParentId());

        // 查询需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep), request);
        if (resetRecordNodeDTOS.size() != 1) {
            return;
        }

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        if (RecordTypeEnum.DIALOG_FLOW_STEP.equals(dialogFlowStep.getType().toRecordTypeEnum())) {
            logger.info("清除主动话术录音，{}", resetRecordNodeDTOS.get(0).toString());
        } else {
            logger.info("清除多轮对话录音，{}", resetRecordNodeDTOS.get(0).toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetHangUpRecord(Long userId, MiniappRecordNodeRequestVO request) {
        // 校验信息
        dialogRecordResetValidate.validateDialogConfigReset(request);

        // 话术Id
        Long dialogFlowId = Long.valueOf(request.getParentId());

        // 获取话术配置
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);

        // 查询需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig), request);
        if (resetRecordNodeDTOS.size() != 1) {
            return;
        }

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        logger.info("删除挂机话术录音，{}", resetRecordNodeDTOS.get(0).toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetRobotKnowledgeRecord(Long userId, MiniappRecordNodeRequestVO request) {
        // 校验信息
        dialogRecordResetValidate.validateRobotKnowledgeReset(request);

        // 获取知识库
        RobotKnowledgePO robotKnowledge = dialogFlowRobotKnowledgeService.getRobotKnowledge(request.getParentId());

        // 查询需要重置的录音节点
        List<RecordNodeDTO> resetRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge), request);
        if (resetRecordNodeDTOS.size() != 1) {
            return;
        }

        // 重置录音
        dialogRecordManager.resetRecordNodeList(resetRecordNodeDTOS, userId);

        logger.info("清除知识库问答录音，{}", resetRecordNodeDTOS.get(0).toString());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniappRecordUploadVO uploadDialogStepRecord(MultipartFile file, Long userId, MiniappRecordNodeRequestVO request) {
        try {
            DialogRecordService.skipValidOnUploadAudio.set(true);
            // 校验信息
            dialogRecordUploadValidate.validateDialogStepUpload(request);

            // 返回的数据
            MiniappRecordUploadVO recordUploadVO = new MiniappRecordUploadVO();

            // 获取录音流程
            DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(request.getParentId());

            // 获取待上传录音列表
            List<RecordNodeDTO> uploadRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                    dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep), request);
            if (uploadRecordNodeDTOS.size() != 1) {
                return recordUploadVO;
            }

            // 待更新的录音文本
            RecordNodeDTO uploadRecordNode = uploadRecordNodeDTOS.get(0);

            // 上传录音到阿里云
            RecordUploadDTO recordUploadDTO = dialogRecordManager.uploadRecord(dialogFlowStep.getDialogFlowId(), request.getParentId(), file, request.getType());
            uploadRecordNode.getRecordContent().setAudioUrl(recordUploadDTO.getDbRecordUrl());
            dialogRecordManager.uploadDialogStepRecord(dialogFlowStep, uploadRecordNode, recordUploadDTO, userId);

            // 返回录音地址
            recordUploadVO.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(recordUploadDTO.getAliOssFullUrl()));
            logger.info("上传主动话术或多轮对话录音，{}", uploadRecordNode.toString());

            autoUploadSameContentText(uploadRecordNode, file, dialogFlowStep.getDialogFlowId(), userId);

            dialogOperateLogGenerateService.addUpdateRecordLogIfAllDone(dialogFlowStep.getDialogFlowId(), userId);

            // 添加日志
            dialogAudioRecordLogService.create(dialogFlowStep.getDialogFlowId(), userId, uploadRecordNode);

            return recordUploadVO;
        } finally {
            DialogRecordService.skipValidOnUploadAudio.set(false);
        }
    }

    private void autoUploadSameContentText(RecordNodeDTO currentRecordNode, MultipartFile file, Long dialogFlowId, Long userId) {
        if (Objects.nonNull(currentRecordNode)
                && Objects.nonNull(currentRecordNode.getRecordContent())
                && org.apache.commons.lang3.StringUtils.isNotBlank(currentRecordNode.getRecordContent().getAudioText())) {
            autoUploadSameContentText(currentRecordNode.getRecordContent().getAudioText(), file, dialogFlowId, userId, false);
        }
    }


    private void autoUploadSameContentText(String currentRecordText, MultipartFile file, Long dialogFlowId, Long userId, boolean fromHumanIntervention) {
        // 查询这个话术下面所有的录音文本，
        AtomicBoolean uploaded = new AtomicBoolean(false);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(currentRecordText)) {
            // 处理多轮
            autoUploadStepRecord(currentRecordText, file, userId, dialogFlowId, uploaded);

            // 处理问答知识
            autoUploadKnowledgeRecord(currentRecordText, file, userId, dialogFlowId, uploaded);

            // 处理对话配置
            autoUploadHangupRecord(currentRecordText, file, dialogFlowId, userId, uploaded, fromHumanIntervention);
        }

        if (uploaded.get()) {
            DialogFlowInfoPO dialogFlowInfo = dialogFlowService.getDialogFlow(dialogFlowId);
            dialogFlowInfo.setLastRecordingTime(LocalDateTime.now());
            dialogFlowService.updateNotNull(dialogFlowInfo);
        }
    }

    private void autoUploadHangupRecord(String currentRecordText, MultipartFile file, Long dialogFlowId, Long userId, AtomicBoolean uploaded, boolean fromHumanIntervention) {
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);

        if (Objects.isNull(dialogFlowConfig)) {
            return;
        }
        List<RecordNodeDTO> configRecordNodeList = new ArrayList<>();
        configRecordNodeList.addAll(dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig));

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(configRecordNodeList)) {
            List<RecordNodeDTO> sameTextContentNodeList = filterSameRecordNodeList(currentRecordText, configRecordNodeList);
            sameTextContentNodeList.forEach(sameRecord -> {
                //保存更新的录音地址
                RecordUploadDTO recordUploadDTO = dialogRecordManager.uploadRecord(dialogFlowId, null, file, sameRecord.getRecordType());
                sameRecord.getRecordContent().setAudioUrl(recordUploadDTO.getDbRecordUrl());
                TextAudioContentPO audio = dialogRecordManager.getHangUpTextAudio(dialogFlowConfig, sameRecord);
                if (textAudioManager.isBlankAudioText(audio)) {
                    return;
                }
                // 更新数据库的录音地址
                RecordMetaDTO recordMeta = sameRecord.getRecordMeta();
                textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), recordUploadDTO.getDbRecordUrl());
                logger.info("话术配置，查找到该录音信息[{}]和待录音文本一致，自动使用该音频上传录音:[{}]", sameRecord, recordUploadDTO);
            });

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sameTextContentNodeList)) {
                dialogFlowConfigService.setDialogFlowConfiguration(dialogFlowConfig, userId);
                uploaded.set(true);
            }
        }

        DialogFlowHumanInterventionPO humanIntervention = dialogFlowConfig.getDialogFlowHumanIntervention();
        if (!fromHumanIntervention
                && Objects.nonNull(humanIntervention)
                && org.apache.commons.lang3.StringUtils.isNotBlank(humanIntervention.getReminderText())
                && humanIntervention.getReminderText().equals(currentRecordText)) {
            doUploadHumanInterventionRecord(dialogFlowConfig, file, userId, dialogFlowId);
            uploaded.set(true);
        }
    }

    private void autoUploadKnowledgeRecord(String currentRecordText, MultipartFile file, Long userId, Long dialogFlowId, AtomicBoolean uploaded) {

        Map<String, List<RecordNodeDTO>> knowledgeRecordNodeMap = new HashMap<>();
        List<RobotKnowledgePO> robotKnowledgeList = dialogFlowRobotKnowledgeService.findRobotKnowledge(dialogFlowId);
        Map<String, RobotKnowledgePO> knowledgeMap = MyCollectionUtils.listToMap(robotKnowledgeList, RobotKnowledgePO::getId);

        // 获取所有知识库
        robotKnowledgeList.forEach(robotKnowledge -> {
            knowledgeRecordNodeMap.put(robotKnowledge.getId(), dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge));
        });
        // 处理问答知识
        knowledgeRecordNodeMap.forEach((knowledgeId, recordList) -> {
            RobotKnowledgePO knowledge = knowledgeMap.get(knowledgeId);
            if (Objects.isNull(knowledge)) {
                return;
            }
            List<RecordNodeDTO> sameTextContentNodeList = filterSameRecordNodeList(currentRecordText, recordList);
            if (CollectionUtils.isEmpty(sameTextContentNodeList)) {
                return;
            }
            sameTextContentNodeList.forEach(sameRecord -> {
                // 获取录音内容
                RecordMetaDTO recordMeta = sameRecord.getRecordMeta();
                // 获取录音节点
                List<? extends TextAudioContentPO> answers = knowledge.getRobotKnowledgeAnswers();
                int hierarchyIndex = HierarchyInfo.toHierarchy(recordMeta.getHierarchy()).getIndex();
                TextAudioContentPO audio = answers.get(hierarchyIndex);
                if (textAudioManager.isBlankAudioText(audio)) {
                    return;
                }
                RecordUploadDTO uploadRecordDTO = dialogRecordManager.uploadRecord(knowledge.getDialogFlowId(), knowledgeId, file, RecordTypeEnum.ROBOT_KNOWLEDGE);
                sameRecord.getRecordContent().setAudioUrl(uploadRecordDTO.getDbRecordUrl());
                // 更新知识库录音信息
                textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), uploadRecordDTO.getDbRecordUrl());
                logger.info("问答知识：[{}] 查找到该录音信息[{}]和待录音文本一致，自动使用该音频上传录音:[{}]", knowledge.getTitle(), sameRecord, uploadRecordDTO);
            });
            // 更新录音流程信息
            uploaded.set(true);
            RobotKnowledgeModifyVO robotKnowledgeModifyVO = new RobotKnowledgeModifyVO();
            BeanUtils.copyProperties(knowledge, robotKnowledgeModifyVO);
            dialogFlowRobotKnowledgeService.modifyRobotKnowledge(robotKnowledgeModifyVO, userId);
        });
    }


    private Map<String, SameTextAudioWrapper> getDialogStepText2AudioMap(Long dialogFlowId, DialogVoiceTypeEnum dialogVoiceType) {
        Map<String, SameTextAudioWrapper> result = new HashMap<>();

        List<DialogFlowStepPO> dialogFlowSteps = dialogFlowStepService.findDialogFlowSteps(dialogFlowId);
        dialogFlowSteps.forEach(dialogFlowStep -> {
            List<RecordNodeDTO> allRecordNodeList = dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep, dialogVoiceType);
            allRecordNodeList.forEach(recordNode -> {
                RecordContentDTO recordContent = recordNode.getRecordContent();
                if (Objects.nonNull(recordContent)
                        && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioText())
                        && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioUrl())) {
                    String audioText = recordContent.getOriginText().trim();
                    SameTextAudioWrapper wrapper = result.get(audioText);
                    if (Objects.isNull(wrapper)) {
                        wrapper = new SameTextAudioWrapper(recordContent.getAudioUrl());
                        result.put(audioText, wrapper);
                    }
                    if (Objects.nonNull(recordNode.getOriginTextAudio())
                            && org.apache.commons.lang3.StringUtils.isNotBlank(recordNode.getOriginTextAudio().getLabel())) {
                        wrapper.providerTextAudioLabelSet.add(recordNode.getOriginTextAudio().getLabel());
                    }
                }
            });
        });

        return result;
    }

    private Map<String, SameTextAudioWrapper> getDialogKnowledgeText2AudioMap(Long dialogFlowId, DialogVoiceTypeEnum dialogVoiceType) {

        Map<String, SameTextAudioWrapper> result = new HashMap<>();
        List<RobotKnowledgePO> knowledgeList = dialogFlowRobotKnowledgeService.findRobotKnowledge(dialogFlowId);
        knowledgeList.forEach(knowledge -> {
            List<RecordNodeDTO> allRecordNodeList = dialogRecordManager.getRobotKnowledgeRecordNodeList(knowledge, dialogVoiceType);
            allRecordNodeList.forEach(recordNode -> {
                RecordContentDTO recordContent = recordNode.getRecordContent();
                if (Objects.nonNull(recordContent)
                        && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioText())
                        && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioUrl())) {
                    String audioText = recordContent.getOriginText().trim();
                    SameTextAudioWrapper wrapper = result.get(audioText);
                    if (wrapper == null) {
                        wrapper = new SameTextAudioWrapper(recordContent.getAudioUrl());
                        result.put(audioText, wrapper);
                    }
                    if (Objects.nonNull(recordNode.getOriginTextAudio())
                            && org.apache.commons.lang3.StringUtils.isNotBlank(recordNode.getOriginTextAudio().getLabel())) {
                        wrapper.providerTextAudioLabelSet.add(recordNode.getOriginTextAudio().getLabel());
                    }
                }
            });
        });

        return result;
    }

    private Map<String, SameTextAudioWrapper> getDialogConfigText2AudioMap(Long dialogFlowId, DialogVoiceTypeEnum dialogVoiceType) {
        Map<String, SameTextAudioWrapper> result = new HashMap<>();
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);

        if (Objects.isNull(dialogFlowConfig)) {
            return result;
        }

        List<RecordNodeDTO> configRecordNodeList = dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig);
        configRecordNodeList.forEach(recordNode -> {
            RecordContentDTO recordContent = recordNode.getRecordContent();
            if (Objects.nonNull(recordContent)
                    && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioText())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(recordContent.getAudioUrl())) {
                String audioText = recordContent.getOriginText().trim();
                SameTextAudioWrapper wrapper = result.get(audioText);
                if (Objects.isNull(wrapper)) {
                    wrapper = new SameTextAudioWrapper(recordContent.getAudioUrl());
                    result.put(audioText, wrapper);
                }
                if (Objects.nonNull(recordNode.getOriginTextAudio())
                        && org.apache.commons.lang3.StringUtils.isNotBlank(recordNode.getOriginTextAudio().getLabel())) {
                    wrapper.providerTextAudioLabelSet.add(recordNode.getOriginTextAudio().getLabel());
                }
            }
        });
        return result;
    }


    private void autoUploadStepRecord(String currentRecordText, MultipartFile file, Long userId, Long dialogFlowId, AtomicBoolean uploaded) {
        Map<String, List<RecordNodeDTO>> stepRecordNodeMap = new HashMap<>();
        List<DialogFlowStepPO> dialogFlowSteps = dialogFlowStepService.findDialogFlowSteps(dialogFlowId);
        Map<String, DialogFlowStepPO> stepMap = MyCollectionUtils.listToMap(dialogFlowSteps, DialogFlowStepPO::getId);
        // 获取所有 DialogFlowStep
        dialogFlowSteps.forEach(dialogFlowStep -> {
            stepRecordNodeMap.put(dialogFlowStep.getId(), dialogRecordManager.getDialogFlowStepRecordNodeList(dialogFlowStep));
        });
        // 处理多轮
        stepRecordNodeMap.forEach((stepId, recordList) -> {
            DialogFlowStepPO step = stepMap.get(stepId);
            if (Objects.isNull(step)) {
                return;
            }
            List<RecordNodeDTO> sameTextContentNodeList = filterSameRecordNodeList(currentRecordText, recordList);
            if (CollectionUtils.isEmpty(sameTextContentNodeList)) {
                return;
            }
            Map<String, TextAudioContentPO> stepTreeMap = dialogFlowStepService.getDialogStepTreeInfo(step);
            sameTextContentNodeList.forEach(sameRecord -> {
                // 获取录音内容
                RecordMetaDTO recordMeta = sameRecord.getRecordMeta();
                TextAudioContentPO audio = stepTreeMap.get(recordMeta.getHierarchy());
                if (textAudioManager.isBlankAudioText(audio)) {
                    return;
                }
                RecordUploadDTO recordUploadDTO = dialogRecordManager.uploadRecord(step.getDialogFlowId(), step.getId(), file, step.getType().toRecordTypeEnum());
                sameRecord.getRecordContent().setAudioUrl(recordUploadDTO.getDbRecordUrl());
                // 更新话术节点中的录音地址
                textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), recordUploadDTO.getDbRecordUrl());
                logger.info("流程：[{}] 查找到该录音信息[{}]和待录音文本一致，自动使用该音频上传录音:[{}]", step.getName(), sameRecord, recordUploadDTO);
            });
            // 更新录音流程信息
            uploaded.set(true);
            dialogFlowStepService.modifyDialogFlowStep(step, userId);
        });
    }

    private List<RecordNodeDTO> filterSameRecordNodeList(String currentRecordText, List<RecordNodeDTO> recordList) {
        if (org.apache.commons.lang3.StringUtils.isBlank(currentRecordText)) {
            return Collections.emptyList();
        }
        final String currentTextContent = textAudioManager.removePartPrefixText(currentRecordText);
        return recordList.stream()
                .filter(node -> {
                    if (Objects.nonNull(node.getRecordContent())
                            && Objects.nonNull(node.getRecordContent().getAudioText())) {
                        String textContent = textAudioManager.removePartPrefixText(node.getRecordContent().getAudioText());
                        return textContent.equals(currentTextContent);
                    }
                    return false;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniappRecordUploadVO uploadHangUpRecord(MultipartFile file, Long userId, MiniappRecordNodeRequestVO request) {

        try {
            DialogRecordService.skipValidOnUploadAudio.set(true);
            // 验证信息
            dialogRecordUploadValidate.validateDialogConfigUpload(request);

            // 返回的结果
            MiniappRecordUploadVO recordUploadVO = new MiniappRecordUploadVO();

            // 获取话术信息类
            Long dialogFlowId = Long.valueOf(request.getParentId());
            DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);

            // 获取待上传录音列表
            List<RecordNodeDTO> uploadRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                    dialogRecordManager.getHangUpRecordNodeList(dialogFlowConfig), request);
            if (uploadRecordNodeDTOS.size() != 1) {
                return recordUploadVO;
            }

            // 上传录音
            RecordUploadDTO recordUploadDTO = dialogRecordManager.uploadRecord(dialogFlowId, null, file, request.getType());

            // 保存更新的录音地址
            RecordNodeDTO uploadRecordNode = uploadRecordNodeDTOS.get(0);
            uploadRecordNode.getRecordContent().setAudioUrl(recordUploadDTO.getDbRecordUrl());
            dialogRecordManager.uploadHangUpRecord(dialogFlowConfig, uploadRecordNode, recordUploadDTO, userId);

            // 返回录音地址
            recordUploadVO.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(recordUploadDTO.getAliOssFullUrl()));

            logger.info("上传挂机话术录音，{}", uploadRecordNode.toString());

            autoUploadSameContentText(uploadRecordNode, file, dialogFlowId, userId);

            dialogOperateLogGenerateService.addUpdateRecordLogIfAllDone(dialogFlowId, userId);

            // 添加日志
            dialogAudioRecordLogService.create(dialogFlowId, userId, uploadRecordNode);
            return recordUploadVO;
        } finally {
            DialogRecordService.skipValidOnUploadAudio.set(false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniappRecordUploadVO uploadRobotKnowledgeRecord(MultipartFile file, Long userId, MiniappRecordNodeRequestVO request) {
        try {
            DialogRecordService.skipValidOnUploadAudio.set(true);
            // 校验信息
            dialogRecordUploadValidate.validateRobotKnowledgeUpload(request);

            // 返回的信息
            MiniappRecordUploadVO recordUploadVO = new MiniappRecordUploadVO();

            // 获取知识库
            RobotKnowledgePO robotKnowledge = dialogFlowRobotKnowledgeService.getRobotKnowledge(request.getParentId());

            // 获取待上传录音列表
            List<RecordNodeDTO> uploadRecordNodeDTOS = dialogRecordManager.queryRecordNodeDTOList(
                    dialogRecordManager.getRobotKnowledgeRecordNodeList(robotKnowledge), request);
            if (uploadRecordNodeDTOS.size() != 1) {
                return recordUploadVO;
            }

            // 上传录音
            RecordUploadDTO uploadRecordDTO = dialogRecordManager.uploadRecord(robotKnowledge.getDialogFlowId(), request.getParentId(), file, RecordTypeEnum.ROBOT_KNOWLEDGE);

            // 保存更新的录音地址
            RecordNodeDTO uploadRecordNode = uploadRecordNodeDTOS.get(0);
            uploadRecordNode.getRecordContent().setAudioUrl(uploadRecordDTO.getDbRecordUrl());
            dialogRecordManager.uploadRobotKnowledgeRecord(robotKnowledge, uploadRecordNode, uploadRecordDTO, userId);

            // 返回录音地址
            recordUploadVO.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(uploadRecordDTO.getAliOssFullUrl()));

            logger.info("上传知识库问答录音，{}", uploadRecordDTO.toString());

            autoUploadSameContentText(uploadRecordNode, file, robotKnowledge.getDialogFlowId(), userId);
            dialogOperateLogGenerateService.addUpdateRecordLogIfAllDone(robotKnowledge.getDialogFlowId(), userId);

            // 添加日志
            dialogAudioRecordLogService.create(robotKnowledge.getDialogFlowId(), userId, uploadRecordNode);
            return recordUploadVO;
        } finally {
            DialogRecordService.skipValidOnUploadAudio.set(false);
        }
    }

    @Override
    public DialogFlowHumanInterventionVO getHumanInterventionRecords(Long dialogFlowId) {
        DialogFlowConfigurationPO dialogFlowConfigurationPO = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
        return getHumanInterventionRecords(dialogFlowConfigurationPO);
    }

    @Override
    public DialogFlowHumanInterventionVO getHumanInterventionRecords(DialogFlowConfigurationPO dialogFlowConfiguration) {
        if(dialogFlowConfiguration != null && dialogFlowConfiguration.getDialogFlowHumanIntervention() != null){
            if(YesOrNoEnum.YES.equals(dialogFlowConfiguration.getDialogFlowHumanIntervention().getReminder())){
                DialogFlowHumanInterventionVO vo = new DialogFlowHumanInterventionVO();
                vo.setReminderText(dialogFlowConfiguration.getDialogFlowHumanIntervention().getReminderText());
                if(!StringUtils.isEmpty(dialogFlowConfiguration.getDialogFlowHumanIntervention().getReminderUrl())) {
                    vo.setReminderUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(dialogFlowConfiguration.getDialogFlowHumanIntervention().getReminderUrl()));
                }
                return vo;
            }
        }
        return null;
    }

    @Override
    public void resetHumanInterventionRecords(Long userId, Long dialogFlowId) {
        DialogRecordManager.skipAutoUploadSameTextAudio.set(true);
        try {
            DialogFlowConfigurationPO dialogFlowConfigurationPO = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
            if(dialogFlowConfigurationPO != null && dialogFlowConfigurationPO.getDialogFlowHumanIntervention() != null){
                DialogFlowHumanInterventionPO po = dialogFlowConfigurationPO.getDialogFlowHumanIntervention();
                po.setReminderUrl(null);
                dialogFlowConfigurationPO.setDialogFlowHumanIntervention(po);
                dialogFlowConfigService.setDialogFlowConfiguration(dialogFlowConfigurationPO, userId);
                dialogFlowService.changeDialogFlowStatus(dialogFlowId, DialogFlowStatusEnum.DRAFT);
                DialogFlowInfoPO infoPO = new DialogFlowInfoPO();
                infoPO.setId(dialogFlowId);
                infoPO.setLastRecordingTime(LocalDateTime.now());
                dialogFlowService.updateNotNull(infoPO);
            }
        } finally {
            DialogRecordManager.skipAutoUploadSameTextAudio.remove();
        }
    }

    @Override
    public MiniappRecordUploadVO uploadHumanInterventionRecord(MultipartFile file, Long userId, Long dialogFlowId) {
        try {
            DialogRecordService.skipValidOnUploadAudio.set(true);
            DialogFlowConfigurationPO config = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
            MiniappRecordUploadVO result = doUploadHumanInterventionRecord(config, file, userId, dialogFlowId);
            if (Objects.nonNull(config.getDialogFlowHumanIntervention())
                    && org.apache.commons.lang3.StringUtils.isNotBlank(config.getDialogFlowHumanIntervention().getReminderText())) {
                autoUploadSameContentText(config.getDialogFlowHumanIntervention().getReminderText(), file, dialogFlowId, userId, true);
            }
            return result;
        } finally {
            DialogRecordService.skipValidOnUploadAudio.set(false);
        }
    }

    @Override
    public void scanAndAutoUploadSameTextAudio(DialogFlowStepPO dialogFlowStep) {
        if (Objects.isNull(dialogFlowStep)) {
            return;
        }
        Long dialogFlowId = dialogFlowStep.getDialogFlowId();
        if (!checkVoiceIsManMade(dialogFlowId)) {
            return;
        }
        Map<String, SameTextAudioWrapper> text2audioMap = queryDialogAllText2AudioUrlMap(dialogFlowId, DialogVoiceTypeEnum.MAN_MADE);
        if (MapUtils.isEmpty(text2audioMap)) {
            return;
        }
        doScanAndAutoUploadSameTextAudio(dialogFlowStep, text2audioMap, false);
    }

    private List<DialogFlowAudioCopyLogVO> doScanAndAutoUploadSameTextAudio(DialogFlowStepPO dialogFlowStep,
                                                                            Map<String, SameTextAudioWrapper> text2audioMap,
                                                                            boolean needCopyAudioFile) {
        List<DialogFlowAudioCopyLogVO> result = new LinkedList<>();
        dialogFlowStepService.traverseDialogFlowNodeTree(dialogFlowStep.getRootDialogFlowNode(), (node) -> {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(node.getTextAudioContentList())) {
                node.getTextAudioContentList().forEach(textAudioContent -> {
                    List<DialogFlowAudioCopyLogVO> tmpList = replaceAudioUrl(dialogFlowStep.getDialogFlowId(), text2audioMap, textAudioContent, needCopyAudioFile);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpList)) {
                        tmpList.forEach(log -> {
                            log.setType(dialogFlowStep.getType().getDesc());
                            log.setNodeName(node.getName());
                            log.setLabel(node.getLabel());
                            log.setStepName(dialogFlowStep.getName());
                        });
                        result.addAll(tmpList);
                    }
                });
            }
        });
        return result;
    }

    private boolean checkVoiceIsManMade(Long dialogFlowId) {
        return checkVoiceIsManMade(dialogFlowService.getDialogFlow(dialogFlowId));
    }

    private boolean checkVoiceIsManMade(DialogFlowInfoPO dialogFlowInfo) {
        return Objects.nonNull(dialogFlowInfo) && DialogVoiceTypeEnum.MAN_MADE.equals(dialogFlowInfo.getVoiceType());
    }

    @Override
    public void scanAndAutoUploadSameTextAudio(RobotKnowledgePO knowledge) {
        if (Objects.isNull(knowledge)) {
            return;
        }
        Long dialogFlowId = knowledge.getDialogFlowId();
        if (!checkVoiceIsManMade(dialogFlowId)) {
            return;
        }
        Map<String, SameTextAudioWrapper> text2audioMap = queryDialogAllText2AudioUrlMap(dialogFlowId, DialogVoiceTypeEnum.MAN_MADE);
        if (MapUtils.isEmpty(text2audioMap)) {
            return;
        }

        doScanAndAutoUploadSameTextAudio(knowledge, text2audioMap, false);
    }

    private List<DialogFlowAudioCopyLogVO> doScanAndAutoUploadSameTextAudio(RobotKnowledgePO knowledge,
                                                                            Map<String, SameTextAudioWrapper> text2audioMap,
                                                                            boolean needCopyAudioFile) {
        if (Objects.isNull(knowledge) || CollectionUtils.isEmpty(knowledge.getRobotKnowledgeAnswers())) {
            return Collections.emptyList();
        }
        List<DialogFlowAudioCopyLogVO> result = new LinkedList<>();
        knowledge.getRobotKnowledgeAnswers()
                .forEach(textAudioContent -> {
                    // 更新录音内容中的录音地址
                    List<DialogFlowAudioCopyLogVO> tmpLogList = replaceAudioUrl(knowledge.getDialogFlowId(), text2audioMap, textAudioContent, needCopyAudioFile);
                    tmpLogList.forEach(log -> {
                        log.setLabel(knowledge.getLabel());
                        log.setType("Knowledge");
                        log.setKnowledgeName(knowledge.getTitle());
                    });
                    result.addAll(tmpLogList);
                });
        return result;
    }

    @Override
    public void scanAndAutoUploadSameTextAudio(DialogFlowConfigurationPO configuration) {
        if (Objects.isNull(configuration)) {
            return;
        }
        Long dialogFlowId = configuration.getDialogFlowId();
        if (!checkVoiceIsManMade(dialogFlowId)) {
            return;
        }
        Map<String, SameTextAudioWrapper> text2audioMap = queryDialogAllText2AudioUrlMap(dialogFlowId, DialogVoiceTypeEnum.MAN_MADE);
        if (MapUtils.isEmpty(text2audioMap)) {
            return;
        }
        doScanAndAutoUploadSameTextAudio(configuration, text2audioMap, false);
    }

    private List<DialogFlowAudioCopyLogVO> doScanAndAutoUploadSameTextAudio(DialogFlowConfigurationPO configuration,
                                                                            Map<String, SameTextAudioWrapper> text2audioMap,
                                                                            boolean needCopyAudioFile) {
        if (Objects.isNull(configuration)) {
            return Collections.emptyList();
        }
        List<DialogFlowAudioCopyLogVO> result = new LinkedList<>();
        configuration.getAllRuleTextAudioMap().forEach((desc, list) -> {
            list.forEach(textAudioContent -> {
                List<DialogFlowAudioCopyLogVO> tmpList = replaceAudioUrl(configuration.getDialogFlowId(), text2audioMap, textAudioContent, needCopyAudioFile);
                tmpList.forEach(log -> {
                    log.setType("Config");
                    log.setConfigDesc(desc);
                });
                result.addAll(tmpList);
            });
        });
        return result;
    }

    @Override
    public DialogAudioCopyResultVO copyAllAudioForDialogFlow(Long fromDialogFlowId, Long toDialogFlowId, Long userId) {
        DialogFlowInfoPO fromDialog = dialogFlowService.getDialogFlow(fromDialogFlowId);
        DialogFlowInfoPO toDialog = dialogFlowService.getDialogFlow(toDialogFlowId);
        if (Objects.isNull(fromDialog)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "复制录音, 源话术不存在");
        }
        if (Objects.isNull(toDialog)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "复制录音, 目标话术不存在");
        }
        if (!checkVoiceIsManMade(fromDialog) || !checkVoiceIsManMade(toDialog)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "源话术/目标话术录音类型不是真人录音");
        }
        if (fromDialogFlowId.equals(toDialogFlowId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "不能自己同步给自己");
        }
        // 查询源话术所有的录音信息
        Map<String, SameTextAudioWrapper> fromText2audioMap = queryDialogAllText2AudioUrlMap(fromDialogFlowId, DialogVoiceTypeEnum.MAN_MADE);

        DialogAudioCopyResultVO result = new DialogAudioCopyResultVO();
        if (CollectionUtils.isEmpty(fromText2audioMap)) {
            result.setSuccess(false);
            result.setMessage("原话术不存在录音");
            return result;
        }

        List<DialogFlowAudioCopyLogVO> logList = new LinkedList<>();

        // 多线程处理加快速度
        List<CompletableFuture<List<DialogFlowAudioCopyLogVO>>> mainStepCopyLogFutureList = new LinkedList<>();
        List<CompletableFuture<List<DialogFlowAudioCopyLogVO>>> knowledgeStepCopyLogFutureList = new LinkedList<>();
        List<CompletableFuture<List<DialogFlowAudioCopyLogVO>>> knowledgeCopyLogFutureList = new LinkedList<>();
        List<CompletableFuture<List<DialogFlowAudioCopyLogVO>>> configLogFutureList = new LinkedList<>();

        // 处理话术流程
        List<DialogFlowStepPO> toStepList = dialogFlowStepService.findDialogFlowSteps(toDialogFlowId);
        List<RobotKnowledgePO> toKnowledgeList = dialogFlowRobotKnowledgeService.findRobotKnowledge(toDialogFlowId);
        DialogFlowConfigurationPO config = dialogFlowConfigService.getDialogFlowConfiguration(toDialogFlowId);

        long start = System.currentTimeMillis();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(toStepList)) {
            toStepList.forEach(step -> {
                CompletableFuture<List<DialogFlowAudioCopyLogVO>> stepFuture = new CompletableFuture<>();
                if (DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW.equals(step.getType())) {
                    mainStepCopyLogFutureList.add(stepFuture);
                } else {
                    knowledgeStepCopyLogFutureList.add(stepFuture);
                }
                DynamicDataSourceApplicationExecutorHolder.execute("流程录音复制", () -> {
                    long startTime = System.currentTimeMillis();
                    List<DialogFlowAudioCopyLogVO> tmpLogList = Collections.emptyList();
                    try {
                        tmpLogList = doScanAndAutoUploadSameTextAudio(step, fromText2audioMap, true);
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpLogList)) {
                            // 有替换成功, 当前流程需要持久化
                            dialogFlowStepService.modifyDialogFlowStep(step, userId);
                        }
                    } finally {
                        long endTime = System.currentTimeMillis();
                        logger.info("执行录音复制耗时:话术流程:{}", endTime - startTime);
                        stepFuture.complete(tmpLogList);
                    }
                });
            });
        }

        // 处理问答知识
        // 问答知识先单线程跑
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(toKnowledgeList)) {
            int perThreadKnowledgeNum = 10;
            AtomicInteger index = new AtomicInteger();
            for (int i = 0; i < toKnowledgeList.size();) {
                int toIndex = Math.min(i + perThreadKnowledgeNum, toKnowledgeList.size());
                List<RobotKnowledgePO> tmpList = new ArrayList<>(toKnowledgeList.subList(i, toIndex));
                i += perThreadKnowledgeNum;
                CompletableFuture<List<DialogFlowAudioCopyLogVO>> future = new CompletableFuture<>();
                knowledgeCopyLogFutureList.add(future);
                DynamicDataSourceApplicationExecutorHolder.execute("问答知识录音复制-" + index.getAndIncrement(), () -> {
                    long startTime = System.currentTimeMillis();
                    List<DialogFlowAudioCopyLogVO> knowledgeLogList = new LinkedList<>();
                    try {
                        tmpList.forEach(knowledge -> {
                            List<DialogFlowAudioCopyLogVO> tmpLogList = doScanAndAutoUploadSameTextAudio(knowledge, fromText2audioMap, true);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpLogList)) {
                                // 有替换成功, 当前流程需要持久化
                                knowledgeLogList.addAll(tmpLogList);
                                dialogFlowRobotKnowledgeService.modifyRobotKnowledge(MyBeanUtils.copy(knowledge, RobotKnowledgeModifyVO.class), userId);
                            }
                        });
                    } finally {
                        long endTime = System.currentTimeMillis();
                        logger.info("执行录音复制耗时:问答知识:{}", endTime - startTime);
                        future.complete(knowledgeLogList);
                    }
                });
            }
        }

        // 处理话术配置
        if (Objects.nonNull(config)) {
            CompletableFuture<List<DialogFlowAudioCopyLogVO>> future = new CompletableFuture<>();
            configLogFutureList.add(future);
            DynamicDataSourceApplicationExecutorHolder.execute("话术配置录音复制", () -> {
                long startTime = System.currentTimeMillis();
                List<DialogFlowAudioCopyLogVO> tmpLogList = Collections.emptyList();
                try {
                    tmpLogList = doScanAndAutoUploadSameTextAudio(config, fromText2audioMap, true);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpLogList)) {
                        // 有替换成功, 当前流程需要持久化
                        dialogFlowConfigService.setDialogFlowConfiguration(config, userId);
                    }
                } finally {
                    long endTime = System.currentTimeMillis();
                    logger.info("执行录音复制耗时:话术配置录音复制:{}", endTime - startTime);
                    future.complete(tmpLogList);
                }
            });
        }

        // 阻塞等待同步完成

        List<DialogFlowAudioCopyLogVO> mainStepList = new LinkedList<>();
        List<DialogFlowAudioCopyLogVO> knowledgeStepList = new LinkedList<>();
        List<DialogFlowAudioCopyLogVO> knowledgeLogList = new LinkedList<>();
        List<DialogFlowAudioCopyLogVO> configLogList = new LinkedList<>();

        mainStepCopyLogFutureList.forEach(future -> {
            try {
                mainStepList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                logger.error("录音同步异常", e);
            }
        });

        knowledgeStepCopyLogFutureList.forEach(future -> {
            try {
                knowledgeStepList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                logger.error("录音同步异常", e);
            }
        });

        knowledgeCopyLogFutureList.forEach(future -> {
            try {
                knowledgeLogList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                logger.error("录音同步异常", e);
            }
        });

        configLogFutureList.forEach(future -> {
            try {
                configLogList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                logger.error("录音同步异常", e);
            }
        });

        long end = System.currentTimeMillis();
        logger.info("录音复制总耗时:{}", end - start);
        result.setExcelFileUrl(writeLogToExcel(toDialog, mainStepList, knowledgeStepList, knowledgeLogList, configLogList));

        logList.addAll(mainStepList);
        logList.addAll(knowledgeLogList);
        logList.addAll(knowledgeStepList);
        logList.addAll(configLogList);

        result.setLogList(logList);
        if (CollectionUtils.isEmpty(logList)) {
            result.setSuccess(false);
            result.setMessage("未匹配成功");
        } else {
            dialogOperateLogGenerateService.simpleLog(toDialogFlowId, userId, String.format("同步话术【%s】的录音到当前话术", fromDialogFlowId), OperationLogOperationTypeEnum.DIALOG_AUDIO, DialogLogResourceTypeEnum.AUDIO, DialogFlowAuditActionEnum.UPDATE);
            result.setSuccess(true);
            result.setMessage(String.format("已完成从【%s】到【%s】的录音同步，自动填充%s句录音。", fromDialog.getName(), toDialog.getName(), logList.size()));
        }
        return result;
    }

    private String writeLogToExcel(DialogFlowInfoPO toDialog,
                                   List<DialogFlowAudioCopyLogVO> mainStepList,
                                   List<DialogFlowAudioCopyLogVO> knowledgeStepList,
                                   List<DialogFlowAudioCopyLogVO> knowledgeLogList,
                                   List<DialogFlowAudioCopyLogVO> configLogList) {
        // 处理导出报表的逻辑
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        String excelFileName = String.format("%s_录音同步明细_%s_%s.xlsx", toDialog.getName(), toDialog.getId(), System.currentTimeMillis());

        SXSSFSheet mainStepSheet = workbook.createSheet("主话术流程");
        writeStepCopyLogToExcel(mainStepSheet, mainStepList);

        SXSSFSheet knowledgeSheet = workbook.createSheet("问答知识");
        writeKnowledgeCopyLogToExcel(knowledgeSheet, knowledgeLogList);

        SXSSFSheet knowledgeStepSheet = workbook.createSheet("问答知识流程");
        writeStepCopyLogToExcel(knowledgeStepSheet, knowledgeStepList);

        SXSSFSheet configSheet = workbook.createSheet("话术配置");
        writeDialogConfigCopyLogToExcel(configSheet, configLogList);

        BufferedOutputStream out;
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(excelFileName);
            out = new BufferedOutputStream(fileOutputStream);
            workbook.write(fileOutputStream);
            out.close();

            String dialogFlowWordKey = OssKeyCenter.getDialogFlowAudioCopyLogExcelKey(excelFileName);
            File localFile = new File(excelFileName);
            String url = objectStorageHelper.upload(dialogFlowWordKey, localFile);
            localFile.delete();
            return AddOssPrefixSerializer.getAddOssPrefixUrl(url);
        } catch (Exception e) {
            logger.error("导出excel上传异常", e);
        }
        return null;
    }


    void writeStepCopyLogToExcel(SXSSFSheet sheet, List<DialogFlowAudioCopyLogVO> dataList) {
        sheet.setDefaultRowHeightInPoints(22);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 100 * 256);
        SXSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue("流程名称");
        title.createCell(1).setCellValue("节点名称");
        title.createCell(2).setCellValue("节点话术");
        String stepName = "";
        AtomicInteger index = new AtomicInteger(0);
        for (DialogFlowAudioCopyLogVO log : dataList) {
            if (!stepName.equals(log.getStepName())) {
                // 切换step了, 需要加一行空白
                stepName = log.getStepName();
                SXSSFRow row = sheet.createRow(index.incrementAndGet());
                row.createCell(0).setCellValue(stepName);
            }
            SXSSFRow row = sheet.createRow(index.incrementAndGet());
            row.createCell(1).setCellValue(String.format("%s:%s", log.getLabel(), log.getNodeName()));
            row.createCell(2).setCellValue(log.getText());
        }
    }

    void writeKnowledgeCopyLogToExcel(SXSSFSheet sheet, List<DialogFlowAudioCopyLogVO> dataList) {
        sheet.setDefaultRowHeightInPoints(22);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 100 * 256);
        SXSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue("问答知识标题");
        title.createCell(1).setCellValue("编号");
        title.createCell(2).setCellValue("答案内容");
        String knowledgeName = "";
        AtomicInteger index = new AtomicInteger(0);
        for (DialogFlowAudioCopyLogVO log : dataList) {
            if (!knowledgeName.equals(log.getKnowledgeName())) {
                // 切换Knowledge了, 需要加一行空白
                knowledgeName = log.getKnowledgeName();
                SXSSFRow row = sheet.createRow(index.incrementAndGet());
                row.createCell(0).setCellValue(knowledgeName);
            }
            SXSSFRow row = sheet.createRow(index.incrementAndGet());
            row.createCell(1).setCellValue(String.format("%s:%s", log.getLabel(), log.getKnowledgeName()));
            row.createCell(2).setCellValue(log.getText());
        }
    }

    void writeDialogConfigCopyLogToExcel(SXSSFSheet sheet, List<DialogFlowAudioCopyLogVO> dataList) {
        sheet.setDefaultRowHeightInPoints(22);
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 100 * 256);
        SXSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue("配置内容");
        title.createCell(1).setCellValue("话术");
        String configDesc = "";
        AtomicInteger index = new AtomicInteger(0);
        for (DialogFlowAudioCopyLogVO log : dataList) {
            if (!configDesc.equals(log.getConfigDesc())) {
                // 切换Knowledge了, 需要加一行空白
                configDesc = log.getConfigDesc();
                SXSSFRow row = sheet.createRow(index.incrementAndGet());
                row.createCell(0).setCellValue(configDesc);
            }
            SXSSFRow row = sheet.createRow(index.incrementAndGet());
            row.createCell(1).setCellValue(log.getText());
        }
    }

    private List<DialogFlowAudioCopyLogVO> replaceAudioUrl(Long dialogFlowId,
                                                           Map<String, SameTextAudioWrapper> text2audioMap,
                                                           TextAudioContentPO textAudioContent,
                                                           boolean isCrossDialogCopy) {
        if (BooleanUtils.isTrue(DialogRecordManager.skipAutoUploadSameTextAudio.get())) {
            logger.info("skipAutoUploadSameTextAudio");
            return Collections.emptyList();
        }
        List<DialogFlowAudioCopyLogVO> result = new LinkedList<>();
        List<String> audioTexts = textAudioManager.splitAudioText(textAudioContent);
        List<String> audioUrls = textAudioManager.reviseAudioUrls(textAudioContent, DialogVoiceTypeEnum.MAN_MADE);
        for (int i = 0; i < audioTexts.size(); i++) {
            String text = audioTexts.get(i);
            String url = audioUrls.get(i);
            if (StringUtils.isEmpty(url) && org.apache.commons.lang3.StringUtils.isNotBlank(text)) {
                text = text.trim();
                // 地址为空, 需要扫描相同文案的录音文件
                SameTextAudioWrapper wrapper = text2audioMap.get(text);

                if (Objects.nonNull(wrapper) && org.apache.commons.lang3.StringUtils.isNotBlank(wrapper.getUrl())) {
                    String containedUrl = wrapper.url;
                    logger.info("文本{}, 扫描到相同的录音{}", text, containedUrl);
                    if (wrapper.providerTextAudioLabelSet.size() == 1
                            && wrapper.providerTextAudioLabelSet.contains(textAudioContent.getLabel())
                            && !isCrossDialogCopy) {
                        logger.info("当前文本是删除逻辑, 不执行自动替换: {}", text);
                    } else {
                        if (isCrossDialogCopy && !wrapper.replaced) {
                            // 把原录音下载下来, 然后再重新上传
                            replaceAudioFile(wrapper, dialogFlowId);
                            containedUrl = wrapper.url;
                        }
                        audioUrls.set(i, containedUrl);
                        if (audioUrls != textAudioContent.getAudioUrl()) {
                            textAudioContent.setAudioUrl(audioUrls);
                        }
                        DialogFlowAudioCopyLogVO log = new DialogFlowAudioCopyLogVO();

                        // 组装log
                        log.setText(text);
                        log.setTextLabel(textAudioContent.getLabel());
                        log.setUrl(containedUrl);
                        result.add(log);
                    }
                } else {
                    logger.info("文本{}, 未扫描到相同的录音", text);
                }
            }
        }
        return result;
    }

    private void replaceAudioFile(SameTextAudioWrapper wrapper, Long toDialogFlowId) {
        if (wrapper == null || org.apache.commons.lang3.StringUtils.isBlank(wrapper.getUrl()) || wrapper.isReplaced()) {
            return;
        }
        String url = wrapper.getUrl();
        String toUrl = OssKeyCenter.getDialogRecordOssFilePreKey(toDialogFlowId, "copyAudio", RecordTypeEnum.DIALOG_FLOW_STEP);
        String[] component = url.split("/");
        if (DOT_FILE_PATTERN.matcher(component[component.length - 1]).matches()) {
            toUrl = toUrl + "/" + component[component.length - 1];
        } else {
            String fileName = RandomStringUtils.randomAlphabetic(6) + ".wav";
            toUrl = toUrl + "/" + fileName;
        }
        toUrl = objectStorageHelper.getKeyFromUrl(objectStorageHelper.copyObject(url, toUrl));
        wrapper.setUrl(toUrl);
        wrapper.setReplaced(true);
        logger.info("复制话术录音, 重新上传, 原路径={}, 复制后={}", url, toUrl);
    }

    private Map<String, SameTextAudioWrapper> queryDialogAllText2AudioUrlMap(Long dialogFlowId, DialogVoiceTypeEnum dialogVoiceType) {
        Map<String, SameTextAudioWrapper> result = new HashMap<>();
        mergeProvider(getDialogConfigText2AudioMap(dialogFlowId, dialogVoiceType), result);
        mergeProvider(getDialogKnowledgeText2AudioMap(dialogFlowId, dialogVoiceType), result);
        mergeProvider(getDialogStepText2AudioMap(dialogFlowId, dialogVoiceType), result);
        return result;
    }

    private void mergeProvider(Map<String, SameTextAudioWrapper> from, Map<String, SameTextAudioWrapper> to) {
        from.forEach((text, wrapper) -> {
            SameTextAudioWrapper toWrapper = to.get(text);
            if (toWrapper == null) {
                to.put(text, wrapper);
            } else {
                toWrapper.providerTextAudioLabelSet.addAll(wrapper.providerTextAudioLabelSet);
            }
        });
    }

    private MiniappRecordUploadVO doUploadHumanInterventionRecord(DialogFlowConfigurationPO dialogFlowConfigurationPO, MultipartFile file, Long userId, Long dialogFlowId) {
        // 返回的信息
        MiniappRecordUploadVO recordUploadVO = new MiniappRecordUploadVO();

        // 上传录音
        RecordUploadDTO recordUpload = new RecordUploadDTO();

        // 保存为临时文件
        String basePath = TempFilePathKeyCenter.getDialogRecordTempFilePath(null);
        File basePathFile = new File(basePath);
        if (!basePathFile.exists()) {
            basePathFile.mkdirs();
        }

        String originalFilename = file.getOriginalFilename();
        try {
            file.transferTo(new File(basePath + originalFilename));
        } catch (IOException exp) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "上传的录音文件保存失败");
        }

        // 录音格式转换
        String wavFilePath = null;
        File wavFile;
        try {
            wavFilePath = AudioHandleUtils.mp3ConvertWav(basePath + originalFilename);
            wavFile = new File(wavFilePath);
            wavFile = AudioHandleUtils.autoCutAudioInfo(wavFile, WAV_TO_PCM_HEAD_LEN);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]人工介入录音处理失败, 话术 id: {}, 录音文件: {}, Exception Message: ",
                    dialogFlowId, wavFilePath, e);
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
        }

        // 获取文件名
        String wavFileName;
        try {
            wavFileName = DialogFlowUploadServiceImpl.getFileName(wavFilePath);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取人工介入录音文件名失败, 话术 id: {}, 录音文件: {}, Exception Message: {}",
                    dialogFlowId, wavFilePath, e.getMessage());
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
        }

        // 上传录音到阿里OSS对象
        String ossFileKey = OssKeyCenter.getDialogRecordOssFileKey(dialogFlowId, "-1", wavFileName, RecordTypeEnum.CONFIG_HANG_UP);
        if (ossFileKey == null) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音文件RecordType类型错误");
        }
        String aliRecordUrl = objectStorageHelper.upload(ossFileKey, wavFile);

        recordUpload.setDbRecordUrl(ossFileKey);
        recordUpload.setAliOssFullUrl(aliRecordUrl);

        // 删除本地录音文件
        if (!MyFileUtils.deleteFileByPath(wavFile.getPath())) {
            logger.error("服务器录音文件删除失败");
        }

        DialogFlowHumanInterventionPO po = dialogFlowConfigurationPO.getDialogFlowHumanIntervention();
        po.setReminderUrl(recordUpload.getAliOssFullUrl());
        dialogFlowConfigurationPO.setDialogFlowHumanIntervention(po);
        dialogFlowConfigService.setDialogFlowConfiguration(dialogFlowConfigurationPO, userId);
        dialogFlowService.changeDialogFlowStatus(dialogFlowId, DialogFlowStatusEnum.DRAFT);
        DialogFlowInfoPO infoPO = new DialogFlowInfoPO();
        infoPO.setId(dialogFlowId);
        infoPO.setLastRecordingTime(LocalDateTime.now());
        dialogFlowService.updateNotNull(infoPO);

        // 返回录音地址
        recordUploadVO.setAudioUrl(recordUpload.getAliOssFullUrl());

        logger.info("上传知识库问答录音，{}", recordUpload.toString());

        return recordUploadVO;
    }

    @Data
    private static class SameTextAudioWrapper {
        String url;

        Set<String> providerTextAudioLabelSet = new HashSet<>();

        SameTextAudioWrapper() {

        }

        // url是否已经被目标替换了
        // 这个是针对跨话术复制的时候, 源话术的录音不可直接复制给目标话术, 需要目标话术先下载 -> 上传, 然后才可以复制给话术
        boolean replaced;

        SameTextAudioWrapper(String url) {
            this.url = url;
        }


    }

}
