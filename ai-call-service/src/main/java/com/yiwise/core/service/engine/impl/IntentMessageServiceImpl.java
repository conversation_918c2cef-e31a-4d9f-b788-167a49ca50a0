package com.yiwise.core.service.engine.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.HandleByPageUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.entity.dto.SheetInfoDTO;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.dao.IntentMessagePOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.IntentMessageStatPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.feignclient.billing.TenantAccountClient;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.intentmessage.ReportStatusCountBO;
import com.yiwise.core.model.bo.intentmessage.SendStatusAndCountBO;
import com.yiwise.core.model.bo.mq.SmsCallBackMessageBO;
import com.yiwise.core.model.bo.sms.SmsTemplateSendBO;
import com.yiwise.core.model.dto.IntentMessageJobStatusCountDTO;
import com.yiwise.core.model.dto.IntentMessageStatusCountDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.sms.VirtualPlatformEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sms.*;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.batchjob.BasicBatchService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.stats.IntentMessageStatsService;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.lcs.api.enums.OwnerTypeEnum;
import com.yiwise.lcs.api.enums.SmsTypeEnum;
import javaslang.Tuple;
import javaslang.Tuple3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.IMPORT_RE_ADD_FILE;
import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORT_INTENTMESSAGE;
import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_JOB_TASK;
import static com.yiwise.core.model.bo.export.CommonExportBO.headerService;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.EXPORT_INTENT_MESSAGE_RECORD;


/**
 * @ClassName IntentMessageServiceImpl
 * <AUTHOR>
 * @Date 2018 7 31 16:23
 * @Version 1.0
 **/
@Service
public class IntentMessageServiceImpl extends BasicServiceImpl<IntentMessagePO> implements IntentMessageService {
    private static final Logger logger = LoggerFactory.getLogger(IntentMessageServiceImpl.class);
    @Resource
    private IntentMessagePOMapper intentMessagePOMapper;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private TenantService tenantService;
    @Resource
    private BatchJobInQueueService batchJobInQueueService;

    @Autowired
    @Qualifier("exportSmsIntentRecordJob")
    private Job exportSmsIntentRecordJob;

    @Resource(name = "intentMessageToSmsJob")
    private Job intentMessageToSmsJob;

    @Resource(name = "reSendIntentMessageJob")
    private Job reSendIntentMessageJob;

    @Resource
    private CustomerPersonService customerPersonService;

    @Resource
    private IntentMessageStatsService intentMessageStatsService;

    @Resource
    private SmsJobService smsJobService;

    @Resource
    private BasicBatchService basicBatchService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Resource
    private SmsService smsService;

    @Resource
    private TenantAccountClient tenantAccountClient;


    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public PageResultObject<IntentMessageVO> getIntentMessageList(Long tenantId, Long smsTemplateId, String customerPersonName, String phoneNumber, LocalDate createBeginTime, LocalDate createEndTime, Long robotCallJobId, SendMessageStatusEnum sendMessageStatus, Integer pageNum, Integer pageSize, String reportStatus, List<Long> callJobIds,Long recordId,LocalDateTime startTime,LocalDateTime endTime,SmsTypeEnum smsType,Long costCount) {
        if (pageSize > 100) {
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "分页大小不能超过100");
        }
        List<Long> smsTemplateIds = new ArrayList<>();
        if(smsType != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(tenantId, smsType);
            if(CollectionUtils.isEmpty(smsTemplateIds)){
                return PageResultObject.of(Collections.emptyList());
            }
        }
        Assert.notNull(tenantId, "租户id不能为空");
        List<IntentMessageVO> intentMessagePOList = intentMessagePOMapper.getIntentMessageList(tenantId, smsTemplateId, customerPersonName, phoneNumber, createBeginTime, createEndTime, robotCallJobId, sendMessageStatus, reportStatus, callJobIds, recordId,startTime,endTime,smsTemplateIds,costCount,pageSize * (pageNum - 1), pageSize);

        if (CollectionUtils.isEmpty(intentMessagePOList)) {
            return PageResultObject.of(Collections.emptyList());
        }
        Set<Long> customerPersonIds = intentMessagePOList.stream().map(IntentMessageVO::getCustomerPersonId).collect(Collectors.toSet());
        Map<Long, AccountVO> accountVOMap = customerPersonService.getAccountBaseInfoByIds(tenantId, customerPersonIds);
        Set<Long> smsTemplateIdSet = intentMessagePOList.stream().map(IntentMessageVO::getSmsTemplateId).collect(Collectors.toSet());
        Map<Long,SmsTemplateTypeVO> smsTemplatePOMap = smsTemplateService.getSmsTemplateByIdSet(smsTemplateIdSet);
        intentMessagePOList.forEach(po -> {
            AccountVO accountVO = accountVOMap.get(po.getCustomerPersonId());
            if (Objects.nonNull(accountVO)) {
                po.setCustomerPersonName(accountVO.getName());
            }
            if(smsTemplatePOMap.containsKey(po.getSmsTemplateId())){
                po.setSmsType(smsTemplatePOMap.get(po.getSmsTemplateId()).getSmsType());
            }
        });
        PageResultObject<IntentMessageVO> pageInfo = PageResultObject.of(intentMessagePOList);
        pageInfo.setPageSize(pageSize);
        pageInfo.setNumber(pageNum);
        return pageInfo;
    }

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public Integer getIntentMessageCount(Long tenantId, Long smsTemplateId, String customerPersonName, String phoneNumber, LocalDate createBeginTime, LocalDate createEndTime, Long robotCallJobId, SendMessageStatusEnum sendMessageStatus, Integer pageNum, Integer pageSize, String reportStatus, List<Long> robotCallJobIds,Long recordId,LocalDateTime startTime,LocalDateTime endTime,SmsTypeEnum smsType,Long costCount) {
        Assert.notNull(tenantId, "租户id不能为空");
        List<Long> smsTemplateIds = new ArrayList<>();
        if(smsType != null) {
            smsTemplateIds = smsTemplateService.selectBySmsType(tenantId, smsType);
        }
        return intentMessagePOMapper.getIntentMessageCount(tenantId, smsTemplateId, customerPersonName, phoneNumber, createBeginTime, createEndTime, robotCallJobId, sendMessageStatus, reportStatus, robotCallJobIds,recordId,startTime,endTime,smsTemplateIds, costCount);
    }

    @Override
    public void setIntentMessageSendStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String sid, IntentMessagePO intentMessagePO, Boolean virtual) {
        intentMessagePOMapper.updateIntentMessageSendStatus(intentMessageId, sendStatus, sendErrorMsg, sid);
        // 更新状态统计
        updateIntentMessageStatusCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), sendStatus, intentMessagePO.getSendStatus(), virtual,intentMessagePO.getReportStatus(), intentMessagePO.getCreateTime());
    }

    @Override
    public Map<Long, List<IntentMessagePO>> selectMapByCallRecordIdList(List<Long> callRecordIdList) {
        if (CollectionUtils.isEmpty(callRecordIdList)) {
            return Collections.emptyMap();
        }
        List<IntentMessagePO> list = intentMessagePOMapper.selectByCallRecordIdList(callRecordIdList);
        return MyCollectionUtils.listToMapList(list, IntentMessagePO::getCallRecordId);
    }

    @Override
    public void batchUpdateSendStatus(Set<Long> intentMessageIdSet, SendMessageStatusEnum sendStatus, String sendErrorMsg) {
        if (CollectionUtils.isEmpty(intentMessageIdSet)) {
            return;
        }
        // 更新统计信息
        updateIntentMessageStatusCount(intentMessageIdSet, sendStatus);
        intentMessagePOMapper.batchUpdateIntentMessageSendStatus(intentMessageIdSet, sendStatus, sendErrorMsg);
    }

    @Override
    public void batchUpdateSendTime(Set<Long> intentMessageIdSet, LocalDateTime sendTime) {
        if (CollectionUtils.isEmpty(intentMessageIdSet)) {
            return;
        }
        intentMessagePOMapper.batchUpdateIntentMessageSendTime(intentMessageIdSet, sendTime);
    }

    @Override
    public void updateIntentMessageStatusCount(Long tenantId, Long robotCallJobId, SendMessageStatusEnum sendStatus, SendMessageStatusEnum preSendStatus, Boolean virtual, String preReportStatus, LocalDateTime createTime) {
        intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, sendStatus, 1, createTime);
        if (preSendStatus != null) {
            intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, preSendStatus, -1, createTime);
        }
        // DB内send_status从SEND_SUCCESSFUL变更为SEND_FAILURE,更新统计数据里的RECV状态
        if (SendMessageStatusEnum.SEND_SUCCESSFUL.equals(preSendStatus) && SendMessageStatusEnum.SEND_FAILURE.equals(sendStatus)) {
            if (virtual) {
                Boolean isDY=judgeVirtualTemplateType(robotCallJobId,tenantId);
                //抖音短信发送失败，接收中数据降低，淘宝为接收中转数量降低
                if(null!=isDY&&isDY){
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, -1, createTime);
                }else{
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.CALLBACK_TRANSIT, -1, createTime);
                }
                
            } else {
                logger.info("preReportStatus={}",preReportStatus);
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, preReportStatus != null && preReportStatus.equals("FAIL") ? SendMessageStatusEnum.RECV_FAILURE:SendMessageStatusEnum.RECV_ING, -1, createTime);
            }
            logger.debug("DB内send_status从SEND_SUCCESSFUL变更为SEND_FAILURE,tenantId={},robotCallJobId={}", tenantId, robotCallJobId);
        }
    }
    
    @Override
    public Boolean judgeVirtualTemplateType(Long robotCallJobId,Long tenantId){
        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
        if(null!=robotCallJobPO&&null!=robotCallJobPO.getVirtualSmsTemplateId()){
            SmsTemplatePO smsTemplatePO = smsTemplateService.getSmsTemplatePO(tenantId,robotCallJobPO.getVirtualSmsTemplateId());
            if(null!=smsTemplatePO){
                List<SmsTemplatePO.VirtualConfigDTO> virtualConfigs = smsTemplatePO.getVirtualConfigs();
                if(CollectionUtils.isNotEmpty(virtualConfigs)){
                    for (SmsTemplatePO.VirtualConfigDTO virtualConfigDTO:virtualConfigs){
                        VirtualPlatformEnum platform = virtualConfigDTO.getPlatform();
                        if(VirtualPlatformEnum.DY.equals(platform)){
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    /**
     * 当前只有单元测试在调用
     */
    @Override
    public void updateIntentMessageStatusCount(Set<Long> intentMessageIdSet, SendMessageStatusEnum sendStatus) {
        List<SendStatusAndCountBO> statusCountList = getStatusCount(intentMessageIdSet);
        if (CollectionUtils.isNotEmpty(intentMessageIdSet)) {
            for (SendStatusAndCountBO sendStatusAndCountBO : statusCountList) {
                SendMessageStatusEnum preStatus = sendStatusAndCountBO.getSendStatus();
                Integer count = sendStatusAndCountBO.getCount();
                Long tenantId = sendStatusAndCountBO.getTenantId();
                if (count != null && preStatus != null) {
                    intentMessageStatsService.updateIntentMessageCount(tenantId, sendStatusAndCountBO.getRobotCallJobId(), sendStatus, count, sendStatusAndCountBO.getCreateTime());
                    intentMessageStatsService.updateIntentMessageCount(tenantId, sendStatusAndCountBO.getRobotCallJobId(), preStatus, -1 * count, sendStatusAndCountBO.getCreateTime());
                }
            }

        }
    }

    @Override
    public List<SendStatusAndCountBO> getStatusCount(Collection<Long> intentMessageIdSet) {
        if (CollectionUtils.isEmpty(intentMessageIdSet)) {
            return Collections.emptyList();
        }
        return intentMessagePOMapper.selectStatusCount(intentMessageIdSet);
    }

    @Override
    public List<SendStatusAndCountBO> getMessageStatusCount(Long tenantId, Long robotCallJobId) {
        return intentMessagePOMapper.selectMessageStatusCount(tenantId, robotCallJobId);
    }

    @Override
    public List<SendStatusAndCountBO> getMessageStatusCountCache(Long tenantId, Long robotCallJobId) {
        List<SendStatusAndCountBO> sendStatusAndCountBOS = new ArrayList<>();
        GroupOperation groupOperation = Aggregation.group("sendStatusCode").sum("count").as("count")
                .first("robotCallJobId").as("robotCallJobId")
                .first("tenantId").as("tenantId").first("sendStatusCode").as("sendStatusCode");
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("tenantId").is(tenantId)),
                Aggregation.match(Criteria.where("robotCallJobId").is(robotCallJobId)),
                groupOperation,
                Aggregation.sort(Sort.by(Sort.Direction.ASC, "_id"))
        );
        List<SendStatusAndCountBO> results = mongoTemplate.aggregate(aggregation, IntentMessageStatPO.COLLECTION_NAME, SendStatusAndCountBO.class).getMappedResults();
        logger.info("短信统计result={}", results);
        Map<Integer,SendStatusAndCountBO> map = MyCollectionUtils.listToMap(results,SendStatusAndCountBO::getSendStatusCode,p->p);
        for (SendMessageStatusEnum value : SendMessageStatusEnum.values()) {
            SendStatusAndCountBO sendStatusAndCountBO;
            if(!map.containsKey(value.getCode())){
                sendStatusAndCountBO = new SendStatusAndCountBO();
                sendStatusAndCountBO.setCount(0);
                sendStatusAndCountBO.setTenantId(tenantId);
                sendStatusAndCountBO.setRobotCallJobId(robotCallJobId);
                sendStatusAndCountBO.setSendStatus(value);
                sendStatusAndCountBO.setSendStatusCode(value.getCode());
            }else{
                sendStatusAndCountBO = map.get(value.getCode());
                sendStatusAndCountBO.setSendStatus(value);
            }
            sendStatusAndCountBOS.add(sendStatusAndCountBO);
        }
        return sendStatusAndCountBOS;
    }

	@Override
	public Map<Long, List<SendStatusAndCountBO>> getMessageStatusCountCache(Collection<Long> robotCallJobIds) {
		GroupOperation groupOperation = Aggregation.group("tenantId", "robotCallJobId", "sendStatusCode")
				.sum("count").as("count")
				.first("tenantId").as("tenantId")
				.first("robotCallJobId").as("robotCallJobId")
				.first("sendStatusCode").as("sendStatusCode");
		Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(Criteria.where("robotCallJobId").in(robotCallJobIds)), groupOperation);
		List<SendStatusAndCountBO> results = mongoTemplate.aggregate(aggregation, IntentMessageStatPO.COLLECTION_NAME, SendStatusAndCountBO.class).getMappedResults();

		Map<Long, List<SendStatusAndCountBO>> result = new HashMap<>();
		for (SendStatusAndCountBO sendStatusAndCount : results) {
			List<SendStatusAndCountBO> list = result.computeIfAbsent(sendStatusAndCount.getRobotCallJobId(), a -> new ArrayList<>());
			SendStatusAndCountBO sendStatusAndCountBO = new SendStatusAndCountBO();
			sendStatusAndCountBO.setCount(sendStatusAndCount.getCount());
			sendStatusAndCountBO.setTenantId(sendStatusAndCount.getTenantId());
			sendStatusAndCountBO.setRobotCallJobId(sendStatusAndCount.getRobotCallJobId());
			sendStatusAndCountBO.setSendStatus(SendMessageStatusEnum.getByCode(sendStatusAndCount.getSendStatusCode()));
			sendStatusAndCountBO.setSendStatusCode(sendStatusAndCount.getSendStatusCode());
			list.add(sendStatusAndCountBO);
		}
		return result;
	}

    @TargetDataSource(value = DataSourceEnum.SLAVE)
    @Override
    public void calcAllIntentMessageStatusCount(Long tenantId, Long robotCallJobId) {
        if (tenantId == null) {
            calcIntentMessageStatusCount();
        } else if (robotCallJobId == null) {
            calcIntentMessageStatusCount(tenantId);
        } else {
            calcIntentMessageStatusCount(tenantId, robotCallJobId);
        }
    }

    private void calcIntentMessageStatusCount() {
        HandleByPageUtils.handleItem(100, () -> tenantService.selectBySearchWords(null),
                (index, tenant) -> calcIntentMessageStatusCount(tenant.getTenantId()), true);
    }

    private void calcIntentMessageStatusCount(Long tenantId) {
        HandleByPageUtils.handleItem(100, () -> robotCallJobService.selectByTenantId(tenantId),
                (index, robotCallJob) -> calcIntentMessageStatusCount(robotCallJob.getTenantId(), robotCallJob.getRobotCallJobId()), true);
    }

    private void calcIntentMessageStatusCount(Long tenantId, Long robotCallJobId) {
        List<SendStatusAndCountBO> messageStatusCount = getMessageStatusCount(tenantId, robotCallJobId);
        if (CollectionUtils.isNotEmpty(messageStatusCount)) {
            for (SendStatusAndCountBO sendStatusAndCountBO : messageStatusCount) {
                if (sendStatusAndCountBO.getSendStatus() != null && sendStatusAndCountBO.getCount() != null) {
                    intentMessageStatsService.setIntentMessageCount(tenantId, robotCallJobId, sendStatusAndCountBO.getSendStatus(), sendStatusAndCountBO.getCount());
//                    String intentMessageStatusCount = RedisKeyCenter.getIntentMessageStatusCount(tenantId, robotCallJobId, sendStatusAndCountBO.getSendStatus());
//                    redisOpsService.set(intentMessageStatusCount, sendStatusAndCountBO.getCount());
                }
            }
        }
    }

    @Override
    public JobStartResultVO export(IntentMessageRecordVO intentMessageRecordVO) {
        if(intentMessageRecordVO.getSystemType() == null){
            intentMessageRecordVO.setSystemType(SystemEnum.SMS_PLATFORM);
        }
        List<Long> smsTemplateIds;
        if(intentMessageRecordVO.getSmsType() != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(intentMessageRecordVO.getTenantId(), intentMessageRecordVO.getSmsType());
            intentMessageRecordVO.setSmsTemplateIds(smsTemplateIds);
        }
        List<Tuple3<String, String, Object>> params = org.assertj.core.util.Lists.newArrayList(
                Tuple.of("EXPORT_REQUEST", "Object", intentMessageRecordVO)
        );
        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_INTENTMESSAGE, PlatformTypeEnum.AICC);
        ArrayList<SheetInfoDTO> sheetInfoDTOS = org.assertj.core.util.Lists.newArrayList(SheetInfoDTO.of(BatchConstant.SheetName.EXPORT_INTENT_MESSAGE_RECORD, headerListCache));
        return basicBatchService.exportWithQuery(DataSourceEnum.MASTER, intentMessageRecordVO.getTenantId(), intentMessageRecordVO.getCurrentUserId(), intentMessageRecordVO.getSystemType(), exportSmsIntentRecordJob, EXPORT_INTENT_MESSAGE_RECORD, params, sheetInfoDTOS);
    }

    @Override
    public void setIntentMessageSendStatusWithMsg(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual) {
        intentMessagePOMapper.updateIntentMessageSendStatusWithMsg(intentMessageId, sendStatus, sendErrorMsg, messageDetail, sid);
        // 更新状态统计
        updateIntentMessageStatusCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), sendStatus, intentMessagePO.getSendStatus(), virtual,intentMessagePO.getReportStatus(), intentMessagePO.getCreateTime());
    }

    @Override
    public void setIntentMessageSendStatusWithMsgAndReportStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual) {
        //intentMessagePOMapper.updateIntentMessageSendStatusWithMsg(intentMessageId, sendStatus, sendErrorMsg, messageDetail, sid);
        intentMessagePOMapper.updateIntentMessageSendStatusWithMsgAndReportStatus(intentMessageId, sendStatus, sendErrorMsg, messageDetail, sid);
        // 更新状态统计
        updateIntentMessageStatusCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), sendStatus, intentMessagePO.getSendStatus(), false,null, intentMessagePO.getCreateTime());
    }

    @Override
    public void setIntentMessageSendStatusWithMsgAndSuccessStatus(Long intentMessageId, SendMessageStatusEnum sendStatus, String sendErrorMsg, String messageDetail, String sid, IntentMessagePO intentMessagePO, Boolean virtual) {
        intentMessagePOMapper.updateIntentMessageSendStatusWithMsgAndSuccessStatus(intentMessageId, sendStatus, sendErrorMsg, messageDetail, sid);
        // 更新状态统计
        updateIntentMessageStatusCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), sendStatus, intentMessagePO.getSendStatus(), virtual,null, intentMessagePO.getCreateTime());
    }

    @Override
    public IntentMessagePO getIntentMessageBySid(String sid) {
        if (StringUtils.isEmpty(sid)) {
            return null;
        }
        return intentMessagePOMapper.getIntentMessageBySid(sid);
    }

    @Override
    public List<IntentMessagePO> selectIntentMessageByJobInfo(Long tenantId, Long robotCallJobId, List<Long> callRecordIdList) {
        if (CollectionUtils.isEmpty(callRecordIdList)) {
            return Lists.newArrayList();
        }
        return intentMessagePOMapper.selectIntentMessageByJobInfo(tenantId, robotCallJobId, callRecordIdList);
    }

    @Override
    public List<IntentMessagePO> selectIntentMessageByJobInfo(Long tenantId, Long robotCallJobId, String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return null;
        }
        return intentMessagePOMapper.selectOneIntentMessageByJobInfo(tenantId, robotCallJobId, phoneNumber);
    }

    @Override
    public Long countSendSuccess(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate) {
        if (CollectionUtils.isEmpty(robotCallJobIds)) {
            return 0L;
        }
        return intentMessagePOMapper.countSendSuccess(tenantId, robotCallJobIds, LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX));
    }

    @Override
    public Long countSend(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate) {
        if (CollectionUtils.isEmpty(robotCallJobIds)) {
            return 0L;
        }
        return intentMessagePOMapper.countSend(tenantId, robotCallJobIds, LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX));

    }

    @Override
    public Long countSmsAcceptSuccess(Long tenantId, List<Long> robotCallJobIds, LocalDate localDate) {
        if (CollectionUtils.isEmpty(robotCallJobIds)) {
            return 0L;
        }
        return intentMessagePOMapper.countSmsAcceptSuccess(tenantId, robotCallJobIds, LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX));
    }

    @Override
    public Long countReceiveSuccessByLocalDate(Long tenantId, Long robotCallJobId, Collection<Long> smsTemplateIds, LocalDate localDate) {
        if (CollectionUtils.isEmpty(smsTemplateIds)) {
            return 0L;
        }
        return intentMessagePOMapper.countReceiveSuccessByCreateTime(tenantId, robotCallJobId, smsTemplateIds, LocalDateTime.of(localDate, LocalTime.MIN), LocalDateTime.of(localDate, LocalTime.MAX));
    }

    @Override
    public List<IntentMessagePO> getIntentMessageListByIds(List<Long> idList) {
        return intentMessagePOMapper.getIntentMessageListByIds(idList);
    }

    @Override
    public JobStartResultVO importToSmsJob(IntentMessageRecordVO intentMessageRecordVO, Long tenantId, Long userId) {
        SystemEnum systemEnum = intentMessageRecordVO.getSystemType();
        if (systemEnum == null) {
            systemEnum = SystemEnum.CRM;
        }
        List<Long> smsTemplateIds = null;
        if(intentMessageRecordVO.getSmsType() != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(intentMessageRecordVO.getTenantId(), intentMessageRecordVO.getSmsType());
            intentMessageRecordVO.setSmsTemplateIds(smsTemplateIds);
        }
        SmsJobDetailInfoVO targetJob = smsJobService.getSmsJobInfo(tenantId, intentMessageRecordVO.getTargetJobId());
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_JOB_TASK, PlatformTypeEnum.AICC);

        jobParametersBuilder.addLong("SMS_JOB_ID", intentMessageRecordVO.getTargetJobId());
        jobParametersBuilder.addString("EXPORT_REQUEST", JsonUtils.object2String(intentMessageRecordVO));
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(IMPORT_RE_ADD_FILE, tenantId, userId, Long.toString(System.currentTimeMillis()));
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey));
        jobParametersBuilder.addString("SYSTEM_TYPE", systemEnum.name());
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerListCache));
        jobParametersBuilder.addString("PROPERTY_MAP", JsonUtils.object2String(intentMessageRecordVO.getPropertiesMap()));
        Integer totalCount;
        if (intentMessageRecordVO.getSelectAll()) {
            totalCount = intentMessagePOMapper.getIntentMessageCount(tenantId, null,
                    intentMessageRecordVO.getCustomerPersonName(),
                    intentMessageRecordVO.getPhoneNumber(),
                    intentMessageRecordVO.getStartTime(),
                    intentMessageRecordVO.getEndTime(),
                    null,
                    intentMessageRecordVO.getSendStatus(),
                    intentMessageRecordVO.getReportStatus(),
                    intentMessageRecordVO.getRobotCallJobIds(),
                    intentMessageRecordVO.getRecordId(),null,null,smsTemplateIds,intentMessageRecordVO.getBillingNum());
        } else {
            totalCount = intentMessageRecordVO.getIds().size();
        }

        JobParameters jobParameters = jobParametersBuilder.toJobParameters();
        Job job = intentMessageToSmsJob;

        String jobName = "外呼短信发送历史导入到短信平台-短信任务 " + targetJob.getSmsJob().getName();
        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.IMPORT_INTENT_MESSAGE_TO_SMS_JOB;
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public Long selectCountByJobAndCallRecord(Long tenantId, Long robotCallJobId, Long callRecordId) {
        return intentMessagePOMapper.selectCountByJobAndCallRecord(tenantId, robotCallJobId, callRecordId);
    }

    @Override
    public JobStartResultVO reSendSms(JobReSendSmsVO jobReSendSmsVO, Long tenantId, Long userId) {
        try {
            //判断任务是否在补发
            String key = RedisKeyCenter.getJobReSendSmsRedisKey(jobReSendSmsVO.getRobotCallJobId());
            Boolean ifAbsent = redisOpsService.getRedisTemplate().opsForHash().putIfAbsent(key, ApplicationConstant.RE_SEND_SMS_HASH_KEY, ApplicationConstant.BATCH_JOB_PROCESS);
            if (!ifAbsent) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前短信补发进行时");
            }
            RobotCallJobPO jobPO = robotCallJobService.selectByKey(jobReSendSmsVO.getRobotCallJobId());
            if (Objects.isNull(jobPO)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务不存在");
            }
            Integer totalCount;
            if (CollectionUtils.isEmpty(jobReSendSmsVO.getReIntentMessageIdList())) {
                totalCount = intentMessagePOMapper.getIntentMessageCount(tenantId, null,
                        null,
                        jobReSendSmsVO.getPhoneNumber(),
                        null,
                        null,
                        jobReSendSmsVO.getRobotCallJobId(),
                        jobReSendSmsVO.getSendStatus(),
                        jobReSendSmsVO.getReportStatus(),
                        null,
                        null,
                        jobReSendSmsVO.getSendStartTime(),
                        jobReSendSmsVO.getSendEndTime(),
                        null,null);
            } else {
                totalCount = jobReSendSmsVO.getReIntentMessageIdList().size();
            }

            if (checkAccountBalance(jobReSendSmsVO, tenantId, totalCount)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "账户余额不足");
            }
            SmsTemplateSendBO smsTemplateSendBO = new SmsTemplateSendBO();
            SmsTemplateSendBO virtualSmsTemplateSendBO = new SmsTemplateSendBO();
            if (jobReSendSmsVO.getSmsTemplateId() != null) {
                smsTemplateSendBO = smsTemplateService.getSmsTemplateSendBO(tenantId, jobReSendSmsVO.getSmsTemplateId());
            }
            if (jobReSendSmsVO.getVirtualSmsTemplateId() != null) {
                virtualSmsTemplateSendBO = smsTemplateService.getSmsTemplateSendBO(tenantId, jobReSendSmsVO.getVirtualSmsTemplateId());
            }
            SystemEnum systemEnum = jobReSendSmsVO.getSystemType();
            if (systemEnum == null) {
                systemEnum = SystemEnum.AICC;
            }
            String jobName = String.format("外呼任务[%s]-短信补发", jobPO.getName());
            JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
            List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_INTENTMESSAGE, PlatformTypeEnum.AICC);

            jobParametersBuilder.addString("QUERY", JsonUtils.object2String(jobReSendSmsVO));
            String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(jobName, tenantId, userId, Long.toString(System.currentTimeMillis()));
            jobParametersBuilder.addLong("TENANT_ID", tenantId);
            jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
            jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey));
            jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
            jobParametersBuilder.addString("SYSTEM_TYPE", systemEnum.name());
            jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
            jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerListCache));
            jobParametersBuilder.addString("SMS_TEMPLATE", JsonUtils.object2String(smsTemplateSendBO));
            jobParametersBuilder.addString("VIRTUAL_SMS_TEMPLATE", JsonUtils.object2String(virtualSmsTemplateSendBO));
            jobParametersBuilder.addString("SMS_TEMPLATE", JsonUtils.object2String(smsTemplateSendBO));
            jobParametersBuilder.addLong("ROBOT_CALL_JOB_ID", jobReSendSmsVO.getRobotCallJobId());

            JobParameters jobParameters = jobParametersBuilder.toJobParameters();
            Job job = reSendIntentMessageJob;

            SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.RE_SEND_SMS_JOB;
            SpringBatchJobBO springBatchJobBO =
                    new SpringBatchJobBO(
                            job,
                            jobParameters,
                            jobName,
                            tenantId,
                            null,
                            totalCount,
                            userId,
                            jobType,
                            systemEnum,
                            false);
            return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
        }catch (Exception e){
            logger.info("删除任务执行标记,jobId={}",jobReSendSmsVO.getRobotCallJobId());
            redisOpsService.getRedisTemplate().opsForHash().delete(RedisKeyCenter.getJobReSendSmsRedisKey(jobReSendSmsVO.getRobotCallJobId()), ApplicationConstant.RE_SEND_SMS_HASH_KEY);
            throw e;
        }
    }

    private Boolean checkAccountBalance(JobReSendSmsVO jobReSendSmsVO,Long tenantId,Integer totalCount) {
        List<Long> templateIds = new ArrayList<>();
        if(jobReSendSmsVO.getSmsTemplateId() != null){
            templateIds.add(jobReSendSmsVO.getSmsTemplateId());
        }
        if(jobReSendSmsVO.getVirtualSmsTemplateId() != null){
            templateIds.add(jobReSendSmsVO.getVirtualSmsTemplateId());
        }
        List<SmsTemplateSendBO> smsTemplateSendBOList = smsTemplateService.getSmsTemplateSendBOList(tenantId, templateIds);
        if(CollectionUtils.isEmpty(smsTemplateSendBOList)){
            throw new ComException(ComErrorCode.VALIDATE_ERROR,"短信模板不存在");
        }
        TenantPO tenantPO = tenantService.selectByKey(tenantId);
        Assert.notNull(tenantPO, "租户不存在");

        Long smsCost = 0L;
        boolean distributor = false;
        boolean direct = false;
        for (SmsTemplateSendBO smsTemplateSendBO : smsTemplateSendBOList) {
            if(BooleanUtils.isFalse(distributor) && !OwnerTypeEnum.accountFare(smsTemplateSendBO.getOwnerType())){
                logger.info("存在自有短信通道");
                distributor =true;
            }
            if(BooleanUtils.isFalse(direct) && OwnerTypeEnum.accountFare(smsTemplateSendBO.getOwnerType())){
                logger.info("存在普通短信通道");
                direct =true;
            }
            Long smsPrice = smsService.getSmsCost(smsTemplateSendBO);
            if(smsPrice * totalCount > smsCost) {
                smsCost = smsPrice * totalCount;
            }
        }
        //账户余额校验
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            if(BooleanUtils.isTrue(distributor)) {
                logger.info("自有短信通道计费");
                return tenantAccountClient.willBeInArrears(tenantId, TenantAccountEnum.DISTRIBUTOR_ACCOUNT_FARE, smsCost);
            }
            if(BooleanUtils.isTrue(direct)) {
                return tenantAccountClient.willBeInArrears(tenantId, TenantAccountEnum.ACCOUNT_FARE, smsCost);
            }
        } else {
            return tenantPO.getAccountFare() < smsCost;
        }
        return false;
    }

    @Override
    public Boolean checkReSendProcess(Long robotCallJobId) {
        return redisOpsService.isKeyExist(RedisKeyCenter.getJobReSendSmsRedisKey(robotCallJobId));
    }

    @Override
    public Integer getReportSuccessRecordCountByJob(Long tenantId,Long robotCallJobId, Long callRecordId) {
        return intentMessagePOMapper.getReportSuccessRecordCountByJob(tenantId,robotCallJobId,callRecordId);
    }

    @Override
    public IntentMessagePO getLastIntentMessageByRecordId(Long recordId) {
        return intentMessagePOMapper.getLastIntentMessageByRecordId(recordId);
    }
    @Override
    public List<IntentMessagePO> selectAllIntentMessageByTime(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return intentMessagePOMapper.selectAllIntentMessageByTime(tenantId, startTime, endTime);
    }

	@Override
	public List<ReportStatusCountBO> countByReportStatus(Long tenantId, LocalDate startDate, LocalDate endDate) {
		return intentMessagePOMapper.countByReportStatus(tenantId, startDate, endDate);
	}

	@Override
	public Long selectIntentMessageIdAfterLocalDateTime(LocalDateTime localDateTime) {
		return intentMessagePOMapper.selectIntentMessageIdAfterLocalDateTime(localDateTime);
	}

	@Override
	public Long selectIntentMessageIdBeforeLocalDateTime(LocalDateTime localDateTime) {
		Long intentMessageId = intentMessagePOMapper.selectIntentMessageIdAfterEqualLocalDateTime(localDateTime);
		if (intentMessageId != null) {
			intentMessageId--;
		}
		return intentMessageId;
	}

	@Override
	public List<IntentMessagePO> selectOrderByIntentMessageId(Long intentMessageId, Integer batchSize, Long lastIntentMessageId) {
		return intentMessagePOMapper.selectOrderByIntentMessageId(intentMessageId, batchSize, lastIntentMessageId);
	}

    @Override
    public Integer countTemplateIdAndCallRecordId(Long tenantId, Long smsTemplateId, Long callRecordId, Long robotCallJobId) {
        if (tenantId == null || smsTemplateId == null || callRecordId == null || robotCallJobId == null) {
            return 0;
        }
        return intentMessagePOMapper.countTemplateIdAndCallRecordId(tenantId, smsTemplateId, callRecordId, robotCallJobId);
    }

	@Override
	public Map<Long, IntentMessageStatusCountDTO> countByJobAndStatus(Collection<Long> robotCallJobIds, LocalDateTime startTime, LocalDateTime endTime) {
		if (CollectionUtils.isEmpty(robotCallJobIds)) {
			return Collections.emptyMap();
		}
		Map<Long, IntentMessageStatusCountDTO> result = new HashMap<>();
		List<IntentMessageJobStatusCountDTO> list = intentMessagePOMapper.countByJobAndStatus(robotCallJobIds, startTime, endTime);
		for (IntentMessageJobStatusCountDTO item : list) {
			IntentMessageStatusCountDTO innerItem = result.computeIfAbsent(item.getRobotCallJobId(), a -> new IntentMessageStatusCountDTO());
			if (item.getSendStatus() != null) {
				innerItem.getSendStatusMap().merge(item.getSendStatus(), item.getCount(), Integer::sum);
			}
			if (StringUtils.hasText(item.getReportStatus())) {
				innerItem.getReportStatusMap().merge(item.getReportStatus(), item.getCount(), Integer::sum);
			}
		}
		logger.debug("list = {}, result = {}", JsonUtils.object2String(list), JsonUtils.object2String(result));
		return result;
	}
    
    @Override
    public void incDouyinStatisData(DouDianSmsCallBack smsCallBackBaseVO){
        if("接收成功".equals(smsCallBackBaseVO.getDescription())){
            SmsCallBackMessageBO smsCallBackMessageBO = smsCallBackBaseVO.packageSmsCallBackMessageBO();
            if(null!=smsCallBackMessageBO){
                String sid = smsCallBackMessageBO.getSid();
                IntentMessagePO intentMessagePO = getIntentMessageBySid(sid);

                //接收成功，统计数据修改
                if(null!=intentMessagePO&&"SUCCESS".equals(smsCallBackMessageBO.getReportStatus())){
                    intentMessageStatsService.updateIntentMessageCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), SendMessageStatusEnum.RECV_ING, -1, intentMessagePO.getCreateTime());
                    intentMessageStatsService.updateIntentMessageCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), SendMessageStatusEnum.RECV_SUCCESS, 1, intentMessagePO.getCreateTime());
                }
            }
        }else if(!"0".equals(smsCallBackBaseVO.getStatusCode())){
            SmsCallBackMessageBO smsCallBackMessageBO = smsCallBackBaseVO.packageSmsCallBackMessageBO();
            
            if(null!=smsCallBackMessageBO){
                String sid = smsCallBackMessageBO.getSid();
                IntentMessagePO intentMessagePO = getIntentMessageBySid(sid);
                if(null!=intentMessagePO){
                    intentMessagePOMapper.updateReportErrorDetail(intentMessagePO.getIntentMessageId(),smsCallBackBaseVO.getDescription());
                }
            }
        }
        
    }
}
