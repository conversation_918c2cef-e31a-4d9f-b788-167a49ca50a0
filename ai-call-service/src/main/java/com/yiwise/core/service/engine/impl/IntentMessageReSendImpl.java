package com.yiwise.core.service.engine.impl;

import com.yiwise.aicc.common.enums.billing.*;
import com.yiwise.aicc.common.enums.billing.TradeTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.billing.api.bo.request.AsyncCostRequestBO;
import com.yiwise.billing.client.AsyncBillingService;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.entity.financial.SmsFinancialPO;
import com.yiwise.core.feignclient.rcs.AccountClient;
import com.yiwise.core.helper.*;
import com.yiwise.core.lock.RedisLock;
import com.yiwise.core.model.bo.robotcalltask.PreFilterTaskBO;
import com.yiwise.core.model.bo.sms.SmsStatsBO;
import com.yiwise.core.model.bo.sms.SmsTemplateSendBO;
import com.yiwise.core.model.dto.CommonDecryptResultDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.billing.SmsCostRequestExtendInfoEnum;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.sms.SmsBusinessTypeEnum;
import com.yiwise.core.model.vo.billing.BillingCalloutContext;
import com.yiwise.core.model.vo.billing.BillingCalloutSmsContext;
import com.yiwise.core.model.vo.qf.*;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.callstats.CallStatsService;
import com.yiwise.core.service.financial.SmsFinancialService;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.TenantEncryptConfigureService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.redis.RedisLockKeyHelper;
import com.yiwise.core.service.stats.IntentMessageStatsService;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.lcs.api.enums.OwnerTypeEnum;
import com.yiwise.lcs.api.enums.SmsTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yiwise.core.config.ApplicationConstant.*;
import static com.yiwise.core.model.enums.SendMessageStatusEnum.SEND_FAILURE;

/**
 * @author: <EMAIL>
 * @date: 2024 08 27 15:37
 */
@Slf4j
@Service
public class IntentMessageReSendImpl implements IntentMessageReSend {

    @Resource
    private SmsService smsService;

    @Resource
    private CallRecordInfoService callRecordInfoService;

    @Resource
    private TenantService tenantService;

    @Resource
    private AccountClient accountClient;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private IntentMessageService intentMessageService;

    @Resource
    private IntentMessageStatsService intentMessageStatsService;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private CallStatsService callStatsService;

    @Resource
    private TenantEncryptConfigureService tenantEncryptConfigureService;

    @Resource
    private SmsFinancialService smsFinancialService;

    @Resource
    private AsyncBillingService asyncBillingService;

    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Resource
    private RobotCallTaskService robotCallTaskService;

    @Override
    public void sendFailureByReSendAsync(CallRecordPO callRecordInfo, TenantPO tenantPO, Map<String, String> runTimeProperties, AccountVO accountVO, PreFilterTaskBO smsPreFilterTaskBO) {
        RedisLock redisLock = new RedisLock(redisOpsService.getRedisTemplate(), String.format(RedisLockKeyHelper.RESEND_SMS_LOCK, callRecordInfo.getCallRecordId()));
        try {
            boolean getLock = redisLock.lock();
            if (getLock) {
                sendFailureByReSend(callRecordInfo, tenantPO, runTimeProperties, accountVO, smsPreFilterTaskBO);
            }else {
                log.info("短信补推-不可同时操作");
            }
        } finally {
            redisLock.unlock();
        }
    }

    private void sendFailureByReSend(CallRecordPO callRecordInfo, TenantPO tenantPO, Map<String, String> runTimeProperties, AccountVO accountVO, PreFilterTaskBO smsPreFilterTaskBO) {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime nowTime20 = now.toLocalDate().atTime(LocalTime.of(20, 0));
            if (CHECK_RESEND_TIME && now.isAfter(nowTime20)) {
                return;
            }

            Long tenantId = callRecordInfo.getTenantId();
            Long robotCallJobId =callRecordInfo.getRobotCallJobId();
            Long callRecordId = callRecordInfo.getCallRecordId();

            RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
            if (BooleanUtils.isNotTrue(robotCallJobPO.getEnableReSendSms())) {
                return;
            }

            List<Long> reSendSmsTemplateIds = robotCallJobPO.getReSendSmsTemplateIds();

            String realPhoneNumer = callRecordInfo.getCalledPhoneNumber();
            if (tenantEncryptConfigureService.commonDecryptSupport(tenantPO)) {
                CommonDecryptResultDTO resultDTO = tenantEncryptConfigureService.commonDecrypt(tenantPO.getTenantId(), tenantPO.getEncryptType(), callRecordInfo.getCalledPhoneNumber());
                if (tenantEncryptConfigureService.commonDecryptSuccess(resultDTO)) {
                    realPhoneNumer = resultDTO.getMobile();
                } else {
                    log.warn("短信自动补发解密失败 callRecordId = {}", callRecordId);
                    return;
                }
            }

            for (Long reSendId : reSendSmsTemplateIds) {
                int reSendCount = intentMessageService.countTemplateIdAndCallRecordId(tenantId, reSendId, callRecordId, robotCallJobId);
                if (reSendCount > 0) {
                    continue;
                }
                log.info("短信自动补发-发送失败 callRecordId = {}, SmsTemplateId = {}", callRecordId, reSendId);
                Boolean isReSend = reSendFailure(realPhoneNumer, callRecordInfo, tenantPO, reSendId, runTimeProperties, accountVO, smsPreFilterTaskBO);
                if (isReSend) {
                    break;
                }
            }
        }catch (Exception e) {
            log.error("短信自动补发-发送失败异常", e);
        }
    }

    @Override
    public void receiveFailByReSend(IntentMessagePO intentMessagePO) {

        try {
            LocalDateTime now = LocalDateTime.now();

            LocalDateTime nowTime20 = now.toLocalDate().atTime(LocalTime.of(20, 0));
            if (CHECK_RESEND_TIME && now.isAfter(nowTime20)) {
                return;
            }

            Long tenantId = intentMessagePO.getTenantId();
            Long robotCallJobId =intentMessagePO.getRobotCallJobId();
            Long callRecordId = intentMessagePO.getCallRecordId();

            //检验同一任务下是否有发送成功记录
            int count = intentMessageService.getReportSuccessRecordCountByJob(tenantId, robotCallJobId, callRecordId);
            if (count != 0) {
                return;
            }
            CallRecordPO callRecordInfo = callRecordInfoService.selectByKey(callRecordId);
            LocalDateTime startTime = callRecordInfo.getStartTime() != null ? callRecordInfo.getStartTime() : LocalDateTime.now();
            startTime = startTime.toLocalDate().atTime(LocalTime.of(20, 0));
            if (CHECK_RESEND_TIME && now.isAfter(startTime)) {
                return;
            }

            TenantPO tenantPO = tenantService.getTenantByTenantId(tenantId);
            RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(robotCallJobId);
            List<Long> reSendSmsTemplateIds = robotCallJobPO.getReSendSmsTemplateIds();

            String realPhoneNumer = callRecordInfo.getCalledPhoneNumber();
            if (tenantEncryptConfigureService.commonDecryptSupport(tenantPO)) {
                CommonDecryptResultDTO resultDTO = tenantEncryptConfigureService.commonDecrypt(tenantPO.getTenantId(), tenantPO.getEncryptType(), callRecordInfo.getCalledPhoneNumber());
                if (tenantEncryptConfigureService.commonDecryptSuccess(resultDTO)) {
                    realPhoneNumer = resultDTO.getMobile();
                } else {
                    log.warn("短信自动补发解密失败 callRecordId = {}", callRecordId);
                    return;
                }
            }

            AccountVO accountVO = accountClient.getAccountInfo(intentMessagePO.getCustomerPersonId(), tenantId);
            PreFilterTaskBO smsPreFilterTaskBO = getSmsPreFilterTaskBO(intentMessagePO, callRecordInfo);
            for (Long reSendId : reSendSmsTemplateIds) {
                int reSendCount = intentMessageService.countTemplateIdAndCallRecordId(tenantId, reSendId, callRecordId, robotCallJobId);
                if (reSendCount > 0) {
                    continue;
                }
                log.info("短信自动补发-接收失败 callRecordId = {}, SmsTemplateId = {}", callRecordId, reSendId);
                Boolean isReSend = reSendFailure(realPhoneNumer, callRecordInfo, tenantPO, reSendId, callRecordInfo.getProperties(), accountVO, smsPreFilterTaskBO);
                if (isReSend) {
                    break;
                }
            }
        }catch (Exception e) {
            log.error("短信自动补发-接收失败异常", e);
        }
    }

    /**
     * 发送失败-触发自动补发短信
     */
    public Boolean reSendFailure(String realPhoneNumer, CallRecordPO callRecordPO, TenantPO tenantPO, Long reSendSmsTemplateId, Map<String, String> runTimeProperties, AccountVO accountInfo, PreFilterTaskBO smsPreFilterTaskBO) {
        if (reSendSmsTemplateId == null) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = callRecordPO.getStartTime() != null ? callRecordPO.getStartTime() : LocalDateTime.now();
        startTime = startTime.toLocalDate().atTime(LocalTime.of(20, 0));
        if (CHECK_RESEND_TIME && now.isAfter(startTime)) {
            return true;
        }

        Long callRecordId = callRecordPO.getCallRecordId();

        Long tenantId = callRecordPO.getTenantId();
        Long robotCallJobId =callRecordPO.getRobotCallJobId();

        //检验同一任务下是否有发送成功记录
        int count = intentMessageService.getReportSuccessRecordCountByJob(tenantId, callRecordPO.getRobotCallJobId(), callRecordId);
        if (count != 0) {
            return false;
        }

        IntentMessagePO reIntentMessagePO = new IntentMessagePO();
        IntentMessagePO lastIntentMessagePO = intentMessageService.getLastIntentMessageByRecordId(callRecordId);
        log.info("lastIntentMessagePO-intentMessageId = {}", lastIntentMessagePO.getIntentMessageId());
        boolean isVirtual = PhoneNumberHelper.isExtensionPhone(lastIntentMessagePO.getPhoneNumber());

        BeanUtils.copyProperties(lastIntentMessagePO,reIntentMessagePO);
        reIntentMessagePO.setIntentMessageId(null);
        reIntentMessagePO.setIntentMessageType(IntentMessageTypeEnum.RESEND);
        reIntentMessagePO.setReportStatus(null);
        reIntentMessagePO.setSendStatus(SendMessageStatusEnum.SEND_SUCCESSFUL);

        boolean decrypt = true;
        boolean result = false;
        if (isVirtual) {
            SmsTemplateSendBO virtualSmsTemplateSendBO = smsTemplateService.getSmsTemplateSendBO(tenantId, reSendSmsTemplateId);
            if (virtualSmsTemplateSendBO == null || !SmsTypeEnum.VIRTUAL.equals(virtualSmsTemplateSendBO.getSmsType())) {
                reIntentMessagePO.setSendErrorMsg("短信模板类型不匹配");
                reIntentMessagePO.setSendStatus(SendMessageStatusEnum.SEND_FAILURE);
                decrypt = false;
            }

            Set<String> varSet = SmsHelper.getPlaceholderOrLink(virtualSmsTemplateSendBO.getTextWithSignature());
            if (!varSet.isEmpty()) {
                if (runTimeProperties == null || runTimeProperties.isEmpty()) {
                    RobotCallTaskPO robotCallTaskPO = robotCallTaskService.selectByKey(callRecordPO.getRobotCallTaskId());
                    runTimeProperties = robotCallTaskPO.getProperties();
                }
            }

            reIntentMessagePO.setSmsTemplateId(virtualSmsTemplateSendBO.getSmsTemplateId());
            reIntentMessagePO.setSmsTemplateName(virtualSmsTemplateSendBO.getSmsTemplateName());
            reIntentMessagePO.setSignatureName(virtualSmsTemplateSendBO.getSmsSignatureName());
            reIntentMessagePO.setCreateTime(LocalDateTime.now());
            intentMessageService.saveNotNull(reIntentMessagePO);
            if(decrypt) {
                int billCount = smsService.sendSms(reIntentMessagePO, realPhoneNumer, virtualSmsTemplateSendBO, runTimeProperties, accountInfo, smsPreFilterTaskBO);
                if (billCount > 0) {
                    result = true;
                }
                updateCostCount(reIntentMessagePO.getIntentMessageId(), virtualSmsTemplateSendBO, callRecordPO);
                billCost(billCount, reIntentMessagePO, callRecordPO, virtualSmsTemplateSendBO, tenantPO);
                addSmsFinancial(reIntentMessagePO, virtualSmsTemplateSendBO, billCount, tenantId);
            }else {
                //设置状态为发送失败
                intentMessageService.setIntentMessageSendStatusWithMsg(reIntentMessagePO.getIntentMessageId(), SEND_FAILURE, "短信模板类型不匹配", "", "", lastIntentMessagePO, isVirtual);

                smsService.smsSendResultCallback(reIntentMessagePO, callRecordPO.getProperties(), SendMessageStatusEnum.SEND_FAILURE);
            }
        }else{
            SmsTemplateSendBO  smsTemplateSendBO = smsTemplateService.getSmsTemplateSendBO(tenantId, reSendSmsTemplateId);
            if (smsTemplateSendBO == null || SmsTypeEnum.VIRTUAL.equals(smsTemplateSendBO.getSmsType())) {
                reIntentMessagePO.setSendErrorMsg("短信模板类型不匹配");
                reIntentMessagePO.setSendStatus(SendMessageStatusEnum.SEND_FAILURE);
                decrypt = false;
            }

            Set<String> varSet = SmsHelper.getPlaceholderOrLink(smsTemplateSendBO.getTextWithSignature());
            if (!varSet.isEmpty()) {
                if (runTimeProperties == null || runTimeProperties.isEmpty()) {
                    RobotCallTaskPO robotCallTaskPO = robotCallTaskService.selectByKey(callRecordPO.getRobotCallTaskId());
                    runTimeProperties = robotCallTaskPO.getProperties();
                }
            }

            reIntentMessagePO.setSmsTemplateId(smsTemplateSendBO.getSmsTemplateId());
            reIntentMessagePO.setCreateTime(LocalDateTime.now());
            reIntentMessagePO.setSmsTemplateName(smsTemplateSendBO.getSmsTemplateName());
            reIntentMessagePO.setSignatureName(smsTemplateSendBO.getSmsSignatureName());
            intentMessageService.saveNotNull(reIntentMessagePO);
            if(decrypt) {
                int billCount = smsService.sendSms(reIntentMessagePO, realPhoneNumer, smsTemplateSendBO, runTimeProperties, accountInfo, smsPreFilterTaskBO);
                updateCostCount(reIntentMessagePO.getIntentMessageId(), smsTemplateSendBO, callRecordPO);
                if (billCount > 0) {
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, 1, reIntentMessagePO.getCreateTime());
                    if (SendMessageStatusEnum.SEND_SUCCESSFUL.equals(lastIntentMessagePO.getSendStatus()) && lastIntentMessagePO.getReportStatus() != null && lastIntentMessagePO.getReportStatus().equals(ReportStatusEnum.FAIL.name())) {
                        intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_FAILURE, -1, reIntentMessagePO.getCreateTime());
                    }else if (SendMessageStatusEnum.SEND_FAILURE.equals(lastIntentMessagePO.getSendStatus())) {
                        intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.SEND_SUCCESSFUL, 1, reIntentMessagePO.getCreateTime());
                        intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.SEND_FAILURE, -1, reIntentMessagePO.getCreateTime());
                    }
                    result = true;
                }else {
                    // 发送失败 统计需要单独处理
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.SEND_FAILURE, -1, reIntentMessagePO.getCreateTime());
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.SEND_SUCCESSFUL, 1, reIntentMessagePO.getCreateTime());
                    intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, 1, reIntentMessagePO.getCreateTime());
                    if (SendMessageStatusEnum.SEND_SUCCESSFUL.equals(lastIntentMessagePO.getSendStatus()) && lastIntentMessagePO.getReportStatus() != null && lastIntentMessagePO.getReportStatus().equals(ReportStatusEnum.FAIL.name())) {
                        intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_FAILURE, -1, reIntentMessagePO.getCreateTime());
                    }
                }
                billCost(billCount, reIntentMessagePO, callRecordPO, smsTemplateSendBO, tenantPO);
                addSmsFinancial(reIntentMessagePO, smsTemplateSendBO, billCount, tenantId);
            }else {
                //设置状态为发送失败
                intentMessageService.setIntentMessageSendStatusWithMsg(reIntentMessagePO.getIntentMessageId(), SEND_FAILURE, "短信模板类型不匹配", "", "", lastIntentMessagePO, isVirtual);

                smsService.smsSendResultCallback(reIntentMessagePO, callRecordPO.getProperties(), SendMessageStatusEnum.SEND_FAILURE);
            }
        }
        return result;
    }

    private PreFilterTaskBO getSmsPreFilterTaskBO(IntentMessagePO reIntentMessagePO, CallRecordPO callRecordPO) {

        Long tenantId = callRecordPO.getTenantId();
        if (QiFuHelper.isQiFuTenant(tenantId)) {
            try {
                if (Objects.isNull(callRecordPO) || MapUtils.isEmpty(callRecordPO.getProperties()) || StringUtils.isBlank(callRecordPO.getProperties().get(QiFuHelper.BATCH_NO))) {
                    return  PreFilterTaskBO.builder()
                            .preFilter(true)
                            .preFilterReason("未获取到批次ID")
                            .build();
                }
                QiFuRiskRequestDTO requestDTO = QiFuRiskRequestDTO.builder()
                        .mobileMd5(reIntentMessagePO.getPhoneNumber())
                        .properties(callRecordPO.getProperties())
                        .build();

                QiFuSmsRiskBatchResponseDTO responseDTO = QiFuHelper.smsRisk(QiFuRiskBatchRequestDTO.builder()
                        .requestDTOS(Collections.singletonList(requestDTO))
                        .build());

                if (responseDTO.isSuccess()) {
                    Map<String, Boolean> responseMap = MyCollectionUtils.listToMap(responseDTO.getSmsCntDetails(), QiFuSmsCountResponseDataItemDTO::getMobileMd5, QiFuSmsCountResponseDataItemDTO::getIsLimit);
                    if (BooleanUtils.isNotFalse(responseMap.get(requestDTO.getMobileMd5()))) {
                        return PreFilterTaskBO.builder()
                                .preFilter(true)
                                .preFilterReason("发送超限")
                                .build();
                    }
                    return PreFilterTaskBO.builder().build();
                } else {
                    return PreFilterTaskBO.builder()
                            .preFilter(true)
                            .preFilterReason(responseDTO.getMsg())
                            .build();
                }
            }catch (Exception e) {
                log.error("IntentMessageReSendImpl getSmsPreFilterTaskBO error", e);
                return PreFilterTaskBO.builder()
                        .preFilter(true)
                        .preFilterReason("查询频控异常")
                        .build();
            }
        }
        return PreFilterTaskBO.builder().build();
    }


    private void updateCostCount(Long intentMessageId,SmsTemplateSendBO smsTemplateSendBO,CallRecordPO callRecordPO) {
        IntentMessagePO intentMessagePO = new IntentMessagePO();
        intentMessagePO.setIntentMessageId(intentMessageId);
        try {
            String realText = SmsHelper.getFullContent(smsTemplateSendBO.getTextWithSignature(), callRecordPO.getProperties());
            intentMessagePO.setCostCount(smsService.getSmsCount(realText));
        } catch (Exception e) {
            log.error("计算短信计费条数时拼接短信变量出错", e);
            intentMessagePO.setCostCount(smsService.getSmsCount(smsTemplateSendBO.getTextWithSignature()));
        }
        intentMessageService.updateNotNull(intentMessagePO);
    }

    private void billCost(int billCount, IntentMessagePO intentMessagePO, CallRecordPO callRecordInfo, SmsTemplateSendBO templateSendBO, TenantPO tenantPO) {
        Long smsPrice = templateSendBO.getRealSmsPrice();
        Long smsProductPrice = templateSendBO.getSmsProductPrice();
        log.debug("计费条数={}, 单条费用={}, 单条成本={}", billCount, smsPrice, smsProductPrice);
        if (billCount <= 0) {
            return;
        }
        if(SmsTypeEnum.SMART_SMS.equals(templateSendBO.getSmsType())){
            return;
        }
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            log.info("新版计费");
            //新版短信计费
            BillingCalloutContext billingCalloutContext = buildBillingContext(callRecordInfo, tenantPO);
            if ("PENDING".equals(intentMessagePO.getReportStatus())) {
                //短信回调默认最迟72小时
                redisOpsService.set(RedisKeyCenter.getCallRecordStatsQueryForSmsReturn(intentMessagePO.getSid()), BillingCalloutSmsContext.builder()
                        .calloutContext(billingCalloutContext)
                        .resultStatus(callRecordInfo.getResultStatus())
                        .build(), 73, TimeUnit.HOURS);
            }
            //新版短信计费
            AsyncCostRequestBO asyncCostRequestBO = new AsyncCostRequestBO();
            Map<String, Object> extendInfo = new HashMap<>(8);
            // 处理回调的时候查阅的上下文
            extendInfo.put("billingCalloutContext", JsonUtils.object2String(billingCalloutContext));
            extendInfo.put(SmsCostRequestExtendInfoEnum.smsType.name(), templateSendBO.getSmsType());
            extendInfo.put(SmsCostRequestExtendInfoEnum.tenantPayType.name(), tenantPO.getTenantPayType());
            extendInfo.put(SmsCostRequestExtendInfoEnum.resultStatus.name(), callRecordInfo.getResultStatus());
            asyncCostRequestBO.setSourceModule(SourceModuleEnum.INTENT_MESSAGE);
            asyncCostRequestBO.setExtendId(intentMessagePO.getSid());
            asyncCostRequestBO.setTenantId(intentMessagePO.getTenantId());
            asyncCostRequestBO.setTradeType(TradeTypeEnum.CHARGE);
            asyncCostRequestBO.setAccount(OwnerTypeEnum.accountFare(templateSendBO.getOwnerType()) ? TenantAccountEnum.ACCOUNT_FARE : TenantAccountEnum.DISTRIBUTOR_ACCOUNT_FARE);
            asyncCostRequestBO.setProductId(templateSendBO.getRealProductId());
            asyncCostRequestBO.setProductCount(billCount);
            asyncCostRequestBO.setExtendInfo(extendInfo);
            if (BooleanUtils.isTrue(intentMessagePO.getIsFree())) {
                //按接通客户挂机短信有免费额度
                asyncBillingService.costWithFreeSmsCount(asyncCostRequestBO);
            } else {
                asyncBillingService.cost(asyncCostRequestBO);
            }
        } else {
            log.info("旧版计费");
            try {
                int freeCount = 0;
                long freeCost = 0L;
                if(BooleanUtils.isTrue(intentMessagePO.getIsFree())){
                    freeCount = 1;
                    freeCost = templateSendBO.getRealSmsPrice();
                    billCount--;
                }
                Long cost = templateSendBO.getRealSmsPrice() * billCount;
                tenantService.reduceSmsCost(tenantPO.getTenantId(),cost);
                smsCostStats(callRecordInfo,templateSendBO,cost,billCount,freeCost,freeCount, tenantPO);
            } catch (Exception e) {
                log.error("[LogHub_Warn]扣除用户短信费用失败 phoneNumber={}", intentMessagePO.getPhoneNumber());
            }
        }
    }

    private void addSmsFinancial(IntentMessagePO intentMessage, SmsTemplateSendBO smsTemplateSend, int billCount, Long tenantId) {
        // 同步新增财务管理元数据
        if (FINANCIAL_GRAY_TEST_ENABLED && !FINANCIAL_GRAY_TEST_TENANTS.contains(tenantId)) {
            log.debug("财务管理开启了灰度测试, 不记录sms_financial");
            return;
        }
        try {
            SmsFinancialPO smsFinancial = new SmsFinancialPO();
            smsFinancial.setTenantId(tenantId);
            smsFinancial.setBusinessType(SmsBusinessTypeEnum.INTENT);
            smsFinancial.setRefId(intentMessage.getIntentMessageId());
            smsFinancial.setIsFree(intentMessage.getIsFree());
            smsFinancial.setSmsChannelId(smsTemplateSend.getSmsPlatformChannelId());
            smsFinancial.setChengbenFare(smsTemplateSend.getSmsProductPrice());
            smsFinancial.setGatewayCostCount(billCount);
            smsFinancial.setChengbenCost(smsTemplateSend.getSmsProductPrice() * billCount);
            smsFinancialService.saveNotNull(smsFinancial);
        } catch (Exception e) {
            log.error("[LogHub_Warn]新增挂机短信计费元数据出错", e);
        }
    }

    private BillingCalloutContext buildBillingContext(CallRecordPO callRecordInfo, TenantPO tenantPO) {
            return new BillingCalloutContext(callRecordInfo.getTenantId(), tenantPO.getTenantPayType(), callRecordInfo.getCreateUserId(),
                    callRecordInfo.getRobotCallJobId(), callRecordInfo.getDialogFlowId(),LocalDateTime.now().getYear(), LocalDateTime.now().getMonthValue(), LocalDateTime.now().getDayOfMonth(),
                    LocalDateTime.now().getHour(), callRecordInfo.getPhoneNumberId(), callRecordInfo.getIntentLevelTagId(), 0L, tenantPO.getComFare(),
                    0L, tenantPO.getDistributorId(), callRecordInfo.getCallOutPlanId(), AccountTypeEnum.isFormal(tenantPO.getAccountType()),
                    tenantPO.getFareVersion(), null, null, null);
        }

    private void smsCostStats(CallRecordPO callRecordPO, SmsTemplateSendBO templateSendBO, Long cost, int billCount, Long freeCost, int freeCount, TenantPO tenantPO) {
        log.debug("计费条数={}, 费用={}, 免费条数={},免费费用={}", billCount, cost, freeCount,freeCost);
        Query callStatsQuery = callStatsService.getCallStatsQuery(buildBillingContext(callRecordPO, tenantPO));
        SmsStatsBO smsStatsBO;
        if (TenantPayTypeEnum.PIECE.equals(tenantPO.getTenantPayType())) {
            //通话结果 闪信的通话结果为null
            Object resultStatusObject = callRecordPO.getResultStatus();
            DialStatusEnum resultStatus = Objects.nonNull(resultStatusObject) ? DialStatusEnum.valueOf(resultStatusObject.toString()) : null;
            //按接通付费 闪信不区分接通和未接通
            smsStatsBO = DialStatusEnum.isAnswered(resultStatus) ?
                    SmsStatsBO.ofAnsweredPiece(templateSendBO.getSmsType(),cost, billCount, freeCost, freeCount)
                    : SmsStatsBO.ofNotAnsweredPiece(templateSendBO.getSmsType(), cost, billCount, freeCost, freeCount);
        } else {
            //订阅制付费, 按分钟付费
            smsStatsBO = SmsStatsBO.ofCommon(templateSendBO.getSmsType(), cost,billCount);
        }
        Update callStatsUpdate = new Update();
        callStatsUpdate.inc("smsPay", cost);
        callStatsUpdate.inc("smsBillAmount", billCount);
        callStatsUpdate.inc("smsCost", cost);
        smsStatsBO.statsInc(callStatsUpdate);
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.CALL_STATS_ROBOT_DONE_JOB, callStatsQuery, callStatsUpdate);
    }



}
