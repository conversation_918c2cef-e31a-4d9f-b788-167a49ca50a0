package com.yiwise.core.service.engine.csseat.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yiwise.aicc.callin.api.model.dto.CallInDetailDTO;
import com.yiwise.aicc.callin.api.model.dto.CallInRecordDTO;
import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.utils.PhoneNumberUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.date.MyDateUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.*;
import com.yiwise.core.dal.dao.*;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.callcost.CallCostDetailBO;
import com.yiwise.core.model.bo.phonenumber.PhoneHomeLocationBO;
import com.yiwise.core.model.bo.user.DialInfoBO;
import com.yiwise.core.model.bo.user.SipAccountBO;
import com.yiwise.core.model.dto.LineStatusDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.*;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.enums.phonenumber.LineStatusEnum;
import com.yiwise.core.model.enums.robotcalljob.CallJobHangupEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.callrecord.*;
import com.yiwise.core.model.vo.cs.CsAndCallInRecordVO;
import com.yiwise.core.model.vo.csseat.*;
import com.yiwise.core.model.vo.customer.CallRecordDetailVO;
import com.yiwise.core.model.vo.ope.CallRecordVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.assistant.AssistantRecordService;
import com.yiwise.core.service.boss.PhoneNumberStatsService;
import com.yiwise.core.service.callin.CallInRecordService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.csseat.*;
import com.yiwise.core.service.engine.phonenumber.FreeswitchInfoService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.openapi.platform.IsvInfoService;
import com.yiwise.core.service.platform.*;
import com.yiwise.core.service.privacynumber.PrivacyNumberContactHistoryService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.util.FreeswitchUtils;
import com.yiwise.core.util.MongoEscapeUtil;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import javaslang.control.Try;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.net.ConnectException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.time.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yiwise.core.model.enums.PhoneTypeEnum.LANDLINE;

/**
 * @Author: wangguomin
 * @Date: 2018-12-28 00:00
 */
@Service
public class CsRecordServiceImpl extends BasicServiceImpl<CsRecordPO> implements CsRecordService {
    private static final Logger logger = LoggerFactory.getLogger(CsRecordServiceImpl.class);

    @Resource(name = "restTemplate")
    private RestTemplate restTemplate;

    @Resource
    private CsRecordPOMapper csRecordPOMapper;

    @Resource
    private CallInRecordPOMapper callInRecordPOMapper;
    @Resource
    private TenantPhoneNumberPOMapper tenantPhoneNumberPOMapper;

    @Resource
    private CsStaffInfoPOMapper csStaffInfoPOMapper;

    @Resource
    private CsStaffUserService csStaffUserService;

    @Resource
    private CallCostService callCostService;

    @Resource
    private CostListService costListService;

    @Resource
    private PhoneNumberPOMapper phoneNumberPOMapper;

    @Resource
    private TenantPhoneNumberService tenantPhoneNumberService;

    @Resource
    private PhoneNumberService phoneNumberService;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    private FreeswitchInfoService freeswitchInfoService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private UserService userService;
    @Resource
    private TenantService tenantService;
    @Resource
    private DistributorService distributorService;
    @Resource
    private CsStaffGroupService csStaffGroupService;
    @Resource
    private CsStaffStatService csStaffStatService;
    @Resource
    private BatchJobInQueueService batchJobInQueueService;
    @Autowired
    @Qualifier("csCallRecordExportJob1")
    private Job csCallRecordExportJob1;
    @Autowired
    @Qualifier("csCallRecordExportJob2")
    private Job csCallRecordExportJob2;
    @Autowired
    @Qualifier("csCallRecordExportJob3")
    private Job csCallRecordExportJob3;
    @Resource
    private CallInRecordService callInRecordService;

    @Autowired
    @Qualifier("csRecordExportJob")
    private Job csRecordExportJob;

    @Resource
    private AssistantRecordService assistantRecordService;
    @Resource
    private PhoneLocationService phoneLocationService;
    @Resource
    private TenantAutoSetService tenantAutoSetService;
    @Resource
    private IsvInfoService isvInfoService;
    @Resource
    private CsCallBackService csCallBackService;
    @Resource
    private IntentLevelTagDetailService intentLevelTagDetailService;
    @Resource
    private PhoneNumberStatsService phoneNumberStatsService;
    @Resource
    private CsStaffInfoService csStaffInfoService;
    @Resource
    private CsDetailService csDetailService;

    @Resource
    private RoleService roleService;

    @Resource
    private CsBatchCallJobService csBatchCallJobService;
    @Resource
    private CustomerWhiteListService customerWhiteListService;
    @Resource
    private PhoneCardService phoneCardService;
    @Resource
    SharePolicyService sharePolicyService;
    @Resource
    ShareWhiteListService shareWhiteListService;
    @Resource
    private PrivacyNumberContactHistoryService privacyNumberContactHistoryService;

    @Override
    public Long createCsRecord(CsRecordVO csRecordVO) {
        CsRecordPO csRecordPO = new CsRecordPO();
        csRecordPO.setTenantId(csRecordVO.getTenantId());
        csRecordPO.setDistributorId(csRecordVO.getDistributorId());
        csRecordPO.setCustomerPersonId(0L);
        csRecordPO.setFreeswitchInfoId(csRecordVO.getFreeswitchInfoId());
        csRecordPO.setCalledPhoneNumber(csRecordVO.getCalledPhoneNumber());

        csRecordPO.setPhoneNumberId(csRecordVO.getPhoneNumberId());
        TenantPhoneNumberPO tpn = tenantPhoneNumberPOMapper.selectOneTenantPhoneNumberByPhoneNumberAndTenantId(csRecordVO.getTenantId(), csRecordVO.getPhoneNumberId());
        if(tpn != null) {
            csRecordPO.setTenantPhoneNumberId(tpn.getTenantPhoneNumberId());
        } else {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "无权限分配此线路");
        }
        csRecordPO.setResultStatus(DialStatusEnum.CAN_NOT_CONNECT);
        csRecordPO.setSeatStatus(DialStatusEnum.CAN_NOT_CONNECT);
        csRecordPO.setCallerPhone(csRecordVO.getCallerPhone());
        csRecordPO.setCallUserId(csRecordVO.getCallUserId());
        if(!csRecordVO.getBatchJob()){
            CsStaffInfoPO csStaffInfoPO = csStaffInfoPOMapper.selectVoiceByUserId(csRecordVO.getCallUserId());
            if (csStaffInfoPO != null) {
                csRecordPO.setCsStaffId(csStaffInfoPO.getCsStaffId());
                csRecordPO.setCsStaffName(csStaffInfoPO.getCsName());
            }
        }
        if(csRecordPO.getCsStaffId() == null && csRecordVO.getCsStaffId() != null){
            //兼容批量外呼的情况
            CsStaffInfoPO csStaffInfoPO = csStaffInfoPOMapper.selectByPrimaryKey(csRecordVO.getCsStaffId());
            if(csStaffInfoPO != null){
                csRecordPO.setCsStaffId(csStaffInfoPO.getCsStaffId());
                csRecordPO.setCsStaffName(csStaffInfoPO.getCsName());
            }
        }
        PhoneNumberPO phoneNumberPO = phoneNumberPOMapper.selectByPrimaryKey(csRecordPO.getPhoneNumberId());
        Boolean isLocal = phoneNumberService.isLocalWithCallerAndCalledPhoneNumber(csRecordPO.getCalledPhoneNumber(), phoneNumberPO);
        csRecordPO.setCallType(isLocal ? CallTypeEnum.LOCAL : CallTypeEnum.LONG);
        csRecordPO.setPhoneLocationId(csRecordVO.getPhoneLocationId());
        csRecordPO.setEnabledStatus(EnabledStatusEnum.ENABLE);
        csRecordPO.setCreateUserId(csRecordVO.getCallUserId());
        csRecordPO.setUpdateUserId(csRecordVO.getCallUserId());
        csRecordPO.setCsStaffGroupId(csRecordVO.getCsStaffGroupId());
        csRecordPO.setCsBatchCallJobRecordId(csRecordVO.getCsBatchCallJobRecordId());
        csRecordPO.setCsBatchCallJobId(csRecordVO.getCsBatchCallJobId());
        csRecordPO.setStartTime(LocalDateTime.now());
        csRecordPOMapper.insertSelective(csRecordPO);

        //往redis加正在外呼的坐席数
        if(!csRecordVO.getBatchJob()){
            String callOutCsCount = RedisKeyCenter.getCallOutCsCount();
            redisOpsService.incrementKey(callOutCsCount);
            redisOpsService.expire(callOutCsCount, 1, TimeUnit.DAYS);
        }
        return csRecordPO.getCsRecordId();
    }


    @Override
    public void updateCsRecordOnHangup(CsRecordVO csRecordVO) {
        Long callCost = 0L;
        Date endTime = new Date();
        CsRecordPO updatePO = new CsRecordPO();
        CsRecordPO csRecordPO = csRecordPOMapper.selectByPrimaryKey(csRecordVO.getCsRecordId());
        if (csRecordPO == null) {
            return ;
        }
        DialInfoBO dialInfoBO = csStaffUserService.getCsDialInfo(csRecordPO.getTenantId(), csRecordPO.getCallUserId(), csRecordPO.getPhoneNumberId(), csRecordPO.getCalledPhoneNumber());
        updatePO.setCsRecordId(csRecordVO.getCsRecordId());
        //初始化
        updatePO.setHangupBy(csRecordVO.getHangupBy());
        updatePO.setEndTime(MyDateUtils.toLocalDateTime(endTime));
        updatePO.setStartTime(csRecordPO.getStartTime());
        updatePO.setChatDuration(0L);
        if (DialStatusEnum.ANSWERED.equals(csRecordVO.getResultStatus())) {
            updatePO.setResultStatus(csRecordVO.getResultStatus());
            updatePO.setSeatStatus(csRecordVO.getResultStatus());
        }else {
            updatePO.setResultStatus(csRecordVO.getResultStatus());
            updatePO.setSeatStatus(csRecordVO.getResultStatus());
            updatePO.setHangupBy(CallJobHangupEnum.OTHER_HANGUP);
        }
        //信令有无返回183
        if(Objects.nonNull(csRecordVO.getComment())){
            updatePO.setComment(csRecordVO.getComment());
        }
        updatePO.setCallCost(callCost);
        // 到freeswitch上拉录音文件，计算通话时长相关的信息
        List<TransferCallVO> list = null;
	    TenantPO tenant = tenantService.selectByKey(csRecordPO.getTenantId());
	    Long distributorId = tenant == null ? CommonApplicationConstant.OPE_DISTRIBUTOR_ID : tenant.getDistributorId();
	    phoneNumberStatsService.phoneNumberCallDayStats(csRecordPO.getPhoneNumberId(), csRecordPO.getTenantId(), distributorId);
        if (DialStatusEnum.ANSWERED.equals(csRecordVO.getResultStatus())) {
            SipAccountBO sipAccountBO = getSipAccountByStaffOrBatchJob(csRecordVO, csRecordPO.getFreeswitchInfoId(), csRecordPO.getPhoneNumberId());
            if (sipAccountBO != null) {
                // 获取freeswitch的地址
                // 获取通话录音到本地
                String queryRecordFsHostIp = sipAccountBO.getFreeswitchHost();
                int audioDownloadPort = sipAccountBO.getAudioDownloadPort();
                String caller = sipAccountBO.getSipAccount();
                String prefixMobile = dialInfoBO.getPrefixMobile();
                String nginxUrl = FreeswitchUtils.generateCsSeatCallRecordNginxUrl(queryRecordFsHostIp, audioDownloadPort, caller, prefixMobile);
                logger.info("csInstanceId=" + csRecordPO.getCsRecordId() + ": nginxUrl=" + nginxUrl);
                String localFileName = "/tmp/csRecord/call-record-" + caller + "-" + prefixMobile + ".wav";
                FreeswitchUtils.downloadFromFs(restTemplate, nginxUrl, localFileName);
                File f = new File(localFileName);
                if (f.exists()) {
                    updatePO.setResultStatus(DialStatusEnum.ANSWERED);
                    // 上传到OSS 【通话记录id】-【姓名】-【实际外呼的联系电话(若为副号码则展示副号码）】;
                    String fileNamePrefix = csRecordPO.getCsRecordId() + "-" + (StringUtils.isEmpty(csRecordPO.getCustomerPersonName()) ? "无" : csRecordPO.getCustomerPersonName()) + "-" + csRecordPO.getCalledPhoneNumber() + "-" + DateFormatUtils.format(new Date(), "YYYYMMDDHHmmss");
                    String fileName = FreeswitchUtils.generateFileName(fileNamePrefix);
                    String identifyDir = "CsPhoneCommunicate" + File.separator + "TenantId"+ csRecordPO.getTenantId();
                    String fileKey = identifyDir.concat(File.separator).concat(fileName);
                    FreeswitchInfoPO freeswitchInfoPO = new FreeswitchInfoPO();
                    freeswitchInfoPO.setAudioDownloadPort(audioDownloadPort);
                    freeswitchInfoPO.setHost(queryRecordFsHostIp);
                    list = getTransferToCsFile(f, String.valueOf(csRecordVO.getCsRecordId()), freeswitchInfoPO);
                    String wavFileUrl = null;
                    try {
                        wavFileUrl = objectStorageHelper.upload(fileKey, f);
                        logger.debug("csInstanceId=" + csRecordPO.getCsRecordId() + ": 上传结束, wavFileUrl=" + wavFileUrl);
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                    updatePO.setFullAudioUrl(wavFileUrl);
                    //先判断这个是不是双声道的录音，如果是的话，则转成单声道存入 fullAudioUrl 另外 双声道存入 twoChannelAudioUrl
                    int c= soundChannel(f);
                    if(c == 2){
                        updatePO.setTwoChannelAudioUrl(wavFileUrl);
                        //转成单声道
                        try {
                            String channelFileName = "/tmp/csRecord/call-record-" + caller + "-" + prefixMobile + "-channel.wav";
                            AudioHandleUtils.convertChannelNum(localFileName, channelFileName, 1);
                            fileNamePrefix = fileNamePrefix + "-channel";
                            fileName = FreeswitchUtils.generateFileName(fileNamePrefix);
                            fileKey = identifyDir.concat(File.separator).concat(fileName);
                            String fullAudioUrl = objectStorageHelper.upload(fileKey, new File(channelFileName));
                            updatePO.setFullAudioUrl(fullAudioUrl);
                        }catch (Exception e){
                            logger.error("双声道转单身道出现问题", e);
                        }
                    }
                    // 获得音频时长
                    Integer duration = AudioHandleUtils.getAudioFileDuration(f);
                    Date startTime = org.apache.commons.lang.time.DateUtils.addSeconds(endTime, -duration);
                    updatePO.setEndTime(MyDateUtils.toLocalDateTime(endTime));
                    updatePO.setStartTime(MyDateUtils.toLocalDateTime((startTime)));
                    updatePO.setChatDuration(Long.valueOf(duration));
                    updatePO.setTotalDuration(Long.valueOf(duration));
                    updatePO.setCsAnswer(YesOrNoEnum.YES);

                    //计费
                    callCost = csCallCost(csRecordVO.getTenantId(), csRecordPO.getCalledPhoneNumber(), updatePO.getStartTime(), updatePO.getChatDuration(), csRecordPO.getCsRecordId(), csRecordPO.getTenantPhoneNumberId(), csRecordPO.getPhoneNumberId(), csRecordVO.getCallUserId());
                    updatePO.setCallCost(callCost);

                    assistantRecordService.relevantRecord(updatePO.getCsRecordId(), CallInstanceTypeEnum.CS_SEAT_RECORD, csRecordPO.getTenantId(), csRecordPO.getCsStaffId(), "CS" + csRecordPO.getCsRecordId());
                }
            }
            phoneNumberStatsService.phoneNumberCallDayAnswerStats(csRecordPO.getPhoneNumberId(), csRecordPO.getTenantId(), distributorId);
        }else {
            updatePO.setFullAudioUrl(csRecordVO.getEarlyMediaUrl());
        }
        csRecordPOMapper.updateByPrimaryKeySelective(updatePO);
        try {
            CsRecordPO dbRecord = csRecordPOMapper.selectByPrimaryKey(updatePO.getCsRecordId());
            callInRecordService.addTransferToCsRecordListFromCsCallOut(csRecordPO.getCsRecordId(), dbRecord.getCalledPhoneNumber(), CsCallCategoryEnum.CS_CALL_OUT_TRANSFER, dbRecord, list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        doAfterStat(csRecordPO.getCsRecordId(), csRecordPO.getTenantPhoneNumberId(), csRecordPO.getTenantId(), distributorId,
		        csRecordPO.getCallUserId(), updatePO.getStartTime() == null ? csRecordPO.getCreateTime() : updatePO.getStartTime(), callCost, csRecordVO.getResultStatus(), null, updatePO.getChatDuration(), csRecordPO.getCallType(), csRecordPO.getPhoneNumberId());
        if(updatePO.getChatDuration() > 0) {
            Long answeredCount = Objects.equals(updatePO.getResultStatus(),DialStatusEnum.ANSWERED) ? 1L : 0L;
            Long effectiveCallOutCount = Objects.equals(updatePO.getSeatStatus(),DialStatusEnum.ANSWERED) ? 1L : 0L;
            Long effectiveChatDuration = Objects.equals(updatePO.getSeatStatus(),DialStatusEnum.ANSWERED) ? updatePO.getChatDuration() : 0L;
            csStaffStatService.staffStatCallOut(csRecordPO.getCallUserId(), csRecordPO.getTenantId(), csRecordPO.getCsStaffId(),1L,answeredCount,effectiveCallOutCount, effectiveChatDuration);
        } else {
            csStaffStatService.staffStatCallOut(csRecordPO.getCallUserId(), csRecordPO.getTenantId(), csRecordPO.getCsStaffId(),1L,0L,0L, 0L);
        }
        csCallback(csRecordPO, updatePO);
        String callOutCount = redisOpsService.get(RedisKeyCenter.getCallOutCsCount());
        if (StringUtils.isNotEmpty(callOutCount)) {
            Integer callCount = Integer.parseInt(callOutCount);
            if(callCount > 0){
                //正在外呼的坐席数-1
                String callOutCsCount = RedisKeyCenter.getCallOutCsCount();
                redisOpsService.incrementKey(callOutCsCount,-1);
            }
        }
        //redis添加人工外呼时间
        try {
            String tenantLastCsCallOutTime = RedisKeyCenter.getTenantCsLastCallOutTime(csRecordVO.getTenantId());
            redisOpsService.set(tenantLastCsCallOutTime, LocalDateTime.now().toString(), 7, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("修改客户最后人工外呼时间错误");
        }
    }

    /**
     * 检查未接原因
     * @param csCheckCallOutVO
     */
    @Override
    public void checkLineLimitBeforeCallOut(CsCheckCallOutVO csCheckCallOutVO){
        //先校验OPE上设置的外呼时间限制
        Boolean flag = tenantService.checkCsDailyCallTime(csCheckCallOutVO.getTenantId());
        if(!flag){
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "当前非可外呼时间，无法外呼");
        }

        //校验黑名单
        checkBlackList(csCheckCallOutVO);

        PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKey(csCheckCallOutVO.getPhoneNumberId());
        //1.检查手机号归属地  是否为外呼盲区
        PhoneHomeLocationBO locationOrNewByPhoneNumber = phoneLocationService.getLocationOrNewByPhoneNumber(csCheckCallOutVO.getCalledPhoneNumber());
//        if(Objects.isNull(locationOrNewByPhoneNumber)){
//            throw new ComException(ComErrorCode.VALIDATE_ERROR, "手机号码格式不对，请重新输入");
//        }
        List<AreaPO> areaPOS =  phoneNumberPO.getDeadArea();
        if(CollectionUtils.isNotEmpty(areaPOS) && Objects.nonNull(locationOrNewByPhoneNumber)){
            areaPOS.stream().forEach(areaPO -> {
                if(Objects.nonNull(areaPO.getCity()) && Objects.nonNull(areaPO.getProv()) &&
                   Objects.nonNull(locationOrNewByPhoneNumber.getCity()) && Objects.nonNull(locationOrNewByPhoneNumber.getProv())){
                    if(areaPO.getCity().contains(locationOrNewByPhoneNumber.getCity())
                            && areaPO.getProv().contains(locationOrNewByPhoneNumber.getProv())){
                        throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "该号码在外呼盲区内，无法外呼");
                    }
                }
            });
        }

        //3.检查运营商限制
        Integer mobilePhoneEnum = PhoneNumberUtils.isChinaMobilePhoneNum(csCheckCallOutVO.getCalledPhoneNumber());

        if(phoneNumberPO.getOperatorRestriction()){
            boolean mobilePhoneLimit = false;
            List<MobileOperatorEnum> mobileOperatorEnums = phoneNumberPO.getMobileOperator();
            for(MobileOperatorEnum mobileOperatorEnum : mobileOperatorEnums){
                if(Objects.equals(mobileOperatorEnum.getCode(),mobilePhoneEnum)){
                    mobilePhoneLimit = true;
                }
            }
            if(!mobilePhoneLimit){
                throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "该号码的运营商被限制，无法外呼");
            }
        }

        //校验线路是否故障
        LineStatusDTO lineStatus = phoneCardService.setStatus(csCheckCallOutVO.getTenantId(), csCheckCallOutVO.getPhoneNumberId());
        if(Objects.isNull(lineStatus.getStatus()) | LineStatusEnum.isBreakdown(lineStatus.getStatus())){
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "线路故障，无法外呼");
        }
    }

    private void checkBlackList(CsCheckCallOutVO csCheckCallOutVO){
        CsStaffInfoPO csStaffInfoPO = csStaffInfoService.queryVoiceByUserId(csCheckCallOutVO.getTenantId(),csCheckCallOutVO.getUserId());
        List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByStaffIdAndStatus(csStaffInfoPO.getCsStaffId(), EnabledStatusEnum.ENABLE);
        List<Long> groupIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(groupPOList)){
            groupPOList.stream().forEach(csStaffGroupPO -> {
                groupIds.addAll(csStaffGroupPO.getCustomerWhiteGroupIdList());
            });
        }
        if(CollectionUtils.isNotEmpty(groupIds)){
            Set<String> filterNumbers = customerWhiteListService.selectFilterNumberByGroupIds(csCheckCallOutVO.getTenantId(),groupIds, csCheckCallOutVO.getCalledPhoneNumber());
            if(CollectionUtils.isNotEmpty(filterNumbers)){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "该号码在外呼黑名单内，无法外呼");
            }
        }

        //校验OPE共享黑名单
        Set<SharePolicyPO> sharePolicyPOSet = sharePolicyService.getPolicyListByTenantId(csCheckCallOutVO.getTenantId());
        if(CollectionUtils.isNotEmpty(sharePolicyPOSet)){
            Set<Long> policyIds = sharePolicyPOSet.stream().map(SharePolicyPO::getSharePolicyId).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(policyIds)){
                List<String> shareWhiteList = shareWhiteListService.selectPhoneNumberByPolicyIds(policyIds, Arrays.asList(csCheckCallOutVO.getCalledPhoneNumber()));
                if(CollectionUtils.isNotEmpty(shareWhiteList)){
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "该号码外呼风险高，无法外呼");
                }
            }
        }
    }

    private void csCallback(CsRecordPO csRecordPO, CsRecordPO update){
        try {
            IsvInfoPO isvInfoPO = isvInfoService.findByTenantId(csRecordPO.getTenantId());
            if(isvInfoPO != null && StringUtils.isNotEmpty(isvInfoPO.getCallbackUrl())){
                TenantPO tenantPO = tenantService.getTenantByTenantId(csRecordPO.getTenantId());
                CsCallBackBO csCallBackBO = new CsCallBackBO();
                csCallBackBO.setChatDuration(update.getChatDuration());
                csCallBackBO.setCsRecordId(csRecordPO.getCsRecordId());
                csCallBackBO.setCsStaffId(csRecordPO.getCsStaffId());
                csCallBackBO.setCsStaffName(csRecordPO.getCsStaffName());
                csCallBackBO.setCustomerPersonId(csRecordPO.getCustomerPersonId());
                csCallBackBO.setCustomerPersonName(csRecordPO.getCustomerPersonName());
                csCallBackBO.setCustomerPersonNumber(csRecordPO.getCalledPhoneNumber());
                csCallBackBO.setEndTime(update.getEndTime());
                csCallBackBO.setFullAudioUrl(update.getFullAudioUrl());
                csCallBackBO.setHangupBy(update.getHangupBy());
                csCallBackBO.setPhoneNumberId(update.getPhoneNumberId());
                csCallBackBO.setResultStatus(update.getResultStatus());
                csCallBackBO.setSeatStatus(update.getSeatStatus());
                csCallBackBO.setStartTime(update.getStartTime());
                csCallBackBO.setTenantId(csRecordPO.getTenantId());
                csCallBackService.doCallBack(isvInfoPO, tenantPO, csCallBackBO);
            }
        }catch (Exception e){
            logger.error("csCallback", e);
        }
    }

    @Override
    public void updateRecordOnHangupForBatchJob(CsRecordVO csRecordVO) {
        if(Objects.nonNull(csRecordVO.getHangupBy())){
            String key = RedisKeyCenter.getCsBatchHangUpKey(csRecordVO.getCsRecordId());
            redisOpsService.set(key, csRecordVO.getHangupBy().name(), 30, TimeUnit.MINUTES);
        }
        if(Objects.nonNull(csRecordVO.getCsRecordId())){
            CsRecordPO csRecordPO = this.selectByKey(csRecordVO.getCsRecordId());
            logger.info("csRecordPO = " + csRecordPO.toString());
            String batchJobCsKey = RedisKeyCenter.getBatchJobCsCount(csRecordPO.getCsBatchCallJobId());
            String batchJobCsCount = redisOpsService.get(batchJobCsKey);
            if (StringUtils.isNotEmpty(batchJobCsCount)) {
                Integer batchCount = Integer.parseInt(batchJobCsCount);
                if(batchCount > 0){
                    logger.info("batchJobCsCount = " + batchJobCsKey);
                    redisOpsService.incrementKey(batchJobCsKey,-1);
                }
            }
        }
    }

    /**
     * 获取声道, 1-单声道；2-双声道, 异常
     * @param f
     * @return
     */
    public short soundChannel(File f){
        RandomAccessFile rdf = null;
        try {
            rdf = new RandomAccessFile(f, "r");
            return toShort(read(rdf, 22, 2));
        }catch (Exception e){
            logger.error("soundChannel :" , e);
        } finally {
            if(rdf != null){
                try {
                    rdf.close();
                }catch (Exception e){
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return 0;
    }

    public static short toShort(byte[] b) {
        return (short) ((b[1] << 8) + (b[0] << 0));
    }

    public static byte[] read(RandomAccessFile rdf, int pos, int length) throws IOException {
        rdf.seek(pos);
        byte result[] = new byte[length];
        for (int i = 0; i < length; i++) {
            result[i] = rdf.readByte();
        }
        return result;
    }

    public List<TransferCallVO> getTransferToCsFile(File fromFile, String callInRecordingString, FreeswitchInfoPO freeswitchInfo){
        if(fromFile == null || fromFile.length() < ApplicationConstant.EMPTY_WAV_FILE_LENGTH){
            return null;
        }
        File transferFile = null;
        boolean transfer = false;
        List<TransferCallVO> res = new ArrayList<>();
        try{
            String key = RedisKeyCenter.getTransferCallKey(callInRecordingString);
            logger.info("transferCall:" + key);
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFSDownloadTempFilePath(callInRecordingString + "-transfer-to-cs-record");
            transferFile = new File(fsDownloadTempFilePath);
            if (!transferFile.exists()) {
                transferFile.getParentFile().mkdirs();
                transferFile.createNewFile();
            }
            if(StringUtils.isNoneEmpty(key)){
                String pop = (String)redisOpsService.getRedisTemplate().opsForList().leftPop(key);
                logger.info("pop-data:" + pop);
                if(StringUtils.isNoneEmpty(pop)) {
                    while (StringUtils.isNoneEmpty(pop)) {
                        transfer = true;
                        try {
                            logger.info("pop 转接数据:" + pop);
                            TransferCallVO vo = JSONObject.parseObject(pop, TransferCallVO.class);
                            res.add(vo);
                            if (vo != null) {
                                File file = getTransferFile(freeswitchInfo.getHost(), freeswitchInfo.getAudioDownloadPort(), "transfer-to-cs-record-" + callInRecordingString + "-" + vo.getCurrentCsStaffId() + "-" + vo.getIndex(), freeswitchInfo.getName(), callInRecordingString);
                                if(file != null) {
                                    if (transferFile.length() < ApplicationConstant.EMPTY_WAV_FILE_LENGTH) {
                                        Files.copy(file.toPath(), transferFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                                    } else {
                                        if (file != null) {
                                            AudioHandleUtils.merge2Wav(transferFile.getAbsolutePath(), file.getAbsolutePath(), transferFile.getAbsolutePath());
                                            transferFile = new File(transferFile.getAbsolutePath());
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.error("获取文件失败", e);
                        }
                        pop = (String) redisOpsService.getRedisTemplate().opsForList().leftPop(key);
                    }
                }
            }
        }catch (Exception e){
            logger.error("获取转接文件失败", e);
        }
        if(transfer){
            try{
                AudioHandleUtils.merge2Wav(fromFile.getAbsolutePath(), transferFile.getAbsolutePath(), fromFile.getAbsolutePath());
            }catch (Exception e){
                logger.error("获取文件失败", e);
            }
            return res;
        }
        return null;
    }

    private File getTransferFile(String fsHost, Integer fsAudioDownloadPort, String fileName, String fsName, String identifyId) {
        File transferFile = null;
        try {
            // 获取录音文件前延迟1s 防止获取空文件
            Thread.sleep(1000);
            String fsSourceFileUrl = "http://" + fsHost + ":" + fsAudioDownloadPort + "/record/" + fileName + ".wav";
            String fsDownloadTempFilePath = TempFilePathKeyCenter.getFSDownloadTempFilePath(identifyId);
            transferFile = new File(fsDownloadTempFilePath);
            if (!transferFile.exists()) {
                transferFile.getParentFile().mkdirs();
                transferFile.createNewFile();
            }
            long lastLength = 0L;
            long thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
            //
            while (lastLength < thisLength) {
                Thread.sleep(3000);
                logger.info("转接未完成,lastLength={},thisLength={}", lastLength, thisLength);
                lastLength = thisLength;
                thisLength = MyFileUtils.getRemoteFileLength(fsSourceFileUrl);
            }
            // 下载文件
            MyFileUtils.downloadFromUrl(fsSourceFileUrl, transferFile);
        } catch (Exception e) {
            if (e instanceof ConnectException) {
                logger.error("[LogHub_Warn] " + fsName + "nginx连接失败 " + fsHost + ":" + fsAudioDownloadPort + "拒绝连接", e);
            }
            // 文件置空 防止合并文件出错
            transferFile = null;
            logger.error("检查并获取转人工录音失败", e);
        }
        return transferFile;
    }

    public SipAccountBO getSipAccountByStaffOrBatchJob(CsRecordVO csRecordVO, Long fsId, Long phoneNumberId) {
        if (csRecordVO.getBatchJob()) {
            //获取线路所在的fs
            FreeswitchInfoPO freeswitchInfoPO = freeswitchInfoService.selectByKey(fsId);
            PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKey(phoneNumberId);
            SipAccountBO sipAccountBO = new SipAccountBO();
            sipAccountBO.setSipAccount(phoneNumberPO.getSipAccount());
            sipAccountBO.setFreeswitchHost(freeswitchInfoPO.getHost());
            sipAccountBO.setFreeswitchPort(freeswitchInfoPO.getPort());
            sipAccountBO.setAudioDownloadPort(freeswitchInfoPO.getAudioDownloadPort());
            return sipAccountBO;
        } else {
            return csStaffUserService.getCsStaffByUserId(csRecordVO.getCallUserId());
        }
    }

    @Override
    public void doAfterStat(Long csRecordId, Long tenantPhoneNumberId, Long tenantId, Long distributorId, Long userId, LocalDateTime currDateTime, Long callCost,
                            DialStatusEnum resultStatus, Set<String> customerConcernSet, Long chatTime, CallTypeEnum local, Long phoneNumberId) {
        // 数据统计
        // 当前年
        int year = currDateTime.getYear();
        // 当前月
        int month = currDateTime.getMonthValue();
        // 当前日
        int day = currDateTime.getDayOfMonth();
        // 当前小时
        int hour = currDateTime.getHour();
        try {
            Update update = new Update();
            // 通话状态统计
            update.inc("callStatus." + resultStatus.name(), 1);
            // task完成量统计
            update.inc("taskTotalCompleted", 1);

            // 呼叫状态=已接听
            if (DialStatusEnum.ANSWERED.equals(resultStatus)) {
                // 客户关注点统计
                if (CollectionUtils.isNotEmpty(customerConcernSet)) {
                    List<String> customerConcernList = Lists.newArrayList(customerConcernSet);
                    for (String customerConcern : customerConcernList) {
                        update.inc("customerConcern." + MongoEscapeUtil.escapePoint(customerConcern), 1);
                    }
                }
                //拨通数统计
                update.inc("answeredCall", 1);
                update.inc("totalAnsweredCall", 1);
                // 通话时长统计(按照秒)
                // 计费时长
                chatTime = chatTime == null ? 0 : chatTime;
                update.inc("chatTime", chatTime);
                TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectByKey(tenantPhoneNumberId);
                Long billChatDuration = (tenantPhoneNumberPO.getBillPeriod() == null || tenantPhoneNumberPO.getBillPeriod() == 0) ? 0 :
                        (chatTime + tenantPhoneNumberPO.getBillPeriod() - 1) / tenantPhoneNumberPO.getBillPeriod();
                update.inc("billChatTime", billChatDuration);
                // 通话时长区间(按照分)
                update.inc("chatDuration." + ChatDurationEnum.getChatDurationEnum(chatTime).name(), 1);
                // 通话计费
                update.inc("callCost", callCost);
            }
            // 任务统计
            callStatsMongoService.updateMongoData(MongoCollectionNameCenter.CS_CALL_STATS_ROBOT, getCsCallStatsQuery(tenantId, distributorId, userId,
                    year, month, day, hour, local == CallTypeEnum.LOCAL, phoneNumberId), update);
        } catch (Exception e) {
            logger.error("执行人工外呼任务数据统计出错, csRecordId={}.", csRecordId, e);
        }
    }

    public Query getCsCallStatsQuery(Long tenantId, Long distributorId, Long userId, int year,
                                     int month, int day, int hour, boolean local, Long phoneNumberId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("distributorId").is(distributorId));
        query.addCriteria(Criteria.where("userId").is(userId));
        query.addCriteria(Criteria.where("local").is(local));
        query.addCriteria(Criteria.where("phoneNumberId").is(phoneNumberId));
        query.addCriteria(Criteria.where("year").is(year));
        query.addCriteria(Criteria.where("month").is(month));
        query.addCriteria(Criteria.where("day").is(day));
        query.addCriteria(Criteria.where("hour").is(hour));
        query.addCriteria(Criteria.where("localDateTime").is(LocalDateTime.of(year, month, day, hour, 0)));
        return query;
    }


    @Override
    public Long csCallCost(Long tenantId, String calledPhoneNumber, LocalDateTime startTime, Long chatDuration, Long csRecordId, Long tenantPhoneNumberId, Long phoneNumberId, Long userId) {
        PhoneNumberPO phoneNumberPO = phoneNumberPOMapper.selectByPrimaryKey(phoneNumberId);
        Boolean isLocal = phoneNumberService.isLocalWithCallerAndCalledPhoneNumber(calledPhoneNumber, phoneNumberPO);
        TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectByKey(tenantPhoneNumberId);
        //计算费用
        Long callCost = callCostService.computeCallCost(tenantPhoneNumberId, phoneNumberId, calledPhoneNumber, chatDuration);

        Try.run(() -> {
            if (callCost > 0) {
                //添加call_cost记录
                CallCostPO callCostInfo = new CallCostPO();
                callCostInfo.setTenantId(tenantId);
                callCostInfo.setCalledPhoneNumber(calledPhoneNumber);
                callCostInfo.setStartTime(startTime);
                callCostInfo.setChatDuration(chatDuration);
                callCostInfo.setSmsCount(0);
                callCostInfo.setCallCost(callCost);
                callCostInfo.setSmsCost(0L);
                callCostInfo.setTotalCost(callCost);
                callCostInfo.setCallRecordId(csRecordId);
                callCostInfo.setCallCostType(CallCostTypeEnum.CS_SEAT_ROBOT);
                callCostService.addCallCostInfo(callCostInfo);

                CostListPO costList = new CostListPO();
                costList.setCallCostId(callCostInfo.getCallCostId());
                costList.setCallRecordId(csRecordId);
                costList.setCostType(isLocal ? CostTypeEnum.LOCAL_CALL : CostTypeEnum.LONG_CALL);
                costList.setCount(chatDuration);
                costList.setFare(callCost);
                costList.setCostListType(CostListTypeEnum.CS_SEAT_ROBOT);
                costList.setCreateUserId(userId);
                costList.setTenantId(tenantId);
                costList.setPhoneNumberId(phoneNumberId);
                costListService.saveNotNull(costList);
            }
        }).onFailure(e -> logger.error("添加通话流水记录错误", e));

        //进行扣费
        // 扣除电话费用
        if (callCost > 0 && needFeeCharging(tenantPhoneNumberPO.getLocalBillRate(), tenantPhoneNumberPO.getOtherBillRate(), phoneNumberPO.getPhoneType(), phoneNumberPO.getOwnerTenantId())) {
            tenantAutoSetService.tenantAutoPay(tenantId,phoneNumberId,TenantAutoSetTypeEnum.Line);
            Try.run(() -> tenantPhoneNumberService.reduceCallCost(tenantPhoneNumberId, callCost))
                    .onFailure(e -> logger.error("[LogHub_Warn]扣除用户电话费用失败 cs_record_id={}", csRecordId));
        }
        return callCost;
    }

    public boolean needFeeCharging(Long localBillRate, Long otherBillRate, PhoneTypeEnum phoneType, Long ownerTenantId) {
        if (localBillRate == 0 && otherBillRate == 0) {
            return false;
        }
        return Stream.of(LANDLINE).anyMatch(item -> Objects.equals(phoneType, item)) && ownerTenantId == 0;
    }

    @Override
    public PageResultObject<CsRecordVO> searchTenantCsRecord(Long tenantId, Long customerPersonId, Long csStaffId, Integer readStatus, DialStatusEnum resultStatus, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<CsRecordVO> list = csRecordPOMapper.searchTenantCsRecord(tenantId, customerPersonId, csStaffId, readStatus, resultStatus.getCode());
        packageCsStaffInfo(list);
        return PageResultObject.of(list);
    }

    @Override
    public CsRecordVO getCsRecordDetail(Long csRecordId) {
        CsRecordVO csRecordVO = csRecordPOMapper.getCsRecordDetail(csRecordId);
        if(Objects.nonNull(csRecordVO.getCsStaffId())){
            CsStaffInfoPO csStaffInfoPO = csStaffInfoService.selectByKey(csRecordVO.getCsStaffId());
            if(Objects.nonNull(csStaffInfoPO)){
                csRecordVO.setCsMobile(csStaffInfoPO.getCsMobile());
                csRecordVO.setCsName(csStaffInfoPO.getCsName());
            }
        }
        return csRecordVO;
    }

    @Override
    public List<CsRecordPO> getCustomerPersonCsRecord(Long tenantId, Long customerPersonId) {
        return csRecordPOMapper.getCustomerPersonCsRecord(tenantId, customerPersonId);
    }

    private void packageCsStaffInfo(List<CsRecordVO> list){
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> csStaffIdList = list.stream().map(CsRecordVO::getCsStaffId).collect(Collectors.toList());
            List<CsStaffInfoPO> csStaffInfoPOList = csStaffInfoService.selectByIdList(csStaffIdList);
            Map<Long, CsStaffInfoPO> csStaffInfoPOMap = csStaffInfoPOList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, d -> d));
            list.stream().forEach(item ->{
                if (csStaffInfoPOMap.containsKey(item.getCsStaffId())) {
                    CsStaffInfoPO csStaffInfoPO = csStaffInfoPOMap.get(item.getCsStaffId());
                    item.setCsName(csStaffInfoPO.getCsName());
                    item.setCsMobile(csStaffInfoPO.getCsMobile());
                }
                if(item.getCsStaffId() == null || item.getCsStaffId() == 0L){
                    item.setCsName(item.getCsStaffName());
                }
            });
        }
    }

    @Override
    public Integer countCsRecordByTenantAndPhoneNumberIdAndDate(Long tenantId, Long phoneNumberId, Boolean local, LocalDate startDate, LocalDate endDate) {
        return csRecordPOMapper.countCsRecordByTenantAndPhoneNumberIdAndDate(tenantId, phoneNumberId, local, startDate, endDate);
    }

//    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCallRecordIntentLevel(Long tenantId, Long csRecordId, Integer intentLevel,Long intentLevelTagId) {
        CsRecordPO csRecord = selectByKeyOrThrow(csRecordId);
        if (!csRecord.getTenantId().equals(tenantId)) {
            throw new ComException(ComErrorCode.FORBIDDEN, "无权访问此通话记录", "访问csRecordId:" + csRecordId + "，所属当前用户tenantId:" + csRecord.getTenantId() + ", 当前用户tenantId:" + tenantId);
        }
        // 更新mongo统计
        updateCallStatIntentLevel(csRecord, intentLevel);
        // 更新数据库的意向
        csRecordPOMapper.updateCsRecordIntentLevel(tenantId, csRecordId, intentLevel,intentLevelTagId);
    }

    public void updateCallStatIntentLevel(CsRecordPO csRecord, Integer intentLevel) {
        PhoneNumberPO phoneNumberPO = phoneNumberPOMapper.selectByPrimaryKey(csRecord.getPhoneNumberId());
        Boolean isLocal = phoneNumberService.isLocalWithCallerAndCalledPhoneNumber(csRecord.getCalledPhoneNumber(), phoneNumberPO);
	    TenantPO tenant = tenantService.selectByKey(csRecord.getTenantId());
	    LocalDateTime localDateTime = csRecord.getStartTime() == null ? csRecord.getCreateTime() : csRecord.getStartTime();
        int year = localDateTime.getYear();
        int month = localDateTime.getMonthValue();
        int day = localDateTime.getDayOfMonth();
        int hour = localDateTime.getHour();
        Long distributorId = tenant == null ? CommonApplicationConstant.OPE_DISTRIBUTOR_ID : tenant.getDistributorId();
        Query query = getCsCallStatsQuery(csRecord.getTenantId(), distributorId, csRecord.getCallUserId(), year, month, day, hour, isLocal, csRecord.getPhoneNumberId());
        if (csRecord.getRealIntentLevel() != null) {
            callStatsMongoService.incrementValue(MongoCollectionNameCenter.CS_CALL_STATS_ROBOT, query, "intentLevel." + csRecord.getRealIntentLevel(), -1L);
        }
        callStatsMongoService.incrementValue(MongoCollectionNameCenter.CS_CALL_STATS_ROBOT, query, "intentLevel." + intentLevel, 1L);

    }

    @Override
    public void updateCallRecordIntentLevel(Long csRecordId, Integer intentLevel) {
        CsRecordPO csRecord = csRecordPOMapper.selectByPrimaryKey(csRecordId);
        // 更新mongo统计
        updateCallStatIntentLevel(csRecord, intentLevel);
        // 更新数据库的意向
        csRecordPOMapper.updateCsRecordIntentLevel(csRecord.getTenantId(), csRecordId, intentLevel,null);
    }

    @Override
    public void setCsRecordStatus(Long csRecordId, Long tenantId, SystemEnum system) {
        CsRecordPO csRecordPO = csRecordPOMapper.selectByPrimaryKey(csRecordId);
        StringBuilder readStatus = new StringBuilder(Integer.toBinaryString(csRecordPO.getReadStatus()));
        while (readStatus.length() < 3) {
            readStatus.insert(0, "0");
        }
        int parameter = 0;
        switch (system) {
            case BOSS:
                parameter = 4;
                break;
            case OPE:
                parameter = 2;
                break;
            case CRM:
                parameter = 1;
                break;
            case MINIAPP:
                parameter = 1;
                break;
        }
        // 已改为或运算
        csRecordPOMapper.setCallRecordStatus(csRecordId, tenantId, parameter);
    }

    @Override
    public PageResultObject<CsRecordVO> searchTenantCsRecordByQuery(CallRecordQueryVO callRecordQuery, UserPO userPO) {
        //搜索条件 ，联系电话， 通话记录， 姓名， 坐席名称
        //通过坐席名称搜索坐席列表
        Optional<List<Long>> authUserIdList =Optional.empty();
        if(Objects.isNull(callRecordQuery.getSystemType()) || Objects.equals(SystemEnum.CALL_OUT,callRecordQuery.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CALL_IN,callRecordQuery.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_company, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,callRecordQuery.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        }else if(Objects.equals(SystemEnum.CUSTOMER_CENTER,callRecordQuery.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.customer_contact_records_company, AuthResourceUriEnum.customer_contact_records_groups);
        }

        List<Long> csStaffIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(callRecordQuery.getRobotName())) {
            List<CsStaffInfoPO> csStaffList = csStaffInfoPOMapper.selectByNameLike(callRecordQuery.getTenantId(), callRecordQuery.getRobotName());
            if (csStaffList != null && !csStaffList.isEmpty()) {
                csStaffList.forEach(e -> csStaffIdList.add(e.getCsStaffId()));
            }
            if (csStaffIdList.isEmpty()) {
                return PageResultObject.of(new ArrayList<>());
            }
        }
        if(callRecordQuery.getCsStaffId() != null){
            csStaffIdList.add(callRecordQuery.getCsStaffId());
        }
        PageHelper.startPage(callRecordQuery.getPageNum(), callRecordQuery.getPageSize());
        List<CsRecordVO> list = csRecordPOMapper.searchTenantCsRecordByQuery(callRecordQuery.getTenantId(), callRecordQuery, csStaffIdList, authUserIdList.orElse(null));
        packageCsStaffInfo(list);
        //设置失效时间
        list.stream().forEach(csRecordVO -> {
            csRecordVO.setRemind(false);//只是为了去除重复代码块
            csRecordVO.setExpires(ApplicationConstant.RECORD_EXPIRE_DAYS - MyDateUtils.dateDiff(csRecordVO.getCreateTime(),LocalDateTime.now()));
            csRecordVO.setRemind(ApplicationConstant.RECORD_EXPIRES_REMIND_DAYS >= csRecordVO.getExpires() ? true:false);
        });

        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> customerPersonIds = list.stream().map(CsRecordVO::getCustomerPersonId).collect(Collectors.toSet());
            Set<Long> phoneNumberIdSet = list.stream().map(CsRecordVO::getPhoneNumberId).collect(Collectors.toSet());
            Set<Long> groupIdList = list.stream().map(CsRecordVO::getCsStaffGroupId).collect(Collectors.toSet());
            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(groupIdList));

            Map<Long, String> groupMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(groupPOList)){
                groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
            }
            List<PhoneNumberPO> phoneNumberPOList = phoneNumberService.getPhoneNumberListByIdList(new ArrayList<>(phoneNumberIdSet));
            Map<Long, PhoneNumberPO> phoneNumberPOMap = phoneNumberPOList.stream().collect(Collectors.toMap(PhoneNumberPO::getPhoneNumberId, d -> d));
            for (CsRecordVO recordVO : list) {
                if(MapUtils.isNotEmpty(phoneNumberPOMap)){
                    PhoneNumberPO phoneNumberPO = phoneNumberPOMap.get(recordVO.getPhoneNumberId());
                    recordVO.setTenantPhoneNumber(phoneNumberPO == null ? null : phoneNumberPO.getPhoneNumber());
                }
                if(recordVO.getCsStaffGroupId() != null){
                    recordVO.setCsStaffGroupName(groupMap.get(recordVO.getCsStaffGroupId()));
                }
                recordVO.setRead(CRMCallRecordReadStatusEnum.getEnumByCode(recordVO.getReadStatus()));
            }
        }
        return PageResultObject.of(list);
    }

    @Override
    public PageResultObject<CsStaffCallRecordVO> searchCsStaffCallRecord(CsStaffCallRecordQueryVO csStaffCallRecordQueryVO) {
        CallInRecordPO lastCallInRecord = getLastCallInRecord(csStaffCallRecordQueryVO.getTenantId());
        PageHelper.startPage(csStaffCallRecordQueryVO.getPageNum(), csStaffCallRecordQueryVO.getPageSize());
        List<CsStaffCallRecordVO> list = new ArrayList<>();
        if(lastCallInRecord == null){
            list = csRecordPOMapper.searchCsStaffCallRecordOnlyCs(csStaffCallRecordQueryVO);
        }else {
            list = csRecordPOMapper.searchCsStaffCallRecord(csStaffCallRecordQueryVO);
        }
        //处理接听状态
        return PageResultObject.of(list);
    }

    private CallInRecordPO getLastCallInRecord(Long tenantId){
        return callInRecordPOMapper.getLastCallInRecord(tenantId);
    }

    private void setFollowUserName(List<CsRecordVO> list) {
        List<Long> followUserIdList = list.stream().map(CsRecordVO::getCreateUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<? extends Serializable, UserPO> userPOMap = userService.selectMapByKeyCollect(followUserIdList);
        list.forEach((csRecord) -> {
            csRecord.setFollowUserName(Try.of(() -> userPOMap.get(csRecord.getCreateUserId()).getName()).getOrElseGet((e) -> null));
        });
    }

    @Override
    public List<String> getRobotJobCallDetailWithIdentifyId(Long robotCallJobId, String identifyId) {
        String csIndex = RedisKeyCenter.getCallJobCallDetailRedisKey(identifyId);
        return redisOpsService.getRedisTemplate().opsForList().range(csIndex, 0, -1);
    }

    private boolean getFilterDistributorIdList(CallRecordBossAndOpeQueryVO condition) {
        Long distributorId = condition.getDistributorId();
        if (Objects.nonNull(distributorId)) {
            List<Long> distributorIdList;
            DistributorPO distributorPO = distributorService.selectByKey(distributorId);
            if (distributorPO.getParentId() == 0L) {
                //一级代理商
                distributorIdList = distributorService.selectByParentId(distributorId);
                distributorIdList.add(distributorId);
            } else {
	            distributorIdList = Collections.singletonList(distributorId);
            }
            if (CollectionUtils.isEmpty(distributorIdList)) {
                return true;
            } else {
                condition.setFilterDistributorIdList(distributorIdList);
            }
        }
        return false;
    }
    @Override
    public PageResultObject<CallRecordVO> queryCsRecord(CallRecordBossAndOpeQueryVO callRecordBossAndOpeQueryVO) {
        //boss系统查询通话记录
        if (getFilterDistributorIdList(callRecordBossAndOpeQueryVO)) {
            return PageResultObject.of(Collections.emptyList());
        }
        //根据坐席名字进行筛选
        List<Long> csStaffIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(callRecordBossAndOpeQueryVO.getRobotName())) {
            List<CsStaffInfoPO> csStaffList = csStaffInfoPOMapper.selectByCsName(callRecordBossAndOpeQueryVO.getRobotName());
            if (csStaffList != null && !csStaffList.isEmpty()) {
                csStaffList.forEach(e -> csStaffIdList.add(e.getCsStaffId()));
            }
            if (csStaffIdList.isEmpty()) {
                return PageResultObject.of(Collections.emptyList());
            }
        }
        PageHelper.startPage(callRecordBossAndOpeQueryVO.getPageNum(), callRecordBossAndOpeQueryVO.getPageSize());
        List<CsRecordVO> list = csRecordPOMapper.queryCsRecord(callRecordBossAndOpeQueryVO,csStaffIdList);
        List<CallRecordVO> resList = new ArrayList<>();
        if(list != null && !list.isEmpty()){
            Set<Long> tenantIdList = list.stream().map(CsRecordVO::getTenantId).collect(Collectors.toSet());
            List<TenantPO> tenantPOList = tenantService.getTenantListByTenantIds(new ArrayList<>(tenantIdList));
            Map<Long, String> tenantMap = tenantPOList.stream().collect(Collectors.toMap(TenantPO::getTenantId, TenantPO::getCompanyName));
            Set<Long> phoneNumberIdSet = list.stream().map(CsRecordVO::getPhoneNumberId).collect(Collectors.toSet());
            List<PhoneNumberPO> phoneNumberPOList = phoneNumberService.getPhoneNumberListByIdList(new ArrayList<>(phoneNumberIdSet));
            Map<Long, String> phoneNumberMap = phoneNumberPOList.stream().collect(Collectors.toMap(PhoneNumberPO::getPhoneNumberId, PhoneNumberPO::getPhoneNumber));

            for (CsRecordVO item : list) {
                CallRecordVO callRecordVO = new CallRecordVO();
                callRecordVO.setCallRecordId(item.getCsRecordId());
                callRecordVO.setTenantId(item.getTenantId());
                callRecordVO.setCustomerPersonId(item.getCustomerPersonId());
                callRecordVO.setCalledPhoneNumber(item.getCustomerPersonNumber());
                callRecordVO.setPhoneNumberId(item.getPhoneNumberId());
                callRecordVO.setChatDuration(item.getChatDuration());
                callRecordVO.setCompanyName(tenantMap.get(item.getTenantId()));
                callRecordVO.setCsStaffName(item.getCsStaffName());
                callRecordVO.setCustomerPersonName(item.getCustomerPersonName());
                callRecordVO.setTenantPhoneNumber(phoneNumberMap.get(item.getPhoneNumberId()));
                callRecordVO.setReadStatus(item.getReadStatus());
                callRecordVO.setRealIntentLevel(item.getRealIntentLevel());
                callRecordVO.setResultStatus(item.getResultStatus());
                callRecordVO.setStartTime(item.getStartTime());
                callRecordVO.setEndTime(item.getEndTime());
                callRecordVO.setComment(item.getComment());
                resList.add(callRecordVO);
            }
        }
        PageResultObject<CsRecordVO> src = PageResultObject.of(list);
        PageResultObject<CallRecordVO> des = PageResultObject.of(resList);
        BeanUtils.copyProperties(src, des);
        des.setContent(resList);
        return des;
    }

    @Override
    public CallRecordDetailVO getCsCallDetailListByCsRecordId(Long callRecordId) {

        // 校验租户id是否合法
        CsRecordPO callRecordPO = csRecordPOMapper.selectByPrimaryKey(callRecordId);

        CallRecordDetailVO callRecordDetailVO = new CallRecordDetailVO();
        callRecordDetailVO.setCallRecordId(callRecordPO.getCsRecordId());
        callRecordDetailVO.setStartTime(callRecordPO.getStartTime());
        callRecordDetailVO.setEndTime(callRecordPO.getEndTime());
        callRecordDetailVO.setChatRound(callRecordPO.getChatRound());
        callRecordDetailVO.setChatDuration(callRecordPO.getChatDuration());
        callRecordDetailVO.setResultStatus(callRecordPO.getResultStatus());
        callRecordDetailVO.setIntentLevel(callRecordPO.getIntentLevel());
        callRecordDetailVO.setRealIntentLevel(callRecordPO.getRealIntentLevel());
        callRecordDetailVO.setFullAudioUrl(callRecordPO.getFullAudioUrl());
        callRecordDetailVO.setCallRecordType(CallRecordTypeEnum.DIRECT_CALL);
        callRecordDetailVO.setEmotion(callRecordPO.getEmotion());
        //设置坐席type
        callRecordDetailVO.setRobotType(RobotTypeEnum.CS_ROBOT.getCode());
        //设置意向标签组
        callRecordDetailVO.setIntentLevelTagId(callRecordPO.getIntentLevelTagId());
        return callRecordDetailVO;
    }

    @Override
    public List<XSecondMonitorVO>  getCountForXSecond(Long tenantId, List<Long> csStaffIdList, Integer second) {
        return csRecordPOMapper.getCountForXSecond(tenantId,csStaffIdList,second);
    }

    @Override
    public List<CallCostDetailBO> selectByCsCallCostByUserId(CallCostQueryVO callCostQueryVO) {
        return csRecordPOMapper.selectByCsCallCostByUserId(callCostQueryVO);
    }

    @Override
    public Integer getCsCallCostCount(CallCostQueryVO callCostQueryVO) {
        return csRecordPOMapper.getCsCallCostCount(callCostQueryVO);
    }

    @Override
    public void updateCreateUserId(Long tenantId, Long userId, Collection<Long> customerPersonIds) {
        csRecordPOMapper.updateCreateUser(tenantId, userId, customerPersonIds);
    }

    @Override
    public void csNotify(Long csJobRecordId, Long tenantId) {}

    @Override
    public PageResultObject<CsRecordVO> getCsRecordList(UserPO userPO, CsRecordQueryVO queryVO) {
        //搜索条件 ，联系电话， 通话记录， 姓名， 坐席名称
        //通过坐席名称搜索坐席列表
        Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<CsRecordVO> list = csRecordPOMapper.searchTenantCsRecordByQueryV2(queryVO.getTenantId(), queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null));
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> customerPersonIds = list.stream().map(CsRecordVO::getCustomerPersonId).collect(Collectors.toSet());
            Set<Long> groupIdList = list.stream().map(CsRecordVO::getCsStaffGroupId).collect(Collectors.toSet());
            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(groupIdList));
            List<Long> csStaffIdList = list.stream().map(CsRecordVO::getCsStaffId).collect(Collectors.toList());
            List<CsStaffInfoPO> csStaffInfoPOList = csStaffInfoService.selectByIdList(csStaffIdList);
            Map<Long, CsStaffInfoPO> csStaffInfoPOMap = csStaffInfoPOList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, d -> d));
            Map<Long, String> groupMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(groupPOList)){
                groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
            }
            for (CsRecordVO recordVO : list) {
                if(recordVO.getCsStaffGroupId() != null){
                    recordVO.setCsStaffGroupName(groupMap.get(recordVO.getCsStaffGroupId()));
                }
                recordVO.setRead(CRMCallRecordReadStatusEnum.getEnumByCode(recordVO.getReadStatus()));
                recordVO.setCallRecordId(recordVO.getCsRecordId());
                if (csStaffInfoPOMap.containsKey(recordVO.getCsStaffId())) {
                    CsStaffInfoPO csStaffInfoPO = csStaffInfoPOMap.get(recordVO.getCsStaffId());
                    recordVO.setCsName(csStaffInfoPO.getCsName());
                    recordVO.setCsMobile(csStaffInfoPO.getCsMobile());
                }else {
                    recordVO.setCsName(recordVO.getCsStaffName());
                }
            }
            Map<Long,PhoneHomeLocationBO> locationMap = phoneLocationService.getByPrimaryKey(list.stream().map(CsRecordVO::getPhoneLocationId).collect(Collectors.toList()));
            list.forEach(csRecordVO -> {
                if (Objects.nonNull(csRecordVO.getPhoneLocationId())){
                    PhoneHomeLocationBO locationBO = locationMap.get(csRecordVO.getPhoneLocationId());
                    if (Objects.nonNull(locationBO)){
                        if (org.apache.commons.lang3.StringUtils.equals(locationBO.getProv(),locationBO.getCity())){
                            csRecordVO.setLocation(locationBO.getCity());
                        }else {
                            csRecordVO.setLocation(locationBO.getProv() + locationBO.getCity());
                        }
                    }
                }
            });
        }
        return PageResultObject.of(list);
    }

    @Override
    public PageResultObject<CsAndCallInRecordVO> getRecordList(UserPO userPO, CsRecordQueryVO queryVO) {
        Optional<List<Long>> authUserIdList =Optional.empty();
        if(Objects.isNull(queryVO.getSystemType()) || Objects.equals(SystemEnum.CALL_OUT,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CALL_IN,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_company, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        }
        CallInRecordPO callInRecordPO = getLastCallInRecord(queryVO.getTenantId());
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<CsAndCallInRecordVO> list = null;
        if(queryVO.getEndDate() != null){
            queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
        }
        if(queryVO.getCallInstanceType() == null) {
            if(callInRecordPO == null){
                list = csRecordPOMapper.searchCsAndCallInRecordListOnlyCs(queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null), null, null);
            }else {
                list = csRecordPOMapper.searchCsAndCallInRecordList(queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null), null, null);
            }
        }
        if(CallInstanceTypeEnum.CS_SEAT_RECORD.equals(queryVO.getCallInstanceType())){
            list = csRecordPOMapper.searchCsRecordList(queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null), null, null);
        }
        if(CallInstanceTypeEnum.CALL_IN_RECORD.equals(queryVO.getCallInstanceType())){
            list = callInRecordPOMapper.searchCallInRecordList(queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null), null, null);

        }
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> groupIdList = list.stream().map(CsAndCallInRecordVO::getCsStaffGroupId).collect(Collectors.toSet());
            Set<Long> staffIdList = list.stream().map(CsAndCallInRecordVO::getCsStaffId).collect(Collectors.toSet());
            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(groupIdList));
            List<CsStaffInfoPO> staffPOList = csStaffInfoPOMapper.selectByIdList(new ArrayList<>(staffIdList));
            Map<Long, String> groupMap = new HashMap<>();
            Map<Long, String> staffMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(groupPOList)){
                groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
            }
            if(CollectionUtils.isNotEmpty(staffPOList)){
                staffMap = staffPOList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, CsStaffInfoPO::getCsName));
            }
            for (CsAndCallInRecordVO recordVO : list) {
                if(recordVO.getCsStaffGroupId() != null){
                    recordVO.setCsStaffGroupName(groupMap.get(recordVO.getCsStaffGroupId()));
                }
                if(recordVO.getCsStaffId() != null){
                    recordVO.setCsStaffName(staffMap.get(recordVO.getCsStaffId()));
                }
                recordVO.setRead(CRMCallRecordReadStatusEnum.getEnumByCode(recordVO.getReadStatus()));
                if(CallInstanceTypeEnum.CS_SEAT_RECORD.equals(recordVO.getCallInstanceType())){
                    if(CallInResultStatusEnum.ANSWERED.equals(recordVO.getResultStatus())) {
                        recordVO.setCsChatDuration(recordVO.getChatDuration());
                    }
                }
                if(CallInstanceTypeEnum.CALL_IN_RECORD.equals(recordVO.getCallInstanceType()) && recordVO.getTransferStartTime() != null){
                    recordVO.setCsChatDuration(Duration.between(recordVO.getTransferStartTime() , recordVO.getEndTime()).getSeconds());
                    if(recordVO.getCsChatDuration() != null && recordVO.getCsChatDuration() < 0){
                        recordVO.setCsChatDuration(0L);
                    }
                }
                if(StaffGroupTypeEnum.CS.equals(recordVO.getSeatType())){
                    recordVO.setCsChatDuration(recordVO.getChatDuration());
                }
                //客服工作台那边需要显示客服状态
                recordVO.setResultStatus(recordVO.getSeatStatus());
                //设置意向标签
                IntentLevelTagDetailPO intentLevelTagDetailPO = intentLevelTagDetailService.getIntentLevelDetailByCode(recordVO.getIntentLevelTagId(),recordVO.getRealIntentLevel(),false);
                if(Objects.nonNull(intentLevelTagDetailPO)){
                    recordVO.setIntentName(intentLevelTagDetailPO.getName());
                }
            }
        }
        return PageResultObject.of(list);
    }

    @Override
    public JobStartResultVO exportRecordList(UserPO userInfo, CsRecordQueryVO queryVO) {
        if(queryVO.getEndDate() != null){
            queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
        }
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", queryVO.getTenantId());
        jobParametersBuilder.addLong("CURRENT_USER_ID", queryVO.getUserId());
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.CUSTOMER_SERVICE.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(Arrays.asList("时间", "类型", "客户号码", "挂断原因", "总时长", "通话时长", "坐席", "坐席组")));
        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey("客服工作台联系历史导出", queryVO.getTenantId(), queryVO.getUserId(), baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        queryVO.setPageNum(1);
        queryVO.setPageSize(10);

        Integer totalCount = 0;
        if(CollectionUtils.isNotEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCsCallRecordIdList().size();
        }else if(CollectionUtils.isEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isNotEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCallInRecordIdList().size();
        }else {
            PageResultObject pageInfo = getRecordList(userInfo, queryVO);
            totalCount = Long.valueOf(pageInfo.getTotalElements()).intValue();
        }

        Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userInfo.getUserId(), userInfo.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        if(queryVO.getEndDate() != null){
            queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
        }
        queryVO.setUserIdList(authUserIdList.orElse(null));
        String callRecordExportRequestVOString = JsonUtils.object2String(queryVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", callRecordExportRequestVOString);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        Job job = null;
        SpringBatchJobTypeEnum jobType = null;
        if(queryVO.getCallInstanceType() == null) {
            if(CollectionUtils.isNotEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isEmpty(queryVO.getCallInRecordIdList())){
                job = csCallRecordExportJob2;
                jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD2;
            }else if(CollectionUtils.isEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isNotEmpty(queryVO.getCallInRecordIdList())){
                job = csCallRecordExportJob3;
                jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD3;
            }else {
                job = csCallRecordExportJob1;
                jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD1;
            }
        }
        if(CallInstanceTypeEnum.CS_SEAT_RECORD.equals(queryVO.getCallInstanceType())){
            job = csCallRecordExportJob2;
            jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD2;
        }
        if(CallInstanceTypeEnum.CALL_IN_RECORD.equals(queryVO.getCallInstanceType())){
            job = csCallRecordExportJob3;
            jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD3;
        }

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        queryVO.getTenantId(),
                        null,
                        totalCount,
                        queryVO.getUserId(),
                        jobType,
                        SystemEnum.CUSTOMER_SERVICE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public void setTransferCall(TransferCallVO callVO) {
        if(callVO.getFromUserId() != null){
            CsStaffInfoPO fromStaff = csStaffInfoPOMapper.selectVoiceByUserIdAndType(callVO.getFromUserId(), StaffTypeEnum.VOICE);
            if(fromStaff != null){
                callVO.setFromCsStaffId(fromStaff.getCsStaffId());
            }
        }
        if(callVO.getIdentifyId() != null){
            redisOpsService.getRedisTemplate().opsForList().rightPush(RedisKeyCenter.getTransferCallKey(callVO.getIdentifyId()), callVO);
        }
        redisOpsService.set(RedisKeyCenter.getTransferCallInfoKey(callVO.getTenantId(), callVO.getCurrentCsStaffId()), callVO, 20, TimeUnit.MINUTES);
    }

    @Override
    public TransferCallVO getTransferCallInfo(Long tenantId, Long userId) {
        CsStaffInfoPO fromStaff = csStaffInfoPOMapper.selectVoiceByUserIdAndType(userId, StaffTypeEnum.VOICE);
        if(fromStaff != null) {
            TransferCallVO vo = redisOpsService.get(RedisKeyCenter.getTransferCallInfoKey(tenantId, fromStaff.getCsStaffId()), TransferCallVO.class);
            return vo;
        }
        return null;
    }

    @Override
    public JobStartResultVO exportCsRecordList(Long tenantId, Long userId, UserPO userPO, CallRecordQueryVO queryVO) {

        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addString("SYSTEM_TYPE", queryVO.getSystemType() == null ? SystemEnum.CUSTOMER_CENTER.name() : queryVO.getSystemType().name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(Arrays.asList("通话记录id", "客户名", "性别", "联系电话", "号码归属地", "坐席名称", "呼叫时间", "通话状态", "主叫号码", "转接坐席", "接入人员", "呼叫时长", "对话详情")));
        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey("人工外呼历史导出", tenantId, userId, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        queryVO.setPageNum(1);
        queryVO.setPageSize(10);
        PageResultObject pageInfo = searchTenantCsRecordByQuery(queryVO, userPO);
        Integer totalCount = Long.valueOf(pageInfo.getTotalElements()).intValue();
        String callRecordExportRequestVOString = JsonUtils.object2String(queryVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", callRecordExportRequestVOString);
        List<Long> csStaffIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(queryVO.getRobotName())) {
            List<CsStaffInfoPO> csStaffList = csStaffInfoPOMapper.selectByNameLike(queryVO.getTenantId(), queryVO.getRobotName());
            if (csStaffList != null && !csStaffList.isEmpty()) {
                csStaffList.forEach(e -> csStaffIdList.add(e.getCsStaffId()));
            }
        }
        if(queryVO.getCsStaffId() != null){
            csStaffIdList.add(queryVO.getCsStaffId());
        }
        jobParametersBuilder.addString("csStaffIdList", csStaffIdList == null ? null :JsonUtils.object2String(csStaffIdList));
        Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        if(authUserIdList.isPresent()){
            jobParametersBuilder.addString("userIdList", authUserIdList.get() == null ? null : JsonUtils.object2String(authUserIdList.get()));
        }else {
            jobParametersBuilder.addString("userIdList", null);
        }
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        Job job = csRecordExportJob;

        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.EXPORT_CS_RECORD;

       //取消导出数据80晚的限制

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        queryVO.getSystemType() == null ? SystemEnum.CUSTOMER_CENTER : queryVO.getSystemType(),
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public PageResultObject<CsAndCallInRecordVO> getCallOutRecordList(UserPO userPO, CsRecordQueryVO queryVO) {
        Optional<List<Long>> authUserIdList =Optional.empty();
        if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,queryVO.getSystemType())  || Objects.equals(SystemEnum.CUSTOMER_CENTER,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company
                    , AuthResourceUriEnum.contact_records_groups);
            //如果数据权限为仅自己，要看到预测试外呼任务转给自己的数据
            if(roleService.hasAuthResource(userPO.getUserId(), SystemEnum.AICC, AuthResourceUriEnum.contact_records_myself)){
                authUserIdList = Optional.empty();
                CsStaffInfoPO csStaffInfoPO = csStaffInfoService.queryByUserIdAndType(userPO.getTenantId(),userPO.getUserId(),StaffTypeEnum.VOICE);
                if(Objects.nonNull(csStaffInfoPO)){
                    queryVO.setCsStaffIdList(Arrays.asList(csStaffInfoPO.getCsStaffId()));
                }
            }
        }
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<CsAndCallInRecordVO> list = null;
        list = csRecordPOMapper.searchCsRecordList(queryVO, null, null, authUserIdList.orElse(null), null, null);
        PageResultObject<CsAndCallInRecordVO> result = PageResultObject.of(list);
        //如果客户去重,则取最新的数据
        if(queryVO.getLastCallRecord()){
            List<Long> csRecordIds = list.stream().map(CsAndCallInRecordVO::getCallRecordId).filter(x->Objects.nonNull(x)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(csRecordIds)){
                List<CsRecordPO> csRecordPOS = csRecordPOMapper.selectByIdList(csRecordIds);
                if(CollectionUtils.isNotEmpty(csRecordPOS)){
                    list = new ArrayList<>();
                    for(CsRecordPO csRecordPO :csRecordPOS){
                        CsAndCallInRecordVO csAndCallInRecordVO = MyBeanUtils.copy(csRecordPO,CsAndCallInRecordVO.class);
                        csAndCallInRecordVO.setCallRecordId(csRecordPO.getCsRecordId());
                        csAndCallInRecordVO.setCallInstanceType(CallInstanceTypeEnum.CS_SEAT_RECORD);
                        csAndCallInRecordVO.setResultStatus(CallInResultStatusEnum.getByName(csRecordPO.getResultStatus().name()));
                        csAndCallInRecordVO.setSeatStatus(CallInResultStatusEnum.getByName(csRecordPO.getSeatStatus().name()));
                        list.add(csAndCallInRecordVO);
                    }
                }
            }
        }
        packageRecordList(list, queryVO.getTenantId());
        result.setContent(list);
        return result;
    }

    @Override
    public PageResultObject<CsAndCallInRecordVO> getCallInRecordList(UserPO userPO, CsRecordQueryVO queryVO) {
        Optional<List<Long>> authUserIdList =Optional.empty();
        if(Objects.isNull(queryVO.getSystemType()) || Objects.equals(SystemEnum.CALL_OUT,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_company, AuthResourceUriEnum.crm_out_call_platform_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CALL_IN,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_company, AuthResourceUriEnum.crm_call_in_recp_dialHistory_data_permission_organization);
        }else if(Objects.equals(SystemEnum.CUSTOMER_SERVICE,queryVO.getSystemType())){
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userPO.getUserId(), userPO.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);
        }
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<CsAndCallInRecordVO> list = null;
        list = callInRecordPOMapper.searchCallInRecordList(queryVO, queryVO.getCsStaffIdList(), queryVO.getCsStaffGroupIdList(), authUserIdList.orElse(null), null, null);
        packageRecordList(list, queryVO.getTenantId());
        return PageResultObject.of(list);
    }

    private void packageRecordList(List<CsAndCallInRecordVO> list, Long tenantId){
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> groupIdList = list.stream().map(CsAndCallInRecordVO::getCsStaffGroupId).collect(Collectors.toSet());
            Set<Long> staffIdList = list.stream().map(CsAndCallInRecordVO::getCsStaffId).collect(Collectors.toSet());
            Set<Long> phoneNumberIdSet = list.stream().map(CsAndCallInRecordVO::getPhoneNumberId).collect(Collectors.toSet());
            Set<Long> csBatchCallJobIdSet = list.stream().map(CsAndCallInRecordVO::getCsBatchCallJobId).collect(Collectors.toSet());
            List<Long> customerPersonIds = list.stream().map(CsAndCallInRecordVO::getCustomerPersonId).collect(Collectors.toList());

            List<CsStaffGroupPO> groupPOList = csStaffGroupService.selectByIdList(new ArrayList<>(groupIdList));
            List<CsStaffInfoPO> staffPOList = csStaffInfoPOMapper.selectByIdList(new ArrayList<>(staffIdList));
            List<CsBatchCallJobPO> csBatchCallJobPOList = csBatchCallJobService.selectBatchJobListByIdList(tenantId,new ArrayList<>(csBatchCallJobIdSet));

            Map<Long, PhoneNumberPO> phoneNumberPOMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(phoneNumberIdSet)) {
                List<PhoneNumberPO> phoneNumberPOS = phoneNumberService.getPhoneNumberListByIdList(new ArrayList<>(phoneNumberIdSet));
                if(phoneNumberPOS != null){
                    phoneNumberPOMap = phoneNumberPOS.stream().collect(Collectors.toMap(PhoneNumberPO::getPhoneNumberId, x-> x));
                }
            }
            Map<Long, String> groupMap = new HashMap<>();
            Map<Long, String> staffMap = new HashMap<>();
            Map<Long, CsBatchCallJobPO> batchJobMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(groupPOList)){
                groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
            }
            if(CollectionUtils.isNotEmpty(staffPOList)){
                staffMap = staffPOList.stream().collect(Collectors.toMap(CsStaffInfoPO::getCsStaffId, CsStaffInfoPO::getCsName));
            }
            if(CollectionUtils.isNotEmpty(csBatchCallJobPOList)){
                batchJobMap = csBatchCallJobPOList.stream().collect(Collectors.toMap(CsBatchCallJobPO::getCsBatchCallJobId,a->a,(k1,k2)->k1));
            }
            for (CsAndCallInRecordVO recordVO : list) {
                if(recordVO.getCsStaffGroupId() != null){
                    recordVO.setCsStaffGroupName(groupMap.get(recordVO.getCsStaffGroupId()));
                }
                if(recordVO.getCsStaffId() != null){
                    recordVO.setCsStaffName(staffMap.get(recordVO.getCsStaffId()));
                }
                recordVO.setRead(CRMCallRecordReadStatusEnum.getEnumByCode(recordVO.getReadStatus()));
                if(CallInstanceTypeEnum.CS_SEAT_RECORD.equals(recordVO.getCallInstanceType())){
                    if(CallInResultStatusEnum.ANSWERED.equals(recordVO.getResultStatus())) {
                        recordVO.setCsChatDuration(recordVO.getChatDuration());
                    }
                    if(recordVO.getCsBatchCallJobRecordId() > 0){
                        recordVO.setCsCallSource(CsCallSourceEnum.CS_BATCH_CALL);
                    }else {
                        recordVO.setCsCallSource(CsCallSourceEnum.CS_CALL_OUT);
                    }
                }
                if(CallInstanceTypeEnum.CALL_IN_RECORD.equals(recordVO.getCallInstanceType()) && recordVO.getTransferStartTime() != null){
                    recordVO.setCsChatDuration(Duration.between(recordVO.getTransferStartTime() , recordVO.getEndTime()).getSeconds());
                    if(recordVO.getCsChatDuration() != null && recordVO.getCsChatDuration() < 0){
                        recordVO.setCsChatDuration(0L);
                    }
                    if(CsCallCategoryEnum.AI_CS.equals(recordVO.getCsCallCategory())){
                        recordVO.setCsCallSource(CsCallSourceEnum.AI_TO_CS_WEB);
                    }
                    if(CsCallCategoryEnum.AI_INT.equals(recordVO.getCsCallCategory())){
                        recordVO.setCsCallSource(CsCallSourceEnum.AI_TO_CS_INT);
                    }
                    if(CsCallCategoryEnum.CALL_IN_CS.equals(recordVO.getCsCallCategory())){
                        recordVO.setCsCallSource(CsCallSourceEnum.CALLIN_TO_CS_WEB);
                    }
                    if(CsCallCategoryEnum.CALL_IN_INT.equals(recordVO.getCsCallCategory())){
                        recordVO.setCsCallSource(CsCallSourceEnum.CALLIN_TO_CS_INT);
                    }
                    if(CsCallCategoryEnum.MONITOR_TRANSFER.equals(recordVO.getCsCallCategory())){
                        recordVO.setCsCallSource(CsCallSourceEnum.MONITOR_AI_TO_CS_WEB);
                    }
                }
                if(StaffGroupTypeEnum.CS.equals(recordVO.getSeatType())){
                    recordVO.setCsChatDuration(recordVO.getChatDuration());
                }
                //客服工作台那边需要显示客服状态
                if(recordVO.getPhoneNumberId() != null){
                    PhoneNumberPO phoneNumberPO = phoneNumberPOMap.get(recordVO.getPhoneNumberId());
                    if(phoneNumberPO != null) {
                        recordVO.setPhoneName(StringUtils.isNotBlank(phoneNumberPO.getPhoneName()) ? phoneNumberPO.getPhoneName() : phoneNumberPO.getPhoneNumber());
                    }
                }else {
                    if(StringUtils.isNotEmpty(recordVO.getCallerPhone())){
                        recordVO.setPhoneName(recordVO.getCallerPhone());
                    }
                }
                IntentLevelTagDetailPO intentLevelTagDetailPO = intentLevelTagDetailService.getIntentLevelDetailByCode(recordVO.getIntentLevelTagId(),recordVO.getRealIntentLevel(),false);
                if(Objects.nonNull(intentLevelTagDetailPO)){
                    recordVO.setIntentName(intentLevelTagDetailPO.getName());
                }
                if(batchJobMap.containsKey(recordVO.getCsBatchCallJobId())){
                    recordVO.setCsBatchJobName(batchJobMap.get(recordVO.getCsBatchCallJobId()).getJobName());
                }

            }
        }
    }

    @Override
    public JobStartResultVO exportCallOutRecordList(UserPO userInfo, CsRecordQueryVO queryVO) {
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", queryVO.getTenantId());
        jobParametersBuilder.addLong("CURRENT_USER_ID", queryVO.getUserId());
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.CUSTOMER_SERVICE.name());
        List<String> headList = getHeadName(queryVO);
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headList));
        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey("客服工作台联系历史导出", queryVO.getTenantId(), queryVO.getUserId(), baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        queryVO.setPageNum(1);
        queryVO.setPageSize(10);

        Integer totalCount = 0;
        if(CollectionUtils.isNotEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCsCallRecordIdList().size();
            if(queryVO.getEndDate() != null){
                queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
            }
        }else if(CollectionUtils.isEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isNotEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCallInRecordIdList().size();
            if(queryVO.getEndDate() != null){
                queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
            }
        }else {
            PageResultObject pageInfo = getCallOutRecordList(userInfo, queryVO);
            totalCount = Long.valueOf(pageInfo.getTotalElements()).intValue();
        }

        Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userInfo.getUserId(), userInfo.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);

        queryVO.setUserIdList(authUserIdList.orElse(null));
        String callRecordExportRequestVOString = JsonUtils.object2String(queryVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", callRecordExportRequestVOString);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        Job job = csCallRecordExportJob2;
        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD2;

        //取消导出数据80晚的限制

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        queryVO.getTenantId(),
                        null,
                        totalCount,
                        queryVO.getUserId(),
                        jobType,
                        SystemEnum.CUSTOMER_SERVICE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    public List<String> getHeadName(CsRecordQueryVO queryVO){
        List<String> head = Arrays.asList("通话id", "客户", "联系电话", "客户标签", "客户意向","客户分组", "自定义变量");
        List<String>  headList = new ArrayList(head);
        if(CollectionUtils.isNotEmpty(queryVO.getCsCallSourceList())){
            //来源改为单选，必填
            if(queryVO.getCsCallSourceList().get(0).equals(CsCallSourceEnum.CS_BATCH_CALL)){
                headList.addAll(Arrays.asList("预测试外呼任务","接待状态"));
            }
        }
        headList.addAll(Arrays.asList("来源","接待坐席", "所在坐席组","客户通话状态","坐席通话时长","总通话时长","挂断方","主叫号码","开始时间", "对话详情"));
        return headList;
    }

    @Override
    public JobStartResultVO exportCallInRecordList(UserPO userInfo, CsRecordQueryVO queryVO) {
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", queryVO.getTenantId());
        jobParametersBuilder.addLong("CURRENT_USER_ID", queryVO.getUserId());
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.CUSTOMER_SERVICE.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(Arrays.asList("通话id", "客户", "联系电话", "客户标签", "客户意向", "来源", "接待坐席", "所在坐席组","客户通话状态","接待状态","坐席通话时长","挂断方","外显号码","开始时间")));
        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey("客服工作台联系历史导出", queryVO.getTenantId(), queryVO.getUserId(), baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        queryVO.setPageNum(1);
        queryVO.setPageSize(10);

        Integer totalCount = 0;
        if(CollectionUtils.isNotEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCsCallRecordIdList().size();
            if(queryVO.getEndDate() != null){
                queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
            }
        }else if(CollectionUtils.isEmpty(queryVO.getCsCallRecordIdList()) && CollectionUtils.isNotEmpty(queryVO.getCallInRecordIdList())){
            totalCount = queryVO.getCallInRecordIdList().size();
            if(queryVO.getEndDate() != null){
                queryVO.setEndDate(queryVO.getEndDate().plusDays(1));
            }
        }else {
            PageResultObject pageInfo = getCallInRecordList(userInfo, queryVO);
            totalCount = Long.valueOf(pageInfo.getTotalElements()).intValue();
        }

        Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userInfo.getUserId(), userInfo.getTenantId(), AuthResourceUriEnum.contact_records_company, AuthResourceUriEnum.contact_records_groups);

        queryVO.setUserIdList(authUserIdList.orElse(null));
        String callRecordExportRequestVOString = JsonUtils.object2String(queryVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", callRecordExportRequestVOString);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        Job job = csCallRecordExportJob3;
        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.EXPORT_CS_CALL_RECORD3;

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        queryVO.getTenantId(),
                        null,
                        totalCount,
                        queryVO.getUserId(),
                        jobType,
                        SystemEnum.CUSTOMER_SERVICE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }


    @Override
    public void updateNumOfCalls(Long csRecordId) {
        csRecordPOMapper.updateNumOfCalls(csRecordId);
    }

    @Override
    public CsRecordVO getCsRecordByCsBatchCallJobRecordId(Long csBatchCallJobRecordId,Long tenantId) {
        return csRecordPOMapper.getCsRecordByCsBatchCallJobRecordId(csBatchCallJobRecordId,tenantId);
    }

    @Override
    public List<CsRecordPO> selectByCustomerPersonId(Long tenantId, Long customerPersonId) {
        return csRecordPOMapper.selectByCustomerPersonId(tenantId, customerPersonId);
    }

    @Override
    public Map<Long, List<CsDetailPO>> recordDetailMap(Long tenantId, List<Long> recordIdList) {
        Map<Long, List<CsDetailPO>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(recordIdList)) {
            Map<Long, List<CsDetailPO>> callDetailMap = csDetailService.getCallDetailListByCallRecordIdList(tenantId, recordIdList);
            recordIdList.parallelStream().forEach(callRecord -> {
                List<CsDetailPO> oneCallDetailList = callDetailMap.getOrDefault(callRecord, Collections.emptyList());
                map.put(callRecord, oneCallDetailList);
            });
        }
        return map;
    }

    @Override
    public List<String> getBatchJobCalledList(Long tenantId,Long csBatchCallJobId,List<String> phoneNumbers) {
        return csRecordPOMapper.getBatchJobCalledList(tenantId,csBatchCallJobId,phoneNumbers);
    }

	@Override
	public void initCsRecordDistributorId() {
		List<Long> tenantIds = csRecordPOMapper.selectDistinctTenantId();
		Map<? extends Serializable, Long> idMap = tenantService.selectMapByKeyCollect(tenantIds, TenantPO::getDistributorId);
		idMap.forEach((key, value) -> {
			if (value != 0) {
				csRecordPOMapper.updateDistributorIdByTenantId((Long) key, value);
			}
		});
	}
}
