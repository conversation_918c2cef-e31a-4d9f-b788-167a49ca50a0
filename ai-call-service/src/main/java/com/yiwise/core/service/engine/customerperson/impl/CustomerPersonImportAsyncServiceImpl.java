package com.yiwise.core.service.engine.customerperson.impl;

import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.encrypt.Md5Utils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.batch.mq.api.dto.BatchMqMessageDTO;
import com.yiwise.batch.mq.api.dto.CustomerPersonImportDTO;
import com.yiwise.batch.mq.api.helper.BaseBatchMqHelper;
import com.yiwise.core.batch.entity.dto.CustomerPersonImportCommonDTO;
import com.yiwise.core.config.ApplicationConstant;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.model.vo.openapi.RobotCallJobAsyncImportResponseVO;
import com.yiwise.core.model.vo.openapi.RobotCallJobAsyncImportVO;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.customerperson.CustomerPersonImportAsyncService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class CustomerPersonImportAsyncServiceImpl implements CustomerPersonImportAsyncService {

    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private BaseBatchMqHelper baseBatchMqHelper;

    @Override
    public RobotCallJobAsyncImportResponseVO importCustomerPersonAsync(RobotCallJobAsyncImportVO asyncImportVO) {
        if (Objects.isNull(asyncImportVO.getRobotCallJobId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "robotCallJobId不能为空");
        }
        RobotCallJobPO robotCallJobPO = robotCallJobService.selectByKey(asyncImportVO.getRobotCallJobId());
        if (Objects.isNull(robotCallJobPO) || !asyncImportVO.getTenantId().equals(robotCallJobPO.getTenantId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务不存在");
        }
        if (CollectionUtils.isEmpty(asyncImportVO.getCustomerPersons())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "用户列表不能为空");
        }
        String batchId = StringUtils.isNotBlank(asyncImportVO.getBatchId()) ? asyncImportVO.getBatchId() : Md5Utils.getMd5String(UUID.randomUUID().toString());
        CustomerPersonImportCommonDTO commonDTO = new CustomerPersonImportCommonDTO();
        commonDTO.setTenantId(asyncImportVO.getTenantId());
        commonDTO.setCurrentUserId(asyncImportVO.getUserId());
        commonDTO.setBatchId(batchId);
        List<CustomerPersonImportDTO> importDTOList = asyncImportVO.getCustomerPersons().stream().map(item -> {
            CustomerPersonImportDTO importDTO = new CustomerPersonImportDTO();
            importDTO.setRobotCallJobId(asyncImportVO.getRobotCallJobId());
            importDTO.setPhoneNumber(item.getPhoneNumber());
            importDTO.setProperties(item.getProperties());
            importDTO.setName(item.getName());
            importDTO.setGender(item.getGender());
            importDTO.setExtraNameInfo(item.getCustomerAttr());
            importDTO.setAlternatePhoneNumbers(item.getAlternatePhoneNumbers());
            return importDTO;
        }).collect(Collectors.toList());
        Lists.partition(importDTOList, ApplicationConstant.BATCH_MQ_IMPORT_CHUNK_SIZE)
                .forEach(subList -> baseBatchMqHelper.sendProcessMqMessageApi(BatchMqMessageDTO.builder()
                        .commonInfo(commonDTO)
                        .records(subList)
                        .build()));
        return RobotCallJobAsyncImportResponseVO.builder()
                .batchId(batchId)
                .build();
    }
}
