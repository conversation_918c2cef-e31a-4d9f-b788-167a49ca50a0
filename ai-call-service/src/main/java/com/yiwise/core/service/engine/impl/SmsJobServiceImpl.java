package com.yiwise.core.service.engine.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.base.common.context.EnvEnum;
import com.yiwise.base.common.utils.RetryRunUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.date.MyDateUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.*;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.aicc.common.enums.billing.TenantAccountEnum;
import com.yiwise.core.batch.ExcelUtils;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.common.ExcelTemplate;
import com.yiwise.core.batch.entity.dto.SheetInfoDTO;
import com.yiwise.core.batch.excelimport.entity.SmsJobMessageImportVO;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.*;
import com.yiwise.core.dal.dao.*;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.SmsInterceptPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.feignclient.billing.TenantAccountClient;
import com.yiwise.core.feignclient.rcs.CustomerBlackGroupRemoteClient;
import com.yiwise.core.feignclient.rcs.RcsApiClient;
import com.yiwise.core.helper.AccountHelper;
import com.yiwise.core.helper.SmsHelper;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.sms.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.sms.UnsubscribeTypeEnum;
import com.yiwise.core.model.getui.GeTuiFileInfoBO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.customer.CustomerPersonImportSmsJobByS3UrlRequestVO;
import com.yiwise.core.model.vo.sms.*;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.batchjob.BasicBatchService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.SmsPlatformChannelService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.platform.*;
import com.yiwise.core.util.ExcelImportUtils;
import com.yiwise.customer.data.platform.rpc.api.service.vo.AccountVO;
import com.yiwise.lcs.api.dto.SmsPlatformChannelDTO;
import com.yiwise.lcs.api.dto.SmsPlatformChannelWithTenantPriceDTO;
import com.yiwise.lcs.api.enums.OwnerTypeEnum;
import com.yiwise.lcs.api.enums.SmsTypeEnum;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.rcs.api.dto.RiskControlStrategyDTO;
import com.yiwise.rcs.api.enums.ModelTypeEnum;
import javaslang.Tuple;
import javaslang.Tuple3;
import javaslang.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.*;
import org.springframework.batch.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.*;
import java.text.MessageFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.yiwise.base.common.helper.ServerInfoConstants.SERVER_HOSTNAME;
import static com.yiwise.base.common.helper.ServerInfoConstants.SERVER_IP_ADDRESS;
import static com.yiwise.base.model.exception.ComErrorCode.OPTIMISTIC_LOCK_VERSION_ERROR;
import static com.yiwise.base.model.exception.ComErrorCode.PRECONDITION_FAILED;
import static com.yiwise.core.batch.common.BatchConstant.Execution.JOB_REQUIRED_PROPERTIES;
import static com.yiwise.core.config.ApplicationConstant.*;
import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORT_GROUPSMS;
import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_JOB_TASK;
import static com.yiwise.core.model.bo.export.CommonExportBO.headerService;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.EXPORT_GROUP_SMS_RECORD;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.IMPORT_GROUP_SMS_TO_SMS_JOB;

/**
 * @Author: wangguomin
 * @Date: 2019-02-11 16:01
 */
@Service
@Slf4j
public class SmsJobServiceImpl extends BasicServiceImpl<SmsJobPO> implements SmsJobService {

    private static final Logger logger = LoggerFactory.getLogger(SmsJobServiceImpl.class);

    @Resource
    private SmsJobPOMapper smsJobPOMapper;

    @Resource
    private DataAccessControlService dataAccessControlService;

    @Resource
    private UserService userService;

    @Resource
    private SmsTemplateService smsTemplateService;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private SmsJobMessagePOMapper smsJobMessagePOMapper;


    @Resource
    private SmsJobTaskPOMapper smsJobTaskPOMapper;

    @Resource
    private SmsJobMessageService smsJobMessageService;

    @Resource
    private SmsJobTaskService smsJobTaskService;

    @Resource
    private SmsService smsService;

    @Resource
    private TenantPOMapper tenantPOMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private OperationLogService operationLogService;

    @Resource
    private CustomerBlackGroupRemoteClient customerBlackGroupRemoteClient;

    @Autowired
    @Qualifier("smsJobImportJob")
    private Job smsJobImportJob;
    @Resource
    private BatchJobInQueueService batchJobInQueueService;
    @Resource
    private SmsSignatureService smsSignatureService;
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantAutoSetService tenantAutoSetService;
    @Resource
    private RcsApiClient rcsApiClient;
    @Autowired
    @Qualifier("geTuiSmsJobImportJob")
    private Job geTuiSmsJobImportJob;

    private static final String[] DEFAULT_HEAD = {"客户名", "联系电话"};
    private static final String SHEET1 = "导入模板";
    private static final String SHEET2 = "导入说明";

    private static final int MAX_RUNNING_COUNT = 100;

    @Resource
    private ObjectStorageHelper objectStorageHelper;

    @Autowired
    @Qualifier("exportSmsJobMessageRecordJob")
    private Job exportSmsJobMessageRecordJob;

    @Resource
    private CallStatsMongoService callStatsMongoService;

    @Resource(name = "groupSmsToSmsJob")
    private Job groupSmsToSmsJob;

    @Resource
    private BasicBatchService basicBatchService;

    @Resource
    private TenantAccountClient tenantAccountClient;

    @Resource
    private SmsPlatformChannelService smsPlatformChannelService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createJob(SmsJobCreateVo smsJobPO) {
        Long tenantId = smsJobPO.getTenantId();
        smsJobPO.setEnabledStatus(true);
        smsJobPO.setStatus(SmsJobStatusEnum.NOT_STARTED);
        // 设置任务下次开始的时间
        if (SmsJobModeEnum.AUTO.equals(smsJobPO.getMode())) {
            RiskControlStrategyDTO riskControlStrategyDTO = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
	        checkCallJobTimeIsValid(riskControlStrategyDTO, smsJobPO.getDailyStartTime(), smsJobPO.getDailyEndTime());
	        smsJobPO.setNextRunTime(getNextRunTime(LocalDateTime.now(), riskControlStrategyDTO, smsJobPO.getFirstStartDate(), smsJobPO.getDailyStartTime(), smsJobPO.getDailyEndTime()));
        }
        // 如果是预发环境上创建的任务只能在预发环境上运行
        // 测试环境区分daily和test
        if (CommonApplicationConstant.CURR_ENV.isPre() || CommonApplicationConstant.CURR_ENV.isTest()) {
            smsJobPO.setIpAddress(SERVER_IP_ADDRESS);
            smsJobPO.setHostname(SERVER_HOSTNAME);
        }

        saveNotNull(smsJobPO);
        operationLogService.onSmsJobCreate(smsJobPO.getSmsJobId(), tenantId, smsJobPO.getCreateUserId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateJob(SmsJobCreateVo smsJobPO) {
	    Long tenantId = smsJobPO.getTenantId();
	    Long smsJobId = smsJobPO.getSmsJobId();
	    SmsJobPO old = selectByKeyOrThrow(smsJobId);
	    smsJobPO.setStatus(SmsJobStatusEnum.NOT_STARTED);
        // 设置任务下次开始的时间
        if (SmsJobModeEnum.AUTO.equals(smsJobPO.getMode())) {
	        RiskControlStrategyDTO riskControlStrategyDTO = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
	        checkCallJobTimeIsValid(riskControlStrategyDTO, smsJobPO.getDailyStartTime(), smsJobPO.getDailyEndTime());
	        smsJobPO.setNextRunTime(getNextRunTime(LocalDateTime.now(), riskControlStrategyDTO, smsJobPO.getFirstStartDate(), smsJobPO.getDailyStartTime(), smsJobPO.getDailyEndTime()));
        }
	    updateNotNull(smsJobPO);
        if (Objects.isNull(smsJobPO.getRiskControlStrategyId())) {
            smsJobPOMapper.updateSmsJobRcs(smsJobId, null);
        }
	    // 任务从自动变成手动清除时间设置, 不能用updateNotNull
	    if (SmsJobModeEnum.AUTO.equals(old.getMode()) && SmsJobModeEnum.MANUAL.equals(smsJobPO.getMode())) {
		    smsJobPOMapper.clearAutoTimeSet(smsJobId);
	    }
        operationLogService.onSmsJobModify(smsJobId, smsJobPO.getTenantId(), smsJobPO.getUpdateUserId());
    }

    private void resolveUserAndOrg(List<SmsJobListInfoVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<Long> userIdSet = list.stream().map(SmsJobListInfoVO::getCreateUserId).collect(Collectors.toSet());
        Map<? extends Serializable, UserPO> userMap = userService.selectMapByKeyCollect(userIdSet);
        list.parallelStream().forEach(jobInfo -> {
            UserPO userInfo = userMap.get(jobInfo.getCreateUserId());
            if (userInfo != null) {
                jobInfo.setCreateUserName(userInfo.getName());

            }
        });
    }

    private void resolveJobStats(List<SmsJobListInfoVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //统计发送情况
        Map<Long, SmsJobStatsBO> map = getSmsJobStats(list.stream().map(SmsJobListInfoVO::getSmsJobId).collect(Collectors.toSet()));
        for (SmsJobListInfoVO item : list) {
            SmsJobStatsBO statsBO = map.get(item.getSmsJobId());
            if(statsBO == null){
                item.setCompletedTask(0L);
                item.setTaskCallTotal(0L);
            } else {
                item.setCompletedTask(statsBO.getCompletedCount());
                item.setTaskCallTotal(statsBO.getTotalCount());
            }
        }
    }

    private void willBeArchived(List<SmsJobListInfoVO> smsJobListInfoVOList) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(90);
        LocalDateTime endTime = startTime.plusDays(7);
        for (SmsJobListInfoVO infoVO : smsJobListInfoVOList) {
            boolean timeYes = startTime.isBefore(infoVO.getLastHeartBeatTime()) && endTime.isAfter(infoVO.getLastHeartBeatTime());
            infoVO.setWillBeArchived(!SmsJobStatusEnum.ARCHIVED.equals(infoVO.getStatus()) && timeYes);
        }
    }

    @TargetDataSource(value = DataSourceEnum.SLAVE)
    @Override
    public PageResultObject<SmsJobListInfoVO> getSmsJobListInfo(SmsJobQueryVO smsJobQueryVO) {
        List<SmsJobListInfoVO> list;
        if(BooleanUtils.isNotTrue(smsJobQueryVO.getOpenApi())) {
            UserPO userPO = smsJobQueryVO.getUserPO();
            // 获取可查看的创建人列表
            Optional<List<Long>> authUserIdList = dataAccessControlService.getAuthUserIdList(userPO.getUserId(), AuthResourceUriEnum.crm_SMS_company);

            PageHelper.startPage(smsJobQueryVO.getPageNum(), smsJobQueryVO.getPageSize());
            list = smsJobPOMapper.searchSmsJob(userPO.getTenantId(), smsJobQueryVO.getName(), smsJobQueryVO.getStatus(),
                    smsJobQueryVO.getBeginCreateDate(), smsJobQueryVO.getEndCreateDate(), authUserIdList.orElse(null),
                    smsJobQueryVO.getStatusSet());
        }else{
            PageHelper.startPage(smsJobQueryVO.getPageNum(), smsJobQueryVO.getPageSize());
            list = smsJobPOMapper.searchSmsJob(smsJobQueryVO.getTenantId(), smsJobQueryVO.getName(), smsJobQueryVO.getStatus(),
                    smsJobQueryVO.getBeginCreateDate(), smsJobQueryVO.getEndCreateDate(), null,
                    smsJobQueryVO.getStatusSet());
        }

        resolveUserAndOrg(list);
        resolveJobStats(list);
        willBeArchived(list);
        return PageResultObject.of(list);
    }

    @Override
    public SmsJobDetailInfoVO getSmsJobInfo(Long tenantId, Long smsJobId) {
        SmsJobPO smsJobPO = selectByKey(smsJobId);
        if(smsJobPO == null || !tenantId.equals(smsJobPO.getTenantId())){
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "任务不存在", "任务已被删除id:" + smsJobId);
        }
        SmsJobDetailInfoVO info = new SmsJobDetailInfoVO();
        info.setSmsJob(smsJobPO);
        SmsTemplatePO smsTemplatePO = smsTemplateService.getSmsTemplatePO(tenantId, smsJobPO.getSmsTemplateId());
        SmsSignaturePO smsSignaturePO = new SmsSignaturePO();
        if(Objects.nonNull(smsTemplatePO)) {
            smsSignaturePO = smsSignatureService.selectByKey(smsTemplatePO.getSmsSignatureId());
            if (smsTemplatePO.getUnsubscribeType() != null
                    && !UnsubscribeTypeEnum.NONE.equals(smsTemplatePO.getUnsubscribeType())) {
                smsTemplatePO.setText(smsTemplatePO.getText() + smsTemplatePO.getUnsubscribeType().getDesc());
            }
        }
        info.setSmsTemplate(smsTemplatePO);
        info.setSmsSignatureName(smsSignaturePO.getName());

        // 用户信息
        Try.run(() -> {
            UserPO userInfo = userService.selectUserFromDB(smsJobPO.getCreateUserId());
            info.setCreatedByUserName(userInfo.getName());
            Long organizationId = userService.queryUserOrganizationId(userInfo.getUserId(), SystemEnum.CRM);

            if (organizationId != null && organizationId > 0) {
                OrganizationPO organization = organizationService.selectByKey(organizationId);
                info.setOrganizationName(organization.getName());
            }
        }).onFailure(e -> logger.error("获取任务创建者信息失败CreateUserId={}", smsJobPO.getCreateUserId(), e));

        // 统计发送情况
        SmsJobStatsBO statsBO = getSmsJobStats(smsJobId);
        if (statsBO != null) {
            Map<String , Long> map = statsBO.getSmsJobStatus();
            Map<String, Long> recvStatus = statsBO.getRecvStatus();
            if (map != null) {
                info.setSmsSuccessCount(map.get(SendMessageStatusEnum.SEND_SUCCESSFUL.name()) == null ? 0L : map.get(SendMessageStatusEnum.SEND_SUCCESSFUL.name()));
                info.setSmsFailCount(map.get(SendMessageStatusEnum.SEND_FAILURE.name()) == null ? 0L : map.get(SendMessageStatusEnum.SEND_FAILURE.name()));
                info.setSmsWaitCount(map.get(SendMessageStatusEnum.SEND_WAIT.name()) == null ? 0L : map.get(SendMessageStatusEnum.SEND_WAIT.name()));
            }
            if (recvStatus != null) {
                info.setSmsRecvingCount(recvStatus.get(SendMessageStatusEnum.RECV_ING.name()) == null ? 0L : recvStatus.get(SendMessageStatusEnum.RECV_ING.name()));
                info.setSmsRecvSuccessCount(recvStatus.get(SendMessageStatusEnum.RECV_SUCCESS.name()) == null ? 0L : recvStatus.get(SendMessageStatusEnum.RECV_SUCCESS.name()));
                info.setSmsRecvFailCount(recvStatus.get(SendMessageStatusEnum.RECV_FAILURE.name()) == null ? 0L : recvStatus.get(SendMessageStatusEnum.RECV_FAILURE.name()));

            }
        }

        //判断模板是否有变量
        if (smsTemplatePO != null) {
            info.setHasPlaceholder(SmsHelper.hasPlaceholder(smsTemplatePO.getText()));
        } else {
            info.setHasPlaceholder(false);
        }
        if (Objects.nonNull(smsJobPO.getRiskControlStrategyId())) {
            info.setRiskControlStrategy(rcsApiClient.getRcsDetail(smsJobPO.getRiskControlStrategyId()));
        }
        if (CollectionUtils.isNotEmpty(smsJobPO.getCustomerBlackGroupIds())) {
            info.setCustomerBlackGroupList(customerBlackGroupRemoteClient.selectCustomerBlackGroupByIdList(new ArrayList<>(smsJobPO.getCustomerBlackGroupIds())));
        }
        if (CollectionUtils.isNotEmpty(smsJobPO.getBrandBlackGroupIds())) {
            info.setBrandBlackGroupList(customerBlackGroupRemoteClient.selectCustomerBlackGroupByIdList(new ArrayList<>(smsJobPO.getBrandBlackGroupIds())));
        }
        return info;
    }

    @Override
    public PageResultObject<SmsJobMessageVO> getSmsJobMessageByJobId(Long tenantId, Long smsTemplateId, List<Long> smsJobIds, SendMessageStatusEnum sendStatus, String customerPersonName, String phoneNumber, LocalDateTime createBeginTime, LocalDateTime createEndTime, Integer pageNum, Integer pageSize, String reportStatus,Long costCount,SmsTypeEnum smsType) {
        Assert.notNull(tenantId, "租户id不能为空");
        List<Long> smsTemplateIds = new ArrayList<>();
        if(smsType != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(tenantId, smsType);
            if(CollectionUtils.isEmpty(smsTemplateIds)){
                return PageResultObject.of(Collections.emptyList());
            }
        }
        List<SmsJobMessageVO> intentMessagePOList;
        intentMessagePOList = smsJobMessagePOMapper.getSmsJobMessageListBySmsJob(tenantId, smsTemplateId, smsJobIds, sendStatus, customerPersonName, phoneNumber, createBeginTime, createEndTime, reportStatus,costCount,smsTemplateIds, (pageNum - 1) * pageSize, pageSize);

        if(intentMessagePOList != null && !intentMessagePOList.isEmpty()) {
            Set<Long> jobIdList = intentMessagePOList.stream().map(SmsJobMessagePO::getSmsJobId).collect(Collectors.toSet());
            Set<Long> userIds = intentMessagePOList.stream().map(SmsJobMessagePO::getCreateUserId).collect(Collectors.toSet());
            List<SmsJobPO> jobPOList = smsJobPOMapper.selectSmsJobByIdList(new ArrayList<>(jobIdList));
            Map<? extends Serializable, UserPO> userPOMap = userService.selectMapByKeyCollect(userIds);
            Map<Long , String> map = jobPOList.stream().collect(Collectors.toMap(SmsJobPO::getSmsJobId, SmsJobPO::getName));
            Set<Long> smsTemplateIdSet = intentMessagePOList.stream().map(SmsJobMessageVO::getSmsTemplateId).collect(Collectors.toSet());
            Map<Long,SmsTemplateTypeVO> smsTemplatePOMap = smsTemplateService.getSmsTemplateByIdSet(smsTemplateIdSet);
            intentMessagePOList.forEach(d -> {
                d.setSmsJobName(map.get(d.getSmsJobId()));
                UserPO userPO = userPOMap.get(d.getCreateUserId());
                if (userPO != null) {
                    d.setSendUserName(userPO.getName());
                }
                if (Objects.isNull(d.getSendTime())) {
                    d.setSendTime(d.getCreateTime());
                }
                if(smsTemplatePOMap.containsKey(d.getSmsTemplateId())){
                    d.setSmsType(smsTemplatePOMap.get(d.getSmsTemplateId()).getSmsType());
                }
            });
        }
        return PageResultObject.of(intentMessagePOList);
    }

    @Override
    public int getSmsJobMessageCountByJobId(Long tenantId, Long smsTemplateId, List<Long> smsJobIds, SendMessageStatusEnum sendStatus, String customerPersonName, String phoneNumber, LocalDateTime createBeginTime, LocalDateTime createEndTime, String reportStatus,Long costCount,SmsTypeEnum smsType) {
        List<Long> smsTemplateIds = new ArrayList<>();
        if(smsType != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(tenantId, smsType);
        }
        return smsJobMessagePOMapper.getSmsJobMessageCountBySmsJob(tenantId, smsTemplateId, smsJobIds, sendStatus, customerPersonName, phoneNumber, createBeginTime, createEndTime, reportStatus,costCount,smsTemplateIds);
    }


    @Override
    public void execute(Long tenantId, Long smsJobId, JobOperationEnum operation, Long userId) {
        SmsJobPO smsJobPO = smsJobPOMapper.selectByPrimaryKey(smsJobId);
        if(smsJobPO == null){
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "无此任务");
        }

	    LocalTime nowTime = LocalTime.now();
	    if (SmsJobModeEnum.AUTO.equals(smsJobPO.getMode()) && (nowTime.isBefore(smsJobPO.getDailyStartTime()) || nowTime.isAfter(smsJobPO.getDailyEndTime()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未到自动发送短信任务时间，不支持手动启动");
        }

        SmsJobPO updateJob = new SmsJobPO();
        updateJob.setSmsJobId(smsJobId);
        int waitTask = 0;
        if (JobOperationEnum.START.equals(operation)) {
            //短信自动续费
            tenantAutoSetService.tenantAutoPay(tenantId,null, TenantAutoSetTypeEnum.MESSAGE);
            //进行总账户余额预警消息推送
            tenantAutoSetService.doPushWarn(tenantId,null, Sets.newHashSet(TenantAutoSetTypeEnum.ALL_ACCOUNT_FARE));
            if(SmsJobStatusEnum.isStartAble(smsJobPO.getStatus())){
                // 检查所有正在运行中的子任务列表
                int runningTask = smsJobTaskPOMapper.countRunningTask(smsJobId, SendMessageStatusEnum.SENDING);
                if (runningTask > 0) {
                    throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "已存在" + runningTask + "个发送中的短信，任务id:" + smsJobId);
                }

                waitTask = smsJobTaskPOMapper.countRunningTask(smsJobId, SendMessageStatusEnum.SEND_WAIT);
                if (waitTask == 0) {
                    throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "不存在待发送的短信，任务id:" + smsJobId);
                }

                if (smsJobPO.getFirstStartDate() == null) {
                    updateJob.setFirstStartDate(LocalDate.now());
                }

                updateJob.setStatus(SmsJobStatusEnum.RUNNABLE);

	            LocalDateTime now = LocalDateTime.now();
	            if (SmsJobModeEnum.MANUAL.equals(smsJobPO.getMode())) {
                    updateJob.setNextRunTime(now);
                    LocalDateTime nextStart = checkStartTime(updateJob.getNextRunTime(), tenantId, smsJobPO);
                    if(nextStart.compareTo(now) > 0){
                        updateJob.setNextRunTime(nextStart);
                        updateJob.setStatus(SmsJobStatusEnum.SYSTEM_SUSPENDED);
                        updateJob.setHangUpType(SmsJobHangUpTypeEnum.TIME_NOT_AVAILABLE);
                        updateJob.setHangUpReason("任务将在" + MyDateUtils.formatLocalDateTime(nextStart) +"启动，请耐心等待");
                    }
                } else {
                    LocalDateTime nextStart = checkStartTime(now, tenantId, smsJobPO);
                    updateJob.setNextRunTime(nextStart);
                    if (nextStart.compareTo(now) > 0) {
                        updateJob.setNextRunTime(nextStart);
                        updateJob.setStatus(SmsJobStatusEnum.SYSTEM_SUSPENDED);
                        updateJob.setHangUpType(SmsJobHangUpTypeEnum.TIME_NOT_AVAILABLE);
                        updateJob.setHangUpReason("任务将在" + MyDateUtils.formatLocalDateTime(nextStart) +"启动，请耐心等待");
                    }
                }

            } else {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "该任务状态(" + smsJobPO.getStatus().getDesc() + ")不可开始", "该任务状态不可开始 当前状态:" + smsJobPO.getStatus());
            }
        } else if (JobOperationEnum.PAUSE.equals(operation)) {
            if (SmsJobStatusEnum.isPauseAble(smsJobPO.getStatus())) {
                updateJob.setStatus(SmsJobStatusEnum.USER_PAUSE);
            } else {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "该任务状态(" + smsJobPO.getStatus().getDesc() + ")不可暂停", "该任务状态不可暂停 当前状态:" + smsJobPO.getStatus());
            }
        } else if (JobOperationEnum.TERMINATE.equals(operation)) {
            if (SmsJobStatusEnum.isTerminateAble(smsJobPO.getStatus())) {
                updateJob.setStatus(SmsJobStatusEnum.TERMINATE);
            } else {
                throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "该任务状态(" + smsJobPO.getStatus().getDesc() + ")不可终止", "该任务状态不可终止 当前状态:" + smsJobPO.getStatus());
            }
        } else {
            throw new ComException(ComErrorCode.REQUEST_PARAM_ERROR, "任务操作类型错误", "操作类型错误 " + operation);
        }
        if (SmsJobStatusEnum.RUNNABLE.equals(updateJob.getStatus()) && JobOperationEnum.START.equals(operation)) {
            //检查费用
            SmsTemplateInfoBO smsTemplateInfo = smsTemplateService.getSmsTemplateInfo(smsJobPO.getTenantId(), smsJobPO.getSmsTemplateId());
            String messageText = MessageFormat.format("【{0}】{1}", smsTemplateInfo.getSmsSignatureName(), smsTemplateInfo.getText());
            SmsPlatformChannelWithTenantPriceDTO smsPlatformChannel = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(tenantId, smsTemplateInfo.getSmsPlatformChannelId(), smsTemplateInfo.getNewSmsPlatform());
            long cost = waitTask * smsService.getSmsCount(messageText) * smsPlatformChannel.getRealSmsPrice();
            TenantPO tenantPO = tenantPOMapper.findTenantByTenantId(tenantId);
            //进行短信预警消息推送
            tenantAutoSetService.doPushWarn(tenantId,null, Sets.newHashSet(TenantAutoSetTypeEnum.MESSAGE));
            String res = judgeAccountFare(tenantPO, smsPlatformChannel, cost);
            if (StringUtils.isNotBlank(res)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, res);
            }
            if (tenantPO.getAccountFare() <= ACCOUNT_FARE) {
                tenantService.isAccountDebt(tenantId, true);
            }
            //检查模板状态
            if (!EnabledStatusEnum.ENABLE.equals(smsTemplateInfo.getEnabledStatus())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务使用的短信模板已删除");
            }
            if (BooleanUtils.isFalse(smsTemplateInfo.getServingStatus())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务使用的短信模板已停用");
            }
        }
        updateJob.setUpdateUserId(userId);
        updateNotNull(updateJob);
        operationLogService.onSmsJobExecution(smsJobPO.getSmsJobId(), smsJobPO.getTenantId(), userId, operation);
    }

    @Override
    public void checkAndUpdateSmsJobStatus() {
        smsJobPOMapper.checkAndUpdateSmsJobStatus();
    }

    @Override
    public void deleteRobotCallJob(Long smsJobId, Long tenantId, Long userId) {
        SmsJobPO update = new SmsJobPO();
        update.setEnabledStatus(Boolean.FALSE);
        update.setUpdateUserId(userId);
        update.setSmsJobId(smsJobId);
        updateNotNull(update);
        operationLogService.onSmsJobDelete(smsJobId, tenantId, userId);
    }

    @Override
    public SmsJobPO getSmsJobAvailableAndLock() {
        // 用于保存在获取任务中出错后的任务的id
        AtomicLong atomicSmsJobId = new AtomicLong(-1);
        int runningCount = smsJobPOMapper.selectRunningCount(SERVER_IP_ADDRESS);
        if (MAX_RUNNING_COUNT <= runningCount) {
            logger.info("短信任务已达上限");
            return null;
        }

        return RetryRunUtils.retryOrThrowComException(() -> getAvailableSmsJob(atomicSmsJobId), 5, 10, true, "获取smsJobPO出错, JobId=" + atomicSmsJobId.get());
    }

    @Transactional(noRollbackFor = ComNoRollbackException.class)
    public SmsJobPO getAvailableSmsJob(AtomicLong atomicSmsJobId) {
        SmsJobPO smsJobPO = smsJobPOMapper.getAvailableSmsJob(SERVER_IP_ADDRESS, CommonApplicationConstant.CURR_ENV.isPre());
        if (smsJobPO != null) {
            long smsJobId = smsJobPO.getSmsJobId();
            MDC.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID") + "_SMSJobId_" + smsJobId);

            atomicSmsJobId.set(smsJobId);

            TenantPO tenantPO = tenantPOMapper.findTenantByTenantId(smsJobPO.getTenantId());
            SmsTemplateInfoBO smsTemplateInfoBO = smsTemplateService.getSmsTemplateInfo(tenantPO.getTenantId(), smsJobPO.getSmsTemplateId());
            SmsPlatformChannelWithTenantPriceDTO smsPlatformChannel = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(tenantPO.getTenantId(), smsTemplateInfoBO.getSmsPlatformChannelId(), smsTemplateInfoBO.getNewSmsPlatform());
            String messageText = MessageFormat.format("【{0}】{1}", smsTemplateInfoBO.getSmsSignatureName(), smsTemplateInfoBO.getText());
            int waitTask = smsJobTaskPOMapper.countRunningTask(smsJobId, SendMessageStatusEnum.SEND_WAIT);
            long cost = waitTask * smsService.getSmsCount(messageText) * smsService.getSingleSmsCost(smsTemplateInfoBO.getSmsPlatformChannelId(), tenantPO.getTenantId(), smsTemplateInfoBO.getNewSmsPlatform());
            // 进行质检预警消息推送
            tenantAutoSetService.doPushWarn(tenantPO.getTenantId(), null, Sets.newHashSet(TenantAutoSetTypeEnum.MESSAGE, TenantAutoSetTypeEnum.ALL_ACCOUNT_FARE));
            // 短信自动续费
            tenantAutoSetService.tenantAutoPay(tenantPO.getTenantId(), null, TenantAutoSetTypeEnum.MESSAGE);

            String res = judgeAccountFare(tenantPO, smsPlatformChannel, cost);
            if (StringUtils.isNotBlank(res)) {
                smsJobPOMapper.updateSmsJobStatusAndLastHeartBeatTimeByIdNotUpdateLastModifyTime(smsJobId, SmsJobStatusEnum.SYSTEM_HANG_UP, SmsJobHangUpTypeEnum.ACCOUNT_NOT_ENOUGH.getMsg(), SmsJobHangUpTypeEnum.ACCOUNT_NOT_ENOUGH);
                throw new NoRetryAndNoRollbackException(PRECONDITION_FAILED, "用户账户余额不足", res);
            }

            int versionNumber = smsJobPO.getVersionNumber();
            // TODO update next start time
            boolean updateSuccess = smsJobPOMapper.updateSmsJobToRunningByVersion(smsJobId, versionNumber, SERVER_IP_ADDRESS, SERVER_HOSTNAME) == 1;
            if (!updateSuccess) {
                // 重置线路状态
                throw new ComNoRollbackException(OPTIMISTIC_LOCK_VERSION_ERROR);
            }
            logger.debug("[LogHub]获取到可执行Job, JobId={}.", smsJobId);
            return smsJobPO;
        }
        return null;
    }

    @Override
    public Integer addCustomerPersonToSmsJob(AccountVO accountVO, Long smsJobId, Long tenantId, Long currentUserId, Map<String, String> properties) {
        Assert.notNull(smsJobId, "任务id不可以是null");
        Assert.notNull(tenantId, "租户id不可以是null");
        Assert.notNull(currentUserId, "用户id不可以是null");
        if (accountVO == null) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "添加到任务的客户不可以为空");
        }
        SmsJobPO smsJobPO = smsJobPOMapper.selectByPrimaryKey(smsJobId);
        Assert.notNull(smsJobPO, "短信任务不存在");
        SmsTemplateInfoBO templateInfoBO = smsTemplateService.getSmsTemplateInfo(tenantId, smsJobPO.getSmsTemplateId());
        Assert.notNull(templateInfoBO, "短信模板不存在");

        logger.debug("开始将{}加入任务{}，此时的租户id是{}，用户id是{}", accountVO, smsJobPO.getName(), tenantId, currentUserId);

        List<SmsJobTaskPO> smsJobTaskPOS = smsJobTaskPOMapper.selectBySmsJobIdAndPhoneNumbers(smsJobId, Collections.singletonList(accountVO.getMainPhoneNumber()));
        if(CollectionUtils.isNotEmpty(smsJobTaskPOS)) {
            logger.warn("客户{}已在该任务中", accountVO.getMainPhoneNumber());
            return 0;
        }

        SmsJobTaskPO smsJobTaskPO = new SmsJobTaskPO();
        smsJobTaskPO.setSmsJobId(smsJobId);
        smsJobTaskPO.setTenantId(tenantId);
        smsJobTaskPO.setSendStatus(SendMessageStatusEnum.SEND_WAIT);
        smsJobTaskPO.setPhoneNumber(accountVO.getMainPhoneNumber());
        smsJobTaskPO.setCustomerPersonName(AccountHelper.getAccountName(accountVO, 20));
        // 不要求替换自定义变量, 这里先不处理一客一链变量
	    String messageText = SmsHelper.getContentWithSignature(templateInfoBO.getSmsSignatureName(),templateInfoBO.getText(),templateInfoBO.getSignPosition());
        smsJobTaskPO.setMessageDetail(messageText);
        smsJobTaskPO.setSmsTemplateId(smsJobPO.getSmsTemplateId());
        // 单个导入短信任务, properties
        smsJobTaskPO.setProperties(properties);
        
        smsJobTaskService.batchInsert(Collections.singletonList(MyBeanUtils.copy(smsJobTaskPO, SmsJobMessageImportVO.class)));

        resetSmsJobStatusWhenCallTaskAddToCallJob(smsJobPO);
        return 1;
    }


    @Override
    public void resetSmsJobStatusWhenCallTaskAddToCallJob(SmsJobPO smsJobPO) {
        // 任务现在处于已完成
        if (SmsJobStatusEnum.COMPLETED.equals(smsJobPO.getStatus())) {
            // 手动任务未开始
            if (SmsJobModeEnum.MANUAL.equals(smsJobPO.getMode())) {
                SmsJobPO update = new SmsJobPO();
                update.setSmsJobId(smsJobPO.getSmsJobId());
                update.setStatus(SmsJobStatusEnum.NOT_STARTED);
                updateNotNull(update);
            } else if (SmsJobStatusEnum.SYSTEM_HANG_UP.equals(smsJobPO.getStatus()) && SmsJobHangUpTypeEnum.FIN_NOT_ENOUGH.equals(smsJobPO.getHangUpType())) {
                Long totalCount = smsJobMessageService.countAvailableListBySmsJobId(smsJobPO.getSmsJobId());
                if (totalCount >= 300) {
                    SmsJobPO update = new SmsJobPO();
                    update.setSmsJobId(smsJobPO.getSmsJobId());
                    update.setStatus(SmsJobStatusEnum.NOT_STARTED);
                    updateNotNull(update);
                }
            }else {
                LocalDateTime nextStartTime = checkStartTime(smsJobPO.getNextRunTime(), smsJobPO.getTenantId(), smsJobPO);
                SmsJobPO update = new SmsJobPO();
                update.setSmsJobId(smsJobPO.getSmsJobId());
                if(nextStartTime.compareTo(LocalDateTime.now()) <= 0) {
                    update.setStatus(SmsJobStatusEnum.IN_QUEUE);
                }else {
                    update.setStatus(SmsJobStatusEnum.NOT_STARTED);
                }
                update.setNextRunTime(nextStartTime);
                updateNotNull(update);
            }
        }
    }

    public Map<Long, SmsJobStatsBO> getSmsJobStats(Set<Long> idSet) {
        Query query = new Query();
        query.addCriteria(Criteria.where("smsJobId").in(idSet));
        List<SmsJobStatsBO> totalList = mongoTemplate.find(query, SmsJobStatsBO.class, MongoCollectionNameCenter.SMS_JOB_STATS);
        // mongo的并发upsert导致多条数据需要手动兼容
	    Map<Long, SmsJobStatsBO> map = new HashMap<>();
	    for (SmsJobStatsBO smsJobStatsBO : totalList) {
		    SmsJobStatsBO info = map.get(smsJobStatsBO.getSmsJobId());
		    if (info == null) {
		    	info = new SmsJobStatsBO();
			    info.setSmsJobId(smsJobStatsBO.getSmsJobId());
		    	map.put(smsJobStatsBO.getSmsJobId(), info);
		    }
		    mergeStatsInfo(info, smsJobStatsBO);
	    }
        return map;
    }

    public SmsJobStatsBO getSmsJobStats(Long smsJobId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
	    List<SmsJobStatsBO> smsJobStatsList = mongoTemplate.find(query, SmsJobStatsBO.class, MongoCollectionNameCenter.SMS_JOB_STATS);
	    SmsJobStatsBO smsJobStatsBO = new SmsJobStatsBO();
	    smsJobStatsBO.setSmsJobId(smsJobId);
	    for (SmsJobStatsBO jobStatsBO : smsJobStatsList) {
	    	mergeStatsInfo(smsJobStatsBO, jobStatsBO);
	    }
	    return smsJobStatsBO;
    }

    @Override
    public void incrementJobStatusAfterSend(Long tenantId, Long smsJobId, SendMessageStatusEnum sendMessageStatus, Long value) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        Update update = new Update();
        // 发送结果
        update.inc("smsJobStatus."+sendMessageStatus.name(), value);
        // 待发送减少
        update.inc("smsJobStatus."+SendMessageStatusEnum.SEND_WAIT, -1*value);
        // 接收状态 发送成功增加接收状态
        if (SendMessageStatusEnum.SEND_SUCCESSFUL.equals(sendMessageStatus)) {
            update.inc("recvStatus." + SendMessageStatusEnum.RECV_ING, value);
        }
        update.inc("completedCount", value);
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.SMS_JOB_STATS);
    }

    @Override
    public void incrementJobStatusAfterImport(Long tenantId, Long smsJobId, Long value) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        Update update = new Update();
        // 待发送
        update.inc("smsJobStatus."+SendMessageStatusEnum.SEND_WAIT, value);
        // 总数
        update.inc("totalCount", value);
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.SMS_JOB_STATS);
    }

    @Override
    public void incrementJobStatusAfterRecv(Long tenantId, Long smsJobId, SendMessageStatusEnum sendMessageStatus, Long value) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        Update update = new Update();
        // 接收中
        update.inc("recvStatus."+SendMessageStatusEnum.RECV_ING, -1*value);
        // 接收状态
        update.inc("recvStatus."+sendMessageStatus.name(), value);
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.SMS_JOB_STATS);
    }

    @TargetDataSource(DataSourceEnum.SLAVE)
    @Override
    public String generateSmsJobUploadTemplateExcel(Long tenantId, Long smsJobId) {
        SmsJobPO smsJobPO = smsJobPOMapper.selectByPrimaryKey(smsJobId);
        SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(smsJobPO.getSmsTemplateId());
        Set<String> properties = SmsHelper.getPlaceholder(smsTemplatePO.getText());
        String filePath = TempFilePathKeyCenter.getSmsJobTemplateExcelFilePath(smsJobId, tenantId);
        String baseOssFileKey = OssKeyCenter.getSmsJobTemplateOssFileKey(tenantId, smsJobId);
        return generateExcel(properties, filePath, baseOssFileKey);
    }

    private String generateExcel(Set<String> properties, String filePath, String baseOssFileKey) {
        List<String> list = new ArrayList<>();
        list.add("客户名");
        list.add("联系电话");
        // 去除重复
        if (CollectionUtils.isNotEmpty(properties)) {
            Set<String> copyProperties = new HashSet<>(properties);
            copyProperties.removeAll(list);
            list.addAll(copyProperties);
        }

        List<String> needList = new ArrayList<>();
        needList.add("联系电话");
        needList.addAll(properties);
        ExcelTemplate templateExcel = new ExcelTemplate(list, needList);

        try {
            List<String> sheetList = new ArrayList<>();
            sheetList.add("红色：必填项！\n" +
                    "联系电话支持手机号！\n" +
                    "①手机号必须填写11位数字！\n");

            templateExcel.open(filePath, SHEET1);
            templateExcel.writeSheetInfo(SHEET2, sheetList);
            templateExcel.close();
        } catch (IOException e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "创建文件" + filePath + "报错", e);
        } catch (Exception e) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "创建Excel模板报错", e);
        }
        File templateFile = new File(filePath);
        String ossFileKey = baseOssFileKey + "/" + templateFile.getName();
        return objectStorageHelper.upload(ossFileKey, templateFile);
    }

    @Override
    public JobStartResultVO importSmsJobPhoneNumber(Long tenantId, Long currentUserId, String objectName, Long smsJobId, Boolean distinct, SystemEnum systemEnum) {
        String importFilePath = ExcelImportUtils.saveFile(objectName, "import/" + objectName);
        TenantPO tenantPO = tenantService.selectByKey(tenantId);
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", currentUserId);
        jobParametersBuilder.addString("IMPORT_FILE_PATH", objectName);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.CRM.name());
        Sheet sheet = ExcelUtils.openExcelSheet(importFilePath);
        Integer fileRowNum = sheet.getLastRowNum();
        List<String> headerList = ExcelImportUtils.getAndCheckHeaderList(sheet);
        logger.info("从文件{}中获得的行数为{}，表头为{}", importFilePath, fileRowNum, headerList);

        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerList));
        // 如果参数中存在呼叫任务id，则这些客户一定要导入私海中，因为不可能客户在公海中同时又在某个用户的任务中。
        SmsJobPO smsJobPO = smsJobPOMapper.selectByPrimaryKey(smsJobId);
        SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(smsJobPO.getSmsTemplateId());
        SmsSignaturePO smsSignaturePO = smsSignatureService.selectByKey(smsTemplatePO.getSmsSignatureId());
        jobParametersBuilder.addLong("SMS_TEMPLATE_ID", smsTemplatePO.getSmsTemplateId());
        jobParametersBuilder.addString("SMS_TEMPLATE_NAME", smsTemplatePO.getName());
        jobParametersBuilder.addString("SMS_TEMPLATE_TEXT", smsTemplatePO.getText());
        jobParametersBuilder.addString("SMS_SIGNATURE_NAME", smsSignaturePO.getName());
        jobParametersBuilder.addString("skipPhoneValidation", tenantPO.getSkipPhoneValidation() + "");
        Set<String> properties = SmsHelper.getPlaceholder(smsTemplatePO.getText());
        jobParametersBuilder.addString("SMS_VAR", JsonUtils.object2String(properties));
        properties.addAll(Arrays.asList(DEFAULT_HEAD));
        jobParametersBuilder.addString(JOB_REQUIRED_PROPERTIES, JsonUtils.object2String(properties));

        String importErrorFileOssKey = OssKeyCenter.getExcelOssFileKey(IMPORT_ERROR_FILE_PREFIX, tenantId, currentUserId, now.toString());
        jobParametersBuilder.addString("OSS_FILE_KEY", importErrorFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(importErrorFileOssKey));
        jobParametersBuilder.addLong("SMS_JOB_ID", smsJobId);
        jobParametersBuilder.addLong("JOB_CREATE_USER_ID", smsJobPO.getCreateUserId());
        jobParametersBuilder.addString("DISTINCT", distinct.toString());
        String jobName = "导入手机号-" + smsJobPO.getName();
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        smsJobImportJob,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        fileRowNum,
                        currentUserId,
                        SpringBatchJobTypeEnum.IMPORT_PHONE_NUMBER_TO_JOB,
                        Objects.isNull(systemEnum) ? SystemEnum.CRM : systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public PageResultObject<SmsJobTaskPO> getNotSendMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return PageResultObject.of(smsJobTaskService.selectNotSendMessageListBySmsJobId(smsJobId));
    }

    @Override
    public PageResultObject<SmsJobMessagePO> getSentMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return PageResultObject.of(smsJobMessagePOMapper.getSentMessageList(tenantId, smsJobId));
    }

    @Override
    public void updateSmsJobDateStatsWithMap(Long tenantId, Long distributorId, TenantPayTypeEnum tenantPayType, Long smsJobId, SmsTypeEnum smsType, Long userId, LocalDate localDate, Map<String, Long> map) {
        if(map == null || map.isEmpty()) {
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
        query.addCriteria(Criteria.where("distributorId").is(distributorId));
        query.addCriteria(Criteria.where("tenantPayType").is(tenantPayType));
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        query.addCriteria(Criteria.where("userId").is(userId));
        query.addCriteria(Criteria.where("year").is(localDate.getYear()));
        query.addCriteria(Criteria.where("month").is(localDate.getMonthValue()));
        query.addCriteria(Criteria.where("day").is(localDate.getDayOfMonth()));
        query.addCriteria(Criteria.where("date").is(localDate));
        Update update = new Update();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            update.inc(entry.getKey(), entry.getValue());
        }
        mongoTemplate.upsert(query, update, MongoCollectionNameCenter.SMS_JOB_DATE_STATS);
    }

    @Override
    public PageResultObject<SmsJobListInfoVO> searchSmsJob(SmsJobQueryVO smsJobQueryVO) {
        PageHelper.startPage(smsJobQueryVO.getPageNum(), smsJobQueryVO.getPageSize());
        UserPO userPO = smsJobQueryVO.getUserPO();
        List<SmsJobListInfoVO> list = smsJobPOMapper.searchSmsJob(userPO.getTenantId(), smsJobQueryVO.getName(), smsJobQueryVO.getStatus(),
                smsJobQueryVO.getBeginCreateDate(), smsJobQueryVO.getEndCreateDate(), null,
                smsJobQueryVO.getStatusSet());
        return PageResultObject.of(list);
    }

    @Override
    public JobStartResultVO export(SmsRecordQueryVO smsRecordQueryVO) {
        List<Long> smsTemplateIds;
        if(smsRecordQueryVO.getSmsType() != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(smsRecordQueryVO.getTenantId(), smsRecordQueryVO.getSmsType());
            smsRecordQueryVO.setSmsTemplateIds(smsTemplateIds);
        }
        List<Tuple3<String, String, Object>> params = Lists.newArrayList(
                Tuple.of("EXPORT_REQUEST", "Object", smsRecordQueryVO)
        );
        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_GROUPSMS, PlatformTypeEnum.AICC);
        ArrayList<SheetInfoDTO> sheetInfoDTOS = Lists.newArrayList(SheetInfoDTO.of(BatchConstant.SheetName.EXPORT_GROUP_SMS_RECORD, headerListCache));
        return basicBatchService.exportWithQuery(DataSourceEnum.MASTER, smsRecordQueryVO.getTenantId(), smsRecordQueryVO.getCurrentUserId(), SystemEnum.SMS_PLATFORM, exportSmsJobMessageRecordJob, EXPORT_GROUP_SMS_RECORD, params, sheetInfoDTOS);
    }

    @Override
    public void updateSmsOperatorStatsWithMap(Long callJobId, Map<String, Long> map) {
        if (map == null || map.isEmpty()) {
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("smsJobId").is(callJobId));
        Update update = new Update();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            update.inc(entry.getKey(), entry.getValue());
        }
        callStatsMongoService.updateMongoData(MongoCollectionNameCenter.SMS_JOB_OPERATOR_STATS, query, update);
    }

    @Override
    public void updateSmsOperatorStats(Long smsJobId, String key, Long value) {
        Map<String, Long> map = new HashMap<>();
        map.put(key, value);
        updateSmsOperatorStatsWithMap(smsJobId, map);
    }

    @Override
    public SmsJobMobileOperatorStatsBO getSmsJobOperatorStats(Long smsJobId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        // 查询mongo获取需要统计的数据
	    return mongoTemplate.findOne(query, SmsJobMobileOperatorStatsBO.class, MongoCollectionNameCenter.SMS_JOB_OPERATOR_STATS);
    }

    @Override
    public PageResultObject<SmsJobMessagePO> getAllSentMessageList(Long tenantId, Long smsJobId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return PageResultObject.of(smsJobMessagePOMapper.getAllSentMessageList(tenantId, smsJobId));
    }

    @Override
    public void deleteSmsJobTask(SmsJobTaskDeleteVO smsJobTaskDeleteVO) {
        Assert.notNull(smsJobTaskDeleteVO.getSmsJobId(), "短信任务ID不能为空");
        SmsJobPO smsJobPO = selectByKey(smsJobTaskDeleteVO.getSmsJobId());
        Assert.notNull(smsJobPO, "短信任务不存在");
        if (!SmsJobStatusEnum.isEditable(smsJobPO.getStatus())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "只有暂停的任务才能删除短信");
        }
        if (CollectionUtils.isNotEmpty(smsJobTaskDeleteVO.getSmsJobTaskIds())) {
            //删除选中的未发送短信记录
            int successCount = smsJobTaskService.deleteSendWaitBySmsJobTaskIds(smsJobTaskDeleteVO.getSmsJobTaskIds());
            if (successCount > 0) {
                incrementJobStatusAfterImport(smsJobPO.getTenantId(), smsJobPO.getSmsJobId(), -1L * successCount);
            }
        } else {
            //删除全部的未发送短信记录
            smsJobTaskService.deleteSendWaitBySmsJobId(smsJobTaskDeleteVO);
        }
    }

    @Override
    public void archive() {
        int successCount = smsJobPOMapper.updateUnarchivedJobIdByIntervalDay(ApplicationConstant.ARCHIVE_DAYS);
        log.info("SmsJobServiceImpl archive successCount={}", successCount);
        while (successCount > 0) {
            successCount = smsJobPOMapper.updateUnarchivedJobIdByIntervalDay(ApplicationConstant.ARCHIVE_DAYS);
            log.info("SmsJobServiceImpl archive successCount={}", successCount);
        }
        log.info("SmsJobServiceImpl archive finished");
    }

    @Override
    public void cleanTaskByArchivedJob() {

        //先更新归档任务
        archive();

        Long smsJobId = smsJobPOMapper.selectNotCleanedArchivedJobId();
        while (smsJobId != null) {
            deleteTaskBySmsJobId(smsJobId);
            int hour = LocalTime.now().getHour();
            if (hour >= ApplicationConstant.START_HOUR && hour <= getEndHour()) {
                log.info("[LogHub]删除sms_job_task等待被中断, smsJobId=[{}]", smsJobId);
                return;
            }
            smsJobId = smsJobPOMapper.selectNotCleanedArchivedJobId();
        }
    }

    @Override
    public Map<String, LocalTime> getFilterTime(Long tenantId) {
        RiskControlStrategyDTO systemRcs = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
        Map<String, LocalTime> result = new HashMap<>(4);
        result.put("startTime", systemRcs.getDialIntervalStart());
        result.put("endTime", systemRcs.getDialIntervalEnd());
        return result;
    }

    @Override
    public List<SmsJobMessageVO> getListByCondition(List<Long> idList) {
        List<SmsJobMessageVO> smsJobMessagePOList = smsJobMessagePOMapper.getListByIds(idList);
        if (smsJobMessagePOList != null && !smsJobMessagePOList.isEmpty()) {
            Set<Long> jobIdList = smsJobMessagePOList.stream().map(SmsJobMessageVO::getSmsJobId).collect(Collectors.toSet());
            Set<Long> userIds = smsJobMessagePOList.stream().map(SmsJobMessageVO::getCreateUserId).collect(Collectors.toSet());
            List<SmsJobPO> jobPOList = smsJobPOMapper.selectSmsJobByIdList(new ArrayList<>(jobIdList));
            Map<? extends Serializable, UserPO> userPOMap = userService.selectMapByKeyCollect(userIds);
            Map<Long, String> map = jobPOList.stream().collect(Collectors.toMap(SmsJobPO::getSmsJobId, SmsJobPO::getName));
            smsJobMessagePOList.forEach(d -> {
                d.setSmsJobName(map.get(d.getSmsJobId()));
                UserPO userPO = userPOMap.get(d.getCreateUserId());
                if (userPO != null) {
                    d.setSendUserName(userPO.getName());
                }
                if (Objects.isNull(d.getSendTime())) {
                    d.setSendTime(d.getCreateTime());
                }
            });
        }
        return smsJobMessagePOList;
    }

    @Override
    public JobStartResultVO importToSmsJob(SmsRecordQueryVO smsRecordQueryVO, Long tenantId, Long userId) {
        SystemEnum systemEnum = smsRecordQueryVO.getSystemType();
        if (systemEnum == null) {
            systemEnum = SystemEnum.CRM;
        }
        List<Long> smsTemplateIds = null;
        if(smsRecordQueryVO.getSmsType() != null) {
            smsTemplateIds  = smsTemplateService.selectBySmsType(smsRecordQueryVO.getTenantId(), smsRecordQueryVO.getSmsType());
            smsRecordQueryVO.setSmsTemplateIds(smsTemplateIds);
        }
        SmsJobDetailInfoVO targetJob = this.getSmsJobInfo(tenantId, smsRecordQueryVO.getTargetJobId());
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();

        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_JOB_TASK, PlatformTypeEnum.AICC);

        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addLong("SMS_JOB_ID", smsRecordQueryVO.getTargetJobId());
        jobParametersBuilder.addString("EXPORT_REQUEST", JsonUtils.object2String(smsRecordQueryVO));
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(IMPORT_RE_ADD_FILE, tenantId, userId, Long.toString(System.currentTimeMillis()));
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey));
        jobParametersBuilder.addString("SYSTEM_TYPE", systemEnum.name());
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerListCache));
        jobParametersBuilder.addString("PROPERTY_MAP", JsonUtils.object2String(smsRecordQueryVO.getPropertiesMap()));
        Integer totalCount;
        if (smsRecordQueryVO.getSelectAll()) {
            totalCount = smsJobMessagePOMapper.getSmsJobMessageCountBySmsJob(tenantId, null, smsRecordQueryVO.getSmsJobIds(), smsRecordQueryVO.getSendStatus(), smsRecordQueryVO.getCustomerPersonName(), smsRecordQueryVO.getPhoneNumber(), smsRecordQueryVO.getSendStartTime(), smsRecordQueryVO.getSendEndTime(), smsRecordQueryVO.getReportStatus(),smsRecordQueryVO.getBillingNum(),smsTemplateIds);
        } else {
            totalCount = smsRecordQueryVO.getIds().size();
        }

        JobParameters jobParameters = jobParametersBuilder.toJobParameters();
        Job job = groupSmsToSmsJob;

        String jobName = "群发短信历史导入到短信平台-短信任务 " + targetJob.getSmsJob().getName();
        SpringBatchJobTypeEnum jobType = IMPORT_GROUP_SMS_TO_SMS_JOB;
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    /**
     * 循环删除等待
     */
    private void deleteTaskBySmsJobId(Long smsJobId) {
        log.info("deleteTaskBySmsJobId smsJobId={}", smsJobId);
        int cnt = smsJobTaskPOMapper.deleteBySmsJobIdId(smsJobId, ApplicationConstant.ROBOT_CALL_JOB_ARCHIVE_CLEAN_BATCH_SIZE);
        while (cnt > 0) {
            try {
                Thread.sleep(ApplicationConstant.ROBOT_CALL_JOB_ARCHIVE_SLEEP_INTERVAL);
            } catch (InterruptedException e) {
                log.error("[LogHub_Warn]删除sms_job_task等待被中断, smsJobId=[{}]", smsJobId, e);
            }
            int hour = LocalTime.now().getHour();
            if (hour >= ApplicationConstant.START_HOUR && hour <= getEndHour()) {
                log.info("[LogHub]删除sms_job_task等待被中断, smsJobId=[{}]", smsJobId);
                return;
            }
            cnt = smsJobTaskPOMapper.deleteBySmsJobIdId(smsJobId, ApplicationConstant.ROBOT_CALL_JOB_ARCHIVE_CLEAN_BATCH_SIZE);
        }
        SmsJobPO update = new SmsJobPO();
        update.setSmsJobId(smsJobId);
        update.setTaskCleaned(true);
        updateNotNull(update);
        log.info("短信任务{}已清理", smsJobId);
    }

    private int getEndHour() {
        if (CommonApplicationConstant.CURR_ENV.isProd()) {
            // 生产环境20:00-8:00
            return 20;
        } else {
            // 测试环境9:00-8:00
            return 9;
        }
    }

    /**
	 * 统计数据整合, 将对象b的数据加到对象a上
	 */
	private void mergeStatsInfo(SmsJobStatsBO a, SmsJobStatsBO b) {
	    b.getSmsJobStatus().forEach((key, value) -> {
		    Map<String, Long> map = a.getSmsJobStatus();
		    map.merge(key, value, Long::sum);
	    });
	    b.getRecvStatus().forEach((key, value) -> {
		    Map<String, Long> map = a.getRecvStatus();
		    map.merge(key, value, Long::sum);
	    });
	    a.setCompletedCount(a.getCompletedCount() + b.getCompletedCount());
	    a.setTotalCount(a.getTotalCount() + b.getTotalCount());
    }

    @Override
    public void addSmsInterceptData(Long tenantId, Long mainBrandId, String phoneNumber) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("tenantId").is(tenantId));
            query.addCriteria(Criteria.where("phoneNumber").is(phoneNumber));
            query.addCriteria(Criteria.where("localDate").is(LocalDate.now()));
            Update update = new Update();
            update.set("mainBrandId", mainBrandId);
            //此条记录删除时间
            update.set("expireAt", LocalDate.now().plusDays(90).atTime(3, 30));
            //发送次数
            update.inc("daySend", 1);
            mongoTemplate.upsert(query, update, SmsInterceptPO.COLLECTION_NAME);
        }catch (Exception e){
            logger.error("addSmsInterceptData", e);
        }
    }

    @Override
    public Integer getSmsInterceptCount(Long tenantId, Long mainBrandId, String phoneNumber, LocalDate startDate, LocalDate endDate) {
        GroupOperation groupOperation = Aggregation.group().sum("daySend").as("daySend");

        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("localDate").gte(startDate).lte(endDate.plusDays(1))));
        aggregationOperationList.add(Aggregation.match(Criteria.where("phoneNumber").is(phoneNumber)));
        if (Objects.nonNull(tenantId)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("tenantId").is(tenantId)));
        }
        if (Objects.nonNull(mainBrandId)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("mainBrandId").is(mainBrandId)));
        }
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        List<Map> list = mongoTemplate.aggregate(aggregation, SmsInterceptPO.COLLECTION_NAME, Map.class).getMappedResults();
        if(CollectionUtils.isNotEmpty(list)){
            Map map = list.get(0);
            Object count = map.get("daySend");
            if(count != null) {
                return Integer.valueOf(count.toString());
            }
        }
        return null;
    }

	@Override
	public LocalDateTime checkStartTime(LocalDateTime time, Long tenantId, SmsJobPO smsJob) {
		RiskControlStrategyDTO riskControlStrategyDTO = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
		return getNextRunTime(time, riskControlStrategyDTO, smsJob.getFirstStartDate(), smsJob.getDailyStartTime(), smsJob.getDailyEndTime());
	}

	@Override
    public Boolean skipFilter(Long tenantId, Long rcsId, Set<Long> brandIds, Set<Long> customerIds) {
	    //任务上选择
	    boolean jobSkipFilter = false;
        boolean systemSkipFilter = false;
	    if(rcsId != null) {
            RiskControlStrategyDTO jobRcs = rcsApiClient.getRcsDetail(rcsId);
            if(jobRcs != null){
                if ((jobRcs.getOperatorAndAreaSwitch() == null || !jobRcs.getOperatorAndAreaSwitch()) &&
                        (jobRcs.getInterceptSwitch() == null || !jobRcs.getInterceptSwitch()) &&
                        (jobRcs.getBrandBlackGroupSwitch() == null || !jobRcs.getBrandBlackGroupSwitch()) &&
                        (jobRcs.getCustomerBlackGroupSwitch() == null || !jobRcs.getCustomerBlackGroupSwitch())
                ){
                    jobSkipFilter = true;
                }
            }
        }else {
            jobSkipFilter =  true;
        }

        RiskControlStrategyDTO riskControlStrategyDTO = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
        if(riskControlStrategyDTO != null){
            if ((riskControlStrategyDTO.getOperatorAndAreaSwitch() == null || !riskControlStrategyDTO.getOperatorAndAreaSwitch()) &&
                    (riskControlStrategyDTO.getInterceptSwitch() == null || !riskControlStrategyDTO.getInterceptSwitch()) &&
                    (riskControlStrategyDTO.getShareBlackGroupSwitch() == null || !riskControlStrategyDTO.getShareBlackGroupSwitch()) &&
                    (riskControlStrategyDTO.getCallOutBlackSwitch() == null || !riskControlStrategyDTO.getCallOutBlackSwitch())
            ){
                systemSkipFilter = true;
            }
        }
        return jobSkipFilter && systemSkipFilter && CollectionUtils.isEmpty(brandIds) && CollectionUtils.isEmpty(customerIds);
    }

    @Override
    public LocalTime getEndTime(Long tenantId, Long rcsId) {
        if(rcsId != null) {
            RiskControlStrategyDTO jobRcs = rcsApiClient.getRcsDetail(rcsId);
            return jobRcs.getDialIntervalEnd();
        }else {
            RiskControlStrategyDTO riskControlStrategyDTO = rcsApiClient.getSystemRcs(tenantId, ModelTypeEnum.SMS);
            if(riskControlStrategyDTO != null){
                return riskControlStrategyDTO.getDialIntervalEnd();
            }
        }
        return null;
    }

    @Override
    public List<SmsJobSimpleVO> selectSmsJobLimited(Long tenantId, String name) {
        return smsJobPOMapper.selectSmsJobLimited(tenantId, name);
    }

	private void checkCallJobTimeIsValid(RiskControlStrategyDTO riskControlStrategyDTO, LocalTime dailyStartTime, LocalTime dailyEndTime) {
		if (riskControlStrategyDTO == null) {
			return;
		}
		if (dailyStartTime.isBefore(riskControlStrategyDTO.getDialIntervalStart())) {
			throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务每日开始时间不能早于" + MyDateUtils.localTimeToStr(riskControlStrategyDTO.getDialIntervalStart()));
		}
		if (dailyEndTime.isAfter(riskControlStrategyDTO.getDialIntervalEnd())) {
			throw new ComException(ComErrorCode.VALIDATE_ERROR, "任务每日结束时间不能晚于" + MyDateUtils.localTimeToStr(riskControlStrategyDTO.getDialIntervalEnd()));
		}
	}

	/**
	 * 根据短信任务的启动日期、每日可发送时间段、风控限制时间，计算从输入的时刻开始下一个可运行时间
	 * @param inputDateTime  输入时刻
	 * @param riskControl    风控策略
	 * @param startDate      启动日期
	 * @param dailyStartTime 每日可发送时间段-开始
	 * @param dailyEndTime   每日可发送时间段-结束
	 * @return 从输入的时刻开始下一个可运行时间
	 */
	@NonNull
	private LocalDateTime getNextRunTime(@NonNull LocalDateTime inputDateTime, @Nullable RiskControlStrategyDTO riskControl,
	                                     @Nullable LocalDate startDate, @Nullable LocalTime dailyStartTime, @Nullable LocalTime dailyEndTime) {
		LocalDate inputDate = inputDateTime.toLocalDate();
		LocalTime inputTime = inputDateTime.toLocalTime();
		// duration in a day
		LocalTime startTime = dailyStartTime;
		LocalTime endTime = dailyEndTime;
		if (riskControl != null) {
			Optional<LocalTime[]> intersection = getLocalTimeIntersection(dailyStartTime, dailyEndTime, riskControl.getDialIntervalStart(), riskControl.getDialIntervalEnd());
			if (intersection.isPresent()) {
				startTime = intersection.get()[0];
				endTime = intersection.get()[1];
			}
		}
		if (startTime == null || endTime == null) {
			return inputDateTime;
		}
		if (startDate != null && inputDate.isBefore(startDate)) {
			return LocalDateTime.of(startDate, startTime);
		} else {
			if (inputTime.isBefore(startTime)) {
				return LocalDateTime.of(inputDate, startTime);
			} else if (inputTime.isAfter(endTime)) {
				return LocalDateTime.of(inputDate.plusDays(1), startTime);
			} else {
				return inputDateTime;
			}
		}
	}

	/**
	 * 计算2个时间段的交集
	 * @return 没有交集的话返回Optional.empty()
	 */
	private Optional<LocalTime[]> getLocalTimeIntersection(LocalTime startTime1, LocalTime endTime1, LocalTime startTime2, LocalTime endTime2) {
		if (startTime1 == null || endTime1 == null) {
			return Optional.of(new LocalTime[]{startTime2, endTime2});
		}
		LocalTime startTime = startTime1.isAfter(startTime2) ? startTime1 : startTime2;
		LocalTime endTime = endTime1.isBefore(endTime2) ? endTime1 : endTime2;
		if (startTime.isAfter(endTime)) {
			return Optional.empty();
		}
		return Optional.of(new LocalTime[]{startTime, endTime});
	}

    @Override
    public void addSmsTypeStats(Map<String, Long> map, SmsTypeEnum smsType, Long count, Long cost) {
        if (smsType == null) {
            return;
        }
        switch (smsType) {
            case VIRTUAL:
            case SMS: {
                map.put("smsCost2", cost);
                map.put("smsBillCount", count);
                break;
            }
            case MMS: {
                map.put("mmsCost", cost);
                map.put("mmsBillCount", count);
                break;
            }
            case FMS: {
                map.put("fmsCost", cost);
                map.put("fmsBillCount", count);
                break;
            }
            default:
        }
    }

    /**
     * 账户欠费校验
     */
    private String judgeAccountFare(TenantPO tenantPO, SmsPlatformChannelDTO smsPlatformChannel, long cost) {
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
	        TenantAccountEnum tenantAccount = OwnerTypeEnum.accountFare(smsPlatformChannel.getOwnerType()) ? TenantAccountEnum.ACCOUNT_FARE : TenantAccountEnum.DISTRIBUTOR_ACCOUNT_FARE;
	        if (tenantAccountClient.willBeInArrears(tenantPO.getTenantId(), tenantAccount, cost)) {
		        return "账户余额不足，需要 " + cost/1000.0 + "元";
	        }
        } else {
            if(cost > tenantPO.getAccountFare()){
                return "账户余额不足，需要 " + cost/1000.0 + "元";
            }
        }
        return "";
    }

    @Override
    public JobStartResultVO importSmsJobPhoneNumberByS3Url(GeTuiFileInfoBO fileInfo, CustomerPersonImportSmsJobByS3UrlRequestVO requestVO) {
        Long tenantId = requestVO.getTenantId();
        Long currentUserId = requestVO.getCurrentUserId();
        Long smsJobId = requestVO.getSmsJobId();
        Boolean distinct = requestVO.getDistinct();
        SystemEnum systemEnum = requestVO.getSystemType();

        String objectName = fileInfo.getObjectName();
        Integer fileRowNum = fileInfo.getCount().intValue();
        List<String> headerList = fileInfo.getHeaderList();
        TenantPO tenantPO = tenantService.selectByKey(tenantId);

        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", currentUserId);
        jobParametersBuilder.addString("IMPORT_FILE_PATH", objectName);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.CRM.name());

        logger.info("从文件{}中获得的行数为{}，表头为{}", objectName, fileRowNum, headerList);

        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerList));
        // 如果参数中存在呼叫任务id，则这些客户一定要导入私海中，因为不可能客户在公海中同时又在某个用户的任务中。
        SmsJobPO smsJobPO = smsJobPOMapper.selectByPrimaryKey(smsJobId);
        SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(smsJobPO.getSmsTemplateId());
        SmsSignaturePO smsSignaturePO = smsSignatureService.selectByKey(smsTemplatePO.getSmsSignatureId());
        jobParametersBuilder.addLong("SMS_TEMPLATE_ID", smsTemplatePO.getSmsTemplateId());
        jobParametersBuilder.addString("SMS_TEMPLATE_NAME", smsTemplatePO.getName());
        jobParametersBuilder.addString("SMS_TEMPLATE_TEXT", smsTemplatePO.getText());
        jobParametersBuilder.addString("SMS_SIGNATURE_NAME", smsSignaturePO.getName());
        jobParametersBuilder.addString("skipPhoneValidation", tenantPO.getSkipPhoneValidation() + "");
        Set<String> properties = SmsHelper.getPlaceholder(smsTemplatePO.getText());
        jobParametersBuilder.addString("SMS_VAR", JsonUtils.object2String(properties));
        properties.addAll(Arrays.asList(DEFAULT_HEAD));
        jobParametersBuilder.addString(JOB_REQUIRED_PROPERTIES, JsonUtils.object2String(properties));

        String importErrorFileOssKey = OssKeyCenter.getExcelOssFileKey(IMPORT_ERROR_FILE_PREFIX, tenantId, currentUserId, now.toString());
        jobParametersBuilder.addString("OSS_FILE_KEY", importErrorFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(importErrorFileOssKey));
        jobParametersBuilder.addLong("SMS_JOB_ID", smsJobId);
        jobParametersBuilder.addLong("JOB_CREATE_USER_ID", smsJobPO.getCreateUserId());
        jobParametersBuilder.addString("DISTINCT", distinct.toString());
        String jobName = "导入手机号-" + smsJobPO.getName();
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        geTuiSmsJobImportJob,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        fileRowNum,
                        currentUserId,
                        SpringBatchJobTypeEnum.GE_TUI_IMPORT_PHONE_NUMBER_TO_JOB,
                        Objects.isNull(systemEnum)?SystemEnum.CRM:systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }


}
