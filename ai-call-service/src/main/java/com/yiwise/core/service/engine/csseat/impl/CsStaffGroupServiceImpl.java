package com.yiwise.core.service.engine.csseat.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.dal.dao.*;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.ChannelSettingPO;
import com.yiwise.core.model.bo.knowledge.KnowledgeBaseInfoBO;
import com.yiwise.core.model.bo.phonenumber.FreeswitchGroupItemInfoBO;
import com.yiwise.core.model.bo.phonenumber.SipAccountInfoBO;
import com.yiwise.core.model.dto.CsSeatDTO;
import com.yiwise.core.model.dto.textservice.RobotNlpDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.StaffGroupTypeEnum;
import com.yiwise.core.model.enums.callin.StaffTypeEnum;
import com.yiwise.core.model.vo.csseat.*;
import com.yiwise.core.service.callin.CallInSeatDispatchService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.CustomerWhiteGroupService;
import com.yiwise.core.service.engine.OperationLogService;
import com.yiwise.core.service.engine.csseat.*;
import com.yiwise.core.service.engine.phonenumber.FreeswitchGroupService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.engine.qc.QcStaffGroupService;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.platform.DataAccessControlService;
import com.yiwise.core.service.platform.RobotService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.textservice.ChannelSettingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wangguomin
 * @Date: 2019-01-11 15:01
 */
@Slf4j
@Service
public class CsStaffGroupServiceImpl extends BasicServiceImpl<CsStaffGroupPO> implements CsStaffGroupService {
    @Resource
    private CsStaffGroupPOMapper csStaffGroupPOMapper;
    @Resource
    private CsStaffInfoPOMapper csStaffInfoPOMapper;
    @Resource
    private CsStaffGroupRelPOMapper csStaffGroupRelPOMapper;
    @Lazy
    @Resource
    private CallInSeatDispatchService callInSeatDispatchService;
    @Resource
    private CallInReceptionPOMapper callInReceptionPOMapper;
    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private RobotCallJobPOMapper robotCallJobPOMapper;
    @Resource
    private CsStaffGroupTransferService csStaffGroupTransferService;
    @Resource
    private TenantPhoneNumberPOMapper tenantPhoneNumberPOMapper;
    @Resource
    private CsStaffGroupPhoneNumberService csStaffGroupPhoneNumberService;
    @Resource
    private PhoneNumberService phoneNumberService;
    @Resource
    private FreeswitchGroupService freeswitchGroupService;
    @Resource
    private UserService userService;
    @Resource
    private CsStaffUserService csStaffUserService;
    @Resource
    private DistributorService distributorService;
    @Resource
    private TenantService tenantService;
    @Resource
    private CsStaffInfoService csStaffInfoService;
    @Resource
    private CsBatchCallJobPOMapper csBatchCallJobPOMapper;
    @Resource
    private RobotService robotService;
    @Resource
    private KnowledgeBaseInfoPOMapper knowledgeBaseInfoPOMapper;
    @Resource
    ChannelSettingService channelSettingService;
    @Resource
    private DataAccessControlService dataAccessControlService;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private QcStaffGroupService qcStaffGroupService;
    @Resource
    private CustomerWhiteGroupService customerWhiteGroupService;
    @Resource
    private OperationLogService operationLogService;

    @Override
    public PageResultObject<CsStaffGroupVO> selectListByPage(Long tenantId, Long userId, Integer pageNum, Integer pageSize, EnabledStatusEnum enabledStatus, StaffGroupTypeEnum groupType) {
        PageHelper.startPage(pageNum, pageSize);
        List<CsStaffGroupVO> list = csStaffGroupPOMapper.selectGroupByTenantIdAndStatus(tenantId, enabledStatus, groupType);
        if(list != null && !list.isEmpty()){
            List<Long> idList = list.stream().filter(group -> (group.getGroupType().equals(StaffGroupTypeEnum.CS) || group.getGroupType().equals(StaffGroupTypeEnum.TEXT)
                    || group.getGroupType().equals(StaffGroupTypeEnum.VOICE_TEXT))&& group.getCsStaffGroupId() > 0).map(CsStaffGroupVO::getCsStaffGroupId).collect(Collectors.toList());
            if(idList != null && !idList.isEmpty()) {
                List<CsStaffInfoVO> staffList = csStaffInfoPOMapper.selectByGroupIdList(idList);
                if (staffList != null && !staffList.isEmpty()) {
                    Map<Long, List<CsStaffInfoVO>> map = staffList.stream().collect(Collectors.groupingBy(CsStaffInfoVO::getCsStaffGroupId));
                    list.stream().forEach(group -> group.setCsStaffList(map.get(group.getCsStaffGroupId())));
                    packageOverflowCsStaffGroup(list);
                }
            }
        }
        return PageResultObject.of(list);
    }

    @Override
    public PageResultObject<CsStaffGroupVO> selectListByPageWithOnlineStaff(Long tenantId, Long userId, Integer pageNum, Integer pageSize, String searchName, EnabledStatusEnum enabledStatus, StaffGroupTypeEnum groupType, List<StaffGroupTypeEnum> groupTypeList, SystemEnum systemType) {

        List<Long> tenandId = new ArrayList<>();
        tenandId.add(tenantId);
        PageHelper.startPage(pageNum, pageSize);
        List<CsStaffGroupVO> list = csStaffGroupPOMapper.selectGroupByTenantIdAndStatusWithGroupList(tenantId, searchName, enabledStatus, groupType, groupTypeList, null);
        if(list != null && !list.isEmpty()){
            packageOverflowCsStaffGroup(list);
            Map<Long, CsSeatDTO> csMap = csStaffUserService.getStaffStatusMap(tenantId);
            List<Long> csIdList = list.stream().filter(group -> (group.getGroupType().equals(StaffGroupTypeEnum.CS) || group.getGroupType().equals(StaffGroupTypeEnum.TEXT)
                    || StaffGroupTypeEnum.VOICE_TEXT.equals(group.getGroupType())) && group.getCsStaffGroupId() > 0).map(CsStaffGroupVO::getCsStaffGroupId).collect(Collectors.toList());
            List<Long> telIdList = list.stream().filter(group -> group.getGroupType().equals(StaffGroupTypeEnum.TEL) && group.getCsStaffGroupId() > 0).map(CsStaffGroupVO::getCsStaffGroupId).collect(Collectors.toList());
            List<Long> aiIdList = list.stream().filter(group -> (group.getGroupType().equals(StaffGroupTypeEnum.AI)) && group.getCsStaffGroupId() > 0).map(CsStaffGroupVO::getCsStaffGroupId).collect(Collectors.toList());
            if(csIdList != null && !csIdList.isEmpty()) {
                List<CsStaffInfoVO> staffList = csStaffInfoPOMapper.selectByGroupIdList(csIdList);
                if (staffList != null && !staffList.isEmpty()) {
                    Map<Long, List<CsStaffInfoVO>> map = staffList.stream().collect(Collectors.groupingBy(CsStaffInfoVO::getCsStaffGroupId));
                    list.stream().forEach(group -> group.setCsStaffList(map.get(group.getCsStaffGroupId())));
                    for (CsStaffGroupVO group : list) {
                        Integer count = 0;
                        Integer offlineCount = 0;
                        Integer leaveCount = 0;
                        Integer freeCount = 0;
                        List<CsStaffInfoVO> onlineList = new ArrayList<>();
                        List<CsStaffInfoVO> offlineList = new ArrayList<>();
                        List<CsStaffInfoVO> leaveList = new ArrayList<>();
                        if(map.get(group.getCsStaffGroupId()) != null) {
                            for (CsStaffInfoVO info : map.get(group.getCsStaffGroupId())) {
                                CsSeatDTO seatList = csMap.get(info.getCsStaffId());
                                if (seatList != null) {
                                    if(CsStaffOnlineStatusEnum.ONLINE.equals(seatList.getStatus())) {
                                        count++;
                                        onlineList.add(info);
                                        String workStatus = redisOpsService.get(RedisKeyCenter.getCsWorkStatus(info.getCsStaffId()));
                                        if(!CsStaffOnlineStatusEnum.BUSY.name().equalsIgnoreCase(workStatus)) {
                                            freeCount ++;
                                        }
                                    }else if(CsStaffOnlineStatusEnum.OFFLINE.equals(seatList.getStatus())){
                                        offlineList.add(info);
                                        offlineCount++;
                                    }else if(CsStaffOnlineStatusEnum.LEAVE.equals(seatList.getStatus())){
                                        leaveList.add(info);
                                        leaveCount++;
                                    }
                                }else {
                                    offlineCount++;
                                    offlineList.add(info);
                                }
                            }
                        }
                        group.setOnlineCount(count);
                        group.setFreeCount(freeCount);
                        group.setOfflineCount(offlineCount);
                        group.setLeaveCount(leaveCount);
                        group.setOnlineList(onlineList);
                        group.setOfflineList(offlineList);
                        group.setLeaveList(leaveList);

                        if(StaffGroupTypeEnum.TEL.equals(group.getGroupType()) || StaffGroupTypeEnum.AI.equals(group.getGroupType())){
                            group.setOnlineCount(group.getGroupCapacity());
                            group.setFreeCount(group.getOnlineCount());
                        }

                        if (CollectionUtils.isNotEmpty(group.getCustomerWhiteGroupIdList())) {
                            List customerWhiteGroupIds = new ArrayList<>(group.getCustomerWhiteGroupIdList());
                            List<CustomerWhiteGroupPO> groupPOS = customerWhiteGroupService.selectCustomerWhiteGroupByIdList(customerWhiteGroupIds);
                            group.setWhiteGroupNames(groupPOS.stream().map(x -> x.getName()).collect(Collectors.toList()));
                        }
                    }
                }
            }
            if(telIdList != null && !telIdList.isEmpty()){
                List<CsStaffGroupTransferVO> transferVOList = csStaffGroupTransferService.selectByGroupIdList(telIdList);
                List<CsStaffGroupPhoneNumberVO> phoneNumberVOList = csStaffGroupPhoneNumberService.selectByGroupIdList(telIdList);
                if(transferVOList != null && !transferVOList.isEmpty()){
                    Map<Long, List<CsStaffGroupTransferVO>> map = transferVOList.stream().collect(Collectors.groupingBy(CsStaffGroupTransferVO::getCsStaffGroupId));
                    list.stream().forEach(group -> group.setTransferList(map.get(group.getCsStaffGroupId())));
                    Set<Long> userIdList = transferVOList.stream().map(CsStaffGroupTransferVO::getUserId).collect(Collectors.toSet());
                    Map<Long, Long> telUserMap = new HashMap<>();
                    Map<Long, CsStaffInfoPO> telCsMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(userIdList)) {
                        List<CsStaffInfoPO> telCsList = csStaffInfoPOMapper.selectByUserIdListAndType(tenantId, new ArrayList<>(userIdList), StaffTypeEnum.VOICE);
                        if(CollectionUtils.isNotEmpty(telCsList)) {

                            telUserMap = telCsList.stream().collect(Collectors.toMap(CsStaffInfoPO::getUserId, CsStaffInfoPO::getCsStaffId));
                        }

                    }
                    for(CsStaffGroupVO item : list){
                        List<CsStaffGroupTransferVO> transferVOS = map.get(item.getCsStaffGroupId());
                        item.setTransferList(transferVOS);
                        if(CollectionUtils.isNotEmpty(transferVOS)){
                            Integer count = 0;
                            Integer offlineCount = 0;
                            Integer leaveCount = 0;
                            List<CsStaffInfoVO> onlineList = new ArrayList<>();
                            List<CsStaffInfoVO> offlineList = new ArrayList<>();
                            List<CsStaffInfoVO> leaveList = new ArrayList<>();
                            for(CsStaffGroupTransferVO vo : transferVOS){
                                CsStaffInfoVO info = new CsStaffInfoVO();
                                info.setCsName(vo.getUserName());
                                info.setCsMobile(vo.getPhoneNumber());
                                if(vo.getUserId() != null){
                                    Long csStaffId = telUserMap.get(vo.getUserId());
                                    if(csStaffId != null){
                                        CsSeatDTO seatList = csMap.get(csStaffId);
                                        if (seatList != null) {
                                            if(CsStaffOnlineStatusEnum.ONLINE.equals(seatList.getStatus())) {
                                                count++;
                                                onlineList.add(info);
                                            }else if(CsStaffOnlineStatusEnum.OFFLINE.equals(seatList.getStatus())){
                                                offlineList.add(info);
                                                offlineCount++;
                                            }else if(CsStaffOnlineStatusEnum.LEAVE.equals(seatList.getStatus())){
                                                leaveList.add(info);
                                                leaveCount++;
                                            }
                                        }else {
                                            offlineList.add(info);
                                            offlineCount++;
                                        }
                                    }else{
                                        onlineList.add(info);
                                        count++;
                                    }
                                }else{
                                    onlineList.add(info);
                                    count++;
                                }
                            }
                            item.setOnlineCount(count);
                            item.setOfflineCount(offlineCount);
                            item.setLeaveCount(leaveCount);
                            item.setOnlineList(onlineList);
                            item.setOfflineList(offlineList);
                            item.setLeaveList(leaveList);
                        }
                    }
                }
                if(phoneNumberVOList != null && !phoneNumberVOList.isEmpty()){
                    Map<Long, List<CsStaffGroupPhoneNumberVO>> map = phoneNumberVOList.stream().collect(Collectors.groupingBy(CsStaffGroupPhoneNumberVO::getCsStaffGroupId));
                    list.stream().forEach(group -> {
                        group.setPhoneNumberInfoList(map.get(group.getCsStaffGroupId()));
                        if(group.getPhoneNumberInfoList() != null && !group.getPhoneNumberInfoList().isEmpty()) {
                            group.setPhoneType(group.getPhoneNumberInfoList().get(0).getPhoneType().name());
                        }
                    });
                }
            }

            if(CollectionUtils.isNotEmpty(aiIdList)){
                Set<Long> transferGroupIdList = list.stream().filter(x -> x.getTransferCsStaffGroupId() != null && x.getTransferCsStaffGroupId() > 0).map(CsStaffGroupVO::getTransferCsStaffGroupId).collect(Collectors.toSet());
                if(CollectionUtils.isNotEmpty(transferGroupIdList)){
                    Map<Long,CsStaffGroupPO> groupMap = new HashMap<>();
                    List<CsStaffGroupPO> groupPOList = csStaffGroupPOMapper.selectByIdList(new ArrayList<>(transferGroupIdList));
                    if(CollectionUtils.isNotEmpty(groupPOList)) {
                        groupMap = groupPOList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, x -> x));
                        Map<Long, List<CsStaffInfoVO>> csOrTextMap = new HashMap<>();
                        List<CsStaffInfoVO> csOrTextList = csStaffInfoPOMapper.selectByGroupIdList(new ArrayList<>(transferGroupIdList));
                        if(CollectionUtils.isNotEmpty(csOrTextList)) {
                            csOrTextMap = csOrTextList.stream().collect(Collectors.groupingBy(CsStaffInfoVO::getCsStaffGroupId));
                            for (CsStaffGroupVO group : list) {
                                if (group.getTransferCsStaffGroupId() != null) {
                                    CsStaffGroupPO transferGroup = groupMap.get(group.getTransferCsStaffGroupId());
                                    if (transferGroup != null) {
                                        //设置坐席组成员
                                        group.setKnowledgeIdList(transferGroup.getKnowledgeIdList());
                                        group.setRobotNlpIdList(transferGroup.getRobotNlpIdList());
                                        group.setCsStaffList(csOrTextMap.get(group.getTransferCsStaffGroupId()));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Set<Long> knowledgeIdList = new HashSet<>();
            Set<Long> robotIdList = new HashSet<>();
            list.forEach(staffGroupVO->{
                if(CollectionUtils.isNotEmpty(staffGroupVO.getKnowledgeIdList())){
                    knowledgeIdList.addAll(staffGroupVO.getKnowledgeIdList());
                }
                if(CollectionUtils.isNotEmpty(staffGroupVO.getRobotNlpIdList())){
                    robotIdList.addAll(staffGroupVO.getRobotNlpIdList());
                }
            });
            if(CollectionUtils.isNotEmpty(knowledgeIdList) || CollectionUtils.isNotEmpty(robotIdList)){
                Map<Long,String> nameMap = new HashMap<>();
                Map<Long,String> robotNameMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(knowledgeIdList)){
                    List<KnowledgeBaseInfoBO> knowledgeBaseInfoBOS = knowledgeBaseInfoPOMapper.selectKnowledgeByIds(knowledgeIdList);
                    nameMap = knowledgeBaseInfoBOS.stream().collect(Collectors.toMap(KnowledgeBaseInfoBO::getKnowledgeBaseInfoId,KnowledgeBaseInfoBO::getName));

                }
                if(CollectionUtils.isNotEmpty(robotIdList)){
                    List<RobotNlpDTO> nlpRobotList = channelSettingService.getRobotListByTenant(tenantId);
                    if(CollectionUtils.isNotEmpty(nlpRobotList)){
                        for(RobotNlpDTO robotNlpDTO: nlpRobotList){
                            robotNameMap.put(robotNlpDTO.getRobotId(), robotNlpDTO.getName());
                        }
                    }
                }
                //设置绑定知识库
                for(CsStaffGroupVO csStaffGroupVO:list){
                    Set<Long> idSet = csStaffGroupVO.getKnowledgeIdList();
                    Set<Long> robotIdSet = csStaffGroupVO.getRobotNlpIdList();
                    String baseName ="";
                    if(CollectionUtils.isNotEmpty(idSet)){
                        for(Long set:idSet){
                            baseName += nameMap.get(set)+",";
                        };
                    }
                    if(CollectionUtils.isNotEmpty(robotIdSet)){
                        for(Long set:robotIdSet){
                            baseName += robotNameMap.get(set)+",";
                        };
                    }
                    //去掉最后一个，
                    if(baseName.length() > 0) {
                        csStaffGroupVO.setBindingKnowledgeBase(baseName.substring(0, baseName.length() - 1));
                    }
                }
            }

        }
        return PageResultObject.of(list);
    }

    @Override
    public void addOrUpdateGroup(CsStaffGroupVO staffGroupVO, Long tenantId, Long userId, Long distributorId) {
        Integer capacity = 0;
        Long dialogFlowId = 0L;
        //新建
        if(StaffGroupTypeEnum.AI.equals(staffGroupVO.getGroupType())){
            //AI坐席组，需要选择话术
            if(Objects.isNull(staffGroupVO.getDialogFlowId()) || staffGroupVO.getDialogFlowId() == 0){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "AI坐席组需要选择话术");
            }
            Integer aiRobotCount = robotService.getTenantTodayRobotCount(tenantId,staffGroupVO.getSystemType());
            if(aiRobotCount.compareTo(staffGroupVO.getGroupCapacity()) < 0){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "坐席数量需要小于有效AI坐席数");
            }
            capacity = staffGroupVO.getGroupCapacity();
            dialogFlowId = staffGroupVO.getDialogFlowId();
        }else if(StaffGroupTypeEnum.CS.equals(staffGroupVO.getGroupType())  || StaffGroupTypeEnum.TEXT.equals(staffGroupVO.getGroupType()) || StaffGroupTypeEnum.VOICE_TEXT.equals(staffGroupVO.getGroupType()) ){
            //人工坐席组
            if(staffGroupVO.getCsStaffIdList() == null || staffGroupVO.getCsStaffIdList().isEmpty() || staffGroupVO.getCsStaffIdList().size() == 0){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "语音坐席组需要选择坐席组成员");
            }
            capacity = staffGroupVO.getCsStaffIdList().size();
        }else if(StaffGroupTypeEnum.TEL.equals(staffGroupVO.getGroupType())){
            if(((staffGroupVO.getTransferNumberList() == null || staffGroupVO.getTransferNumberList().isEmpty()))){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "移动端人工坐席组需要选电话号码");
            }
            capacity = staffGroupVO.getTransferNumberList() == null ? 0 : staffGroupVO.getTransferNumberList().size();
        }
        OperationLogOperationTypeEnum type;
        if(staffGroupVO.getCsStaffGroupId() == null || staffGroupVO.getCsStaffGroupId() == 0){
            type = OperationLogOperationTypeEnum.STAFF_GROUP_ADD;
            CsStaffGroupPO groupPO = new CsStaffGroupPO();
            BeanUtils.copyProperties(staffGroupVO, groupPO);
            groupPO.setEnabledStatus(EnabledStatusEnum.ENABLE);
            groupPO.setTenantId(tenantId);
            groupPO.setCreateUserId(userId);
            groupPO.setUpdateUserId(userId);
            groupPO.setGroupCapacity(capacity);
            groupPO.setDialogFlowId(dialogFlowId);
            groupPO.setTransferSipUri(staffGroupVO.getTransferSipUri());
            groupPO.setKnowledgeIdList(staffGroupVO.getKnowledgeIdList());
            groupPO.setCustomerWhiteGroupIdList(staffGroupVO.getCustomerWhiteGroupIdList());
            saveNotNull(groupPO);
            if(StaffGroupTypeEnum.CS.equals(staffGroupVO.getGroupType()) || StaffGroupTypeEnum.TEXT.equals(staffGroupVO.getGroupType()) || StaffGroupTypeEnum.VOICE_TEXT.equals(staffGroupVO.getGroupType())){
                //关联坐席
                List<Long> idList = staffGroupVO.getCsStaffIdList();
                csStaffGroupRelPOMapper.batchInsert(groupPO.getCsStaffGroupId(), idList);
                qcStaffGroupService.createOrUpdate(tenantId, groupPO.getCsStaffGroupId(), staffGroupVO.getGroupName(), idList);
            }
            if(StaffGroupTypeEnum.TEL.equals(staffGroupVO.getGroupType())){
                if(staffGroupVO.getTransferNumberList() != null && !staffGroupVO.getTransferNumberList().isEmpty()){
                    for(String phoneNumber : staffGroupVO.getTransferNumberList()) {
                        CsStaffGroupTransferPO transferPO = new CsStaffGroupTransferPO();
                        UserPO userPO = userService.selectUserByTenantIdAndPhoneNumber(tenantId, phoneNumber);
                        transferPO.setCsStaffGroupId(groupPO.getCsStaffGroupId());
                        transferPO.setTransferNumber(phoneNumber);
                        transferPO.setUserId(userPO == null ? null : userPO.getUserId());
                        csStaffGroupTransferService.saveNotNull(transferPO);
                    }
                }
                if(staffGroupVO.getTenantPhoneNumberIdList() != null && !staffGroupVO.getTenantPhoneNumberIdList().isEmpty()){
                    List<TenantPhoneNumberPO> tenantPhoneNumberPOS = tenantPhoneNumberPOMapper.selectTenantPhoneNumberByIds(staffGroupVO.getTenantPhoneNumberIdList());
                    for(TenantPhoneNumberPO phoneNumberPO : tenantPhoneNumberPOS){
                        CsStaffGroupPhoneNumberPO item = new CsStaffGroupPhoneNumberPO();
                        item.setCsStaffGroupId(groupPO.getCsStaffGroupId());
                        item.setTenantPhoneNumberId(phoneNumberPO.getTenantPhoneNumberId());
                        item.setPhoneNumberId(phoneNumberPO.getPhoneNumberId());
                        csStaffGroupPhoneNumberService.saveNotNull(item);
                    }
                }
            }
            List<String> operationContent = new ArrayList<>();
            addVoiceStaffGroupLog(groupPO, staffGroupVO, operationContent);
            operationLogService.addSeatLog(userId, tenantId, distributorId, null, type, operationContent, groupPO.getCsStaffGroupId());
        }else {
            type = OperationLogOperationTypeEnum.STAFF_GROUP_EDIT;
            //编辑
            CsStaffGroupPO dbGroup = selectByKey(staffGroupVO.getCsStaffGroupId());
            if(dbGroup == null || !dbGroup.getTenantId().equals(tenantId)){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "坐席组不存在");
            }
            if(staffGroupVO.getOverflowCsStaffGroupId() != null && staffGroupVO.getCsStaffGroupId().equals(staffGroupVO.getOverflowCsStaffGroupId())){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前坐席组不能作为自己的溢出坐席组");
            }
            CsStaffGroupPO groupPO = new CsStaffGroupPO();
            groupPO.setTenantId(tenantId);
            groupPO.setGroupCapacity(capacity);
            groupPO.setGroupName(staffGroupVO.getGroupName());
            groupPO.setDialogFlowId(dialogFlowId);
            groupPO.setCsStaffGroupId(staffGroupVO.getCsStaffGroupId());
            groupPO.setUpdateUserId(userId);
            groupPO.setTransferCsStaffGroupId(staffGroupVO.getTransferCsStaffGroupId());
            groupPO.setTransferSipUri(staffGroupVO.getTransferSipUri());
            groupPO.setKnowledgeIdList(staffGroupVO.getKnowledgeIdList());
            groupPO.setRobotNlpIdList(staffGroupVO.getRobotNlpIdList());
            groupPO.setDistributionMethod(staffGroupVO.getDistributionMethod());
            groupPO.setOverflowCsStaffGroupId(staffGroupVO.getOverflowCsStaffGroupId());
            groupPO.setCustomerWhiteGroupIdList(staffGroupVO.getCustomerWhiteGroupIdList());
            groupPO.setSipHeaders(staffGroupVO.getSipHeaders());
            updateNotNull(groupPO);

            if(StaffGroupTypeEnum.CS.equals(staffGroupVO.getGroupType()) || StaffGroupTypeEnum.TEXT.equals(staffGroupVO.getGroupType()) || StaffGroupTypeEnum.VOICE_TEXT.equals(staffGroupVO.getGroupType())){
                csStaffGroupRelPOMapper.deleteByGroupId(staffGroupVO.getCsStaffGroupId());
                //关联坐席
                List<Long> idList = staffGroupVO.getCsStaffIdList();
                csStaffGroupRelPOMapper.batchInsert(groupPO.getCsStaffGroupId(), idList);
                qcStaffGroupService.createOrUpdate(tenantId, groupPO.getCsStaffGroupId(), staffGroupVO.getGroupName(), idList);
            }
            if(StaffGroupTypeEnum.TEL.equals(staffGroupVO.getGroupType())){
                csStaffGroupTransferService.deleteByGroupId(staffGroupVO.getCsStaffGroupId());
                csStaffGroupPhoneNumberService.deleteByGroupId(staffGroupVO.getCsStaffGroupId());
                if(staffGroupVO.getTransferNumberList() != null && !staffGroupVO.getTransferNumberList().isEmpty()){
                    for(String phoneNumber : staffGroupVO.getTransferNumberList()) {
                        CsStaffGroupTransferPO transferPO = new CsStaffGroupTransferPO();
                        UserPO userPO = userService.selectUserByTenantIdAndPhoneNumber(tenantId, phoneNumber);
                        transferPO.setCsStaffGroupId(groupPO.getCsStaffGroupId());
                        transferPO.setTransferNumber(phoneNumber);
                        transferPO.setUserId(userPO == null ? null : userPO.getUserId());
                        csStaffGroupTransferService.saveNotNull(transferPO);
                    }
                }
                if(staffGroupVO.getTenantPhoneNumberIdList() != null && !staffGroupVO.getTenantPhoneNumberIdList().isEmpty()){
                    List<TenantPhoneNumberPO> tenantPhoneNumberPOS = tenantPhoneNumberPOMapper.selectTenantPhoneNumberByIds(staffGroupVO.getTenantPhoneNumberIdList());
                    for(TenantPhoneNumberPO phoneNumberPO : tenantPhoneNumberPOS){
                        CsStaffGroupPhoneNumberPO item = new CsStaffGroupPhoneNumberPO();
                        item.setCsStaffGroupId(groupPO.getCsStaffGroupId());
                        item.setTenantPhoneNumberId(phoneNumberPO.getTenantPhoneNumberId());
                        item.setPhoneNumberId(phoneNumberPO.getPhoneNumberId());
                        csStaffGroupPhoneNumberService.saveNotNull(item);
                    }
                }
            }
            // 清空呼入那边的坐席缓存信息
            callInSeatDispatchService.clearSeatInfoByStaffGroupId(dbGroup.getCsStaffGroupId());
            List<String> operationContent = new ArrayList<>();
            addVoiceStaffGroupLog(groupPO, staffGroupVO, operationContent);
            operationLogService.addSeatLog(userId, tenantId, distributorId, null, type, operationContent, groupPO.getCsStaffGroupId());
        }
    }

    private void addVoiceStaffGroupLog(CsStaffGroupPO groupPO, CsStaffGroupVO staffGroupVO, List<String> operationContent) {
        try {
            operationContent.add("坐席组名：" + groupPO.getGroupName());
            if (groupPO.getGroupType() != null) {
                operationContent.add("坐席组类型：" + groupPO.getGroupType().getDesc());
            }
            if (groupPO.getDistributionMethod() != null) {
                operationContent.add("坐席分配方式：" + groupPO.getDistributionMethod().getDesc());
            }
            if (CollectionUtils.isNotEmpty(groupPO.getKnowledgeIdList()) || CollectionUtils.isNotEmpty(groupPO.getRobotNlpIdList())) {
                operationContent.add("知识来源：" + groupPO.getGroupType());
            }
            List<Long> csStaffIdList = staffGroupVO.getCsStaffIdList();
            if (CollectionUtils.isNotEmpty(csStaffIdList)) {
                List<CsStaffInfoPO> csStaffInfoPOS = csStaffInfoService.selectListByKeyCollect(csStaffIdList);
                List<String> collect = csStaffInfoPOS.stream().map(CsStaffInfoPO::getCsName).collect(Collectors.toList());
                operationContent.add("坐席组成员：" + StringUtils.join(collect, ","));
            }
            Long overflowCsStaffGroupId = groupPO.getOverflowCsStaffGroupId();
            if (overflowCsStaffGroupId != null) {
                CsStaffGroupPO csStaffGroupPO = selectByKey(overflowCsStaffGroupId);
                operationContent.add("溢出坐席组：" + (csStaffGroupPO == null ? "" : csStaffGroupPO.getGroupName()));
            }
            List<Long> customerWhiteGroupIdList = groupPO.getCustomerWhiteGroupIdList();
            if (CollectionUtils.isNotEmpty(customerWhiteGroupIdList)) {
                List<CustomerWhiteGroupPO> customerWhiteGroupPOS = customerWhiteGroupService.selectListByKeyCollect(customerWhiteGroupIdList);
                List<String> whiteGroupNames = customerWhiteGroupPOS.stream().map(CustomerWhiteGroupPO::getName).collect(Collectors.toList());
                operationContent.add("人工外呼黑名单：" + StringUtils.join(whiteGroupNames, ","));
            }
        } catch (Exception e) {
            log.error("生成日志失败",e);
        }
    }

    @Override
    public void updateGroupStatus(Long staffGroupId, EnabledStatusEnum enableStatus, Long tenantId, Long userId) {
        CsStaffGroupPO dbGroup = selectByKey(staffGroupId);
        if(dbGroup == null || !dbGroup.getTenantId().equals(tenantId)){
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "坐席组不存在");
        }
        if(enableStatus == EnabledStatusEnum.ENABLE){
            if(EnabledStatusEnum.DISABLE.equals(dbGroup.getEnabledStatus())){
                CsStaffGroupPO update = new CsStaffGroupPO();
                update.setCsStaffGroupId(staffGroupId);
                update.setUpdateUserId(userId);
                update.setEnabledStatus(EnabledStatusEnum.ENABLE);
                updateNotNull(update);
            }
        }else if(enableStatus == EnabledStatusEnum.DISABLE){
            //检查是否被使用
            List<CallInReceptionPO> list = callInReceptionPOMapper.selectByStaffGroupId(staffGroupId);
            if(list != null && !list.isEmpty()){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "该坐席组已被用于"+list.size() +"个呼入场景，不可以操作禁用");
            }
            List<RobotCallJobPO> robotCallJobPOList = robotCallJobPOMapper.selectByCsStaffGroupId(tenantId, staffGroupId);
            if(robotCallJobPOList != null && !robotCallJobPOList.isEmpty()){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "该坐席组已被用于"+robotCallJobPOList.size() +"个呼叫任务，不可以操作禁用");
            }
            if(StaffGroupTypeEnum.TEXT.equals(dbGroup.getGroupType())){
                List<ChannelSettingPO> channelSettingPOS = channelSettingService.getChannelByTenantIdAndGroupId(tenantId, staffGroupId);
                if(CollectionUtils.isNotEmpty(channelSettingPOS)){
                    Set<String> names = channelSettingPOS.stream().map(ChannelSettingPO::getName).collect(Collectors.toSet());
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "该坐席组已被用于文本客服接入渠道（"+String.join(",", names) +"），不可以操作禁用");
                }
            }
            if(EnabledStatusEnum.ENABLE.equals(dbGroup.getEnabledStatus())){
                CsStaffGroupPO update = new CsStaffGroupPO();
                update.setCsStaffGroupId(staffGroupId);
                update.setUpdateUserId(userId);
                update.setEnabledStatus(EnabledStatusEnum.DISABLE);
                updateNotNull(update);
            }
        }else {
            if(dbGroup.getEnabledStatus() != EnabledStatusEnum.DISABLE){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "请先禁用才可删除");
            }
            CsStaffGroupPO update = new CsStaffGroupPO();
            update.setCsStaffGroupId(staffGroupId);
            update.setUpdateUserId(userId);
            update.setEnabledStatus(EnabledStatusEnum.DELETE);
            updateNotNull(update);
        }
    }

    @Override
    public CsStaffGroupVO selectByGroupId(Long staffGroupId) {
        CsStaffGroupVO groupVO = csStaffGroupPOMapper.selectByGroupId(staffGroupId);
        if(groupVO != null){
            List<CsStaffGroupTransferVO> transferVOList = csStaffGroupTransferService.selectTransferByGroupIdWithUser(staffGroupId);
            groupVO.setTransferList(transferVOList);
            List<CsStaffGroupPhoneNumberVO> phoneNumberVOListt = csStaffGroupPhoneNumberService.selectPhoneNumberByGroupId(staffGroupId);
            groupVO.setPhoneNumberInfoList(phoneNumberVOListt);

            if (CollectionUtils.isNotEmpty(groupVO.getCustomerWhiteGroupIdList())) {
                List customerWhiteGroupIds = new ArrayList<>(groupVO.getCustomerWhiteGroupIdList());
                List<CustomerWhiteGroupPO> groupPOS = customerWhiteGroupService.selectCustomerWhiteGroupByIdList(customerWhiteGroupIds);
                groupVO.setWhiteGroupNames(groupPOS.stream().map(x -> x.getName()).collect(Collectors.toList()));
            }
        }
        return groupVO;
    }

    @Override
    public List<CsStaffGroupPO> selectTenantGroup(Long tenantId) {
        return csStaffGroupPOMapper.selectTenantGroup(tenantId);
    }

    @Override
    public List<CsStaffGroupPO> selectByIdList(List<Long> staffGroupIdList) {
        if(staffGroupIdList == null || staffGroupIdList.isEmpty()){
            return new ArrayList<>();
        }
        return csStaffGroupPOMapper.selectByIdList(staffGroupIdList);
    }

    @Override
    public SipAccountInfoBO getSipInfoByGroupId(Long csStaffGroupId) {
        SipAccountInfoBO sipAccountInfoBO = new SipAccountInfoBO();
        List<CsStaffGroupPhoneNumberVO> groupPhoneNumberPOList = csStaffGroupPhoneNumberService.selectPhoneNumberByGroupId(csStaffGroupId);
        if(groupPhoneNumberPOList == null || groupPhoneNumberPOList.isEmpty()){
            return null;
        }
        PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKey(groupPhoneNumberPOList.get(0).getPhoneNumberId());
        List<FreeswitchGroupItemInfoBO> fsList = freeswitchGroupService.getFreeswithList(phoneNumberPO.getFreeswitchGroupId());
        sipAccountInfoBO.setFreeswitchInfoList(fsList);
        sipAccountInfoBO.setAreaCode(phoneNumberPO.getAreaCode());
        sipAccountInfoBO.setDefaultCallPrefix(phoneNumberPO.getDefaultCallPrefix());
        sipAccountInfoBO.setOtherCallPrefix(phoneNumberPO.getOtherCallPrefix());
        sipAccountInfoBO.setFreeswitchGroupId(phoneNumberPO.getFreeswitchGroupId());
        sipAccountInfoBO.setLineIp(phoneNumberPO.getLineIp());
        sipAccountInfoBO.setLinePort(phoneNumberPO.getLinePort());
        sipAccountInfoBO.setPhoneNumber(phoneNumberPO.getPhoneNumber());
        sipAccountInfoBO.setPhoneNumberId(phoneNumberPO.getPhoneNumberId());
        sipAccountInfoBO.setPhoneType(phoneNumberPO.getPhoneType());
        sipAccountInfoBO.setSipAccount(phoneNumberPO.getSipAccount());
        sipAccountInfoBO.setSipPassword(phoneNumberPO.getSipPassword());
        sipAccountInfoBO.setUseDialplanType(phoneNumberPO.getUseDialplanType());
        sipAccountInfoBO.setVariableSet(phoneNumberPO.getVariableSet());
        sipAccountInfoBO.setMobileOperator(phoneNumberPO.getMobileOperator());
        sipAccountInfoBO.setOperatorRestriction(phoneNumberPO.getOperatorRestriction());
        return sipAccountInfoBO;
    }

    @Override
    public PageResultObject<CsStaffGroupVO> callJobStaffGroupList(Long tenantId, Long userId, StaffGroupTypeEnum staffGroupType,Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<CsStaffGroupVO> list = csStaffGroupPOMapper.selectGroupByTenantIdAndCsOrTel(tenantId, staffGroupType);
        if(list != null && !list.isEmpty()){
            List<Long> csIdList = list.stream().filter(group -> (group.getGroupType().equals(StaffGroupTypeEnum.CS) || group.getGroupType().equals(StaffGroupTypeEnum.TEXT) || group.getGroupType().equals(StaffGroupTypeEnum.TEL)) && group.getCsStaffGroupId() > 0).map(CsStaffGroupVO::getCsStaffGroupId).collect(Collectors.toList());
             if(csIdList != null && !csIdList.isEmpty()) {
                List<CsStaffInfoVO> staffList = csStaffInfoPOMapper.selectByGroupIdList(csIdList);
                if (staffList != null && !staffList.isEmpty()) {
                    Map<Long, List<CsStaffInfoVO>> map = staffList.stream().collect(Collectors.groupingBy(CsStaffInfoVO::getCsStaffGroupId));
                    Map<Long, CsSeatDTO> csMap = csStaffUserService.getStaffStatusMap(tenantId);
                    for (CsStaffGroupVO group : list) {
                        Integer count = 0;
                        Integer offlineCount = 0;
                        Integer leaveCount = 0;
                        if (map.get(group.getCsStaffGroupId()) != null) {
                            for (CsStaffInfoVO info : map.get(group.getCsStaffGroupId())) {
                                CsSeatDTO seatList = csMap.get(info.getCsStaffId());
                                if (seatList != null && CsStaffOnlineStatusEnum.ONLINE.equals(seatList.getStatus())) {
                                    count++;
                                }else if(seatList != null && CsStaffOnlineStatusEnum.LEAVE.equals(seatList.getStatus())){
                                    leaveCount++;
                                }else {
                                    offlineCount++;
                                }
                            }
                        }
                        group.setOnlineCount(count);
                        group.setOfflineCount(offlineCount);
                        group.setLeaveCount(leaveCount);
                    }
                }
            }
             for(CsStaffGroupVO group : list){
                 if(StaffGroupTypeEnum.TEL.equals(group.getGroupType())){
                     group.setOnlineCount(group.getGroupCapacity());
                     group.setOfflineCount(0);
                     group.setLeaveCount(0);
                 }
             }
        }

        return PageResultObject.of(list);
    }

    @Override
    public List<Long> selectStaffIdListByStaffGroupIdList(List<Long> staffGroupIdList) {
        if (CollectionUtils.isEmpty(staffGroupIdList)) {
            return new ArrayList<>();
        }
        return csStaffGroupRelPOMapper.selectListByKeyCollect(staffGroupIdList).stream().map(CsStaffGroupRelPO::getCsStaffInfoId).collect(Collectors.toList());
    }

    @Override
    public CsStaffGroupVO buildCsStaffGroupInfo(CsStaffGroupVO groupVO) {
        List<Long> arr = new ArrayList<>();
        arr.add(groupVO.getCsStaffGroupId());
        if(groupVO.getGroupType() == StaffGroupTypeEnum.CS || groupVO.getGroupType() == StaffGroupTypeEnum.TEXT || StaffGroupTypeEnum.VOICE_TEXT.equals(groupVO.getGroupType())) {
            Map<Long, CsSeatDTO> csMap = csStaffUserService.getGroupStaffStatus(groupVO.getCsStaffGroupId(), groupVO.getTenantId());
            List<CsStaffInfoVO> list  = csStaffInfoPOMapper.selectByGroupIdList(arr);
            if(list == null){
                return groupVO;
            }
            Integer count = 0;
            Integer offlineCount = 0;
            Integer leaveCount = 0;
            List<CsStaffInfoVO> onlineList = new ArrayList<>();
            List<CsStaffInfoVO> offlineList = new ArrayList<>();
            List<CsStaffInfoVO> leaveList = new ArrayList<>();
            Set<Long> idList = list.stream().map(CsStaffInfoVO::getCsStaffId).collect(Collectors.toSet());
            groupVO.setCsStaffIdList(new ArrayList<>(idList));
            for (CsStaffInfoVO info : list) {
                CsSeatDTO seatList = csMap.get(info.getCsStaffId());
                if (seatList != null) {
                    if(CsStaffOnlineStatusEnum.ONLINE.equals(seatList.getStatus())) {
                        count++;
                        onlineList.add(info);
                    }else if(CsStaffOnlineStatusEnum.OFFLINE.equals(seatList.getStatus())){
                        offlineList.add(info);
                        offlineCount++;
                    }else if(CsStaffOnlineStatusEnum.LEAVE.equals(seatList.getStatus())){
                        leaveList.add(info);
                        leaveCount++;
                    }
                } else {
                    offlineList.add(info);
                    offlineCount++;
                }
            }
            groupVO.setOnlineCount(count);
            groupVO.setOfflineCount(offlineCount);
            groupVO.setLeaveCount(leaveCount);
            groupVO.setGroupCapacity(list.size());
            groupVO.setOnlineList(onlineList);
            groupVO.setOfflineList(offlineList);
            groupVO.setLeaveList(leaveList);
        }else if(groupVO.getGroupType() == StaffGroupTypeEnum.TEL){
            List<CsStaffGroupTransferVO> transferVOList = csStaffGroupTransferService.selectByGroupIdList(arr);
            if(transferVOList != null && !transferVOList.isEmpty()){
                groupVO.setTransferList(transferVOList);
            }
            List<CsStaffGroupPhoneNumberVO> phoneNumberVOList = csStaffGroupPhoneNumberService.selectByGroupIdList(arr);
            if(phoneNumberVOList != null && !phoneNumberVOList.isEmpty()){
                groupVO.setPhoneNumberInfoList(phoneNumberVOList);
                if(groupVO.getPhoneNumberInfoList() != null && !groupVO.getPhoneNumberInfoList().isEmpty()) {
                    groupVO.setPhoneType(groupVO.getPhoneNumberInfoList().get(0).getPhoneType().name());
                }
            }
            groupVO.setOnlineCount(groupVO.getGroupCapacity());
        }
        return groupVO;
    }

    @Override
    public PageResultObject<CsStaffGroupPO> searchGroupList(String searchName, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<CsStaffGroupPO> list = csStaffGroupPOMapper.searchGroupList(searchName, null);
        return PageResultObject.of(list);
    }

    @Override
    public CsStaffGroupVO getCurrentCsStaffGroupInfo(Long tenantId, Long userId) {
        CsStaffGroupVO groupVO = new CsStaffGroupVO();
        // 人工坐席
        CsStaffInfoPO csStaffInfoPO = csStaffInfoService.queryVoiceByUserId(tenantId, userId);
        if (csStaffInfoPO != null) {
            CsBatchCallJobPO csBatchCallJobPO = null;
            List<CsStaffGroupRelPO> groupRelPOList = csStaffGroupRelPOMapper.selectStaffGroupRelList(csStaffInfoPO.getCsStaffId());
            if (CollectionUtils.isNotEmpty(groupRelPOList)) {
                List<Long> groupIdList = groupRelPOList.stream().map(CsStaffGroupRelPO::getCsStaffGroupId).collect(Collectors.toList());
                csBatchCallJobPO = csBatchCallJobPOMapper.selectBatchJobByGroupIdList(tenantId, groupIdList);
            }
            // 当前坐席所在的坐席组有正在执行的任务
            if (csBatchCallJobPO != null && callJobNotCompleted(csBatchCallJobPO)) {
                // 在线情况
                Map<Long, CsSeatDTO> csMap = csStaffUserService.getStaffStatusMap(tenantId);
                Long groupId = csBatchCallJobPO.getCsStaffGroupId();
                List<CsStaffInfoPO> csStaffInfoPOS = csStaffInfoPOMapper.selectStaffByGroupIdAndStatus(groupId);

                List<CsStaffInfoVO> onlineList = Lists.newArrayList();
                List<CsStaffInfoVO> offlineList = Lists.newArrayList();
                List<CsStaffInfoVO> leaveList = Lists.newArrayList();
                for (CsStaffInfoPO staffInfoPO : csStaffInfoPOS) {
                    CsSeatDTO seatList = csMap.get(staffInfoPO.getCsStaffId());
                    CsStaffInfoVO staffInfoVO = wrapCsStaffInfoPO2VO(staffInfoPO);
                    if (seatList != null) {
                        if(CsStaffOnlineStatusEnum.ONLINE.equals(seatList.getStatus())) {
                            onlineList.add(staffInfoVO);
                        }else if(CsStaffOnlineStatusEnum.OFFLINE.equals(seatList.getStatus())) {
                            offlineList.add(staffInfoVO);
                        }else if(CsStaffOnlineStatusEnum.LEAVE.equals(seatList.getStatus())) {
                            leaveList.add(staffInfoVO);
                        }
                    } else {
                        offlineList.add(staffInfoVO);
                    }
                }
                groupVO.setOnlineList(onlineList);
                groupVO.setOfflineList(offlineList);
                groupVO.setLeaveList(leaveList);
                CsStaffGroupPO csStaffGroupPO = csStaffGroupPOMapper.selectByPrimaryKey(groupId);
                groupVO.setGroupName(csStaffGroupPO.getGroupName());
            }
        }
        return groupVO;
    }

    @Override
    public List<CsStaffGroupVO> getGroupByType(Long tenantId, Long userId, StaffGroupTypeEnum staffGroupType, SystemEnum systemType) {
        List<CsStaffInfoPO> staffInfoPOs = new ArrayList<>();
        Optional<List<Long>> csStaffGroupIdList = Optional.empty();
        List<Long> tenandId = new ArrayList<>();

        tenandId.add(tenantId);
        Optional<List<Long>> authUserIdList = Optional.empty();
        if (Objects.equals(SystemEnum.CUSTOMER_SERVICE, systemType)) {
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userId, tenantId, AuthResourceUriEnum.data_statistics_company, AuthResourceUriEnum.data_statistics_groups);
        } else if (Objects.equals(SystemEnum.CUSTOMER_CENTER, systemType)) {
            authUserIdList = dataAccessControlService.getAuthUserIdListWithTenantId(userId, tenantId, AuthResourceUriEnum.customer_center_text_records_dataAuth_company, AuthResourceUriEnum.customer_center_text_records_dataAuth_groups);
        }
        staffInfoPOs = csStaffInfoPOMapper.searchCsStaffList(null, null, tenandId, null, authUserIdList.orElse(null));
        if (CollectionUtils.isEmpty(staffInfoPOs)) {
            return Lists.newArrayList();
        }
        List<CsStaffGroupRelPO> csStaffGroupRelPOS = csStaffGroupRelPOMapper.selectStaffGroupRelListByCsStaffids(staffInfoPOs.stream().map(CsStaffInfoPO::getCsStaffId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(csStaffGroupRelPOS)) {
            csStaffGroupIdList = Optional.of(csStaffGroupRelPOS.stream().map(CsStaffGroupRelPO::getCsStaffGroupId).collect(Collectors.toList()));
        }

        List<CsStaffGroupVO> list = csStaffGroupPOMapper.selectGroupByTenantIdAndCsOrTelAndIds(tenantId, staffGroupType, csStaffGroupIdList.orElse(null));
        return list;
    }

    private boolean callJobNotCompleted(CsBatchCallJobPO csBatchCallJobPO) {
        return csBatchCallJobPO.getJobStatus() != CsBatchCallJobStatusEnum.TERMINATE
                && csBatchCallJobPO.getJobStatus() != CsBatchCallJobStatusEnum.COMPLETED;
    }

    private CsStaffInfoVO wrapCsStaffInfoPO2VO(CsStaffInfoPO po) {
        CsStaffInfoVO vo = new CsStaffInfoVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    @Override
    public PageResultObject<CsStaffGroupVO> getStaffGroupList(Long tenantId, Long userId, StaffGroupQueryVO staffGroupQueryVO) {
        return selectListByPageWithOnlineStaff(tenantId, userId, staffGroupQueryVO.getPageNum(), staffGroupQueryVO.getPageSize(), null, staffGroupQueryVO.getEnabledStatus(), null, staffGroupQueryVO.getGroupTypeList(),staffGroupQueryVO.getSystemType());
    }

    @Override
    public List<CsStaffGroupPO> selectByStaffIdAndStatus(Long staffId, EnabledStatusEnum enabledStatus) {
        return csStaffGroupPOMapper.selectByStaffIdAndStatus(staffId, enabledStatus);
    }

    @Override
    public CsGroupListInfoVO getCsGroupListInfo(Long tenantId, StaffTypeEnum staffType) {
        Integer enableCount = csStaffGroupPOMapper.countByTenantIdAndTypeAndStatus(tenantId, StaffGroupTypeEnum.TEXT, EnabledStatusEnum.ENABLE);
        Integer disableCount = csStaffGroupPOMapper.countByTenantIdAndTypeAndStatus(tenantId, StaffGroupTypeEnum.TEXT, EnabledStatusEnum.DISABLE);
        CsGroupListInfoVO vo = new CsGroupListInfoVO();
        if(StaffTypeEnum.TEXT.equals(staffType)) {
            vo.setEnableCount(enableCount);
            vo.setDisableCount(disableCount);
        }else {
            Integer allenableCount = csStaffGroupPOMapper.countByTenantIdAndTypeAndStatus(tenantId, null, EnabledStatusEnum.ENABLE);
            Integer alldisableCount = csStaffGroupPOMapper.countByTenantIdAndTypeAndStatus(tenantId, null, EnabledStatusEnum.DISABLE);
            vo.setEnableCount(allenableCount - enableCount);
            vo.setDisableCount(alldisableCount - disableCount);
        }
        return vo;
    }

    private void packageOverflowCsStaffGroup(List<CsStaffGroupVO> list){
        if(CollectionUtils.isNotEmpty(list)){
            Set<Long> idSet = list.stream().filter(x -> x.getOverflowCsStaffGroupId() != null).map(CsStaffGroupVO::getOverflowCsStaffGroupId).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(idSet)){
                List<CsStaffGroupPO> overFlowStaffGroupList = csStaffGroupPOMapper.selectByIdList(new ArrayList<>(idSet));
                if(CollectionUtils.isNotEmpty(overFlowStaffGroupList)){
                    Map<Long, String> map = overFlowStaffGroupList.stream().collect(Collectors.toMap(CsStaffGroupPO::getCsStaffGroupId, CsStaffGroupPO::getGroupName));
                    list.forEach(item ->{
                        item.setOverflowCsStaffGroupName(map.get(item.getOverflowCsStaffGroupId()));
                    });
                }
            }
        }
    }

    @Override
    public Integer countByCsStaffGroupIdAndTenantId(Long csStaffGroupId, Long tenantId) {
        return csStaffGroupPOMapper.countByCsStaffGroupIdAndTenantId(csStaffGroupId, tenantId);
    }
}
