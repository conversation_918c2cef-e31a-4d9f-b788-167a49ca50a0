package com.yiwise.core.service.export;

import com.yiwise.base.common.utils.HandleByPageUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.core.dal.dao.FilteredRobotCallTaskPOMapper;
import com.yiwise.core.dal.dao.RobotCallJobFolderPOMapper;
import com.yiwise.core.dal.dao.RobotCallJobPOMapper;
import com.yiwise.core.dal.entity.CallDetailPO;
import com.yiwise.core.dal.entity.RobotCallJobFolderPO;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.model.dto.RobotCallJobInfoByFolderBO;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.engine.calljob.RobotCallJobFolderService;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.ope.platform.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @date: 2024 07 26 13:48
 */
@Slf4j
@Service
public class ExportCallRecordServiceImpl {

    @Resource
    private RobotCallJobFolderService robotCallJobFolderService;
    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private TenantService tenantService;
    @Resource
    private RobotCallJobFolderPOMapper robotCallJobFolderPOMapper;
    @Resource
    private RobotCallJobPOMapper robotCallJobPOMapper;
    @Resource
    private FilteredRobotCallTaskPOMapper filteredRobotCallTaskPOMapper;


    /**
     * 需要撰写取数脚本，可支持基于租户ID+文件夹名称，
     * 将文件夹下的所有外呼任务通话记录数据，以已接通、未接通、过滤分别导出3个Excel文件
     * Excel文件名称：文件夹名称-已接通、文件夹名称-未接通、文件夹名称-过滤
     * 导出字段：联系电话、任务名称、任务id、外呼时间
     * 是否去重：单一任务内去重（例如A任务下已接通只取最后一次接通的数据）
     */
    public void exportDataByCSV(String company,List<Long> tenantIds, List<String> folderNames) {

//        if (tenantIds == null || folderNames == null) {
//            return;
//        }
////        Map<String, Set<Long>> folderJobIdsMap = new HashMap<>();
//        List<Long> handleJobIdList = new ArrayList<>();
//        for (Long tenantId : tenantIds) {
//            // 查询第一层文件夹 及每个文件下的子文件夹
//            Map<Long, List<Long>> allSubFolders = robotCallJobFolderService.selectFirstLevelFolderAndSubFolders(tenantId);
//
//            List<RobotCallJobFolderPO> folderPOList = robotCallJobFolderPOMapper.selectByTenantAndNames(tenantId, folderNames);
//            Set<Long> useFolder = folderPOList.stream().map(RobotCallJobFolderPO::getRobotCallJobFolderId).collect(Collectors.toSet());
//
//            // 获取首层文件夹下 对应的任务id
//            for (Long folderId : allSubFolders.keySet()) {
//                if (!useFolder.contains(folderId)) {
//                    continue;
//                }
//
//                List<Long> subFolders = allSubFolders.get(folderId);
//
//                List<Long> jobPOList = robotCallJobPOMapper.selectAllByFolderIds(tenantId, subFolders);
//
//                handleJobIdList.addAll(jobPOList);
//            }
//        }
////        folderJobIdsMap.put(company, new HashSet<>(handleJobIdList));
//
//
//        log.info("folderJobIdsMap = {}",JsonUtils.object2String(handleJobIdList));
//
//        Map<? extends Serializable, RobotCallJobPO> allJobInfo = robotCallJobService.selectMapByKeyCollect(handleJobIdList);
//        for(int m = 1; m < 7 ; m++) {
//            String parentDirPath = TempFilePathKeyCenter.getCommonTempFilePath() + "exportCsv/" + company;
//            File dir = new File(parentDirPath);
//            // 目录不存在则创建
//            if (!dir.exists() && !dir.isDirectory()) {
//                dir.mkdirs();
//            }
//
//            String fileName = company + "-" + m + "月-已接通.csv";
//            String filePath = String.format("%s/%s", parentDirPath, fileName);
//
//
//            // 先写表头
//            exportToCsv(new ArrayList<>(), filePath, true);
//            // 查询已接通
//            int finalM = m;
//            HandleByPageUtils.handlePage(500000, () -> callRecordPOADBMapper.selectByJobIdsAndMonth(handleJobIdList, LocalDate.of(2024, Month.of(finalM),1),LocalDate.of(2024, Month.of(finalM+1),1)),
//                    (list) -> {
//                        list.forEach(item -> {
//                            RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
//                            if (jobPO != null) {
//                                item.setRobotCallJobName(jobPO.getName());
//                            }
//                        });
//                        exportToCsv(list, filePath, false);
//                    });
//
////            // 查询已接通
////            List<RobotCallJobInfoByFolderBO> robotCallJobInfo = callRecordPOADBMapper.selectByJobIds(tenantId, jobPOList);
////            robotCallJobInfo.forEach( item -> {
////                RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
////                if (jobPO != null) {
////                    item.setRobotCallJobName(jobPO.getName());
////                }
////            });
//
//            String fileName2 = company + "-" + m + "月-未接通.csv";
//            String filePath2 = String.format("%s/%s", parentDirPath, fileName2);
//            exportToCsv(new ArrayList<>(), filePath2, true);
//            // 查询未接通
//            HandleByPageUtils.handlePage(500000, () -> callRecordPOADBMapper.selectNoAnswerByJobIdsAndMonth(handleJobIdList, LocalDate.of(2024, Month.of(finalM),1),LocalDate.of(2024, Month.of(finalM+1),1)),
//                    (list) -> {
//                        list.forEach(item -> {
//                            RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
//                            if (jobPO != null) {
//                                item.setRobotCallJobName(jobPO.getName());
//                            }
//                        });
//                        exportToCsv(list, filePath2, false);
//                    });


//            // 查询未接通
//            List<RobotCallJobInfoByFolderBO> callRecordUnCall = callRecordPOADBMapper.selectByUnEnableJobIds(tenantId, jobPOList);
//            callRecordUnCall.forEach( item -> {
//                RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
//                if (jobPO != null) {
//                    item.setRobotCallJobName(jobPO.getName());
//                }
//            });
//            exportToCsv(callRecordUnCall, filePath2, false);


//            String fileName3 = tenantId + "-" + folderPO.getName() + "-过滤.csv";
//            String filePath3 = String.format("%s/%s", parentDirPath, fileName3);
//            exportToCsv(new ArrayList<>(), filePath3, true);
//            // 查询过滤
//            HandleByPageUtils.handlePage(500000, () -> filteredRobotCallTaskPOMapper.selectByJobIds(tenantId, jobPOList),
//                    (list) -> {
//                        list.forEach(item -> {
//                            RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
//                            if (jobPO != null) {
//                                item.setRobotCallJobName(jobPO.getName());
//                            }
//                        });
//                        exportToCsv(list, filePath3, false);
//                    });


            // 查询过滤
//            List<RobotCallJobInfoByFolderBO> filteredCall = filteredRobotCallTaskPOMapper.selectByJobIds(tenantId, jobPOList);
//            filteredCall.forEach( item -> {
//                RobotCallJobPO jobPO = allJobInfo.get(item.getRobotCallJobId());
//                if (jobPO != null) {
//                    item.setRobotCallJobName(jobPO.getName());
//                }
//            });
//            String fileName3 = tenantId +"-"+ folderPO.getName()+"-过滤.csv";
//            String filePath3 = String.format("%s/%s", parentDirPath, fileName3);
//            exportToCsv(filteredCall, filePath3, false);
    }

    private void exportToCsv(List<RobotCallJobInfoByFolderBO> data, String filePath, boolean isWriterHeader) {
        log.info("导出csv文件, filePath = {} ", filePath);
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath, true))) {
            // 写入表头（可选），表头也加上双引号  联系电话、任务名称、任务id、外呼时间
            if (isWriterHeader) {
                writer.write("\"联系电话\",\"任务名称\",\"任务id\",\"外呼时间\",\"客户名\"\n");
            }
            for (RobotCallJobInfoByFolderBO bo : data) {
                // 写入数据行
                writer.write(bo.toStringByCsv());
            }
        } catch (IOException e) {
            log.error("导出出错 filePath = {}", filePath, e);
        } catch (Exception err) {
            log.error("导出出错err filePath = {}", filePath, err);
        }
    }

}
