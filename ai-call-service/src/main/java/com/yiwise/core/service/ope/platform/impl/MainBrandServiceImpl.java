package com.yiwise.core.service.ope.platform.impl;

import com.github.pagehelper.PageHelper;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.bill.statistic.model.vo.BillStatisticTenantVO;
import com.yiwise.bill.statistic.service.BillStatisticSyncMetaInfoService;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.entity.dto.SheetInfoDTO;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.*;
import com.yiwise.core.dal.dao.MainBrandPOMapper;
import com.yiwise.core.dal.dao.TenantPOMapper;
import com.yiwise.core.dal.entity.MainBrandPO;
import com.yiwise.core.dal.mongo.TenantConsumeDailyPO;
import com.yiwise.core.model.dto.stats.TenantSimpleInfoDTO;
import com.yiwise.core.model.enums.PlatformTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.tenant.*;
import com.yiwise.core.service.batchjob.BasicBatchService;
import com.yiwise.core.service.header.HeaderService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.MainBrandService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.utils.MyCollectionUtils;
import javaslang.Tuple;
import javaslang.Tuple3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.batch.core.Job;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.core.batch.common.BatchConstant.SheetInfoList.MAIN_BRAND_TENANT_COST_EXPORT_TUPLE_LIST;
import static com.yiwise.core.config.TableUrlConstant.APIOPE_MAINBRAND_LIST;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.EXPORT_MAIN_BRAND_LIST;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.EXPORT_MAIN_BRAND_TENANT_COST;


/**
 * <AUTHOR>
 */
@Service
public class MainBrandServiceImpl extends BasicServiceImpl<MainBrandPO> implements MainBrandService {

    @Resource
    private MainBrandPOMapper mainBrandPOMapper;

    @Resource
    private TenantPOMapper tenantPOMapper;

    @Resource
    private UserService userService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private BasicBatchService basicBatchService;

    @Resource(name = "mainBrandExportJob")
    private Job mainBrandListExportJob;

    @Resource(name = "mainBrandTenantCostExportJob")
    private Job mainBrandTenantCostExportJob;

    @Resource
    private HeaderService headerService;

    @Resource
    private BillStatisticSyncMetaInfoService billStatisticSyncMetaInfoService;

    @Override
    public void addMainBrand(MainBrandPO mainBrandPO) {
        if (StringUtils.isBlank(mainBrandPO.getFkMainBrandId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "主品牌ID不能为空");
        }
        if (Objects.nonNull(mainBrandPOMapper.selectOneByFkMainBrandId(mainBrandPO.getFkMainBrandId()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "品牌ID已存在");
        }
        mainBrandPO.setMainBrandId(null);
        saveNotNull(mainBrandPO);
    }

    @Override
    public void deleteByFkMainBrandId(String fkMainBrandId) {
        if (StringUtils.isBlank(fkMainBrandId)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "主品牌ID不能为空");
        }
        mainBrandPOMapper.deleteByFkMainBrandId(fkMainBrandId);
    }

    @Override
    public void updateByFkMainBrand(MainBrandPO mainBrandPO) {
        if (StringUtils.isBlank(mainBrandPO.getFkMainBrandId())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "主品牌ID不能为空");
        }
        if (Objects.isNull(mainBrandPOMapper.selectOneByFkMainBrandId(mainBrandPO.getFkMainBrandId()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "主品牌信息不存在");
        }
        mainBrandPOMapper.updateByFkMainBrand(mainBrandPO);

        //同步到计费统计服务
        List<Long> tenantIds = tenantPOMapper.selectByBrandId(mainBrandPO.getMainBrandId());
        List<BillStatisticTenantVO> list = new ArrayList<>();
        tenantIds.forEach(id -> {
            BillStatisticTenantVO tenantVO = new BillStatisticTenantVO();
            tenantVO.setTenantId(id);
            tenantVO.setMainBrandName(mainBrandPO.getMainBrandName());
            tenantVO.setEnv(CommonApplicationConstant.CURR_STATISTIC_ENV);
            list.add(tenantVO);
        });
        billStatisticSyncMetaInfoService.batchSaveTenant(list);

    }

    @Override
    public PageResultObject<MainBrandVO> listMainBrand(MainBrandQueryVO queryVO) {
        dealDataAuth(queryVO);
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<MainBrandVO> mainBrandVOList = mainBrandPOMapper.selectMainBrandPage(queryVO);
        mainBrandVOList.forEach(vo -> vo.setId(vo.getMainBrandId()));
        return PageResultObject.of(mainBrandVOList);
    }

    @Override
    public JobStartResultVO exportMainBrand(MainBrandQueryVO queryVO) {
        dealDataAuth(queryVO);
        List<Tuple3<String, String, Object>> param = Lists.newArrayList(
                Tuple.of("queryVO", "Object", queryVO)
        );
        List<String> headerListCache = headerService.getHeaderListCache(APIOPE_MAINBRAND_LIST, PlatformTypeEnum.AICC);
        ArrayList<SheetInfoDTO> sheetInfoDTOS = Lists.newArrayList(SheetInfoDTO.of(BatchConstant.SheetName.DATA_REPORT_EXPORT_SHEET_NAME, headerListCache));
        return basicBatchService.exportWithQuery(DataSourceEnum.SLAVE, queryVO.getTenantId(), queryVO.getCurrentUserId(), SystemEnum.OPE, mainBrandListExportJob, EXPORT_MAIN_BRAND_LIST, param, sheetInfoDTOS);
    }

    @Override
    public PageResultObject<MainBrandCostResultVO> listMainBrandTenantCost(MainBrandCostQueryVO queryVO) {
        Assert.notNull(queryVO.getStartDate(), "查询开始时间不能为空");
        Assert.notNull(queryVO.getEndDate(), "查询结束时间不能为空");
        //数据权限
        dealDataAuth(queryVO);
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<TenantSimpleInfoDTO> tenantSimpleInfoDTOS = tenantPOMapper.selectByMainBrandIdPage(queryVO);
        if (CollectionUtils.isEmpty(tenantSimpleInfoDTOS)) {
            return PageResultObject.of(Lists.emptyList());
        }

        PageResultObject pageResultObject = PageResultObject.of(tenantSimpleInfoDTOS);
        List<Long> tenantIdList = tenantSimpleInfoDTOS.stream().map(TenantSimpleInfoDTO::getTenantId).collect(Collectors.toList());
        //费用消耗
        Map<Long, TenantCostVO> tenantCostVOMap = queryTenantCostByTenantIds(tenantIdList, queryVO.getStartDate(), queryVO.getEndDate());
        //充值金额
        Map<Long, TenantConsumeDailyPO> tenantConsumeDailyPOMap = queryTenantRechargeByTenantIds(tenantIdList, queryVO.getStartDate(), queryVO.getEndDate());
        //账户余额
        Map<Long, TenantConsumeDailyPO> accountDailyPOMap = queryTenantAccountByTenantIds(tenantIdList, queryVO.getEndDate());

        List<MainBrandCostResultVO> resultVOList = new ArrayList<>();
        tenantSimpleInfoDTOS.forEach(dto -> {
            MainBrandCostResultVO resultVO = new MainBrandCostResultVO(dto.getTenantId(), dto.getName());
            TenantCostVO tenantCostVO = tenantCostVOMap.get(dto.getTenantId());
            if (Objects.nonNull(tenantCostVO)) {
                resultVO.setCallCost(tenantCostVO.getCallCost());
                resultVO.setSmsCost(tenantCostVO.getSmsCost() - tenantCostVO.getRefund());
            }
            TenantConsumeDailyPO tenantConsumeDailyPO = tenantConsumeDailyPOMap.get(dto.getTenantId());
            if (Objects.nonNull(tenantConsumeDailyPO)) {
                resultVO.setRecharge(tenantConsumeDailyPO.getRecharge());
            }
            TenantConsumeDailyPO accountDailyPO = accountDailyPOMap.get(dto.getTenantId());
            if (Objects.nonNull(accountDailyPO)) {
                resultVO.setAllAccountFare(accountDailyPO.getAllAccountFare());
                resultVO.setPhoneAccountFare(accountDailyPO.getPhoneAccountFare());
                resultVO.setSmsAccountFare(accountDailyPO.getSmsAccountFare());
            }
            resultVOList.add(resultVO);
        });

        pageResultObject.setContent(resultVOList);

        return pageResultObject;
    }

    @Override
    public JobStartResultVO exportMainBrandTenantCost(MainBrandCostQueryVO queryVO) {
        List<Tuple3<String, String, Object>> param = Lists.newArrayList(
                Tuple.of("queryVO", "Object", queryVO)
        );
        return basicBatchService.exportWithQuery(DataSourceEnum.SLAVE, queryVO.getTenantId(), queryVO.getCurrentUserId(), SystemEnum.OPE, mainBrandTenantCostExportJob, EXPORT_MAIN_BRAND_TENANT_COST, param, MAIN_BRAND_TENANT_COST_EXPORT_TUPLE_LIST);
    }

    @Override
    public List<MainBrandSimpleVO> selectMainBrand(String name) {
        return mainBrandPOMapper.selectMainBrandLimit(name);
    }

    @Override
    public MainBrandPO selectOneByMainBrandId(String mainBrandId) {
        return mainBrandPOMapper.selectOneByFkMainBrandId(mainBrandId);
    }

    /**
     * 处理数据权限
     */
    private void dealDataAuth(MainBrandQueryDataAuthVO dataAuthVO) {
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(dataAuthVO.getCurrentUserId())) {
            //个人的数据权限
            Set<Long> userId = new HashSet<>();
            userId.add(dataAuthVO.getCurrentUserId());
            dataAuthVO.setFilterUserIdSet(userId);
        } else if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(dataAuthVO.getCurrentUserId())) {
            //所在组及下级组的数据权限
            dataAuthVO.setFilterUserIdSet(userService.getOpeChildUserIdByParentUserId(dataAuthVO.getCurrentUserId()));
        }
    }

    /**
     * 获取客户的费用消耗
     *
     * @param tenantIds
     * @return
     */
    private Map<Long, TenantCostVO> queryTenantCostByTenantIds(List<Long> tenantIds, LocalDate startDate, LocalDate endDate) {
        List<AggregationOperation> operations = new ArrayList<>();
        operations.add(Aggregation.match(Criteria.where("tenantId").in(tenantIds)));
        operations.add(Aggregation.match(Criteria.where("formal").is(1)));
        operations.add(Aggregation.match(Criteria.where("localDateTime").gte(startDate).lt(endDate.plusDays(1))));
        operations.add(Aggregation.group("tenantId").first("tenantId").as("tenantId")
                .sum("callCost").as("callCost")
                .sum("refund").as("refund")
                .sum("smsCost").as("smsCost"));
        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<TenantCostVO> mappedResults = mongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.CALL_STATS_ROBOT_DONE_JOB, TenantCostVO.class).getMappedResults();
        return MyCollectionUtils.listToMap(mappedResults, TenantCostVO::getTenantId);
    }

    /**
     * 查询客户充值金额
     *
     * @param tenantIds
     * @param startDate
     * @param endDate
     */
    private Map<Long, TenantConsumeDailyPO> queryTenantRechargeByTenantIds(List<Long> tenantIds, LocalDate startDate, LocalDate endDate) {
        GroupOperation groupOperation = Aggregation.group("tenantId").first("tenantId").as("tenantId")
                .sum("recharge").as("recharge");
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("tenantId").in(tenantIds)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("localDate").gte(startDate).lt(endDate.plusDays(1))));
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        List<TenantConsumeDailyPO> resList = mongoTemplate.aggregate(aggregation, TenantConsumeDailyPO.COLLECTION_NAME, TenantConsumeDailyPO.class).getMappedResults();
        return MyCollectionUtils.listToMap(resList, TenantConsumeDailyPO::getTenantId);
    }

    /**
     * 查询账户余额信息
     *
     * @param tenantIds
     * @param endDate
     * @return
     */
    private Map<Long, TenantConsumeDailyPO> queryTenantAccountByTenantIds(List<Long> tenantIds, LocalDate endDate) {
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").in(tenantIds).and("localDate").is(endDate));
        List<TenantConsumeDailyPO> resList = mongoTemplate.find(query, TenantConsumeDailyPO.class);
        return MyCollectionUtils.listToMap(resList, TenantConsumeDailyPO::getTenantId);
    }
}
