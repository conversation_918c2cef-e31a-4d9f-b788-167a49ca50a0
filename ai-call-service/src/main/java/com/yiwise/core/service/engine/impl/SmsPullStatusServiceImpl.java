package com.yiwise.core.service.engine.impl;

import com.alicom.mns.tools.DefaultAlicomMessagePuller;
import com.yiwise.aicc.common.enums.billing.SourceModuleEnum;
import com.yiwise.aicc.common.enums.billing.TradeTypeEnum;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.billing.api.bo.request.RefundRequestBO;
import com.yiwise.billing.api.bo.response.CostResponseBO;
import com.yiwise.aicc.common.enums.billing.SourceModuleEnum;
import com.yiwise.aicc.common.enums.billing.TradeTypeEnum;
import com.yiwise.core.dal.dao.SmsJobMessagePOMapper;
import com.yiwise.core.dal.dao.TenantPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.SmsTestStatPO;
import com.yiwise.core.feignclient.billing.CostClient;
import com.yiwise.core.feignclient.lcs.LcsSmsReceiveClient;
import com.yiwise.core.feignclient.ma.MaSmsCallbackClient;
import com.yiwise.core.feignclient.rcs.CustomerBlackListRemoteClient;
import com.yiwise.core.feignclient.rcs.SmsCancelBlackConfigApiClient;
import com.yiwise.core.helper.SmsHelper;
import com.yiwise.core.helper.objectstorage.AddOssPrefixSerializer;
import com.yiwise.core.lock.RedisLock;
import com.yiwise.core.model.bo.mq.SmsCallBackMessageBO;
import com.yiwise.core.model.bo.sms.*;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.SendMessageStatusEnum;
import com.yiwise.core.model.enums.isv.ISVDataTypeEnum;
import com.yiwise.core.model.enums.ope.DistributorTypeEnum;
import com.yiwise.core.model.enums.sms.SmsBusinessTypeEnum;
import com.yiwise.core.model.vo.billing.BillingCalloutSmsContext;
import com.yiwise.core.model.vo.sms.YiwiseSmsGatewayReceivedCallbackVO;
import com.yiwise.core.service.engine.SmsTemplateService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.asyncjob.impl.AsyncJobServiceImpl;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.callstats.CallStatsService;
import com.yiwise.core.service.engine.isv.IsvCallbackService;
import com.yiwise.core.service.financial.SmsFinancialService;
import com.yiwise.core.service.mongo.CallStatsMongoService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.ope.stats.impl.SmsTestStatService;
import com.yiwise.core.service.openapi.platform.IsvInfoService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.redis.RedisLockKeyHelper;
import com.yiwise.core.service.sms.SmsCostBillingDetailContext;
import com.yiwise.core.service.sms.SmsCostBillingDetailService;
import com.yiwise.core.service.sms.impl.*;
import com.yiwise.core.service.stats.IntentMessageStatsService;
import com.yiwise.lcs.api.dto.*;
import com.yiwise.lcs.api.enums.*;
import com.yiwise.ma.center.api.dto.request.MaSmsBillingCallbackRequest;
import com.yiwise.ma.center.api.dto.request.MaSmsCallbackRequest;
import com.yiwise.rcs.api.dto.SmsCancelBlackDTO;
import javaslang.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yiwise.core.config.ApplicationConstant.FINANCIAL_GRAY_TEST_ENABLED;
import static com.yiwise.core.config.ApplicationConstant.FINANCIAL_GRAY_TEST_TENANTS;

/**
 * @Author: wangguomin
 * @Date: 2019-11-07 20:17
 */
@Service
public class SmsPullStatusServiceImpl implements SmsPullStatusService {

    private static final Logger logger = LoggerFactory.getLogger(SmsPullStatusServiceImpl.class);
    private static final String FAIL = "FAIL";
    private static final String SUCCESS = "SUCCESS";
    private static final String PENDING = "PENDING";

    @Resource
    private IntentMessageService intentMessageService;

    @Resource
    private TenantPOMapper tenantPOMapper;

    @Resource
    CallStatsMongoService callStatsMongoService;

    @Resource
    SmsJobMessagePOMapper smsJobMessagePOMapper;

    @Resource
    private SmsJobMessageService smsJobMessageService;

    @Resource
    private SmsTestStatService smsTestStatService;

    @Resource
    private SmsPlatformChannelService smsPlatformChannelService;

    @Resource
    private CustomerSmsMessageService customerSmsMessageService;

    @Resource
    private SmsTemplateService smsTemplateService;

    private static DefaultAlicomMessagePuller puller;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private TenantService tenantService;
    @Resource
    private CallRecordInfoService callRecordInfoService;

    @Resource
    private SmsJobService smsJobService;
    @Resource
    private SmsRefundService smsRefundService;
    @Resource
    private CallStatsService callStatsService;
    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private AsyncJobServiceImpl asyncJobService;

    @Resource
    private CustomerBlackListRemoteClient customerBlackListRemoteClient;

    @Resource
    private SmsReceiveRecordService smsReceiveRecordService;

    @Resource
    private IntentMessageStatsService intentMessageStatsService;

    @Resource
    private MaSmsCallbackClient smsCallbackClient;

    @Resource
    private IsvCallbackService isvCallbackService;

    @Resource
    private IsvInfoService isvInfoService;

    @Resource
    private CostClient costClient;

    @Resource
    private SmsCallBackNewService smsCallBackNewService;

    @Resource
    private LcsSmsReceiveClient lcsSmsReceiveClient;

    @Resource
    private SmsCancelBlackConfigApiClient smsCancelBlackConfigApiClient;

    @Resource
    private SmsCostBillingDetailContext smsCostBillingDetailContext;
    @Resource
    private SmsCostBillingDetailForJobServiceImpl smsCostBillingDetailForJobService;
    @Resource
    private SmsCostBillingDetailForIntentServiceImpl smsCostBillingDetailForIntentService;
    @Resource
    private SmsCostBillingDetailForCustomerServiceImpl smsCostBillingDetailForCustomerService;
    @Resource
    private SmsCostBillingDetailService smsCostBillingDetailService;
	@Resource
	private SmsFinancialService smsFinancialService;
	@Resource
	private DistributorService distributorService;
    @Resource
    private IntentMessageReSend intentMessageReSend;

    static {
        puller = new DefaultAlicomMessagePuller();
        //设置异步线程池大小及任务队列的大小，还有无数据线程休眠时间
        puller.setConsumeMinThreadSize(6);
        puller.setConsumeMaxThreadSize(16);
        puller.setThreadQueueSize(200);
        puller.setPullMsgThreadSize(1);
        //和服务端联调问题时开启,平时无需开启，消耗性能
        puller.openDebugLog(false);
    }

    @Override
    public void pullStatus() {

        logger.info("拉短信结束");
    }

    @Override
    public boolean smsCallBackHandle(SmsCallBackMessageBO messageBO) {
        logger.info("短信回调接收到数据：{}", messageBO);
        String sid = messageBO.getSid();
        if (StringUtils.isBlank(sid)) {
            return true;
        }
            try {
                SmsReportDTO doSmsReport = lcsSmsReceiveClient.doSmsReportResult(SmsCallBackMessageDTO.builder()
                        .smsPlatform(messageBO.getSmsPlatform())
                        .sid(sid)
                        .mobile(messageBO.getMobile())
                        .reportStatus(messageBO.getReportStatus())
                        .reportStatusCode(messageBO.getReportCode())
                        .errorMsg(messageBO.getErrorMsg())
                        .userReceiveTime(messageBO.getUserReceiveTime())
                        .cost(messageBO.getCost())
		                .channelNo(messageBO.getChannelNo())
		                .count(messageBO.getCount())
		                .price(messageBO.getPrice())
                        .errorCode(messageBO.getInitStatusCode())
                        .firstChannelNo(messageBO.getInitChannelNo())
                        .build());
                if (BooleanUtils.isTrue(doSmsReport.getSuccess()) &&
                        (Objects.equals(doSmsReport.getSmsSource(), SmsSourceEnum.API)
                                || Objects.equals(doSmsReport.getSmsSource(), SmsSourceEnum.CALLOUT)
                                || Objects.equals(doSmsReport.getSmsSource(), SmsSourceEnum.VIDEO)
                                || Objects.equals(doSmsReport.getSmsSource(), SmsSourceEnum.MA))) {
                    return true;
                }
            } catch (Exception e) {
                logger.error("smsCallBackHandle error", e);
            }
        Tuple3<IntentMessagePO, SmsJobMessagePO, CustomerSmsMessagePO> tuple3 = getMessageBySid(sid);
        if (Objects.isNull(tuple3._1()) && Objects.isNull(tuple3._2()) && Objects.isNull(tuple3._3())) {
            //有可能是短信发送记录还没有入库成功，返回失败等待mq消息重试
            //会有验证码预警等短信没有入库，防止进入死信队列
            String smsReportRetryCountKey = RedisKeyCenter.getSmsReportRetryCountRedisKey(sid);
            Long retryCount = redisOpsService.incrementKey(smsReportRetryCountKey);
            redisOpsService.expire(smsReportRetryCountKey, 10, TimeUnit.MINUTES);
            if (retryCount > 4) {
                redisOpsService.delete(smsReportRetryCountKey);
                return true;
            }
            return false;
        }
        //只处理意向短信
        RobotCallJobPO robotCallJob = null;
        if (!Objects.isNull(tuple3._1())) {
            robotCallJob = robotCallJobService.selectByKey(tuple3._1.getRobotCallJobId());
        }
        String jobDescription = null != robotCallJob ? robotCallJob.getDescription() : "";
        if (Objects.nonNull(tuple3._1()) && PENDING.equals(tuple3._1().getReportStatus())) {
            SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(tuple3._1().getSmsTemplateId());
            //如果用户单独设置了价格 就用设置的价格
            SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(smsTemplatePO.getTenantId(), smsTemplatePO.getSmsPlatformChannelId(), smsTemplatePO.getNewSmsPlatform());
            // 意向短信的退费
            updateIntentMessageForRefund(messageBO, tuple3._1(), channelWithTenantPriceDTO);
            smsReceptResultCallback(messageBO, tuple3._1.getTenantId(), tuple3._1.getIntentMessageId(), tuple3._1.getCustomerPersonName(), "AI外呼任务短信", tuple3._1.getRobotCallJobName(), tuple3._1.getSmsTemplateId(), tuple3._1.getSmsTemplateName(), tuple3._1.getMessageDetail(), tuple3._1.getCallRecordId(), tuple3._1.getPhoneNumber(), jobDescription, null, tuple3._1.getCreateTime());

            receiveFailByReSendSms(robotCallJob, messageBO, tuple3._1());
        } else if (Objects.nonNull(tuple3._2()) && PENDING.equals(tuple3._2().getReportStatus())) {
            SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(tuple3._2().getSmsTemplateId());
            //如果用户单独设置了价格 就用设置的价格
            SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(smsTemplatePO.getTenantId(), smsTemplatePO.getSmsPlatformChannelId(), smsTemplatePO.getNewSmsPlatform());
            // 短信任务的退费
            updateSmsJobMessageForRefund(messageBO, tuple3._2(), channelWithTenantPriceDTO);
            SmsJobPO smsJobPO = smsJobService.selectByKey(tuple3._2.getSmsJobId());
            smsReceptResultCallback(messageBO, tuple3._2.getTenantId(), tuple3._2.getSmsJobMessageId(), tuple3._2.getCustomerPersonName(), "短信任务短信", Objects.nonNull(smsJobPO) ? smsJobPO.getName() : null, tuple3._2.getSmsTemplateId(), tuple3._2.getSmsTemplateName(), tuple3._2.getMessageDetail(), null, tuple3._2.getPhoneNumber(), jobDescription, null, tuple3._2.getCreateTime());
        } else if (Objects.nonNull(tuple3._3()) && PENDING.equals(tuple3._3().getReportStatus()) && Objects.nonNull(tuple3._3().getSmsTemplateId())) {
            if(SystemEnum.isOPE(tuple3._3().getSystemType())){
                secondSave(tuple3._3(),messageBO);
            } else {
                try {
                    //接收状态回调MA
                    MaSmsCallbackRequest request = new MaSmsCallbackRequest();
                    request.setSuccess(SUCCESS.equals(messageBO.getReportStatus()));
                    request.setMessageId(messageBO.getSid());
                    request.setErrorMessage(messageBO.getErrorMsg());
	                request.setCost(messageBO.getCost());
	                request.setChannelNo(messageBO.getChannelNo());
	                request.setCount(messageBO.getCount());
	                request.setPrice(messageBO.getPrice());
                    smsCallbackClient.sendCallback(request);
                } catch (Exception e) {
                    logger.error("smsCallBackHandle smsCallbackClient sendCallback error", e);
                }
                SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(tuple3._3().getSmsTemplateId());
                //如果用户单独设置了价格 就用设置的价格
                SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(smsTemplatePO.getTenantId(), smsTemplatePO.getSmsPlatformChannelId(), smsTemplatePO.getNewSmsPlatform());
                //更新CustomerSmsMessage
                updateCustomerSmsMessageForRefund(messageBO, tuple3._3(), channelWithTenantPriceDTO);
                smsReceptResultCallback(messageBO, tuple3._3.getTenantId(), tuple3._3.getCustomerSmsMessageId(), tuple3._3.getCustomerPersonName(), "MA短信", null, tuple3._3.getSmsTemplateId(), tuple3._3.getSmsTemplateName(), tuple3._3.getMessageDetail(), null, tuple3._3.getPhoneNumber(), jobDescription, tuple3._3.getProperties(), tuple3._3.getCreateTime());
            }
        }
        return true;
    }

    private void receiveFailByReSendSms(RobotCallJobPO robotCallJob, SmsCallBackMessageBO messageBO, IntentMessagePO intentMessagePO) {
        if (robotCallJob != null && BooleanUtils.isTrue(robotCallJob.getEnableReSendSms()) && Objects.equals(messageBO.getReportStatus(), FAIL)) {
            RedisLock redisLock = new RedisLock(redisOpsService.getRedisTemplate(), String.format(RedisLockKeyHelper.RESEND_SMS_LOCK, intentMessagePO.getCallRecordId()));
            try {
                boolean getLock = redisLock.lock();
                if (getLock) {
                    intentMessageReSend.receiveFailByReSend(intentMessagePO);
                }else {
                    logger.info("短信补推-不可同时操作");
                }
            } finally {
                redisLock.unlock();
            }
        }
    }

    private void smsReceptResultCallback(SmsCallBackMessageBO messageBO, Long tenantId, Long recordId, String customerPersonName, String smsType, String smsSource, Long smsTemplateId, String templateName, String messageDetail, Long callRecordId, String phoneNumber, String jobdescription, Map<String, String> properties, LocalDateTime createTime) {
        try {
            IsvInfoPO isvInfo = isvInfoService.findByTenantId(tenantId);
            if (isvInfo != null) {
                ISVDataTypeEnum isvDataTypeEnum = ISVDataTypeEnum.SMS_RECEPT_RECORD;
                if (CollectionUtils.isNotEmpty(isvInfo.getEventSubscriptionTypes())
                        && isvInfo.getEventSubscriptionTypes().contains(isvDataTypeEnum)
                        && StringUtils.isNotBlank(isvInfo.getEventSubscriptionUrl())) {
                    SmsReceptCallRecordBO callRecordBO = new SmsReceptCallRecordBO();
                    if (Objects.nonNull(callRecordId)) {
                        CallRecordPO callRecordPO = callRecordInfoService.selectByKey(callRecordId);
                        if (Objects.nonNull(callRecordPO)) {
                            properties = callRecordPO.getProperties();
                            callRecordBO.setRobotCallJobId(callRecordPO.getRobotCallJobId());
                            callRecordBO.setResultStatus(callRecordPO.getResultStatus());
                            callRecordBO.setStartTime(callRecordPO.getStartTime());
                            callRecordBO.setFullAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(callRecordPO.getFullAudioUrl()));
                            callRecordBO.setChatDuration(callRecordPO.getChatDuration());
                            callRecordBO.setIntentLevel(CodeDescEnum.getFromCodeOrNull(IntentLevelEnum.class, callRecordPO.getIntentLevel()));
                        }
                    } else {
                        //尝试获取MA的通话记录
                        if (MapUtils.isNotEmpty(properties)) {
                            String relatedCallRecordId = properties.get("relatedCallRecordId");
                            if (StringUtils.isNotBlank(relatedCallRecordId)) {
                                callRecordId = Long.valueOf(relatedCallRecordId);
                            }
                        }
                    }
                    SmsReceptResultBO smsReceptResultBO = SmsReceptResultBO.builder()
                            .recordId(recordId)
                            .callRecordId(callRecordId)
                            .customerPersonName(customerPersonName)
                            .phoneNumber(phoneNumber)
                            .smsType(smsType)
                            .smsSource(smsSource)
                            .createTime(createTime)
                            .updateTime(StringUtils.isNotBlank(messageBO.getUserReceiveTime()) ? LocalDateTime.parse(messageBO.getUserReceiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : LocalDateTime.now())
                            .reportStatus(messageBO.getReportStatus())
                            .reportCode(messageBO.getReportCode())
                            .smsTemplateId(smsTemplateId)
                            .smsTemplateName(templateName)
                            .jobDescription(jobdescription)
                            .messageDetail(messageDetail)
                            .properties(properties)
                            .callRecord(callRecordBO)
                            .build();
                    logger.info("发送短信接收结果回调请求,body:{}", JsonUtils.object2String(smsReceptResultBO));
                    isvCallbackService.smsReceptRecordCallback(isvInfo, ISVDataTypeEnum.SMS_RECEPT_RECORD, recordId, smsReceptResultBO, "smsReceptResultCallback");
                }
            }
        } catch (Exception e) {
            logger.error("发送短信接收结果回调请求失败,phoneNumber:{}", messageBO.getMobile(), e);
        }
    }

    @Override
    public void recvCallbackHandle(YiwiseSmsGatewayReceivedCallbackVO callback) {
        logger.info("receive user msg ：【{}】", callback);
        if (Objects.isNull(callback) || Objects.isNull(callback.getMid()) || StringUtils.isBlank(callback.getMobile())
                || StringUtils.isBlank(callback.getRecvContent()) || StringUtils.isBlank(callback.getSendContent())) {
            return;
        }
        try {
            Boolean doSmsReceive = lcsSmsReceiveClient.doSmsReceive(YiwiseSmsGatewayReceivedCallbackDTO.builder()
                    .appKey(callback.getAppKey())
                    .channelNo(callback.getChannelNo())
                    .mobile(callback.getMobile())
                    .recvTime(callback.getRecvTime())
                    .recvContent(callback.getRecvContent())
                    .mid(callback.getMid())
                    .sendTime(callback.getSendTime())
                    .sendContent(callback.getSendContent())
                    .build());
            if (BooleanUtils.isTrue(doSmsReceive)) {
                return;
            }
        } catch (Exception e) {
            logger.error("recvCallbackHandle error", e);
        }

        String sid = callback.getMid() + callback.getMobile();
        Tuple3<IntentMessagePO, SmsJobMessagePO, CustomerSmsMessagePO> tuple3 = getMessageBySid(sid);
        SmsCancelBlackDTO smsCancelBlackDTO = null;
        if (Objects.nonNull(tuple3._1())) {
            IntentMessagePO intentMessagePO = tuple3._1();
            smsCancelBlackDTO = SmsCancelBlackDTO.builder()
                    .tenantId(intentMessagePO.getTenantId())
                    .phoneNumber(intentMessagePO.getPhoneNumber())
                    .content(callback.getRecvContent())
                    .accountName(intentMessagePO.getCustomerPersonName())
                    .build();
        } else if (Objects.nonNull(tuple3._2())) {
            SmsJobMessagePO smsJobMessagePO = tuple3._2();
            smsCancelBlackDTO = SmsCancelBlackDTO.builder()
                    .tenantId(smsJobMessagePO.getTenantId())
                    .phoneNumber(smsJobMessagePO.getPhoneNumber())
                    .content(callback.getRecvContent())
                    .accountName(smsJobMessagePO.getCustomerPersonName())
                    .build();
        } else if (Objects.nonNull(tuple3._3())) {
            CustomerSmsMessagePO customerSmsMessagePO = tuple3._3();
            smsCancelBlackDTO = SmsCancelBlackDTO.builder()
                    .tenantId(customerSmsMessagePO.getTenantId())
                    .phoneNumber(customerSmsMessagePO.getPhoneNumber())
                    .content(callback.getRecvContent())
                    .accountName(customerSmsMessagePO.getCustomerPersonName())
                    .build();
        }
        if (Objects.isNull(smsCancelBlackDTO)) {
            return;
        }
        //添加记录
        saveSmsReceiveRecord(callback, smsCancelBlackDTO.getTenantId());
        //回调
        smsReceiveCallBack(callback, tuple3, smsCancelBlackDTO.getTenantId());
        //加黑
        try {
            smsCancelBlackConfigApiClient.doSmsCancelBlack(smsCancelBlackDTO);
        } catch (Exception e) {
            logger.error("recvCallbackHandle doSmsCancelBlack error", e);
        }
    }

    /**
     * 上行短信回调
     */
    private void smsReceiveCallBack(YiwiseSmsGatewayReceivedCallbackVO callback, Tuple3<IntentMessagePO, SmsJobMessagePO, CustomerSmsMessagePO> tuple3, Long tenantId) {
        try {
            IsvInfoPO isvInfo = isvInfoService.findByTenantId(tenantId);
            if (Objects.isNull(isvInfo)) {
                return;
            }
            if (StringUtils.isBlank(isvInfo.getEventSubscriptionUrl())) {
                return;
            }
            if (CollectionUtils.isEmpty(isvInfo.getEventSubscriptionTypes()) || !isvInfo.getEventSubscriptionTypes().contains(ISVDataTypeEnum.UP_SMS_RECORD)) {
                return;
            }
            LocalDateTime recvTime = LocalDateTime.now();
            if (Objects.nonNull(callback.getRecvTime())) {
                recvTime = LocalDateTime.ofInstant(callback.getRecvTime().toInstant(), ZoneId.systemDefault());
            }
            Long refId = 0L;
            String phoneNumber = "";
            Long callRecordId = null;
            if (Objects.nonNull(tuple3._1)) {
                refId = tuple3._1.getCallRecordId();
                phoneNumber = tuple3._1.getPhoneNumber();
                callRecordId = tuple3._1.getCallRecordId();
            } else if (Objects.nonNull(tuple3._2)) {
                refId = tuple3._2.getSmsJobMessageId();
                phoneNumber = tuple3._2.getPhoneNumber();
            } else if (Objects.nonNull(tuple3._3)) {
                refId = tuple3._3.getCustomerSmsMessageId();
                phoneNumber = tuple3._3.getPhoneNumber();
            }
            Tuple2<String, String> tuple2 = SmsHelper.getSignatureAndContentFromMsg(callback.getSendContent());
            smsCallBackNewService.exceIsvCallbackNew(isvInfo, ISVDataTypeEnum.UP_SMS_RECORD, refId, SmsUpMessageBO.builder()
                    .callRecordId(callRecordId)
                    .phoneNumber(phoneNumber)
                    .replyTime(recvTime)
                    .replyContent(callback.getRecvContent())
                    .sendSignature(tuple2._1)
                    .sendContent(tuple2._2)
                    .sendMsg(callback.getSendContent())
                    .build(), ISVDataTypeEnum.UP_SMS_RECORD.name());
        } catch (Exception e) {
            logger.error("smsReceiveCallBack error", e);
        }
    }

    private void saveSmsReceiveRecord(YiwiseSmsGatewayReceivedCallbackVO callback, Long tenantId) {
        try {
            SmsReceiveRecordPO receiveRecordPO = new SmsReceiveRecordPO();
            receiveRecordPO.setPhoneNumber(callback.getMobile());
            receiveRecordPO.setReceiveMessage(callback.getRecvContent());
            receiveRecordPO.setSendContent(callback.getSendContent());
            receiveRecordPO.setTenantId(tenantId);
            LocalDateTime recvTime = LocalDateTime.now();
            if (Objects.nonNull(callback.getRecvTime())) {
                Instant instant = callback.getRecvTime().toInstant();
                ZoneId zone = ZoneId.systemDefault();
                recvTime = LocalDateTime.ofInstant(instant, zone);
            }
            receiveRecordPO.setReceiveTime(recvTime);
            LocalDateTime sendTime = LocalDateTime.now();
            if (Objects.nonNull(callback.getSendTime())) {
                Instant instant = callback.getSendTime().toInstant();
                ZoneId zone = ZoneId.systemDefault();
                sendTime = LocalDateTime.ofInstant(instant, zone);
            }
            receiveRecordPO.setSendTime(sendTime);
            receiveRecordPO.setCreateTime(LocalDateTime.now());
            smsReceiveRecordService.save(receiveRecordPO);
        } catch (Exception e) {
            logger.info("添加回复短信记录失败");
        }
    }

    @Override
    public Tuple3<IntentMessagePO, SmsJobMessagePO, CustomerSmsMessagePO> getMessageBySid(String sid) {
        IntentMessagePO intentMessagePO = intentMessageService.getIntentMessageBySid(sid);
        if (Objects.nonNull(intentMessagePO)) {
            return Tuple.of(intentMessagePO, null, null);
        }
        SmsJobMessagePO smsJobMessagePO = smsJobMessagePOMapper.getSmsJobMessageBySid(sid);
        if (Objects.nonNull(smsJobMessagePO)) {
            return Tuple.of(null, smsJobMessagePO, null);
        }
        CustomerSmsMessagePO customerSmsMessagePO = customerSmsMessageService.getCustomerSmsMessageBySid(sid);
        return Tuple.of(null, null, customerSmsMessagePO);
    }

    private void secondSave(CustomerSmsMessagePO customerSmsMessagePO, SmsCallBackMessageBO messageBO){
        CustomerSmsMessagePO updatePO = new CustomerSmsMessagePO();
        updatePO.setCustomerSmsMessageId(customerSmsMessagePO.getCustomerSmsMessageId());
        updatePO.setUserReceiveTime(LocalDateTime.parse(messageBO.getUserReceiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        updatePO.setReportErrorDetail(messageBO.getErrorMsg());
        SmsTestStatPO smsTestStatPO = new SmsTestStatPO();
        smsTestStatPO.setSmsCreateDate(customerSmsMessagePO.getCreateTime().toLocalDate());
        SmsTemplatePO smsTemplatePO = smsTemplateService.selectByKey(customerSmsMessagePO.getSmsTemplateId());
        SmsPlatformChannelDTO smsPlatformChannel = smsPlatformChannelService.getSmsPlatformChannel(customerSmsMessagePO.getSmsPlatformChannelId(), smsTemplatePO.getNewSmsPlatform());
        if (Objects.nonNull(smsPlatformChannel)) {
            smsTestStatPO.setSmsPlatformChannelId(smsPlatformChannel.getSmsPlatformChannelId());
            if (SendMessageStatusEnum.SEND_SUCCESSFUL.equals(customerSmsMessagePO.getSendStatus())) {
                if (Objects.equals(messageBO.getReportStatus(), SUCCESS)) {
                    smsTestStatPO.setSuccessSend(0L);
                    smsTestStatPO.setSuccessReceive(1L);
                    smsTestStatPO.setFailReceive(0L);
                    smsTestStatPO.setTotalCost(smsPlatformChannel.getSmsProductPrice());
                } else if (Objects.equals(messageBO.getReportStatus(), FAIL)) {
                    smsTestStatPO.setSuccessSend(0L);
                    smsTestStatPO.setSuccessReceive(0L);
                    smsTestStatPO.setFailReceive(1L);
                    smsTestStatPO.setTotalCost(0);
                }
                updatePO.setReportStatus(messageBO.getReportStatus());
                smsTestStatService.editSmsTestData(smsTestStatPO);
                customerSmsMessageService.updateNotNull(updatePO);
            }
        }
    }

    private void updateIntentMessageForRefund(SmsCallBackMessageBO messageBO, IntentMessagePO intentMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        if (intentMessagePO != null) {
            logger.info("updateIntentMessageForRefund smsStatus = {}", messageBO.toString());
            if (Objects.equals(messageBO.getReportStatus(), FAIL)) {
                //去退费
                if(channelWithTenantPriceDTO != null && !SmsTypeEnum.SMART_SMS.equals(channelWithTenantPriceDTO.getSmsType())) {
                    refundAndUpdateMongoDBForIntent(intentMessagePO, channelWithTenantPriceDTO, false);
                    smsCostBillingDetailForIntentService.reFound(intentMessagePO, messageBO, channelWithTenantPriceDTO);
                }
                // 任务的意向短信需要统计
                Long tenantId = intentMessagePO.getTenantId();
                Long robotCallJobId = intentMessagePO.getRobotCallJobId();
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_FAILURE, 1, intentMessagePO.getCreateTime());
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, -1, intentMessagePO.getCreateTime());
//                String preIntentMessageStatusCount = RedisKeyCenter.getIntentMessageStatusCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_SUCCESS);
//                redisOpsService.incrementKey(preIntentMessageStatusCount, -1);
//                String intentMessageStatusCount = RedisKeyCenter.getIntentMessageStatusCount(intentMessagePO.getTenantId(), intentMessagePO.getRobotCallJobId(), SendMessageStatusEnum.RECV_FAILURE);
//                redisOpsService.incrementKey(intentMessageStatusCount, 1);
            } else if (SmsPlatformEnum.YIWISE_SMS_GATEWAY.equals(messageBO.getSmsPlatform())) {
                // 自研短信网关，如果ope成本价设置为0，成本价从网关获取；
                // 如果ope成本价设置为>0，成本价依然从ope设置的值计算
                Long tenantId = intentMessagePO.getTenantId();
                Long robotCallJobId = intentMessagePO.getRobotCallJobId();
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_SUCCESS, 1, intentMessagePO.getCreateTime());
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, -1, intentMessagePO.getCreateTime());
                if (Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() == 0L) {
                        chengbenUpdateMongoDBForIntent(intentMessagePO, messageBO, channelWithTenantPriceDTO);
                }
                logger.info("去更新短信记录基础和扣费信息");
                if (Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() > 0L){
                    smsCostBillingDetailService.saveSmsCostBillingDetail(messageBO);
                }
                redisOpsService.delete(RedisKeyCenter.getCallRecordStatsQueryForSmsReturn(intentMessagePO.getSid()));
	            if (FINANCIAL_GRAY_TEST_ENABLED && !FINANCIAL_GRAY_TEST_TENANTS.contains(intentMessagePO.getTenantId())) {
		            logger.debug("财务管理开启了灰度测试, 不更新sms_financial");
	            } else {
		            smsFinancialService.updateChannel(SmsBusinessTypeEnum.INTENT, intentMessagePO.getIntentMessageId(),
				            messageBO.getChannelNo(), messageBO.getPrice(), messageBO.getCount());
	            }
            } else {
                Long tenantId = intentMessagePO.getTenantId();
                Long robotCallJobId = intentMessagePO.getRobotCallJobId();
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_SUCCESS, 1, intentMessagePO.getCreateTime());
                intentMessageStatsService.updateIntentMessageCount(tenantId, robotCallJobId, SendMessageStatusEnum.RECV_ING, -1, intentMessagePO.getCreateTime());
                redisOpsService.delete(RedisKeyCenter.getCallRecordStatsQueryForSmsReturn(intentMessagePO.getSid()));
            }

            IntentMessagePO updatePO = new IntentMessagePO();
            updatePO.setIntentMessageId(intentMessagePO.getIntentMessageId());
            if (StringUtils.isNotBlank(messageBO.getUserReceiveTime())) {
                updatePO.setUserReceiveTime(LocalDateTime.parse(messageBO.getUserReceiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            updatePO.setReportErrorDetail(messageBO.getErrorMsg());
            updatePO.setReportStatus(messageBO.getReportStatus());
            intentMessageService.updateNotNull(updatePO);
        }
    }

    private void updateSmsJobMessageForRefund(SmsCallBackMessageBO messageBO, SmsJobMessagePO smsJobMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        if (smsJobMessagePO != null) {
            logger.debug("updateSmsJobMessageForRefund smsStatus = {}, channelWithTenantPriceDTO = {}", messageBO, channelWithTenantPriceDTO);
            if (Objects.equals(FAIL, messageBO.getReportStatus())) {
                if(channelWithTenantPriceDTO != null && !SmsTypeEnum.SMART_SMS.equals(channelWithTenantPriceDTO.getSmsType())) {
                    //去退费
                    refundAndUpdateMongoDBForJobMessage(smsJobMessagePO, channelWithTenantPriceDTO);
                    // 更新短信详情退费信息
                    smsCostBillingDetailForJobService.reFound(smsJobMessagePO, messageBO, channelWithTenantPriceDTO);
                }
            } else if (SmsPlatformEnum.YIWISE_SMS_GATEWAY.equals(messageBO.getSmsPlatform())) {
                // 自研短信网关，如果ope成本价设置为0，成本价从网关获取；
                // 如果ope成本价设置为>0，成本价依然从ope设置的值计算
                if (Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() == 0L) {
                    chengbenUpdateMongoDBForJobMessage(smsJobMessagePO, messageBO, channelWithTenantPriceDTO);
                }
                logger.info("去更新短信记录基础和扣费信息");
                if (Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() > 0L) {
                    smsCostBillingDetailService.saveSmsCostBillingDetail(messageBO);
                }
	            if (FINANCIAL_GRAY_TEST_ENABLED && !FINANCIAL_GRAY_TEST_TENANTS.contains(smsJobMessagePO.getTenantId())) {
		            logger.debug("财务管理开启了灰度测试, 不更新sms_financial");
	            } else {
		            smsFinancialService.updateChannel(SmsBusinessTypeEnum.SMSJOB, smsJobMessagePO.getSmsJobMessageId(),
				            messageBO.getChannelNo(), messageBO.getPrice(), messageBO.getCount());
	            }
            }

            SmsJobMessagePO updatePO = new SmsJobMessagePO();
            updatePO.setSmsJobMessageId(smsJobMessagePO.getSmsJobMessageId());
            if (StringUtils.isNotBlank(messageBO.getUserReceiveTime())) {
                updatePO.setUserReceiveTime(LocalDateTime.parse(messageBO.getUserReceiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            updatePO.setReportErrorDetail(messageBO.getErrorMsg());
            updatePO.setReportStatus(messageBO.getReportStatus());
            smsJobMessageService.updateNotNull(updatePO);
            // 接收状态统计
            smsJobService.incrementJobStatusAfterRecv(smsJobMessagePO.getTenantId(), smsJobMessagePO.getSmsJobId(), Objects.equals("FAIL", messageBO.getReportStatus()) ? SendMessageStatusEnum.RECV_FAILURE : SendMessageStatusEnum.RECV_SUCCESS, 1L);
        }
    }

    private void updateCustomerSmsMessageForRefund(SmsCallBackMessageBO messageBO, CustomerSmsMessagePO customerSmsMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        if (customerSmsMessagePO != null) {
            logger.info("updateCustomerSmsMessageForRefund smsStatus = {}", messageBO.toString());
            if (Objects.equals(FAIL, messageBO.getReportStatus())) {
                if(channelWithTenantPriceDTO != null && !SmsTypeEnum.SMART_SMS.equals(channelWithTenantPriceDTO.getSmsType())) {
                    //去退费
                    refundAndUpdateMongoDBForCustomerMessage(customerSmsMessagePO, channelWithTenantPriceDTO);
                    smsCostBillingDetailForCustomerService.reFound(customerSmsMessagePO, messageBO, channelWithTenantPriceDTO);
                }
            } else if (SmsPlatformEnum.YIWISE_SMS_GATEWAY.equals(messageBO.getSmsPlatform())) {
                // 自研短信网关，如果ope成本价设置为0，成本价从网关获取；
                // 如果ope成本价设置为>0，成本价依然从ope设置的值计算
                if(Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() == 0L) {
                    chengbenUpdateMongoDBForCustomerMessage(customerSmsMessagePO, messageBO, channelWithTenantPriceDTO);
                }
                logger.info("去更新短信记录基础和扣费信息");
                if (Objects.nonNull(channelWithTenantPriceDTO) && channelWithTenantPriceDTO.getSmsProductPrice() > 0L){
                    smsCostBillingDetailService.saveSmsCostBillingDetail(messageBO);
                }
            }

            CustomerSmsMessagePO updatePO = new CustomerSmsMessagePO();
            updatePO.setCustomerSmsMessageId(customerSmsMessagePO.getCustomerSmsMessageId());
            updatePO.setReportErrorDetail(messageBO.getErrorMsg());
            if (StringUtils.isNotBlank(messageBO.getUserReceiveTime())) {
                updatePO.setUserReceiveTime(LocalDateTime.parse(messageBO.getUserReceiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            updatePO.setReportStatus(messageBO.getReportStatus());
            customerSmsMessageService.updateNotNull(updatePO);
        }
    }

	/**
	 * @param reStats 重新统计时不修改账户余额,不保存退费信息
	 */
    private void refundAndUpdateMongoDBForIntent(IntentMessagePO intentMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO, boolean reStats) {
        logger.info("短信退费:intent_message_id为为{}的短信", intentMessagePO.getIntentMessageId());
        if (Objects.isNull(channelWithTenantPriceDTO)) {
            logger.error("refundAndUpdateMongoDBForIntent channelWithTenantPriceDTO is null");
            return;
        }
        Long tenantId = intentMessagePO.getTenantId();
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行退费统计", tenantId);
            return;
        }
        Integer costCount = intentMessagePO.getCostCount().intValue();
        Long smsPrice = channelWithTenantPriceDTO.getRealSmsPrice();
        Long smsProductPrice = channelWithTenantPriceDTO.getSmsProductPrice();
        Long cost = costCount * smsPrice;
        Long chengben = costCount * smsProductPrice;
		// 财务管理的免费短信成本
		Long freeCost = 0L;
        if (costCount == null || costCount <= 0) {
        	logger.warn("退费数据异常costCount={}", costCount);
        	return;
        }
        if (!reStats) {
            if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
                //新版计费逻辑
                CostResponseBO costResponseBO = costClient.refund(RefundRequestBO.builder()
                        .sourceModule(SourceModuleEnum.INTENT_MESSAGE_REFUND)
                        .extendId(intentMessagePO.getSid())
                        .tenantId(tenantId)
                        .tradeType(TradeTypeEnum.REFUND)
                        .preSourceModule(SourceModuleEnum.INTENT_MESSAGE)
                        .preExtendId(intentMessagePO.getSid())
                        .build());
                cost = costResponseBO.getCost();
                costCount = costResponseBO.getCostCount();
	            freeCost = costResponseBO.getFreeCost() > 0 ? tenantPO.getSmsComFare() : 0;
            } else {
                if (Boolean.TRUE.equals(intentMessagePO.getIsFree())) {
                    cost = smsPrice * (costCount - 1);
                    costCount--;
	                freeCost = tenantPO.getSmsComFare();
                }
                // 更新余额
                if (BooleanUtils.isNotTrue(intentMessagePO.getIsFree())) {
                    if (TenantPayTypeEnum.PIECE.equals(tenantPO.getTenantPayType())) {
                        tenantService.increaseTenantAllAccount(tenantId, cost, true);
                    } else {
                        tenantService.increaseSmsCost(cost, tenantId);
                    }
                } else if (costCount >= 1) {
                    // 有免费额度的都是按接通付费客户
                    tenantService.increaseTenantAllAccount(tenantId, cost, true);
                } else {
                    logger.info("intentMessageId={},免费短信退费不返回余额", intentMessagePO.getIsFree());
                }
                // 保存退费信息
                saveSmsRefund(intentMessagePO.getTenantId(), SmsModuleEnum.INTENT_MESSAGE, cost, costCount.intValue(), intentMessagePO.getIntentMessageId());
            }
	        if (FINANCIAL_GRAY_TEST_ENABLED && !FINANCIAL_GRAY_TEST_TENANTS.contains(intentMessagePO.getTenantId())) {
		        logger.debug("财务管理开启了灰度测试, 不更新sms_financial");
	        } else {
		        if (tenantPO.getDistributorId() != null && tenantPO.getDistributorId() > 0 && !TenantPayTypeEnum.isSubscribe(tenantPO.getTenantPayType())) {
			        DistributorPO distributor = distributorService.selectByKey(tenantPO.getDistributorId());
			        if (DistributorTypeEnum.ECOLOGY.equals(distributor.getType())) {
				        cost = tenantPO.getDistributorSmsComFare() * costCount;
			        }
		        }
		        smsFinancialService.updateRefund(SmsBusinessTypeEnum.INTENT, intentMessagePO.getIntentMessageId());
	        }
        }
	    // 更新统计
	    SmsTypeEnum smsType = channelWithTenantPriceDTO.getSmsType();
        if (smsType == null) {
        	return;
        }
        Query query;
        DialStatusEnum resultStatus;
        String callRecordStatsQueryForSmsReturn = "";
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            callRecordStatsQueryForSmsReturn = RedisKeyCenter.getCallRecordStatsQueryForSmsReturn(intentMessagePO.getSid());
            BillingCalloutSmsContext smsContext = redisOpsService.get(callRecordStatsQueryForSmsReturn, BillingCalloutSmsContext.class);
            if (Objects.isNull(smsContext)) {
                logger.warn("[LogHub_Warn]refundAndUpdateMongoDBForIntent smsContext is null");
                return;
            }
            query = callStatsService.getCallStatsQuery(smsContext.getCalloutContext());
            resultStatus = smsContext.getResultStatus();
        } else {
            CallRecordPO callRecord = callRecordInfoService.selectByKey(intentMessagePO.getCallRecordId());
            if (callRecord == null) {
                logger.info("callRecordId={}, 对应的callRecord不存在, 不进行退费统计", intentMessagePO.getCallRecordId());
                return;
            }
            resultStatus = callRecord.getResultStatus();
            RobotCallJobPO robotCallJob = robotCallJobService.selectByKeyOrThrow(callRecord.getRobotCallJobId());
            query = callStatsService.getCallStatsQuery(tenantPO, robotCallJob, callRecord);
        }
	    Update update = new Update();
	    update.inc("smsRefundChengben", chengben);
	    if (Boolean.TRUE.equals(intentMessagePO.getIsFree())) {
		    update.inc("freeSmsRefund", smsPrice);
		    update.inc("freeSmsRefundCount", 1);
	    }
	    update.inc("refund", cost);
        if (costCount > 0) {
	        update.inc("smsRefundCount", 1);
	    }
	    update.inc("smsRefundBillCount", costCount);
	    if (TenantPayTypeEnum.PIECE.equals(tenantPO.getTenantPayType())) {
            // 按接通付费
		    if (DialStatusEnum.isAnswered(resultStatus)) {
		        // 接通
			    switch (smsType) {
                    case VIRTUAL: {
                        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
                            update.inc("answeredVirtualSmsRefund", cost);
                            update.inc("answeredVirtualSmsRefundCount", costCount);
                        } else {
                            update.inc("answeredSmsRefund", cost);
                            update.inc("answeredSmsRefundCount", costCount);
                        }
                        break;
                    }
                    case SMS: {
					    update.inc("answeredSmsRefund", cost);
					    update.inc("answeredSmsRefundCount", costCount);
					    break;
				    }
				    case MMS: {
					    update.inc("answeredMmsRefund", cost);
					    update.inc("answeredMmsRefundCount", costCount);
					    break;
				    }
				    case FMS: {
					    update.inc("answeredFmsRefund", cost);
					    update.inc("answeredFmsRefundCount", costCount);
					    break;
				    }
			    }
		    } else {
		        // 未接通
			    switch (smsType) {
                    case VIRTUAL: {
                        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
                            update.inc("notAnsweredVirtualSmsRefund", cost);
                            update.inc("notAnsweredVirtualSmsRefundCount", costCount);
                        } else {
                            update.inc("notAnsweredSmsRefund", cost);
                            update.inc("notAnsweredSmsRefundCount", costCount);
                        }
                        break;
                    }
                    case SMS: {
					    update.inc("notAnsweredSmsRefund", cost);
					    update.inc("notAnsweredSmsRefundCount", costCount);
					    break;
				    }
				    case MMS: {
					    update.inc("notAnsweredMmsRefund", cost);
					    update.inc("notAnsweredMmsRefundCount", costCount);
					    break;
				    }
				    case FMS: {
					    update.inc("notAnsweredFmsRefund", cost);
					    update.inc("notAnsweredFmsRefundCount", costCount);
					    break;
				    }
			    }
		    }
        } else {
            // 订阅制或按分钟付费
		    switch (smsType) {
                case VIRTUAL: {
                    if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
                        update.inc("virtualSmsRefund", cost);
                        update.inc("virtualSmsRefundCount", costCount);
                    } else {
                        update.inc("smsRefund", cost);
                        update.inc("smsRefundCount2", costCount);
                    }
                    break;
                }
                case SMS: {
				    update.inc("smsRefund", cost);
				    update.inc("smsRefundCount2", costCount);
				    break;
			    }
			    case MMS: {
				    update.inc("mmsRefund", cost);
				    update.inc("mmsRefundCount", costCount);
				    break;
			    }
			    case FMS: {
				    update.inc("fmsRefund", cost);
				    update.inc("fmsRefundCount", costCount);
				    break;
			    }
		    }
        }
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.CALL_STATS_ROBOT_DONE_JOB, query, update);
        if (StringUtils.isNotBlank(callRecordStatsQueryForSmsReturn)) {
            redisOpsService.delete(callRecordStatsQueryForSmsReturn);
        }
        logger.info("意向短信退费结束");
    }

	@Override
	public void updateStatsBySmsRefunds(Long tenantId, List<IntentMessagePO> intentMessages) {
	    for (IntentMessagePO intentMessagePO : intentMessages) {
		    SmsTemplateInfoBO smsTemplate = smsTemplateService.getSmsTemplateInfo(tenantId, intentMessagePO.getSmsTemplateId());
            SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO = smsPlatformChannelService.getSmsPlatformChannelWithTenantPrice(smsTemplate.getTenantId(), smsTemplate.getSmsPlatformChannelId(), smsTemplate.getNewSmsPlatform());
            refundAndUpdateMongoDBForIntent(intentMessagePO, channelWithTenantPriceDTO, true);
	    }
	}

    private void chengbenUpdateMongoDBForIntent(IntentMessagePO intentMessagePO, SmsCallBackMessageBO messageBO,SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        Long chengben = messageBO.getCost();
        logger.info("短信成本:intent_message_id={}的短信，成本={}", intentMessagePO.getIntentMessageId(), chengben);
        Long tenantId = intentMessagePO.getTenantId();
        if(tenantId == null) {
            logger.info("tenantId不存在，不进行成本统计");
            return;
        }
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行成本统计", tenantId);
            return;
        }
        Query query;
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            // 更新统计
            String callRecordStatsQueryForSmsReturn = RedisKeyCenter.getCallRecordStatsQueryForSmsReturn(intentMessagePO.getSid());
            BillingCalloutSmsContext smsContext = redisOpsService.get(callRecordStatsQueryForSmsReturn, BillingCalloutSmsContext.class);
            if (Objects.isNull(smsContext)) {
                logger.warn("[LogHub_Warn]refundAndUpdateMongoDBForIntent smsContext is null");
                return;
            }
            query = callStatsService.getCallStatsQuery(smsContext.getCalloutContext());
        } else {
            CallRecordPO callRecord = callRecordInfoService.selectByKey(intentMessagePO.getCallRecordId());
            if (callRecord == null) {
                logger.info("callRecordId={}, 对应的callRecord不存在, 不进行退费统计", intentMessagePO.getCallRecordId());
                return;
            }
            RobotCallJobPO robotCallJob = robotCallJobService.selectByKeyOrThrow(callRecord.getRobotCallJobId());
            query = callStatsService.getCallStatsQuery(tenantPO, robotCallJob, callRecord);
        }
        smsCostBillingDetailContext.addCostBillingDetailForIntentMessage(messageBO, channelWithTenantPriceDTO,intentMessagePO);
        Update update = new Update();
        update.inc("smsChengben", chengben);
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.CALL_STATS_ROBOT_DONE_JOB, query, update);
    }

    private void refundAndUpdateMongoDBForJobMessage(SmsJobMessagePO smsJobMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        logger.info("短信退费:sms_job_id为{}的短信", smsJobMessagePO);
        Long tenantId = smsJobMessagePO.getTenantId();
        if(tenantId == null) {
            logger.info("tenantId不存在，不进行成本统计");
            return;
        }
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行成本统计", tenantId);
            return;
        }
        if (Objects.isNull(channelWithTenantPriceDTO)) {
            logger.error("refundAndUpdateMongoDBForJobMessage channelWithTenantPriceDTO is null");
            return;
        }
        Long costCount = smsJobMessagePO.getCostCount();
        Long cost = costCount * channelWithTenantPriceDTO.getRealSmsPrice();
        Long chengben = costCount * channelWithTenantPriceDTO.getSmsProductPrice();
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            //新版计费逻辑
            CostResponseBO costResponseBO = costClient.refund(RefundRequestBO.builder()
                    .sourceModule(SourceModuleEnum.SMS_JOB_MESSAGE_REFUND)
                    .extendId(smsJobMessagePO.getSid())
                    .tenantId(tenantId)
                    .tradeType(TradeTypeEnum.REFUND)
                    .preSourceModule(SourceModuleEnum.SMS_JOB_MESSAGE)
                    .preExtendId(smsJobMessagePO.getSid())
                    .build());
            cost = costResponseBO.getCost();
        } else {
            //老版计费
            tenantService.increaseSmsCost(cost, smsJobMessagePO.getTenantId());
            saveSmsRefund(smsJobMessagePO.getTenantId(), SmsModuleEnum.SMS_JOB_MESSAGE, cost, costCount.intValue(), smsJobMessagePO.getSmsJobMessageId());
        }
	    if (FINANCIAL_GRAY_TEST_ENABLED && !FINANCIAL_GRAY_TEST_TENANTS.contains(smsJobMessagePO.getTenantId())) {
		    logger.debug("财务管理开启了灰度测试, 不更新sms_financial");
	    } else {
		    if (tenantPO.getDistributorId() != null && tenantPO.getDistributorId() > 0 && !TenantPayTypeEnum.isSubscribe(tenantPO.getTenantPayType())) {
			    DistributorPO distributor = distributorService.selectByKey(tenantPO.getDistributorId());
			    if (DistributorTypeEnum.ECOLOGY.equals(distributor.getType())) {
				    cost = tenantPO.getDistributorSmsComFare() * costCount;
			    }
		    }
		    smsFinancialService.updateRefund(SmsBusinessTypeEnum.SMSJOB, smsJobMessagePO.getSmsJobMessageId());
	    }

        // 更新统计
	    SmsTypeEnum smsType = channelWithTenantPriceDTO.getSmsType();
	    if (smsType == null) {
		    return;
	    }

        LocalDateTime statsTime = Objects.nonNull(smsJobMessagePO.getSendTime()) ? smsJobMessagePO.getSendTime() : smsJobMessagePO.getCreateTime();
        Query query = getSmsStatusQuery(smsJobMessagePO.getTenantId(), tenantPO.getDistributorId(), tenantPO.getTenantPayType(), null, null,
			    smsJobMessagePO.getCreateUserId(), statsTime, null, smsJobMessagePO.getSmsJobId(), "smsJob");
	    Update update = new Update();
	    update.inc("refund", cost);
	    update.inc("smsRefundCount", 1);
	    update.inc("smsRefundBillCount", costCount);
	    update.inc("smsRefundChengben", chengben);
	    switch (smsType) {
            case VIRTUAL:
            case SMS: {
			    update.inc("smsRefund", cost);
			    update.inc("smsRefundCount2", costCount);
			    break;
		    }
		    case MMS: {
			    update.inc("mmsRefund", cost);
			    update.inc("mmsRefundCount", costCount);
			    break;
		    }
		    case FMS: {
			    update.inc("fmsRefund", cost);
			    update.inc("fmsRefundCount", costCount);
			    break;
		    }
	    }
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.SMS_JOB_DATE_STATS, query, update);
        logger.info("短信任务退费结束");
    }

    private void chengbenUpdateMongoDBForJobMessage(SmsJobMessagePO smsJobMessagePO, SmsCallBackMessageBO messageBO,SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        long chengben = messageBO.getCost();
        logger.info("短信成本:sms_job_id={}的短信，成本={}", smsJobMessagePO.getSmsJobId(), chengben);
        Long tenantId = smsJobMessagePO.getTenantId();
        if(tenantId == null) {
            logger.info("tenantId不存在，不进行成本统计");
            return;
        }
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行成本统计", tenantId);
            return;
        }
        smsCostBillingDetailContext.addCostBillingDetailForJobMessage(messageBO,channelWithTenantPriceDTO,smsJobMessagePO);
        LocalDateTime statsTime = Objects.nonNull(smsJobMessagePO.getSendTime()) ? smsJobMessagePO.getSendTime() : smsJobMessagePO.getCreateTime();
        Query query = getSmsStatusQuery(smsJobMessagePO.getTenantId(), tenantPO.getDistributorId(), tenantPO.getTenantPayType(), null, null,
                smsJobMessagePO.getCreateUserId(), statsTime, null, smsJobMessagePO.getSmsJobId(), "smsJob");
        Update update = new Update();
        update.inc("smsChengben", chengben);
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.SMS_JOB_DATE_STATS, query, update);
    }

    private void refundAndUpdateMongoDBForCustomerMessage(CustomerSmsMessagePO customerSmsMessagePO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
	    logger.info("短信退费:customer_sms_message_id为{}的短信", customerSmsMessagePO.getCustomerSmsMessageId());
        Long tenantId = customerSmsMessagePO.getTenantId();
        if(tenantId == null) {
            logger.info("tenantId不存在，不进行成本统计");
            return;
        }
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行成本统计", tenantId);
            return;
        }
        if (Objects.isNull(channelWithTenantPriceDTO)) {
            logger.error("refundAndUpdateMongoDBForCustomerMessage channelWithTenantPriceDTO is null");
            return;
        }
        Integer costCount = customerSmsMessagePO.getCostCount().intValue();
        Long cost = costCount * channelWithTenantPriceDTO.getRealSmsPrice();
        Long chengben = costCount * channelWithTenantPriceDTO.getSmsProductPrice();
        if (BooleanUtils.isTrue(tenantPO.getUsingNewBillingService())) {
            //新版计费逻辑
            CostResponseBO costResponseBO = costClient.refund(RefundRequestBO.builder()
                    .sourceModule(SourceModuleEnum.MA_INTENT_MESSAGE_REFUND)
                    .extendId(customerSmsMessagePO.getSid())
                    .tenantId(tenantId)
                    .tradeType(TradeTypeEnum.REFUND)
                    .preSourceModule(SourceModuleEnum.MA_INTENT_MESSAGE)
                    .preExtendId(customerSmsMessagePO.getSid())
                    .build());
            cost = costResponseBO.getCost();
            costCount = costResponseBO.getCostCount();
            try {
                //回调MA
                MaSmsBillingCallbackRequest billingCallbackRequest = new MaSmsBillingCallbackRequest();
                billingCallbackRequest.setMessageId(customerSmsMessagePO.getSid());
                billingCallbackRequest.setRefundNum(costResponseBO.getCostCount());
                billingCallbackRequest.setRefundAmount(cost.intValue());
                smsCallbackClient.sendBillingCallback(billingCallbackRequest);
            } catch (Exception e) {
                logger.error("smsCallBackHandle smsCallbackClient sendCallback error", e);
            }
        } else {
            // 老版计费
            tenantService.increaseSmsCost(cost, customerSmsMessagePO.getTenantId());
            saveSmsRefund(customerSmsMessagePO.getTenantId(), SmsModuleEnum.CUSTOMER_SMS_MESSAGE, cost, costCount.intValue(), customerSmsMessagePO.getCustomerSmsMessageId());
        }
	    // 更新统计
	    SmsTypeEnum smsType = channelWithTenantPriceDTO.getSmsType();
	    if (smsType == null) {
		    return;
	    }
	    Query query = getSmsStatusQuery(customerSmsMessagePO.getTenantId(), tenantPO.getDistributorId(), tenantPO.getTenantPayType(), null, null,
			    customerSmsMessagePO.getCreateUserId(), customerSmsMessagePO.getCreateTime(), null, null, "customer");
	    Update update = new Update();
	    update.inc("refund", cost);
	    update.inc("smsRefundCount", 1);
	    update.inc("smsRefundBillCount", costCount);
	    update.inc("smsRefundChengben", chengben);
	    switch (smsType) {
            case VIRTUAL:
            case SMS: {
			    update.inc("smsRefund", cost);
			    update.inc("smsRefundCount2", costCount);
			    break;
		    }
		    case MMS: {
			    update.inc("mmsRefund", cost);
			    update.inc("mmsRefundCount", costCount);
			    break;
		    }
		    case FMS: {
			    update.inc("fmsRefund", cost);
			    update.inc("fmsRefundCount", costCount);
			    break;
		    }
	    }
        callStatsMongoService.updateMongoData(MongoCollectionNameCenter.CUSTOMER_SMS_DATE_STATS, query, update);
    }

    private void chengbenUpdateMongoDBForCustomerMessage(CustomerSmsMessagePO customerSmsMessagePO, SmsCallBackMessageBO messageBO, SmsPlatformChannelWithTenantPriceDTO channelWithTenantPriceDTO) {
        logger.info("保存短信计费信息：{},{},{}", customerSmsMessagePO, messageBO, channelWithTenantPriceDTO);
        Long chengben = messageBO.getCost();
        logger.info("短信成本:customer_sms_message_id={}的短信，成本={}", customerSmsMessagePO.getCustomerSmsMessageId(), chengben);
        Long tenantId = customerSmsMessagePO.getTenantId();
        if(tenantId == null) {
            logger.info("tenantId不存在，不进行成本统计");
            return;
        }
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO == null) {
            logger.info("tenantId={}，对应的tenantPO不存在，不进行成本统计", tenantId);
            return;
        }
        smsCostBillingDetailContext.addCostBillingDetailForCustomerSmsMessage(messageBO, channelWithTenantPriceDTO, customerSmsMessagePO);
        Query query = getSmsStatusQuery(tenantId, tenantPO.getDistributorId(), tenantPO.getTenantPayType(), null, null,
                customerSmsMessagePO.getCreateUserId(), customerSmsMessagePO.getCreateTime(), null, null, "customer");
        Update update = new Update();
        update.inc("smsChengben", chengben);
        callStatsMongoService.updateMongoDataUsingCache(MongoCollectionNameCenter.CUSTOMER_SMS_DATE_STATS, query, update);
    }

    private Query getSmsStatusQuery(Long tenantId, Long distributorId, TenantPayTypeEnum tenantPayType, Long phoneNumberId, Long aiFare,
                                    Long userId, LocalDateTime localDateTime, Long robotCallJobId, Long smsJobId, String type) {
        int day = localDateTime.getDayOfMonth();
        int month = localDateTime.getMonthValue();
        int year = localDateTime.getYear();
        Query query = new Query();
        query.addCriteria(Criteria.where("tenantId").is(tenantId));
	    query.addCriteria(Criteria.where("distributorId").is(distributorId));
        query.addCriteria(Criteria.where("tenantPayType").is(tenantPayType));
        if (aiFare != null) {
	        query.addCriteria(Criteria.where("aiFare").is(aiFare));
        }
        if (phoneNumberId != null) {
	        query.addCriteria(Criteria.where("phoneNumberId").is(phoneNumberId));
        }
        if (Objects.nonNull(userId) && !Objects.equals(0L, userId)) {
            query.addCriteria(Criteria.where("userId").is(userId));
        }
        query.addCriteria(Criteria.where("year").is(year));
        query.addCriteria(Criteria.where("month").is(month));
        query.addCriteria(Criteria.where("day").is(day));
        if (Objects.nonNull(smsJobId)) {
            query.addCriteria(Criteria.where("smsJobId").is(smsJobId));
        }
        if (Objects.nonNull(robotCallJobId)) {
            query.addCriteria(Criteria.where("callStatsId").is(robotCallJobId));
        }
	    switch (type) {
		    case "smsJob":
			    query.addCriteria(Criteria.where("date").is(LocalDateTime.of(year, month, day, 0, 0)));
			    break;
		    case "customer":
			    query.addCriteria(Criteria.where("localDateTime").is(LocalDateTime.of(year, month, day, 0, 0)));
			    break;
	    }
        return query;
    }

	private void saveSmsRefund(Long tenantId, SmsModuleEnum smsModule, Long refund, Integer costCount, Long refId) {
		SmsRefundPO smsRefund = new SmsRefundPO();
		smsRefund.setTenantId(tenantId);
		smsRefund.setSmsModule(smsModule);
		smsRefund.setRefund(refund);
		smsRefund.setCostCount(costCount);
		smsRefund.setRefId(refId);
		smsRefundService.saveNotNull(smsRefund);
	}
}
