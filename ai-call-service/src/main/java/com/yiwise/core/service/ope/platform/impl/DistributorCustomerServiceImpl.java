package com.yiwise.core.service.ope.platform.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.common.utils.string.MyStringUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.enums.CodeDescEnum;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.base.service.cache.api.CacheTplService;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.bill.statistic.model.enums.StatisticCompanyTypeEnum;
import com.yiwise.bill.statistic.model.enums.StatisticTenantPayTypeEnum;
import com.yiwise.bill.statistic.model.vo.BillStatisticTenantVO;
import com.yiwise.bill.statistic.service.BillStatisticSyncMetaInfoService;
import com.yiwise.calloutjob.api.vo.TenantCallJobConfigVO;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.*;
import com.yiwise.core.dal.dao.RobotPOMapper;
import com.yiwise.core.dal.dao.TenantPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.mongo.DistributorConfigModelMongoPO;
import com.yiwise.core.dal.mongo.tenant.TenantCalloutJobConfigMongoPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.feignclient.callout.CallOutJobClient;
import com.yiwise.core.feignclient.rcs.CrowdClient;
import com.yiwise.core.feignclient.rcs.CustomerBlackGroupRemoteClient;
import com.yiwise.core.lock.RedisLock;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.distributorcustomer.DistributorCustomerFunctionCostBO;
import com.yiwise.core.model.dto.esign.IdentityInfoDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.enums.ope.AuditStatusEnum;
import com.yiwise.core.model.enums.rechargstream.RechargeMethodEnum;
import com.yiwise.core.model.vo.TenantEncryptConfigureRequestVO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DistributorCustomerQueryVO;
import com.yiwise.core.model.vo.costlist.CostListAccountFareListVO;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerFunctionInfo;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.phonenumber.BindOnePhoneNumberVO;
import com.yiwise.core.model.vo.tenant.TenantIdAndNamePairVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.authentication.AuthenticationService;
import com.yiwise.core.service.engine.*;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.qc.QcCostService;
import com.yiwise.core.service.header.HeaderService;
import com.yiwise.core.service.mongo.impl.MongoOperationService;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.platform.*;
import com.yiwise.core.service.platform.impl.PasswordUtils;
import com.yiwise.core.service.reception.ReceptionConfigService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.redis.RedisLockKeyHelper;
import com.yiwise.customer.data.platform.rpc.api.service.request.crowd.TenantCrowdSnapshotConfigDTO;
import com.yiwise.lcs.api.dto.TenantSmsPriceAddDTO;
import javaslang.control.Try;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.NEW_TENANT_VERSION;
import static com.yiwise.core.config.CommonApplicationConstant.OPE_DISTRIBUTOR_ID;
import static com.yiwise.core.config.CommonApplicationConstant.OPE_TENANT_ID;
import static com.yiwise.core.config.TableUrlConstant.*;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.*;
import static com.yiwise.core.util.SignatureUtils.CRED_ORG_USCC;

/**
 * <AUTHOR>
 * @Date 2018 8 20 13:24
 **/
@Service
public class DistributorCustomerServiceImpl extends BasicServiceImpl<TenantPO> implements DistributorCustomerService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private static final Long qiyuTestDistributorId = 5025L;
    private static final Long qiyuProdDistributorId = 215L;

    @Resource
    private UserService userService;
    @Resource
    private TenantPOMapper tenantPOMapper;
    @Resource
    private RobotPOMapper robotPOMapper;
    @Resource
    private DistributorService distributorService;
    @Resource
    private RobotService robotService;
    @Resource
    private RedisOpsService redisOpsService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private RobotStreamService robotStreamService;
    @Resource
    private RoleService roleService;
    @Resource
    private TenantService tenantService;
    @Resource
    private PurchaseDetailService purchaseDetailService;
    @Resource
    private CacheTplService cacheTplService;
    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private DeploymentUserService deploymentUserService;
    @Resource
    private HeaderService headerService;
    @Resource
    private RechargePrestoreStreamService rechargePrestoreStreamService;
    @Resource
    private DistributorPriceService distributorPriceService;
    @Resource
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    @Qualifier("opeDistributorCustomerExportJob")
    private Job opeDistributorCustomerExportJob;
    @Autowired
    @Qualifier("opeDistributorCustomerByRobotExportJob")
    private Job opeDistributorCustomerByRobotExportJob;
    @Autowired
    @Qualifier("opeDistributorCustomerExportForQiyuJob")
    private Job opeDistributorCustomerExportForQiyuJob;
    @Autowired
    @Qualifier("opeSubDistributorCustomerExportJob")
    private Job opeSubDistributorCustomerExportJob;

    @Resource
    private BatchJobInQueueService batchJobInQueueService;
    @Resource
    private TenantUngivedCsCountService tenantUngivedCsCountService;
    @Resource
    private TenantAiAssistantCountService tenantAiAssistantCountService;
    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;
    @Resource
    private TenantHelpCenterAuthService tenantHelpCenterAuthService;
    @Resource
    private CustomerBlackGroupRemoteClient customerBlackGroupRemoteClient;
    @Resource
    private AuthenticationService authenticationService;
    @Resource
    private QcCostService qcCostService;
    @Resource
    private CostListService costListService;
    @Resource
    private BillStatisticSyncMetaInfoService billStatisticSyncMetaInfoService;
	@Resource
	private MongoOperationService mongoOperationService;
	@Resource
	private CallOutJobClient callOutJobClient;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private TenantPhoneNumberService tenantPhoneNumberService;
    @Resource
    private TenantSmsPriceService tenantSmsPriceService;
    @Resource
    private TenantEncryptConfigureService tenantEncryptConfigureService;

    @Resource
    private CrowdClient crowdClient;

    @Resource
    private ReceptionConfigService receptionConfigService;

    @Override
    public String addDistributorCustomer(CustomerInsertVO customerInsertVO) {
        //校验条件
        valid(customerInsertVO);
        //更新代理商库存
        distributorService.updateRepositoryOfDistributor(customerInsertVO.getStartTime(), customerInsertVO.getEndTime(), customerInsertVO.getDistributorId(), 0, customerInsertVO.getAiConcurrencyLevel());

        DistributorConfigModelMongoPO one = distributorService.getModelConfigMongoPO(customerInsertVO.getDistributorId());

        //新增机器人消费流水/ 预存款账户流水
        addRobotStream(customerInsertVO, one);

        //新增组织 /用户 /创建客户/新增机器人 新加crm端购买记录明细
        TenantPO tenantPO = new TenantPO();
        OrganizationPO organizationPO = new OrganizationPO();
        batchAddPeople(customerInsertVO, tenantPO, organizationPO, one);
        // 根据代理商的线路绑定线路线路&绑定短信通道
        if (Objects.nonNull(one)) {
            Optional<TenantEncryptTypeEnum> encryptType = Optional.ofNullable(one.getEncryptType());
            if (encryptType.isPresent() && encryptType.get().equals(customerInsertVO.getEncryptType()) && encryptType.get().equals(TenantEncryptTypeEnum.CUSTOMER_ENCRYPT)) {
                TenantEncryptConfigureRequestVO requestVO = new TenantEncryptConfigureRequestVO();
                requestVO.setTenantId(tenantPO.getTenantId());
                requestVO.setDecryptUrl(one.getDecryptUrl());
                requestVO.setBatchDecryptUrl(one.getBatchDecryptUrl());
                tenantEncryptConfigureService.updateCustomerConfig(requestVO);
            }
            // 线路
            Optional<List<Long>> phoneNumberIdList = Optional.ofNullable(one.getPhoneNumberIdList());
            if (BooleanUtils.isTrue(one.getRobotEnabled()) && phoneNumberIdList.isPresent()) {
                for (Long l : phoneNumberIdList.get()) {
                    BindOnePhoneNumberVO bindOnePhoneNumberVO = new BindOnePhoneNumberVO();
                    bindOnePhoneNumberVO.setTenantId(tenantPO.getTenantId());
                    bindOnePhoneNumberVO.setPhoneNumberId(l);
                    bindOnePhoneNumberVO.setCallInBillMode(CallInBillModeEnum.MONTHLY);
                    // 0表示无限制
                    bindOnePhoneNumberVO.setConcurrenceLimit(0);
                    bindOnePhoneNumberVO.setUserId(customerInsertVO.getUpdateUserId());
                    bindOnePhoneNumberVO.setLocalBillRate(0D);
                    bindOnePhoneNumberVO.setOtherBillRate(0D);
                    tenantPhoneNumberService.newBindTenantLine(bindOnePhoneNumberVO, customerInsertVO.getDistributorId());
                }
            }
            // 短信
            Optional<List<Long>> smsPlatformChannelIdList = Optional.ofNullable(one.getSmsPlatformChannelIdList());
            if (BooleanUtils.isTrue(one.getSmsPriceEnabled()) && smsPlatformChannelIdList.isPresent() && !CollectionUtils.isEmpty(one.getSmsPriceList())) {
                for (Long l : smsPlatformChannelIdList.get()) {
                    TenantSmsPriceAddDTO addDTO = new TenantSmsPriceAddDTO();
                    addDTO.setTenantId(tenantPO.getTenantId());
                    addDTO.setSmsPlatformChannelId(l);
                    Optional<TenantSmsPriceAddDTO> first = one.getSmsPriceList().stream().filter(e -> e.getSmsPlatformChannelId().equals(l)).findFirst();
                    if (!first.isPresent()) {
                        logger.info("短信通道【{}】未配置单价", l);
                        continue;
                    }
                    first.ifPresent(tenantSmsPriceAddDTO -> addDTO.setTenantSmsPrice(tenantSmsPriceAddDTO.getTenantSmsPrice()));
                    addDTO.setCurrenUserId(customerInsertVO.getUpdateUserId());
                    first.ifPresent(tenantSmsPriceAddDTO -> addDTO.setTenantSmsPrice(tenantSmsPriceAddDTO.getTenantSmsPrice()));
                    tenantSmsPriceService.addTenantSmsPrice(addDTO);
                }
            }
        }
        // /实施客户操作/删除redis缓存
        String password = createPassword(tenantPO, customerInsertVO, organizationPO);
        batchAddOther(tenantPO, customerInsertVO);
        //新增预存款流水
        addPrestoreRechargeStream(tenantPO, customerInsertVO);
        tenantHelpCenterAuthService.initData(tenantPO.getTenantId(), customerInsertVO.getCreateUserId());
        // 创建默认黑名单分组，没有获取到会自动创建
        customerBlackGroupRemoteClient.getDefaultGroup(tenantPO.getTenantId());

	    // 初始化旧版外呼和新版外呼的[导入任务时保留全部变量]字段
	    initSkipPropertiesDeal(tenantPO.getTenantId());

        //同步到计费统计服务
        syncToBillStatisticService(tenantPO);
        return password;
    }

    private void addPrestoreRechargeStream(TenantPO tenantPO, CustomerInsertVO customerInsertVO) {
        //代理商预存款流水
        RechargePrestoreStreamPO rechargePrestoreStreamPO = new RechargePrestoreStreamPO();
        rechargePrestoreStreamPO.setTenantId(tenantPO.getTenantId());
        rechargePrestoreStreamPO.setDistributorId(customerInsertVO.getDistributorId());
        rechargePrestoreStreamPO.setTargetId(tenantPO.getTenantId());
        rechargePrestoreStreamPO.setTargetType(TargetTypeEnum.DISTRIBUTOR);
        rechargePrestoreStreamPO.setTenantName(customerInsertVO.getCompanyName());
        rechargePrestoreStreamPO.setTenantLinkman(customerInsertVO.getLinkman());
        rechargePrestoreStreamPO.setPhoneNumber(customerInsertVO.getPhoneNumber());
        rechargePrestoreStreamPO.setHandleType(RechargePrestoreStreamHandEnum.CONSUME_ADD_DIRECTCUSTOMER);
        rechargePrestoreStreamPO.setFunType(RechargePrestoreStreamFunEnum.FUN_AI);
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(customerInsertVO.getDistributorId());
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到维护的AI坐席价格！");
        }
        rechargePrestoreStreamPO.setFunPrice(distributorPricePO.getRobotPrice());
        rechargePrestoreStreamPO.setFunPriceUnit(distributorPricePO.getRobotUnit());
        rechargePrestoreStreamPO.setFunCount(customerInsertVO.getAiConcurrencyLevel());
        Double fare = 0d;
        if (Objects.nonNull(customerInsertVO.getStartTime()) && Objects.nonNull(customerInsertVO.getEndTime())) {
            rechargePrestoreStreamPO.setFunDays(DistributorCustomerFunctionCostBO.getDay(customerInsertVO.getStartTime(), customerInsertVO.getEndTime(), true));
            fare = distributorCustomerService.calculateAiCost(customerInsertVO.getAiConcurrencyLevel().doubleValue(), customerInsertVO.getDistributorId(), customerInsertVO.getStartTime(), customerInsertVO.getEndTime(), true);
            rechargePrestoreStreamPO.setFare(fare);
        }

        DistributorPO distributorPO = distributorService.selectByKey(customerInsertVO.getDistributorId());
        rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare());
        rechargePrestoreStreamPO.setRemark(customerInsertVO.getComment());
        rechargePrestoreStreamPO.setCreateUserId(customerInsertVO.getCreateUserId());
        rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
        rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(customerInsertVO.getDistributorId(), 0d, fare, 0d);
    }

    private void batchAddOther(TenantPO tenantPO, CustomerInsertVO customerInsertVO) {
        //实施客户操作
        if (customerInsertVO.getDeploymentUserIdList() != null && !customerInsertVO.getDeploymentUserIdList().isEmpty()) {
            deploymentUserService.batchTenantInsert(tenantPO.getTenantId(), customerInsertVO.getDeploymentUserIdList());
        }
        //redis相关操作
        String tenantTodayRobotCountRedisKey = RedisKeyCenter.getTenantTodayRobotCount(tenantPO.getTenantId());
        redisOpsService.delete(tenantTodayRobotCountRedisKey);
    }

    private String createPassword(TenantPO tenantPO, CustomerInsertVO customerInsertVO, OrganizationPO organizationPO) {
        UserPO user = new UserPO();
        user.setTenantId(tenantPO.getTenantId());
        user.setPhoneNumber(tenantPO.getPhoneNumber());

        //同步名字
        user.setName(customerInsertVO.getLinkman());
        user.setNickname(tenantPO.getPhoneNumber());
        //OPE系统新建的经销商的客户的默认用户 distributor_id 必须非空
        user.setDistributorId(customerInsertVO.getDistributorId());
        //内置超级角色，roleId 为 1
        Long roleId = roleService.selectByCharacterAndSystem(RoleTypeEnum.BUILT_IN_SUPERADMIN, SystemEnum.CRM).getRoleId();
        //初始化密码
        String password = PasswordUtils.getPassword(8);
        userService.addUser(user, password, SystemEnum.CRM, organizationPO.getOrganizationId(), roleId);
        //更新组织
        organizationPO.setManagerUserId(user.getUserId());
        organizationService.updateNotNull(organizationPO);

        return password;
    }

    private void batchAddPeople(CustomerInsertVO customerInsertVO, TenantPO tenantPO, OrganizationPO organizationPO, DistributorConfigModelMongoPO one) {
        //这里的distributorId为前端所传
        BeanUtils.copyProperties(customerInsertVO, tenantPO);
        //根据userStatusEnum设置tenant的status
        setCustomerStatus(customerInsertVO, tenantPO);
        //购买AI 坐席送 人工座席
//        tenantPO.setEnableCsSeat(true);
        tenantPO.setEnableCsTransfer(true);
        tenantPO.setEnableCsTransferTel(true);
        // 代理商客户默认关闭
//        tenantPO.setEnableRechargeOnline(false);
        tenantPO.setUseYiwiseAsr(false);

        if (Objects.nonNull(one)) {
            Optional<Boolean> skipPhoneValidation = Optional.ofNullable(one.getSkipPhoneValidation());
            skipPhoneValidation.ifPresent(tenantPO::setSkipPhoneValidation);
            Optional<TenantEncryptTypeEnum> encryptType = Optional.ofNullable(one.getEncryptType());
            if (encryptType.isPresent() && customerInsertVO.getEncryptType().equals(encryptType.get())) {
                encryptType.ifPresent(tenantPO::setEncryptType);
            }
            Optional<Boolean> enableRechargeOnline = Optional.ofNullable(one.getEnableRechargeOnline());
            enableRechargeOnline.ifPresent(tenantPO::setEnableRechargeOnline);
            Optional<List<Long>> smsPlatformChannelIdList = Optional.ofNullable(one.getSmsPlatformChannelIdList());
            // 短信通道id>=10000的是新版，小于10000的是旧版
            if (smsPlatformChannelIdList.isPresent()) {
                Set<Long> newSmsChannelIds = smsPlatformChannelIdList.get().stream().filter(e -> e >= 10000L).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(newSmsChannelIds)) {
                    tenantPO.setSmsChannelShowIdsNew(newSmsChannelIds);
                }
                Set<Long> oldSmsChannelIds = smsPlatformChannelIdList.get().stream().filter(e -> e < 10000L).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(oldSmsChannelIds)) {
                    tenantPO.setSmsChannelShowIds(oldSmsChannelIds);
                }
            }
            Optional<List<TenantSmsPriceAddDTO>> smsPriceList = Optional.ofNullable(one.getSmsPriceList());
            if (BooleanUtils.isTrue(one.getSmsPriceEnabled()) && smsPriceList.isPresent()) {
                tenantPO.setEnableMessagePrice(true);
            }
            Optional<List<Long>> phoneNumberIdList = Optional.ofNullable(one.getPhoneNumberIdList());
            if (BooleanUtils.isTrue(one.getRobotEnabled()) && phoneNumberIdList.isPresent()) {
                tenantPO.setTenantVersion(0);
            }
            Optional<LocalDate> robotStartTime = Optional.ofNullable(one.getRobotStartTime());
            Optional<LocalDate> robotEndTime = Optional.ofNullable(one.getRobotEndTime());
            if (BooleanUtils.isTrue(one.getRobotEnabled()) && robotStartTime.isPresent() && robotEndTime.isPresent()) {
                tenantPO.setEnableOldCallOut(true);
            }
        }
        tenantService.insertTenant(tenantPO);
        RobotPO robot = new RobotPO();
        if (Objects.nonNull(one)) {
            Optional<LocalDate> robotStartTime = Optional.ofNullable(one.getRobotStartTime());
            Optional<LocalDate> robotEndTime = Optional.ofNullable(one.getRobotEndTime());
            if (robotStartTime.isPresent() && robotEndTime.isPresent()) {
                tenantPO.setAiConcurrencyLevel(one.getRobotCount());
                tenantPO.setStartTime(robotStartTime.get());
                tenantPO.setEndTime(robotEndTime.get());
            }
        }
        //新增客户的机器坐席
        if (!TenantPayTypeEnum.SUBSCRIBE.equals(tenantPO.getTenantPayType()) && Objects.nonNull(one) && BooleanUtils.isTrue(one.getRobotEnabled())) {
            if (Objects.nonNull(tenantPO.getAiConcurrencyLevel()) && Objects.nonNull(tenantPO.getStartTime()) && Objects.nonNull(tenantPO.getEndTime())) {
                robot = createRobot(tenantPO);
            }
        }

        //新建一个组织，给新建的客户提供一个默认的组织

        organizationPO.setTenantId(tenantPO.getTenantId());
        organizationPO.setName("默认根组织");
        //默认根组织的parentId为0
        organizationPO.setParentId(ApplicationConstant.PARENT_ID);
        organizationPO.setManagerUserId(ApplicationConstant.ORGANIZATION_MANAGER_ID);
        organizationService.saveNotNull(organizationPO);
        //新加crm端购买记录明细
        createPurchaseDetail(tenantPO, robot);
    }

    private void addRobotStream(CustomerInsertVO customerInsertVO, DistributorConfigModelMongoPO one) {
        if (Objects.nonNull(one)) {
            Optional<LocalDate> robotStartTime = Optional.ofNullable(one.getRobotStartTime());
            Optional<LocalDate> robotEndTime = Optional.ofNullable(one.getRobotEndTime());
            if (robotStartTime.isPresent() && robotEndTime.isPresent()) {
                customerInsertVO.setStartTime(robotStartTime.get());
                customerInsertVO.setEndTime(robotEndTime.get());
                customerInsertVO.setAiConcurrencyLevel(one.getRobotCount());
            }
        }
        //新增机器人流水
        if (Objects.nonNull(customerInsertVO.getStartTime()) && Objects.nonNull(customerInsertVO.getEndTime())) {
            Integer aiCount = (int) customerInsertVO.getStartTime().until(customerInsertVO.getEndTime(), ChronoUnit.DAYS) + 1;
            createRobotStream(customerInsertVO, aiCount);
        }
    }

    private void valid(CustomerInsertVO customerInsertVO) {
        //首先校验客户名字不能重复
        String companyName = customerInsertVO.getCompanyName();
        TenantPO tenantPO = tenantPOMapper.selectByCompanyName(companyName);
        if (Objects.nonNull(tenantPO)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "客户名重复,请重新设置");
        }
        String phoneNumber = customerInsertVO.getPhoneNumber();
        if (!userService.isAllowCreateUser(phoneNumber, SystemEnum.CRM)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "已经存在手机号为" + phoneNumber + "的用户");
        }
        customerInsertVO.setPhoneNumber(phoneNumber);
        LocalDate startTime = customerInsertVO.getStartTime();
        LocalDate endTime = customerInsertVO.getEndTime();
        if ((Objects.nonNull(startTime) && Objects.nonNull(endTime)) && (startTime.isBefore(LocalDate.now()) || endTime.isBefore(LocalDate.now()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "服务日期选择不能早于今天！");
        }
    }

    private RobotPO createRobot(TenantPO tenantPO) {
        RobotPO robot = new RobotPO();
        robot.setTenantId(tenantPO.getTenantId());
        robot.setCount(tenantPO.getAiConcurrencyLevel().intValue());
        robot.setStartTime(tenantPO.getStartTime());
        robot.setEndTime(tenantPO.getEndTime());
        robotService.addRobotNotNull(robot);
        return robot;
    }

    private void createPurchaseDetail(TenantPO tenantPO, RobotPO robot) {
        PurchaseDetailPO purchaseDetailPO = new PurchaseDetailPO();
        purchaseDetailPO.setTenantId(tenantPO.getTenantId());
        purchaseDetailPO.setRobotCount(robot.getCount());
        purchaseDetailPO.setDetailType(RobotPurchaseDetailTypeEnum.BUY);
        purchaseDetailPO.setStartDate(robot.getStartTime());
        purchaseDetailPO.setEndDate(robot.getEndTime());
        purchaseDetailService.saveNotNull(purchaseDetailPO);
    }

    private void createRobotStream(CustomerInsertVO customerInsertVO, Integer aiCount) {
        RobotStreamPO robotStreamPO = new RobotStreamPO();
        robotStreamPO.setType(RobotStreamTypeEnum.CONSUME_ADD_DIRECT_CUSTOMER);
        robotStreamPO.setTenantName(customerInsertVO.getCompanyName());
        robotStreamPO.setDistributorName(distributorService.selectByKey(customerInsertVO.getDistributorId()).getName());
        robotStreamPO.setLinkMan(customerInsertVO.getLinkman());
        robotStreamPO.setPhoneNumber(customerInsertVO.getPhoneNumber());
        robotStreamPO.setAiConcurrency(customerInsertVO.getAiConcurrencyLevel().intValue());
        robotStreamPO.setAiMonth(aiCount);
        robotStreamPO.setAiCount(aiCount * customerInsertVO.getAiConcurrencyLevel().intValue());
        robotStreamPO.setRobotRemaining(distributorService.selectByKey(customerInsertVO.getDistributorId()).getRemainingRobot());
        robotStreamPO.setDistributorId(customerInsertVO.getDistributorId());
        robotStreamService.createRecord(robotStreamPO);
    }

    private void setCustomerStatus(CustomerInsertVO customerInsertVO, TenantPO tenantPO) {
        if (Objects.isNull(customerInsertVO.getStatus())) {
            tenantPO.setStatus(TenantStatusEnum.ENABLED);
        } else if (customerInsertVO.getStatus().getCode() == 1) {
            tenantPO.setStatus(TenantStatusEnum.ENABLED);
        } else {
            tenantPO.setStatus(TenantStatusEnum.CLOSED);
        }
    }

    private String initUserPassword(UserPO user) {
        String password = PasswordUtils.getPassword(8);
        userService.fillWithPassword(user, password);
        return password;
    }

    private boolean filterByQcValue(DistributorCustomerQueryVO queryVO) {
        if (queryVO.getMinChatCount() != null || queryVO.getMinChatDuration() != null) {
            List<Long> list = new ArrayList<>();
            if (queryVO.getSubDistributor()) {
                list = qcCostService.getOpeSubDistributorCustomerIdList(queryVO.getMinChatCount(), queryVO.getMinChatDuration());
            } else {
                list = qcCostService.getOpeDistributorCustomerIdList(queryVO.getMinChatCount(), queryVO.getMinChatDuration());
            }
            if (!CollectionUtils.isEmpty(list)) {
                queryVO.setQcIdSet(new HashSet<>(list));
            } else {
                return true;
            }
        }
        return false;
    }

    @Override
    public PageResultObject listDistributorTotalCustomerForQiyu(DistributorCustomerQueryVO query) {
        Assert.notNull(query.getDistributorId(), "未选择代理商");
        //查询代理商  正式/试用转正式
        PageResultObject<QiyuTenantAiDetailVO> pageResultObject = tenantService.selectDistributorTenantPageByDistributorId(query.getDistributorId(), query.getPageNum(), query.getPageSize());
        if (CollectionUtils.isEmpty(pageResultObject.getContent())) {
            return PageResultObject.of(Lists.newArrayList());
        }
        List<Long> longList = pageResultObject.getContent().stream().map(QiyuTenantAiDetailVO::getTenantId).collect(Collectors.toList());
        query.setSelectTenantIdList(longList);
        if (Objects.nonNull(query.getEndDate())) {
            query.setEndDate(query.getEndDate().plusDays(1));
        }
        List<QiyuTenantAiDetailVO> result = robotService.selectByCondition(query);
        if (CollectionUtils.isEmpty(result)) {
            return pageResultObject;
        }
        Map<Long, List<QiyuTenantAiDetailVO>> listMap = result.stream().collect(Collectors.groupingBy(QiyuTenantAiDetailVO::getTenantId));
        pageResultObject.getContent().parallelStream().forEach(qiyuTenantAiDetailVO -> {
            List<QiyuTenantAiDetailVO> qiyuTenantAiDetailVOS = listMap.get(qiyuTenantAiDetailVO.getTenantId());
            if (!CollectionUtils.isEmpty(qiyuTenantAiDetailVOS)) {
                qiyuTenantAiDetailVO.setCount(qiyuTenantAiDetailVOS.stream().mapToInt(RobotPO::getCount).sum());
            }
        });
        return pageResultObject;
    }

    @Override
    public PageResultObject listDistributorDetailCustomerForQiyu(DistributorCustomerQueryVO query) {
        DistributorCustomerQueryVO queryVO = new DistributorCustomerQueryVO();
        queryVO.setSelectTenantIdList(Collections.singletonList(query.getTenantId()));
        queryVO.setStartDate(query.getStartDate());
        queryVO.setEndDate(query.getEndDate());
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<QiyuTenantAiDetailVO> qiyuTenantAiDetailVOS = robotPOMapper.selectByCondition(queryVO);
        if (CollectionUtils.isEmpty(qiyuTenantAiDetailVOS)) {
            return PageResultObject.of(Lists.newArrayList());
        }
        List<QiyuTenantAiDetailVO> qiyuTenantAiDetailVOList = tenantPOMapper.selectDistributorTenantPageByTenantId(query.getTenantId(), Arrays.asList(AccountTypeEnum.FORMAL, AccountTypeEnum.TRAIL_TO_FORMAL));
        QiyuTenantAiDetailVO qiyuTenantAiDetailVO = qiyuTenantAiDetailVOList.get(0);
        qiyuTenantAiDetailVOS.forEach(s -> {
            s.setEnableConcurrency(qiyuTenantAiDetailVO.getEnableConcurrency());
            s.setEnableTts(qiyuTenantAiDetailVO.getEnableTts());
            if (null != s.getStartTime() && null != s.getEndTime()) {
                Long aiDays = s.getStartTime().until(s.getEndTime(), ChronoUnit.DAYS) + 1;
                s.setAiDays(aiDays);
            }
        });
        return PageResultObject.of(qiyuTenantAiDetailVOS);
    }

    @Override
    public void infoResult(List<? extends QiyuTenantAiDetailVO> result) {
        result.forEach(qiyuTenantAiDetailVO -> {
            try {
                TenantPO tenantPO = tenantService.selectByKey(qiyuTenantAiDetailVO.getTenantId());
                qiyuTenantAiDetailVO.setDistributorName(distributorService.selectByKey(tenantPO.getDistributorId()).getName());
                qiyuTenantAiDetailVO.setTenantName(tenantPO.getCompanyName());
                qiyuTenantAiDetailVO.setLinkName(tenantPO.getLinkman());
                Long aiDays = qiyuTenantAiDetailVO.getStartTime().until(qiyuTenantAiDetailVO.getEndTime(), ChronoUnit.DAYS) + 1;
                qiyuTenantAiDetailVO.setAiDays(aiDays);
                qiyuTenantAiDetailVO.setEnableConcurrency(tenantPO.getEnableConcurrency());
                qiyuTenantAiDetailVO.setEnableTts(tenantPO.getEnableTts());
            } catch (Exception e) {
                return;
            }
        });
    }

    /**
     * 追加费用明细
     */
    @Override
    public void appendListAllDistributorCustomer(List<? extends DirectAndDistributorCustomerListVO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        items.forEach(item -> {
            CostListAccountFareListVO fare = costListService.getAccountFare(item.getTenantId());
            item.setQcAccountFare(fare.getQcAccountFare());
            item.setAllAccountFare(fare.getAllAccountFare());
            item.setPhoneNumberAccountFareList(fare.getPhoneNumberAccountFareList());
            item.setPrestoreFare(fare.getPrestoreFare());
        });
    }

    private boolean selectByAubAccountPhone(String subAccountPhone, DistributorCustomerQueryVO queryVO) {
        if (StringUtils.isNotBlank(subAccountPhone)) {
            UserPO user = userService.selectByPhoneNumber(subAccountPhone);
            if (Objects.isNull(user)) {
                return true;
            }
            queryVO.setTenantIdBySubAccountPhone(user.getTenantId());
        }
        return false;
    }

    @Override
    public JobStartResultVO exportListAllDistributorCustomer(DistributorCustomerQueryVO query) {
        //根据子账户手机号过滤
        if (selectByAubAccountPhone(query.getSubAccountPhone(), query)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }

        //根据质检过滤
        if (filterByQcValue(query)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }

        Long currentUserId = query.getCurrentLoginUserId();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("CURRENT_USER_ID", currentUserId);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.OPE.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerService.getHeaderListCache(APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTDISTRIBUTORCUSTOMER, PlatformTypeEnum.AICC)));
        jobParametersBuilder.addLong("TENANT_ID", OPE_TENANT_ID);


        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getBossExcelOssFileKey(EXPORT_OPE_DISTRIBUTOR_CUSTOMER.getDesc(), OPE_DISTRIBUTOR_ID, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        String queryVO = JsonUtils.object2String(query);
        jobParametersBuilder.addString("EXPORT_REQUEST", queryVO);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        SpringBatchJobTypeEnum jobType = EXPORT_OPE_DISTRIBUTOR_CUSTOMER;
        Integer totalCount = tenantService.countDistributorTenantListResult(query);
        if (totalCount.equals(0)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }
        Job job;
        //产品类型，使用状态服务结束时间过滤导出
        if (!CollectionUtils.isEmpty(query.getAccountStatuss()) || Objects.nonNull(query.getTenantAiccPart()) || Objects.nonNull(query.getStartDate())) {
            job = opeDistributorCustomerByRobotExportJob;
        } else {
            job = opeDistributorCustomerExportJob;
        }
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        OPE_TENANT_ID,
                        OPE_DISTRIBUTOR_ID,
                        totalCount,
                        currentUserId,
                        jobType,
                        SystemEnum.OPE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public JobStartResultVO exportListAllDistributorCustomerForQiyu(DistributorCustomerQueryVO query) {
        Assert.notNull(query.getDistributorId(), "未选择代理商");
        // 查询代理商的所有tenantId  正式/试用转正式
        // todo 范围查询优化
        List<Long> tenantIds = tenantService.getIdListByDistributorIdAndAccountType(query.getDistributorId(), Arrays.asList(AccountTypeEnum.FORMAL, AccountTypeEnum.TRAIL_TO_FORMAL));
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }
        query.setSelectTenantIdList(tenantIds);
        if (Objects.nonNull(query.getEndDate())) {
            query.setEndDate(query.getEndDate().plusDays(1));
        }

        Long currentUserId = query.getCurrentLoginUserId();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("CURRENT_USER_ID", currentUserId);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.OPE.name());
        List<HeaderInfoPO> headerInfoPOS = headerService.getHeaderCache(APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTDISTRIBUTORCUSTOMERFORQIYU, PlatformTypeEnum.AICC);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(headerInfoPOS)) {
            List<String> headList = headerInfoPOS.stream().map(HeaderInfoPO::getHeaderName).collect(Collectors.toList());
            jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headList));
        } else {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "表头数据不存在，请检查数据库");
        }
        jobParametersBuilder.addLong("TENANT_ID", OPE_TENANT_ID);


        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getBossExcelOssFileKey(EXPORT_OPE_DISTRIBUTOR_CUSTOMER_FOR_QIYU.getDesc(), OPE_DISTRIBUTOR_ID, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        String queryVO = JsonUtils.object2String(query);
        jobParametersBuilder.addString("EXPORT_REQUEST", queryVO);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        SpringBatchJobTypeEnum jobType = EXPORT_OPE_DISTRIBUTOR_CUSTOMER_FOR_QIYU;
        List<QiyuTenantAiDetailVO> result = robotService.selectByCondition(query);
        if (CollectionUtils.isEmpty(result)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        opeDistributorCustomerExportForQiyuJob,
                        jobParameters,
                        null,
                        OPE_TENANT_ID,
                        OPE_DISTRIBUTOR_ID,
                        result.size(),
                        currentUserId,
                        jobType,
                        SystemEnum.OPE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public JobStartResultVO exportListAllSubDistributorCustomer(DistributorCustomerQueryVO query) {

        //根据客户的子账号过滤
        if (selectByAubAccountPhone(query.getSubAccountPhone(), query)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }

        //根据质检过滤
        if (filterByQcValue(query)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }

        Long currentUserId = query.getCurrentLoginUserId();
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("CURRENT_USER_ID", currentUserId);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.OPE.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerService.getHeaderListCache(APIOPE_DISTRIBUTORCUSTOMER_EXPORTLISTSUBDISTRIBUTORCUSTOMER, PlatformTypeEnum.AICC)));
        jobParametersBuilder.addLong("TENANT_ID", OPE_TENANT_ID);

        String baseName = String.valueOf(now);
        String exportFileOssKey = OssKeyCenter.getBossExcelOssFileKey(EXPORT_OPE_SUB_DISTRIBUTOR_CUSTOMER.getDesc(), OPE_DISTRIBUTOR_ID, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        String queryVO = JsonUtils.object2String(query);
        jobParametersBuilder.addString("EXPORT_REQUEST", queryVO);
        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        SpringBatchJobTypeEnum jobType = EXPORT_OPE_SUB_DISTRIBUTOR_CUSTOMER;
        Integer totalCount = tenantService.countDistributorTenantListResult(query);
        if (totalCount.equals(0)) {
            throw new ComException(ComErrorCode.RESOURCE_NOT_FOUND, "创建导出任务失败，没有符合条件的客户");
        }
        Job job;
        //产品类型，使用状态服务结束时间过滤导出
        if (!CollectionUtils.isEmpty(query.getAccountStatuss()) || Objects.nonNull(query.getTenantAiccPart()) || Objects.nonNull(query.getStartDate())) {
            job = opeDistributorCustomerByRobotExportJob;
        } else {
            job = opeSubDistributorCustomerExportJob;
        }
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        null,
                        OPE_TENANT_ID,
                        OPE_DISTRIBUTOR_ID,
                        totalCount,
                        currentUserId,
                        jobType,
                        SystemEnum.OPE,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDistributorCustomer(TenantPO tenantPO) {
        //校验客户名不能重复
        String companyName = tenantPO.getCompanyName();
        List<TenantPO> tenantPOList = tenantPOMapper.selectByCompanyNameExceptSelf(companyName, tenantPO.getTenantId());
        if (!CollectionUtils.isEmpty(tenantPOList)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "客户名重复,请重新设置");
        }
        String phoneNumber = tenantPO.getPhoneNumber();
        //如果是七鱼用户 不检查超管
        if (!Objects.equals(qiyuTestDistributorId, tenantPO.getDistributorId()) && !Objects.equals(qiyuProdDistributorId, tenantPO.getDistributorId())) {
            //获取该客户的超管
            UserPO user = userService.selectAdminByTenantId(tenantPO.getTenantId());
            if (user == null) {
                throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "该客户没有对应的超管!");
            }
            //排除自身看还能不能查出其他用户
            if (!phoneNumber.equals(user.getPhoneNumber()) && !userService.isAllowCreateUser(phoneNumber, SystemEnum.CRM)) {
                throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "已经存在手机号为" + phoneNumber + "的用户");
            }
            //更新超管手机号
            user.setPhoneNumber(phoneNumber);
            user.setName(tenantPO.getLinkman());
            user.setNickname(tenantPO.getLinkman());
            userService.updateNotNull(user);
        }
        //对于直销客户要添加检查
        TenantPO oldTenantPo = tenantService.selectByKey(tenantPO.getTenantId());

        if (Objects.nonNull(oldTenantPo.getDistributorId())) {
            //如果是转正客户
            if (AccountTypeEnum.ON_TRIAL.equals(oldTenantPo.getAccountType()) && AccountTypeEnum.FORMAL.equals(tenantPO.getAccountType())) {
                tenantPO.setUpdateFormalTime(LocalDateTime.now());
                tenantPO.setAccountType(AccountTypeEnum.TRAIL_TO_FORMAL);
                robotService.updateFreeAccountToFormal(tenantPO.getTenantId());
                //如果是测试客户
            } else if (AccountTypeEnum.TEST.equals(oldTenantPo.getAccountType())) {
                tenantPO.setAccountType(null);
            }

            if (!oldTenantPo.getDistributorId().equals(tenantPO.getDistributorId())) {
                DistributorPO distributorPO = distributorService.selectByKey(tenantPO.getDistributorId());
                if (Boolean.TRUE.equals(distributorPO.getMainBrandBlacklist())) {
                    tenantPO.setMainBrandBlacklist(distributorPO.getMainBrandBlacklist());
                    tenantPO.setNewMainBrandId(distributorPO.getNewMainBrandId());
                }else {
                    if (oldTenantPo.getNewMainBrandId() != null) {
                        tenantService.updateNewMainBrandIdAndMainBrandBlacklist(Collections.singletonList(tenantPO.getTenantId()), null, false);
                    }
                }
            }

        }
        tenantPO.setPhoneNumber(phoneNumber);
        updateNotNull(tenantPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOpeDistributorCustomer(CustomerUpdateVO customerUpdateVO) {
        updateDistributorCustomer(customerUpdateVO);
        if (customerUpdateVO.getDeploymentUserIdList() != null && !customerUpdateVO.getDeploymentUserIdList().isEmpty()) {
            deploymentUserService.deleteByTenantId(customerUpdateVO.getTenantId());
            deploymentUserService.batchTenantInsert(customerUpdateVO.getTenantId(), customerUpdateVO.getDeploymentUserIdList());
        }

        if (StringUtils.isNotEmpty(customerUpdateVO.getOrgIdCardNum()) && StringUtils.isNotEmpty(customerUpdateVO.getOrgName())) {
            IdentityInfoDTO identityInfoDTO = new IdentityInfoDTO(customerUpdateVO.getOrgIdCardNum(), customerUpdateVO.getOrgName(), CRED_ORG_USCC);
            tenantService.saveTenantIdentityInfo(customerUpdateVO.getTenantId(), identityInfoDTO);
        }
        //同步到计费统计服务
        syncToBillStatisticService(customerUpdateVO);
    }

	/**
	 * 初始化旧版外呼和新版外呼的[导入任务时保留全部变量]字段
	 */
	private void initSkipPropertiesDeal(Long tenantId) {
		if (!CommonApplicationConstant.CURR_ENV.isFinance()) {
			return;
		}
		try {
			TenantCalloutJobConfigMongoPO update = new TenantCalloutJobConfigMongoPO();
			update.setTenantId(tenantId);
			update.setSkipPropertiesDeal(1);
			mongoOperationService.saveTenantCallJobConfig(update);
		} catch (Exception e) {
			logger.error("初始化旧版外呼的[导入任务时保留全部变量]字段失败, tenantId={}", tenantId, e);
		}
		try {
			TenantCallJobConfigVO tenantCallJobConfigVO = new TenantCallJobConfigVO();
			tenantCallJobConfigVO.setTenantId(tenantId);
			tenantCallJobConfigVO.setSkipPropertiesDeal(1);
			callOutJobClient.saveTenantCallJobConfig(tenantCallJobConfigVO);
		} catch (Exception e) {
			logger.error("初始化新版外呼的[导入任务时保留全部变量]字段失败, tenantId={}", tenantId, e);
		}
        try{
            TenantCrowdSnapshotConfigDTO tenantCrowdSnapshotConfigDTO=new TenantCrowdSnapshotConfigDTO();
            tenantCrowdSnapshotConfigDTO.setCrowdSnapshotOpen(YesOrNoEnum.YES.getCode());
            tenantCrowdSnapshotConfigDTO.setTenantId(tenantId);
            crowdClient.saveTenantCrowdConfig(tenantCrowdSnapshotConfigDTO);
        }catch (Exception e){
            logger.error("初始化人群包快照开关失败,tenantId={}",tenantId,e);
        }
	}

    private void syncToBillStatisticService(TenantPO tenantPO) {

        BillStatisticTenantVO billStatisticTenantVO = new BillStatisticTenantVO();
        billStatisticTenantVO.setTenantId(tenantPO.getTenantId());
        billStatisticTenantVO.setDistributorId(tenantPO.getDistributorId());
        billStatisticTenantVO.setCompanyName(tenantPO.getCompanyName());
        billStatisticTenantVO.setCompanyType(StatisticCompanyTypeEnum.ECOLOGICAL);
        billStatisticTenantVO.setEnv(CommonApplicationConstant.CURR_STATISTIC_ENV);
        billStatisticTenantVO.setTenantPayType(CodeDescEnum.getFromDescOrThrow(StatisticTenantPayTypeEnum.class, tenantPO.getTenantPayType().getDesc()));

        DistributorPO distributorPO = distributorService.selectByKeyOrThrow(tenantPO.getDistributorId());
        billStatisticTenantVO.setDistributorName(distributorPO.getName());
        billStatisticSyncMetaInfoService.saveTenant(billStatisticTenantVO);
    }

    @Override
    public List<TenantIdAndNamePairVO> getTenantIdAndNamePairList(Long distributorId, String searchName) {
        List<Long> distributorIds = distributorService.selectByParentId(distributorId);
        if (distributorIds.isEmpty()) {
            return Collections.emptyList();
        }
        return tenantPOMapper.getTenantIdAndNamePairByDistributorIds(new HashSet<>(distributorIds), searchName);
    }

    @Override
    public void resolveDistributorName(List<? extends DirectAndDistributorCustomerListVO> tenantPOList) {
        if (CollectionUtils.isEmpty(tenantPOList)) {
            return;
        }
        Set<Long> distributorIdSet = tenantPOList.stream().map(DirectAndDistributorCustomerListVO::getDistributorId).collect(Collectors.toSet());
        Map<? extends Serializable, DistributorPO> distributorPOMap = distributorService.selectMapByKeyCollect(distributorIdSet);
        if (MapUtils.isEmpty(distributorPOMap)) {
            return;
        }
        tenantPOList.forEach(item -> {
            Try.run(() -> {
                DistributorPO distributorPO = distributorPOMap.get(item.getDistributorId());
                if (distributorPO != null) {
                    item.setName(distributorPO.getName());
                }
                // 资质认证
                AuthenticationPO authenticationPO = authenticationService.selectAuthenticationByTenantId(item.getTenantId());
                item.setAuthenticationPO(authenticationPO);
            }).onFailure(e -> logger.error("设置分销商名称失败 DistributorId={}", item.getDistributorId(), e));
        });
    }

    /**
     * 消耗下一级代理商的坐席
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addBossDistributorCustomer(CustomerInsertVO customerInsertVO, Long localDistributorId) {
        Long distributorId = customerInsertVO.getDistributorId();
        DistributorPO distributor = distributorService.selectByKey(distributorId);
        DistributorPO distributorPO = distributorService.selectByKey(localDistributorId);
        //二级代理商无权限新建代理商客户
        if (distributorPO.getParentId() != 0) {
            throw new ComException(ComErrorCode.FORBIDDEN, "二级代理商无权限新建代理商客户");
        }
        //校验客户名字不能重复
        String companyName = customerInsertVO.getCompanyName();
        TenantPO tenantPO = tenantPOMapper.selectByCompanyName(companyName);
        if (Objects.nonNull(tenantPO)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "客户名重复,请重新设置");
        }
        String phoneNumber = MyStringUtils.getNumber(customerInsertVO.getPhoneNumber());
        if (!userService.isAllowCreateUser(phoneNumber, SystemEnum.CRM)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "已经存在手机号为" + phoneNumber + "的用户");
        }
        customerInsertVO.setPhoneNumber(phoneNumber);
        LocalDate startTime = customerInsertVO.getStartTime();
        LocalDate endTime = customerInsertVO.getEndTime();
        if ((Objects.nonNull(startTime) && Objects.nonNull(endTime)) && (startTime.isBefore(LocalDate.now()) || endTime.isBefore(LocalDate.now()))) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "服务日期选择不能早于今天！");
        }
        tenantPO = new TenantPO();
        BeanUtils.copyProperties(customerInsertVO, tenantPO);
        //根据userStatusEnum设置tenant的status
        setCustomerStatus(customerInsertVO, tenantPO);
        //直销客户的distributor_id 为非空
        tenantPO.setDistributorId(distributorId);
        // 代理商客户默认关闭
        tenantPO.setEnableRechargeOnline(false);
        tenantPO.setUseYiwiseAsr(false);
        tenantService.insertTenant(tenantPO);
        if (Objects.nonNull(tenantPO.getAiConcurrencyLevel()) && Objects.nonNull(tenantPO.getStartTime()) && Objects.nonNull(tenantPO.getEndTime())) {
            //计算月份差距（）
            int total = (int) startTime.until(endTime, ChronoUnit.DAYS) + 1;
            Integer totalAll = total * tenantPO.getAiConcurrencyLevel().intValue();
            tenantPO.setEnableCsSeat(true);

            //减去该目标经销商的AI坐席库存，这里不涉及话术去库存的问题
            //已售坐席
            Integer soldRobotNew = distributor.getSoldRobot() + totalAll;
            distributor.setSoldRobot(soldRobotNew);
            //设置预存款余额
            double cost = distributorCustomerService.calculateAiCost(tenantPO.getAiConcurrencyLevel().doubleValue(), distributor.getDistributorId(), startTime, endTime, true);
            if (distributor.getPrestoreFare() < cost) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "代理商预存款不足，无法新增客户");
            }
            distributor.setPrestoreFare(distributor.getPrestoreFare() - cost);
            distributorService.updateNotNull(distributor);
            //添加预存款流水
            rechargePrestoreStreamService.addPrestoreStream(tenantPO.getTenantId(), distributorId, cost, distributor.getPrestoreFare(), tenantPO.getAiConcurrencyLevel().intValue(), customerInsertVO.getCreateUserId(), TargetTypeEnum.DISTRIBUTOR, RechargePrestoreStreamHandEnum.CONSUME_ADD_DIRECTCUSTOMER, RechargePrestoreStreamFunEnum.DEFAULT, "", RechargeMethodEnum.OFF_LINE);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, cost, 0d);
            //新增客户的机器坐席
            RobotPO robot = createRobot(tenantPO);
            //添加AI坐席的消费流水
            RobotStreamPO robotStream = new RobotStreamPO();
            robotStream.setType(RobotStreamTypeEnum.CONSUME_ADD_DIRECT_CUSTOMER);
            robotStream.setTenantName(tenantPO.getCompanyName());
            robotStream.setLinkMan(tenantPO.getLinkman());
            robotStream.setPhoneNumber(tenantPO.getPhoneNumber());
            robotStream.setAiConcurrency(tenantPO.getAiConcurrencyLevel().intValue());
            robotStream.setAiMonth(total);
            robotStream.setAiCount(totalAll);
//        robotStream.setRobotRemaining(remainingRobot);
            //经销商的id
            robotStream.setDistributorId(distributor.getDistributorId());
            robotStreamService.saveNotNull(robotStream);

            //新加crm端购买记录明细
            createPurchaseDetail(tenantPO, robot);
        }

        //新建一个组织，给新建的客户提供一个默认的组织
        OrganizationPO organizationPO = new OrganizationPO();
        organizationPO.setTenantId(tenantPO.getTenantId());
        organizationPO.setName("默认根组织");
        //默认根组织的parentId为0
        organizationPO.setParentId(ApplicationConstant.PARENT_ID);
        organizationPO.setManagerUserId(ApplicationConstant.ORGANIZATION_MANAGER_ID);
        organizationService.saveNotNull(organizationPO);

        //新建一个用户,给新建的客户提供一个默认的用户
        UserPO user = new UserPO();
        user.setTenantId(tenantPO.getTenantId());
        user.setPhoneNumber(tenantPO.getPhoneNumber());

        //同步用户信息
        user.setName(customerInsertVO.getLinkman());
        user.setDistributorId(distributorId);
        //设置默认用户的账号状态
//        user.setStatus(customerInsertVO.getStatus());
        user.setNickname(tenantPO.getPhoneNumber());
//        user.setOrganizationId(organizationPO.getOrganizationId());
        Long roleId = roleService.selectByCharacterAndSystem(RoleTypeEnum.BUILT_IN_SUPERADMIN, SystemEnum.CRM).getRoleId();
        String defaultPassword = initUserPassword(user);
        try {
            //设置初始密码。并把常量盐写入数据库
            userService.addUser(user, defaultPassword, SystemEnum.CRM, organizationPO.getOrganizationId(), roleId);
        } catch (Exception e) {
            if (e instanceof DuplicateKeyException && e.getMessage().contains("Duplicate entry")) {
                throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "联系方式重复,请确认!!!");
            }
            logger.error(e.getMessage());
        }
        //更新组织
        organizationPO.setManagerUserId(user.getUserId());
        organizationService.updateNotNull(organizationPO);


        //redis相关操作
        String tenantTodayRobotCountRedisKey = RedisKeyCenter.getTenantTodayRobotCount(tenantPO.getTenantId());
        redisOpsService.delete(tenantTodayRobotCountRedisKey);
        return defaultPassword;
    }

    @Override
    @Transactional
    public void addSubBossDistributorCustomer(TenantPO tenantPO) {
        //校验客户名字不能重复
        String companyName = tenantPO.getCompanyName();
        TenantPO exitTenantPO = tenantPOMapper.selectByCompanyName(companyName);
        if (Objects.nonNull(exitTenantPO)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "客户名重复,请重新设置");
        }
        //检验手机号是否重复
        String phoneNumber = MyStringUtils.getNumber(tenantPO.getPhoneNumber());
        if (!userService.isAllowCreateUser(phoneNumber, SystemEnum.CRM)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "已经存在手机号为" + phoneNumber + "的用户");
        }
        tenantPO.setPhoneNumber(phoneNumber);
        Long distributorId = tenantPO.getDistributorId();
        DistributorPO distributor = distributorService.selectByKey(distributorId);
        if (Objects.nonNull(tenantPO.getStartTime()) && Objects.nonNull(tenantPO.getEndTime())) {
            int total = (int) tenantPO.getStartTime().until(tenantPO.getEndTime(), ChronoUnit.DAYS) + 1;
            Integer totalAll = total * tenantPO.getAiConcurrencyLevel().intValue();
            //设置预存款余额
            double cost = distributorCustomerService.calculateAiCost(tenantPO.getAiConcurrencyLevel().doubleValue(), distributorId, tenantPO.getStartTime(), tenantPO.getEndTime(), true);
            if (distributor.getPrestoreFare() < cost) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "分销商预存款不足，无法新增客户");
            }
            distributor.setPrestoreFare(distributor.getPrestoreFare() - cost);
            distributor.setSoldRobot(distributor.getSoldRobot() + totalAll);
            distributorService.updateNotNull(distributor);
            //添加预存款流水
            rechargePrestoreStreamService.addPrestoreStream(tenantPO.getTenantId(), distributorId, cost, distributor.getPrestoreFare(), tenantPO.getAiConcurrencyLevel().intValue(), tenantPO.getCreateUserId(), TargetTypeEnum.DISTRIBUTOR, RechargePrestoreStreamHandEnum.CONSUME_ADD_DIRECTCUSTOMER, RechargePrestoreStreamFunEnum.DEFAULT, "", RechargeMethodEnum.OFF_LINE);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, cost, 0d);
        }
        tenantPO.setAuditStatus(AuditStatusEnum.PENDING);
        tenantPO.setEnableCsSeat(true);
        // 代理商客户默认关闭
        tenantPO.setEnableRechargeOnline(false);
        tenantPO.setUseYiwiseAsr(false);
        tenantService.insertTenant(tenantPO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String auditSubDistributorCustomer(Boolean result, String auditNote, Long tenantId, Long userId) {
        TenantPO tenantPO = tenantPOMapper.selectByPrimaryKey(tenantId);
        if (tenantPO.getAuditStatus() == AuditStatusEnum.PASS) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "改客户已经审核通过");
        }
        if (result) {
            tenantPO.setAuditStatus(AuditStatusEnum.PASS);
            tenantPO.setAuditNote(auditNote);
            updateNotNull(tenantPO);
            //创建人信息
            UserPO userPO = userService.selectByKey(tenantPO.getCreateUserId());
            DistributorPO distributor = distributorService.selectByKey(tenantPO.getDistributorId());
            RobotPO robot = new RobotPO();
            if (Objects.nonNull(tenantPO.getStartTime()) && Objects.nonNull(tenantPO.getEndTime())) {
                //需删减的库存
                int total = (int) tenantPO.getStartTime().until(tenantPO.getEndTime(), ChronoUnit.DAYS) + 1;
                Integer totalAll = total * tenantPO.getAiConcurrencyLevel().intValue();
                //给二级代理商的流水
                RobotStreamPO robotStreamPO = new RobotStreamPO(
                        null, RobotStreamTypeEnum.CONSUME_ADD_DIRECT_CUSTOMER, tenantPO.getCompanyName(),
                        distributor.getName(), tenantPO.getLinkman(),
                        tenantPO.getPhoneNumber(), null, tenantPO.getAiConcurrencyLevel().intValue(), total, totalAll,
                        distributor.getRemainingRobot() - totalAll, distributor.getDistributorId(),
                        userPO.getUserId(), userPO.getUserId(), null, 1
                );
                robotStreamService.createRecord(robotStreamPO);
                //减库存
                distributor.setSoldRobot(distributor.getSoldRobot() + totalAll);
                distributorService.updateNotNull(distributor);
                //新增客户的机器坐席
                robot = createRobot(tenantPO);
            }


            //新建一个组织，给新建的客户提供一个默认的组织
            OrganizationPO organizationPO = new OrganizationPO();
            organizationPO.setTenantId(tenantPO.getTenantId());
            organizationPO.setName("默认根组织");
            //默认根组织的parentId为0
            organizationPO.setParentId(ApplicationConstant.PARENT_ID);
            organizationPO.setManagerUserId(ApplicationConstant.ORGANIZATION_MANAGER_ID);
            organizationService.saveNotNull(organizationPO);

            //新建一个用户,给新建的客户提供一个默认的用户
            UserPO user = new UserPO();
            user.setTenantId(tenantPO.getTenantId());
            user.setPhoneNumber(tenantPO.getPhoneNumber());

            //同步用户信息
            user.setDistributorId(tenantPO.getDistributorId());
            user.setCreateUserId(userId);
            user.setName(tenantPO.getLinkman());
            //设置默认用户的账号状态
            user.setNickname(tenantPO.getPhoneNumber());
            RolePO rolePO = roleService.selectByCharacterAndSystem(RoleTypeEnum.BUILT_IN_SUPERADMIN, SystemEnum.CRM);
            // 初始化密码数据
            String defaultPassword = initUserPassword(user);
            userService.addUser(user, defaultPassword, SystemEnum.CRM, organizationPO.getOrganizationId(), rolePO.getRoleId());
            //更新组织
            organizationPO.setManagerUserId(user.getUserId());
            organizationService.updateNotNull(organizationPO);
            //新加crm端购买记录明细
            PurchaseDetailPO purchaseDetailPO = new PurchaseDetailPO();
            purchaseDetailPO.setTenantId(tenantId);
            purchaseDetailPO.setRobotCount(robot.getCount());
            purchaseDetailPO.setDetailType(RobotPurchaseDetailTypeEnum.BUY);
            purchaseDetailPO.setStartDate(robot.getStartTime());
            purchaseDetailPO.setEndDate(robot.getEndTime());

            //redis相关操作
            String tenantTodayRobotCountRedisKey = RedisKeyCenter.getTenantTodayRobotCount(tenantPO.getTenantId());
            redisOpsService.delete(tenantTodayRobotCountRedisKey);
            return defaultPassword;
        } else {
            tenantPO.setAuditNote(auditNote);
            tenantPO.setAuditStatus(AuditStatusEnum.FAIL);
            updateNotNull(tenantPO);
            return null;
        }
    }

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public PageResultObject<TenantPO> queryCustomerWaitingForAuditing(Integer pageNum, Integer pageSize, Long distributorId, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager, Long createUserId) {
        List<Long> distributorIds = distributorService.selectByParentId(distributorId);
        PageHelper.startPage(pageNum, pageSize);
        List<TenantPO> tenantPOS = tenantService.getAuditedTenantByDistributorId(distributorIds, accountType, auditStatus, companyName, linkMan, phoneNumber, accountManager, createUserId);
        return PageResultObject.of(tenantPOS);
    }

    @Override
    @TargetDataSource(value = DataSourceEnum.SLAVE)
    public PageResultObject<TenantPO> queryForBeingAudited(Integer pageNum, Integer pageSize, Long distributorId, AccountTypeEnum accountType, AuditStatusEnum auditStatus, String companyName, String linkMan, String phoneNumber, String accountManager) {
        PageHelper.startPage(pageNum, pageSize);
        List<TenantPO> tenantPOS = tenantService.getAuditingTenantByDistributorId(distributorId, accountType, auditStatus, companyName, linkMan, phoneNumber, accountManager);
        return PageResultObject.of(tenantPOS);
    }

    @Override
    public void updateSubBossDistributorCustomer(TenantPO tenantPO) {
        //校验客户名不能重复
        //别再找user了!!!!!!
        String companyName = tenantPO.getCompanyName();
        List<TenantPO> tenantPOList = tenantPOMapper.selectByCompanyNameExceptSelf(companyName, tenantPO.getTenantId());
        if (!CollectionUtils.isEmpty(tenantPOList)) {
            throw new ComException(ComErrorCode.RESOURCE_CONFLICT, "客户名重复,请重新设置");
        }
        String phoneNumber = MyStringUtils.getNumber(tenantPO.getPhoneNumber());
        tenantPO.setPhoneNumber(phoneNumber);
        tenantPO.setAuditStatus(AuditStatusEnum.PENDING);
        updateNotNull(tenantPO);

    }

    @Override
    public TenantPO selectByKey(Number key) {
        String redisKey = RedisKeyCenter.getTenantPrimaryRedisKey(key);
        return cacheTplService.findCache(redisKey, ApplicationConstant.POJO_CACHE_TIMEOUT_OF_SECOND, TimeUnit.SECONDS, TenantPO.class, () -> super.selectByKey(key));
    }

    @Override
    public TenantPO selectByKeyOrThrow(Number key) {
        String redisKey = RedisKeyCenter.getTenantPrimaryRedisKey(key);
        return cacheTplService.findCache(redisKey, ApplicationConstant.POJO_CACHE_TIMEOUT_OF_SECOND, TimeUnit.SECONDS, TenantPO.class, () -> super.selectByKeyOrThrow(key));
    }

    @Override
    public int delete(Number key) {
        int result = super.delete(key);
        deleteCacheFromRedis(key);
        return result;
    }

    @Override
    public int updateAll(TenantPO entity) {
	    return tenantService.updateAll(entity);
    }

    @Override
    public int updateNotNull(TenantPO entity) {
	    return tenantService.updateNotNull(entity);
    }

    private void deleteCacheFromRedis(Number tenantId) {
        String primaryRedisKey = RedisKeyCenter.getTenantPrimaryRedisKey(tenantId);
        cacheTplService.delete(primaryRedisKey);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void opeAndBossDistributorCustomerDelay(Long id, LocalDate newEndTime, Long tenantId, Long userId, TenantAiccPartEnum tenantAiccPartEnum) {
        Assert.notNull(id, "机器坐席的id不能为空");
        Assert.notNull(newEndTime, "要延期的时间不能为空");
        RobotPO robotPO = robotService.selectByKey(id);
        if (newEndTime.isBefore(robotPO.getEndTime())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要延期的时间不能早于原结束服务时间");
        }
        //要延期的时间不能早于今天
        if (newEndTime.isBefore(LocalDate.now())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要延期的时间不能早于今天!");
        }
        TenantPO tenantPO = tenantService.selectByKey(tenantId);
        Long distributorId = tenantPO.getDistributorId();
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        //校验预存款够不够 正数表示退回，负数表示消耗
        double cost = distributorCustomerService.calculateDelayCost(robotPO.getCount().doubleValue(), tenantPO.getDistributorId(), tenantId, newEndTime, id);
        if (distributorPO.getPrestoreFare() + cost < 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "该代理商预存款余额不足，请调整延期日期或充值");
        }
        //加锁
        RedisLock redisLock = new RedisLock(redisTemplate, RedisLockKeyHelper.DISTRIBUTOR_PRESTORE_FARE + distributorPO.getDistributorId());
        try {
            redisLock.lock();

            // 若代理商客户为“按接通”或“按分钟”的计费模式 则延期不动预存款不扣预存款
            TenantPayTypeEnum tenantPayType = tenantPO.getTenantPayType();
            boolean noDesPreMoney = (tenantPayType.equals(TenantPayTypeEnum.PIECE) || tenantPayType.equals(TenantPayTypeEnum.MINUTE)) && (TenantAiccPartEnum.crm_out_call_platform.equals(robotPO.getTenantAiccPart()) || TenantAiccPartEnum.FUN_MULTI_CONCURRENCY.equals(robotPO.getTenantAiccPart()));
            if (!noDesPreMoney) {
                //代理商预存款更新
                distributorPO.setPrestoreFare(distributorPO.getPrestoreFare() + cost);
                distributorPO.setUpdateTime(LocalDateTime.now());
                distributorService.updateNotNull(distributorPO);
                //添加流水
	            rechargePrestoreStreamService.addPrestoreStream(tenantId, distributorPO.getDistributorId(),
			            Math.abs(cost), distributorPO.getPrestoreFare(), robotPO.getCount(), userId, TargetTypeEnum.DISTRIBUTOR,
			            RechargePrestoreStreamHandEnum.CONSUME_DIRECTCUSTOMER_DELAY, RechargePrestoreStreamFunEnum.getByTenantAiccPartEnum(tenantAiccPartEnum),
			            "", RechargeMethodEnum.OFF_LINE, LocalDate.now(), newEndTime);
                if (cost > 0) {
                    rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorPO.getDistributorId(), 0d, 0d, Math.abs(cost));
                } else {
                    rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorPO.getDistributorId(), 0d, Math.abs(cost), 0d);
                }
            }


        }finally {
            redisLock.unlock();
        }

        //如果服务已经结束，再点击延期的话，原坐席记录不变动，新增一条坐席记录，坐席个数与原要调整的坐席个数相同
        //坐席起始日期为当前日期，坐席结束日期为调整后的结束日期,扣除坐席数按照 （新的结束服务时间 - 当前时间 +1）* 并发数 计算
        if (robotPO.getEndTime().isBefore(LocalDate.now())) {
            //首先判断库存,更新库存
            Integer aiCount = (int) (LocalDate.now().until(newEndTime, ChronoUnit.DAYS) + 1);
            Integer aiCountAll = aiCount * robotPO.getCount();
            distributorPO.setSoldRobot(distributorPO.getSoldRobot() + aiCountAll);
            distributorService.updateNotNull(distributorPO);
            //新建新的机器坐席
            RobotPO newRobot = createNewRobot(newEndTime, tenantId, userId, robotPO, tenantAiccPartEnum);
            createRobotStream(userId, tenantPO, distributorId, distributorPO, aiCount, aiCountAll, newRobot);
            //新加crm端购买记录明细
            PurchaseDetailPO purchaseDetailPO = new PurchaseDetailPO();
            purchaseDetailPO.setTenantId(tenantId);
            purchaseDetailPO.setRobotCount(newRobot.getCount());
            purchaseDetailPO.setDetailType(RobotPurchaseDetailTypeEnum.DELAY);
            purchaseDetailPO.setStartDate(newRobot.getStartTime());
            purchaseDetailPO.setEndDate(newRobot.getEndTime());
            purchaseDetailService.saveNotNull(purchaseDetailPO);
            //redis相关操作
            updateRedis(tenantId);
        } else {
            //代理商新建流水，减去库存
            Integer aiCount = (int) robotPO.getEndTime().until(newEndTime, ChronoUnit.DAYS);
            Integer aiCountAll = aiCount * robotPO.getCount();
            distributorPO.setSoldRobot(distributorPO.getSoldRobot() + aiCountAll);
            distributorService.updateNotNull(distributorPO);
            //新建流水记录
            createRobotStream(userId, tenantPO, distributorId, distributorPO, aiCount, aiCountAll, robotPO);
            //更新机器坐席的结束时间
            robotPO.setEndTime(newEndTime);
            robotService.updateNotNull(robotPO);
            //新加crm端购买记录明细
            PurchaseDetailPO purchaseDetailPO = new PurchaseDetailPO();
            purchaseDetailPO.setTenantId(tenantId);
            purchaseDetailPO.setRobotCount(robotPO.getCount());
            purchaseDetailPO.setDetailType(RobotPurchaseDetailTypeEnum.DELAY);
            purchaseDetailPO.setStartDate(robotPO.getStartTime());
            purchaseDetailPO.setEndDate(newEndTime);
            purchaseDetailService.saveNotNull(purchaseDetailPO);
            //redis相关操作
            updateRedis(tenantId);
        }
        //redis删除 distributor信息
        redisOpsService.delete(RedisKeyCenter.getDistributorPrimaryRedisKey(distributorId));

        if (tenantAiccPartEnum == TenantAiccPartEnum.RECEPTION_BOT) {
            receptionConfigService.syncLatestConcurrency(tenantId);
        }
    }

    @Override
    public Double calculateDelayCost(Double objectCount, Long distributorId, Long tenantId, LocalDate newEndTime, Long robotId) {
        //计算消费金额
        RobotPO robotPO = robotService.selectByKey(robotId);
        TenantPO tenantPO = tenantService.selectByKey(tenantId);

        TenantPayTypeEnum tenantPayType = tenantPO.getTenantPayType();
        boolean noDesPreMoney = (tenantPayType.equals(TenantPayTypeEnum.PIECE) || tenantPayType.equals(TenantPayTypeEnum.MINUTE)) && (TenantAiccPartEnum.crm_out_call_platform.equals(robotPO.getTenantAiccPart()) || TenantAiccPartEnum.FUN_MULTI_CONCURRENCY.equals(robotPO.getTenantAiccPart()));
        if (noDesPreMoney) {
            return 0d;
        }
        if (newEndTime.isBefore(robotPO.getEndTime())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要延期的时间不能早于原结束服务时间");
        }
        //要延期的时间不能早于今天
        if (newEndTime.isBefore(LocalDate.now())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "要延期的时间不能早于今天!");
        }
	    //如果之前的AI 已经过期，则新加的时间需要+1  否则不加
	    boolean isAddDay = robotPO.getEndTime().isBefore(LocalDate.now());
	    //如果是语音坐席 直接计算返回
        if (Objects.equals(robotPO.getTenantAiccPart(), TenantAiccPartEnum.voice_cs_console)) {
            double csConsole = distributorCustomerService.calculateCsConsoleCost(objectCount, distributorId, robotPO.getEndTime().isBefore(LocalDate.now()) ? LocalDate.now() : robotPO.getEndTime(), newEndTime, isAddDay);
            return -csConsole;
        }
        //如果是一线多并发 直接计算返回
        if (Objects.equals(robotPO.getTenantAiccPart(), TenantAiccPartEnum.FUN_MULTI_CONCURRENCY)) {
            double csConsole = distributorCustomerService.calculateMultiConcurrencyCost(objectCount, distributorId, robotPO.getEndTime().isBefore(LocalDate.now()) ? LocalDate.now() : robotPO.getEndTime(), newEndTime, isAddDay);
            return -csConsole;
        }
        //计算AI 坐席费用
        double aiCost = distributorCustomerService.calculateAiCost(objectCount, distributorId, robotPO.getEndTime().isBefore(LocalDate.now()) ? LocalDate.now() : robotPO.getEndTime(), newEndTime, isAddDay);
        //确定开通功能
        AtomicDouble result = new AtomicDouble(0);
        result.getAndAdd(aiCost);
        if (tenantPO.getEnableTts()) {
            double ttsCost = distributorCustomerService.calculateFunctionDelayCost(objectCount, distributorId, tenantId, RechargePrestoreStreamFunEnum.FUN_TTS_COMPOSE, robotId, newEndTime, isAddDay);
            result.getAndAdd(ttsCost);
        }
        //计算总的费用
        return -result.doubleValue();
    }

    private void createRobotStream(Long userId, TenantPO tenantPO, Long distributorId, DistributorPO distributorPO, Integer aiCount, Integer aiCountAll, RobotPO newRobot) {
        //新建流水记录
        RobotStreamPO robotStreamPO = new RobotStreamPO(
                null, RobotStreamTypeEnum.CONSUME_DIRECT_CUSTOMER_DELAY, tenantPO.getCompanyName(), distributorPO.getName(),
                tenantPO.getLinkman(), tenantPO.getPhoneNumber(), null, newRobot.getCount(), aiCount, aiCountAll,
                distributorPO.getRemainingRobot(), distributorId, userId, userId, null, 1
        );
        robotStreamService.createRecord(robotStreamPO);
    }

    private RobotPO createNewRobot(LocalDate newEndTime, Long tenantId, Long userId, RobotPO robotPO, TenantAiccPartEnum tenantAiccPartEnum) {
        RobotPO newRobot = new RobotPO();
        newRobot.setTenantId(tenantId);
        newRobot.setStartTime(LocalDate.now());
        newRobot.setEndTime(newEndTime);
        newRobot.setCount(robotPO.getCount());
        newRobot.setTenantAiccPart(tenantAiccPartEnum);
        newRobot.setCreateUserId(userId);
        newRobot.setUpdateUserId(userId);
        robotService.saveNotNull(newRobot);
        return newRobot;
    }

    private void updateRedis(Long tenantId) {
        String tenantTodayRobotCountRedisKey = RedisKeyCenter.getTenantTodayRobotCount(tenantId);
        // 重置当天的可用坐席数量
        redisOpsService.delete(tenantTodayRobotCountRedisKey);
        //重置一线多并发数
        redisOpsService.delete(RedisKeyCenter.getDistributorTenantTodayConcurrency(tenantId));
        // 将因为没有可用坐席停掉的任务变为排队中
        robotCallJobService.updateAllNoRobotAvailableJobToInQueue(tenantId);
    }

    @Override
    public Double calculateAiCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime, Boolean isAddDay) {
        //先确定周期
        int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, isAddDay);
        //获取对应的维护价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到维护的对应AI坐席价格");
        }
        //AI 坐席只有 每年 跟 每月
        double result;
        if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getRobotUnit())) {
            result = BigDecimal.valueOf(distributorPricePO.getRobotPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } else {
            result = BigDecimal.valueOf(distributorPricePO.getRobotPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        }
        return result;
    }

    @Override
    public Double calculateCsConsoleCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime, Boolean isAddDay) {
        //先确定周期
        int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, isAddDay);
        //获取对应的维护价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到维护的对应语音坐席价格");
        }
        //AI 坐席只有 每年 跟 每月
        double result;
        if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getAgentUnit())) {
            result = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } else {
            result = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        }
        return result;
    }

    @Override
    public Double calculateMultiConcurrencyCost(Double robotCount, Long distributorId, LocalDate startTime, LocalDate endTime, Boolean isAddDay) {
        //先确定周期
        int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, isAddDay);
        //获取对应的维护价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到维护的对应语音坐席价格");
        }
        //AI 坐席只有 每年 跟 每月
        double result;
        if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getMultiConcurrencyUnit())) {
            result = BigDecimal.valueOf(distributorPricePO.getMultiConcurrencyPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        } else {
            result = BigDecimal.valueOf(distributorPricePO.getMultiConcurrencyPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(robotCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
        }
        return result;
    }

    @Override
    public Double calculateFunctionCost(Double objectCount, Long distributorId, Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Boolean isAddDay, LocalDate startTime, LocalDate endTime) {
        //获取代理商维护的价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "代理商未找到维护的对应" + rechargePrestoreStreamFunEnum.getDesc() + "价格");
        }
        //获取用户的AI 坐席
        List<RobotPO> robotList = Collections.EMPTY_LIST;
        //计算消费金额
        DistributorCustomerFunctionCostBO distributorCustomerFunctionCost = DistributorCustomerFunctionCostBO.getSubFactory(rechargePrestoreStreamFunEnum);
        //兼容 代理商绑定话术
        if (RechargePrestoreStreamFunEnum.FUN_DIALOG_FLOW.equals(rechargePrestoreStreamFunEnum)) {
            //计算话术
            DistributorPO distributorPO = distributorService.selectByKey(distributorId);
            return distributorCustomerFunctionCost.calculate(distributorPricePO, distributorPO, robotList, objectCount);
        } else if (RechargePrestoreStreamFunEnum.FUN_MULTI_CONCURRENCY.equals(rechargePrestoreStreamFunEnum)) {
            return distributorCustomerFunctionCost.calculate(distributorPricePO, objectCount, startTime, endTime, isAddDay);
        } else {
            robotList = robotService.selectByTenantId(tenantId);
            TenantPO tenantPO = tenantService.getTenantByTenantId(tenantId);
            robotList = robotList.stream().filter(a -> {
                if (NEW_TENANT_VERSION.equals(tenantPO.getTenantVersion())) {
                    return Objects.equals(a.getTenantAiccPart(), TenantAiccPartEnum.NEW_CALL_OUT_PLATFORM);
                }
                return Objects.equals(a.getTenantAiccPart(), TenantAiccPartEnum.crm_out_call_platform);
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(robotList)) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "用户未开通AI 坐席");
            }
            //由于是异步调用，此时有可能查出来还没commit的数据
            robotList = robotList.stream().filter(robotPO -> Objects.nonNull(robotPO.getRobotId())).collect(Collectors.toList());
            return distributorCustomerFunctionCost.calculate(distributorPricePO, robotList, objectCount, isAddDay);
        }
    }

    @Override
    public double calculateFunctionDelayCost(Double objectCount, Long distributorId, Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long robotId, LocalDate newEndTime, Boolean isAddDay) {
        //获取代理商维护的价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "代理商未找到维护的对应" + rechargePrestoreStreamFunEnum.getDesc() + "价格");
        }
        //获取用户的AI 坐席
        List<RobotPO> robotList = robotService.selectByTenantId(tenantId);
        //计算消费金额
        DistributorCustomerFunctionCostBO distributorCustomerFunctionCost = DistributorCustomerFunctionCostBO.getSubFactory(rechargePrestoreStreamFunEnum);
        if (CollectionUtils.isEmpty(robotList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "用户未开通AI 坐席");
        }
        return distributorCustomerFunctionCost.calculateFunctionDelayCost(distributorPricePO, robotList, objectCount, robotId, newEndTime, isAddDay);
    }

    @Override
    public Double calculateAgentAndAiAssistantCost(Double count, Long distributorId, LocalDate startTime, LocalDate endTime, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        double cost = 0.0;
        //先确定日期
        int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, true);
        //获取代理商维护的价格
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        //计算AI费用
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "代理商未找到维护的对应" + rechargePrestoreStreamFunEnum.getDesc() + "价格");
        }
        switch (rechargePrestoreStreamFunEnum) {
            case FUN_AGENT:
                if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getAgentUnit())) {
                    cost = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(count)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP).doubleValue();
                } else {
                    cost = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(count)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP).doubleValue();
                }
                break;
            case FUN_AI_ASSISTANT:
                if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getAiAssistantUnit())) {
                    cost = BigDecimal.valueOf(distributorPricePO.getAiAssistantPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(count)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
                } else {
                    cost = BigDecimal.valueOf(distributorPricePO.getAiAssistantPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(count)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
                }
                break;
            default:
        }
        return cost;
    }

    @Override
    public List<DistributorCustomerFunctionInfo> getFunctionInfo(Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        List<DistributorCustomerFunctionInfo> list = new ArrayList<>(1);
        DistributorCustomerFunctionInfo distributorCustomerFunctionInfo = new DistributorCustomerFunctionInfo();
        TenantPO tenantPO = tenantService.getTenantByTenantId(tenantId);
        List<RobotPO> robotList = robotService.selectByTenantId(tenantId);
        if (CollectionUtils.isEmpty(robotList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "用户未开通AI 坐席");
        }
        distributorCustomerFunctionInfo.setCompanyName(tenantPO.getCompanyName());
        distributorCustomerFunctionInfo.setLinkman(tenantPO.getLinkman());
        distributorCustomerFunctionInfo.setPhoneNumber(tenantPO.getPhoneNumber());
        distributorCustomerFunctionInfo.setRobotList(robotList);
        distributorCustomerFunctionInfo.setMaxAiLimitDate(robotList.stream().map(RobotPO::getEndTime).max(LocalDate::compareTo).get());
        // 只有 一线多并发/人工座席 AI 助理需要此字段
        if (rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_MULTI_CONCURRENCY)) {
            Integer concurrency = robotService.getDistributorTenantTodayConcurrency(tenantId);
            distributorCustomerFunctionInfo.setOpedFunctionCount(concurrency);
        } else if (rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_AGENT)) {
            //人工坐席包含 赠送+购买  赠送的数量与ai坐席一致
            int AgentResult = getAllAgentCount(tenantId);
            distributorCustomerFunctionInfo.setOpedFunctionCount(AgentResult);
        } else if (rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_AI_ASSISTANT)) {
            int AiAssistantResult = getAllAiAssistantCount(tenantId);
            distributorCustomerFunctionInfo.setOpedFunctionCount(AiAssistantResult);
        }
        list.add(distributorCustomerFunctionInfo);
        return list;
    }

    private int getAllAiAssistantCount(Long tenantId) {
        //没过期
	    return tenantAiAssistantCountService.countByCondition(tenantId);
    }


    @Override
    public int getAllAgentCount(Long tenantId) {
        // 购买的语音坐席的数量
        Integer freeCount = robotService.getValidCountByPart(tenantId, TenantAiccPartEnum.voice_cs_console);
        // 购买的且没过期的坐席数量
        Integer payCount = tenantUngivedCsCountService.countByCondition(tenantId);
        return freeCount + payCount;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void openFunction(Long distributorId, Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long userId, LocalDate startTime, LocalDate endTime) {
        //open api 扩展2种类型
        //OEM 易赚 开通的AI外呼  客服工作台 进行处理，之前的老的开通功能不包含
        if (Objects.equals(rechargePrestoreStreamFunEnum, RechargePrestoreStreamFunEnum.FUN_AI) ||
                Objects.equals(rechargePrestoreStreamFunEnum, RechargePrestoreStreamFunEnum.FUN_VOICE_CS_CONSOLE)) {
            doWithOEM(tenantId, objectCount, rechargePrestoreStreamFunEnum, startTime, endTime);
        }
        //添加预存款账户流水
        addCustomerPrestoreStream(distributorId, tenantId, objectCount, rechargePrestoreStreamFunEnum, userId, startTime, endTime);
        //代理商 账户金额跟新
        distributorService.handDistributor(distributorId, tenantId, objectCount, rechargePrestoreStreamFunEnum, userId, startTime, endTime);
        //客户功能开通
        tenantService.openTenantFunction(distributorId, tenantId, objectCount, rechargePrestoreStreamFunEnum, userId);
        //保存到distributor_customer_fun_time  过期了需要清空 功能数
//        save2DistributorCustomerFunTime(tenantId,rechargePrestoreStreamFunEnum,userId,endTime,Objects.nonNull(objectCount)?objectCount:0);
        //保存到robot
        save2Robot(tenantId, rechargePrestoreStreamFunEnum, userId, startTime, endTime, Objects.nonNull(objectCount) ? objectCount : 0);
        //删除redis缓存
        deleteCacheFromRedis(tenantId);
    }

    private void save2Robot(Long tenantId, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long userId, LocalDate startTime, LocalDate endTime, Double objectCount) {
        //目前只有 一线多并发
        if (Objects.equals(RechargePrestoreStreamFunEnum.FUN_MULTI_CONCURRENCY, rechargePrestoreStreamFunEnum)) {
            RobotPO robotPO = new RobotPO();
            robotPO.setTenantAiccPart(TenantAiccPartEnum.FUN_MULTI_CONCURRENCY);
            robotPO.setStartTime(startTime);
            robotPO.setEndTime(endTime);
            robotPO.setTenantId(tenantId);
            robotPO.setCount(objectCount.intValue());
            robotPO.setCreateUserId(userId);
            robotService.saveNotNull(robotPO);
        }
    }

    private void doWithOEM(Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, LocalDate startTime, LocalDate endTime) {
        RobotPO robotPO = new RobotPO();
        robotPO.setStartTime(startTime);
        robotPO.setEndTime(endTime);
        robotPO.setTenantId(tenantId);
        robotPO.setCount(objectCount.intValue());
        TenantAiccPartEnum tenantAiccPartEnum = TenantAiccPartEnum.crm_out_call_platform;
        if (Objects.equals(rechargePrestoreStreamFunEnum, RechargePrestoreStreamFunEnum.FUN_VOICE_CS_CONSOLE)) {
            tenantAiccPartEnum = TenantAiccPartEnum.voice_cs_console;
        }
        robotPO.setTenantAiccPart(tenantAiccPartEnum);
        robotService.saveNotNull(robotPO);
    }

    private void addCustomerPrestoreStream(Long distributorId, Long tenantId, Double objectCount, RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum, Long userId, LocalDate startTime, LocalDate endTime) {
        //代理商预存款流水
        RechargePrestoreStreamPO rechargePrestoreStreamPO = new RechargePrestoreStreamPO();
        rechargePrestoreStreamPO.setTenantId(tenantId);
        rechargePrestoreStreamPO.setDistributorId(distributorId);
        rechargePrestoreStreamPO.setTargetId(tenantId);
        rechargePrestoreStreamPO.setTargetType(TargetTypeEnum.DISTRIBUTOR);
        TenantPO tenantPO = tenantService.getTenantByTenantId(tenantId);
        rechargePrestoreStreamPO.setTenantName(tenantPO.getCompanyName());
        rechargePrestoreStreamPO.setPhoneNumber(tenantPO.getPhoneNumber());
        rechargePrestoreStreamPO.setTenantLinkman(tenantPO.getLinkman());
        rechargePrestoreStreamPO.setHandleType(RechargePrestoreStreamHandEnum.CONSUME_OPEN_FUNCTION);
        rechargePrestoreStreamPO.setFunType(rechargePrestoreStreamFunEnum);
        DistributorPricePO distributorPricePO = distributorPriceService.selectOneByDisId(distributorId);
        if (null == distributorPricePO) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "未找到代理商维护的" + rechargePrestoreStreamFunEnum.getDesc() + "价格！");
        }
        DistributorCustomerFunctionCostBO.setRechargePrestorePram(rechargePrestoreStreamFunEnum, rechargePrestoreStreamPO, distributorPricePO);
        if (rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_QC)) {
            //qc时候 单价设置是当时设置价格
            rechargePrestoreStreamPO.setFunPrice(objectCount == null ? 0 : BigDecimal.valueOf(objectCount).multiply(BigDecimal.valueOf(1000)).doubleValue());
        }
        if (!rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_QC) && !rechargePrestoreStreamFunEnum.equals(RechargePrestoreStreamFunEnum.FUN_TTS_COMPOSE)) {
            rechargePrestoreStreamPO.setFunCount(objectCount.longValue());
        }
        rechargePrestoreStreamPO.setRemark(rechargePrestoreStreamFunEnum.getDesc() + "功能开通");
        rechargePrestoreStreamPO.setCreateUserId(userId);
        DistributorPO distributorPO = distributorService.selectByKey(distributorId);
        List<RobotPO> robotList = robotService.selectByTenantId(tenantId);
        AtomicDouble costAll = new AtomicDouble(0.0);
        if (CollectionUtils.isEmpty(robotList) && !Objects.equals(RechargePrestoreStreamFunEnum.FUN_AGENT, rechargePrestoreStreamFunEnum)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "客户未开通AI坐席！");
        }
        //如果是 TTS 可能有多条  ，其他都一条流水
        if (RechargePrestoreStreamFunEnum.FUN_TTS_COMPOSE.equals(rechargePrestoreStreamFunEnum)) {
            robotList = robotList.stream().filter(a -> Objects.equals(a.getTenantAiccPart(), TenantAiccPartEnum.crm_out_call_platform)).collect(Collectors.toList());
            robotList.forEach(robotPO -> {
                rechargePrestoreStreamPO.setRechargePrestoreStreamId(null);
                int days = DistributorCustomerFunctionCostBO.getDay(robotPO.getStartTime(), robotPO.getEndTime(), true);
                if (Objects.equals(0, days)) {
                    return;
                }
                rechargePrestoreStreamPO.setFunCount(robotPO.getCount().longValue());
                rechargePrestoreStreamPO.setFunDays(days);
                Double fare = distributorCustomerService.calculateFunctionCost(objectCount == null ? 0 : objectCount, distributorId, tenantId, rechargePrestoreStreamFunEnum, true, startTime, endTime);
                rechargePrestoreStreamPO.setFare(fare);
                costAll.addAndGet(fare);
                rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare() - costAll.doubleValue());
                rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
                rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, fare, 0d);
            });
        } else if (RechargePrestoreStreamFunEnum.FUN_AGENT.equals(rechargePrestoreStreamFunEnum)) {
            //先确定日期
            int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, true);
            rechargePrestoreStreamPO.setFunDays(days);
            double fare;
            if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getAgentUnit())) {
                fare = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP).doubleValue();
            } else {
                fare = BigDecimal.valueOf(distributorPricePO.getAgentPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }
            rechargePrestoreStreamPO.setFare(fare);
            rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare() - fare);
            rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, fare, 0d);
        } else if (RechargePrestoreStreamFunEnum.FUN_AI_ASSISTANT.equals(rechargePrestoreStreamFunEnum)) {
            //先确定日期
            int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, true);
            rechargePrestoreStreamPO.setFunDays(days);
            double fare;
            if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getAiAssistantUnit())) {
                fare = BigDecimal.valueOf(distributorPricePO.getAiAssistantPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
            } else {
                fare = BigDecimal.valueOf(distributorPricePO.getAiAssistantPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
            }
            rechargePrestoreStreamPO.setFare(fare);
            rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare() - fare);
            rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, fare, 0d);
        } else if (RechargePrestoreStreamFunEnum.FUN_MULTI_CONCURRENCY.equals(rechargePrestoreStreamFunEnum)) {
            //先确定日期
            int days = DistributorCustomerFunctionCostBO.getDay(startTime, endTime, true);
            rechargePrestoreStreamPO.setFunDays(days);
            double fare;
            if (DistributorPriceUnitEnum.YEARLY.equals(distributorPricePO.getMultiConcurrencyUnit())) {
                fare = BigDecimal.valueOf(distributorPricePO.getMultiConcurrencyPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(365), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
            } else {
                fare = BigDecimal.valueOf(distributorPricePO.getMultiConcurrencyPrice()).multiply(BigDecimal.valueOf(days)).multiply(BigDecimal.valueOf(objectCount)).divide(BigDecimal.valueOf(31), 3, RoundingMode.HALF_UP).setScale(3, RoundingMode.HALF_UP).doubleValue();
            }
            rechargePrestoreStreamPO.setFare(fare);
            rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare() - fare);
            rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, fare, 0d);
        } else {
            if (!CollectionUtils.isEmpty(robotList)) {
                //查询服务时间
                Optional<LocalDate> minStartTime = robotList.stream().map(RobotPO::getStartTime).min(LocalDate::compareTo);
                Optional<LocalDate> maxEndTime = robotList.stream().map(RobotPO::getEndTime).max(LocalDate::compareTo);
                //先确定日期
                int days = DistributorCustomerFunctionCostBO.getDay(minStartTime.get(), maxEndTime.get(), true);
                rechargePrestoreStreamPO.setFunDays(days);
            }
            Double fare = distributorCustomerService.calculateFunctionCost(objectCount == null ? 0 : objectCount, distributorId, tenantId, rechargePrestoreStreamFunEnum, true, startTime, endTime);
            rechargePrestoreStreamPO.setFare(fare);
            rechargePrestoreStreamPO.setPrestoreFare(distributorPO.getPrestoreFare() - fare);
            rechargePrestoreStreamService.saveNotNull(rechargePrestoreStreamPO);
            rechargePrestoreStreamService.saveDistributorPrestoreStatsToMongo(distributorId, 0d, fare, 0d);
        }
    }
}
