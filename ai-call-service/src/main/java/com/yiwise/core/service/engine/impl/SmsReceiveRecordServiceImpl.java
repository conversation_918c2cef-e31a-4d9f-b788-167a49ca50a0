package com.yiwise.core.service.engine.impl;

import com.github.pagehelper.PageHelper;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.entity.dto.SheetInfoDTO;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.dao.SmsReceiveRecordPOMapper;
import com.yiwise.core.dal.entity.SmsReceiveRecordPO;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.sms.*;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.batchjob.BasicBatchService;
import com.yiwise.core.service.engine.SmsJobService;
import com.yiwise.core.service.engine.SmsReceiveRecordService;
import com.yiwise.core.service.header.HeaderService;
import javaslang.Tuple;
import javaslang.Tuple3;
import org.assertj.core.util.Lists;
import org.springframework.batch.core.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.yiwise.core.config.ApplicationConstant.IMPORT_RE_ADD_FILE;
import static com.yiwise.core.config.CommonApplicationConstant.EXPORT_FILE_PREFIX;
import static com.yiwise.core.config.TableUrlConstant.APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_RECEIVE_RECORD;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.EXPORT_SMS_RECEIVE_RECORD;
import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.IMPORT_SMS_RECEIVE_TO_BLACK_LIST;

/**
 * <AUTHOR> @ yiwise . com>
 * @version v0.1 2023/3/2
 * @class <code>SmsReceiveRecordServiceImpl</code>
 * @since JDK1.8
 */
@Service
public class SmsReceiveRecordServiceImpl extends BasicServiceImpl<SmsReceiveRecordPO> implements SmsReceiveRecordService {

    @Resource(name = "smsReceiveRecordExportJob")
    private Job smsReceiveRecordExportJob;

    @Resource(name = "smsReceiveRecordToSmsJob")
    private Job smsReceiveRecordToSmsJob;

    @Resource(name = "smsReceiveRecordToBlackListStep")
    private Job smsReceiveRecordToBlackListStep;

    @Resource
    private HeaderService headerService;

    @Resource
    private BatchJobInQueueService batchJobInQueueService;

    @Resource
    private BasicBatchService basicBatchService;

    @Resource
    private SmsReceiveRecordPOMapper smsReceiveRecordPOMapper;

    @Resource
    private SmsJobService smsJobService;

    @Override
    public PageResultObject<SmsReceiveRecordPO> getSmsReceivePageInfo(SmsReceiveQueryVO smsReceiveQueryVO) {
        PageHelper.startPage(smsReceiveQueryVO.getPageNum(), smsReceiveQueryVO.getPageSize());
        List<SmsReceiveRecordPO> smsReceiveRecordPOList = smsReceiveRecordPOMapper.getSmsReceivePageInfo(smsReceiveQueryVO);
        return PageResultObject.of(smsReceiveRecordPOList);
    }

    @Override
    public JobStartResultVO export(SmsReceiveRecordVO exportVO, Long tenantId, Long userId) {
        List<Tuple3<String, String, Object>> params = Lists.newArrayList(
                Tuple.of("EXPORT_REQUEST", "Object", exportVO)
        );
        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_RECEIVE_RECORD, PlatformTypeEnum.AICC);
        ArrayList<SheetInfoDTO> sheetInfoDTOS = Lists.newArrayList(SheetInfoDTO.of(BatchConstant.SheetName.EXPORT_SMS_RECEIVE_RECORD, headerListCache));
        return basicBatchService.exportWithQuery(DataSourceEnum.MASTER, tenantId, userId, SystemEnum.SMS_PLATFORM, smsReceiveRecordExportJob, EXPORT_SMS_RECEIVE_RECORD, params, sheetInfoDTOS);
    }

    @Override
    public List<SmsReceiveRecordPO> getSmsReceiveListByIds(List<Long> idList, SmsReceiveRecordVO exportVO) {
        PageHelper.startPage(exportVO.getPageNum(), exportVO.getPageSize());
        return smsReceiveRecordPOMapper.getSmsReceiveListByIds(idList, exportVO);
    }

    @Override
    public JobStartResultVO importToSmsJob(SmsReceiveRecordVO smsReceiveRecordVO, Long tenantId, Long userId) {
        SystemEnum systemEnum = smsReceiveRecordVO.getSystemType();
        if (systemEnum == null) {
            systemEnum = SystemEnum.CRM;
        }
        SmsJobDetailInfoVO targetJob = smsJobService.getSmsJobInfo(tenantId, smsReceiveRecordVO.getTargetJobId());
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();

        List<String> headerListCache = headerService.getHeaderListCache(APIENGINE_SPRINGBATCHJOB_EXPORT_SMS_RECEIVE_RECORD, PlatformTypeEnum.AICC);

        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addLong("SMS_JOB_ID", smsReceiveRecordVO.getTargetJobId());
        jobParametersBuilder.addString("SMS_RECEIVE_ID_LIST", JsonUtils.object2String(smsReceiveRecordVO.getIds()));
        jobParametersBuilder.addString("EXPORT_REQUEST", JsonUtils.object2String(smsReceiveRecordVO));
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(IMPORT_RE_ADD_FILE, tenantId, userId, Long.toString(System.currentTimeMillis()));
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        jobParametersBuilder.addString("ERROR_FILE_PATH", TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey));
        jobParametersBuilder.addString("SYSTEM_TYPE", systemEnum.name());
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.MASTER.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(headerListCache));
        jobParametersBuilder.addString("PROPERTY_MAP", JsonUtils.object2String(smsReceiveRecordVO.getPropertiesMap()));
        Integer totalCount;
        if (smsReceiveRecordVO.getSelectAll()) {
            totalCount = smsReceiveRecordPOMapper.getRecordCount(tenantId, smsReceiveRecordVO);
        } else {
            totalCount = smsReceiveRecordVO.getIds().size();
        }

        JobParameters jobParameters = jobParametersBuilder.toJobParameters();
        Job job = smsReceiveRecordToSmsJob;

        String jobName = "回复短信历史导入到短信平台-短信任务 " + targetJob.getSmsJob().getName();
        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.IMPORT_SMS_RECEIVE_TO_SMS_JOB;
        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        job,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        systemEnum,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public JobStartResultVO importToBlackList(SmsReceiveRecordVO smsReceiveRecordVO, Long tenantId, Long userId) {
        JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
        Long now = System.currentTimeMillis();
        jobParametersBuilder.addString("DATASOURCE_TYPE", DataSourceEnum.SLAVE.name());
        jobParametersBuilder.addLong("TIME", now);
        jobParametersBuilder.addLong("TENANT_ID", tenantId);
        jobParametersBuilder.addLong("CURRENT_USER_ID", userId);
        jobParametersBuilder.addString("SYSTEM_TYPE", SystemEnum.SMS_PLATFORM.name());
        jobParametersBuilder.addString("HEADER_LIST", JsonUtils.object2String(BatchConstant.CRM_BATCH_IMPORT_WHITE_HEADER_LIST));
        SpringBatchJobTypeEnum jobType = IMPORT_SMS_RECEIVE_TO_BLACK_LIST;

        Integer totalCount = smsReceiveRecordPOMapper.getRecordCount(tenantId, smsReceiveRecordVO);
        String baseName = String.valueOf(now);
        jobParametersBuilder.addLong("WHITE_GROUP_ID", smsReceiveRecordVO.getTargetJobId());
        String exportFileOssKey = OssKeyCenter.getExcelOssFileKey(EXPORT_FILE_PREFIX, tenantId, userId, baseName);
        jobParametersBuilder.addString("OSS_FILE_KEY", exportFileOssKey);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(exportFileOssKey);
        jobParametersBuilder.addString("EXPORT_FILE_PATH", exportFilePath);
        String customerPersonExportRequestVOString = JsonUtils.object2String(smsReceiveRecordVO);
        jobParametersBuilder.addString("EXPORT_REQUEST", customerPersonExportRequestVOString);

        JobParameters jobParameters = jobParametersBuilder.toJobParameters();

        String jobName = "批量加入黑名单";

        SpringBatchJobBO springBatchJobBO =
                new SpringBatchJobBO(
                        smsReceiveRecordToBlackListStep,
                        jobParameters,
                        jobName,
                        tenantId,
                        null,
                        totalCount,
                        userId,
                        jobType,
                        SystemEnum.SMS_PLATFORM,
                        false);
        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    @Override
    public void addSmsReceive(SmsReceiveRecordPO param) {
        saveNotNull(param);
    }

    @Override
    public PageResultObject<SmsReceiveRecordPO> getSmsReceivePageInfoLimit(SmsReceiveQueryVO smsReceiveQueryVO) {
        Integer start = (smsReceiveQueryVO.getPageNum() - 1) * smsReceiveQueryVO.getPageSize();
        Integer end = smsReceiveQueryVO.getPageSize();
        List<SmsReceiveRecordPO> smsReceiveRecordPOList = smsReceiveRecordPOMapper.getSmsReceivePageInfoLimit(smsReceiveQueryVO,start,end);
        return PageResultObject.of(smsReceiveRecordPOList);
    }

    @Override
    public Long getSmsReceivePageInfoCount(SmsReceiveQueryVO smsReceiveQueryVO) {
        return smsReceiveRecordPOMapper.getSmsReceivePageInfoCount(smsReceiveQueryVO);
    }
}
