package com.yiwise.core.service.ope.stats.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.google.common.collect.Lists;
import com.yiwise.account.api.dto.UserDetailDTO;
import com.yiwise.aicc.common.enums.calloutjob.CallOutJobStatusEnum;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.encrypt.Md5Utils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.calloutjob.api.dto.CallJobMonitorHeaderDTO;
import com.yiwise.calloutjob.api.dto.CallOutJobRequestDTO;
import com.yiwise.calloutjob.api.vo.*;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.feignclient.callout.*;
import com.yiwise.core.feignclient.v3bot.V3BotClient;
import com.yiwise.core.model.alertmessage.MonitorWarnMsg;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.request.NewCallJobMonitorRequest;
import com.yiwise.core.model.vo.NewMonitorIndexVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.ope.CallJobDataInfo;
import com.yiwise.core.model.vo.ope.DialogBusiMetricsDataInfo;
import com.yiwise.core.model.vo.ope.RealTimeCallJobDataInfo;
import com.yiwise.core.service.alertmessage.SimpleAlertMessageSenderStrategy;
import com.yiwise.core.service.ope.monitor.impl.NewWarningConfigService;
import com.yiwise.core.service.ope.monitor.impl.NewWarningHistoryService;
import com.yiwise.core.service.ope.stats.NewCallJobWholeMonitorService;
import com.yiwise.core.service.platform.*;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.dialogflow.api.dto.request.BotSearchRequest;
import com.yiwise.dialogflow.api.dto.response.SimpleBotInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yiwise.core.util.ObjUtil.isWithinRange;

/**
 * 新版外呼任务实时监控
 * Create DateTime: 2024/9/12 18:50
 *
 * <AUTHOR>
 **/
@Service
@Slf4j
public class NewCallJobWholeMonitorServiceImpl implements NewCallJobWholeMonitorService {

    private static final DateTimeFormatter localDateTimeMinuteFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Resource
    private CallOutStatsApiClient callOutStatsApiClient;

    @Resource
    private CallOutJobClient callOutJobClient;

    @Resource
    private RoleService roleService;

    @Resource
    private UserService userService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private V3BotClient v3BotClient;

    @Resource
    private NewWarningConfigService newWarningConfigService;

    @Resource
    private SimpleAlertMessageSenderStrategy simpleAlertMessageSenderStrategy;

    @Resource
    private NewWarningHistoryService newWarningHistoryService;

    @Resource
    private CallOutJobWarningClient callOutJobWarningClient;
    @Resource
    private RedisOpsService redisOpsService;

    @Override
    public CallJobMonitorHeaderVO getCallMonitorHeader(Long userId) {
        CallJobMonitorHeaderDTO callMonitorHeader = callOutStatsApiClient.getCallMonitorHeader(userId);
        return MyBeanUtils.copy(callMonitorHeader, CallJobMonitorHeaderVO.class);
    }

    @Override
    public void saveCallJobMonitorHeader(CallJobMonitorHeaderVO callJobMonitorHeaderVO) {
        CallJobMonitorHeaderDTO copy = MyBeanUtils.copy(callJobMonitorHeaderVO, CallJobMonitorHeaderDTO.class);
        callOutStatsApiClient.saveCallJobMonitorHeader(copy);
    }

    @Override
    public PageResultObject<NewCallJobMonitorResultVO> getNewCallJobWholeMonitor(NewCallJobMonitorRequest request) {
        if (CollectionUtils.isEmpty(request.getRobotCallJobStatusList()) || !dealQueryAuth(request)) {
            return PageResultObject.of(Collections.emptyList());
        }
        CallJobMonitorRequest callJobMonitorRequest = MyBeanUtils.copy(request, CallJobMonitorRequest.class);
        if (Objects.nonNull(request.getStartDate()) && Objects.nonNull(request.getEndDate())) {
            callJobMonitorRequest.setStartTime(request.getStartDate().atStartOfDay());
            callJobMonitorRequest.setEndTime(request.getEndDate().atTime(23, 59, 59));
        }
        if (BooleanUtils.isTrue(callJobMonitorRequest.getWarning())) {
            callJobMonitorRequest.setWarningNum(1);
        }
        PageResultObject<CallJobMonitorInfoVO> pageResult = callOutStatsApiClient.pageCallJobMonitorInfo(callJobMonitorRequest);
        List<CallJobMonitorInfoVO> content = pageResult.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return PageResultObject.of(Lists.newArrayList());
        }
        List<NewCallJobMonitorResultVO> resultVOList = JSON.parseArray(JSON.toJSONString(content), NewCallJobMonitorResultVO.class);
        //处理预警字段
        dealCallJobWaring(resultVOList, CallOutJobTypeEnum.JOB_MONITOR);
        return PageResultObject.of(resultVOList, pageResult.getNumber(), pageResult.getPageSize(), (int) pageResult.getTotalElements());
    }

    @Override
    public PageResultObject<NewCallJobMonitorResultVO> getDialogCallOutDataReport(NewCallJobMonitorRequest request) {
        if (CollectionUtils.isEmpty(request.getRobotCallJobStatusList()) || !dealQueryAuth(request)) {
            return PageResultObject.of(Collections.emptyList());
        }
        CallJobMonitorRequest callJobMonitorRequest = MyBeanUtils.copy(request, CallJobMonitorRequest.class);
        if (Objects.nonNull(request.getStartDate()) && Objects.nonNull(request.getEndDate())) {
            callJobMonitorRequest.setStartTime(request.getStartDate().atStartOfDay());
            callJobMonitorRequest.setEndTime(request.getEndDate().atTime(23, 59, 59));
        }
        if (BooleanUtils.isTrue(callJobMonitorRequest.getWarning())) {
            callJobMonitorRequest.setWarningNum(2);
        }
        PageResultObject<CallJobMonitorInfoVO> pageResult = callOutStatsApiClient.pageCallOutJobDialogMonitorInfo(callJobMonitorRequest);
        List<CallJobMonitorInfoVO> content = pageResult.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return PageResultObject.of(Lists.newArrayList());
        }
        List<NewCallJobMonitorResultVO> resultVOList = JSON.parseArray(JSON.toJSONString(content), NewCallJobMonitorResultVO.class);
        //处理预警字段
        dealCallJobWaring(resultVOList, CallOutJobTypeEnum.DIALOG_MONITOR);
        return PageResultObject.of(resultVOList, pageResult.getNumber(), pageResult.getPageSize(), (int) pageResult.getTotalElements());
    }

    @Override
    public List<CallOutJobSimpleInfoVO> getCallOutJobSimpleInfo(OpeQueryCallOutInfoVO opeQueryCallOutInfoVO) {
        return callOutJobClient.getCallOutJobSimpleInfo(opeQueryCallOutInfoVO);
    }

    @Override
    public PageResultObject<SimpleBotInfo> selectDialogFlow(String searchWord,
                                                            Integer pageNum,
                                                            Integer pageSize) {
        BotSearchRequest request = new BotSearchRequest();
        request.setSearch(searchWord);
        request.setContainsMagicBot(true);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        return v3BotClient.searchBot(request);
    }

    @Override
    public void dealWarning() {

        callOutJobWarningClient.resetWarningStatus();

        List<NewWarningConfigPO> newWarningConfigPOS = newWarningConfigService.selectEffectiveWarningConfig();
        if (CollectionUtils.isEmpty(newWarningConfigPOS)) {
            return;
        }
        for (NewWarningConfigPO config : newWarningConfigPOS) {

            log.info("新版外呼预警本次遍历={}", config);

            try {
                if (WarningConfigTypeEnum.JOB_DIALOG.equals(config.getType())) {
                    List<Long> runningRobotCallJobIds;
                    CallOutJobRequestDTO callOutJobRequestDTO = new CallOutJobRequestDTO();
                    if (CollectionUtils.isNotEmpty(config.getRobotCallJobIds())) {
                        //配置了具体的任务
                        callOutJobRequestDTO.setCallOutJobIds(config.getRobotCallJobIds());
                        runningRobotCallJobIds = callOutJobClient.getByIds(callOutJobRequestDTO).stream().filter(po -> CallOutJobStatusEnum.IN_PROCESS.equals(po.getStatus())).map(CallOutJobSimpleInfoVO::getCallOutJobId).collect(Collectors.toList());
                    } else {
                        //客户下的所有任务
                        callOutJobRequestDTO.setTenantId(config.getTenantId());
                        runningRobotCallJobIds = callOutJobClient.getByIdsTenantId(callOutJobRequestDTO).stream().filter(po -> CallOutJobStatusEnum.IN_PROCESS.equals(po.getStatus())).map(CallOutJobSimpleInfoVO::getCallOutJobId).collect(Collectors.toList());
                    }
                    //触发任务预警的任务ID列表
                    List<Long> warningCallJobIds = new CopyOnWriteArrayList<>();
                    //触发话术预警的任务ID列表
                    List<Long> warningDialogCallJobIds = new CopyOnWriteArrayList<>();
                    //处理预警
                    doDealWarning(runningRobotCallJobIds, config, warningCallJobIds, warningDialogCallJobIds);
                    //处理预警 包含非进行中的任务
                    doDealWarningContainsNotRunningNew(config, warningCallJobIds);
                    boolean anyMatch = config.getSpecifiedTime().stream().anyMatch(e -> isWithinRange(e.getLocalTime(), LocalTime.now()));
                    log.info("新版是否需要新增预警次数={} 是否开启={}", anyMatch, config.getSpecifiedFlag());
                    if (CollectionUtils.isNotEmpty(warningCallJobIds) || CollectionUtils.isNotEmpty(warningDialogCallJobIds)) {
                        if (CollectionUtils.isNotEmpty(config.getSpecifiedTime()) && anyMatch && config.getSpecifiedFlag()) {
                            //更新最后预警时间及预警次数
                            newWarningConfigService.updateWarningConfigWarningNum(config.getWarningConfigurationId());
                        }
                        if (!config.getSpecifiedFlag()) {
                            //更新最后预警时间及预警次数
                            newWarningConfigService.updateWarningConfigWarningNum(config.getWarningConfigurationId());
                        }

                    }
                    //更新触发任务预警的任务状态
                    if (CollectionUtils.isNotEmpty(warningCallJobIds)) {
                        callOutJobWarningClient.updateWarningStatusIds(warningCallJobIds, 1);
                    }
                    //更新触发话术预警的任务状态
                    if (CollectionUtils.isNotEmpty(warningDialogCallJobIds)) {
                        callOutJobWarningClient.updateWarningStatusIds(warningDialogCallJobIds, 2);
                    }
                }
            } catch (Exception e) {
                log.error("dealWarning error", e);
            }
        }
    }

    private void doDealWarning(List<Long> runningRobotCallJobIds, NewWarningConfigPO warningConfigPO, List<Long> warningCallJobIds, List<Long> warningDialogCallJobIds) {
        if (CollectionUtils.isEmpty(runningRobotCallJobIds) || (CollectionUtils.isEmpty(warningConfigPO.getWarningRange()) && CollectionUtils.isEmpty(warningConfigPO.getWarningMultipleRange()))) {
            log.info("runningRobotCallJobIds={} warningRange={} warningMultipleRange={}", runningRobotCallJobIds, warningConfigPO.getWarningRange(),warningConfigPO.getWarningMultipleRange());
            return;
        }
        //任务数据
        CallJobMonitorRequest callJobMonitorRequest = new CallJobMonitorRequest();
        callJobMonitorRequest.setWithPage(false);
        callJobMonitorRequest.setRobotCallJobIds(runningRobotCallJobIds);
        if (warningConfigPO.getWarningDate().equals(WarningDateEnum.TODAY)) {
            callJobMonitorRequest.setQueryToday(true);
        }
        PageResultObject<CallJobMonitorInfoVO> pageResult = callOutStatsApiClient.pageCallJobMonitorInfo(callJobMonitorRequest);
        List<CallJobMonitorInfoVO> content = pageResult.getContent();
        List<NewCallJobMonitorResultVO> resultVOList = JSON.parseArray(JSON.toJSONString(content), NewCallJobMonitorResultVO.class);
        log.info("新版获取到外呼任务={}", resultVOList);
        for (NewCallJobMonitorResultVO vo : resultVOList) {
            try {
                if (Objects.isNull(warningConfigPO.getWarningLine()) || Objects.isNull(vo.getCallJobData()) || Objects.isNull(vo.getCallJobData().getCallTaskCompleted()) || vo.getCallJobData().getCallTaskCompleted() < warningConfigPO.getWarningLine()) {
                    return;
                }
                List<NewMonitorIndexVO> monitorList = new ArrayList<>();
                if(MultipleIndexEnum.SINGLE.equals(warningConfigPO.getMultipleIndex())) {
                    monitorList = doDealSingleJob(warningConfigPO, vo, null,warningConfigPO.getWarningRange());
                }else{
                    if(CollectionUtils.isNotEmpty(warningConfigPO.getWarningMultipleRange())){
                        for(List<NewMonitorIndexVO> indexVO : warningConfigPO.getWarningMultipleRange()){
                            //多指标只需要获取最先触发的分组
                            monitorList = doDealSingleJob(warningConfigPO, vo, null, indexVO);
                            if(CollectionUtils.isNotEmpty(monitorList)){
                                break;
                            }
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(monitorList)) {
                    // 新版外呼去除在时间间隔内的预警
                    doCheckLastWarningTime(vo.getCallOutJobId(), warningConfigPO, monitorList);
                    if (monitorList.stream().anyMatch(monitor -> CallOutJobTypeEnum.JOB_MONITOR.equals(monitor.getJobType()))) {
                        warningCallJobIds.add(vo.getCallOutJobId());
                    }
                    if (monitorList.stream().anyMatch(monitor -> CallOutJobTypeEnum.DIALOG_MONITOR.equals(monitor.getJobType()))) {
                        warningDialogCallJobIds.add(vo.getCallOutJobId());
                    }
                    //发送预警消息
                    doSendWarningMessage(warningConfigPO, vo, monitorList);
                    //预警动作执行
                    doWaringAction(vo.getTenantId(), vo.getCallOutJobId(), monitorList);
                }
            } catch (Exception e) {
                log.error("doDealWarning error", e);
            }
        }
    }

    /**
     * 去除不需要预警的规则
     */
    private void doCheckLastWarningTime(Long robotCallJobId, NewWarningConfigPO warningConfigPO,
                                        List<NewMonitorIndexVO> monitorList) {
        if (CollectionUtils.isEmpty(monitorList)) {
            return;
        }
        try {
            boolean checkResult =callOutJobWarningClient.doCheckLastWarningTime(robotCallJobId,warningConfigPO.getWarningConfigurationId(),warningConfigPO.getWarningInterval());
            if(BooleanUtils.isNotTrue(checkResult)){
                log.info("预警在间隔时间内,不执行,warningConfigurationId={}",warningConfigPO.getWarningConfigurationId());
                monitorList.clear();
            }
        } catch (Exception e) {
            log.error("callout job doCheckLastWarningTime error", e);
        }
    }

    /**
     * 预警处理-包含非进行中的任务
     */
    private void doDealWarningContainsNotRunningNew(NewWarningConfigPO warningConfigPO, List<Long> warningCallJobIds) {
        try {
            if (CollectionUtils.isEmpty(warningConfigPO.getWarningRange())) {
                log.info("warningRange is empty");
                return;
            }
            List<NewMonitorIndexVO> warningRange = warningConfigPO.getWarningRange().stream().filter(item -> CollectionUtils.isNotEmpty(item.getIndexTypes()) && item.getIndexTypes().contains(CallOutTotalTypesEnum.NOT_CALLED_NUMBER)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(warningRange)) {
                log.info("warningRange also is empty");
                return;
            }
            if (CollectionUtils.isNotEmpty(warningConfigPO.getRobotCallJobIds())) {
                CallJobMonitorContainsNotRunningRequestVO requestVO = new CallJobMonitorContainsNotRunningRequestVO();
                requestVO.setCallOutJobIds(warningConfigPO.getRobotCallJobIds());
                requestVO.setTenantId(warningConfigPO.getTenantId());
                PageResultObject<CallJobMonitorContainsNotRunningResponseVO> resultObject = callOutStatsApiClient.pageCallJobMonitorContainsNotRunning(requestVO);
                doDealWarningNotRunningPageNew(resultObject.getContent(), warningConfigPO, warningRange, warningCallJobIds);
            } else {
                int pageNum = 1;
                List<CallJobMonitorContainsNotRunningResponseVO> responseVOS;
                do {
                    CallJobMonitorContainsNotRunningRequestVO requestVO = new CallJobMonitorContainsNotRunningRequestVO();
                    requestVO.setTenantId(warningConfigPO.getTenantId());
                    requestVO.setPageNum(pageNum++);
                    requestVO.setPageSize(100);
                    PageResultObject<CallJobMonitorContainsNotRunningResponseVO> resultObject = callOutStatsApiClient.pageCallJobMonitorContainsNotRunning(requestVO);
                    responseVOS = resultObject.getContent();
                    doDealWarningNotRunningPageNew(responseVOS, warningConfigPO, warningRange, warningCallJobIds);
                } while (CollectionUtils.isNotEmpty(responseVOS));
            }
        } catch (Exception e) {
            log.error("doDealWarningContainsNotRunningNew error", e);
        }
    }

    private void doDealWarningNotRunningPageNew(List<CallJobMonitorContainsNotRunningResponseVO> responseVOS, NewWarningConfigPO warningConfigPO, List<NewMonitorIndexVO> warningRange, List<Long> warningCallJobIds) {
        try {
            if (CollectionUtils.isEmpty(responseVOS)) {
                return;
            }
            responseVOS.forEach(responseVO -> {
                long taskTotal = Objects.nonNull(responseVO.getTaskTotal()) ? Math.max(responseVO.getTaskTotal(), 0L) : 0;
                long filteredTask = Objects.nonNull(responseVO.getFilteredTask()) ? Math.max(responseVO.getFilteredTask(), 0) : 0;
                long taskCompleted = Objects.nonNull(responseVO.getTaskCompleted()) ? Math.max(responseVO.getTaskCompleted(), 0) : 0;
                long called = filteredTask + taskCompleted;
                log.info("doDealWarningNotRunningPageNew callOutJobId={} called={}, total={}", responseVO.getCallOutJobId(), called, taskTotal);
                if (Objects.isNull(warningConfigPO.getWarningLine()) || called < warningConfigPO.getWarningLine()) {
                    return;
                }
                List<NewMonitorIndexVO> monitorList = new ArrayList<>();
                for (NewMonitorIndexVO monitor : warningRange) {
                    String value = String.valueOf(taskTotal - called);
                    log.info("doDealWarningNotRunningPageNew doDealWarningNotRunningPageNew thresholdCompare IndexName={} IndexType={} value={} threshold={} numType={}", monitor.getIndexName().getDesc(), monitor.getIndexTypes().get(0).getDesc(), value, monitor.getNumber(), monitor.getNumType());
                    if (thresholdCompare(value, monitor.getNumber(), monitor.getNumType())) {
                        monitor.setWarnValue(value);
                        //添加预警信息
                        monitorList.add(monitor);
                    } else if (Objects.equals(monitor.getOperator(), WarningOperatorEnum.AND)) {
                        //如果是AND关系，有一个不满足就全部不满足
                        monitorList.clear();
                        break;
                    }
                }
                log.info("doDealWarningNotRunningPageNew monitorList={}", monitorList);
                //去除在时间间隔内的预警
                doCheckLastWarningTime(responseVO.getCallOutJobId(), warningConfigPO, monitorList);
                if (CollectionUtils.isEmpty(monitorList)) {
                    return;
                }
                warningCallJobIds.add(responseVO.getCallOutJobId());
                //发送预警消息
                CallJobDataInfo callJobDataInfo = new CallJobDataInfo();
                callJobDataInfo.setCallOutJobId(responseVO.getCallOutJobId());
                callJobDataInfo.setCallOutJobName(responseVO.getCallOutJobName());
                NewCallJobMonitorResultVO resultVO = new NewCallJobMonitorResultVO();
                resultVO.setCallOutJobId(responseVO.getCallOutJobId());
                resultVO.setCallJobData(callJobDataInfo);
                doSendWarningMessage(warningConfigPO, resultVO, monitorList);
                //预警动作执行
                doWaringAction(warningConfigPO.getTenantId(), responseVO.getCallOutJobId(), monitorList);
            });
        } catch (Exception e) {
            log.error("doDealWarningNotRunningPageNew error", e);
        }
    }

    /**
     * 处理查询数据权限
     */
    public boolean dealQueryAuth(CallJobMonitorAuthQueryVO request) {
        if (roleService.hasAuthResource(request.getCurrentUserId(), SystemEnum.OPE, AuthResourceUriEnum.ope_customer_realtime_monitor_company)) {
            return true;
        }
        if (CollectionUtils.isEmpty(request.getCsmUserIds())) {
            request.setCsmUserIds(getAuthCsmUserIds(request.getCurrentUserId()));
        }
        if (CollectionUtils.isEmpty(request.getAitUserIds())) {
            request.setAitUserIds(getAuthAitUserIds(request.getCurrentUserId()));
        }
        return !CollectionUtils.isEmpty(request.getCsmUserIds()) || !CollectionUtils.isEmpty(request.getAitUserIds());
    }

    /**
     * 查询有权限的csm用户ID列表
     */
    private List<Long> getAuthCsmUserIds(Long userId) {
        RolePO rolePO = roleService.selectByUserIdAndSystem(userId, SystemEnum.OPE);
        List<Long> csmRoleIdList = roleService.selectCsmRoleIds();
        if (Objects.nonNull(rolePO) && csmRoleIdList.contains(rolePO.getRoleId())) {
            if (roleService.hasAuthResource(userId, SystemEnum.OPE, AuthResourceUriEnum.ope_customer_realtime_monitor_group)) {
                //所在组及下级组权限
                Set<Long> userIds = userService.getOpeChildUserIdByParentUserId(userId);
                List<UserRolePO> userRoleList = userRoleService.selectByUserIdsAndSystemType(new ArrayList<>(userIds), SystemEnum.OPE);
                return userRoleList.stream().filter(userRolePO -> csmRoleIdList.contains(userRolePO.getRoleId())).map(UserRolePO::getUserId).collect(Collectors.toList());
            } else {
                //我自己的数据权限
                return Collections.singletonList(userId);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 查询有权限的AIT用户ID列表
     */
    private List<Long> getAuthAitUserIds(Long userId) {
        RolePO rolePO = roleService.selectByUserIdAndSystem(userId, SystemEnum.OPE);
        if (Objects.nonNull(rolePO) && CommonApplicationConstant.OPE_IMPLEMENT_ROLE_IDS.contains(rolePO.getRoleId())) {
            if (roleService.hasAuthResource(userId, SystemEnum.OPE, AuthResourceUriEnum.ope_customer_realtime_monitor_group)) {
                //所在组及下级组权限
                Set<Long> userIds = userService.getOpeChildUserIdByParentUserId(userId);
                List<UserRolePO> userRoleList = userRoleService.selectByUserIdsAndSystemType(new ArrayList<>(userIds), SystemEnum.OPE);
                return userRoleList.stream().filter(userRolePO -> CommonApplicationConstant.OPE_IMPLEMENT_ROLE_IDS.contains(userRolePO.getRoleId())).map(UserRolePO::getUserId).collect(Collectors.toList());
            } else {
                //我自己的数据权限
                return Collections.singletonList(userId);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 处理监控预警信息
     */
    private void dealCallJobWaring(List<NewCallJobMonitorResultVO> callJobMonitorResultVOS, CallOutJobTypeEnum jobTypeEnum) {
        List<Long> runningRobotCallJobIds = callJobMonitorResultVOS.stream().filter(vo -> CallOutJobStatusEnum.IN_PROCESS.equals(vo.getCallJobData().getStatus())).map(NewCallJobMonitorResultVO::getCallOutJobId).collect(Collectors.toList());
        List<NewWarningConfigPO> warningConfigPOList = newWarningConfigService.selectByRobotCallJobIds(runningRobotCallJobIds);
        if (CollectionUtils.isEmpty(warningConfigPOList)) {
            return;
        }
        callJobMonitorResultVOS.parallelStream().forEach(vo -> {
            List<String> warningFields = new ArrayList<>();
            if (CallOutJobStatusEnum.IN_PROCESS.equals(vo.getCallJobData().getStatus())) {
                warningConfigPOList.stream().filter(config -> config.getRobotCallJobIds().contains(vo.getCallOutJobId()))
                        .forEach(config -> {
                            List<NewMonitorIndexVO> monitorIndexVOS = new ArrayList<>();
                            if(MultipleIndexEnum.SINGLE.equals(config.getMultipleIndex())) {
                                monitorIndexVOS = doDealSingleJob(config, vo, null,config.getWarningRange());
                            }else{
                                if(CollectionUtils.isNotEmpty(config.getWarningMultipleRange())){
                                    for(List<NewMonitorIndexVO> indexVO : config.getWarningMultipleRange()){
                                        monitorIndexVOS = doDealSingleJob(config, vo, null, indexVO);
                                        if(CollectionUtils.isNotEmpty(monitorIndexVOS)){
                                            break;
                                        }
                                    }
                                }
                            }
                            if (CollectionUtils.isNotEmpty(monitorIndexVOS)) {
                                List<String> filedNameList = monitorIndexVOS.stream().flatMap(monitor -> monitor.getIndexTypes().stream()).map(CallOutTotalTypesEnum::getFieldName).collect(Collectors.toList());
                                warningFields.addAll(filedNameList);
                            }
                        });
            }
            vo.setWarningFields(warningFields);
        });
    }

    /**
     * 处理单个任务的预警
     */
    private List<NewMonitorIndexVO> doDealSingleJob(NewWarningConfigPO warningConfigPO, NewCallJobMonitorResultVO callJob,
                                                    CallOutJobTypeEnum jobTypeEnum,List<NewMonitorIndexVO> warningRange) {
        if (Objects.nonNull(jobTypeEnum)) {
            //只处理任务或者话术
            warningRange = warningConfigPO.getWarningRange().stream().filter(monitor -> jobTypeEnum.equals(monitor.getJobType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(warningRange)) {
            return Collections.emptyList();
        }
        if (newWarningConfigService.skipProcessV2(warningRange)) {
            // 不是旧版外呼关心的指标, 跳过
            return Collections.emptyList();
        }
        List<NewMonitorIndexVO> monitorList = new ArrayList<>();
        for (NewMonitorIndexVO monitor : warningRange) {
            List<CallOutTotalTypesEnum> indexTypes = monitor.getIndexTypes();
            if (CollectionUtils.isEmpty(indexTypes)) {
                continue;
            }
//	        if (newWarningConfigService.skipProcess(monitor)) {
//		        // 不是旧版外呼关心的指标, 跳过
//		        continue;
//	        }
            String value = "";
            if (CallOutJobTypeEnum.JOB_MONITOR.equals(monitor.getJobType())) {
                Map<CallOutTotalTypesEnum, String> valueMap = getValueMap(callJob);
                //任务预警
                if (CallOutIndexTypeEnum.CALLING_STATUS.equals(monitor.getIndexName())) {
                    //通话状态
                    Long sum = 0L;
                    for (CallOutTotalTypesEnum indexType : indexTypes) {
                        Long num = callJob.getDialStatusCount().get(indexType.name());
                        if (Objects.nonNull(num)) {
                            sum += num;
                        }
                    }
                    value = String.valueOf(calculateRate(sum, getCallTaskCompletedWithoutFilter(callJob)));
                } else if (CallOutIndexTypeEnum.PURPOSE_STATUS.equals(monitor.getIndexName())) {
                    //意向状态
                    Long sum = 0L;
                    for (CallOutTotalTypesEnum indexType : indexTypes) {
                        Long num = callJob.getIntentLevelCodeCountMap().get(indexType.getCode());
                        if (Objects.nonNull(num)) {
                            sum += num;
                        }
                    }
                    value = String.valueOf(calculateRate(sum, getAnsweredCall(callJob)));
                } else {
                    // 其他指标是单个维度
                    value = valueMap.get(monitor.getIndexTypes().get(0));
                }
            } else if (CallOutJobTypeEnum.DIALOG_MONITOR.equals(monitor.getJobType())) {
                //话术预警
                if (Objects.nonNull(callJob.getDialogStats())) {
                    Map<CallOutTotalTypesEnum, String> valueMap = getValueMap(callJob.getRealTimeData(), callJob.getDialogStats());
                    value = valueMap.get(monitor.getIndexTypes().get(0));
                }
            }
            log.info("doDealSingleJob thresholdCompare IndexName={} IndexType={} value={} threshold={} numType={}", monitor.getIndexName().getDesc(), monitor.getIndexTypes().get(0).getDesc(), value, monitor.getNumber(), monitor.getNumType());
            if (thresholdCompare(value, monitor.getNumber(), monitor.getNumType())) {
                monitor.setWarnValue(String.valueOf(value));
                //添加预警信息
                monitorList.add(monitor);
            } else if (Objects.equals(monitor.getOperator(), WarningOperatorEnum.AND)) {
                //如果是AND关系，有一个不满足就全部不满足
                monitorList.clear();
                break;
            }
        }
        log.info("doDealSingleJob monitorList={}", monitorList);
        return monitorList;
    }

    /**
     * 发送预警
     */
    private void doSendWarningMessage(NewWarningConfigPO warningConfigPO, NewCallJobMonitorResultVO callJob, List<NewMonitorIndexVO> monitorList) {
        if (CollectionUtils.isEmpty(monitorList) || CollectionUtils.isEmpty(warningConfigPO.getWarningObject())) {
            log.info("monitorList={} warningObject={}", monitorList, warningConfigPO.getWarningObject());
            return;
        }
        log.info("新版预警指定的时间={} 是否开启={}", warningConfigPO.getSpecifiedTime(), warningConfigPO.getSpecifiedFlag());
        // 判断本次预警的时间是不是在指定的时间周围
        if (CollectionUtils.isNotEmpty(warningConfigPO.getSpecifiedTime()) && warningConfigPO.getSpecifiedFlag()) {
            boolean anyMatch = warningConfigPO.getSpecifiedTime().stream().anyMatch(e -> isWithinRange(e.getLocalTime(), LocalTime.now()));
            log.info("匹配指定时间结果={}", anyMatch);
            if (!anyMatch) {
                return;
            }
        }
        List<String> alertMessageList = getAlertMessage(monitorList);
        log.info("doDealWarning doSendMessage warningObject={}, alertMessageList={}", warningConfigPO.getWarningObject(), alertMessageList);
        //发送预警
        MonitorWarnMsg monitorWarnMsg = new MonitorWarnMsg(warningConfigPO.getTenantId(),
                callJob.getCallJobData().getCallOutJobName(),
                alertMessageList,
                userService.selectListByKeyCollect(warningConfigPO.getWarningObject()));
        simpleAlertMessageSenderStrategy.sendAnyTimeMessage(monitorWarnMsg);
        LocalDateTime now = LocalDateTime.now();
        //保存预警历史
        List<NewWarningHistoryPO> warningHistoryPOList = warningConfigPO.getWarningObject().stream().map(userId -> {
            NewWarningHistoryPO historyPO = new NewWarningHistoryPO();
            historyPO.setWarningConfigurationId(warningConfigPO.getWarningConfigurationId());
            historyPO.setRobotCallJobId(callJob.getCallOutJobId());
            historyPO.setWarningRange(alertMessageList);
            historyPO.setWarningTime(now);
            historyPO.setType(WarningConfigTypeEnum.JOB_DIALOG);
            historyPO.setWarningObject(userId);
            return historyPO;
        }).collect(Collectors.toList());
        newWarningHistoryService.batchInsert(warningHistoryPOList);
    }

	@Override
	public void remoteSendWarningMessage(Long tenantId, Long warningConfigurationId, Long callOutJobId, String jobName, Collection<Long> userIds, List<String> alertMessageList) {
		// 发送预警
		MonitorWarnMsg monitorWarnMsg = new MonitorWarnMsg(tenantId, jobName, alertMessageList, userService.selectListByKeyCollect(userIds));
		simpleAlertMessageSenderStrategy.sendAnyTimeMessage(monitorWarnMsg);
		// 保存预警历史
		List<NewWarningHistoryPO> warningHistoryPOList = userIds.stream().map(userId -> {
			NewWarningHistoryPO historyPO = new NewWarningHistoryPO();
			historyPO.setWarningConfigurationId(warningConfigurationId);
			historyPO.setRobotCallJobId(callOutJobId);
			historyPO.setWarningRange(alertMessageList);
			historyPO.setWarningTime(LocalDateTime.now());
			historyPO.setType(WarningConfigTypeEnum.JOB_DIALOG);
			historyPO.setWarningObject(userId);
			return historyPO;
		}).collect(Collectors.toList());
		newWarningHistoryService.batchInsert(warningHistoryPOList);
	}

    /**
     * 执行预警后操作
     *
     * <AUTHOR>
     * date: 2024/10/17 11:06
     */
    private void doWaringAction(Long tenantId, Long robotCallJobId, List<NewMonitorIndexVO> monitorList) {
        try {
            if (CollectionUtils.isEmpty(monitorList)) {
                return;
            }
            Collections.reverse(monitorList);
            Optional<NewMonitorIndexVO> optional = monitorList.stream().filter(item -> Objects.nonNull(item.getWarningAction())).findFirst();
            if (optional.isPresent()) {
                if(WarningActionEnum.STOP_JOB.equals(optional.get().getWarningAction())) {
                    log.info("doWaringAction STOP_JOB={}", JSON.toJSONString(optional.get()));
                    UserPO userPO = userService.selectAdminByTenantId(tenantId);
                    callOutJobClient.monitorPauseJob(robotCallJobId, tenantId, userPO.getUserId());
                }else if(WarningActionEnum.START_JOB.equals(optional.get().getWarningAction())){
                    log.info("doWaringAction START_JOB={}", JSON.toJSONString(optional.get()));
                    UserPO userPO = userService.selectAdminByTenantId(tenantId);
                    callOutJobClient.monitorStartJob(robotCallJobId,tenantId, userPO.getUserId());
                }
            }
        } catch (Exception e) {
            log.error("doWaringAction error", e);
        }
    }

    /**
     * 获取任务的值Map
     */
    private Map<CallOutTotalTypesEnum, String> getValueMap(NewCallJobMonitorResultVO vo) {
        Map<CallOutTotalTypesEnum, String> valueMap = new HashMap<>(32);
        // 外呼实时数据
        if (Objects.nonNull(vo.getRealTimeData())) {
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_CALL_NUM, vo.getRealTimeData().getCurrentCallCount().toString());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_ANSWERED_RATE, vo.getRealTimeData().getRealTimeAnsweredRate());
//            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_REAL_ANSWERED_RATE, vo.getRealTimeData().getRealTimeAnsweredRateReal());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_DURATION_AVG, vo.getRealTimeData().getRealTimeDurationAvg());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_ROBOT_NUM, vo.getRealTimeData().getRobotCount().toString());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_TENANT_ROBOT_NOW, vo.getRealTimeData().getTenantRobotCountNow().toString());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_NO_CONNECT_RATE, vo.getRealTimeData().getRealTimeNotConnectRate());
            valueMap.put(CallOutTotalTypesEnum.REAL_TIME_FILTER_RATE, vo.getRealTimeData().getRealTimeFilteredRate());
        }
        // 接听状态
        if (Objects.nonNull(vo.getCallStatusData())) {
            valueMap.put(CallOutTotalTypesEnum.IMPORT_COUNT, vo.getCallStatusData().getImportCount().toString());
            valueMap.put(CallOutTotalTypesEnum.CALL_OUT_COUNT, vo.getCallStatusData().getCallOutCount().toString());
            valueMap.put(CallOutTotalTypesEnum.ANSWERED_COUNT, vo.getCallStatusData().getAnsweredCount().toString());
            valueMap.put(CallOutTotalTypesEnum.IMPORT_ANSWER_RATE, vo.getCallStatusData().getImportAnswerRate());
            valueMap.put(CallOutTotalTypesEnum.ANSWERED_RATE, vo.getCallStatusData().getAnsweredRate());
            valueMap.put(CallOutTotalTypesEnum.HANGUP_RATE, vo.getCallStatusData().getHangupRate());
            valueMap.put(CallOutTotalTypesEnum.ANSWERED_RATE_1, vo.getCallStatusData().getAnsweredRate1());
            valueMap.put(CallOutTotalTypesEnum.ANSWERED_RATE_2, vo.getCallStatusData().getAnsweredRate2());
//            valueMap.put(CallOutTotalTypesEnum.ANSWERED_RATE_REAL, vo.getCallStatusData().getAnsweredRateReal());
            valueMap.put(CallOutTotalTypesEnum.HANGUP_COUNT, vo.getCallStatusData().getHangupCount().toString());
        }
        // 短信发送状态
        if (Objects.nonNull(vo.getMessageSendData())) {
            valueMap.put(CallOutTotalTypesEnum.TOTAL_SMS_SEND, vo.getMessageSendData().getSendCount().toString());
            valueMap.put(CallOutTotalTypesEnum.SEND_SUCCESS_COUNT, vo.getMessageSendData().getSendSuccessCount().toString());
            valueMap.put(CallOutTotalTypesEnum.SEND_FAIL_COUNT, vo.getMessageSendData().getSendFailCount().toString());
            valueMap.put(CallOutTotalTypesEnum.SUCCESS_SEND_RATE, vo.getMessageSendData().getSmsSendSuccessRate());
            valueMap.put(CallOutTotalTypesEnum.REPORT_SUCCESS_COUNT, vo.getMessageSendData().getReportSuccessCount().toString());
            valueMap.put(CallOutTotalTypesEnum.REPORT_FAIL_COUNT, vo.getMessageSendData().getReportFailCount().toString());
            valueMap.put(CallOutTotalTypesEnum.REPORTING_COUNT, vo.getMessageSendData().getReportingCount().toString());
            valueMap.put(CallOutTotalTypesEnum.REPORT_TRANSFER_COUNT, vo.getMessageSendData().getReportTransferCount().toString());
            valueMap.put(CallOutTotalTypesEnum.SUCCESS_RECEIVE_RATE, vo.getMessageSendData().getReportSuccessRate());
            valueMap.put(CallOutTotalTypesEnum.REPORT_FAIL_RATE, vo.getMessageSendData().getReportFailRate());
        }
        // 过滤数据
        if (Objects.nonNull(vo.getFilterData())) {
            valueMap.put(CallOutTotalTypesEnum.FILTERED_COUNT_1, vo.getFilterData().getFilteredCount1().toString());
            valueMap.put(CallOutTotalTypesEnum.FILTERED_RATE_1, vo.getFilterData().getFilteredRate1());
            valueMap.put(CallOutTotalTypesEnum.FILTERED_COUNT_2, vo.getFilterData().getFilteredCount2().toString());
            valueMap.put(CallOutTotalTypesEnum.FILTERED_RATE_2, vo.getFilterData().getFilteredRate2());
        }
        return valueMap;
    }

    /**
     * 获取话术的值Map
     */
    private Map<CallOutTotalTypesEnum, String> getValueMap(RealTimeCallJobDataInfo realTimeCallJobDataInfo,
                                                           DialogBusiMetricsDataInfo dialog) {
        Map<CallOutTotalTypesEnum, String> valueMap = new HashMap<>(16);
        // 实时数据
        valueMap.put(CallOutTotalTypesEnum.REAL_TIME_DIALOG_CALL_NUM, realTimeCallJobDataInfo.getCurrentCallCount().toString());
        valueMap.put(CallOutTotalTypesEnum.REAL_TIME_ROBOT_KNOWLEDGE_PERCENT, realTimeCallJobDataInfo.getInaudibleKnowledgePercent());
        valueMap.put(CallOutTotalTypesEnum.USER_WHOLE_MUTE_PERCENT, realTimeCallJobDataInfo.getUserWholeMutePercent());
        valueMap.put(CallOutTotalTypesEnum.ROBOT_KNOWLEDGE_PERCENT, realTimeCallJobDataInfo.getRobotKnowledgePercent());
        valueMap.put(CallOutTotalTypesEnum.ABUSE_KNOWLEDGE_PERCENT, realTimeCallJobDataInfo.getAbuseKnowledgePercent());

        // 用户听不清
        valueMap.put(CallOutTotalTypesEnum.CALL_NUMBER1, dialog.getInaudibleKnowledge().toString());
        valueMap.put(CallOutTotalTypesEnum.CALL_PERCENT1, dialog.getInaudibleKnowledgePercent());

        // 客户全程无应答
        valueMap.put(CallOutTotalTypesEnum.CALL_NUMBER2, dialog.getUserWholeMute().toString());
        valueMap.put(CallOutTotalTypesEnum.CALL_PERCENT2, dialog.getUserWholeMutePercent());

        // 命中机器人
        valueMap.put(CallOutTotalTypesEnum.CALL_NUMBER3, dialog.getRobotKnowledge().toString());
        valueMap.put(CallOutTotalTypesEnum.CALL_PERCENT3, dialog.getRobotKnowledgePercent());

        // 命中骚扰电话/骂人
        valueMap.put(CallOutTotalTypesEnum.CALL_NUMBER4, dialog.getAbuseKnowledge().toString());
        valueMap.put(CallOutTotalTypesEnum.CALL_PERCENT4, dialog.getAbuseKnowledgePercent());

        // 意图识别率
        valueMap.put(CallOutTotalTypesEnum.REGULAR_INTENTION, dialog.getRegexMatchPercent());
        valueMap.put(CallOutTotalTypesEnum.QUESTION_INTENTION, dialog.getAlgorithmMatchPercent());
        valueMap.put(CallOutTotalTypesEnum.NO_INTENTION, dialog.getNoneIntentPercent());

        log.info("dialog={}, valueMap={}", dialog, valueMap);
        return valueMap;
    }


    /**
     * 拼接预警消息
     * (进入预警历史，添加触发预警值)
     */
    public List<String> getAlertMessage(List<NewMonitorIndexVO> monitorList) {
        if (CollectionUtils.isEmpty(monitorList)) {
            return Collections.emptyList();
        }
        log.debug("getAlertMessage monitorList={}", JSON.toJSONString(monitorList));
        List<String> alterMessageList = new ArrayList<>();
        monitorList.forEach(monitor -> {
            StringBuffer buffer = new StringBuffer();
            buffer.append(monitor.getIndexName().getDesc());
            if (monitor.getIndexTypes() != null) {
                buffer.append("-");
                if (monitor.getIndexTypes().size() > 1) {
                    buffer.append(monitor.getIndexName().getDesc()).append("（");
                    monitor.getIndexTypes().forEach(indexType -> buffer.append(indexType.getDesc()).append(","));
                    buffer.append("）").append(" ");
                } else if (monitor.getIndexTypes().size() > 0) {
                    buffer.append(monitor.getIndexTypes().get(0).getDesc()).append(" ");
                }
                buffer.append(monitor.getNumType().getDesc()).append(" ").append(monitor.getNumber());
            }
            StringBuffer buffer2 = new StringBuffer();
            buffer2.append("触发预警值 = ").append(monitor.getWarnValue());
            if (WarningActionEnum.STOP_JOB.equals(monitor.getWarningAction())) {
                buffer2.append("，预警后动作：暂停任务");
            }else if(WarningActionEnum.START_JOB.equals(monitor.getWarningAction())){
                buffer.append("，预警后动作：开始任务");
            }
            alterMessageList.add(buffer.toString());
            alterMessageList.add(buffer2.toString());

        });
        return alterMessageList;
    }

	/**
     * 阈值比较
     */
    private boolean thresholdCompare(String value, Double threshold, WarningNumEnum numType) {
        if (StringUtils.isBlank(value) || Objects.isNull(threshold) || Objects.isNull(numType)) {
            return false;
        }
        Double valueDouble;
        if (StringUtils.endsWith(value, "%")) {
            value = StringUtils.substringBefore(value, "%");
        }
        try {
            valueDouble = Double.parseDouble(value);
        } catch (NumberFormatException e) {
            log.error("thresholdCompare value={} threshold={} numType={} NumberFormatException", value, threshold, numType);
            return false;
        }
        switch (numType) {
            case GREATER:
                return valueDouble.compareTo(threshold) > 0;
            case LESS:
                return valueDouble.compareTo(threshold) < 0;
            case GREATER_EQUAL:
                return valueDouble.compareTo(threshold) >= 0;
            case LESS_EQUAL:
                return valueDouble.compareTo(threshold) <= 0;
            case EQUAL:
                return valueDouble.compareTo(threshold) == 0;
            default: return false;
        }
    }

    /**
     * 获取外呼通话数
     */
    private Long getCallTaskCompletedWithoutFilter(NewCallJobMonitorResultVO callJob) {
        Long callTaskCompleted = Objects.nonNull(callJob.getCallJobData()) ? callJob.getCallJobData().getCallTaskCompleted() : 0L;
        Long filteredCount2 = Objects.nonNull(callJob.getFilterData()) ? callJob.getFilterData().getFilteredCount2() : 0L;
        return callTaskCompleted - filteredCount2;
    }

	/**
	 * 获取接听通话数
	 */
	private Long getAnsweredCall(NewCallJobMonitorResultVO callJob) {
		if (callJob.getCallStatusData() == null || callJob.getCallStatusData().getAnsweredCount() == null) {
			return 0L;
		}
		return callJob.getCallStatusData().getAnsweredCount();
	}

    /**
     * 计算百分比
     */
    private static Double calculateRate(Long a, Long b) {
        if (a == null || b == null || a == 0L || b == 0L) {
            return 0.0;
        }
        return BigDecimal.valueOf(a).multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(b), 1, RoundingMode.HALF_UP).doubleValue();
    }
}
