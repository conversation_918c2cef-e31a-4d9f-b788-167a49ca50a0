package com.yiwise.core.service.engine.calljob.impl;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.billing.api.api.ProductApi;
import com.yiwise.billing.api.bo.request.IdsRequestBO;
import com.yiwise.core.dal.dao.RobotCallJobPhoneNumberPOMapper;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.phonenumber.FreeswitchGroupItemInfoBO;
import com.yiwise.core.model.bo.phonenumber.RobotCallPhoneNumberWithSipInfoBO;
import com.yiwise.core.model.bo.robotcalljob.TimeRobotCountBO;
import com.yiwise.core.model.dto.LineStatusDTO;
import com.yiwise.core.model.dto.RobotCallJobPhoneNumberDTO;
import com.yiwise.core.model.enums.PhoneTypeEnum;
import com.yiwise.core.model.enums.RobotCallJobHangUpTypeEnum;
import com.yiwise.core.model.enums.phonenumber.UseDialplanTypeEnum;
import com.yiwise.core.service.callpolicygroup.CallPolicyGroupService;
import com.yiwise.core.service.engine.TenantPhoneNumberService;
import com.yiwise.core.service.engine.calljob.RobotCallJobPhoneNumberService;
import com.yiwise.core.service.engine.impl.TenantPhoneNumberServiceImpl;
import com.yiwise.core.service.engine.phonenumber.FreeswitchGroupService;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.ope.platform.PhoneCardService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.platform.RobotService;
import com.yiwise.core.utils.MyCollectionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.*;

/**
 * <AUTHOR>
 * @date 24/07/2018
 */
@Service
public class RobotCallJobPhoneNumberServiceImpl extends BasicServiceImpl<RobotCallJobPhoneNumberPO> implements RobotCallJobPhoneNumberService {
    private static final Logger logger = LoggerFactory.getLogger(RobotCallJobPhoneNumberServiceImpl.class);

    @Resource
    private RobotCallJobPhoneNumberPOMapper robotCallJobPhoneNumberPOMapper;
    @Resource
    private PhoneNumberService phoneNumberService;
    @Resource
    private TenantPhoneNumberService tenantPhoneNumberService;
    @Resource
    private TenantService tenantService;
    @Resource
    private FreeswitchGroupService freeswitchGroupService;
    @Lazy
    @Resource
    private CallPolicyGroupService callPolicyGroupService;
    @Resource
    private PhoneCardService phoneCardService;
    @Resource
    private RobotService robotService;
    @Resource
    private ProductApi productApi;

    @Override
    public List<TenantPhoneNumberPO> checkRobotCallJobPhoneNumberAndConcurrencyIsValid(RobotCallJobPO robotCallJob, Integer concurrencyQuota, Boolean enableConcurrency, List<Long> tenantPhoneNumberIdList) {
        Long tenantId = robotCallJob.getTenantId();

        List<TenantPhoneNumberPO> robotCallJobPhoneNumberList = tenantPhoneNumberService.getRobotCallJobPhoneNumberList(tenantId, tenantPhoneNumberIdList);
        Set<Long> validPhoneNumberIdSet = robotCallJobPhoneNumberList.stream().map(TenantPhoneNumberPO::getTenantPhoneNumberId).collect(Collectors.toSet());

        // 不合法的线路集合（不属于该租户）
        List<Long> invalidPhoneNumberIds = (List<Long>) CollectionUtils.subtract(tenantPhoneNumberIdList, validPhoneNumberIdSet);

        if (CollectionUtils.isNotEmpty(invalidPhoneNumberIds)) {
            Long invalidPhoneNumberId = invalidPhoneNumberIds.get(0);
//            PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKeyOrThrow(invalidPhoneNumberId);
            String msg;
            String msgDetail;
            TenantPhoneNumberPO invalidPhoneNumber = tenantPhoneNumberService.selectByKeyOrThrow(invalidPhoneNumberId);
            if (invalidPhoneNumber != null) {
                PhoneNumberPO phoneNumberPO = phoneNumberService.selectByKeyOrThrow(invalidPhoneNumber.getPhoneNumberId());
                msg = "线路选择错误，任务所选线路" + (phoneNumberPO == null ? "" : phoneNumberPO.getPhoneNumber()) + "不属于该租户的线路";
                msgDetail = msg + ", 所选非法线路ID=" + invalidPhoneNumberId + "，所属租户ID=" + invalidPhoneNumber.getTenantId() + "，当前租户ID=" + tenantId;
            } else {
                msg = "线路选择错误，任务所选线路不属于该租户的线路";
                msgDetail = msg;
            }
            throw new ComException(ComErrorCode.VALIDATE_ERROR, msg, msgDetail);
        }

        PhoneTypeEnum phoneType = robotCallJob.getPhoneType();
        boolean isSamePhoneType = robotCallJobPhoneNumberList.stream().map(TenantPhoneNumberPO::getPhoneType).allMatch(item -> Objects.equals(item, phoneType));
        if (!isSamePhoneType) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选线路类型和传入类型不匹配", "传入主叫号码类型为: " + phoneType);
        }

        // 创建任务，所选线路只有在是手机号码的情况下才支持有多条
        if (CollectionUtils.size(tenantPhoneNumberIdList) > 1 && !PhoneTypeEnum.CALL_POLICY_GROUP.equals(phoneType) && !PhoneTypeEnum.MOBILE.equals(phoneType)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选线路只有在是手机号码的情况下才支持有多条");
        }

        // 如果是手机号线路，所选线路总和要等于并发个数
        if (PhoneTypeEnum.MOBILE.equals(phoneType)) {
            if (concurrencyQuota == null) {
                concurrencyQuota = tenantPhoneNumberIdList.size();
            }

            if (concurrencyQuota != tenantPhoneNumberIdList.size()) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选并发数量必须等于手机线路数量");
            }
        } else {
            Assert.notNull(concurrencyQuota, "并发数不能为空");
        }

        checkJobConcurrencyIsValid(robotCallJob, concurrencyQuota, enableConcurrency, tenantId);
        return robotCallJobPhoneNumberList;
    }

    @Override
    public List<TenantPhoneNumberPO> checkRobotCallJobPolicyGroupAndConcurrencyIsValid(RobotCallJobPO robotCallJob, Integer concurrencyQuota, Boolean enableConcurrency, Long callPolicyGroupId) {
        Long tenantId = robotCallJob.getTenantId();
        // 线路的归属情况，在维护策略组的时候会检查，这里不需要检查， 只检查并发设置信息
        List<TenantPhoneNumberPO> tenantPhoneNumberList = callPolicyGroupService.queryTenantPhoneNumberListByPolicyGroupId(robotCallJob.getTenantId(), callPolicyGroupId);

        checkJobConcurrencyIsValid(robotCallJob, concurrencyQuota, enableConcurrency, tenantId);
        return tenantPhoneNumberList;
    }

    // 校验任务的并发数量是否合法
    private void checkJobConcurrencyIsValid(RobotCallJobPO robotCallJob, Integer concurrencyQuota, Boolean enableConcurrency, Long tenantId) {
        // 校验多并发参数
        if (enableConcurrency) {
            checkConcurrencyQuota(tenantId, robotCallJob.getBaseRobotCount(), concurrencyQuota, robotCallJob.getTimeRobotCountList());
        } else {
            if (robotCallJob.getBaseRobotCount() != null && !concurrencyQuota.equals(robotCallJob.getBaseRobotCount())) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "坐席数量必须等于并发数量");
            }
            // 如果robotCount前端传入为空
            robotCallJob.setBaseRobotCount(concurrencyQuota);
        }
        robotCallJob.setBaseConcurrencyQuota(concurrencyQuota);
    }

    @Override
    public void checkConcurrencyQuota(Long tenantId, Integer robotCount, Integer concurrencyQuota, List<TimeRobotCountBO> timeRobotCountList) {
        TenantPO tenantPO = tenantService.selectByKey(tenantId);
        if (CollectionUtils.isNotEmpty(timeRobotCountList)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "分时设置不支持一线多并发");
        }

        //对代理商客户或直销客户进行不同的多并发校验
        if(tenantPO.getDistributorId() > 0){
	        Integer concurrency = robotService.getDistributorTenantTodayConcurrency(tenantId);
	        if(concurrency <= 0){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前账户不支持多并发，请联系商务开通");
            }
        }else{
            if(!tenantPO.getEnableConcurrency()){
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "当前账户不支持多并发，请联系商务开通");
            }
        }

        if (robotCount >= concurrencyQuota) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选坐席数量必须小于并发数量");
        }

        if (robotCount == 0) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "坐席数量不能为0");
        }

        if (concurrencyQuota / robotCount >= 3) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "所选并发数量不能超过坐席数量的3倍");
        }
    }

    @Override
    public boolean checkMobilePhoneNumberNotUsed(Long tenantId, Set<Long> phoneNumberIdSet, Long exceptRobotCallJobId) {
        if (CollectionUtils.isEmpty(phoneNumberIdSet)) {
            return true;
        }
//        List<Long> usedMobileTenantPhoneNumber = tenantPhoneNumberService.getUsedMobileTenantPhoneNumber(tenantId, phoneNumberIdSet);
        // 判断选择手机线路的任务是否在可运行或进行中状态
        List<Long> usedMobileTenantPhoneNumber = robotCallJobPhoneNumberPOMapper.selectUsedMobile(phoneNumberIdSet, tenantId, exceptRobotCallJobId);
        boolean isConflict = CollectionUtils.isNotEmpty(usedMobileTenantPhoneNumber);

        if (isConflict) {
            final Long conflictTenantPhoneNumberId = usedMobileTenantPhoneNumber.get(0);
            logger.info("输入的手机线路中, 有被占用的手机线路, tenantPhoneNumberId={}.", conflictTenantPhoneNumberId);
        }
        return !isConflict;
    }

    @Override
    public void saveRobotCallJobPhoneNumber(RobotCallJobPO robotCallJob, Integer concurrencyQuota, List<TenantPhoneNumberPO> tenantPhoneNumberList) {
        if (CollectionUtils.isEmpty(tenantPhoneNumberList)) {
            return;
        }
        List<RobotCallJobPhoneNumberPO> robotCallJobPhoneNumberList = tenantPhoneNumberList.stream().map(item -> {
            RobotCallJobPhoneNumberPO robotCallJobPhoneNumber = new RobotCallJobPhoneNumberPO();
            robotCallJobPhoneNumber.setTenantId(robotCallJob.getTenantId());
            robotCallJobPhoneNumber.setTenantPhoneNumberId(item.getTenantPhoneNumberId());
            robotCallJobPhoneNumber.setPhoneNumberId(item.getPhoneNumberId());

            robotCallJobPhoneNumber.setRobotCallJobId(robotCallJob.getRobotCallJobId());
            return robotCallJobPhoneNumber;
        }).collect(Collectors.toList());
        robotCallJobPhoneNumberPOMapper.saveRobotCallJobPhoneNumber(robotCallJobPhoneNumberList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRobotCallJobPhoneNumber(RobotCallJobPO robotCallJob, TenantPhoneNumberPO tenantPhoneNumberPO) {
        Assert.notNull(robotCallJob, "任务不存在");
        Assert.notNull(tenantPhoneNumberPO, "线路不存在");
        //删除原来的
        deleteAllRobotCallJobPhoneNumber(robotCallJob.getTenantId(), robotCallJob.getRobotCallJobId());
        //新增
        RobotCallJobPhoneNumberPO robotCallJobPhoneNumberPO = new RobotCallJobPhoneNumberPO();
        robotCallJobPhoneNumberPO.setTenantId(robotCallJob.getTenantId());
        robotCallJobPhoneNumberPO.setTenantPhoneNumberId(tenantPhoneNumberPO.getTenantPhoneNumberId());
        robotCallJobPhoneNumberPO.setPhoneNumberId(tenantPhoneNumberPO.getPhoneNumberId());
        robotCallJobPhoneNumberPO.setRobotCallJobId(robotCallJob.getRobotCallJobId());
        saveNotNull(robotCallJobPhoneNumberPO);
    }

    @Override
    public void saveRobotCallJobPhoneNumberCallPolicyGroupId(RobotCallJobPO robotCallJob,  Long callPolicyGroupId) {
        PhoneTypeEnum phoneType = robotCallJob.getPhoneType();
        // 如果是外呼策略组 调度方式，则只需要设置一条记录即可
        if (PhoneTypeEnum.CALL_POLICY_GROUP.equals(phoneType)) {
            RobotCallJobPhoneNumberPO robotCallJobPhoneNumber = new RobotCallJobPhoneNumberPO();
            robotCallJobPhoneNumber.setTenantId(robotCallJob.getTenantId());
            robotCallJobPhoneNumber.setCallPolicyGroupId(callPolicyGroupId);
            robotCallJobPhoneNumber.setRobotCallJobId(robotCallJob.getRobotCallJobId());
            robotCallJobPhoneNumber.setTenantPhoneNumberId(0L);
            robotCallJobPhoneNumber.setPhoneNumberId(0L);
            robotCallJobPhoneNumberPOMapper.saveRobotCallJobPhoneNumber(Collections.singletonList(robotCallJobPhoneNumber));
        }
    }

    @Override
    public Map<Integer, RobotCallPhoneNumberWithSipInfoBO> getCallJobCallerInfoMapForJob(Long callJobId) {
        // 获取到该job下拥有的所有主叫号码的列表
        List<RobotCallPhoneNumberWithSipInfoBO> jobPhoneNumberList = getCallJobCallerInfoList(callJobId);
        return handleRobotCallPhoneNumberWithSipInfo(jobPhoneNumberList);
    }

    @Override
    public Map<Integer, RobotCallPhoneNumberWithSipInfoBO> getTenantCallerInfoMapForJob(Long tenantId) {
        RobotCallPhoneNumberWithSipInfoBO tenantExtensionPhoneNumber = tenantPhoneNumberService.getTenantExtensionPhoneNumber(tenantId);
        if (Objects.isNull(tenantExtensionPhoneNumber)) {
            return Collections.emptyMap();
        }
        List<RobotCallPhoneNumberWithSipInfoBO> jobPhoneNumberList = new ArrayList<>();
        jobPhoneNumberList.add(tenantExtensionPhoneNumber);
	    setProductUnitPrice(jobPhoneNumberList);
        return handleRobotCallPhoneNumberWithSipInfo(jobPhoneNumberList);
    }

    @Override
    public Map<Integer, RobotCallPhoneNumberWithSipInfoBO> getCallJobCallerInfoMapForJobUsePolicyGroup(Long callJobId) {
        // 获取到该job下拥有的所有主叫号码的列表
        List<RobotCallPhoneNumberWithSipInfoBO> jobPhoneNumberList = getCallJobCallerInfoListByJobUsePolicyGroup(callJobId);
	    setProductUnitPrice(jobPhoneNumberList);
        return handleRobotCallPhoneNumberWithSipInfo(jobPhoneNumberList);
    }

    @Override
    public int deleteRobotCallJobPhoneNumber(Set<Long> tenantPhoneNumberIdSet, Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.deleteRobotCallJobPhoneNumber(tenantPhoneNumberIdSet, robotCallJobId);
    }

    @Override
    public List<Long> getCallJobMobilePhoneNumberIdList(Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.getCallJobMobilePhoneNumberIdList(robotCallJobId);
    }

    @Override
    public List<Long> getCallJobMobilePhoneNumberIdListByCollction(List<Long> robotCallJobIds) {
        return robotCallJobPhoneNumberPOMapper.getCallJobMobilePhoneNumberIdListByCollection(robotCallJobIds);
    }

    @Override
    public List<RobotCallJobPhoneNumberPO> getCallJobPhoneNumberIdList(Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.getCallJobPhoneNumberIdList(robotCallJobId);
    }

    @Override
    public List<Long> getCallJobTenantPhoneNumberList(Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.getCallJobTenantPhoneNumberList(robotCallJobId);
    }

    @Override
    public List<RobotCallPhoneNumberWithSipInfoBO> getCallJobCallerInfoList(Long callJobId) {
	    List<RobotCallPhoneNumberWithSipInfoBO> robotCallJobPhoneNumberList = robotCallJobPhoneNumberPOMapper.getRobotCallJobPhoneNumberList(callJobId);
	    setProductUnitPrice(robotCallJobPhoneNumberList);
	    return robotCallJobPhoneNumberList;
    }

    @Override
    public Long queryCallPolicyGroupIdByRobotCallJobId(Long tenantId, Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.queryCallPolicyGroupIdByRobotCallJobId(tenantId, robotCallJobId);
    }

    @Override
    public void updateCallPolicyGroupByRobotCallJobId(Long tenantId, Long robotCallJobId, Long callPolicyGroupId) {
        robotCallJobPhoneNumberPOMapper.updateCallPolicyGroupByRobotCallJobId(tenantId, robotCallJobId, callPolicyGroupId);
    }

    @Override
    public int deleteAllRobotCallJobPhoneNumber(Long tenantId, Long robotCallJobId) {
        return robotCallJobPhoneNumberPOMapper.deleteAllRobotCallJobPhoneNumber(tenantId, robotCallJobId);
    }

    @Override
    public void deleteAllRobotCallJobPhoneNumberByRobotCallJobIdList(Long tenantId, List<Long> callJobIdList) {
        if (CollectionUtils.isEmpty(callJobIdList)) {
            return;
        }
        robotCallJobPhoneNumberPOMapper.deleteAllRobotCallJobPhoneNumberByRobotCallJobIdList(tenantId, callJobIdList);
    }

    @Override
    public List<RobotCallPhoneNumberWithSipInfoBO> getCallJobCallerInfoListByJobUsePolicyGroup(Long callJobId) {
	    List<RobotCallPhoneNumberWithSipInfoBO> robotCallJobPhoneNumberListByJobUsePolicyGroup = robotCallJobPhoneNumberPOMapper.getRobotCallJobPhoneNumberListByJobUsePolicyGroup(callJobId);
	    setProductUnitPrice(robotCallJobPhoneNumberListByJobUsePolicyGroup);
	    return robotCallJobPhoneNumberListByJobUsePolicyGroup;
    }

    @Override
    public List<RobotCallJobPO> getRobotCallJobIdByPhoneNumberId(Long phoneNumberId) {
        return robotCallJobPhoneNumberPOMapper.getRobotCallJobIdByPhoneNumberId(phoneNumberId);
    }

    @Override
    public List<RobotCallJobPhoneNumberPO> getRobotCallJobIdByPhoneNumberList(Collection<Long> phoneNumberIdList, Collection<Long> tenantIds) {
        List<RobotCallJobPhoneNumberPO> result = robotCallJobPhoneNumberPOMapper.getRobotCallJobIdByPhoneNumberList(phoneNumberIdList,tenantIds);
        return CollectionUtils.isEmpty(result) ?  new ArrayList<>() : result;
    }

    @Override
    public boolean checkLineOfJobAvailable(Long robotCallJobId) {
        boolean result = true;
        List<RobotCallJobPhoneNumberPO> robotCallJobPhoneNumberPOList = getCallJobPhoneNumberIdList(robotCallJobId);
        for (RobotCallJobPhoneNumberPO robotCallJobPhoneNumberPO : robotCallJobPhoneNumberPOList) {
            Long phoneNumberId = robotCallJobPhoneNumberPO.getPhoneNumberId();
            PhoneNumberPO phoneNumberPO = phoneCardService.selectByKey(phoneNumberId);
            if (phoneNumberPO == null) {
                Long callPolicyGroupId = robotCallJobPhoneNumberPO.getCallPolicyGroupId();
                CallPolicyGroupPO callPolicyGroupPO = callPolicyGroupService.selectByKey(callPolicyGroupId);
                if (callPolicyGroupPO != null) {
                    boolean policyGroupAvailable = callPolicyGroupService.checkPolicyGroupAvailable(callPolicyGroupId);
                    result = result & policyGroupAvailable;
                    continue;
                } else {
                    logger.error("PID={}线路不存在", phoneNumberId);
                    continue;
                }
            }
            // 检查线路状态
            boolean phoneNumberAvailable = phoneCardService.checkPhoneNumberWithGatewayAvailable(phoneNumberId) && !tenantPhoneNumberService.checkTenantPhoneNumberStopped(robotCallJobPhoneNumberPO.getTenantPhoneNumberId());
            result = result & phoneNumberAvailable;
        }
        return result;
    }

    @Override
    public boolean checkLineOfJobSupported(Long robotCallJobId) {
        boolean result = true;
        List<RobotCallJobPhoneNumberPO> robotCallJobPhoneNumberPOList = getCallJobPhoneNumberIdList(robotCallJobId);
        for (RobotCallJobPhoneNumberPO robotCallJobPhoneNumberPO : robotCallJobPhoneNumberPOList) {
            Long phoneNumberId = robotCallJobPhoneNumberPO.getPhoneNumberId();
            PhoneNumberPO phoneNumberPO = phoneCardService.selectByKey(phoneNumberId);
            if (phoneNumberPO == null) {
                Long callPolicyGroupId = robotCallJobPhoneNumberPO.getCallPolicyGroupId();
                CallPolicyGroupPO callPolicyGroupPO = callPolicyGroupService.selectByKey(callPolicyGroupId);
                if (callPolicyGroupPO != null) {
                    boolean policyGroupAvailable = callPolicyGroupService.checkPolicyGroupSupported(callPolicyGroupId);
                    result = result & policyGroupAvailable;
                    continue;
                } else {
                    logger.error("PID={}线路不存在", phoneNumberId);
                    continue;
                }
            }
            // 检查线路状态
            boolean phoneNumberSupported = phoneCardService.checkPhoneNumberSupported(phoneNumberId);
            result = result & phoneNumberSupported;
        }
        return result;
    }

    @Override
    public String getBreakDownMsg(Long robotCallJobId) {
        List<RobotCallJobPhoneNumberPO> robotCallJobPhoneNumberPOList = getCallJobPhoneNumberIdList(robotCallJobId);
        if (CollectionUtils.isNotEmpty(robotCallJobPhoneNumberPOList)) {
            for (RobotCallJobPhoneNumberPO robotCallJobPhoneNumberPO : robotCallJobPhoneNumberPOList) {
                Optional<LineStatusDTO> lineStatus = phoneCardService.getLineStatus(robotCallJobPhoneNumberPO.getPhoneNumberId());
                // 手机线路多条
                if (lineStatus.isPresent() && !lineStatus.get().getIsLineAvailable()) {
                    return lineStatus.get().getHint();
                }
                // 停用线路的原因
                TenantPhoneNumberPO tenantPhoneNumberPO = tenantPhoneNumberService.selectByKey(robotCallJobPhoneNumberPO.getTenantPhoneNumberId());
                if (tenantPhoneNumberPO == null) {
                    // 策略组
                    return RobotCallJobHangUpTypeEnum.LINE_BREAKDOWN.getMsg();
                } else {
                    if (Boolean.TRUE.equals(tenantPhoneNumberPO.getStopStatus())) {
                        return TenantPhoneNumberServiceImpl.STOPPED_REASON;
                    }
                }
            }
        }
        return RobotCallJobHangUpTypeEnum.LINE_BREAKDOWN.getMsg();
    }

    @Override
    public Map<Integer, RobotCallPhoneNumberWithSipInfoBO> handleRobotCallPhoneNumberWithSipInfo(List<RobotCallPhoneNumberWithSipInfoBO> jobPhoneNumberList){
        for (RobotCallPhoneNumberWithSipInfoBO phoneNumber : jobPhoneNumberList) {
            logger.debug("TenantPhoneNumberId={}, PhoneNumber={}, needFeeCharging={}, LocalBillRate={}, OtherBillRate={}, productId={}",
                    phoneNumber.getTenantPhoneNumberId(), phoneNumber.getPhoneNumber(), phoneNumber.needFeeCharging(),
		            phoneNumber.getLocalBillRate(), phoneNumber.getOtherBillRate(), phoneNumber.getProductId());
        }

        Set<Long> freeswitchGroupIdSet = jobPhoneNumberList.stream().map(RobotCallPhoneNumberWithSipInfoBO::getFreeswitchGroupId).collect(Collectors.toSet());

        Map<Integer, RobotCallPhoneNumberWithSipInfoBO> jobPhoneNumberMap = new HashMap<>();

        AtomicInteger jobPhoneIndex = new AtomicInteger(0);

        Map<? extends Serializable, FreeswitchGroupPO> freeswitchGroupMap = freeswitchGroupService.selectMapByKeyCollect(freeswitchGroupIdSet);
        for (RobotCallPhoneNumberWithSipInfoBO jobPhoneNumber : jobPhoneNumberList) {
            Long freeswitchGroupId = jobPhoneNumber.getFreeswitchGroupId();

            FreeswitchGroupPO freeswitchGroupPO = freeswitchGroupMap.get(freeswitchGroupId);
            if (freeswitchGroupPO == null) {
                throw new ComException(ComErrorCode.NOT_EXIST, "不存在的freeswitch配置信息", "不存在的freeswitch配置信息, freeswitchGroupId=" + freeswitchGroupId);
            }

            // 获取本条线路所属freeswitch分组的所有freeswitch的信息
            List<FreeswitchGroupItemInfoBO> freeswitchList = freeswitchGroupService.getFreeswithList(jobPhoneNumber.getFreeswitchGroupId());
            jobPhoneNumber.setFreeswitchInfoList(freeswitchList);

            //设置opensips信息
            if (UseDialplanTypeEnum.OPENSIPS.equals(jobPhoneNumber.getUseDialplanType())) {
                //使用配置文件中最新的OPENSIP地址
                jobPhoneNumber.setLineIp(OPENSIPS_SERVER);
                String opensipsGroup = jobPhoneNumber.getVariableSet().get("opensips_group");
                if (StringUtils.equals("encrypt", opensipsGroup)) {
                    jobPhoneNumber.setLineIp(OPENSIPS_SERVER_ENCRYPT);
                }
                jobPhoneNumber.setLinePort(OPENSIPS_PORT);
            }

            jobPhoneNumberMap.put(jobPhoneIndex.getAndIncrement(), jobPhoneNumber);
        }
        return jobPhoneNumberMap;
    }


    @Override
    public void copyRobotCallJobPhoneNumber(RobotCallJobPO robotCallJob, RobotCallJobPO copyRobotCallJob) {
        List<RobotCallJobPhoneNumberPO> robotCallJobPhoneNumberList = robotCallJobPhoneNumberPOMapper.getCallJobPhoneNumberIdList(copyRobotCallJob.getRobotCallJobId());
        if(robotCallJobPhoneNumberList.size() > 0){
            robotCallJobPhoneNumberList.forEach(item -> {
                item.setTenantId(robotCallJob.getTenantId());
                item.setRobotCallJobId(robotCallJob.getRobotCallJobId());
            });
            robotCallJobPhoneNumberPOMapper.saveRobotCallJobPhoneNumber(robotCallJobPhoneNumberList);
        }
    }

    @Override
    public Integer countLineConcurrency(Long tenantId, Long tenantPhoneNumberId) {
        try {
            Integer concurrency = robotCallJobPhoneNumberPOMapper.countLineConcurrency(tenantId, tenantPhoneNumberId);
            if (concurrency == null) {
                concurrency = 0;
            }
            return concurrency;
        } catch (Exception e) {
            logger.error("[LogHub_Warn]查询线路当前并发数出错, tenantId=[{}], tenantPhoneNumberId=[{}]", tenantId, tenantPhoneNumberId);
            return 0;
        }
    }

    @Override
    public Map<Long, List<RobotCallJobPhoneNumberDTO>> selectRobotCallJobPhoneNumberMap(List<Long> robotCallJobIdList) {
        if (CollectionUtils.isEmpty(robotCallJobIdList)) {
            return Collections.emptyMap();
        }
        List<RobotCallJobPhoneNumberDTO> robotCallJobPhoneNumberDTOS = robotCallJobPhoneNumberPOMapper.selectRobotCallJobPhoneNumber(robotCallJobIdList);
        return MyCollectionUtils.listToMapList(robotCallJobPhoneNumberDTOS, RobotCallJobPhoneNumberDTO::getRobotCallJobId);
    }

	/**
	 * 向计费服务查询线路单价并赋值
	 */
	private void setProductUnitPrice(Collection<RobotCallPhoneNumberWithSipInfoBO> infoCollection) {
	    if (CollectionUtils.isEmpty(infoCollection)) {
	    	return;
	    }
	    Set<Long> productIds = infoCollection.stream().map(RobotCallPhoneNumberWithSipInfoBO::getProductId).collect(Collectors.toSet());
	    if (!productIds.isEmpty()) {
		    IdsRequestBO idsRequest = new IdsRequestBO();
		    idsRequest.setIds(productIds);
		    Map<Long, Long> idPriceMap = productApi.selectUnitPriceByProductIds(idsRequest);
		    for (RobotCallPhoneNumberWithSipInfoBO info : infoCollection) {
			    info.setProductUnitPrice(idPriceMap.get(info.getProductId()));
		    }
	    }
    }
}
