package com.yiwise.core.service.engine.impl;

import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.dal.entity.ApiDocumentMenuPO;
import com.yiwise.core.dal.mongo.ApiDocumentArticlePO;
import com.yiwise.core.model.enums.ope.OpenApiTypeEnum;
import com.yiwise.core.model.vo.apiDocumentCenter.ApiDocumentArticleVO;
import com.yiwise.core.model.vo.apiDocumentCenter.ApiDocumentMenuVO;
import com.yiwise.core.service.apidocumentCenter.ApiDocumentArticleService;
import com.yiwise.core.service.apidocumentCenter.ApiDocumentMenuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.yiwise.base.model.exception.ComErrorCode.UNKNOWN_ERROR;

/**
 * <AUTHOR>
 */
@Service
public class ApiDocumentArticleServiceImpl implements ApiDocumentArticleService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ApiDocumentMenuService apiDocumentMenuService;

    /**
     * 添加文章
     * @param apiDocumentArticleVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addApiDocumentArticle(ApiDocumentArticleVO apiDocumentArticleVO) {
        //根据文章状态，判断是否需要更新父菜单
        Integer publishedCnt = apiDocumentArticleVO.getPublishedCnt();
        //需要更新父菜单
        if(1 == publishedCnt){
            updateParentPublicCnt(apiDocumentArticleVO.getParentMenuId(),publishedCnt);
        }
        //更新菜单
        ApiDocumentMenuVO apiDocumentMenuVO = new ApiDocumentMenuVO();
        BeanUtils.copyProperties(apiDocumentArticleVO, apiDocumentMenuVO);
        apiDocumentMenuVO.setType("article");
        apiDocumentMenuVO.setParentId(apiDocumentArticleVO.getParentMenuId());
        Long articleId = apiDocumentMenuService.addApiDocumentMenu(apiDocumentMenuVO);
        //更新文章
        ApiDocumentArticlePO apiDocumentArticlePO = new ApiDocumentArticlePO();
        BeanUtils.copyProperties(apiDocumentArticleVO,apiDocumentArticlePO);
        apiDocumentArticlePO.setApiDocumentMenuId(articleId);
        mongoTemplate.insert(apiDocumentArticlePO);
        return articleId;
    }

    @Override
    public ApiDocumentArticleVO getArticleById(Long id ,Integer systemType) {
        systemType = Objects.isNull(systemType) ? OpenApiTypeEnum.ORIGINAL.getCode() : systemType;
        Query query = new Query();
        query.addCriteria(Criteria.where("apiDocumentMenuId").is(id)).addCriteria(Criteria.where("envType").is(systemType));
        ApiDocumentArticlePO articlePO = mongoTemplate.findOne(query, ApiDocumentArticlePO.class);
        ApiDocumentMenuPO apiDocumentMenuPO = apiDocumentMenuService.selectByKey(id);
        if (apiDocumentMenuPO == null || articlePO == null) {
            return null;
        }
        ApiDocumentArticleVO apiDocumentArticleVO = new ApiDocumentArticleVO();
        BeanUtils.copyProperties(apiDocumentMenuPO,apiDocumentArticleVO);
        BeanUtils.copyProperties(articlePO,apiDocumentArticleVO);
        apiDocumentArticleVO.setCreateTime(apiDocumentMenuPO.getCreateTime());
        apiDocumentArticleVO.setUpdateTime(apiDocumentMenuPO.getUpdateTime());
        return apiDocumentArticleVO;
    }

    /**
     * 删除文章
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteArticleById(Long id, Integer systemType) {
        systemType = Objects.isNull(systemType) ? OpenApiTypeEnum.ORIGINAL.getCode() : systemType;
        //判断是否需要更新父菜单发布计数
        ApiDocumentMenuPO apiDocumentMenuPO = apiDocumentMenuService.selectByKey(id);
        if (Objects.isNull(apiDocumentMenuPO) || !Objects.equals(apiDocumentMenuPO.getEnvType(), systemType)) {
            throw new ComException(UNKNOWN_ERROR,"无此文章或文章类型不匹配");
        }
        Integer publishedCnt = apiDocumentMenuPO.getPublishedCnt();
        //如果原先是发布状态，则需要更新父菜单发布计数
        if(publishedCnt == 1){
            updateParentPublicCnt(apiDocumentMenuPO.getParentId(),-1);
        }
        //先删除文章对应的标题
        apiDocumentMenuService.delete(id);
        //再删除文章
        Query query = new Query();
        query.addCriteria(Criteria.where("apiDocumentMenuId").is(id)).addCriteria(Criteria.where("envType").is(systemType));
        mongoTemplate.remove(query,ApiDocumentArticlePO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateArticle(ApiDocumentArticleVO apiDocumentArticleVO) {
        //判断是否需要更新父菜单的发布计数，并进行更新
        Integer publishedChange = judgePublishedChange(apiDocumentArticleVO);
        if( -1 == publishedChange || 1 == publishedChange ){
            updateParentPublicCnt(apiDocumentArticleVO.getParentMenuId(),publishedChange);
        }

        //先更新标题
        ApiDocumentMenuPO apiDocumentMenuPO = new ApiDocumentMenuPO();
        BeanUtils.copyProperties(apiDocumentArticleVO, apiDocumentMenuPO);
        apiDocumentMenuPO.setUpdateTime(LocalDateTime.now());
        apiDocumentMenuService.updateNotNull(apiDocumentMenuPO);
        //再更新文章
        ApiDocumentArticlePO apiDocumentArticlePO = new ApiDocumentArticlePO();
        BeanUtils.copyProperties(apiDocumentArticleVO, apiDocumentArticlePO);
        Query query = new Query();
        query.addCriteria(Criteria.where("apiDocumentMenuId").is(apiDocumentArticlePO.getApiDocumentMenuId()));
        Update update = new Update();
        //这里支持内容和发布状态的修改
        if(apiDocumentArticlePO.getContent() != null){
            update.set("content",apiDocumentArticlePO.getContent());
        }
        if(apiDocumentArticlePO.getPublished() != null){
            update.set("published",apiDocumentArticlePO.getPublished());
        }
        mongoTemplate.upsert(query,update,ApiDocumentArticlePO.class);
    }

    @Override
    public void deleteAllArticleById(List<Long> ids, Integer systemType) {
        systemType = Objects.isNull(systemType) ? OpenApiTypeEnum.ORIGINAL.getCode() : systemType;
        Query query = new Query();
        query.addCriteria(Criteria.where("apiDocumentMenuId").in(ids)).addCriteria(Criteria.where("envType").is(systemType));
        mongoTemplate.remove(query,ApiDocumentArticlePO.class);
    }

    @Override
    public Set<Long> selectPublishArticleById() {
        Query query = new Query();
        query.addCriteria(Criteria.where("published").is(true));
        List<ApiDocumentArticlePO> apiDocumentArticlePOS = mongoTemplate.find(query, ApiDocumentArticlePO.class);
        Set<Long> result = new HashSet<>();
        apiDocumentArticlePOS.forEach(item->{
            result.add(item.getApiDocumentMenuId());
        });
        return result;
    }

    @Override
    public void syncArticle() {
        List<ApiDocumentArticlePO> all = mongoTemplate.findAll(ApiDocumentArticlePO.class, ApiDocumentArticlePO.COLLECTION_NAME);
        if (CollectionUtils.isNotEmpty(all)) {
            all.forEach(item -> {
                if (Objects.isNull(item.getEnvType())) {
                    item.setEnvType(OpenApiTypeEnum.ORIGINAL.getCode());
                    mongoTemplate.save(item, ApiDocumentArticlePO.COLLECTION_NAME);
                }
            });
        }
    }

    /**
     * 判断文章是否变更了发布状态。
     * -如果从未发布->发布  return 1
     * -如果从发布->发布 return 0
     * -如果没有变更，return 0
     * @param apiDocumentArticleVO
     * @return
     */
    private Integer judgePublishedChange(ApiDocumentArticleVO apiDocumentArticleVO) {
        Integer nowPublished = apiDocumentArticleVO.getPublishedCnt();
        ApiDocumentMenuPO apiDocumentMenuPO = apiDocumentMenuService.selectByKey(apiDocumentArticleVO.getApiDocumentMenuId());
        Integer oldPublished = apiDocumentMenuPO.getPublishedCnt();
        if(!nowPublished.equals(oldPublished)){
            if(1 == nowPublished){
                return  1;
            }else {
                return  0;
            }
        }

        return 0;
    }

    /**
     * 更新从 id以及id的父级菜单的发布计数
     * @param parentId
     * @param appendValue 追加的值
     */
    private void updateParentPublicCnt(Long parentId, Integer appendValue){
        List<Long> needUpdateList = new ArrayList<>();
        //递归多次查询出父级菜单Id（目前三级菜单，所以只需要查询1次即可）
        while(0 != parentId){
            needUpdateList.add(parentId);
            //获取父id
            ApiDocumentMenuPO temp = apiDocumentMenuService.selectByKey(parentId);
            parentId = temp.getParentId();
        }
        if(needUpdateList.size() > 0){
            apiDocumentMenuService.updatePublishedCntInList(needUpdateList, appendValue);
        }
    }

}
