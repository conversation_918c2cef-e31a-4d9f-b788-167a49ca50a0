package com.yiwise.core.service.engine.impl;

import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.dao.SmsJobMessagePOMapper;
import com.yiwise.core.dal.entity.SmsJobMessagePO;
import com.yiwise.core.dal.entity.SmsJobPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.model.enums.SendMessageStatusEnum;
import com.yiwise.core.model.enums.SmsJobStatusEnum;
import com.yiwise.core.model.vo.sms.SmsMessageDeleteVO;
import com.yiwise.core.service.engine.SmsJobMessageService;
import com.yiwise.core.service.engine.SmsJobService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName IntentMessageServiceImpl
 * <AUTHOR>
 * @Date 2018 7 31 16:23
 * @Version 1.0
 **/
@Service
public class SmsJobMessageServiceImpl extends BasicServiceImpl<SmsJobMessagePO> implements SmsJobMessageService {

    private static final Logger logger = LoggerFactory.getLogger(SmsJobMessageServiceImpl.class);

    @Resource
    private SmsJobMessagePOMapper smsJobMessagePOMapper;

    @Resource
    private SmsJobService smsJobService;

    @Override
    public int batchInsert(List<SmsJobMessagePO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return smsJobMessagePOMapper.batchInsert(list);
    }

    /**
     * TargetDataSource需要与Transactional在同一层，不然会因为方法调用的先后顺序，事务先于动态数据源生效，报错table not exists
     */
    @TargetDataSource(DataSourceEnum.POLARDB_MASTER)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<SmsJobMessagePO> selectAvailableListBySmsJobIdLimit(Long smsJobId) {
        return smsJobMessagePOMapper.selectAvailableListBySmsJobIdLimit(smsJobId);
    }

    @Override
    public Long countAvailableListBySmsJobId(Long smsJobId) {
        return smsJobMessagePOMapper.countAvailableListBySmsJobId(smsJobId);
    }

    @Override
    public void deleteSmsJobMessage(SmsMessageDeleteVO smsMessageDeleteVO) {
        Long smsJobId = smsMessageDeleteVO.getSmsJobId();
        List<Long> smsJobMessageIds = smsMessageDeleteVO.getSmsJobMessageIds();
        if (smsJobId==null && CollectionUtils.isEmpty(smsJobMessageIds)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "参数不能同时为null");
        }
        // 要删除的短信
        List<SmsJobMessagePO> smsJobMessageList;
        if (smsJobId!=null) {
            // 只有暂停的任务才能删除
            SmsJobPO smsJobPO = smsJobService.selectByKey(smsJobId);
            if (Objects.nonNull(smsJobPO) && (SmsJobStatusEnum.IN_PROCESS.equals(smsJobPO.getStatus()) || SmsJobStatusEnum.RUNNABLE.equals(smsJobPO.getStatus())
                    || SmsJobStatusEnum.IN_QUEUE.equals(smsJobPO.getStatus()))) {
                throw new ComException(ComErrorCode.VALIDATE_ERROR, "只有暂停的任务才能删除短信");
            }
            // TODO 短信不是很多，后面优化
            List<SmsJobMessagePO> smsJobMessagePOS = selectAvailableListBySmsJobIdLimit(smsJobId);
            while (!smsJobMessagePOS.isEmpty()) {
                logger.debug("短信全部删除，当前数据量={}", smsJobMessagePOS.size());
                smsJobMessagePOS.forEach(smsJobMessagePO -> {
                    if (!Objects.nonNull(smsJobMessagePO) || SendMessageStatusEnum.SEND_WAIT.equals(smsJobMessagePO.getSendStatus())) {
                                int count = delete(smsJobMessagePO.getSmsJobMessageId());
                                if (count>=0) {
                                    smsJobService.incrementJobStatusAfterImport(smsJobMessagePO.getTenantId(), smsJobId, -1L);
                                }
                            }
                });
                smsJobMessagePOS = selectAvailableListBySmsJobIdLimit(smsJobId);
            }
        } else {
            smsJobMessageList = selectListByKeyCollect(smsMessageDeleteVO.getSmsJobMessageIds());
            if (CollectionUtils.isNotEmpty(smsJobMessageList)) {
                SmsJobPO smsJobPO = smsJobService.selectByKey(smsJobMessageList.get(0).getSmsJobId());
                if (Objects.nonNull(smsJobPO) && (SmsJobStatusEnum.IN_PROCESS.equals(smsJobPO.getStatus()) || SmsJobStatusEnum.RUNNABLE.equals(smsJobPO.getStatus())
                        || SmsJobStatusEnum.IN_QUEUE.equals(smsJobPO.getStatus()))) {
                    throw new ComException(ComErrorCode.VALIDATE_ERROR, "只有暂停的任务才能删除短信");
                }
            }
            smsJobMessageList.forEach(smsJobMessagePO -> {
                if (!Objects.nonNull(smsJobMessagePO) || SendMessageStatusEnum.SEND_WAIT.equals(smsJobMessagePO.getSendStatus())) {
                    int delete = delete(smsJobMessagePO.getSmsJobMessageId());
                    if (delete > 0) {
                        smsJobService.incrementJobStatusAfterImport(smsJobMessagePO.getTenantId(), smsJobMessagePO.getSmsJobId(), -1L);
                    }
                }
            });
        }

    }

    @Override
    public Long selectIdBySid(String sid) {
        if (StringUtils.isBlank(sid)) {
            return null;
        }
        return smsJobMessagePOMapper.selectIdBySid(sid);
    }

	@Override
	public Long selectSmsJobMessageIdAfterLocalDateTime(LocalDateTime localDateTime) {
		return smsJobMessagePOMapper.selectSmsJobMessageIdAfterLocalDateTime(localDateTime);
	}

	@Override
	public Long selectSmsJobMessageIdBeforeLocalDateTime(LocalDateTime localDateTime) {
		Long smsJobMessageId = smsJobMessagePOMapper.selectSmsJobMessageIdAfterEqualLocalDateTime(localDateTime);
		if (smsJobMessageId != null) {
			smsJobMessageId--;
		}
		return smsJobMessageId;
	}

	@Override
	public List<SmsJobMessagePO> selectOrderBySmsJobMessageId(Long smsJobMessageId, Integer batchSize, Long lastSmsJobMessageId) {
		return smsJobMessagePOMapper.selectOrderBySmsJobMessageId(smsJobMessageId, batchSize, lastSmsJobMessageId);
	}
}
