package com.yiwise.core.service.ope.stats.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import com.yiwise.base.common.utils.bean.MyBeanUtils;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.service.BasicServiceImpl;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.excelimport.service.BatchJobInQueueService;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.dao.CustomerTrackTypePOMapper;
import com.yiwise.core.dal.dao.DataReportPOMapper;
import com.yiwise.core.dal.dao.TenantPOMapper;
import com.yiwise.core.dal.entity.CustomerTrackTypePO;
import com.yiwise.core.dal.entity.DataReportPO;
import com.yiwise.core.dal.entity.RobotCallJobPO;
import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.dal.mongo.*;
import com.yiwise.core.feignclient.v3bot.V3BotStatsClient;
import com.yiwise.core.model.bo.batch.SpringBatchJobBO;
import com.yiwise.core.model.bo.callstats.DataReportStatsBO;
import com.yiwise.core.model.dialogflow.dto.DialogFlowNodeDTO;
import com.yiwise.core.model.dialogflow.dto.DialogFlowStepListDTO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowStepPO;
import com.yiwise.core.model.dialogflow.entity.IntentBranchPO;
import com.yiwise.core.model.dialogflow.vo.StatsInfoVO;
import com.yiwise.core.model.enums.DataReportStatusEnum;
import com.yiwise.core.model.enums.SpringBatchJobTypeEnum;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.enums.dialogflow.*;
import com.yiwise.core.model.enums.ope.AccountTypeEnum;
import com.yiwise.core.model.po.DataReportResultIndustryPO;
import com.yiwise.core.model.po.DataReportResultTrackPO;
import com.yiwise.core.model.po.DataReportTenantPO;
import com.yiwise.core.model.po.DataReportTrackPO;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.datareport.DataReportQueryVO;
import com.yiwise.core.model.vo.datareport.DataReportSaveVO;
import com.yiwise.core.model.vo.dialogflownodestats.DialogFlowNodeFanOutStatsVO;
import com.yiwise.core.model.vo.dialogflownodestats.DialogFlowStatsQueryVO;
import com.yiwise.core.model.vo.dialogflownodestats.DialogFlowStepStatsVO;
import com.yiwise.core.model.vo.dialogflownodestats.DialogFlowTotalNodeStatsVO;
import com.yiwise.core.model.vo.tenant.TenantIdAndNamePairVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.dialogflow.DialogFlowIntentBranchService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.dialogflow.DialogFlowStepService;
import com.yiwise.core.service.engine.DialogFlowNodeStatsService;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.CustomerSceneService;
import com.yiwise.core.service.ope.platform.PhoneCardService;
import com.yiwise.core.service.ope.stats.DataReportService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.remote.V3StepStatsService;
import com.yiwise.core.util.JobParametersUtil;
import com.yiwise.dialogflow.api.dto.request.StepAggregationStatsRequest;
import javaslang.Tuple;
import javaslang.Tuple2;
import javaslang.Tuple3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.util.Lists;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.core.service.mongo.MongoCollectionNameCenter.CALL_STATS_ROBOT_DONE_JOB;


/**
 * <AUTHOR>
 */
@Service
public class DataReportServiceImpl extends BasicServiceImpl<DataReportPO> implements DataReportService {

    @Resource
    private DataReportPOMapper dataReportPOMapper;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private TenantPOMapper tenantPOMapper;

    @Resource
    private DialogFlowService dialogFlowService;

    @Resource
    private RobotCallJobService robotCallJobService;

    @Resource
    private PhoneCardService phoneCardService;

    @Resource
    private CustomerTrackTypePOMapper customerTrackTypePOMapper;

    @Resource
    private UserService userService;

    @Resource
    private BatchJobInQueueService batchJobInQueueService;

    @Resource
    private DialogFlowNodeStatsService dialogFlowNodeStatsService;

    @Resource
    private DialogFlowIntentBranchService dialogFlowIntentBranchService;

    @Resource
    private DialogFlowStepService dialogFlowStepService;

    @Resource
    private CustomerSceneService customerSceneService;

    @Resource(name = "dataReportExportJob")
    private Job dataReportExportJob;

    @Resource
    private V3BotStatsClient v3BotStatsClient;

    @Override
    public PageResultObject<DataReportSaveVO> listDataReport(DataReportQueryVO queryVO) {
        PageHelper.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<DataReportPO> dataReportPOList = dataReportPOMapper.selectAllDataReport(queryVO);
        PageInfo<DataReportPO> pageInfo = new PageInfo<>(dataReportPOList);
        List<DataReportSaveVO> resultList = MyBeanUtils.copyList(dataReportPOList, DataReportSaveVO.class);
        if (CollectionUtils.isNotEmpty(resultList)) {
            //查询条件数据
            List<DataReportQueryPO> dataReportQueryPOList = mongoTemplate.find(Query.query(Criteria.where("dataReportId").in(resultList.stream().map(DataReportPO::getDataReportId).collect(Collectors.toList()))), DataReportQueryPO.class);
            Map<Long, DataReportQueryPO> dataReportQueryPOMap = MyCollectionUtils.listToMap(dataReportQueryPOList, DataReportQueryPO::getDataReportId);
            //创建人姓名
            Set<Long> userIdSet = resultList.stream().map(DataReportSaveVO::getUserId).collect(Collectors.toSet());
            Map<? extends Serializable, UserPO> userPOMap = userService.selectMapByKeyCollect(userIdSet);

            resultList.forEach(data -> {
                DataReportQueryPO dataReportQueryPO = dataReportQueryPOMap.get(data.getDataReportId());
                if (Objects.nonNull(dataReportQueryPO)) {
                    //填充查询条件数据
                    data.setDataReportTrackPOList(dataReportQueryPO.getDataReportTrackPOList());
                }

                UserPO userPO = userPOMap.get(data.getUserId());
                if (Objects.nonNull(userPO)) {
                    //填充创建人姓名
                    data.setUserName(userPO.getName());
                }
            });
        }

        PageResultObject<DataReportSaveVO> pageResultObject = PageResultObject.of(resultList);
        pageResultObject.setPageSize(pageInfo.getPageSize());
        pageResultObject.setPages(pageInfo.getPages());
        pageResultObject.setNumber(pageInfo.getPageNum());
        pageResultObject.setTotalElements(pageInfo.getTotal());

        return pageResultObject;
    }

    @Override
    public void addDataReport(DataReportSaveVO saveVO) {
        //保存报表基本信息
        DataReportPO dataReportPO = MyBeanUtils.copy(saveVO, DataReportPO.class);
        saveNotNull(dataReportPO);
        //保存报表的查询条件
        DataReportQueryPO dataReportQueryPO = new DataReportQueryPO();
        dataReportQueryPO.setDataReportId(dataReportPO.getDataReportId());
        dataReportQueryPO.setDataReportTrackPOList(saveVO.getDataReportTrackPOList());
        mongoTemplate.save(dataReportQueryPO);
    }

    @Override
    public void editDataReport(DataReportSaveVO saveVO) {
        Assert.notNull(saveVO.getDataReportId(), "报表ID不能为空");
        //更新报表基本信息
        DataReportPO update = MyBeanUtils.copy(saveVO, DataReportPO.class);
        update.setStatus(DataReportStatusEnum.NOT_GENERATE);
        updateNotNull(update);
        //更新报表查询条件
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(saveVO.getDataReportId())), DataReportQueryPO.class);
        DataReportQueryPO dataReportQueryPO = new DataReportQueryPO();
        dataReportQueryPO.setDataReportId(saveVO.getDataReportId());
        dataReportQueryPO.setDataReportTrackPOList(saveVO.getDataReportTrackPOList());
        mongoTemplate.save(dataReportQueryPO);
        //删除报表查询数据
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(saveVO.getDataReportId())), DataReportDataPO.class);
        //删除报表
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(saveVO.getDataReportId())), DataReportResultPO.class);
    }

    @Override
    public void copyDataReport(Long dataReportId, Long userId) {
        Assert.notNull(dataReportId, "报表ID不能为空");
        //复制报表基本信息
        DataReportPO dataReportPO = selectByKey(dataReportId);
        dataReportPO.setDataReportId(null);
        dataReportPO.setName(dataReportPO.getName() + "（复制）");
        dataReportPO.setStatus(DataReportStatusEnum.NOT_GENERATE);
        dataReportPO.setUserId(userId);
        saveNotNull(dataReportPO);
        //复制报表的查询条件
        DataReportQueryPO dataReportQueryPO = mongoTemplate.findOne(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportQueryPO.class);
        dataReportQueryPO.setId(null);
        dataReportQueryPO.setDataReportId(dataReportPO.getDataReportId());
        mongoTemplate.save(dataReportQueryPO);
    }

    @Override
    public void deleteDataReport(Long dataReportId) {
        Assert.notNull(dataReportId, "报表ID不能为空");
        //删除报表基本数据
        delete(dataReportId);
        //删除报表的查询条件
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportQueryPO.class);
        //删除报表的查询数据
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportDataPO.class);
        //删除报表
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportResultPO.class);
    }

    @Override
    public List<DataReportResultTrackPO> viewDataReport(Long dataReportId) {
        DataReportPO dataReportPO = selectByKey(dataReportId);
        if (DataReportStatusEnum.IN_PROCESS.equals(dataReportPO.getStatus())) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "报表正在生成中，请稍后查看");
        }
        if (DataReportStatusEnum.NOT_GENERATE.equals(dataReportPO.getStatus())) {
            //更新数据报表为生成中
            DataReportPO update = new DataReportPO();
            update.setDataReportId(dataReportId);
            update.setStatus(DataReportStatusEnum.IN_PROCESS);
            updateNotNull(update);

            //生成报表的查询数据
            generateDataReport(dataReportId);
            //生成报表统计数据
            doGenerateDataReportResult(dataReportId);

            //更新数据报表为已生成
            update.setStatus(DataReportStatusEnum.GENERATED);
            updateNotNull(update);
        }
        //查询报表
        DataReportResultPO dataReportResultPO = mongoTemplate.findOne(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportResultPO.class);
        if (Objects.nonNull(dataReportResultPO) && CollectionUtils.isNotEmpty(dataReportResultPO.getDataReportResultTrackPOList())) {
            List<DataReportResultTrackPO> resList = dataReportResultPO.getDataReportResultTrackPOList().stream().filter(c -> Objects.isNull(c.getCustomerTrackParentType())).collect(Collectors.toList());
            //获取二级赛道的一级赛道列表
            Set<Integer> trackParentTypeList = dataReportResultPO.getDataReportResultTrackPOList().stream().map(DataReportResultTrackPO::getCustomerTrackParentType).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(trackParentTypeList)) {
                customerTrackTypePOMapper.selectByCustomerTrackTypeList(trackParentTypeList).forEach(c -> {
                    DataReportResultTrackPO dataReportResultTrackPO = new DataReportResultTrackPO();
                    dataReportResultTrackPO.setCustomerTrackTypeName(c.getCustomerTrackTypeName());
                    dataReportResultTrackPO.setSubDataReportResultTrackList(dataReportResultPO.getDataReportResultTrackPOList().stream().filter(cc -> Objects.equals(c.getCustomerTrackType(), cc.getCustomerTrackParentType())).collect(Collectors.toList()));
                    resList.add(dataReportResultTrackPO);
                });
            }
            return resList;
        }
        return Collections.emptyList();
    }

    @Override
    public void generateDataReport(Long dataReportId) {
        Assert.notNull(dataReportId, "数据报表ID不能为空");
        DataReportPO dataReportPO = selectByKey(dataReportId);
        //获取报表的查询条件
        DataReportQueryPO dataReportQueryPO = mongoTemplate.findOne(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportQueryPO.class);
        if (Objects.nonNull(dataReportQueryPO) && Objects.nonNull(dataReportPO) && DataReportStatusEnum.IN_PROCESS.equals(dataReportPO.getStatus())) {
            //删除报表历史数据
            mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportDataPO.class);

            //根据查询条件生成数据存储到报表中
            dataReportQueryPO.getDataReportTrackPOList().forEach(track -> {
                //有二级赛道取二级赛道的值， 没有二级赛道取一级赛道，最终数据是以customerTrackType进行group by查询
                Integer customerTrackType = Objects.nonNull(track.getCustomerTrackSubTypeId()) ? track.getCustomerTrackSubTypeId() : track.getCustomerTrackTypeId();
                if (BooleanUtils.isTrue(track.getIsAllTenant())) {
                    //查询该赛道下所有的客户
                    List<Long> selectTenantIdList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(track.getDataReportTenantPOList())) {
                        //选择了任务或话术的客户
                        selectTenantIdList = track.getDataReportTenantPOList().stream().map(DataReportTenantPO::getTenantId).collect(Collectors.toList());
                        doDealDataReportTenant(track, dataReportId, dataReportPO.getBeginDate(), dataReportPO.getEndDate());
                    }
                    List<Long> tenantIdList;
                    int pageNum = 1;
                    boolean isLoop;
                    DirectCustomerQueryVO queryVO = new DirectCustomerQueryVO();
                    queryVO.setCustomerTrackType(track.getCustomerTrackTypeId());
                    queryVO.setCustomerTrackSubType(track.getCustomerTrackSubTypeId());
                    queryVO.setAccountTypes(Lists.newArrayList(AccountTypeEnum.FORMAL, AccountTypeEnum.TRAIL_TO_FORMAL));
                    do {
                        PageHelper.startPage(pageNum++, 100);
                        tenantIdList = tenantPOMapper.selectDirectTenantByCustomerTrackType(queryVO).stream().map(TenantIdAndNamePairVO::getTenantId).collect(Collectors.toList());
                        isLoop = CollectionUtils.isNotEmpty(tenantIdList);
                        if (CollectionUtils.isNotEmpty(tenantIdList)) {
                            if (CollectionUtils.isNotEmpty(selectTenantIdList)) {
                                //去除选择了话术或任务的客户
                                tenantIdList.removeAll(selectTenantIdList);
                            }
                            //全选的客户
                            doGenerateDataReportData(customerTrackType, dataReportId, tenantIdList, null, dataReportPO.getBeginDate(), dataReportPO.getEndDate());
                        }
                    } while (isLoop);
                } else if (CollectionUtils.isNotEmpty(track.getDataReportTenantPOList())) {
                    //选择该赛道下的部分客户
                    doDealDataReportTenant(track, dataReportId, dataReportPO.getBeginDate(), dataReportPO.getEndDate());
                }
            });
        }
    }

    @Override
    public JobStartResultVO exportDataReport(Long distributorId, Long userId, Long dataReportId) {

        SpringBatchJobTypeEnum jobType = SpringBatchJobTypeEnum.EXPORT_DATA_REPORT;
        SystemEnum systemType = SystemEnum.OPE;

        DataReportPO dataReportPO = selectByKey(dataReportId);
        List<Tuple3<String, String, Object>> params = com.google.common.collect.Lists.newArrayList(
                Tuple.of("DATA_REPORT_ID", "Long", dataReportId),
                Tuple.of("STEP_COUNT", "Long", Objects.isNull(dataReportPO.getStepCount()) ? null : (long) dataReportPO.getStepCount())
        );

        long now = System.currentTimeMillis();
        String baseName = jobType.getDesc() + "-" + now;
        String ossFileKey = OssKeyCenter.getBossExcelOssFileKey(jobType.getDesc(), distributorId, baseName);
        String exportFilePath = TempFilePathKeyCenter.getExcelTempFilePath(ossFileKey);

        JobParameters jobParameters = JobParametersUtil.builder()
                .addDatasource(DataSourceEnum.SLAVE)
                .addDistributorId(distributorId)
                .addCurrentUserId(userId)
                .addSystemType(systemType)
                .addJobType(jobType)
                .addTime()
                .addExportFilePath(exportFilePath)
                .addOssFileKey(ossFileKey)
                .addHeaderList(jobType.getHeaderList())
                .addParams(params)
                .addSheetInfoDTOList(BatchConstant.SheetInfoList.DATA_REPORT_EXPORT_TUPLE_LIST)
                .build();

        SpringBatchJobBO springBatchJobBO = SpringBatchJobBO.builder()
                .setJob(dataReportExportJob)
                .setJobParameters(jobParameters)
                .setJobName(jobType.getDesc())
                .setCurrentUserId(userId)
                .setJobType(jobType)
                .setSystemType(systemType)
                .setWithTransaction(false)
                .build();

        return batchJobInQueueService.runBatchWithQueue(springBatchJobBO);
    }

    /**
     * 处理选择部分客户的数据
     * @param track
     * @param dataReportId
     * @param beginDate
     * @param endDate
     */
    private void doDealDataReportTenant(DataReportTrackPO track, Long dataReportId, LocalDate beginDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(track.getDataReportTenantPOList())) {
            return;
        }
        //有二级赛道取二级赛道的值， 没有二级赛道取一级赛道，最终数据是以customerTrackType进行group by查询
        Integer customerTrackType = Objects.nonNull(track.getCustomerTrackSubTypeId()) ? track.getCustomerTrackSubTypeId() : track.getCustomerTrackTypeId();
        List<Long> tenantIdList = track.getDataReportTenantPOList().stream().filter(t -> BooleanUtils.isTrue(t.getIsAll())).map(DataReportTenantPO::getTenantId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tenantIdList)) {
            //选择客户下面所有的任务或话术
            doGenerateDataReportData(customerTrackType, dataReportId, tenantIdList, null, beginDate, endDate);
        }
        track.getDataReportTenantPOList().forEach(tenant -> {
            if (CollectionUtils.isNotEmpty(tenant.getDialogFlowList())) {
                List<RobotCallJobPO> robotCallJobPOList = robotCallJobService.selectByDialogFlowIdList(tenant.getTenantId(), tenant.getDialogFlowList().stream().map(StatsInfoVO::getId).collect(Collectors.toList()));
                doGenerateDataReportData(customerTrackType, dataReportId, null, robotCallJobPOList.stream().map(RobotCallJobPO::getRobotCallJobId).collect(Collectors.toList()), beginDate, endDate);
            }
            if (CollectionUtils.isNotEmpty(tenant.getRobotCallJobList())) {
                doGenerateDataReportData(customerTrackType, dataReportId, null, tenant.getRobotCallJobList().stream().map(StatsInfoVO::getId).collect(Collectors.toList()), beginDate, endDate);
            }
        });
    }

    /**
     * 根据客户ID列表或任务ID列表生成报表的查询数据
     * @param customerTrackTypeId
     * @param dataReportId
     * @param tenantIdList
     * @param robotCallJobIdList
     * @param beginDate
     * @param endDate
     */
    private void doGenerateDataReportData(Integer customerTrackTypeId, Long dataReportId, List<Long> tenantIdList, List<Long> robotCallJobIdList, LocalDate beginDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(tenantIdList) && CollectionUtils.isEmpty(robotCallJobIdList)) {
            return;
        }
        List<DataReportDataPO> dataReportDataPOList = new ArrayList<>();
        //根据任务统计数据生成报表查询数据
        List<DataReportStatsBO> dataReportStatsBOList = queryCallJobStats(tenantIdList, robotCallJobIdList, beginDate, endDate);
        List<Long> robotCallJobIds = dataReportStatsBOList.stream().map(DataReportStatsBO::getCallStatsId).collect(Collectors.toList());
        List<RobotCallJobPO> robotCallJobPOList = robotCallJobService.selectListByKeyCollect(robotCallJobIds);
        Map<Long, RobotCallJobPO> robotCallJobPOMap = MyCollectionUtils.listToMap(robotCallJobPOList, RobotCallJobPO::getRobotCallJobId);
        List<Long> dialogFlowIds = robotCallJobPOList.stream().map(RobotCallJobPO::getDialogFlowId).distinct().collect(Collectors.toList());
        Map<? extends Serializable, DialogFlowInfoPO> dialogFlowInfoPOMap = dialogFlowService.selectMapByKeyCollect(dialogFlowIds);
        Map<Integer, String> sceneMap = customerSceneService.getSceneId2NameMap(Collections.emptyList());

        //加微通过人数
        List<DataReportStatsBO> wechatStatsList = queryWechatStats(tenantIdList, robotCallJobIdList, beginDate, endDate);
        Map<Long, DataReportStatsBO> wechatStatsMap = MyCollectionUtils.listToMap(wechatStatsList, DataReportStatsBO::getCallStatsId);

        dataReportStatsBOList.forEach(data -> {
            DataReportDataPO dataReportDataPO = MyBeanUtils.copy(data, DataReportDataPO.class);
            dataReportDataPO.setDataReportId(dataReportId);
            dataReportDataPO.setCustomerTrackTypeId(customerTrackTypeId);

            //保存加微通过人数
            DataReportStatsBO wechatStats = wechatStatsMap.get(data.getCallStatsId());
            if (Objects.nonNull(wechatStats)) {
                dataReportDataPO.setSendAddFriendAdoptCount(wechatStats.getSendAddFriendAdoptCount());
                //配置自动加微的任务
                dataReportDataPO.setType(1);
            }

            RobotCallJobPO robotCallJobPO = robotCallJobPOMap.get(data.getCallStatsId());
            if (Objects.nonNull(robotCallJobPO)) {
                DialogFlowInfoPO dialogFlowInfoPO = dialogFlowInfoPOMap.get(robotCallJobPO.getDialogFlowId());
                if (Objects.nonNull(dialogFlowInfoPO)) {
                    dataReportDataPO.setIndustry(dialogFlowInfoPO.getIndustry().name());
                    dataReportDataPO.setSubIndustry(dialogFlowInfoPO.getSubIndustry().name());
                    dataReportDataPO.setCustomerSceneName(sceneMap.getOrDefault(dialogFlowInfoPO.getCustomerSceneId(), null));
                    dataReportDataPOList.add(dataReportDataPO);
                }
            }
        });


        if (CollectionUtils.isNotEmpty(dataReportDataPOList)) {
            //保存报表查询数据
            mongoTemplate.insertAll(dataReportDataPOList);
        }
    }

    /**
     * 生成数据报表
     * @param dataReportId
     */
    private void doGenerateDataReportResult(Long dataReportId) {
        //删除报表历史数据
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportResultPO.class);

        DataReportPO dataReportPO = selectByKey(dataReportId);
        DataReportQueryPO dataReportQueryPO = mongoTemplate.findOne(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportQueryPO.class);

        List<DataReportDataPO> dataReportDataPOList = queryDataReportStats(dataReportId, null);
        List<DataReportDataPO> wechatDataReportDataPOList = queryDataReportStats(dataReportId, 1);

        DataReportResultPO dataReportResultPO = new DataReportResultPO();
        dataReportResultPO.setDataReportId(dataReportId);
        List<DataReportResultTrackPO> dataReportResultTrackPOList = new ArrayList<>();
        dataReportResultPO.setDataReportResultTrackPOList(dataReportResultTrackPOList);
        List<CustomerTrackTypePO> trackTypeList = customerTrackTypePOMapper.selectAll();
        Map<Integer, CustomerTrackTypePO> trackTypeMap = MyCollectionUtils.listToMap(trackTypeList, CustomerTrackTypePO::getCustomerTrackType);
        Map<Integer, List<DataReportDataPO>> trackMap = dataReportDataPOList.stream().collect(Collectors.groupingBy(DataReportDataPO::getCustomerTrackTypeId));
        Map<Integer, String> sceneMap = customerSceneService.getSceneId2NameMap(Collections.emptyList());

        // 用于获取每个赛道的客户ID-话术ID
        Set<Long> dialogFlowIdSet = Sets.newHashSet();
        List<Tuple3<Integer, Long, Long>> tupleList = com.google.common.collect.Lists.newArrayList();
        if (Objects.nonNull(dataReportQueryPO) && CollectionUtils.isNotEmpty(dataReportQueryPO.getDataReportTrackPOList())) {
            dataReportQueryPO.getDataReportTrackPOList()
                    .stream()
                    .filter(item -> CollectionUtils.isNotEmpty(item.getDataReportTenantPOList()))
                    .forEach(dataReportTrackPO -> {
                        if (CollectionUtils.isNotEmpty(dataReportTrackPO.getDataReportTenantPOList())) {
                            Integer customerTrackType = Objects.nonNull(dataReportTrackPO.getCustomerTrackSubTypeId()) ? dataReportTrackPO.getCustomerTrackSubTypeId() : dataReportTrackPO.getCustomerTrackTypeId();
                            dataReportTrackPO.getDataReportTenantPOList()
                                    .stream()
                                    .filter(item -> CollectionUtils.isNotEmpty(item.getDialogFlowList()))
                                    .forEach(dataReportTenantPO -> {
                                        List<Long> dialogFlowIdList = dataReportTenantPO.getDialogFlowList()
                                                .stream()
                                                .map(StatsInfoVO::getId)
                                                .collect(Collectors.toList());
                                        dialogFlowIdSet.addAll(dialogFlowIdList);
                                        dialogFlowIdList.forEach(dialogFlowId -> {
                                            tupleList.add(Tuple.of(customerTrackType, dataReportTenantPO.getTenantId(), dialogFlowId));
                                        });
                                    });
                        }
                    });
        }
        List<DialogFlowInfoPO> dialogFlowInfoPOList = dialogFlowService.selectListByKeyCollect(dialogFlowIdSet);

        Set<Long> v3DialogFlowIdSet = dialogFlowInfoPOList.stream()
                .filter(item -> BotTypeEnum.V3.equals(item.getBotType()))
                .map(DialogFlowInfoPO::getId)
                .collect(Collectors.toSet());

        // 话术ID-行业
        if (CollectionUtils.isEmpty(dialogFlowInfoPOList)) {
            return;
        }
        Map<Long, String> dialogFlowSceneMap = dialogFlowInfoPOList.stream().filter(k -> Objects.nonNull(k.getCustomerSceneId())).collect(Collectors.toMap(DialogFlowInfoPO::getId, k -> sceneMap.get(k.getCustomerSceneId())));
        // <赛道ID，场景> - <客户ID, 话术ID>
        Map<Tuple2<Integer, String>, List<Tuple2<Long, Long>>> subSceneTenantMap = tupleList.stream().collect(
                Collectors.toMap(
                        tuple -> Tuple.of(tuple._1, dialogFlowSceneMap.get(tuple._3)),
                        tuple -> com.google.common.collect.Lists.newArrayList(Tuple.of(tuple._2, tuple._3)),
                        MyCollectionUtils::merge
                )
        );

        trackMap.keySet().forEach(track -> {
            DataReportResultTrackPO dataReportResultTrackPO = new DataReportResultTrackPO();
            CustomerTrackTypePO trackType = trackTypeMap.get(track);
            if (Objects.nonNull(trackType)) {
                dataReportResultTrackPO.setCustomerTrackType(trackType.getCustomerTrackType());
                dataReportResultTrackPO.setCustomerTrackTypeName(trackType.getCustomerTrackTypeName());
                dataReportResultTrackPO.setCustomerTrackParentType(trackType.getCustomerTrackParentType());
            }
            List<DataReportResultIndustryPO> dataReportResultIndustryPOList = new ArrayList<>();
            dataReportResultTrackPO.setDataReportResultIndustryPOList(dataReportResultIndustryPOList);

            Map<String, List<DataReportDataPO>> subSceneMap = trackMap.get(track).stream().filter(p -> Objects.nonNull(p.getCustomerSceneName())).collect(Collectors.groupingBy(DataReportDataPO::getCustomerSceneName));
            for (String sceneName : subSceneMap.keySet()) {
                DataReportResultIndustryPO industryPO = new DataReportResultIndustryPO();
                industryPO.setIndustry(sceneName);
                industryPO.setCustomerTrackTypeName(dataReportResultTrackPO.getCustomerTrackTypeName());
                dealTenantDataReportData(subSceneMap.get(sceneName), wechatDataReportDataPOList.stream().filter(w -> sceneName.equals(w.getCustomerSceneName()) && Objects.equals(track, w.getCustomerTrackTypeId())).collect(Collectors.toList()), industryPO);

                // 以是否传入 stepCount 作为判断是否开启话术统计的依据
                if (MapUtils.isNotEmpty(subSceneTenantMap) && Objects.nonNull(dataReportPO.getStepCount())) {
                    List<Tuple2<Long, Long>> subIndustryTupleList = subSceneTenantMap.get(Tuple.of(track, sceneName));
                    if (CollectionUtils.isNotEmpty(subIndustryTupleList)) {
                        // 生成话术统计数据
                        List<DataReportStepDataPO> dataReportStepDataPOList = generateDialogFlowDataReportData(subIndustryTupleList, v3DialogFlowIdSet, dataReportPO);
                        industryPO.setDataReportStepDataPOList(dataReportStepDataPOList);
                    }
                }

                dataReportResultIndustryPOList.add(industryPO);
            }
            dataReportResultTrackPOList.add(dataReportResultTrackPO);
        });
        mongoTemplate.save(dataReportResultPO);

        //删除报表历史数据
        mongoTemplate.remove(Query.query(Criteria.where("dataReportId").is(dataReportId)), DataReportDataPO.class);
    }

    /**计算最大值，最小值和平均值
     * @param tenantDataList
     * @param industryPO
     */
    private void dealTenantDataReportData(List<DataReportDataPO> tenantDataList, List<DataReportDataPO> wechatTenantDataList, DataReportResultIndustryPO industryPO) {
        tenantDataList.forEach(tenant -> {
            //维护接通率
            Integer answeredRate = calculateRateInteger(tenant.getAnsweredCall(), tenant.getTaskTotalCompleted());
            if (Objects.nonNull(answeredRate)) {
                if (Objects.isNull(industryPO.getMaxAnsweredRate()) || answeredRate > industryPO.getMaxAnsweredRate()) {
                    industryPO.setMaxAnsweredRate(answeredRate);
                }
                if (Objects.isNull(industryPO.getMinAnsweredRate()) || answeredRate < industryPO.getMinAnsweredRate()) {
                    industryPO.setMinAnsweredRate(answeredRate);
                }
            }
            //维护AB类意向占比
            Integer aBRate = calculateRateInteger(tenant.getIntentLevel0() + tenant.getIntentLevel1(), tenant.getAnsweredCall());
            if (Objects.nonNull(aBRate)) {
                if (Objects.isNull(industryPO.getMaxAbRate()) || aBRate > industryPO.getMaxAbRate()) {
                    industryPO.setMaxAbRate(aBRate);
                }
                if (Objects.isNull(industryPO.getMinAbRate()) || aBRate < industryPO.getMinAbRate()) {
                    industryPO.setMinAbRate(aBRate);
                }
            }
        });

        //配置自动加微的外呼任务
        wechatTenantDataList.forEach(tenant -> {
            //维护意向转化
            Integer intentRate = calculateRateInteger(tenant.getSendAddFriendAdoptCount(), tenant.getIntentLevel0() + tenant.getIntentLevel1());
            if (Objects.nonNull(intentRate)) {
                if (Objects.isNull(industryPO.getMaxIntentRate()) || intentRate > industryPO.getMaxIntentRate()) {
                    industryPO.setMaxIntentRate(intentRate);
                }
                if (Objects.isNull(industryPO.getMinIntentRate()) || intentRate < industryPO.getMinIntentRate()) {
                    industryPO.setMinIntentRate(intentRate);
                }
            }

            //维护接通转化
            Integer answeredTransformRate = calculateRateInteger(tenant.getSendAddFriendAdoptCount(), tenant.getAnsweredCall());
            if (Objects.nonNull(answeredTransformRate)) {
                if (Objects.isNull(industryPO.getMaxAnsweredTransformRate()) || answeredTransformRate > industryPO.getMaxAnsweredTransformRate()) {
                    industryPO.setMaxAnsweredTransformRate(answeredTransformRate);
                }
                if (Objects.isNull(industryPO.getMinAnsweredTransformRate()) || answeredTransformRate < industryPO.getMinAnsweredTransformRate()) {
                    industryPO.setMinAnsweredTransformRate(answeredTransformRate);
                }
            }
        });

        int taskTotalCompletedTotal = tenantDataList.stream().mapToInt(DataReportDataPO::getTaskTotalCompleted).sum();
        int answeredCallTotal = tenantDataList.stream().mapToInt(DataReportDataPO::getAnsweredCall).sum();
        int intentLevelTotal = tenantDataList.stream().mapToInt(DataReportDataPO::getIntentLevel0).sum() + tenantDataList.stream().mapToInt(DataReportDataPO::getIntentLevel1).sum();
        //平均接通率
        industryPO.setAvgAnsweredRate(calculateRateInteger(answeredCallTotal, taskTotalCompletedTotal));
        //平均AB类意向占比
        industryPO.setAvgAbRate(calculateRateInteger(intentLevelTotal, answeredCallTotal));



        //配置自动加微的外呼任务
        int sendAddFriendAdoptCountTotal = wechatTenantDataList.stream().mapToInt(DataReportDataPO::getSendAddFriendAdoptCount).sum();
        int wechatAnsweredCallTotal = wechatTenantDataList.stream().mapToInt(DataReportDataPO::getAnsweredCall).sum();
        int wechatIntentLevelTotal = wechatTenantDataList.stream().mapToInt(DataReportDataPO::getIntentLevel0).sum() + wechatTenantDataList.stream().mapToInt(DataReportDataPO::getIntentLevel1).sum();
        //平均意向转化
        industryPO.setAvgAnsweredTransformRate(calculateRateInteger(sendAddFriendAdoptCountTotal, wechatAnsweredCallTotal));
        //平均接通转化
        industryPO.setAvgIntentRate(calculateRateInteger(sendAddFriendAdoptCountTotal, wechatIntentLevelTotal));

    }

    /**
     * 按照话术类型, 对请求进行分流, 其中v3的话术需要远程调用接口获取数据, v1的话术则使用原有的逻辑
     * todo 目前会多次请求v3的接口
     * @param subIndustryTupleList
     * @param v3DialogFlowIdSet
     * @param dataReportPO
     * @return
     */
    private List<DataReportStepDataPO> generateDialogFlowDataReportData(List<Tuple2<Long, Long>> subIndustryTupleList,
                                                                        Set<Long> v3DialogFlowIdSet,
                                                                        DataReportPO dataReportPO) {

        // 对请求进行分流
        List<Tuple2<Long, Long>> v3List = new ArrayList<>(subIndustryTupleList.size());
        List<Tuple2<Long, Long>> oldDialogList = new ArrayList<>(subIndustryTupleList.size());

        subIndustryTupleList.forEach(tuple -> {
            if (v3DialogFlowIdSet.contains(tuple._2())) {
                v3List.add(tuple);
            } else {
                oldDialogList.add(tuple);
            }
        });

        List<DataReportStepDataPO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(v3List)) {
            result.addAll(doGenerateV3DialogFlowDataReportData(v3List, dataReportPO));
        }
        if (CollectionUtils.isNotEmpty(oldDialogList)) {
            result.addAll(doGenerateDialogFlowDataReportData(oldDialogList, dataReportPO));
        }
        return result;
    }

    private List<DataReportStepDataPO> doGenerateV3DialogFlowDataReportData(List<Tuple2<Long, Long>> v3List, DataReportPO dataReportPO) {
        if (CollectionUtils.isEmpty(v3List)) {
            return Collections.emptyList();
        }
        StepAggregationStatsRequest request = new StepAggregationStatsRequest();
        request.setBeginDate(dataReportPO.getBeginDate());
        request.setEndDate(dataReportPO.getEndDate());
        request.setStepCount(dataReportPO.getStepCount());
        Map<Long, Long> tenantDialogFlowIdPairMap = new HashMap<>();
        for (Tuple2<Long, Long> tuple : v3List) {
            tenantDialogFlowIdPairMap.put(tuple._1, tuple._2);
        }
        request.setTenantDialogFlowIdPairMap(tenantDialogFlowIdPairMap);
        return v3BotStatsClient.generateDialogFlowDataReportData(request).stream()
                .map(item -> MyBeanUtils.copy(item, DataReportStepDataPO.class))
                .collect(Collectors.toList());
    }

    /**
     * 处理话术流程统计数据
     * - 流程到达率（平均值）：所有话术下到达第一个流程的通话数/所有话术下接通的通话数
     * - 流程到达率（最高/最低值）：以话术为单元聚合统计，最高/最低值即流程到达率最高/最低的话术的值
     * - 节点挂机率（平均值）：所有话术下该节点客户主动挂断次数/该节点所有走向次数（分支+客户主动挂机+问答知识）
     * - 节点挂机率（最高/最低值）：以话术为单元聚合统计，最高/最低值即节点挂机率最高/最低的话术的值
     * - 节点拒绝率（平均值）：所有话术下该节点所有拒绝属性的分支的触发次数/该节点所有走向次数（分支+客户主动挂机+问答知识）
     * - 节点拒绝率（最高/最低值）：以话术为单元聚合统计，最高/最低值即节点拒绝率最高/最低的话术的值
     */
    private List<DataReportStepDataPO> doGenerateDialogFlowDataReportData(List<Tuple2<Long, Long>> subIndustryTupleList, DataReportPO dataReportPO) {
        List<DataReportStepDataPO> stepDataPOList = Lists.newArrayList();
        Integer stepCount = dataReportPO.getStepCount();

        int nodeCount1 = 2;
        for (int i = 1; i <= stepCount; i++) {
            DataReportStepDataPO stepDataPO = new DataReportStepDataPO();
            stepDataPO.setSeq(i);

            List<DataReportNodeDataPO> nodeDataPOList = Lists.newArrayList();
            if (nodeCount1 == 0) {
                nodeCount1 = 1;
            }
            for (; nodeCount1 > 0; nodeCount1--) {
                DataReportNodeDataPO nodeDataPO = new DataReportNodeDataPO();
                nodeDataPO.setSeq(nodeCount1);
                nodeDataPOList.add(0, nodeDataPO);
            }
            stepDataPO.setDataReportNodeDataPOList(nodeDataPOList);

            stepDataPOList.add(stepDataPO);
        }

        subIndustryTupleList.forEach(tuple -> {
            wrapDialogFlowDataReportData(stepDataPOList, tuple._1(), tuple._2(), dataReportPO);
        });

        // 完成话术遍历后进行后处理
        stepDataPOList.forEach(stepDataPO -> {
            // 计算平均流程到达率
            Integer totalArriveCount = stepDataPO.getTotalArriveCount();
            Integer totalCount = stepDataPO.getTotalCount();
            if (Objects.isNull(totalArriveCount) || Objects.isNull(totalCount) || totalCount == 0) {
                stepDataPO.setAvgArriveRate(null);;
            } else {
                String avgArriveRate = String.format("%.2f", totalArriveCount * 100 / (double) totalCount);
                stepDataPO.setAvgArriveRate(avgArriveRate);
            }

            stepDataPO.getDataReportNodeDataPOList().forEach(nodeDataPO -> {
                // 计算平均节点挂断率
                Integer totalHangupCount = nodeDataPO.getTotalHangupCount();
                Integer nodeTotalCount = nodeDataPO.getTotalCount();
                if (Objects.isNull(totalHangupCount) || Objects.isNull(nodeTotalCount) || nodeTotalCount == 0) {
                    nodeDataPO.setAvgHangupRate(null);
                } else {
                    String avgHangupRate = String.format("%.2f", totalHangupCount * 100 / (double) nodeTotalCount);
                    nodeDataPO.setAvgHangupRate(avgHangupRate);
                }

                // 计算平均节点拒绝率
                Integer totalDeclineCount = nodeDataPO.getTotalDeclineCount();
                if (Objects.isNull(totalDeclineCount) || Objects.isNull(nodeTotalCount) || nodeTotalCount == 0) {
                    nodeDataPO.setAvgDeclineRate(null);
                } else {
                    String avgDeclineRate = String.format("%.2f", totalDeclineCount * 100 / (double) nodeTotalCount);
                    nodeDataPO.setAvgDeclineRate(avgDeclineRate);
                }
            });
        });

        return stepDataPOList;
    }

    private void wrapDialogFlowDataReportData(List<DataReportStepDataPO> stepDataPOList, Long tenantId, Long dialogFlowId, DataReportPO dataReportPO) {

        // 拒绝分支列表
        List<IntentBranchPO> intentBranchPOList = dialogFlowIntentBranchService.findDialogFlowIntentBranch(dialogFlowId);
        List<String> uidList = intentBranchPOList.stream().filter(item -> IntentBranchCategoryEnum.DECLINE.equals(item.getCategory())).map(IntentBranchPO::getUid).collect(Collectors.toList());

        // 获取step的扁平结构，用于按顺序获取node
        List<DialogFlowStepPO> dialogFlowStepPOList = dialogFlowStepService.findDialogFlowSteps(dialogFlowId, DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW);
        List<DialogFlowStepListDTO> dialogFlowStepListDTOList = dialogFlowStepService.flattenStepStructure(dialogFlowStepPOList);

        // 查询流程统计数据
        DialogFlowStatsQueryVO queryVO = DialogFlowStatsQueryVO.builder()
                .tenantId(tenantId)
                .dialogFlowId(dialogFlowId)
                .dialogFlowStepType(DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW)
                .beginDateTime(LocalDateTime.of(dataReportPO.getBeginDate(), LocalTime.MIN))
                .endDateTime(LocalDateTime.of(dataReportPO.getEndDate(), LocalTime.MAX))
                .build();
        List<DialogFlowStepStatsVO> dialogFlowStepStatsVOList = dialogFlowNodeStatsService.queryStepTotalStats(queryVO);
        Map<String, DialogFlowStepStatsVO> stepStatsMap = MyCollectionUtils.listToMap(dialogFlowStepStatsVOList, DialogFlowStepStatsVO::getDialogFlowStepId);
        int totalCallCount = dialogFlowNodeStatsService.queryTotalCallCount(tenantId, null, dialogFlowId, LocalDateTime.of(dataReportPO.getBeginDate(), LocalTime.MIN), LocalDateTime.of(dataReportPO.getEndDate(), LocalTime.MAX));

        if (CollectionUtils.isNotEmpty(dialogFlowStepStatsVOList)) {

            // 需要统计的节点数，只有第一个流程为2，其余都为1
            int nodeCount = 2;

            for (int i = 0; i < dataReportPO.getStepCount() && i < dialogFlowStepStatsVOList.size(); i++) {
                DialogFlowStepListDTO dialogFlowStepListDTO = dialogFlowStepListDTOList.get(i);
                String dialogFlowStepId = dialogFlowStepListDTO.getId();
                DialogFlowStepStatsVO dialogFlowStepStatsVO = stepStatsMap.get(dialogFlowStepId);
                if (Objects.isNull(dialogFlowStepStatsVO)) {
                    continue;
                }
                DataReportStepDataPO stepDataPO = stepDataPOList.get(i);
                stepDataPO.setArriveRate(dialogFlowStepStatsVO.getDistinctArrivePercent());
                stepDataPO.addArriveCount(dialogFlowStepStatsVO.getDistinctArriveCount());
                stepDataPO.addTotalCount(totalCallCount);
                List<DataReportNodeDataPO> nodeDataPOList = stepDataPO.getDataReportNodeDataPOList();
                if (CollectionUtils.isEmpty(nodeDataPOList)) {
                    nodeDataPOList = com.google.common.collect.Lists.newArrayList();
                }

                // 所有普通节点的ID列表
                List<DialogFlowNodeDTO> flattenTreeNodeList = dialogFlowStepListDTO.getFlattenTreeNodeList();
                List<Long> nodeIdList = flattenTreeNodeList
                        .stream()
                        .filter(dialogFlowNodeDTO -> dialogFlowNodeDTO.getType().equals(DialogFlowNodeTypeEnum.CHAT))
                        .map(DialogFlowNodeDTO::getId)
                        .collect(Collectors.toList());

                // 查询节点统计数据
                queryVO.setDialogFlowStepId(dialogFlowStepId);
                List<DialogFlowTotalNodeStatsVO> dialogFlowTotalNodeStatsVOList = dialogFlowNodeStatsService.queryNodeTotalStatsByStepId(queryVO);

                if (CollectionUtils.isNotEmpty(nodeIdList) && CollectionUtils.isNotEmpty(dialogFlowTotalNodeStatsVOList)) {
                    Map<Long, DialogFlowTotalNodeStatsVO> nodeStatsMap = MyCollectionUtils.listToMap(dialogFlowTotalNodeStatsVOList, DialogFlowTotalNodeStatsVO::getDialogFlowNodeId);

                    if (nodeCount == 0) {
                        nodeCount = 1;
                    }
                    for (int j = 0; nodeCount > 0 && j < nodeIdList.size(); j++, nodeCount--) {

                        Long nodeId = nodeIdList.get(j);
                        DialogFlowTotalNodeStatsVO nodeStats = nodeStatsMap.get(nodeId);
                        if (Objects.isNull(nodeStats)) {
                            continue;
                        }
                        List<DialogFlowNodeFanOutStatsVO> branchStatsList = nodeStats.getBranchStatsList();

                        if (CollectionUtils.isNotEmpty(branchStatsList)) {
                            DataReportNodeDataPO nodeDataPO = nodeDataPOList.get(j);

                            // 计算节点挂断数
                            int hangupCount = branchStatsList.stream()
                                    .filter(branchStats -> branchStats.getTargetType().equals("HANGUP"))
                                    .map(DialogFlowNodeFanOutStatsVO::getArriveCount)
                                    .reduce(0, Integer::sum);
                            nodeDataPO.addHangupCount(hangupCount);

                            // 计算节点拒绝数
                            int declineCount = branchStatsList.stream()
                                    .filter(branchStats -> branchStats.getTargetType().equals("BRANCH") && uidList.contains(branchStats.getIntentBranchUid()))
                                    .map(DialogFlowNodeFanOutStatsVO::getArriveCount)
                                    .reduce(0, Integer::sum);
                            nodeDataPO.addDeclineCount(declineCount);

                            // 计算节点总数
                            int totalCount = branchStatsList.stream()
                                    .map(DialogFlowNodeFanOutStatsVO::getArriveCount)
                                    .reduce(0, Integer::sum);
                            nodeDataPO.addTotalCount(totalCount);

                            // 计算节点挂断率
                            if (totalCount > 0) {
                                String hangupRate = String.format("%.2f", hangupCount * 100 / (double) totalCount);
                                nodeDataPO.setHangupRate(hangupRate);
                            }

                            // 计算节点拒绝率
                            if (totalCount > 0) {
                                String declineRate = String.format("%.2f", declineCount * 100 / (double) totalCount);
                                nodeDataPO.setDeclineRate(declineRate);
                            }
                        }
                    }

                }

                stepDataPO.setDataReportNodeDataPOList(nodeDataPOList);
            }
        }
    }

    /**
     * 计算各种率
     * @param a
     * @param b
     * @return
     */
    private Integer calculateRateInteger(Integer a, Integer b) {
        if (Objects.isNull(b) || b <= 0) {
            return null;
        }

        if (Objects.isNull(a) || a <= 0) {
            return 0;
        }

        return (new BigDecimal(a).divide(BigDecimal.valueOf(b), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(10000))).intValue();
    }

    /**
     * 查询外呼统计数据
     * @param tenantIdList
     * @param robotCallJobIdList
     * @param beginDate
     * @param endDate
     * @return
     */
    private List<DataReportStatsBO> queryCallJobStats(List<Long> tenantIdList,  List<Long> robotCallJobIdList, LocalDate beginDate, LocalDate endDate) {

        if (CollectionUtils.isEmpty(tenantIdList) && CollectionUtils.isEmpty(robotCallJobIdList)) {
            return Lists.emptyList();
        }

        GroupOperation groupOperation = Aggregation.group("tenantId", "callStatsId").sum("taskTotalCompleted").as("taskTotalCompleted")
                .sum("answeredCall").as("answeredCall").sum("intentLevel.0").as("intentLevel0").sum("intentLevel.1").as("intentLevel1")
                .first("callStatsId").as("callStatsId");

        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("localDateTime").gte(beginDate).lt(endDate.plusDays(1))));

        if (CollectionUtils.isNotEmpty(tenantIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("tenantId").in(tenantIdList)));
        }
        if (CollectionUtils.isNotEmpty(robotCallJobIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("callStatsId").in(robotCallJobIdList)));
        }

        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        return mongoTemplate.aggregate(aggregation, CALL_STATS_ROBOT_DONE_JOB, DataReportStatsBO.class).getMappedResults();
    }

    /**
     * 查询好友通过数
     * @param tenantIdList
     * @param robotCallJobIdList
     * @param beginDate
     * @param endDate
     * @return
     */
    private List<DataReportStatsBO> queryWechatStats(List<Long> tenantIdList,  List<Long> robotCallJobIdList, LocalDate beginDate, LocalDate endDate) {

        if (CollectionUtils.isEmpty(tenantIdList) && CollectionUtils.isEmpty(robotCallJobIdList)) {
            return Lists.emptyList();
        }

        GroupOperation groupOperation = Aggregation.group("tenantId", "robotCallJobId")
                .sum("sendAddFriendAdoptCount").as("sendAddFriendAdoptCount")
                .first("robotCallJobId").as("callStatsId");
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("localDate").gte(beginDate).lt(endDate.plusDays(1))));

        if (CollectionUtils.isNotEmpty(tenantIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("tenantId").in(tenantIdList)));
        }
        if (CollectionUtils.isNotEmpty(robotCallJobIdList)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("robotCallJobId").in(robotCallJobIdList)));
        }
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        return mongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.WECHAT_CP_ADD_FRIEND_STATS, DataReportStatsBO.class).getMappedResults();
    }

    /**
     * 查询报表数据
     * @param dataReportId
     * @param type
     * @return
     */
    private List<DataReportDataPO> queryDataReportStats(Long dataReportId, Integer type) {
        GroupOperation groupOperation = Aggregation.group("customerTrackTypeId", "customerSceneName", "tenantId")
                .sum("answeredCall").as("answeredCall").sum("taskTotalCompleted").as("taskTotalCompleted").sum("intentLevel0").as("intentLevel0")
                .sum("intentLevel1").as("intentLevel1").sum("sendAddFriendAdoptCount").as("sendAddFriendAdoptCount");
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();
        aggregationOperationList.add(Aggregation.match(Criteria.where("dataReportId").is(dataReportId)));
        if (Objects.nonNull(type)) {
            aggregationOperationList.add(Aggregation.match(Criteria.where("type").is(type)));
        }
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);
        return mongoTemplate.aggregate(aggregation, DataReportDataPO.COLLECTION_NAME, DataReportDataPO.class).getMappedResults();
    }
}
