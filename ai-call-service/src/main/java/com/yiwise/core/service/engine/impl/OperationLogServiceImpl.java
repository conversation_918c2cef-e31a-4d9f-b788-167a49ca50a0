package com.yiwise.core.service.engine.impl;

import com.github.pagehelper.Page;
import com.mongodb.client.result.DeleteResult;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.date.MyDateUtils;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.calloutjob.api.dto.CallOutJobRequestDTO;
import com.yiwise.calloutjob.api.vo.CallOutJobSimpleInfoVO;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.dal.entity.calloutplan.CallOutPlanPO;
import com.yiwise.core.feignclient.callout.CallOutJobClient;
import com.yiwise.core.model.bo.operationlog.JobOperationLogBO;
import com.yiwise.core.model.dialogflow.dto.DialogFlowLogInfoDTO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowInfoPO;
import com.yiwise.core.model.dialogflow.entity.DialogFlowLogInfoPO;
import com.yiwise.core.model.dialogflow.vo.DialogLogQueryVO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.dialogflow.DialogFlowAuditActionEnum;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import com.yiwise.core.model.vo.operationlog.*;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.engine.OperationLogService;
import com.yiwise.core.service.engine.SmsJobService;
import com.yiwise.core.service.engine.calljob.RobotCallJobService;
import com.yiwise.core.service.engine.calloutplan.CallOutPlanService;
import com.yiwise.core.service.engine.csseat.*;
import com.yiwise.core.service.engine.phonenumber.PhoneNumberService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.platform.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.NEW_TENANT_VERSION;
import static com.yiwise.core.model.enums.OperationLogLogTypeEnum.*;
import static com.yiwise.core.model.enums.OperationLogOperationTypeEnum.*;


/**
 * <AUTHOR>
 * @Date 2018/7/30
 **/
@Service
public class OperationLogServiceImpl implements OperationLogService {
    private static final Logger logger = LoggerFactory.getLogger(OperationLogServiceImpl.class);

    public static final List<OperationLogLogTypeEnum> enabledLogList = Arrays.asList(ROBOTCALLJOB, SEAT_MANAGEMENT,
            AI_CONTACT_HISTORY, CALL_OUT_PLAN, DOOR_LOGIN_BOSS_TO_AICC, ACCOUNT, PHONE_SMS_CONFIG, QC, ISV);

    @Resource
    private RobotCallJobService robotCallJobService;
    @Resource
    private UserService userService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private SmsJobService smsJobService;

    @Resource
    private CsBatchCallJobService csBatchCallJobService;

    @Resource
    private LoginToCrmService loginToCrmService;
    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private TenantService tenantService;
    @Resource
    private PhoneNumberService phoneNumberService;
    @Resource
    private CallOutPlanService callOutPlanService;
    @Resource
    @Lazy
    private CsStaffInfoService csStaffInfoService;
    @Resource
    @Lazy
    private CsStaffGroupService csStaffGroupService;
    @Resource
    private DistributorService distributorService;
    @Resource
    private CallOutJobClient callOutJobClient;


    @Override
    public PageResultObject<JobOperationLogBO> getOperationLogByJobCondition(OperationLogJobQueryVO condition) {
        return getOperationLogByCondition(condition, ROBOTCALLJOB);
    }

    @Override
    public PageResultObject<JobOperationLogBO> getBatchJobOperationLog(OperationLogJobQueryVO condition) {
        return getOperationLogByCondition(condition, BATCH_JOB);
    }

    @Override
    public PageResultObject<JobOperationLogBO> getOperationLogByCondition(OperationLogJobQueryVO condition, OperationLogLogTypeEnum operationLogLogType) {
        condition.setOperationLogLogType(operationLogLogType);
        return getOperationLogByCondition(condition);
    }


    @Override
    public PageResultObject<JobOperationLogBO> getOperationLogByCondition(OperationLogJobQueryVO condition) {
	    TenantPO tenantPO = tenantService.selectByKey(condition.getTenantId());
	    boolean hideJumpToAiccRecord = false;
	    if (tenantPO.getDistributorId() != null) {
		    DistributorPO distributor = distributorService.selectByKey(tenantPO.getDistributorId());
		    if (distributor != null && distributor.getHideJumpToAiccRecord() != null) {
			    hideJumpToAiccRecord = distributor.getHideJumpToAiccRecord();
		    }
	    }
	    Query query = new Query();
        // 日志类型
        if (condition.getOperationLogLogType() != null) {
            query.addCriteria(Criteria.where("logType").is(condition.getOperationLogLogType().toString()));
        } else if (condition.getIsEnterprisePage()){
            //过滤无法正常展示的日志
	        List<OperationLogLogTypeEnum> logTypes = new ArrayList<>(enabledLogList);
	        if (hideJumpToAiccRecord) {
	        	logTypes.remove(DOOR_LOGIN_BOSS_TO_AICC);
	        }
            query.addCriteria(Criteria.where("logType").in(logTypes));
        }
        query.addCriteria(Criteria.where("tenantId").is(condition.getTenantId()));
        // 操作类型
	    List<OperationLogOperationTypeEnum> operationTypes = new ArrayList<>();
	    if (condition.getOperationType() != null) {
		    operationTypes.add(condition.getOperationType());
	    }
	    if (CollectionUtils.isNotEmpty(condition.getOperationTypes())) {
	    	operationTypes.addAll(condition.getOperationTypes());
	    }
	    if (CollectionUtils.isNotEmpty(operationTypes)) {
		    query.addCriteria(Criteria.where("operationType").in(operationTypes));
	    } else {
		    // 过滤无法正常展示的日志
		    query.addCriteria(Criteria.where("operationType").ne(DEFAULT));
	    }

        if (Objects.nonNull(condition.getSubModule())) {
            query.addCriteria(Criteria.where("subModule").is(condition.getSubModule()));
        }
        if (Objects.nonNull(condition.getRefId())) {
            query.addCriteria(Criteria.where("refId").is(condition.getRefId()));
        }
        if (Objects.nonNull(condition.getCreateUserId())) {
            query.addCriteria(Criteria.where("createUserId").is(condition.getCreateUserId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getModules())) {
            query.addCriteria(Criteria.where("module").in(condition.getModules()));
        }
        if(Objects.nonNull(condition.getStartDate()) && Objects.nonNull(condition.getEndDate())){
            query.addCriteria(Criteria.where("createTime")
                    .gte(condition.getStartDate())
                    .lte(condition.getEndDate()));
        }

        long totalCount = mongoTemplate.count(query, MongoCollectionNameCenter.OPERATION_LOG);

        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((long) (condition.getPageNum() - 1) * condition.getPageSize()).limit(condition.getPageSize());
        List<JobOperationLogBO> operationLogList = mongoTemplate.find(query, JobOperationLogBO.class, MongoCollectionNameCenter.OPERATION_LOG);

        Set<Long> robotCallJobIds = operationLogList.stream().filter(item -> item.getRefId() != null && ROBOTCALLJOB.equals(item.getLogType())).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        Set<Long> callOutPlanIds = operationLogList.stream().filter(item -> item.getRefId() != null && CALL_OUT_PLAN.equals(item.getLogType())).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        Set<Long> dialogFlowIds = operationLogList.stream().filter(item -> item.getRefId() != null && CRM_SHOW_DIALOG_FLOW.equals(item.getLogType())).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        Set<Long> userIds = operationLogList.stream().filter(item -> item.getRefId() != null && ACCOUNT.equals(item.getLogType())).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        Set<Long> phoneNumberIds = operationLogList.stream().filter(item -> item.getRefId() != null && PHONE_SMS_CONFIG.equals(item.getLogType())).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        userIds.addAll(operationLogList.stream().map(JobOperationLogBO::getCreateUserId).filter(Objects::nonNull).collect(Collectors.toSet()));
        Set<Long> staffGroupIds = operationLogList.stream().filter(item -> item.getRefId() != null && SEAT_MANAGEMENT.equals(item.getLogType()) && (STAFF_GROUP_ADD.equals(item.getOperationType())
		        || STAFF_GROUP_EDIT.equals(item.getOperationType()))).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());
        Set<Long> staffIds = operationLogList.stream().filter(item -> item.getRefId() != null && SEAT_MANAGEMENT.equals(item.getLogType()) && !(STAFF_GROUP_ADD.equals(item.getOperationType())
		        || STAFF_GROUP_EDIT.equals(item.getOperationType()))).map(JobOperationLogBO::getRefId).collect(Collectors.toSet());

	    Map<Long, String> robotCallJobNameMap;
	    if (NEW_TENANT_VERSION.equals(tenantPO.getTenantVersion())) {
		    robotCallJobNameMap = MyCollectionUtils.listToMap(
				    callOutJobClient.getByIdsTenantId(new CallOutJobRequestDTO(condition.getTenantId(), robotCallJobIds)),
				    CallOutJobSimpleInfoVO::getCallOutJobId, CallOutJobSimpleInfoVO::getCallOutJobName);
	    } else {
		    List<RobotCallJobPO> robotCallJobs = robotCallJobService.selectListByKeyCollect(robotCallJobIds);
		    robotCallJobNameMap = MyCollectionUtils.listToMap(robotCallJobs, RobotCallJobPO::getRobotCallJobId, RobotCallJobPO::getName);
	    }
	    Map<? extends Serializable, DialogFlowInfoPO> dialogFlowInfoPOMap = dialogFlowService.selectMapByKeyCollect(dialogFlowIds);
        Map<? extends Serializable, PhoneNumberPO> phoneNumberPOMap = phoneNumberService.selectMapByKeyCollect(phoneNumberIds);
        Map<? extends Serializable, UserPO> userPOMap = userService.selectMapByKeyCollect(userIds);
        Map<? extends Serializable, CsStaffInfoPO> csStaffInfoPOMap = csStaffInfoService.selectMapByKeyCollect(staffIds);
        Map<? extends Serializable, CsStaffGroupPO> csStaffGroupPOMap = csStaffGroupService.selectMapByKeyCollect(staffGroupIds);
        Map<? extends Serializable, CallOutPlanPO> callOutPlanPOMapper = callOutPlanService.selectMapByKeyCollect(callOutPlanIds);


        Page<JobOperationLogBO> page = new Page<>();
        for(JobOperationLogBO operationLog: operationLogList) {
            //UserPO user = userService.getTenantUser(condition.getTenantId(), operationLog.getCreateUserId());
            //OPE登录AICC操作的日志TenantId和userID不匹配，直接通过userId查询
            UserPO user = userPOMap.get(operationLog.getCreateUserId());
            if (user != null) {
                if (operationLog.getModule() != null && operationLog.getModule().equals(SystemEnum.OPENAPI)) {
                    operationLog.setCreatedByUserName(user.getName() + " " + operationLog.getModule().getDesc());
                } else {
                    operationLog.setCreatedByUserName(user.getName());
                }
            }

            if (CollectionUtils.isEmpty(operationLog.getOperationContent())){
                List<String> content = new ArrayList<>();
                if (Objects.nonNull(operationLog.getOperationType())){
                    content.add(operationLog.getOperationType().getDesc());
                }
                operationLog.setOperationContent(content);
            }
            if (operationLog.getLogType() != null) {
                switch (operationLog.getLogType()) {
                    case ROBOTCALLJOB:
	                    operationLog.setRefName(robotCallJobNameMap.get(operationLog.getRefId()));
                        break;
                    case CRM_SHOW_DIALOG_FLOW:
                        DialogFlowInfoPO dialogFlowInfoPO = dialogFlowInfoPOMap.get(operationLog.getRefId());
                        operationLog.setRefName(dialogFlowInfoPO == null ? null : dialogFlowInfoPO.getName());
                        break;
                    case ACCOUNT:
                        if (USER_LOGIN.equals(operationLog.getOperationType())) {
                            UserPO userPO = userPOMap.get(operationLog.getRefId());
                            operationLog.setRefName(userPO == null ? null : userPO.getName());
                        } else {
                            operationLog.setRefName(tenantPO.getCompanyName());
                        }
                        break;
                    case PHONE_SMS_CONFIG:
                        if (operationLog.getOperationType() != null && operationLog.getOperationType().isSmsLog()) {
                            operationLog.setRefName(tenantPO.getCompanyName());
                        } else {
                            PhoneNumberPO phoneNumberPO = phoneNumberPOMap.get(operationLog.getRefId());
                            if (phoneNumberPO != null) {
                                operationLog.setRefName(phoneNumberPO.getPhoneNumber());
                            }
                        }
                        break;
                    case SEAT_MANAGEMENT:
                        if (STAFF_GROUP_ADD.equals(operationLog.getOperationType()) ||
                                STAFF_GROUP_EDIT.equals(operationLog.getOperationType())) {
                            CsStaffGroupPO csStaffGroupPO = csStaffGroupPOMap.get(operationLog.getRefId());
                            if (csStaffGroupPO != null) {
                                operationLog.setRefName(csStaffGroupPO.getGroupName());
                            }
                        } else {
                            CsStaffInfoPO csStaffInfoPO = csStaffInfoPOMap.get(operationLog.getRefId());
                            if (csStaffInfoPO != null) {
                                operationLog.setRefName(csStaffInfoPO.getCsName());
                            }
                        }
                        break;
                    case QC:
                        operationLog.setRefName(tenantPO.getCompanyName());
                        break;
                    case CALL_OUT_PLAN:
                        CallOutPlanPO callOutPlanPO = callOutPlanPOMapper.get(operationLog.getRefId());
                        if (callOutPlanPO != null) {
                            operationLog.setRefName(callOutPlanPO.getCallOutPlanName());
                        }

                        break;
                    default:
                        break;
                }
            }
            page.add(operationLog);
        }
        page.setPageNum(condition.getPageNum());
        page.setPageSize(condition.getPageSize());
        page.setTotal(totalCount);

        return PageResultObject.of(page);
    }

    /**
     * 删除超时的临时数据-操作日志
     */
    @Override
    public void deleteOverTimeOperationLog() {
        Query query = new Query();
        query.addCriteria(Criteria.where("createTime").lt(LocalDateTime.now().minusYears(1)));
        query.limit(1000);
        String collection = MongoCollectionNameCenter.OPERATION_LOG;
        DeleteResult remove = mongoTemplate.remove(query, collection);
        long deletedTotalCount = remove.getDeletedCount();
        while (deletedTotalCount > 0) {
            logger.debug("删除mongo数据，collection={}, deletedCount={}", collection, deletedTotalCount);
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
            	logger.error("删除mongo数据中断", e);
            }
            remove = mongoTemplate.remove(query, collection);
            deletedTotalCount = remove.getDeletedCount();
        }
    }

    @Override
    public PageResultObject selectByCondition(OperationLogQueryVO condition) {
        Query query = new Query();

        if (Objects.nonNull(condition.getStartDatetime()) && Objects.nonNull(condition.getEndDatetime())) {
            query.addCriteria(Criteria.where("createTime").gte(condition.getStartDatetime()).lte(condition.getEndDatetime()));
        } else {
            if (Objects.nonNull(condition.getStartDatetime())) {
                query.addCriteria(Criteria.where("createTime").gte(condition.getStartDatetime()));
            }
            if (Objects.nonNull(condition.getEndDatetime())) {
                query.addCriteria(Criteria.where("createTime").lte(condition.getEndDatetime()));
            }
        }

        if (Objects.nonNull(condition.getDistributorId())) {
            query.addCriteria(Criteria.where("distributorId").is(condition.getDistributorId()));
        }

        if (Objects.nonNull(condition.getTenantId())) {
            query.addCriteria(Criteria.where("tenantId").is(condition.getTenantId()));
        }

        if (Objects.nonNull(condition.getModule())) {
            query.addCriteria(Criteria.where("module").is(condition.getModule()));
        }

        if (Objects.nonNull(condition.getSubModule())) {
            query.addCriteria(Criteria.where("subModule").is(condition.getSubModule()));
        }

        if (Objects.nonNull(condition.getLogType())) {
            query.addCriteria(Criteria.where("logType").is(condition.getLogType().toString()));
        }

        if (Objects.nonNull(condition.getRefId())) {
            query.addCriteria(Criteria.where("refId").is(condition.getRefId()));
        }
        if (Objects.nonNull(condition.getCreateUserId())) {
            query.addCriteria(Criteria.where("createUserId").is(condition.getCreateUserId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getExtraDialogResourceTypeList())) {
            query.addCriteria(Criteria.where("extraDialogResourceType").in(condition.getExtraDialogResourceTypeList()));
        }
        if (BooleanUtils.isTrue(condition.getExcludeOpeOnly())) {
            query.addCriteria(Criteria.where("opeOnly").ne(true));
        }

        long total = mongoTemplate.count(query, MongoCollectionNameCenter.OPERATION_LOG);
        if (total == 0) {
            return PageResultObject.of(Collections.emptyList());
        }

        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.skip((long) (condition.getPageNum() - 1) * condition.getPageSize()).limit(condition.getPageSize());
        List<JobOperationLogBO> operationLogList = mongoTemplate.find(query, JobOperationLogBO.class, MongoCollectionNameCenter.OPERATION_LOG);

        List<Long> userIds = operationLogList.stream().map(JobOperationLogBO::getCreateUserId).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            List<UserPO> userList = userService.selectAllByIdList(userIds);
            Map<Long, String> userIdNameMap = userList.stream().collect(Collectors.toMap(UserPO::getUserId, UserPO::getName));
            operationLogList.forEach(x -> x.setCreatedByUserName(userIdNameMap.get(x.getCreateUserId())));
        }
        Page<JobOperationLogBO> result = wrapList2Page(total, condition, operationLogList);
        return PageResultObject.of(result);
    }

    public <T> Page<T> wrapList2Page(long total, AbstractQueryVO aqv, List<T> list) {
        Page<T> page = new Page<>();
        page.addAll(list);
        page.setPageNum(aqv.getPageNum());
        page.setPageSize(aqv.getPageSize());
        page.setTotal(total);
        return page;
    }

    @Override
    public void onRobotCallJobCreate(Long robotCallJobId, Long tenantId, Long userId, Boolean isOpenApi) {
        try {
            if (BooleanUtils.isNotTrue(isOpenApi)) {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, ROBOTCALLJOB_CREATE);
            } else {
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, ROBOTCALLJOB_CREATE,isOpenApi);
            }
        } catch (Exception e) {
            logger.error("任务创建日志生成失败", e);
        }
    }

    @Override
    public void onCsBatchCallJobCreate(Long csBatchCallJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogCsBatchCallJob(csBatchCallJobId, tenantId, userId, BATCH_JOB_CREATE);
        } catch (Exception e) {
            logger.error("任务创建日志生成失败", e);
        }
    }

    @Override
    public void onRobotCallTaskDelete(Long robotCallJobId, Long tenantId, Long userId, boolean isOpenApi) {
        try {
            if (BooleanUtils.isNotTrue(isOpenApi)) {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, ROBOTCALLJOB_DEl_TASK);
            } else {
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, ROBOTCALLJOB_DEl_TASK,isOpenApi);
            }
        } catch (Exception e) {
            logger.error("任务创建日志生成失败", e);
        }
    }

    @Override
    public void onCsBatchCallTaskDelete(Long csBatchCallJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogCsBatchCallJob(csBatchCallJobId, tenantId, userId, BATCH_JOB_DEl_TASK);
        } catch (Exception e) {
            logger.error("任务创建日志生成失败", e);
        }
    }

    @Override
    public void onCsBatchCallJobImport(Long csBatchCallJobId, Long tenantId, Long userId, List<String> operationContent) {
        try {
            buildAndAddLogRobotCallJob(csBatchCallJobId, tenantId, userId, BATCH_JOB, BATCH_JOB_IMPORT,operationContent,true);
        } catch (Exception e) {
            logger.error("任务修改日志生成失败", e);
        }
    }

    @Override
    public void onRobotCallJobModify(Long robotCallJobId, Long tenantId, Long userId, List<String> operationContent, Boolean isOpenApi) {
        try {
            if (BooleanUtils.isNotTrue(isOpenApi)) {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, ROBOTCALLJOB, ROBOTCALLJOB_UPDATE,operationContent,false);
            } else {
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, ROBOTCALLJOB_UPDATE,operationContent, isOpenApi);
            }
        } catch (Exception e) {
            logger.error("任务修改日志生成失败", e);
        }
    }

    @Override
    public void onRobotCallJobDynamicRobot(Long robotCallJobId, Long tenantId, Long userId, int robotCount, Boolean isOpenApi) {
        try {
            List<String> operationContent = new ArrayList<>();
            operationContent.add("更新BOT数量：" + robotCount);
            if (BooleanUtils.isNotTrue(isOpenApi)) {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, ROBOTCALLJOB, ROBOTCALLJOB_DYNAMIC_ROBOT,operationContent,false);
            } else{
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, ROBOTCALLJOB_DYNAMIC_ROBOT,operationContent, isOpenApi);
            }
        } catch (Exception e) {
            logger.error("任务修改日志生成失败", e);
        }
    }

    @Override
    public void onCsBatchCallJobModify(Long csBatchCallJobId, Long tenantId, Long userId, List<String> operationContent) {
        try {
            buildAndAddLogRobotCallJob(csBatchCallJobId, tenantId, userId, BATCH_JOB, BATCH_JOB_UPDATE,operationContent,true);
        } catch (Exception e) {
            logger.error("任务修改日志生成失败", e);
        }
    }

    @Override
    public void onRobotCallJobDelete(Long robotCallJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, ROBOTCALLJOB_DELETE);
        }catch (Exception e) {
            logger.error("任务删除日志生成失败", e);
        }
    }

    @Override
    public void onRobotCallJobExecution(Long robotCallJobId, Long tenantId, Long userId, JobOperationEnum operation, Boolean openApi) {
        try {
            if (BooleanUtils.isNotTrue(openApi)) {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, userId, operation.getLogType());
            } else {
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, operation.getLogType(), openApi);
            }
        } catch (Exception e) {
            logger.error("任务执行日志生成失败", e);
        }
    }

    @Override
    public void onCsBatchCallJobExecution(Long csBatchCallJobId, Long tenantId, Long userId, JobOperationEnum operation) {
        try {
            buildAndAddLogCsBatchCallJob(csBatchCallJobId, tenantId, userId, operation.getBatchJobLogType());
        }catch (Exception e) {
            logger.error("任务执行日志生成失败", e);
        }
    }

	@Override
	public void addCommonOperateLog(SystemEnum module, Long operateUserId, String content, Long distributorId, Long tenantId, OperationLogLogTypeEnum logType, OperationLogOperationTypeEnum operationType, Long refId) {
		OperationLogPO po = new OperationLogPO();
		po.setModule(module);
		po.setCreateUserId(operateUserId);
		po.setUpdateUserId(operateUserId);
		po.setCreateTime(LocalDateTime.now());
		po.setDescription(content);
		po.setDistributorId(distributorId);
		po.setTenantId(tenantId);
		po.setLogType(logType);
		po.setOperationType(operationType);
		po.setRefId(refId);
		mongoTemplate.insert(po, MongoCollectionNameCenter.OPERATION_LOG);
	}

    @Override
    public void addToMongo(OperationLogPO operationLogPO) {
        operationLogPO.setUpdateUserId(operationLogPO.getCreateUserId());
        if (Objects.isNull(operationLogPO.getCreateTime())) {
            operationLogPO.setCreateTime(LocalDateTime.now());
        }

        //如果写入的日志类型是任务日志，更新任务日志的last_edit_time字段
        // 导入客户不更新last_edit_time
        if(ROBOTCALLJOB.equals(operationLogPO.getLogType())
                && (!ROBOTCALLJOB_IMPORT.equals(operationLogPO.getOperationType())
                && !ROBOTCALLJOB_ADD_ONE.equals(operationLogPO.getOperationType())
                && !ROBOTCALLJOB_READD.equals(operationLogPO.getOperationType()))){
            robotCallJobService.updateEditTimeById(operationLogPO.getTenantId(), operationLogPO.getRefId());
        }

        mongoTemplate.insert(operationLogPO, MongoCollectionNameCenter.OPERATION_LOG);
    }

    @Override
    public void addToMongo(List<OperationLogPO> operationLogList) {
        if (CollectionUtils.isEmpty(operationLogList)) {
            return;
        }
        for (OperationLogPO operationLogPO : operationLogList) {
            operationLogPO.setUpdateUserId(operationLogPO.getCreateUserId());
            if (Objects.isNull(operationLogPO.getCreateTime())) {
                operationLogPO.setCreateTime(LocalDateTime.now());
            }

            //如果写入的日志类型是任务日志，更新任务日志的last_edit_time字段
            // 导入客户不更新last_edit_time
            if(operationLogPO.getLogType().equals(ROBOTCALLJOB)
                    && (!ROBOTCALLJOB_IMPORT.equals(operationLogPO.getOperationType())
                    && !ROBOTCALLJOB_ADD_ONE.equals(operationLogPO.getOperationType())
                    && !ROBOTCALLJOB_READD.equals(operationLogPO.getOperationType()))){
                robotCallJobService.updateEditTimeById(operationLogPO.getTenantId(), operationLogPO.getRefId());
            }
        }

        mongoTemplate.insert(operationLogList, MongoCollectionNameCenter.OPERATION_LOG);
    }

    @Override
    public OperationLogPO queryCallJobFirstStart(Long callJobId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("refId").is(callJobId));
        query.addCriteria(Criteria.where("logType").is(ROBOTCALLJOB.toString()));
        query.addCriteria(Criteria.where("operationType").is(ROBOTCALLJOB_START.toString()));
        query.with(Sort.by(Sort.Direction.ASC, "createTime"));
        query.limit(1);
        List<JobOperationLogBO> operationLogList = mongoTemplate.find(query, JobOperationLogBO.class, MongoCollectionNameCenter.OPERATION_LOG);
        if (CollectionUtils.isEmpty(operationLogList)) {
            return null;
        }
        return operationLogList.get(0);
    }

    @Override
    public List<DialogFlowLogInfoDTO> queryDialogFlowLog(Long dialogFlowId) {
        Query query = new Query().addCriteria(Criteria.where("refId").is(dialogFlowId))
                .addCriteria(Criteria.where("logType").is(CRM_SHOW_DIALOG_FLOW.name()));
        List<JobOperationLogBO> logs = mongoTemplate.find(query, JobOperationLogBO.class,
                MongoCollectionNameCenter.OPERATION_LOG);
        List<Long> userIds = MyCollectionUtils.listToConvertList(logs, JobOperationLogBO::getCreateUserId);
        List<UserPO> users = userService.selectAllByIdList(userIds);
        Map<Long, UserPO> userMap = MyCollectionUtils.listToMap(users, UserPO::getUserId);
        return logs.stream().peek(log -> {
            UserPO user = userMap.get(log.getCreateUserId());
            if (user != null) {
                log.setCreatedByUserName(user.getName());
            }
        }).map(OperationLogServiceImpl::operationLog2DialogFlowLog).collect(Collectors.toList());
    }

    @Override
    public PageResultObject<DialogFlowLogInfoDTO> queryDialogFlowLog(DialogLogQueryVO condition) {
        OperationLogQueryVO query = new OperationLogQueryVO();
        query.setRefId(condition.getDialogFlowId());
        query.setLogType(CRM_SHOW_DIALOG_FLOW);
        query.setPageNum(condition.getPageNum());
        query.setPageSize(condition.getPageSize());
        query.setCreateUserId(condition.getUserId());
        if (Objects.nonNull(condition.getStartDateTime())) {
            query.setStartDatetime(condition.getStartDateTime().atTime(0, 0));
        }
        if (Objects.nonNull(condition.getEndDateTime())) {
            query.setEndDatetime(condition.getEndDateTime().atTime(23, 59));
        }
        query.setExtraDialogResourceTypeList(condition.getResourceTypeList());
        query.setExcludeOpeOnly(condition.getExcludeOpeOnly());

        PageResultObject operationLogPageInfo = selectByCondition(query);
        if (CollectionUtils.isEmpty(operationLogPageInfo.getContent())) {
            return operationLogPageInfo;
        }
        List<JobOperationLogBO> operationLogs = operationLogPageInfo.getContent();
        List<DialogFlowLogInfoDTO> resList = new ArrayList<>();
        operationLogs.forEach(x -> resList.add(operationLog2DialogFlowLog(x)));
        operationLogPageInfo.setContent(resList);
        return operationLogPageInfo;
    }

    public PageResultObject<DialogFlowLogInfoPO> findDialogFlowAudit(int pageNum, int pageSize) {
        Query query = new Query();
        query.with(PageRequest.of(pageNum-1, pageSize, Sort.Direction.DESC, DialogFlowLogInfoPO.SORT_ATTRIBUTE));
        List<DialogFlowLogInfoPO> dialogFlowLogInfos = mongoTemplate.find(query, DialogFlowLogInfoPO.class, DialogFlowLogInfoPO.COLLECTION_NAME);
        PageResultObject<DialogFlowLogInfoPO> pageResultObject = PageResultObject.of(dialogFlowLogInfos);
        pageResultObject.setTotalElements(mongoTemplate.count(query, DialogFlowLogInfoPO.COLLECTION_NAME));
        return pageResultObject;
    }

    private static DialogFlowLogInfoDTO operationLog2DialogFlowLog(JobOperationLogBO jobOperationLog) {
        DialogFlowLogInfoDTO result = new DialogFlowLogInfoDTO();
        result.setOperatorName(jobOperationLog.getCreatedByUserName());
        result.setDetail(jobOperationLog.getDescription());
        result.setDialogFlowId(jobOperationLog.getRefId());
        result.setOperateTime(jobOperationLog.getCreateTime());
        DialogFlowAuditActionEnum action = jobOperationLog.getExtraDialogAction() == null ?
                DialogFlowAuditActionEnum.getByLogType(jobOperationLog.getOperationType()) : jobOperationLog.getExtraDialogAction();
        result.setAction(action);
        result.setResourceType(jobOperationLog.getExtraDialogResourceType());
        result.setRequestId(jobOperationLog.getRequestId());
        result.setHost(jobOperationLog.getHost());
        result.setOperateLogType(jobOperationLog.getOperationType());
        return result;
    }

    private OperationLogPO initOperationLog(Long robotCallJobId, Long tenantId, Long userId, OperationLogLogTypeEnum operationLogLogType) {
        OperationLogPO operationLog = new OperationLogPO();
        operationLog.setTenantId(tenantId);
        operationLog.setCreateUserId(userId);
        operationLog.setRefId(robotCallJobId);
        operationLog.setLogType(operationLogLogType);
        //判断是否是OPE登录AICC的用户
        UserPO aiccUser = loginToCrmService.getAiccUserForOpeLogin(userId);
        if (Objects.nonNull(aiccUser)) {
            operationLog.setModule(SystemEnum.OPE_LOGIN_AICC);
        } else {
            operationLog.setModule(SystemEnum.AICC);
        }
        return operationLog;
    }
    private void buildAndAddLogRobotCallJob(Long robotCallJobId, Long tenantId, Long userId, OperationLogOperationTypeEnum operationLogOperationType){
        buildAndAddLogRobotCallJob(robotCallJobId,tenantId,userId, ROBOTCALLJOB,operationLogOperationType,null,false);
    }

    //对于来自openAPI的操作，单独处理
    private void buildAndAddLogRobotCallJobOpenAPI(Long robotCallJobId, Long tenantId, Long userId,
                                                   OperationLogOperationTypeEnum operationLogOperationType, Boolean isOpenApi) {
        buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, userId, operationLogOperationType,
                null, isOpenApi);
    }

    private void buildAndAddLogCsBatchCallJob(Long robotCallJobId, Long tenantId, Long userId, OperationLogOperationTypeEnum operationLogOperationType){
        buildAndAddLogRobotCallJob(robotCallJobId,tenantId,userId, BATCH_JOB,operationLogOperationType,null,true);
    }


    private void buildAndAddLogRobotCallJob(Long robotCallJobId, Long tenantId, Long userId, OperationLogLogTypeEnum operationLogLogType,
                                            OperationLogOperationTypeEnum operationLogOperationType,
                                            List<String> operationContent,Boolean isBatchJob){
        if (CollectionUtils.isEmpty(operationContent)){
            operationContent = new ArrayList<>();
            operationContent.add(operationLogOperationType.getDesc());
        }
        OperationLogPO operationLog = initOperationLog(robotCallJobId, tenantId, userId, operationLogLogType);
        operationLog.setOperationType(operationLogOperationType);
        //isBatchJob 是来判断是预测试外呼还是AI外呼
        String description = getDescription(robotCallJobId,tenantId,userId,operationLogOperationType,isBatchJob);
        if (isBatchJob) {
            operationLog.setSubModule(SystemEnum.CUSTOMER_SERVICE);
        } else {
            operationLog.setSubModule(SystemEnum.CALL_OUT);
        }
        operationLog.setDescription(description);
        operationLog.setOperationContent(operationContent);
        addToMongo(operationLog);
    }
    private void buildAndAddLogRobotCallJobOpenAPI(Long robotCallJobId, Long tenantId, Long userId,
                                                   OperationLogOperationTypeEnum operationLogOperationType,
                                                   List<String> operationContent, Boolean isOpenApi){
        if (CollectionUtils.isEmpty(operationContent)){
            operationContent = new ArrayList<>();
            operationContent.add(operationLogOperationType.getDesc());
        }
        OperationLogPO operationLog = initOperationLog(robotCallJobId, tenantId, userId, ROBOTCALLJOB);
        operationLog.setOperationType(operationLogOperationType);
        //isBatchJob 是来判断是预测试外呼还是AI外呼
        String description = getDescription(robotCallJobId,tenantId,userId,operationLogOperationType, false);
        if(isOpenApi){
            operationLog.setModule(SystemEnum.OPENAPI);
        }
	    operationLog.setSubModule(SystemEnum.CALL_OUT);
	    operationLog.setDescription(description);
        operationLog.setOperationContent(operationContent);
        addToMongo(operationLog);
    }
    /**
     * 预测试外呼任务和AI外呼获取description
     */
    private String getDescription(Long robotCallJobId, Long tenantId, Long userId, OperationLogOperationTypeEnum operationLogOperationType,Boolean isBatchJob){
	    return isBatchJob ? "用户 " +getUserName(userId)+ "[" + userId + "]在 " + MyDateUtils.formatLocalDateTime(null) + operationLogOperationType.getDesc() + getCsBatchCallJobName(tenantId,robotCallJobId) + "[" + robotCallJobId + "]"
	            : "用户 " +getUserName(userId)+ "[" + userId + "]在 " + MyDateUtils.formatLocalDateTime(null) + operationLogOperationType.getDesc() + getRobotCallJobName(robotCallJobId) + "[" + robotCallJobId + "]";
    }

    private void buildAndAddLogSmsJob(Long smsJobId, Long tenantId, Long userId, OperationLogOperationTypeEnum operationLogOperationType){
        OperationLogPO operationLog = initOperationLog(smsJobId, tenantId, userId, SMS);
        operationLog.setSubModule(SystemEnum.SMS_PLATFORM);
        operationLog.setOperationType(operationLogOperationType);
        String description = "用户 " +getUserName(userId)+ "[" + userId + "]在 " + MyDateUtils.formatLocalDateTime(null) + operationLogOperationType.getDesc() + getSmsJobName(smsJobId) + "[" + smsJobId + "]";
        operationLog.setDescription(description);
        addToMongo(operationLog);
    }

    private String getRobotCallJobName(Long robotCallJobId) {
        return robotCallJobService.getRobotCallJobName(robotCallJobId);
    }

    private String getCsBatchCallJobName(Long tenantId,Long csBatchCallJobId) {
        String name = "";
        CsBatchCallJobPO csBatchCallJobPO = csBatchCallJobService.getCsBatchCallJobById(tenantId, csBatchCallJobId);
        if(Objects.nonNull(csBatchCallJobPO)){
            name = csBatchCallJobPO.getJobName();
        }
        return name;
    }

    private String getSmsJobName(Long smsJobId){
        SmsJobPO smsJobPO = smsJobService.selectByKey(smsJobId);
        return smsJobPO == null ? "" : smsJobPO.getName();
    }

    private String getUserName(Long userId) {
        UserPO user = userService.selectByKey(userId);
        return user.getName();
    }

    @Override
    public void onSmsJobCreate(Long smsJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogSmsJob(smsJobId, tenantId, userId, ROBOTCALLJOB_CREATE);
        } catch (Exception e) {
            logger.error("任务日志生成失败", e);
        }
    }

    @Override
    public void onSmsJobModify(Long smsJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogSmsJob(smsJobId, tenantId, userId, ROBOTCALLJOB_UPDATE);
        } catch (Exception e) {
            logger.error("任务日志生成失败", e);
        }
    }

    @Override
    public void onSmsJobDelete(Long smsJobId, Long tenantId, Long userId) {
        try {
            buildAndAddLogSmsJob(smsJobId, tenantId, userId, ROBOTCALLJOB_DELETE);
        } catch (Exception e) {
            logger.error("任务日志生成失败", e);
        }
    }

    @Override
    public void onSmsJobExecution(Long smsJobId, Long tenantId, Long userId, JobOperationEnum operation) {
        try {
            buildAndAddLogSmsJob(smsJobId, tenantId, userId, operation.getLogType());
        }catch (Exception e) {
            logger.error("任务执行日志生成失败", e);
        }
    }

	@Override
	public void onImportCustomerPersonToPlan(Long tenantId, Long callOutPlanId, String callOutPlanName, Collection<String> jobNames, Long createUserId, String jobType) {
    	if (CollectionUtils.isEmpty(jobNames)) {
    		logger.error("所选任务为空,不生成操作日志");
	    }
		OperationLogPO operationLog = new OperationLogPO();
		operationLog.setTenantId(tenantId);
		operationLog.setCreateUserId(createUserId);
		operationLog.setRefId(callOutPlanId);
		operationLog.setLogType(CALL_OUT_PLAN);
		UserPO aiccUser = loginToCrmService.getAiccUserForOpeLogin(createUserId);
		if (Objects.nonNull(aiccUser)) {
			operationLog.setModule(SystemEnum.OPE_LOGIN_AICC);
		} else {
			operationLog.setModule(SystemEnum.AICC);
		}
		operationLog.setSubModule(null);
		operationLog.setOperationType(CALL_OUT_PLAN_IMPORT_CUSTOMER);
        List<String> content = new ArrayList<>();
        content.add("导入模式："+ jobType);
        content.add("导入任务名: " + String.join("/", jobNames));
		String desc = "导入客户:" + "\n导入模式："+ jobType + "\n导入任务名:" + String.join(",", jobNames);
		operationLog.setDescription(desc);
		operationLog.setOperationContent(content);
		addToMongo(operationLog);
	}

    @Override
    public void addLoginLog(Long userId, Long tenantId, Long distributorId, String desc, SystemEnum system) {
        try {
            addLoginLog(userId, tenantId, distributorId, desc, system, ACCOUNT);
        } catch (Exception e) {
            logger.error("记录用户登陆日志信息异常", e);
        }
    }

    @Override
    public void addLoginLog(Long userId, Long tenantId, Long distributorId, String desc, SystemEnum system, OperationLogLogTypeEnum operationLogLogTypeEnum) {
        try {
            OperationLogPO operationLogPO = new OperationLogPO();
            operationLogPO.setModule(system);
            operationLogPO.setCreateUserId(userId);
            operationLogPO.setDistributorId(distributorId);
            operationLogPO.setTenantId(tenantId);
            operationLogPO.setUpdateUserId(userId);
            operationLogPO.setCreateTime(LocalDateTime.now());
            operationLogPO.setLogType(operationLogLogTypeEnum);
            operationLogPO.setDescription(desc);
            operationLogPO.setOperationContent(Collections.singletonList(desc));
            operationLogPO.setOperationType(USER_LOGIN);
            operationLogPO.setRefId(userId);
            addToMongo(operationLogPO);
        } catch (Exception e) {
            logger.error("记录用户登陆日志信息异常", e);
        }
    }

    @Override
    public void addFenxiangLog(String description, OperationLogOperationTypeEnum type, Long tenantId) {
        OperationLogPO operationLogPO = new OperationLogPO();
        operationLogPO.setOperationType(type);
        operationLogPO.setModule(SystemEnum.OPENAPI);
        operationLogPO.setLogType(FENXIANGXIAOKE_OPERATION);
        operationLogPO.setRefId(tenantId);
        operationLogPO.setDistributorId(0L);
        operationLogPO.setDescription(description);
        addToMongo(operationLogPO);
    }

    @Override
    public void addCallOutPlanLog(String description, OperationLogOperationTypeEnum type, Long refId, String content, Long tenantId, Long userId) {
        List<String> contentList = new ArrayList<>(4);
        contentList.add(content);
        addCallOutPlanLog(description, type, refId, contentList, tenantId, userId);
    }

    @Override
    public void addCallOutPlanLog(String description, OperationLogOperationTypeEnum type, Long refId, List<String> content, Long tenantId, Long userId) {
        OperationLogPO operationLogPO = new OperationLogPO();
        operationLogPO.setOperationType(type);
        operationLogPO.setModule(SystemEnum.AICC);
        operationLogPO.setOperationContent(content);
        operationLogPO.setLogType(CALL_OUT_PLAN);
        operationLogPO.setRefId(refId);
        operationLogPO.setTenantId(tenantId);
        operationLogPO.setDistributorId(0L);
        operationLogPO.setDescription(description);
        operationLogPO.setCreateUserId(userId);
        addToMongo(operationLogPO);
    }

    @Override
    public List<Long> queryOperateUserIdListByRefId(OperationLogLogTypeEnum logType, Long refId) {
        List<AggregationOperation> aggregationOperationList = new ArrayList<>();

        aggregationOperationList.add(Aggregation.match(Criteria.where("refId").is(refId)));
        aggregationOperationList.add(Aggregation.match(Criteria.where("logType").is(logType.name())));

        AggregationOperation groupOperation = Aggregation.group("refId", "createUserId")
                .first("createUserId").as("createUserId");
        aggregationOperationList.add(groupOperation);
        Aggregation aggregation = Aggregation.newAggregation(aggregationOperationList);

        List<OperationLogPO> list = mongoTemplate.aggregate(aggregation, MongoCollectionNameCenter.OPERATION_LOG, OperationLogPO.class).getMappedResults();
        return list.stream()
                .map(OperationLogPO::getCreateUserId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getTypeList(SystemEnum systemType) {

        Map<String, String> result = new HashMap<>();

        if (Objects.equals(SystemEnum.OPE_LOGIN_AICC, systemType)) {
            result.put(ROBOTCALLJOB.name(), "AI外呼任务日志");
            result.put(CRM_SHOW_DIALOG_FLOW.name(), "标准版话术配置");
            result.put(INTENT_LEVEL_TAG.name(), "意向标签");
            result.put(AI_CONTACT_HISTORY.name(), "AI外呼联系历史");
            result.put(CALL_OUT_CONFIG.name(), "AI外呼通用设置");
            result.put(BATCH_JOB.name(), "预测式外呼任务日志");
            result.put(INTELLIGENT_ASSISTANT.name(), "智能辅助");
            result.put(MANUAL_CONTACT_HISTORY.name(), "人工外呼联系历史");
            result.put(SEAT_MANAGEMENT.name(), "语音坐席管理");
            result.put(RECEPTION_SCENE.name(), "接待场景日志");
            result.put(CALL_IN_CONTACT_HISTORY.name(), "呼入联系历史");
            result.put(SMS.name(), "短信任务日志");
            result.put(SMS_TEMPLATE.name(), "短信模板创建日志");
            result.put(SMS_SEND_HISTORY.name(), "短信发送历史");
            result.put(CUSTOMER.name(), "我的客户日志");
            result.put(CUSTOMER.name(), "客户公海日志");
            result.put(BLACKLIST_MANAGEMENT.name(), "黑名单管理");
            result.put(CUSTOMER_CONTACT_HISTORY.name(), "客户中心联系历史");
            result.put(PHONE_SMS_CONFIG.name(), "线路/短信设置");
            result.put(USER.name(), "团队管理");
        } else {
            for (OperationLogLogTypeEnum type: OperationLogLogTypeEnum.values()) {
                result.put(type.name(), type.getDesc());
            }
        }

        return result;
    }

    public void addRecharge(Long userId, Long tenantId, Long distributorId, String desc, SystemEnum system, OperationLogLogTypeEnum operationLogLogTypeEnum) {
        try {
            OperationLogPO operationLogPO = new OperationLogPO();
            operationLogPO.setModule(system);
            operationLogPO.setCreateUserId(userId);
            operationLogPO.setDistributorId(distributorId);
            operationLogPO.setTenantId(tenantId);
            operationLogPO.setUpdateUserId(userId);
            operationLogPO.setCreateTime(LocalDateTime.now());
            operationLogPO.setLogType(operationLogLogTypeEnum);
            operationLogPO.setDescription(desc);
            operationLogPO.setOperationType(ACCOUNT_RECHARGE);
            addToMongo(operationLogPO);
        } catch (Exception e) {
            logger.error("记录用户登陆日志信息异常", e);
        }
    }

    /**
     * 坐席管理日志
     */
    @Override
    public void addSeatLog(Long userId, Long tenantId, Long distributorId, String desc, OperationLogOperationTypeEnum operationLogOperationTypeEnum, List<String> operationContent, Long staffId) {
        try {
            OperationLogPO operationLogPO = new OperationLogPO();
            operationLogPO.setModule(SystemEnum.AICC);
            operationLogPO.setSubModule(SystemEnum.CALL_OUT);
            operationLogPO.setCreateUserId(userId);
            operationLogPO.setDistributorId(distributorId);
            operationLogPO.setTenantId(tenantId);
            operationLogPO.setUpdateUserId(userId);
            operationLogPO.setCreateTime(LocalDateTime.now());
            operationLogPO.setDescription(desc);
            operationLogPO.setLogType(SEAT_MANAGEMENT);
            operationLogPO.setOperationType(operationLogOperationTypeEnum);
            operationLogPO.setOperationContent(operationContent);
            operationLogPO.setRefId(staffId);
            addToMongo(operationLogPO);
        } catch (Exception e) {
            logger.error("坐席组管理日志信息异常", e);
        }
    }

    @Override
    public void addISVUpdateLog(Long userId, Long tenantId, Long distributorId, String desc, SystemEnum systemEnum) {
        OperationLogPO operationLogPO = new OperationLogPO();
        // 操作类型
        operationLogPO.setOperationType(ISV_UPDATE);
        operationLogPO.setModule(systemEnum);
        operationLogPO.setSubModule(SystemEnum.DEFAULT);
        operationLogPO.setOperationContent(Collections.singletonList(desc));
        // 日志类型
        operationLogPO.setLogType(ISV);
        operationLogPO.setRefId(tenantId);
        operationLogPO.setTenantId(tenantId);
        operationLogPO.setDistributorId(distributorId == null ? 0L : distributorId);
        operationLogPO.setDescription(desc);
        operationLogPO.setCreateUserId(userId);
        addToMongo(operationLogPO);
    }

    @Override
    public void addISVUpdateLog(Long userId, Long tenantId, Long distributorId, String desc, SystemEnum systemEnum, OperationLogLogTypeEnum logType) {
        OperationLogPO operationLogPO = new OperationLogPO();
        // 操作类型
        operationLogPO.setOperationType(ISV_UPDATE);
        operationLogPO.setModule(systemEnum);
        operationLogPO.setSubModule(SystemEnum.DEFAULT);
        operationLogPO.setOperationContent(Collections.singletonList(desc));
        // 日志类型
        operationLogPO.setLogType(logType);
        operationLogPO.setRefId(tenantId);
        operationLogPO.setTenantId(tenantId);
        operationLogPO.setDistributorId(distributorId == null ? 0L : distributorId);
        operationLogPO.setDescription(desc);
        operationLogPO.setCreateUserId(userId);
        addToMongo(operationLogPO);
    }

    @Override
    public void addRobotCallJobLog(OperationLogAddVO addVO) {
        OperationLogPO operationLogPO = new OperationLogPO();
        operationLogPO.setOperationType(addVO.getOperationType());
        operationLogPO.setModule(SystemEnum.AICC);
        operationLogPO.setOperationContent(Lists.newArrayList(addVO.getContent()));
        operationLogPO.setLogType(ROBOTCALLJOB);
        operationLogPO.setRefId(addVO.getRobotCallJobId());
        operationLogPO.setTenantId(addVO.getTenantId());
        operationLogPO.setDistributorId(0L);
        operationLogPO.setDescription(addVO.getContent());
        operationLogPO.setCreateUserId(addVO.getCurrentUserId());
        addToMongo(operationLogPO);
    }

	@Override
	public void addDeleteCallRecordLog(Long tenantId, String description) {
		OperationLogPO operationLogPO = new OperationLogPO();
		operationLogPO.setOperationType(null);
		operationLogPO.setModule(SystemEnum.AICC);
		operationLogPO.setOperationContent(Collections.singletonList(description));
		operationLogPO.setLogType(AI_CONTACT_HISTORY);
		operationLogPO.setRefId(null);
		operationLogPO.setTenantId(tenantId);
		operationLogPO.setDistributorId(0L);
		operationLogPO.setDescription(description);
		operationLogPO.setCreateUserId(null);
		addToMongo(operationLogPO);
	}

    @Override
    public void geTuiImportRobotCallJobLog(Long robotCallJobId, Long tenantId, Long currentUserId, List<String> operationContent, Boolean isOpenApi) {
        try {
            if (BooleanUtils.isTrue(isOpenApi)) {
                buildAndAddLogRobotCallJobOpenAPI(robotCallJobId, tenantId, currentUserId, OperationLogOperationTypeEnum.ROBOTCALLJOB_IMPORT, operationContent, isOpenApi);
            }else {
                buildAndAddLogRobotCallJob(robotCallJobId, tenantId, currentUserId, OperationLogLogTypeEnum.ROBOTCALLJOB, OperationLogOperationTypeEnum.ROBOTCALLJOB_IMPORT, operationContent,false);
            }
        } catch (Exception e) {
            logger.error("个推导入客户到任务日志生成失败", e);
        }
    }

    @Override
    public void geTuiImportSmsJobLog(Long smsJobId, Long tenantId, Long userId, List<String> operationContent) {
        try {
            OperationLogPO operationLog = initOperationLog(smsJobId, tenantId, userId, SMS);
            operationLog.setSubModule(SystemEnum.SMS_PLATFORM);
            operationLog.setOperationType(OperationLogOperationTypeEnum.BATCH_JOB_IMPORT);
            String description = "用户 " +getUserName(userId)+ "[" + userId + "]在 " + MyDateUtils.formatLocalDateTime(null) + OperationLogOperationTypeEnum.BATCH_JOB_IMPORT.getDesc() + getSmsJobName(smsJobId) + "[" + smsJobId + "]";
            operationLog.setDescription(description);
            operationLog.setOperationContent(operationContent);
            addToMongo(operationLog);
        } catch (Exception e) {
            logger.error("个推导入客户到短信任务日志生成失败", e);
        }
    }
}
