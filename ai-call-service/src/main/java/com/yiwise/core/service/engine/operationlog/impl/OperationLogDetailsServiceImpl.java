package com.yiwise.core.service.engine.operationlog.impl;

import com.github.pagehelper.Page;
import com.mongodb.client.*;
import com.mongodb.client.model.Filters;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.core.config.UnitConstant;
import com.yiwise.core.dal.entity.*;
import com.yiwise.core.model.bo.operationlog.OperationLogDetailsBO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.basic.AbstractQueryVO;
import com.yiwise.core.model.vo.operationlog.*;
import com.yiwise.core.model.vo.phonenumber.BindOnePhoneNumberVO;
import com.yiwise.core.model.vo.phonenumber.UpdateBindOnePhoneNumberVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberConfigVO;
import com.yiwise.core.model.vo.sms.SmsCostStatisticsRequestParam;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberConfigVO;
import com.yiwise.core.service.engine.operationlog.OperationLogDetailsService;
import com.yiwise.core.service.mongo.MongoCollectionNameCenter;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.core.service.opensips.DailyStatLineSupplierGwStatisticsService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.service.sms.SmsCostBillingStatisticsService;
import com.yiwise.lcs.api.dto.SmsPlatformChannelDTO;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @date: 2022 06 07 14:20
 */
@Service
public class OperationLogDetailsServiceImpl implements OperationLogDetailsService {
    private static final Logger logger = LoggerFactory.getLogger(OperationLogDetailsServiceImpl.class);

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private TenantService tenantService;
    @Resource
    private UserService userService;
    @Resource
    private MongoDatabase mongoDatabase;
    @Resource
    private DailyStatLineSupplierGwStatisticsService dailyStatLineSupplierGwStatisticsService;
    @Resource
    private SmsCostBillingStatisticsService smsCostBillingStatisticsService;


    @Override
    public void createOperationLogDetails(List<OperationLogDetailsPO> operationDetailsList, Long tenantId, Long distributorId, Long createUserId,
                                          String operationObject, CostOperationLogTypeEnum costOperationLogType, LocalDateTime createTime,
                                          String oldInformation, String newInformation, String unit) {

        OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
        operationLogDetailsPO.setTenantId(tenantId);
        operationLogDetailsPO.setDistributorId(distributorId);
        operationLogDetailsPO.setCreateUserId(createUserId);
        operationLogDetailsPO.setOperationObject(operationObject);
        operationLogDetailsPO.setCreateTime(createTime);
        operationLogDetailsPO.setCostOperationLogType(costOperationLogType);
        operationLogDetailsPO.setOldInformation(oldInformation);
        operationLogDetailsPO.setNewInformation(newInformation);
        operationLogDetailsPO.setUnit(unit);
        String details = oldInformation + "→" + newInformation + unit;
        operationLogDetailsPO.setLogDetail(details);

        operationDetailsList.add(operationLogDetailsPO);
    }

    @Override
    public void insertOperationLogDetails(Long tenantId, Long distributorId, Long createUserId, CostOperationLogTypeEnum costOperationLogType,
                                          LocalDateTime createTime, String details, String oldInformation, String newInformation) {
        try {
            OperationLogDetailsPO operationLogDetailsPO = createLogDetail(tenantId, distributorId, createUserId, costOperationLogType, createTime, details, oldInformation, newInformation, null);

            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e){
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

    @Override
    public void insertOperationLogDetails(Long tenantId, Long distributorId, Long createUserId, CostOperationLogTypeEnum costOperationLogType, LocalDateTime createTime, String details, String oldInformation, String newInformation, String unit) {
        try {
            OperationLogDetailsPO operationLogDetailsPO = createLogDetail(tenantId, distributorId, createUserId, costOperationLogType, createTime, details, oldInformation, newInformation, unit);

            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e){
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

    private OperationLogDetailsPO createLogDetail(Long tenantId, Long distributorId, Long createUserId, CostOperationLogTypeEnum costOperationLogType,
                                                  LocalDateTime createTime, String details, String oldInformation, String newInformation, String unit) {
        TenantPO tenantPO = tenantService.selectByKey(tenantId);
        OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
        operationLogDetailsPO.setTenantId(tenantId);
        operationLogDetailsPO.setDistributorId(distributorId);
        operationLogDetailsPO.setCreateUserId(createUserId);
        operationLogDetailsPO.setOperationObject(tenantPO.getCompanyName());
        operationLogDetailsPO.setCreateTime(createTime);
        operationLogDetailsPO.setCostOperationLogType(costOperationLogType);
        operationLogDetailsPO.setOldInformation(oldInformation);
        operationLogDetailsPO.setNewInformation(newInformation);
        operationLogDetailsPO.setLogDetail(details);
        operationLogDetailsPO.setUnit(unit);

        return operationLogDetailsPO;
    }

    @Override
    public void addVoiceQualityControlOperationLogDetails(TenantPO tenantPO, Long createUserId, Boolean enableVoiceQualityControl) {
        try {
            OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
            operationLogDetailsPO.setTenantId(tenantPO.getTenantId());
            operationLogDetailsPO.setDistributorId(tenantPO.getDistributorId());
            operationLogDetailsPO.setCreateUserId(createUserId);
            operationLogDetailsPO.setOperationObject(tenantPO.getCompanyName());
            operationLogDetailsPO.setCreateTime(LocalDateTime.now());
            operationLogDetailsPO.setCostOperationLogType(CostOperationLogTypeEnum.SET_QC);
            operationLogDetailsPO.setOldInformation(tenantPO.getEnableVoiceQualityControl()?"开通":"关闭");
            if (enableVoiceQualityControl) {
                operationLogDetailsPO.setNewInformation("开通");
                operationLogDetailsPO.setLogDetail("开通智能质检");
            }else {
                operationLogDetailsPO.setNewInformation("关闭");
                operationLogDetailsPO.setLogDetail("关闭智能质检");
            }
            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e) {
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

    @Override
    public void addOpeCustomerBindLineOperationLogDetails(BindOnePhoneNumberVO addVO, TenantPhoneNumberPO savePO, TenantPhoneNumberPO tenantPhoneNumberPO, Long userId, PhoneNumberPO phoneNumberPO) {

        try {
            TenantPO tenantPO = tenantService.selectByKey(addVO.getTenantId());
            OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
            operationLogDetailsPO.setTenantId(addVO.getTenantId());
            operationLogDetailsPO.setDistributorId(tenantPO.getDistributorId());
            operationLogDetailsPO.setCreateUserId(userId);
            operationLogDetailsPO.setOperationObject(tenantPO.getCompanyName());
            operationLogDetailsPO.setCreateTime(LocalDateTime.now());
            operationLogDetailsPO.setCostOperationLogType(CostOperationLogTypeEnum.CUSTOMER_CONFIG_LINE);
            String phoneNumber = phoneNumberPO.getPhoneNumber();

            Double localBillRate = addVO.getLocalBillRate() != null ? addVO.getLocalBillRate() * 10 : 0;
            Double otherBillRate = addVO.getOtherBillRate() != null ? addVO.getOtherBillRate() * 10 : 0;
            Double callInLocalBillRate = addVO.getCallInLocalBillRate() != null ? addVO.getCallInLocalBillRate() * 10 : 0;
            Double callInOtherBillRate = addVO.getCallInOtherBillRate() != null ? addVO.getCallInOtherBillRate() * 10 : 0;
            Double monthlyBillRate = addVO.getMonthlyBillRate() != null ? addVO.getMonthlyBillRate() * 1000 : 0;
            savePO.setLocalBillRate(localBillRate.longValue());
            savePO.setOtherBillRate(otherBillRate.longValue());
            savePO.setMonthlyBillRate(monthlyBillRate.longValue());
            savePO.setCallInLocalBillRate(callInLocalBillRate.longValue());
            savePO.setCallInOtherBillRate(callInOtherBillRate.longValue());
            savePO.setCallInBillMode(addVO.getCallInBillMode());

            if (Objects.isNull(tenantPhoneNumberPO)) {
                String callInBillMode = savePO.getCallInBillMode() != null ? savePO.getCallInBillMode().getDesc() : "";

                String newInfo = "外呼话费："+"本地话费："+ savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "外地话费：" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "呼入话费：\n"
                        + callInBillMode + "：\n"
                        + "月租：" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                        + "本地话费：" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                        + "外地话费：" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n" ;

                String details =  "【" + phoneNumber + "】" + "外呼话费：\n"
                        + "本地话费：" + "-- →" + savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "外地话费：" + "-- →" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "呼入话费：\n"
                        + "-- →" + callInBillMode + "：\n"
                        + "月租：" + "-- →" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                        + "本地话费："+ "-- →" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                        + "外地话费：" + "-- →" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n" ;
                operationLogDetailsPO.setOldInformation("--");
                operationLogDetailsPO.setNewInformation(newInfo);
                operationLogDetailsPO.setLogDetail(details);
            }else {
                boolean callout = false;
                if (!savePO.getLocalBillRate().equals(tenantPhoneNumberPO.getLocalBillRate())) {
                    callout = true;
                }
                if (!savePO.getOtherBillRate().equals(tenantPhoneNumberPO.getOtherBillRate())) {
                    callout = true;
                }
                if (!savePO.getCallInBillMode().equals(tenantPhoneNumberPO.getCallInBillMode())) {
                    callout = true;
                }
                if (!savePO.getMonthlyBillRate().equals(tenantPhoneNumberPO.getMonthlyBillRate())) {
                    callout = true;
                }
                if (!savePO.getCallInLocalBillRate().equals(tenantPhoneNumberPO.getCallInLocalBillRate())) {
                    callout = true;
                }
                if (!savePO.getCallInOtherBillRate().equals(tenantPhoneNumberPO.getCallInOtherBillRate())) {
                    callout = true;
                }

               if (callout) {
                   String oldCallInBillMode = tenantPhoneNumberPO.getCallInBillMode() != null ? tenantPhoneNumberPO.getCallInBillMode().getDesc() : "";
                   String newCallInBillMode = savePO.getCallInBillMode() != null ? savePO.getCallInBillMode().getDesc() : "";
                   String oldInfo = "外呼话费："
                           + "本地话费：" + tenantPhoneNumberPO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "外地话费：" + tenantPhoneNumberPO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "呼入话费：\n"
                           + oldCallInBillMode + "：\n"
                           + "月租：" + tenantPhoneNumberPO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                           + "本地话费：" + tenantPhoneNumberPO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                           + "外地话费：" + tenantPhoneNumberPO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n" ;

                   String newInfo = "外呼话费："
                           + "本地话费：" + savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "外地话费：" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "呼入话费：\n"
                           + newCallInBillMode + "：\n"
                           + "月租：" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                           + "本地话费：" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                           + "外地话费：" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n" ;

                   String details =  "【" + phoneNumber + "】" + "外呼话费：\n"
                           + "本地话费：" + tenantPhoneNumberPO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE +"→" + savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "外地话费：" + tenantPhoneNumberPO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE +"→" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                           + "呼入话费：\n"
                           + oldCallInBillMode + "→" + newCallInBillMode + "：\n"
                           + "月租：" + tenantPhoneNumberPO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + "→" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                           + "本地话费："+ tenantPhoneNumberPO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE +"→" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                           + "外地话费："+ tenantPhoneNumberPO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE +"→" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n" ;
                   operationLogDetailsPO.setOldInformation(oldInfo);
                   operationLogDetailsPO.setNewInformation(newInfo);
                   operationLogDetailsPO.setLogDetail(details);
               }else {
                   return;
               }
            }
            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e) {
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

    @Override
    public PageResultObject selectByQuery(OperationLogDetailsVO query) {
        Query queryInfo = new Query();

        if (Objects.nonNull(query.getCostOperationLogType()) && query.getCostOperationLogType().size() > 0) {
            queryInfo.addCriteria(Criteria.where("costOperationLogType").in(query.getCostOperationLogType()));
        }

        if (Objects.nonNull(query.getStartDatetime()) && Objects.nonNull(query.getEndDatetime())) {
            queryInfo.addCriteria(
                    Criteria.where("createTime")
                            .gte(query.getStartDatetime())
                            .lte(query.getEndDatetime())
            );
        } else {
            if (Objects.nonNull(query.getStartDatetime())) {
                queryInfo.addCriteria(Criteria.where("createTime").gte(query.getStartDatetime()));
            }
            if (Objects.nonNull(query.getEndDatetime())) {
                queryInfo.addCriteria(Criteria.where("createTime").lte(query.getEndDatetime()));
            }
        }

        if (StringUtils.isNotEmpty(query.getOperationObject())) {
            queryInfo.addCriteria(Criteria.where("operationObject").is(query.getOperationObject()));
        }

        if (Objects.nonNull(query.getCreateUserId())) {
            queryInfo.addCriteria(Criteria.where("createUserId").is(query.getCreateUserId()));
        }

        long total = mongoTemplate.count(queryInfo, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        if (total == 0) {
            return PageResultObject.of(Collections.EMPTY_LIST);
        }

        queryInfo.with(Sort.by(Sort.Direction.DESC, "createTime"));
        queryInfo.skip((query.getPageNum() - 1) * query.getPageSize()).limit(query.getPageSize());

        List<OperationLogDetailsBO>  operationLogDetailsList = mongoTemplate.find(queryInfo, OperationLogDetailsBO.class, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        List<Long> userIds = operationLogDetailsList.stream().map(OperationLogDetailsBO::getCreateUserId).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            List<UserPO> userList = userService.selectAllByIdList(userIds);
            Map<Long, String> userIdNameMap = userList.stream().collect(Collectors.toMap(UserPO::getUserId, UserPO::getName));
            // 纷享销客 默认id为0
            userIdNameMap.put(-1L, "纷享销客");
            operationLogDetailsList.forEach(x -> x.setCreateUserName(userIdNameMap.get(x.getCreateUserId())));
        }
        Page result = wrapList2Page(total, query, operationLogDetailsList);
        return PageResultObject.of(result);
    }

    public Page wrapList2Page(long total, AbstractQueryVO aqv, List list) {
        Page page = new Page<>();
        page.addAll(list);
        page.setPageNum(aqv.getPageNum());
        page.setPageSize(aqv.getPageSize());
        page.setTotal(total);
        return page;
    }


    @Override
    public List<OperationObjectQueryVO> selectByOperationObjectOrCreateUserId(OperationObjectTypeEnum query) {

        List<OperationObjectQueryVO> result = new ArrayList<>();
        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        // 操作对象
        if (query.equals(OperationObjectTypeEnum.operation_user_name)) {
            DistinctIterable<String> iterable = mongoCollection.distinct("operationObject", Filters.ne("costOperationLogType", CostOperationLogTypeEnum.SMS_CHANNEL.name()), String.class);
            MongoCursor<String> mongoCursor = iterable.iterator();
            while (mongoCursor.hasNext()) {
                OperationObjectQueryVO vo = new OperationObjectQueryVO();
                vo.setName(mongoCursor.next());
                result.add(vo);
            }
        }else if (query.equals(OperationObjectTypeEnum.operation_sms_name)) {
            DistinctIterable<String> iterable = mongoCollection.distinct("operationObject", Filters.eq("costOperationLogType", CostOperationLogTypeEnum.SMS_CHANNEL.name()), String.class);
            MongoCursor<String> mongoCursor = iterable.iterator();
            while (mongoCursor.hasNext()) {
                OperationObjectQueryVO vo = new OperationObjectQueryVO();
                vo.setName(mongoCursor.next());
                result.add(vo);
            }
        }else if (query.equals(OperationObjectTypeEnum.operation_user_id)) {
            // 操作人
            DistinctIterable<Long> distinctIterable = mongoCollection.distinct("createUserId", Long.class);
            MongoCursor<Long> mongoCursor = distinctIterable.iterator();

            while (mongoCursor.hasNext()) {
                OperationObjectQueryVO vo = new OperationObjectQueryVO();
                vo.setId(mongoCursor.next());
                result.add(vo);
            }
            List<Long> userIds = result.stream().map(OperationObjectQueryVO::getId).collect(Collectors.toList());
            if (!userIds.isEmpty()) {
                List<UserPO> userList = userService.selectAllByIdList(userIds);
                Map<Long, String> userIdNameMap = userList.stream().collect(Collectors.toMap(UserPO::getUserId, UserPO::getName));
                // 纷享销客 默认id为0
                userIdNameMap.put(-1L, "纷享销客");
                result.forEach(x -> x.setName(userIdNameMap.get(x.getId())));
            }
        }else if (query.equals(OperationObjectTypeEnum.operation_log_type)) {
            DistinctIterable<String> iterable = mongoCollection.distinct("costOperationLogType", String.class);
            MongoCursor<String> mongoCursor = iterable.iterator();
            while (mongoCursor.hasNext()) {
                OperationObjectQueryVO vo = new OperationObjectQueryVO();
                String str = mongoCursor.next();
                vo.setName(str);
                vo.setDesc(CostOperationLogTypeEnum.getDescByName(str));
                result.add(vo);
            }
        }
        return result;
    }

    @Override
    public PageResultObject selectByTenantIdAndType(OperationLogDetailsByTenantIdVO query) {
        Assert.notNull(query.getTenantId(), "tenantId不能为null");
        Assert.notNull(query.getCostOperationLogType(), "日志类型不能为null");
        Query queryInfo = new Query();

        queryInfo.addCriteria(Criteria.where("tenantId").is(query.getTenantId()));
        queryInfo.addCriteria(Criteria.where("costOperationLogType").in(query.getCostOperationLogType()));

        long total = mongoTemplate.count(queryInfo, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        if (total == 0) {
            return PageResultObject.of(Collections.EMPTY_LIST);
        }

        queryInfo.with(Sort.by(Sort.Direction.DESC, "createTime"));
        queryInfo.skip((query.getPageNum() - 1) * query.getPageSize()).limit(query.getPageSize());

        List<OperationLogDetailsBO>  operationLogDetailsList = mongoTemplate.find(queryInfo, OperationLogDetailsBO.class, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        List<Long> userIds = operationLogDetailsList.stream().map(OperationLogDetailsBO::getCreateUserId).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            List<UserPO> userList = userService.selectAllByIdList(userIds);
            Map<Long, String> userIdNameMap = userList.stream().collect(Collectors.toMap(UserPO::getUserId, UserPO::getName));
            // 纷享销客 默认id为0
            userIdNameMap.put(-1L, "纷享销客");
            operationLogDetailsList.forEach(x -> x.setCreateUserName(userIdNameMap.get(x.getCreateUserId())));
        }
        Page result = wrapList2Page(total, query, operationLogDetailsList);
        return PageResultObject.of(result);
    }

    @Override
    public void addSmsChannelOperationLogDetails(SmsPlatformChannelDTO channelDTO){
        StringBuilder newInfo = new StringBuilder();
        StringBuilder details = new StringBuilder();
        com.yiwise.lcs.api.enums.SmsPlatformEnum smsPlatform = channelDTO.getSmsPlatform();
        newInfo.append("【").append(smsPlatform.getDesc()).append("】")
                .append("成本价:").append(channelDTO.getSmsProductPrice()).append(UnitConstant.YUAN).append(" \n")
                .append("单价:").append(channelDTO.getSmsPrice()).append(UnitConstant.YUAN).append(" \n");
        details.append("【").append(smsPlatform.getDesc()).append("】")
                .append("成本价:--→").append(channelDTO.getSmsProductPrice()).append(UnitConstant.YUAN).append(" \n")
                .append("单价:--→").append(channelDTO.getSmsPrice()).append(UnitConstant.YUAN).append(" \n");

        OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
        operationLogDetailsPO.setCreateUserId(channelDTO.getCurrentUserId());
        operationLogDetailsPO.setOperationObject(channelDTO.getSmsChannelName());
        operationLogDetailsPO.setCreateTime(LocalDateTime.now());
        operationLogDetailsPO.setCostOperationLogType(CostOperationLogTypeEnum.SMS_CHANNEL);
        operationLogDetailsPO.setOldInformation("");
        operationLogDetailsPO.setNewInformation(newInfo.toString());
        operationLogDetailsPO.setLogDetail(details.toString());
        insertOperationLogDetailsToMongo(null, null, channelDTO.getCurrentUserId(), channelDTO.getSmsChannelName(),CostOperationLogTypeEnum.SMS_CHANNEL,
                LocalDateTime.now(), "", newInfo.toString(), details.toString());
    }

    @Override
    public void addUpdateSmsChannelOperationLogDetails(SmsPlatformChannelDTO channelDTO, SmsPlatformChannelDTO oldChannelDTO){

        StringBuilder details = new StringBuilder();
        StringBuilder oldInfo = new StringBuilder();
        StringBuilder newInfo = new StringBuilder();

        if (oldChannelDTO != null) {
            com.yiwise.lcs.api.enums.SmsPlatformEnum oldSmsPlatform = oldChannelDTO.getSmsPlatform();
            com.yiwise.lcs.api.enums.SmsPlatformEnum smsPlatform = channelDTO.getSmsPlatform();
            if (!oldChannelDTO.getSmsProductPrice().equals(channelDTO.getSmsProductPrice()) && !oldChannelDTO.getSmsPrice().equals(channelDTO.getSmsPrice())) {
                oldInfo.append("【").append(oldSmsPlatform.getDesc()).append("】")
                        .append("成本价:").append(oldChannelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n")
                        .append("单价:").append(oldChannelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                newInfo.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("成本价:").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n")
                        .append("单价:").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                details.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("成本价:").append(oldChannelDTO.getSmsProductPrice() / 1000d).append("→").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n")
                        .append("单价:").append(oldChannelDTO.getSmsPrice() / 1000d).append("→").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
            }else if (!oldChannelDTO.getSmsProductPrice().equals(channelDTO.getSmsProductPrice()) && oldChannelDTO.getSmsPrice().equals(channelDTO.getSmsPrice())) {
                oldInfo.append("【").append(oldSmsPlatform.getDesc()).append("】")
                        .append("成本价:").append(oldChannelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                newInfo.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("成本价:").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                details.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("成本价:").append(oldChannelDTO.getSmsProductPrice() / 1000d).append("→").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
            }else if (oldChannelDTO.getSmsProductPrice().equals(channelDTO.getSmsProductPrice()) && !oldChannelDTO.getSmsPrice().equals(channelDTO.getSmsPrice())) {
                oldInfo.append("【").append(oldSmsPlatform.getDesc()).append("】")
                        .append("单价:").append(oldChannelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                newInfo.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("单价:").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
                details.append("【").append(smsPlatform.getDesc()).append("】")
                        .append("单价:").append(oldChannelDTO.getSmsPrice() / 1000d).append("→").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
            }
        } else {
            com.yiwise.lcs.api.enums.SmsPlatformEnum smsPlatform = channelDTO.getSmsPlatform();
            newInfo.append("【").append(smsPlatform.getDesc()).append("】")
                    .append("成本价:").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n")
                    .append("单价:").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
            details.append("【").append(smsPlatform.getDesc()).append("】")
                    .append("成本价:--→").append(channelDTO.getSmsProductPrice() / 1000d).append(UnitConstant.YUAN).append(" \n")
                    .append("单价:--→").append(channelDTO.getSmsPrice() / 1000d).append(UnitConstant.YUAN).append(" \n");
        }

        insertOperationLogDetailsToMongo(null, null, channelDTO.getCurrentUserId(), channelDTO.getSmsChannelName(), CostOperationLogTypeEnum.SMS_CHANNEL,
                LocalDateTime.now(), oldInfo.toString(), newInfo.toString(), details.toString());
    }

    @Override
    public void insertOperationLogDetailsToMongo(Long tenant, Long distributorId, Long userId, String OperationObject, CostOperationLogTypeEnum type,
                                                 LocalDateTime time, String oldInfo, String newInfo, String details){

        try {
            OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
            operationLogDetailsPO.setTenantId(tenant);
            operationLogDetailsPO.setDistributorId(distributorId);
            operationLogDetailsPO.setCreateUserId(userId);
            operationLogDetailsPO.setOperationObject(OperationObject);
            operationLogDetailsPO.setCreateTime(time);
            operationLogDetailsPO.setCostOperationLogType(type);
            operationLogDetailsPO.setOldInformation(oldInfo);
            operationLogDetailsPO.setNewInformation(newInfo);
            operationLogDetailsPO.setLogDetail(details);

            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e) {
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

    @Override
    public void addQcCostUnitOperationLogDetails(TenantPO oldTenantPO, Long qcCostUnit, Long qcTextCostUnit, Long userId) {
        String details,oldInfo,newInfo;
        if (!oldTenantPO.getQcCostUnit().equals(qcCostUnit) && !oldTenantPO.getQcTextCostUnit().equals(qcTextCostUnit)) {
            oldInfo = "语音质检费用设置：" + oldTenantPO.getQcCostUnit() / 1000d + UnitConstant.YUAN_MINUTE + "文本质检费用设置：" + oldTenantPO.getQcTextCostUnit()  / 1000d + UnitConstant.YUAN_TIAO ;
            newInfo = "语音质检费用设置：" + qcCostUnit  / 1000d + UnitConstant.YUAN_MINUTE + "文本质检费用设置：" + qcTextCostUnit  / 1000d + UnitConstant.YUAN_TIAO ;
            details = "语音质检费用设置：" + oldTenantPO.getQcCostUnit()  / 1000d + "→" + qcCostUnit  / 1000d + UnitConstant.YUAN_MINUTE + " \n"
                    + "文本质检费用设置：" + oldTenantPO.getQcTextCostUnit()  / 1000d + "→" + qcTextCostUnit  / 1000d + UnitConstant.YUAN_TIAO ;
        }else if (!oldTenantPO.getQcCostUnit().equals(qcCostUnit) && oldTenantPO.getQcTextCostUnit().equals(qcTextCostUnit)) {
            oldInfo = "语音质检费用设置：" + oldTenantPO.getQcCostUnit()  / 1000d + UnitConstant.YUAN_MINUTE;
            newInfo = "语音质检费用设置：" + qcCostUnit  / 1000d + UnitConstant.YUAN_MINUTE;
            details = "语音质检费用设置：" + oldTenantPO.getQcCostUnit()  / 1000d + "→" + qcCostUnit  / 1000d + UnitConstant.YUAN_MINUTE;
        }else if (oldTenantPO.getQcCostUnit().equals(qcCostUnit) && !oldTenantPO.getQcTextCostUnit().equals(qcTextCostUnit)) {
            oldInfo = "文本质检费用设置：" + oldTenantPO.getQcTextCostUnit()  / 1000d + UnitConstant.YUAN_TIAO ;
            newInfo = "文本质检费用设置：" + qcTextCostUnit  / 1000d + UnitConstant.YUAN_TIAO ;
            details = "文本质检费用设置：" + oldTenantPO.getQcTextCostUnit()  / 1000d + "→" + qcTextCostUnit  / 1000d + UnitConstant.YUAN_TIAO ;
        }else {
            return;
        }
        insertOperationLogDetails(oldTenantPO.getTenantId(), oldTenantPO.getDistributorId(), userId, CostOperationLogTypeEnum.SET_QC, LocalDateTime.now(), details, oldInfo, newInfo);
    }

    @Override
    public void addOperationUpdateType(TenantPO oldTenantPO, TenantPO newTenantPO) {
        try {
            Long tenantId = oldTenantPO.getTenantId();
            Long distributorId = oldTenantPO.getDistributorId();
            LocalDateTime localDateTime = LocalDateTime.now();
            // 纷享销客 默认id为-1
            Long currentUserId = newTenantPO.getUpdateUserId() != null ? newTenantPO.getUpdateUserId() : -1L;

            List<OperationLogDetailsPO> operationDetailsList = new ArrayList<>();

            // 客户付费类型
            if (newTenantPO.getTenantPayType() != null && !oldTenantPO.getTenantPayType().equals(newTenantPO.getTenantPayType())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.TENANT_PAY_TYPE,
                        localDateTime, oldTenantPO.getTenantPayType().getDesc(), newTenantPO.getTenantPayType().getDesc(), "");
            }
            // 赠送话术套数
            if (newTenantPO.getFreeDialogFlowCount() != null && !oldTenantPO.getFreeDialogFlowCount().equals(newTenantPO.getFreeDialogFlowCount())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_DIALOGFLOW_COUNT,
                        localDateTime, String.valueOf(oldTenantPO.getFreeDialogFlowCount()), String.valueOf(newTenantPO.getFreeDialogFlowCount()), UnitConstant.TAO);
            }
            // 话术制作成本（不含录音）
            if (newTenantPO.getDialogFlowCostWithoutRecord() != null && !oldTenantPO.getDialogFlowCostWithoutRecord().equals(newTenantPO.getDialogFlowCostWithoutRecord())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITHOUT_RECORD,
                        localDateTime, String.valueOf(oldTenantPO.getDialogFlowCostWithoutRecord()), String.valueOf(newTenantPO.getDialogFlowCostWithoutRecord()), UnitConstant.YUAN);
            }
            // 话术制作成本（含录音）
            if (newTenantPO.getDialogFlowCostWithRecord() != null && !oldTenantPO.getDialogFlowCostWithRecord().equals(newTenantPO.getDialogFlowCostWithRecord())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_RECORD,
                        localDateTime, String.valueOf(oldTenantPO.getDialogFlowCostWithRecord()), String.valueOf(newTenantPO.getDialogFlowCostWithRecord()), UnitConstant.YUAN);
            }
            // 话术制作成本（10句以内）
            if (newTenantPO.getDialogFlowCostWithinTenSentence() != null && !oldTenantPO.getDialogFlowCostWithinTenSentence().equals(newTenantPO.getDialogFlowCostWithinTenSentence())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_IN_TEN_SENTENCE,
                        localDateTime, String.valueOf(oldTenantPO.getDialogFlowCostWithinTenSentence()), String.valueOf(newTenantPO.getDialogFlowCostWithinTenSentence()), UnitConstant.YUAN);
            }
            // 话术制作成本（50句以内）
            if (newTenantPO.getDialogFlowCostWithinFiftySentence() != null && !oldTenantPO.getDialogFlowCostWithinFiftySentence().equals(newTenantPO.getDialogFlowCostWithinFiftySentence())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_IN_FIFTY_SENTENCE,
                        localDateTime, String.valueOf(oldTenantPO.getDialogFlowCostWithinFiftySentence()), String.valueOf(newTenantPO.getDialogFlowCostWithinFiftySentence()), UnitConstant.YUAN);
            }
            // 赠送话术修改句
            if (newTenantPO.getFreeDialogFlowSentenceCount() != null && !oldTenantPO.getFreeDialogFlowSentenceCount().equals(newTenantPO.getFreeDialogFlowSentenceCount())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_DIALOGFLOW_SENTENCE_COUNT,
                        localDateTime, String.valueOf(oldTenantPO.getFreeDialogFlowSentenceCount()), String.valueOf(newTenantPO.getFreeDialogFlowSentenceCount()), UnitConstant.JU);
            }
            //  单句修改成本
            if (newTenantPO.getSentenceCost() != null && !oldTenantPO.getSentenceCost().equals(newTenantPO.getSentenceCost())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.SENTENCE_COST,
                        localDateTime, String.valueOf(oldTenantPO.getSentenceCost()), String.valueOf(newTenantPO.getSentenceCost()), UnitConstant.YUAN);
            }
            //  AI外呼音频保留时长
            if (newTenantPO.getCallOutAudioRetentionTime() != null && !Objects.equals(oldTenantPO.getCallOutAudioRetentionTime(), newTenantPO.getCallOutAudioRetentionTime())) {
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.CALL_OUT_AUDIO_RETENTION_TIME,
                        localDateTime, String.valueOf(oldTenantPO.getCallOutAudioRetentionTime() == null ? null : oldTenantPO.getCallOutAudioRetentionTime().getExpirationDays()),
		                String.valueOf(newTenantPO.getCallOutAudioRetentionTime().getExpirationDays()), UnitConstant.TIAN);
            }
            // 按分钟付费
            if (TenantPayTypeEnum.MINUTE.equals(newTenantPO.getTenantPayType()) || TenantPayTypeEnum.QIYU_MINUTE.equals(newTenantPO.getTenantPayType())) {
                // AI外呼报价
                if (newTenantPO.getAiFare() != null && !oldTenantPO.getAiFare().equals(newTenantPO.getAiFare())) {
                    createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.AI_FARE,
                            localDateTime, String.valueOf(oldTenantPO.getAiFare() / 10), String.valueOf(newTenantPO.getAiFare() / 10), UnitConstant.FEN_MINUTE);
                }
                // 通讯结算价
                if (newTenantPO.getComFare() != null && !oldTenantPO.getComFare().equals(newTenantPO.getComFare())) {
                    createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.COM_FARE,
                            localDateTime, String.valueOf(oldTenantPO.getComFare() / 10), String.valueOf(newTenantPO.getComFare() / 10), UnitConstant.FEN_MINUTE);
	        }
            }else if (TenantPayTypeEnum.PIECE.equals(newTenantPO.getTenantPayType())) {
            // 按接通付费
                // AI外呼报价
                if (newTenantPO.getAiFare() != null && !oldTenantPO.getAiFare().equals(newTenantPO.getAiFare())) {
                    createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.AI_FARE,
                            localDateTime, String.valueOf(oldTenantPO.getAiFare() / 10), String.valueOf(newTenantPO.getAiFare() / 10), UnitConstant.FEN_TONG);
                }
                // 通讯结算价
                if (newTenantPO.getComFare() != null && !oldTenantPO.getComFare().equals(newTenantPO.getComFare())) {
                    createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.COM_FARE,
                            localDateTime, String.valueOf(oldTenantPO.getComFare() / 10), String.valueOf(newTenantPO.getComFare() / 10), UnitConstant.FEN_TONG);
                }
	            // 是否赠送短信 前端没有传  无法修改
	            if (newTenantPO.getFreeSmsCount() != null && !oldTenantPO.getFreeSmsCount().equals(newTenantPO.getFreeSmsCount())) {
		            // 仅可通过纷享销客同步修改此项
		            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_SMS_COUNT,
				            localDateTime, oldTenantPO.getFreeSmsCount() == 0 ? "否" : "是", newTenantPO.getFreeSmsCount() == 0 ? "否" : "是", "");
	            }
            }

	        // 短信内部结算价
	        if (newTenantPO.getSmsComFare() != null && !Objects.equals(oldTenantPO.getSmsComFare(), newTenantPO.getSmsComFare())) {
		        createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, oldTenantPO.getCompanyName(), CostOperationLogTypeEnum.SMS_INTERNAL_SETTLEMENT_FARE,
				        localDateTime, String.valueOf(oldTenantPO.getSmsComFare() == null ? null : oldTenantPO.getSmsComFare() / 1000d),
				        String.valueOf(newTenantPO.getSmsComFare() / 1000d), UnitConstant.YUAN_TONG);
	        }

            if (!operationDetailsList.isEmpty()) {
                mongoTemplate.insert(operationDetailsList, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
            }
        }catch (Exception e) {
            logger.error("添加费用操作日志细节失败。",e);
        }
    }

	@Override
    public void addInsertTenantOperationLogDetails(TenantPO tenant) {
        try {
            Long tenantId = tenant.getTenantId();
            LocalDateTime localDateTime = LocalDateTime.now();
            // 纷享销客 默认id为-1
            Long currentUserId = tenant.getCreateUserId() != null ? tenant.getUpdateUserId() : -1L;

            TenantPO tenantPO = tenantService.selectByKey(tenantId);
            Long distributorId = tenantPO.getDistributorId();

            List<OperationLogDetailsPO> operationDetailsList = new ArrayList<>();

            // 客户付费类型
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.TENANT_PAY_TYPE,
                        localDateTime, "--", tenantPO.getTenantPayType().getDesc(), "");
            // 赠送话术套数
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_DIALOGFLOW_COUNT,
                    localDateTime, "--", String.valueOf(tenantPO.getFreeDialogFlowCount()), UnitConstant.TAO);
            // 话术制作成本（不含录音）
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITHOUT_RECORD,
                    localDateTime, "--", String.valueOf(tenantPO.getDialogFlowCostWithoutRecord()), UnitConstant.YUAN);
            // 话术制作成本（含录音）
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_RECORD,
                    localDateTime, "--", String.valueOf(tenantPO.getDialogFlowCostWithRecord()), UnitConstant.YUAN);
            // 话术制作成本（10句以内）
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_IN_TEN_SENTENCE,
                    localDateTime, "--", String.valueOf(tenantPO.getDialogFlowCostWithinTenSentence()), UnitConstant.YUAN);
            // 话术制作成本（50句以内）
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.DIALOGFLOW_COST_WITH_IN_FIFTY_SENTENCE,
                    localDateTime, "--", String.valueOf(tenantPO.getDialogFlowCostWithinFiftySentence()), UnitConstant.YUAN);
            // 赠送话术修改句
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_DIALOGFLOW_SENTENCE_COUNT,
                    localDateTime, "--", String.valueOf(tenantPO.getFreeDialogFlowSentenceCount()), UnitConstant.JU);
            //  单句修改成本
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.SENTENCE_COST,
                    localDateTime, "--", String.valueOf(tenantPO.getSentenceCost()), UnitConstant.YUAN);
            //  单句修改成本
	        CallOutAudioRetentionTimeEnum callOutAudioRetentionTime = tenantPO.getCallOutAudioRetentionTime() == null ? CallOutAudioRetentionTimeEnum.getDefault() : tenantPO.getCallOutAudioRetentionTime();
            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.CALL_OUT_AUDIO_RETENTION_TIME,
                    localDateTime, "--", String.valueOf(callOutAudioRetentionTime.getExpirationDays()), UnitConstant.TIAN);

            // 按分钟付费
            if (TenantPayTypeEnum.MINUTE.equals(tenantPO.getTenantPayType()) || TenantPayTypeEnum.QIYU_MINUTE.equals(tenantPO.getTenantPayType())) {
                // AI外呼报价 单位 厘
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.AI_FARE,
                        localDateTime, "--", String.valueOf(tenantPO.getAiFare() / 10), UnitConstant.FEN_MINUTE);
                // 通讯结算价 单位 厘
                createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.COM_FARE,
                        localDateTime, "--", String.valueOf(tenantPO.getComFare() / 10), UnitConstant.FEN_MINUTE);
            } else if (TenantPayTypeEnum.PIECE.equals(tenantPO.getTenantPayType())) {
	            // 按接通付费
	            // AI外呼报价 单位 厘
	            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.AI_FARE,
			            localDateTime, "--", String.valueOf(tenantPO.getAiFare() / 10), UnitConstant.FEN_TONG);
	            // 通讯结算价 单位 厘
	            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.COM_FARE,
			            localDateTime, "--", String.valueOf(tenantPO.getComFare() / 10), UnitConstant.FEN_TONG);

	            // 是否赠送短信 前端没有传  无法修改
	            if (TenantPayTypeEnum.PIECE.equals(tenantPO.getTenantPayType())) {
		            // 仅可通过纷享销客同步修改此项
		            createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.FREE_SMS_COUNT,
				            localDateTime, "--", tenantPO.getFreeSmsCount() == 0 ? "否" : "是", "");
	            }
            }

	        // 短信内部结算价
	        if (tenantPO.getSmsComFare() != null) {
		        createOperationLogDetails(operationDetailsList, tenantId, distributorId, currentUserId, tenantPO.getCompanyName(), CostOperationLogTypeEnum.SMS_INTERNAL_SETTLEMENT_FARE,
				        localDateTime, "--", String.valueOf(tenantPO.getSmsComFare() / 1000d), UnitConstant.YUAN_TONG);
	        }

            if (!operationDetailsList.isEmpty()) {
                mongoTemplate.insert(operationDetailsList, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
            }
        }catch (Exception e) {
            logger.error("【新增客户】添加费用操作日志细节失败。",e);
        }
    }

    @Override
    public void addUpdateOpeCustomerBindLineOperationLogDetails(UpdateBindOnePhoneNumberVO updateVO, TenantPhoneNumberPO savePO, TenantPhoneNumberPO tenantPhoneNumberPO, TenantPO tenantPO, PhoneNumberPO phoneNumberPO, Long userId) {

        try {
            OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
            operationLogDetailsPO.setTenantId(savePO.getTenantId());
            operationLogDetailsPO.setDistributorId(tenantPO.getDistributorId());
            operationLogDetailsPO.setCreateUserId(userId);
            operationLogDetailsPO.setOperationObject(tenantPO.getCompanyName());
            operationLogDetailsPO.setCreateTime(LocalDateTime.now());
            operationLogDetailsPO.setCostOperationLogType(CostOperationLogTypeEnum.CUSTOMER_CONFIG_LINE);
            String phoneNumber = phoneNumberPO.getPhoneNumber();

            Double localBillRate = updateVO.getLocalBillRate() * 10;
            Double otherBillRate = updateVO.getOtherBillRate() * 10;
            Double callInLocalBillRate = updateVO.getCallInLocalBillRate() * 10;
            Double callInOtherBillRate = updateVO.getCallInOtherBillRate() * 10;
            Double monthlyBillRate = updateVO.getMonthlyBillRate() * 1000;
            savePO.setLocalBillRate(localBillRate.longValue());
            savePO.setOtherBillRate(otherBillRate.longValue());
            savePO.setMonthlyBillRate(monthlyBillRate.longValue());
            savePO.setCallInLocalBillRate(callInLocalBillRate.longValue());
            savePO.setCallInOtherBillRate(callInOtherBillRate.longValue());
            savePO.setCallInBillMode(updateVO.getCallInBillMode());

            boolean callout = false;
            if (!savePO.getLocalBillRate().equals(tenantPhoneNumberPO.getLocalBillRate())) {
                callout = true;
            }
            if (!savePO.getOtherBillRate().equals(tenantPhoneNumberPO.getOtherBillRate())) {
                callout = true;
            }
            if (!savePO.getCallInBillMode().equals(tenantPhoneNumberPO.getCallInBillMode())) {
                callout = true;
            }
            if (!savePO.getMonthlyBillRate().equals(tenantPhoneNumberPO.getMonthlyBillRate())) {
                callout = true;
            }
            if (!savePO.getCallInLocalBillRate().equals(tenantPhoneNumberPO.getCallInLocalBillRate())) {
                callout = true;
            }
            if (!savePO.getCallInOtherBillRate().equals(tenantPhoneNumberPO.getCallInOtherBillRate())) {
                callout = true;
            }

            if (callout) {
                String oldCallInBillMode = tenantPhoneNumberPO.getCallInBillMode() != null ? tenantPhoneNumberPO.getCallInBillMode().getDesc() : "";
                String newCallInBillMode = savePO.getCallInBillMode() != null ? savePO.getCallInBillMode().getDesc() : "";
                String oldInfo = "外呼话费："
                        + "本地话费：" + tenantPhoneNumberPO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "外地话费：" + tenantPhoneNumberPO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "呼入话费：\n"
                        + oldCallInBillMode + "：\n"
                        + "月租：" + tenantPhoneNumberPO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                        + "本地话费：" + tenantPhoneNumberPO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                        + "外地话费：" + tenantPhoneNumberPO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n";

                String newInfo = "外呼话费："
                        + "本地话费：" + savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "外地话费：" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "呼入话费：\n"
                        + newCallInBillMode + "：\n"
                        + "月租：" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                        + "本地话费：" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                        + "外地话费：" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n";

                String details = "【" + phoneNumber + "】" + "外呼话费：\n"
                        + "本地话费：" + tenantPhoneNumberPO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "→" + savePO.getLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "外地话费：" + tenantPhoneNumberPO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "→" + savePO.getOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + " \n"
                        + "呼入话费：\n"
                        + oldCallInBillMode + "→" + newCallInBillMode + "：\n"
                        + "月租：" + tenantPhoneNumberPO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + "→" + savePO.getMonthlyBillRate() / 1000d + UnitConstant.YUAN + " \n"
                        + "本地话费：" + tenantPhoneNumberPO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "→" + savePO.getCallInLocalBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n"
                        + "外地话费：" + tenantPhoneNumberPO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "→" + savePO.getCallInOtherBillRate() / 10d + UnitConstant.FEN_MINUTE + "\n";
                operationLogDetailsPO.setOldInformation(oldInfo);
                operationLogDetailsPO.setNewInformation(newInfo);
                operationLogDetailsPO.setLogDetail(details);
            }else {
                return;
            }
            mongoTemplate.insert(operationLogDetailsPO, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
        }catch (Exception e) {
            logger.error("【客户编辑线路】添加费用操作日志细节失败。",e);
        }

    }


    @Override
    public void insertInitializationTenant() {
        List<TenantPO> tenantPOList = tenantService.selectDirectTenant();
        for (TenantPO tenant : tenantPOList) {
            addInsertTenantOperationLogDetails(tenant);
        }
    }

    @Override
    public void addPrivacyNumberLog(TenantPO tenantPO, PrivacyNumberConfigVO config, Map<Long, Long> productIdUnitPriceMap) {
        try {
            LocalDateTime now = LocalDateTime.now();
            String companyName = tenantPO.getCompanyName();

            logger.info("【隐私号】添加隐私号操作日志细节。privacyNumberPhoneCostId = {}, privacyNumberCallCostId = {}, privacyNumberPhoneInternalCostId = {}, privacyNumberCallInternalCostId = {}", tenantPO.getPrivacyNumberPhoneCostId(), tenantPO.getPrivacyNumberCallCostId(), tenantPO.getPrivacyNumberPhoneInternalCostId(), tenantPO.getPrivacyNumberCallInternalCostId());
            List<OperationLogDetailsPO> operationLogDetails = new ArrayList<>();
            if (tenantPO.getPrivacyNumberPhoneCostId() != null && tenantPO.getPrivacyNumberCallCostId() != null) {
                if (config.getPhoneCost() != null && !config.getPhoneCost().equals(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberPhoneCostId()))) {
                    operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_PHONE_OUT_COST, now,
                            "[" + companyName + "]" +"隐私号对外报价-隐私号号码月租" + config.getPhoneCost() / 1000d + UnitConstant.YUAN_GE,
                            String.valueOf(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberPhoneCostId()) / 1000d),
                            String.valueOf(config.getPhoneCost() / 1000d), UnitConstant.YUAN_GE)
                    );
                }
                if (config.getCallCost() != null && !config.getCallCost().equals(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberCallCostId()))) {
                    operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_CALL_OUT_COST, now,
                            "[" + companyName + "]" +"隐私号对外报价-隐私号号码通话计费" + config.getCallCost() / 1000d + UnitConstant.YUAN_MINUTE_NO,
                            String.valueOf(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberCallCostId()) / 1000d),
                            String.valueOf(config.getCallCost() / 1000d), UnitConstant.YUAN_MINUTE_NO)
                    );
                }
            }else {
                operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_PHONE_OUT_COST, now,
                        "[" + companyName + "]" +"隐私号对外报价-隐私号号码月租" + config.getPhoneCost() / 1000d + UnitConstant.YUAN_GE,
                        "--",
                        String.valueOf(config.getPhoneCost() / 1000d), UnitConstant.YUAN_GE)
                );
                operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_CALL_OUT_COST, now,
                        "[" + companyName + "]" +"隐私号对外报价-隐私号通话计费" + config.getCallCost() / 1000d + UnitConstant.YUAN_MINUTE_NO,
                        "--",
                        String.valueOf(config.getCallCost() / 1000d), UnitConstant.YUAN_MINUTE_NO)
                );
            }

            if (tenantPO.getPrivacyNumberPhoneInternalCostId() != null && tenantPO.getPrivacyNumberCallInternalCostId() != null) {
                if (config.getPhoneInternalCost() != null && !config.getPhoneInternalCost().equals(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberPhoneInternalCostId()))) {
                    operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_PHONE_INTERNAL_COST, now,
                            "[" + companyName + "]" +"隐私号内部结算价-隐私号号码月租" + config.getPhoneInternalCost() / 1000d + UnitConstant.YUAN_GE,
                            String.valueOf(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberPhoneInternalCostId()) / 1000d),
                            String.valueOf(config.getPhoneInternalCost() / 1000d), UnitConstant.YUAN_GE)
                    );
                }
                if (config.getCallInternalCost() != null && !config.getCallInternalCost().equals(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberCallInternalCostId()))) {
                    operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_CALL_INTERNAL_COST, now,
                            "[" + companyName + "]" +"隐私号内部结算价-隐私号通话计费" + config.getCallInternalCost() / 1000d + UnitConstant.YUAN_MINUTE_NO,
                            String.valueOf(productIdUnitPriceMap.get(tenantPO.getPrivacyNumberCallInternalCostId()) / 1000d),
                            String.valueOf(config.getCallInternalCost() / 1000d), UnitConstant.YUAN_MINUTE_NO)
                    );
                }
            }else {
                operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_PHONE_INTERNAL_COST, now,
                        "[" + companyName + "]" +"隐私号内部结算价-隐私号号码月租" + config.getPhoneInternalCost() / 1000d + UnitConstant.YUAN_GE,
                        "--",
                        String.valueOf(config.getPhoneInternalCost() / 1000d), UnitConstant.YUAN_GE)
                );
                operationLogDetails.add(createLogDetail(tenantPO, config.getUpdateUserId(), CostOperationLogTypeEnum.PRIVACY_NUMBER_CALL_INTERNAL_COST, now,
                        "[" + companyName + "]" +"隐私号内部结算价-隐私号通话计费" + config.getCallInternalCost() / 1000d + UnitConstant.YUAN_MINUTE_NO,
                        "--",
                        String.valueOf(config.getCallInternalCost() / 1000d), UnitConstant.YUAN_MINUTE_NO)
                );
            }
            if (!operationLogDetails.isEmpty()) {
                mongoTemplate.insert(operationLogDetails, MongoCollectionNameCenter.OPERATION_LOG_DETAILS);
            }
        }catch (Exception e) {
            logger.error("【修改客户隐私号报价】添加费用操作日志细节失败。", e);
        }
    }


    private OperationLogDetailsPO createLogDetail(TenantPO tenantPO, Long createUserId, CostOperationLogTypeEnum costOperationLogType,
                                                  LocalDateTime createTime, String details, String oldInformation, String newInformation, String unit) {
        OperationLogDetailsPO operationLogDetailsPO = new OperationLogDetailsPO();
        operationLogDetailsPO.setTenantId(tenantPO.getTenantId());
        operationLogDetailsPO.setDistributorId(tenantPO.getDistributorId());
        operationLogDetailsPO.setCreateUserId(createUserId);
        operationLogDetailsPO.setOperationObject(tenantPO.getCompanyName());
        operationLogDetailsPO.setCreateTime(createTime);
        operationLogDetailsPO.setCostOperationLogType(costOperationLogType);
        operationLogDetailsPO.setOldInformation(oldInformation);
        operationLogDetailsPO.setNewInformation(newInformation);
        operationLogDetailsPO.setLogDetail(details);
        operationLogDetailsPO.setUnit(unit);

        return operationLogDetailsPO;
    }

}
