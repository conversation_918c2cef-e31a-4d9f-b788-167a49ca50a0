package com.yiwise.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * @Author: zqy
 * @Date: 2020/8/13 14:15
 */
@Slf4j
public class Base64ToImgUtil {
    /**
     * base64编码字符串转换为图片
     * @param sourcePath base64编码字符串
     * @param path 图片路径
     * @return
     */
    public static boolean base64StrToImage(String sourcePath, String path) {
        if (sourcePath == null)
            return false;
        try {
            File file = new File(sourcePath);
            FileInputStream inputStream = new FileInputStream(file);
            int length = inputStream.available();
            byte bytes[] = new byte[length];
            inputStream.read(bytes);
            inputStream.close();
            String base64Str = new String(bytes, StandardCharsets.UTF_8);
            // 解密
            byte[] b = Base64.decodeBase64(base64Str);
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            //文件夹不存在则自动创建
            File tempFile = new File(path);
            if (!tempFile.getParentFile().exists()) {
                tempFile.getParentFile().mkdirs();
            }
            OutputStream out = new FileOutputStream(tempFile);
            out.write(b);
            out.flush();
            out.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    /**
     * 图片转base64字符串
     * @param imgFile 图片路径
     * @return
     */
    public static String imageToBase64Str(String imgFile) {
        InputStream inputStream = null;
        byte[] data = null;
        try {
            inputStream = new FileInputStream(imgFile);
            data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
        } catch (IOException e) {
	        log.error(e.getMessage(), e);
        }
        // 加密
        return Base64.encodeBase64String(data);
    }

    public static void main(String[] args) {
//        String base64Str = imageToBase64Str("D:/pic/001.jpg");
//        System.out.println(base64Str);
        try {
            File file = new File("/Users/<USER>/Downloads/656a7d3088744dbd9dd6301b651ce1bf");
            FileInputStream inputStream = new FileInputStream(file);
            int length = inputStream.available();
            byte bytes[] = new byte[length];
            inputStream.read(bytes);
            inputStream.close();
            String base64Str = new String(bytes, StandardCharsets.UTF_8);

            boolean b = base64StrToImage(base64Str, "/Users/<USER>/Downloads/test.jpg");
            System.out.println(b);
        } catch (Exception e) {

        }
    }
}
