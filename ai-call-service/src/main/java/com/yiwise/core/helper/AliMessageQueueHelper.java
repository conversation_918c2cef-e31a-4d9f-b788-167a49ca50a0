package com.yiwise.core.helper;

import com.aliyun.openservices.ons.api.*;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.ons.model.v20190214.*;
import com.aliyuncs.profile.DefaultProfile;
import com.google.gson.Gson;
import com.yiwise.base.common.context.AppContextUtils;
import com.yiwise.base.common.utils.PropertyLoaderUtils;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.core.batch.entity.dto.RobotCallJobAccountAnalysisDTO;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.MQConst;
import com.yiwise.core.dal.entity.financial.CallFinancialPO;
import com.yiwise.core.dal.entity.financial.SmsFinancialPO;
import com.yiwise.core.model.bo.dialogflownodestats.DialogStatsMsgBO;
import com.yiwise.core.model.bo.mq.*;
import com.yiwise.core.model.bo.websocket.BasicMsg;
import com.yiwise.core.model.dialogflow.dto.PredictLogBatchDTO;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.po.CallEventLogBatchPO;
import com.yiwise.core.model.vo.callrecord.ReturnVisitMsgBO;
import com.yiwise.customer.data.platform.rpc.api.service.request.crowd.CrowdPushCallBackDTO;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.*;
import org.springframework.stereotype.Component;
import org.springframework.util.*;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-11-05
 */
@Component
public class AliMessageQueueHelper {

    private static Logger logger = LoggerFactory.getLogger(AliMessageQueueHelper.class);

    private static String topic;
    private static String callBackTopic;
    private static String callRecordTopic;
    private static String asyncJobTopic;
    private static String filteredTaskTopic;
    private static String filteredTaskComputeTopic;
    private static String locationAnalysisTopic;
    public static String financialTopic;
    private static String callOutOtherTopic;
    private static String smsCallBackTopic;
    private static String qcPlanTopic;
    public static String peersTopic;
    public static String wechatTopic;
    private static String callEventLogTopic;
    private static String dialogStatsTopic;
    private static String predictLogTopic;
    private static final String returnVisitTopic;
    private static final String crowdPushCallBackTopic;
    private static Producer commonProducer;
    private static Producer socketIOProducer;
    private static String robotAccountAnalysisTopic;

    private static String accessKey;
    private static String secretKey;
    private static String instanceId;
    private static String regionId;
    public static String namesrvAddr;

    private static RocketMQTemplate rocketMQTemplate = null;

    static {
        topic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.topic");
        callBackTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callback.topic");
        callRecordTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.call.record.topic");
        asyncJobTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.asyncjob.topic");
        filteredTaskTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.filteredTask.topic");
        filteredTaskComputeTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.filteredTaskCompute.topic");
        locationAnalysisTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.locationAnalysis.topic");
        financialTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.financial.topic");
        callOutOtherTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callOutOther.topic");
        smsCallBackTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.smsCallBack.topic");
        qcPlanTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.qc.plan.topic");
        peersTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.peers.topic");
        wechatTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.wechatJob.topic");
        callEventLogTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callEventLog.topic");
        dialogStatsTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.dialogStats.topic");
        predictLogTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.predictLog.topic");
        returnVisitTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.returnVisit.topic");
        robotAccountAnalysisTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.robot.account.analysis.topic");
        if(PropertyLoaderUtils.containsProperty("aliyun.ons.rocketmq.crowd.push.callback.topic")){
            crowdPushCallBackTopic = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.crowd.push.callback.topic");
        }else {
            crowdPushCallBackTopic = "";
        }

        if(!CommonApplicationConstant.MQ_TYPE.isLocal()){
            accessKey = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.accessKey");
            secretKey = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.secretKey");
            instanceId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.instanceId");
            regionId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.regionId");
            namesrvAddr = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.namesrvAddr");
        }else {
            namesrvAddr = PropertyLoaderUtils.getProperty("rocketmq.name-server");
        }
    }

    @PostConstruct
    private void postConstruct() {
        if(!CommonApplicationConstant.MQ_TYPE.isLocal()){
            Properties producerProperties = getProperties();

            String producerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.producerId");
            producerProperties.put(PropertyKeyConst.GROUP_ID, producerId);
            commonProducer = ONSFactory.createProducer(producerProperties);
            commonProducer.start();

            Properties properties = getProperties();
            properties.put(PropertyKeyConst.GROUP_ID, MQConst.AICC_SOCKETIO_MESSAGE_PRODUCER_GROUP);
            logger.info("创建生产者的配置：{}", properties);
            socketIOProducer = ONSFactory.createProducer(properties);
            socketIOProducer.start();

        }else {
            rocketMQTemplate = AppContextUtils.getBean(RocketMQTemplate.class);
        }

        logger.info("==== aliMessageQueueHelper execute ====");
    }

    /**
     * 发送普通消息队列消息
     */
    public void sendBasicMsg(String userName, String targetUrl, BasicMsg msg, SystemEnum clientType) {
        String moduleTag = clientType.getDesc();
        Message message = new Message(topic, clientType.getDesc(), JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();

        Assert.notNull(userName, "userName不能为空");
        Assert.notNull(targetUrl, "targetUrl不能为空");

        properties.put("userName", userName);
        properties.put("targetUrl", targetUrl);
        message.setReconsumeTimes(3);
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, topic);
    }

    /**
     * 发送回调消息提醒，给回调模块
     */
    public void sendCallBackMessage(Object msg, String moduleTag) {
        Message message = new Message(callBackTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, callBackTopic);
    }


    /**
     * 发送全量的通话记录到MQ
     */
    public void sendCallRecordMessage(Object msg, String moduleTag) {
        Message message = new Message(callRecordTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, callRecordTopic);
    }

    /**
     * 通话统计MQ消息
     */
    public void sendAsyncJobMessage(Object msg, String moduleTag) {
        Message message = new Message(asyncJobTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, asyncJobTopic);
    }

    /**
     * 外呼过滤MQ消息
     */
    public void sendFilteredTaskMessage(FilteredTaskMessageBO msg, String moduleTag) {
        Message message = new Message(filteredTaskTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, filteredTaskTopic);
    }

    /***
     * 功能描述： 外呼过滤消息
     * @param
     * @return void
     * <AUTHOR>
     * @date 4/24/24-1:47 PM
     * @version
     * @since
     */
    public void sendFilteredTaskComputeMessage(FilteredTaskComputeMessageBO msg, String moduleTag) {
        Message message = new Message(filteredTaskComputeTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, filteredTaskComputeTopic);
    }

    /**
     * 归属地分析MQ消息
     */
    public void sendLocationAnalysisMessage(CallRecordMqMessageBO msg, String moduleTag) {
        Message message = new Message(locationAnalysisTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, locationAnalysisTopic);
    }

    /**
     * 外呼计费元数据
     */
    public void sendCallFinancialMessage(CallFinancialPO msg, String moduleTag) {
        Message message = new Message(financialTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, financialTopic);
    }

    /**
     * 短信计费元数据
     */
    public void sendSMsFinancialMessage(SmsFinancialPO msg, String moduleTag) {
        Message message = new Message(financialTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, financialTopic);
    }

    /**
     * 外呼后的其他处理MQ消息
     */
    public void sendCallOutOtherMessage(CallOutOtherMessageBO msg, String moduleTag) {
        Message message = new Message(callOutOtherTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, callOutOtherTopic);
    }

    /**
     * 短信回调
     */
    public void sendSmsCallBackMessage(SmsCallBackMessageBO msg, String moduleTag) {
        Message message = new Message(smsCallBackTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, smsCallBackTopic);
    }

    public void sendWechatMessage(Object msg, String moduleTag) {
        Message message = new Message(wechatTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, wechatTopic);
    }

    public void sendQcPlanMessage(Object msg, String moduleTag, Long delayTime) {
        Message message = new Message(qcPlanTopic, moduleTag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        if(delayTime != null){
            message.setStartDeliverTime(delayTime);
        }
        sendMessage(message, moduleTag, msg, qcPlanTopic);
    }

    /**
     * 发送对话事件日志
     *
     * @param batchLog  批量日志
     * @param moduleTag 消息标签
     */
    public void sendCallEventLogMessage(CallEventLogBatchPO batchLog, String moduleTag) {
        Message message = new Message(callEventLogTopic, moduleTag, JsonUtils.object2StringNotNull(batchLog).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, batchLog, callEventLogTopic);
    }

    /**
     * 发送话术统计相关消息
     *
     * @param dialogStatsMsg 话术统计数据
     * @param moduleTag      tag
     */
    public void sendDialogStatsMessage(DialogStatsMsgBO dialogStatsMsg, String moduleTag) {
        Message message = new Message(dialogStatsTopic, moduleTag, JsonUtils.object2StringNotNull(dialogStatsMsg).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, dialogStatsMsg, dialogStatsTopic);
    }

    /**
     * 发送对话预测日志
     *
     * @param predictLogBatch 预测日志批量包
     * @param moduleTag       tag
     */
    public void sendPredictLogMessage(PredictLogBatchDTO predictLogBatch, String moduleTag) {
        Message message = new Message(predictLogTopic, moduleTag, JsonUtils.object2StringNotNull(predictLogBatch).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, predictLogBatch, predictLogTopic);
    }

    /**
     * 发送回访信息
     */
    public void sendReturnVisitMessage(ReturnVisitMsgBO msg, String moduleTag) {
        Message message = new Message(returnVisitTopic, moduleTag, JsonUtils.object2StringNotNull(msg).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, returnVisitTopic);
    }

    /**
     * 发送人群包推送回调
     */
    public void sendCrowdPushCallBackMessage(CrowdPushCallBackDTO msg, String moduleTag) {
        Message message = new Message(crowdPushCallBackTopic, moduleTag, JsonUtils.object2StringNotNull(msg).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, msg, crowdPushCallBackTopic);
    }

    /**
     * 发送socket.io信息
     */
    public void sendSocketIOMessage(Message message) {
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        logger.info("发送者的配置：{}", socketIOProducer.toString());
        socketIOProducer.sendAsync(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                logger.info("SocketIO消息发送MQ成功:{}", sendResult);
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                logger.info("SocketIO消息发送MQ失败:[{}],[{}]", onExceptionContext.getException().toString(), onExceptionContext.getMessageId());
            }
        });
    }

    /**
     * 发送外呼任务人群分析数据到MQ
     *
     * @param dto      人群分析数据
     * @param moduleTag tag
     * <AUTHOR>
     * date: 2025/1/15 10:04
     */
    public void sendRobotAccountAnalysisMessage(RobotCallJobAccountAnalysisDTO dto, String moduleTag) {
        Message message = new Message(robotAccountAnalysisTopic, moduleTag, JsonUtils.object2StringNotNull(dto).getBytes(StandardCharsets.UTF_8));
        Properties properties = new Properties();
        properties.put("MDC_LOG_ID", MDC.get("MDC_LOG_ID"));
        message.setUserProperties(properties);
        sendMessage(message, moduleTag, dto,robotAccountAnalysisTopic );

    }

    /**
     * 发送MQ
     */
    private void sendMessage(Message message, String moduleTag, Object msg, String topic) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            rocketMQTemplate.asyncSend(topic + ":" + moduleTag, message, new org.apache.rocketmq.client.producer.SendCallback() {
                @Override
                public void onSuccess(org.apache.rocketmq.client.producer.SendResult sendResult) {
                    logger.debug("[AliMessageQueue]阿里云发送消息队列成功，发送消息为=[{}], Id={}, tag={}", msg.toString(), sendResult.getMsgId(), moduleTag);
                }

                @Override
                public void onException(Throwable throwable) {
                    logger.error("[LogHub_Warn] [AliMessageQueue] 阿里云发送消息队列失败，请检查，消息内容=[{}], tag={}, topic={}, errormsg={}", msg.toString(), moduleTag, topic, throwable.getMessage(), throwable.getCause());
                }
            });
        }else {
            commonProducer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    logger.debug("[AliMessageQueue]阿里云发送回调消息成功，发送消息为=[{}], Id={}, tag={}", msg.toString(), sendResult.getMessageId(), moduleTag);
                }

                @Override
                public void onException(OnExceptionContext onExceptionContext) {
                    logger.error("[LogHub_Warn] [AliMessageQueue] 阿里云发送回调消息成失败，请检查，消息内容=[{}], messageId={}, tag={}, topic={}, errormsg={}", msg.toString(), onExceptionContext.getMessageId(), moduleTag, onExceptionContext.getTopic(), onExceptionContext.getException().getMessage(), onExceptionContext.getException().getCause());
                }
            });
        }
    }

    /**
     * 这个方法是用来配置config的，是用于建立阿里云rocketMq的监听端，tag是监听的tag，MessageListener要为bean
     */
    public static ConsumerBean getMessageQueueConsumerBean(String topic, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String groupId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.groupId");

        return getMessageQueueConsumerBean(groupId, topic, "*", messageListener, PropertyValueConst.CLUSTERING);
    }

    /**
     * 这个方法是用来配置config的，是用于建立阿里云rocketMq的监听端，tag是监听的tag，MessageListener要为bean, 获取回调的监听
     */
    public static ConsumerBean getCallBackConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callback.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置500个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 500);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }


    /**
     * ai外呼统计后处理消费者
     */
    public static ConsumerBean getAsyncJobConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.asyncjob.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置500个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 500);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    /**
     * 过滤统计
     */
    public static ConsumerBean getFilteredTaskConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.filteredTask.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置200个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 200);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }


    /**
     * 过滤统计
     */
    public static ConsumerBean getFilteredComputeConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }

        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.filteredTaskCompute.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();

        // 设置200个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 30);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    /**
     * 启动消费者组
     *
     * @param topic
     * @param tag
     * @param groupId
     * @param messageListener
     * @return
     */
    public static ConsumerBean getConsumeBeanByGroupId(String topic, String tag, String groupId, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }

        ConsumerBean consumerBean = getMessageQueueConsumerBean(groupId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        properties.put(PropertyKeyConst.ConsumeThreadNums, 32);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    /**
     * 归属地统计
     * @param topic
     * @param tag
     * @param messageListener
     * @return
     */
    public static ConsumerBean getLocationAnalysisConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.locationAnalysis.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置200个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 200);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    /**
     * 外呼后其他处理
     */
    public static ConsumerBean getCallOutOtherConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callOutOther.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置500个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 200);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getSmsCallBackConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.smsCallBack.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置500个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 100);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getQcPlanConsumeBean(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.qc.plan.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置150个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 150);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getDialogStatsConsumeBean(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.dialogStats.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置150个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 150);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getPredictLogConsumeBean(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.predictLog.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置150个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 150);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getCallEventLogInterruptAnalysisConsumeBean(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.callEventLog.interruptAnalysis.groupId");

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, PropertyValueConst.CLUSTERING);

        Properties properties = consumerBean.getProperties();
        // 设置300个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 300);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getReturnVisitConsumeBean(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.returnVisit.groupId");
        String messageModel = PropertyValueConst.CLUSTERING;

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, messageModel);

        Properties properties = consumerBean.getProperties();
        // 设置50个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 50);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public static ConsumerBean getInterceptResultGroup(String topic, String tag, MessageListener messageListener) {
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.transfer.async.callback.groupId");
        String messageModel = PropertyValueConst.CLUSTERING;

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, messageModel);

        Properties properties = consumerBean.getProperties();
        // 设置50个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 50);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 10);

        consumerBean.start();
        return consumerBean;
    }

    public Producer getCommonProducer() {
        return commonProducer;
    }

    /**
     * 这里是用于装配bean的，有两种bean要装
     *
     * @param consumerId      消费者ID
     * @param topic           topic
     * @param tag             tag
     * @param messageListener messageListener处理监听到的消息
     * @param messageModel    发送方式群发或者集群
     */
    private static ConsumerBean getMessageQueueConsumerBean(String consumerId, String topic, String tag,
                                                            MessageListener messageListener, String messageModel) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        ConsumerBean consumerBean = new ConsumerBean();

        Properties properties = getProperties();
        properties.put(PropertyKeyConst.GROUP_ID, consumerId);
        properties.put(PropertyKeyConst.MessageModel, messageModel);
        return getConsumerBean(topic, tag, messageListener, consumerBean, properties);
    }

    private static ConsumerBean getConsumerBean(String topic, String tag, MessageListener messageListener, ConsumerBean consumerBean, Properties properties) {
        consumerBean.setProperties(properties);

        Map<Subscription,MessageListener> subscriptionTable = new HashMap<>();

        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        subscription.setExpression(tag);

        subscriptionTable.put(subscription, messageListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        return consumerBean;
    }

    private static Properties getProperties() {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.AccessKey, accessKey);
        properties.put(PropertyKeyConst.SecretKey, secretKey);
        properties.put(PropertyKeyConst.NAMESRV_ADDR, namesrvAddr);
        properties.put(PropertyKeyConst.ConsumeTimeout, "3000");
        return properties;
    }

    public void sendPeersMsg(Object msg, String tag, String JobKey) {
        Message message = new Message(peersTopic, tag, JsonUtils.object2Bytes(msg));
        Properties properties = new Properties();
        properties.put("JobKey", JobKey);

        message.setReconsumeTimes(3);
        message.setUserProperties(properties);
        sendMessage(message, tag, msg, peersTopic);
    }

    public static ConsumerBean getPeersConsumerBean(String groupId, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        ConsumerBean consumerBean = new ConsumerBean();

        Properties properties = getProperties();
        properties.put(PropertyKeyConst.GROUP_ID, groupId);
        // 广播消费模式
        properties.put(PropertyKeyConst.MessageModel, PropertyValueConst.BROADCASTING);
        // 设置3个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 3);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 1);
        return getConsumerBean(peersTopic, tag, messageListener, consumerBean, properties);
    }

    public static ConsumerBean getAddWechatFriendConsumeBean(String topic, String tag, MessageListener messageListener) {
        if(CommonApplicationConstant.MQ_TYPE.isLocal()){
            throw new RuntimeException("本地化不可使用此方法");
        }
        String consumerId = PropertyLoaderUtils.getProperty("aliyun.ons.rocketmq.wechatJob.groupId");
        String messageModel = PropertyValueConst.CLUSTERING;

        ConsumerBean consumerBean = getMessageQueueConsumerBean(consumerId, topic, tag, messageListener, messageModel);

        Properties properties = consumerBean.getProperties();
        // 设置30个消费线程
        properties.put(PropertyKeyConst.ConsumeThreadNums, 30);
        properties.put(PropertyKeyConst.ConsumeMessageBatchMaxSize, 1);

        consumerBean.start();
        return consumerBean;
    }


    public static void createGroup(String groupId) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKey, secretKey);
        IAcsClient client = new DefaultAcsClient(profile);

        OnsGroupCreateRequest request = new OnsGroupCreateRequest();
        request.setRegionId(regionId);
        request.setInstanceId(instanceId);
        request.setGroupId(groupId);
        request.setRemark("弹性坐席");
        try{
            OnsGroupCreateResponse response = client.getAcsResponse(request);
            logger.info("创建groupId={} response={}", groupId, new Gson().toJson(response));
        }catch (ServerException e){
            logger.error("创建topic错误", e);
        }catch (ClientException e){
            logger.error("ErrCode:" + e.getErrCode(), e);
            logger.error("ErrMsg:" + e.getErrMsg());
            logger.error("RequestId:" + e.getRequestId());
        }
    }


    public static boolean findGroup(String groupId) {
        // 本地化不需要创建 regionId为空认为手动创建
        if(CommonApplicationConstant.MQ_TYPE.isLocal() || StringUtils.isEmpty(regionId)){
            return true;
        }
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKey, secretKey);
        IAcsClient client = new DefaultAcsClient(profile);

        OnsGroupListRequest request = new OnsGroupListRequest();
        request.setRegionId(regionId);
        request.setInstanceId(instanceId);
        request.setGroupId(groupId);

        try{
            OnsGroupListResponse response = client.getAcsResponse(request);
            List<OnsGroupListResponse.SubscribeInfoDo> data = response.getData();
            logger.info("查询groupId={} response={}", groupId, new Gson().toJson(response));
            return !CollectionUtils.isEmpty(data);
        }catch (ServerException e){
            logger.error("创建topic错误", e);
        }catch (ClientException e){
            logger.error("ErrCode:" + e.getErrCode(), e);
            logger.error("ErrMsg:" + e.getErrMsg());
            logger.error("RequestId:" + e.getRequestId());
        }
        return false;
    }
}
