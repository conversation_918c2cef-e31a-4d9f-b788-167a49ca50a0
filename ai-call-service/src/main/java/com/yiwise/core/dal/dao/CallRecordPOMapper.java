package com.yiwise.core.dal.dao;


import com.yiwise.core.batch.excelimport.entity.SmsJobMessageImportVO;
import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.entity.CallRecordPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.model.bo.callcost.CallCostDetailBO;
import com.yiwise.core.model.dto.CallRecordExportDTO;
import com.yiwise.core.model.dto.ReAddRecordDTO;
import com.yiwise.core.model.dto.callcost.CallSmsCostDetailExportDTO;
import com.yiwise.core.model.dto.mq.CustomerPersonCalledImportDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.enums.callin.CallTypeEnum;
import com.yiwise.core.model.enums.ope.CallRecordTrackStatusEnum;
import com.yiwise.core.model.po.CallTaskCountPO;
import com.yiwise.core.model.vo.callcost.CallCostDetailExportRequestVO;
import com.yiwise.core.model.vo.callcost.CallCostQueryVO;
import com.yiwise.core.model.vo.callrecord.*;
import com.yiwise.core.model.vo.ope.CallRecordVO;
import com.yiwise.core.model.vo.ope.TrainRecordVO;
import com.yiwise.core.model.vo.openapi.ant.GetJobDetailAntRequestVO;
import com.yiwise.core.model.vo.robotcalltask.CallRecordLimitQueryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Primary;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * modified by Haokun Wang on 12/9/2018
 * @date 20/07/2018
 */
@Primary
@TargetDataSource(value = DataSourceEnum.POLARDB)
public interface CallRecordPOMapper extends Mapper<CallRecordPO> {

    /**
     * 根据客户ID获取所有的通话记录
     */
    List<CallRecordPO> selectByCustomerPersonIdAndTenantId(@Param("customerPersonId") Long customerPersonId, @Param("tenantId") Long tenantId);

    void updateCallRecordIntentLevel(@Param("tenantId") Long tenantId, @Param("callRecordId") Long callRecordId, @Param("intentLevel") Integer intentLevel);

    void updateCallRecordIntentLevelWithoutTenantId(@Param("callRecordId") Long callRecordId, @Param("intentLevel") Integer intentLevel);

    /**
     * 通话记录列表
     *
     * @param request 导出request
     * @return 需要导出的通话记录
     */
    List<CallRecordExportDTO> selectCallRecordsForExportByIdsOrConditions(@Param("request") CallRecordQueryVO request);


    /**
     * 通话记录列表
     *
     * @param request 导出request
     * @param start 记录起始位置
     * @param end 记录结束位置
     * @return 需要导出的通话记录
     */
    List<CallRecordExportDTO> selectCallRecordsForExportByIdsOrConditionsWithStartAndEnd(@Param("request") CallRecordQueryVO request,
                                                                          @Param("start")Integer start,
                                                                          @Param("end")Integer end);

    /**
     * 导出子账号话单详情
     */
    List<CallSmsCostDetailExportDTO> selectCallRecordsForExportBySubAccount(@Param("tenantId") Long tenantId,
                                                                            @Param("userId") Long userId,
                                                                            @Param("phoneNumberId") Long phoneNumberId,
                                                                            @Param("local") CallTypeEnum local,
                                                                            @Param("startDate") LocalDate startDate,
                                                                            @Param("endDate") LocalDate endDate);

    /**
     * 来自任务的通话记录列表 含可访问的用户id列表
     *
     * @param request    导出request
     * @param userIdList 创建用户id列表
     * @return 需要导出的通话记录
     */
    List<CallRecordExportDTO> selectCallRecordsFromJob(@Param("request") CallRecordQueryVO request,
                                                       @Param("userIdList") List<Long> userIdList);
    List<CallRecordExportDTO> selectCallRecordsFromJobList(@Param("request") CallRecordQueryVO request,
                                                       @Param("userIdList") List<Long> userIdList,
                                                           @Param("start") Integer start, @Param("end") Integer end);
    Long selectCallRecordsFromJobCount(@Param("request") CallRecordQueryVO request,
                                                       @Param("userIdList") List<Long> userIdList);

    Set<Long> selectRobotCallJobIdsFromJob(@Param("request") CallRecordQueryVO request);

    /**
     * 来自快速拨打的通话记录列表 含可访问的用户id列表
     *
     * @param request    导出request
     * @param userIdList 用户id列表
     * @return 需要导出的通话记录
     */
    List<CallRecordExportDTO> selectCallRecordsFromDirectCall(@Param("request") CallRecordQueryVO request,
                                                              @Param("userIdList") @Nullable List<Long> userIdList);

    /**
     * 查询话术体验拨打历史
     */
    List<CallRecordExportDTO> selectCallRecordsFromTryDialogFlowCall(@Param("request") CallRecordQueryVO request);

    /**
     * 来自话术训练的通话记录列表 含可访问的用户id列表
     *
     * @param request    导出request
     * @param userIdList 用户id列表
     * @return 需要导出的通话记录
     */
    List<CallRecordExportDTO> selectCallRecordsFromTraining(@Param("request") CallRecordQueryVO request,
                                                            @Param("userIdList") @Nullable List<Long> userIdList);

    /**
     * 通过通话记录重新添加拨打到其他任务
     */
    List<ReAddRecordDTO> selectCallRecordsForReAddByIdsOrConditions(@Param("request") CallRecordQueryVO request);

    /**
     * 已呼客户列表MQ导入查询
     */
    List<CustomerPersonCalledImportDTO> selectCallRecordsForReAddBatchMq(@Param("request") CallRecordQueryVO request);

    /**
     * 通话记录重新添加到短信任务
     */
    List<SmsJobMessageImportVO> selectCallRecordForReAddToSmsJob(@Param("request") CallRecordQueryVO request);
    /**
     * 联系历史(含有权限)重新添加到拨打任务
     */
    List<ReAddRecordDTO> selectCallRecordsForReAddFromJob(@Param("request") CallRecordQueryVO request,
                                                          @Param("userIdList") List<Long> userIdList);

    /**
     * 做计数，参数含义同上
     */
    Integer countCallRecordsByIdsOrConditions(@Param("tenantId") Long tenantId, @Param("request") CallRecordQueryVO request);

    Integer countTasksByIdsOrConditions(@Param("tenantId") Long tenantId, @Param("request") CallRecordQueryVO request);
	Set<Long> getTaskIdsByIdsOrConditions(@Param("tenantId") Long tenantId, @Param("request") CallRecordQueryVO request);

    Integer countCallRecordsBySubAccount(@Param("tenantId") Long tenantId,
                                         @Param("userId") Long userId,
                                         @Param("phoneNumberId") Long phoneNumberId,
                                         @Param("local") CallTypeEnum local,
                                         @Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate);

    /**
     * ope 端获取call record list
     */
    List<CallRecordVO> queryCallRecord(@Param(value = "request") CallRecordBossAndOpeQueryVO callRecordBossAndOpeQueryVO,
                                       @Param(value = "start") Integer start,
                                       @Param(value = "end") Integer end);

    /**
     * 更改跟踪状态
     */
    void updateTrack(@Param("callRecordId") Long callRecordId,
                     @Param("trackStatus") CallRecordTrackStatusEnum callRecordTrackStatusEnum);

    List<Long> selectCustomerPersonIdsByConditions(@Param("request") CallRecordQueryVO request);

    /**
     * 设置通话记录的阅读状态
     *
     * @param callRecordId 通话记录id
     * @param tenantId     租户id
     * @param parameter    更新值
     */
    void updateCallRecordStatus(@Param("callRecordId") Long callRecordId,
                                @Param("tenantId") Long tenantId,
                                @Param("parameter") Integer parameter);


    /**
     * Boss端获取通话记录列表
     */
    List<CallRecordVO> queryBossCallRecord(@Param("request") CallRecordBossAndOpeQueryVO callRecordBossAndOpeQueryVO,
                                           @Param("start") Integer start,
                                           @Param("end") Integer end);

    /**
     * 分页查询某个任务的通话记录
     */
    List<CallRecordPO> selectCallRecordPage(@Param("tenant_id") Long tenant_id, @Param("robot_call_job_id") Long robot_call_job_id,
                                            @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,
                                            @Param("start") Long start, @Param("size") Integer size);

    /**
     * 查询某个任务的通话记录总数
     */
    Long selectCallRecordCount(@Param("tenant_id") Long tenant_id, @Param("robot_call_job_id") Long robot_call_job_id, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 获取昨日的任务数量统计，因为在凌晨2点进行的
     */
    CallTaskCountPO selectRobotCallJobCount();

    /**
     * 查询单个话术的通话记录
     * @param tenantId 客户id
     * @param dialogFlowId 话术id
     */
    Long selectCallRecordCountByDialogFlowId(@Param("tenantId") Long tenantId, @Param("dialogFlowId") Long dialogFlowId);

    /**
     * 分页查询单个话术的通话记录
     * @param tenantId 客户id
     * @param dialogFlowId 话术id
     * @param offset 偏移量
     * @param pageSize 分页大小
     */
    List<CallRecordPO> selectByDialogFlowIdAndPage(@Param("tenantId") Long tenantId, @Param("dialogFlowId") Long dialogFlowId, @Param("offset") Long offset, @Param("pageSize") Integer pageSize);

    /**
     * 添加微信发送记录
     */
    void updateWechatPushUserInfo(@Param("tenantId") Long tenantId, @Param("callRecordId") Long callRecordId, @Param("time") LocalDateTime time, @Param("userIds") Set<Long> userIds);

    List<CallRecordPO> selectCallRecordByCallJobId(@Param("tenantId") Long tenantId, @Param("robotCallJobId") Long robotCallJobId);

    List<CallRecordPO> getByRobotCallTaskId(@Param("tenantId") Long tenantId, @Param("robotCallTaskId") Long robotCallTaskId);
    List<CallRecordPO> getByRobotCallTaskIds(@Param("tenantId") Long tenantId, @Param("robotCallTaskIds") Collection<Long> robotCallTaskIds);

    int countAnsweredByRobotCallTaskId(@Param("tenantId") Long tenantId, @Param("robotCallTaskId") Long robotCallTaskId);

    List<CallCostDetailBO>selectCallCostDetail(@Param("condition") CallCostQueryVO callCostQuery, @Param("userIdList") List<Long> userIdList);

    List<CallCostDetailBO> selectExportCallCostDetail(@Param("condition") CallCostDetailExportRequestVO callCostDetailExportRequest);

    Integer selectCountExportCallCostDetail(@Param("condition")CallCostDetailExportRequestVO callCostDetailExportRequest);


    CallRecordPO selectCallRecordByJobIdAndPhoneNumber(@Param("tenantId") Long tenantId, @Param("robotCallJobId") Long robotCallJobId, @Param("phoneNumber") String phoneNumber);

    List<CallRecordExportDTO> selectOpeAndBossCallRecordsFromDirectCall(@Param("request") CallRecordQueryVO condition);

    void updateRecognize(@Param("callRecordId") Long callRecordId, @Param("emotion") CustomerEmotionEnum emotion, @Param("gender") GenderEnum gender);

    List<TrainRecordVO> selectTrainRecord(@Param("condition") TrainRecordOpeQueryVO condition);

    Integer countTrainRecordResult(@Param("condition")  TrainRecordOpeQueryVO condition);

    int updateCallRecordDistributor(@Param("distributorId") Long distributorId,
                                    @Param("tenantId") Long tenantId,
                                    @Param("limit") Long limit);

    List<CallRecordPO> selectAllRecords(@Param("tenantId") Long tenantId);

        Integer selectRealIntentLevel(@Param("callRecordId") Long callRecordId);

    Long selectCountCallRecordsForExportByIdsOrConditions(@Param("request") CallRecordQueryVO request);

    /**
     * 更新加微相关字段
     */
    void updateAddWechatFriend(@Param("callRecordId") Long callRecordId,
                               @Param("addWechatFriendAccountName") String addWechatFriendAccountName,
                               @Param("addWechatFriendStatus") Integer addWechatFriendStatus,
                               @Param("addWechatFriendScrmResult") ScrmAddFriendResultEnum result,
                               @Param("addWechatFriendSendTime") LocalDateTime addWechatFriendSendTime,
                               @Param("addWechatFriendAdoptTime") LocalDateTime addWechatFriendAdoptTime);

	List<CallRecordPO> selectByCalledPhoneNumber(@Param("tenantId") Long tenantId, @Param("calledPhoneNumbers") List<String> calledPhoneNumbers, @Param("pageSize") Integer pageSize);

	LocalDateTime selectEarliestInDay(@Param("tenantId") Long tenantId, @Param("robotCallJobId") Long robotCallJobId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
	LocalDateTime selectLatestInDay(@Param("tenantId") Long tenantId, @Param("robotCallJobId") Long robotCallJobId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    int checkExistByRobotCallTaskId(@Param("tenantId") Long tenantId, @Param("robotCallTaskId") Long robotCallTaskId);

    List<CallRecordVO> queryCallRecordByIds(@Param("callRecordIdList") List<Long> callRecordIdList);

    Integer selectCallRecordsFromTrainingCount(@Param("request") CallRecordQueryVO request,
                                                   @Param("userIdList") @Nullable List<Long> userIdList);

    List<Long> selectIdsByRobotCallJobId(@Param("tenantId") Long tenantId,
                                         @Param("robotCallJobId")  Long robotCallJobId);

    LocalDateTime queryFirstOrLastRecordPlan(@Param(value = "callOutPlanId") Long callOutPlanId,
                                            @Param("tenantId") Long tenantId,
                                            @Param(value = "start") Boolean start);

    List<CallRecordPO> selectCallRecordByTime(@Param("tenantId") Long tenantId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("callbackStartTime") LocalDateTime callbackStartTime, @Param("callbackEndTime") LocalDateTime callbackEndTime);

    List<CallRecordPO> selectCallRecordForAntRequest(@Param("request")GetJobDetailAntRequestVO request,
                                                     @Param("start")Integer start,
                                                     @Param("end")Integer end);

    Integer selectCountCallRecordForAntRequest(@Param("request")GetJobDetailAntRequestVO request);

    String selectBingJianId(@Param("id") String id);

	List<CallRecordPO> selectByTenantIdStartTime(@Param("tenantId") Long tenantId, @Param("day") Integer day);

    /**
     * 根据线路和时间获取计费分钟数
     */
    Integer getBillChatDuration(@Param("phoneNumberId") Long phoneNumberId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    Integer countCallRecordByJobIdOnDistinctNumber(@Param("condition") CallResultQueryVO callResultQueryVO);

	List<Long> selectCallRecordIdsOrderByCallRecordId(@Param("callRecordId") Long callRecordId, @Param("batchSize") Integer batchSize, @Param("localDate") LocalDate localDate);

	List<CallRecordPO> selectOrderByCallRecordId(@Param("callRecordId") Long callRecordId, @Param("batchSize") Integer batchSize, @Param("lastCallRecordId") Long lastCallRecordId);

	void deleteDeploymentInformationByCallRecordIds(@Param("callRecordIds") Collection<Long> callRecordIds);

	Long selectCallRecordIdAfterLocalDateTime(@Param("localDateTime") LocalDateTime localDateTime);
	Long selectCallRecordIdAfterEqualLocalDateTime(@Param("localDateTime") LocalDateTime localDateTime);

    List<CallRecordExportDTO> getCallRecordListLimit(@Param("request") CallRecordLimitQueryVO callRecordLimitQueryVO,@Param("startId")Long startId);
    
    List<CallRecordPO> selectLastThreeCallRecord(@Param("tenantId") Long tenantId,@Param("calledPhoneNumber") String calledPhoneNumber);
    

    @TargetDataSource(value = DataSourceEnum.BASE_ADB)
    List<Long> getIdListByADB(@Param("tenantId") Long tenantId, @Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime,@Param("startPoint") Long startPoint,@Param("limit") Integer limit);

    @TargetDataSource(value = DataSourceEnum.BASE_ADB)
    List<CallRecordPO> getListByIdsADB(@Param("idList") List<Long> ids);
}
