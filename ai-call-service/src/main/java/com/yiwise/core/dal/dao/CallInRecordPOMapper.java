package com.yiwise.core.dal.dao;

import com.yiwise.core.config.DataSourceEnum;
import com.yiwise.core.dal.entity.CallInRecordPO;
import com.yiwise.core.datasource.TargetDataSource;
import com.yiwise.core.model.dto.AssignCallOutTaskExportDTO;
import com.yiwise.core.model.dto.CallInRecordExportDTO;
import com.yiwise.core.model.dto.ReAddRecordDTO;
import com.yiwise.core.model.vo.callin.CallInRecordExportCustomerVO;
import com.yiwise.core.model.vo.callin.CallInRecordListVO;
import com.yiwise.core.model.vo.callin.CallInRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.CsRecordQueryVO;
import com.yiwise.core.model.vo.callrecord.CsStaffCallRecordQueryVO;
import com.yiwise.core.model.vo.cs.CsAndCallInRecordVO;
import com.yiwise.core.model.vo.csseat.CsStaffCallRecordVO;
import com.yiwise.core.model.vo.csseat.XSecondMonitorVO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/1/14 2:58 PM
 **/
@TargetDataSource(DataSourceEnum.QC_POLARDB)
public interface CallInRecordPOMapper extends Mapper<CallInRecordPO> {

    List<CallInRecordListVO> queryByCondition(@Param("tenantId") Long tenantId, @Param("condition") CallInRecordQueryVO condition);

    List<CallInRecordListVO> getCallInRecordHistory(@Param("tenantId") Long tenantId, @Param("condition") CallInRecordQueryVO condition);

    List<CallInRecordExportDTO> getCallInRecordHistoryForExport(@Param("tenantId") Long tenantId, @Param("condition") CallInRecordQueryVO condition);

    /**
     * 从呼入记录查询客户信息用于导入到人工外呼任务
     */
    List<ReAddRecordDTO> selectCustomerInfo(@Param("tenantId") Long tenantId, @Param("condition") CallInRecordExportCustomerVO condition);

    Integer countCallInRecordsByIdsOrConditions(@Param("tenantId") Long tenantId, @Param("condition") CallInRecordExportCustomerVO condition);

    void updateReadStatus(@Param("tenantId") Long tenantId, @Param("callInRecordId") Long callInRecordId, @Param("partStatus") Integer partStatus);

    void updateResultStatus2Answered(@Param("tenantId") Long tenantId, @Param("callInRecordId") Long callInRecordId, @Param("hangupBy") Integer hangupBy);

    CallInRecordPO findByCallInRecordId(@Param("tenantId") Long tenantId, @Param("callInRecordId") Long callInRecordId);

    CallInRecordPO getCsStaffRecentRecord(@Param("tenantId") Long tenantId, @Param("csStaffId") Long csStaffId);

    List<CallInRecordPO> queryByCustomerPersonId(@Param("tenantId") Long tenantId, @Param("customerPersonId") Long customerPersonId);

    void updateCallInRecordIntentLevel(@Param("tenantId") Long tenantId, @Param("callInRecordId") Long callInRecordId, @Param("realIntentLevel") Integer intentLevel, @Param("intentLevelTagId") Long intentLevelTagId);

    List<CallInRecordPO> queryAllByTenantId(@Param("tenantId") Long tenantId);

    List<CallInRecordListVO> queryOpeByCondition(@Param("condition") CallInRecordQueryVO condition);

    List<CsStaffCallRecordVO> queryCallInHistory(@Param("request") CsStaffCallRecordQueryVO csStaffCallRecordQueryVO);

    List<CsStaffCallRecordVO> queryCallInHistoryOnlyCs(@Param("request") CsStaffCallRecordQueryVO csStaffCallRecordQueryVO);

    List<XSecondMonitorVO> getCountForXSecond(@Param("tenantId") Long tenantId, @Param("csStaffIdList") List<Long> csStaffIdList, @Param("second") Integer second);

    /**
     * 修改call_in_record的创建人并将crm读取状态
     */
    void updateCreateUser(@Param("tenantId") Long tenantId, @Param("userId") Long userId, @Param("customerPersonIds") Collection<Long> customerPersonIds);

    List<CallInRecordPO> selectByCustomerPersonId(@Param("tenantId") Long tenantId, @Param("customerPersonId") Long customerPersonId);

    List<Long> getCsStaffIdListCallInRecord(@Param("tenantId") Long tenantId, @Param("list") List<Long> csStaffIdList, @Param("startDate") LocalDate starDate,
                                            @Param("endDate") LocalDate endDate, @Param("startTime") LocalTime startTime, @Param("endTime") LocalTime endTime);

    List<CsAndCallInRecordVO> searchCallInRecordList(@Param("request") CsRecordQueryVO queryVO, @Param("staffIdList") List<Long> staffIdList, @Param("groupIdList") List<Long> groupIdList, @Param("userIdList") List<Long> userIdList, @Param("csCallRecordIdList") List<Long> csCallRecordIdList, @Param("callInRecordIdList") List<Long> callInRecordIdList);

    List<AssignCallOutTaskExportDTO> searchCallInRecordImportToWaitingCallDTO(@Param("request") CsRecordQueryVO queryVO, @Param("staffIdList") List<Long> staffIdList, @Param("groupIdList") List<Long> groupIdList, @Param("userIdList") List<Long> userIdList, @Param("csCallRecordIdList") List<Long> csCallRecordIdList, @Param("callInRecordIdList") List<Long> callInRecordIdList);

    List<CallInRecordPO> selectAllRecords(@Param("tenantId") Long tenantId);

    List<CallInRecordPO> selectByIds(@Param("list") List<Long> ids);

    Integer countByTransferType(@Param("tenantId") Long tenantId,@Param("transferType") Integer transferType, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    CallInRecordPO getLastCallInRecord(@Param("tenantId")Long tenantId);

    List<Long> selectDistinctTenantId();
    void updateDistributorIdByTenantId(@Param("tenantId") Long tenantId, @Param("distributorId") Long distributorId);
}
