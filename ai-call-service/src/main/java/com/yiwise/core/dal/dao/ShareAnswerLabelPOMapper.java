package com.yiwise.core.dal.dao;

import com.yiwise.core.model.dialogflow.entity.ShareAnswerLabelPO;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface ShareAnswerLabelPOMapper extends Mapper<ShareAnswerLabelPO> {

    List<ShareAnswerLabelPO> selectByIdList(@Param("shareAnswerLabelIdList") List<Long> shareAnswerLabelIdList);

    List<ShareAnswerLabelPO> queryAll();

    List<ShareAnswerLabelPO> getByName(@Param("name") String name);
}
