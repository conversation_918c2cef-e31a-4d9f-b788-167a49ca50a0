package com.yiwise.core.batch.config.job;

import com.yiwise.core.batch.config.BatchConfig;
import com.yiwise.core.batch.listener.jobexecution.ImportJobExecListener;
import com.yiwise.core.model.enums.SpringBatchJobTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.yiwise.core.model.enums.SpringBatchJobTypeEnum.UPLOAD_ORDER_CALLOUT_DATA;


/**
 * <AUTHOR>
 * @Date 2018/11/16
 **/
@Slf4j
@Configuration
public class OPEJobConfig extends BatchConfig {

    /**
     * 话费详单导出
     */
    @Bean
    public Job callCostDetailExportJob(@Qualifier("callCostDetailExportStep") Step callCostDetailExport,
                                       @Qualifier("ossUploadStep") Step ossUploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_CALL_COST.name())
                .start(callCostDetailExport)
                .next(ossUploadStep)
                .build();
    }

    /**
     * 话费详单导出 (人工外呼)
     */
    @Bean
    public Job callCostCsRecordExportJob(@Qualifier("callCostCsRecordExportStep") Step callCostCsRecordExportStep,
                                         @Qualifier("ossUploadStep") Step ossUploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_CALL_COST_CS_RECORD.name())
                .start(callCostCsRecordExportStep)
                .next(ossUploadStep)
                .build();
    }

    /**
     * 线路消费流水导出
     */
    @Bean
    public Job costListTenantLineExportJob(@Qualifier("costListTenantLineExportStep") Step exportStep,
                                           @Qualifier("ossUploadStep") Step ossUploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_TENANT_COST_LIST_LINE.name())
                .start(exportStep)
                .next(ossUploadStep)
                .build();
    }


    /**
     * 短信消费流水导出
     */
    @Bean
    public Job costListTenantMessageExportJob(@Qualifier("costListTenantMessageExportStep") Step exportStep,
                                              @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_TENANT_COST_LIST_MESSAGE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job allRechargeRecordExportJob(@Qualifier("allRechargeRecordExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_ALL_RECHARGE_RECORD.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job financeStatsExportJob(@Qualifier("financeStatsExportStep") Step exportStep,
                                     @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_FINANCE_TENANT_LINE_STATS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job financeStatsExportJobForQiyu(@Qualifier("financeStatsExportForQiyuStep") Step exportStep,
                                            @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_FINANCE_TENANT_LINE_STATS_FOR_QIYU.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDistributorPrestoreJob(@Qualifier("opeDistributorPrestoreStep") Step exportStep,
                                         @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_FINANCE_DISTRIBUTOR_PRESTORE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job financeTenantAllLineStatsExportJob(@Qualifier("financeTenantAllLineStatsExportStep") Step exportStep,
                                                  @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_FINANCE_TENANT_ALL_LINE_STATS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job rechargeStreamExportJob(@Qualifier("rechargeStreamExportStep") Step exportStep,
                                       @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_RECHARGE_DISTRIBUTE_STREAM.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job qcCostExportJob(@Qualifier("qcCostExportStep") Step exportStep,
                               @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_QC_COST_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();

    }

    @Bean
    public Job qcRechargeExportJob(@Qualifier("qcRechargeExportStep") Step exportStep,
                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_QC_RECHARGE_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantAccountRechargeJob(@Qualifier("tenantAccountRechargeStep") Step exportStep,
                                        @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_AICC_ACCOUNT_RECHARGE_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantLineRechargeJob(@Qualifier("tenantLineRechargeStep") Step exportStep,
                                     @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_AICC_LINE_RECHARGE_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantMessageRechargeJob(@Qualifier("tenantMessageRechargeStep") Step exportStep,
                                        @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_AICC_MESSAGE_RECHARGE_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantQcRechargeJob(@Qualifier("tenantQcRechargeStep") Step exportStep,
                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_AICC_QC_RECHARGE_RECORDS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantStatsExportJob(@Qualifier("opeTenantStatsExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_EXPORT_TENANT_STATS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeBossShareWhiteListExportJob(@Qualifier("opeBossShareWhiteListExportStep") Step exportStep,
                                              @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_BOSS_SHARE_WHITE_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantStatsDetailExportJob(@Qualifier("tenantStatsDetailExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_TENANT_STATS_DETAIL.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job tenantStatsDetailNewExportJob(@Qualifier("tenantStatsDetailNewExportStep") Step exportStep,
                                             @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_TENANT_STATS_DETAIL_NEW.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDirectCustomerExportJob(@Qualifier("opeDirectCustomerExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_CUSTOMER.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDirectCustomerByRobotExportJob(@Qualifier("opeDirectCustomerByRobotExportStep") Step exportStep,
                                                 @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_CUSTOMER_BY_ROBOT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDistributorCustomerByRobotExportJob(@Qualifier("opeDistributorCustomerByRobotExportStep") Step exportStep,
                                                      @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DISTRIBUTOR_CUSTOMER_BY_ROBOT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDirectCustomerByDialogFlowExportJob(@Qualifier("opeDirectCustomerByDialogFlowExportStep") Step exportStep,
                                                      @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_CUSTOMER_BY_DIALOG_FLOW.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDistributorDialogFlowExportJob(@Qualifier("opeDistributorDialogFlowExportStep") Step exportStep,
                                                 @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DISTRIBUTOR_DIALOG_FLOW.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opePhoneLineExportJob(@Qualifier("opePhoneLineExportStep") Step exportStep,
                                     @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_PHONE_LINE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDialogPhoneRelationExportJob(@Qualifier("opeDialogPhoneRelationExportStep") Step exportStep,
                                               @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIALOG_PHONE_RELATION.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDirectDistributorExportJob(@Qualifier("opeDirectDistributorExportStep") Step exportStep,
                                             @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DIRECT_DISTRIBUTOR.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDistributorCustomerExportJob(@Qualifier("opeDistributorCustomerExportStep") Step exportStep,
                                               @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DISTRIBUTOR_CUSTOMER.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeDistributorCustomerExportForQiyuJob(@Qualifier("opeDistributorCustomerExportForQiyuStep") Step exportStep,
                                                      @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_DISTRIBUTOR_CUSTOMER_FOR_QIYU.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeSubDistributorExportJob(@Qualifier("opeSubDistributorExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_SUB_DISTRIBUTOR.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeSubDistributorCustomerExportJob(@Qualifier("opeSubDistributorCustomerExportStep") Step exportStep,
                                                  @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_SUB_DISTRIBUTOR_CUSTOMER.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }


    @Bean
    public Job huaweiFreeTryExportJob(@Qualifier("huaweiFreeTryExportStep") Step exportStep,
                                      @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_HUAWEI_FREE_TRY.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opeTenantQcCostExportJob(@Qualifier("opeTenantQcCostExportStep") Step step,
                                        @Qualifier("ossUploadStep") Step ossUploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_EXPORT_QC_COST_STATS.name())
                .start(step)
                .next(ossUploadStep)
                .build();
    }

    @Bean
    public Job opeTenantQcCostDetailExportJob(@Qualifier("opeTenantQcCostDetailExportStep") Step step,
                                              @Qualifier("ossUploadStep") Step ossUploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_EXPORT_QC_COST_DETAIL_STATS.name())
                .start(step)
                .next(ossUploadStep)
                .build();
    }

    @Bean
    public Job vosReportExportJob(@Qualifier("vosReportExportStep") Step exportStep,
                                  @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_VOS_REPORT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job dataReportExportJob(@Qualifier("dataReportExportStep") Step exportStep,
                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_DATA_REPORT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job complaintHistoryExportJob(@Qualifier("complaintHistoryExportStep") Step exportStep,
                                         @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_COMPLAINT_HISTORY.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job complaintStatsExportJob(@Qualifier("complaintStatsExportStep") Step exportStep,
                                       @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_COMPLAINT_STATS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 批量修改客户话费 step
     */
    @Bean
    public Job customerPhoneBillBatchJob(@Qualifier("customerPhoneBillStep") Step startStep,
                                         @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_BATCH_UPDATE_PHONE_BILL_STEP.name())
                .start(startStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 共享黑名单批量导入
     */
    @Bean
    public Job shareBlackListImportJob(@Qualifier("shareBlackListImportStep") Step shareBlackListImportStep,
                                       @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.IMPORT_SHARED_BLACK_LIST.name())
                .start(shareBlackListImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 话术推荐批量导入
     */
    @Bean
    public Job dialogFlowRecommendImportJob(@Qualifier("dialogFlowRecommendImportStep") Step dialogFlowRecommendImportStep,
                                            @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.IMPORT_DIALOGFLOW_RECOMMEND.name())
                .start(dialogFlowRecommendImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 话术推荐导出
     */
    @Bean
    public Job dialogFlowRecommendeXportJob(@Qualifier("dialogFlowRecommendExportStep") Step dialogFlowRecommendExportStep,
                                            @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_DIALOGFLOW_RECOMMEND.name())
                .start(dialogFlowRecommendExportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job exportFinanceSmsStatsJob(@Qualifier("exportFinanceSmsStatsStep") Step exportStep,
                                        @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_FINANCE_SMS_STATS.name()).start(exportStep).next(uploadStep).build();
    }

    @Bean
    public Job callOutDataReportExportJob(@Qualifier("callOutDataReportExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_CALL_OUT_DATA_REPORT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job callOutDataReportDetailExportJob(@Qualifier("callOutDataReportDetailExportStep") Step exportStep,
                                                @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_CALL_OUT_DATA_DETAIL_REPORT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }


    @Bean
    public Job exportFinanceDirectCustomerStatsJob(@Qualifier("exportFinanceDirectCustomerStatsStep") Step exportStep,
                                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_FINANCE_DIRECT_CUSTOMER_STATS.name()).start(exportStep).next(uploadStep).build();
    }

    @Bean
    public Job exportSmsTestStatJob(@Qualifier("exportSmsTestStatStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_SMS_TEST_STAT_JOB.name()).start(exportStep).next(uploadStep).build();
    }

    @Bean
    public Job phoneNumberByGwExportJob(@Qualifier("phoneNumberByGwExportStep") Step exportStep,
                                        @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONE_NUMBER_BY_GATEWAYS.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job phoneNumberByCarrierExportJob(@Qualifier("phoneNumberByCarrierExportStep") Step exportStep,
                                             @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONE_NUMBER_BY_CARRIER.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job opensipsAccExportJob(@Qualifier("opensipsAccExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPENSIPS_ACC.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job mainBrandExportJob(@Qualifier("mainBrandExportStep") Step exportStep,
                                  @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_MAIN_BRAND_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job mainBrandTenantCostExportJob(@Qualifier("mainBrandTenantCostExportStep") Step exportStep,
                                            @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_MAIN_BRAND_TENANT_COST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job saasFinanceExportJob(@Qualifier("saasFinanceExportStep") Step exportStep,
                             @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_SAAS_FINANCE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job directCallStatsOpeExportJob(@Qualifier("directCallStatsOpeExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.DIRECT_CALL_STATS_OPE_EXPORT_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job maCallStatsOpeExportJob(@Qualifier("maCallStatsOpeExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.MA_CALL_STATS_OPE_EXPORT_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job directCallStatsAiccExportJob(@Qualifier("directCallStatsAiccExportStep") Step exportStep,
                                     @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.DIRECT_CALL_STATS_AICC_EXPORT_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job maCallStatsAiccExportJob(@Qualifier("maCallStatsAiccExportStep") Step exportStep,
                                     @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.MA_CALL_STATS_AICC_EXPORT_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job opensispsCallCostExportJob(@Qualifier("opensispsCallCostExportStep") Step exportStep,
                                 @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPENSIPS_CALL_COST_EXPORT_JOB.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job smsBillingCostExportJob(@Qualifier("smsBillingCostExportStep") Step exportStep,
                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.SMS_BILLING_COST_EXPORT_JOB.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job maCallCostDetailExportJob(@Qualifier("maCallCostDetailExportStep") Step exportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_MA_CALL_COST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job tenantChurnWarnExportJob(@Qualifier("tenantChurnWarnExportStep") Step exportStep,
                                 @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.TENANT_CHURN_WARN_EXPORT_LIST.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job phoneNumberStatementExportJob(@Qualifier("phoneNumberStatementExportStep") Step exportStep,
                                      @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONENUMBER_STATEMENT.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job phoneNumberInvoiceExportJob(@Qualifier("phoneNumberInvoiceExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONENUMBER_INVOICE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job phoneNumberConsumeExportJob(@Qualifier("phoneNumberConsumeExportStep") Step exportStep,
                                    @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONENUMBER_CONSUME.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    Job phoneNumberRechargeRecordExportJob(@Qualifier("phoneNumberReChargeRecordExportStep") Step exportStep,
                                           @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PHONENUMBER_RECHARGE_RECORD.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

	/**
	 * OPE-财务管理-充值记录-支付宝充值-账户余额充值
	 */
	@Bean
	public Job opeBillingAlipayRechargeExportJob(@Qualifier("opeBillingAlipayRechargeExportStep") Step exportStep,
	                                             @Qualifier("ossUploadStep") Step uploadStep) {
	    return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_BILLING_ALIPAY_RECHARGE_EXPORT_JOB.name())
			    .start(exportStep)
			    .next(uploadStep)
			    .build();
    }

	/**
	 * OPE-财务管理-充值记录-线下充值-账户余额充值
	 */
	@Bean
	public Job opeBillingOfflineRechargeExportJob(@Qualifier("opeBillingOfflineRechargeExportStep") Step exportStep,
	                                              @Qualifier("ossUploadStep") Step uploadStep) {
	    return jobBuilderFactory.get(SpringBatchJobTypeEnum.OPE_BILLING_OFFLINE_RECHARGE_EXPORT_JOB.name())
			    .start(exportStep)
			    .next(uploadStep)
			    .build();
    }

    /**
     * 导出隐私号计费统计
     */
    @Bean
    public Job exportPrivacyNumberCostOpeJob(@Qualifier("exportPrivacyNumberCostOpeStep") Step exportStep,
                                             @Qualifier("ossUploadStep") Step uploadStep) {
        return  jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PRIVACY_NUMBER_COST_OPE.name())
                .start(exportStep)
                .next(uploadStep)
                .build();
    }

	/**
	 * OPE-通讯工作台-数据统计-供应商月成本-导出表格
	 */
    @Bean
	public Job exportSupplierMonthlyCostJob(@Qualifier("exportSupplierMonthlyCostStep") Step exportStep,
                                            @Qualifier("ossUploadStep") Step uploadStep) {
	    return jobBuilderFactory.get(SpringBatchJobTypeEnum.SUPPLIER_MONTHLY_COST_EXPORT_JOB.name())
			    .start(exportStep)
			    .next(uploadStep)
			    .build();
    }

	/**
	 * OPE-通讯工作台-隐私号管理-实名状态查询
	 */
    @Bean
	public Job exportPrivacyNumberBindingOpeJob(@Qualifier("exportPrivacyNumberBindingOpeStep") Step exportStep,
                                                @Qualifier("ossUploadStep") Step uploadStep) {
	    return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_PRIVACY_NUMBER_BINDING_OPE.name())
			    .start(exportStep)
			    .next(uploadStep)
			    .build();
    }

    /**
     * 双呼固话配置导入
     *
     */
    @Bean
    public Job fixedPhoneConfigImportJob(@Qualifier("fixedPhoneConfigStep") Step fixedPhoneConfigStep,
                                       @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.IMPORT_FIXED_PHONE_CONFIG_LIST.name())
                .start(fixedPhoneConfigStep)
                .next(uploadStep)
                .build();
    }

    @Bean
    public Job compliantHistoryImportJob(@Qualifier("complaintHistoryStep") Step complaintHistoryStep,
                                         @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.IMPORT_HISTORY_COMPLAINT_HISTORY_LIST.name())
                .start(complaintHistoryStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 择时外呼excel导入
     * @param robotTimeCallTaskImportStep
     * @param uploadStep
     * @return
     */
    @Bean
    public Job robotTimeCallTaskImportJob(@Qualifier("robotTimeCallTaskImportStep") Step robotTimeCallTaskImportStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.ROBOT_TIME_CALL_TASK_IMPORT.name())
                .start(robotTimeCallTaskImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 择时外呼人群包导入
     * @param robotTimeCallTaskUserGroupImportStep
     * @param uploadStep
     * @return
     */
    @Bean
    public Job robotTimeCallTaskUserGroupImportJob(@Qualifier("robotTimeCallTaskUserGroupImportStep") Step robotTimeCallTaskUserGroupImportStep,
                                                   @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.ROBOT_TIME_CALL_TASK_USER_GROUP_IMPORT.name())
                .start(robotTimeCallTaskUserGroupImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 择时数据全量导入到任务
     * @param robotTimeCallTaskTimeUserGroupImportStep
     * @param uploadStep
     * @return
     */
    @Bean
    public Job robotTimeCallTaskTimeUserGroupImportJob(@Qualifier("robotTimeCallTaskTimeUserGroupImportStep") Step robotTimeCallTaskTimeUserGroupImportStep,
                                                       @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.ROBOT_TIME_CALL_TASK_TIME_USER_GROUP_IMPORT.name())
                .start(robotTimeCallTaskTimeUserGroupImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 择时数据全量拉取
     * @param robotTimeCallTaskCacheUserGroupImportStep
     * @param uploadStep
     * @return
     */
    @Bean
    public Job robotTimeCallTaskCacheUserGroupImportJob(@Qualifier("robotTimeCallTaskCacheUserGroupImportStep") Step robotTimeCallTaskCacheUserGroupImportStep,
                                                        @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.ROBOT_TIME_CALL_TASK_CACHE_USER_GROUP_IMPORT.name())
                .start(robotTimeCallTaskCacheUserGroupImportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * ope导出隐私号通话记录
     */
    @Bean
    public Job privacyNumberOpeHistoryExportJob(@Qualifier("privacyNumberOpeHistoryExportStep") Step privacyNumberOpeHistoryExportStep,
                                                @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_PRIVACY_NUMBER_HISTORY.name())
                .start(privacyNumberOpeHistoryExportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * ope导出隐私号双呼固话号码管理
     */
    @Bean
    public Job fixedPhoneConfigExportJob(@Qualifier("fixedPhoneConfigExportStep") Step fixedPhoneConfigExportStep,
                                         @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_FIXED_PHONE_CONFIG.name())
                .start(fixedPhoneConfigExportStep)
                .next(uploadStep)
                .build();
    }

    /**
     * ope导出A路号码报备
     */
    @Bean
    public Job privacyNumberAuthenticationExportJob(@Qualifier("privacyNumberAuthenticationExportStep") Step privacyNumberAuthenticationStep,
                                              @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.EXPORT_OPE_PRIVACY_NUMBER_AUTHENTICATION.name())
                .start(privacyNumberAuthenticationStep)
                .next(uploadStep)
                .build();
    }

    /**
     * 择时外呼拉取人群到导入任务
     * @param robotTimeCallTaskUserGroupImportStepV2
     * @param uploadStep
     * @return
     */
    @Bean
    public Job robotTimeCallTaskUserGroupImportJobV2(@Qualifier("robotTimeCallTaskUserGroupImportStepV2") Step robotTimeCallTaskUserGroupImportStepV2,
                                                     @Qualifier("ossUploadStep") Step uploadStep,
                                                     ImportJobExecListener importJobExecListener) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.ROBOT_TIME_CALL_TASK_USER_GROUP_IMPORT_V2.name())
                .listener(importJobExecListener)
                .start(robotTimeCallTaskUserGroupImportStepV2)
                .next(uploadStep)
                .build();
    }


    @Bean
    public Job uploadTongDaCallOutDataJob(@Qualifier("orderDataUploadStep") Step orderDataUploadStep,
                                          @Qualifier("ossUploadStep") Step uploadStep) {
        return jobBuilderFactory.get(SpringBatchJobTypeEnum.UPLOAD_ORDER_CALLOUT_DATA.name())
                .start(orderDataUploadStep)
                .next(uploadStep)
                .build();
    }

}
