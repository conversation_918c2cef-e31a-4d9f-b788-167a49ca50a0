package com.yiwise.core.batch.support;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.bean.JsonUtils;
import com.yiwise.base.service.redis.api.RedisOpsService;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.common.ExcelRowModel;
import com.yiwise.core.batch.entity.dto.SheetInfoDTO;
import com.yiwise.core.config.CommonApplicationConstant;
import com.yiwise.core.config.SocketIOEventConst;
import com.yiwise.core.helper.PhoneNumberEncryptDesensitizationUtils;
import com.yiwise.core.model.alimessagequeue.websocket.ImportOutputProgressMsg;
import com.yiwise.core.model.bo.websocket.DistributorUserIdPrincipal;
import com.yiwise.core.model.bo.websocket.TenantUserIdPrincipal;
import com.yiwise.core.model.bo.websocket.UserIdPrincipal;
import com.yiwise.core.model.enums.SystemEnum;
import com.yiwise.core.model.vo.batch.JobQueryResultVO;
import com.yiwise.core.model.vo.batch.QueryStatus;
import com.yiwise.core.model.vo.customer.ImportOutputProgressVO;
import com.yiwise.core.service.engine.SpringBatchJobLogService;
import com.yiwise.core.service.redis.RedisKeyCenter;
import com.yiwise.core.service.socketio.SocketIOService;
import com.yiwise.core.service.websocket.WebSocketOverMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.ItemStreamWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.yiwise.core.config.CommonApplicationConstant.OPE_TENANT_ID;

/**
 * @see WorkbookWriter
 * @see SheetWriter
 *
 * 从jobParameter中获取必需的参数，将参数传入WorkbookWriter和SheetWriter用于初始化
 * 这个Writer主要是给导出用的，open()里面会初始化所有的sheet
 * 因为一部分需求中Reader读到的数据和Writer写入的数据是一对多的关系，所以需要重写write方法来实现一对多写入的业务
 *
 * <AUTHOR>
 * @date 2019-07-10
 */
@Slf4j
@Service
@StepScope
public class ExcelWriter<T> implements ItemStreamWriter<T>, StepExecutionListener {

    @Lazy
    @Resource
    protected WebSocketOverMQService webSocketOverMQService;

    @Lazy
    @Resource
    private SocketIOService socketIOService;

    @Resource
    private RedisOpsService redisOpsService;

    @Resource
    protected SpringBatchJobLogService springBatchJobLogService;

    @Value("#{jobParameters['EXPORT_FILE_PATH']}")
    protected String exportFilePath;

    @Value("#{jobParameters['CURRENT_USER_ID']}")
    protected Long currentUserId;

    @Value("#{jobParameters['TENANT_ID']}")
    protected Long tenantId;

    @Value("#{jobParameters['DISTRIBUTOR_ID']}")
    protected Long distributorId;

    @Value("#{jobParameters['SYSTEM_TYPE']}")
    protected SystemEnum systemEnum;

    @Value("#{jobParameters['JOB_TYPE']}")
    protected Long jobType;

    protected List<SheetInfoDTO> sheetInfoList = Lists.newArrayList();

    protected ImportOutputProgressVO importOutputProgressVO;

    protected UserIdPrincipal userIdPrincipal;

    protected StepExecution stepExecution;

    protected ExecutionContext executionContext;

    protected WorkbookWriter workbookWriter;

    protected List<SheetWriter> sheetWriterList = Lists.newArrayList();

    protected boolean isHidePhoneNumber;

    public ExcelWriter() {
        workbookWriter = new WorkbookWriter();
    }

    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        try {
            workbookWriter.open(exportFilePath);
            sheetInfoList.forEach(sheetInfoDTO -> workbookWriter.createSheet(sheetInfoDTO.getSheetName(), sheetInfoDTO.getHeaderList()));
            sheetWriterList = workbookWriter.getSheetWriterList();
        } catch (IOException | InvalidFormatException e) {
            throw new ItemStreamException("打开输出文件" + exportFilePath + "报错", e);
        }
    }

    /**
     * 根据实际业务重写该方法
     */
    @Override
    public void write(List<? extends T> items) {
        if (CollectionUtils.isNotEmpty(sheetWriterList)) {
            items.forEach(item -> {
                int size = 0;

                for (SheetWriter sheetWriter : sheetWriterList) {
                    ExcelRowModel excelRowModel = (ExcelRowModel) item;
                    sheetWriter.writeRow(excelRowModel.getRowModelMap());
                    size++;
                }

                if (Objects.nonNull(importOutputProgressVO)) {
                    importOutputProgressVO.addSuccessProgress(size);
                    sendMQ();
                }
            });
        }
    }

    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {

    }

    @Override
    public void close() throws ItemStreamException {
        if (workbookWriter != null) {
            try {
                workbookWriter.close();
            } catch (IOException e) {
                throw new ItemStreamException("关闭excelWriter报错", e);
            }
        }
    }

    /**
     * 现在没有使用bean注入，所以这些参数需要手动获取
     */
    @Override
    public void beforeStep(StepExecution stepExecution) {
        this.stepExecution = stepExecution;
        executionContext = stepExecution.getExecutionContext();
        String sheetInfoListStr = stepExecution.getJobParameters().getString("SHEET_INFO_LIST");
        if (StringUtils.isEmpty(sheetInfoListStr)) {
            log.error("任务参数中缺少SHEET_INFO_LIST");
        }
        sheetInfoList = JsonUtils.string2Object(sheetInfoListStr, new TypeReference<List<SheetInfoDTO>>() {
        });

        JobInstance jobInstance = stepExecution.getJobExecution().getJobInstance();
        JobQueryResultVO jobLogByJobInstanceId = springBatchJobLogService.getJobLogByJobInstanceId(jobInstance.getInstanceId());
        this.importOutputProgressVO = new ImportOutputProgressVO(jobLogByJobInstanceId.getJobName(), jobInstance.getInstanceId());
        if (Objects.isNull(systemEnum)) {
            systemEnum = SystemEnum.CRM;
        }
        if (SystemEnum.BOSS.equals(systemEnum)) {
            this.userIdPrincipal = new DistributorUserIdPrincipal(distributorId, currentUserId);
        } else if (SystemEnum.OPE.equals(systemEnum)) {
            // OPE里面tenantId和distributedID都是0
            this.userIdPrincipal = new TenantUserIdPrincipal(OPE_TENANT_ID, currentUserId);
        } else {
            this.userIdPrincipal = new TenantUserIdPrincipal(tenantId, currentUserId);
        }
        isHidePhoneNumber = PhoneNumberEncryptDesensitizationUtils.isHidePhoneNumberNotCache(currentUserId, systemEnum);
        beforeStepAction();
    }

    protected void beforeStepAction() {

    }

    protected void afterStepAction() {

    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        afterStepAction();
        this.importOutputProgressVO.setQueryStatus(QueryStatus.COMPLETED);
        importOutputProgressVO.setSuccessProgress(stepExecution.getWriteCount());

        sendMQ();
        sendReadStatusMQ();
        return stepExecution.getExitStatus();
    }

    private void sendReadStatusMQ() {
        if (CommonApplicationConstant.CURR_ENV.isNotLocal()) {
            try {
                String judgeType = SocketIOEventConst.READ_TOTAL_EVENT;
                if (tenantId != null) {
                    // 此时还没更新任务状态 总数需要加1
                    Integer readStatusCount = springBatchJobLogService.getReadStatusCount(tenantId, currentUserId);
                    this.importOutputProgressVO.setReadStatusTotal(readStatusCount + 1);
                    socketIOService.sendSocketIOMsg(currentUserId, judgeType, new ImportOutputProgressMsg(this.importOutputProgressVO), tenantId);
                } else {
                    Integer readStatusCount = springBatchJobLogService.getReadStatusCount(new ImportOutputProgressMsg(this.importOutputProgressVO), currentUserId);
                    this.importOutputProgressVO.setReadStatusTotal(readStatusCount + 1);
                    socketIOService.sendSocketIOMsg(currentUserId, judgeType, new ImportOutputProgressMsg(this.importOutputProgressVO));
                }
            }catch (Exception e) {
                log.error("发送mq未读任务数量出错", e);
            }
        }
    }

    protected void putConflictCount(int conflictCount) {
        if (!executionContext.containsKey(BatchConstant.Execution.CONFLICT_COUNT)) {
            executionContext.putInt(BatchConstant.Execution.CONFLICT_COUNT, conflictCount);
        } else {
            int currentConflictCount = executionContext.getInt(BatchConstant.Execution.CONFLICT_COUNT);
            executionContext.putInt(BatchConstant.Execution.CONFLICT_COUNT, currentConflictCount + conflictCount);
        }
    }

    public void sendMQ() {
        if (CommonApplicationConstant.CURR_ENV.isNotLocal()) {
            try {
                webSocketOverMQService.sendIOProgressMessageToUser(userIdPrincipal.getName(), new ImportOutputProgressMsg(this.importOutputProgressVO), systemEnum);
                String judgeType = SocketIOEventConst.judgeType(jobType);
                this.importOutputProgressVO.setTotalProgress(getBatchJobTotalCount());
                if (tenantId != null) {
                    socketIOService.sendSocketIOMsg(currentUserId, judgeType, new ImportOutputProgressMsg(this.importOutputProgressVO), tenantId);
                }else {
                    socketIOService.sendSocketIOMsg(currentUserId, judgeType, new ImportOutputProgressMsg(this.importOutputProgressVO));
                }
            } catch (Exception e) {
                log.error("发送mq出错", e);
            }
        }

        writeBackCount();
    }

    private Integer getBatchJobTotalCount() {
        String countStr = redisOpsService.get(RedisKeyCenter.getBatchJobCountKey(this.importOutputProgressVO.getJobInstanceId()));
        if (StringUtils.isEmpty(countStr)){
            return 0;
        }
        return Integer.parseInt(countStr);
    }

    protected void writeBackCount() {
        log.info("writeBackCount开始更新数量");
        if (!stepExecution.getJobExecution().getJobInstance().getJobName().equals("EXPORT_FAILED_TENANT_ID")) {
            springBatchJobLogService.updateSuccessAndFailureCounts(importOutputProgressVO.getSuccessProgress(), importOutputProgressVO.getFailProgress(), null, importOutputProgressVO.getJobInstanceId());
        }
    }
}
