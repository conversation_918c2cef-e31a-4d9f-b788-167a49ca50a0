package com.yiwise.core.batch.excelimport.writer;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.yiwise.base.common.utils.MyThreadUtils;
import com.yiwise.base.model.enums.SystemEnum;
import com.yiwise.calloutjob.api.vo.CallOutRecordSimpleVO;
import com.yiwise.core.batch.common.BatchConstant;
import com.yiwise.core.batch.excelimport.entity.ComplaintHistoryImportVO;
import com.yiwise.core.batch.listener.stepexecution.ComExceptionHandler;
import com.yiwise.core.dal.entity.ComplaintHistoryPO;
import com.yiwise.core.dal.entity.PhoneNumberSupplierPO;
import com.yiwise.core.dal.entity.opensips.GatewayIndustryPO;
import com.yiwise.core.feignclient.callout.CallRecordClient;
import com.yiwise.core.model.enums.ComplaintTypeEnum;
import com.yiwise.core.model.enums.callrecord.CallRecordTypeEnum;
import com.yiwise.core.model.vo.ope.CallRecordVO;
import com.yiwise.core.service.ope.platform.CallRecordOpeService;
import com.yiwise.core.service.ope.platform.ComplaintHistoryService;
import com.yiwise.core.service.ope.platform.PhoneNumberSupplierService;
import com.yiwise.core.service.opensips.GatewayIndustryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.AfterStep;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.yiwise.core.batch.common.BatchConstant.Execution.CONFLICT_COUNT;

/**
 * @author: <EMAIL>
 * @date: 2024 06 21 15:50
 */
@Slf4j
@Component
@StepScope
public class ComplaintHistoryImportWriter implements ItemWriter<ComplaintHistoryImportVO> {

    @Resource
    private ComplaintHistoryService complaintHistoryService;
    @Resource
    private ComExceptionHandler comExceptionHandler;
    @Resource
    private CallRecordOpeService callRecordOpeService;
    @Resource
    private GatewayIndustryService gatewayIndustryService;
    @Resource
    private PhoneNumberSupplierService phoneNumberSupplierService;
    @Resource
    private CallRecordClient callRecordClient;

    public ExecutionContext executionContext;

    private Integer failCount = 0;

    @Value("#{jobParameters['userId']}")
    private Long userId;
    @Value("#{jobParameters['tenantId']}")
    private Long tenantId;

    @Override
    public void write(List<? extends ComplaintHistoryImportVO> items) throws Exception {

        log.info("userId={} tenantId={}", userId, tenantId);

        if (CollectionUtils.isNotEmpty(items)) {

            List<ComplaintHistoryImportVO> faultList = Lists.newArrayList();

            for (ComplaintHistoryImportVO item : items) {

                log.info("本次遍历={}", item);

                try {
                    if (StringUtils.isEmpty(item.getCallRecordId())) {
                        log.error("通话记录id必填项为空");
                        item.setErrorMessage("通话记录id为空");
                        faultList.add(item);
                        continue;
                    }

                    if (StringUtils.isEmpty(item.getGatewayIndustry())) {
                        log.error("网关行业必填项为空");
                        item.setErrorMessage("网关行业为空");
                        faultList.add(item);
                        continue;
                    }

                    if (StringUtils.isEmpty(item.getComplaintType())) {
                        log.error("投诉类型必填项为空");
                        item.setErrorMessage("投诉类型为空");
                        faultList.add(item);
                        continue;
                    }

                    if (StringUtils.isEmpty(item.getPhoneNumberSupplierId())) {
                        log.error("投诉线路商来源必填项为空");
                        item.setErrorMessage("投诉线路商来源为空");
                        faultList.add(item);
                        continue;
                    }
                    if (!NumberUtil.isNumber(item.getPhoneNumberSupplierId())) {
                        log.error("投诉线路商来源必须为数字");
                        item.setErrorMessage("投诉线路商来源必须为数字");
                        faultList.add(item);
                        continue;
                    }

                    ComplaintHistoryPO current = complaintHistoryService.selectByCallRecordId(Long.valueOf(item.getCallRecordId()));
                    if (Objects.nonNull(current)) {
                        log.error("已存在该通话记录的投诉记录");
                        item.setErrorMessage("已存在该通话记录的投诉记录");
                        faultList.add(item);
                        continue;
                    }

                    // 获取 callRecordId，避免每次调用重复转换
                    Long callRecordId = Long.valueOf(item.getCallRecordId());

                    CallRecordVO lastCallRecord = null;
                    CallRecordTypeEnum callRecordTypeEnum = null;

                    int retryCount = 3;
                    for (int attempt = 1; attempt <= retryCount; attempt++) {
                        try {
                            // 以新版外呼作为初始值
                            CallOutRecordSimpleVO simpleRecord = callRecordOpeService.getSimpleCallRecord(tenantId, callRecordId, userId);
                            lastCallRecord = new CallRecordVO();
                            lastCallRecord.setCallRecordId(callRecordId);
                            lastCallRecord.setStartTime(simpleRecord.getStartTime());
                            log.info("新版外呼查询到={}, 尝试次数={}", lastCallRecord, attempt);
                            callRecordTypeEnum = CallRecordTypeEnum.CALL_OUT_JOB;
                            break; // 成功获取数据，跳出循环
                        } catch (Exception e) {
                            log.warn("尝试第{}次查询新版外呼失败: {}", attempt, e.getMessage());
                            if (attempt == retryCount) {
                                log.error("重试{}次后仍然失败，放弃查询", retryCount);
                            }
                        }
                    }


                    // 查询 MA 记录
                    try {
                        CallRecordVO maRecord = callRecordOpeService.getCallRecordInfo(tenantId, callRecordId, userId, CallRecordTypeEnum.MA);
                        if (maRecord != null && (lastCallRecord == null || maRecord.getStartTime().isAfter(lastCallRecord.getStartTime()))) {
                            lastCallRecord = maRecord;
                            callRecordTypeEnum = CallRecordTypeEnum.MA;
                        }
                    } catch (Exception e) {
                        log.warn("{}查询ma报错error: {}", callRecordId, e.getMessage());
                    }

                    // 查询旧版外呼记录
                    try {
                        CallRecordVO oldCallRecord = callRecordOpeService.getCallRecordInfo(tenantId, callRecordId, userId, CallRecordTypeEnum.NORMAL);
                        if (oldCallRecord != null && (lastCallRecord == null || oldCallRecord.getStartTime().isAfter(lastCallRecord.getStartTime()))) {
                            lastCallRecord = oldCallRecord;
                            callRecordTypeEnum = CallRecordTypeEnum.NORMAL;
                        }
                    } catch (Exception e) {
                        log.warn("{}查询旧版外呼报错error: {}", callRecordId, e.getMessage());
                    }

                    if (Objects.isNull(lastCallRecord)) {
                        log.error("最终未查询到对应通话记录");
                        item.setErrorMessage("未查询到对应通话记录");
                        faultList.add(item);
                        continue;
                    }

                    GatewayIndustryPO gatewayIndustryPO = gatewayIndustryService.selectByDesc(item.getGatewayIndustry());
                    if (Objects.isNull(gatewayIndustryPO)) {
                        log.error("网关行业错误");
                        item.setErrorMessage("网关行业错误");
                        faultList.add(item);
                        continue;
                    } else {
                        gatewayIndustryService.updateCanDelFlag(gatewayIndustryPO.getGatewayIndustryId());
                    }

                    ComplaintTypeEnum complaintTypeEnum = ComplaintTypeEnum.getByDesc(item.getComplaintType());
                    if (Objects.isNull(complaintTypeEnum)) {
                        log.error("投诉类型错误");
                        item.setErrorMessage("投诉类型错误");
                        faultList.add(item);
                        continue;
                    }

                    PhoneNumberSupplierPO phoneNumberSupplierPO = phoneNumberSupplierService.selectByOrderId(Long.valueOf(item.getPhoneNumberSupplierId())).stream().findFirst().orElse(null);
                    if (Objects.isNull(phoneNumberSupplierPO)) {
                        log.error("线路商来源错误");
                        item.setErrorMessage("线路商来源错误");
                        faultList.add(item);
                        continue;
                    }

                    if (Objects.isNull(tenantId)) {
                        log.error("tenantId为空");
                        item.setErrorMessage("tenantId为空");
                        faultList.add(item);
                        continue;
                    }

                    ComplaintHistoryPO complaintHistoryPO = new ComplaintHistoryPO();
                    complaintHistoryPO.setCallRecordId(Long.valueOf(item.getCallRecordId()));
                    complaintHistoryPO.setStartTime(lastCallRecord.getStartTime());
                    complaintHistoryPO.setPhoneNumber(lastCallRecord.getPhoneNumber());
                    complaintHistoryPO.setPhoneLocationId(lastCallRecord.getPhoneLocationId());
                    complaintHistoryPO.setCallingLocationId(lastCallRecord.getCallingLocationId());
                    complaintHistoryPO.setTenantId(tenantId);
                    complaintHistoryPO.setComplaintType(complaintTypeEnum);
                    complaintHistoryPO.setPhoneNumberSupplierId(phoneNumberSupplierPO.getPhoneNumberSupplierId());
                    complaintHistoryPO.setContent(item.getContent());
                    complaintHistoryPO.setCallRecordType(callRecordTypeEnum);
                    complaintHistoryPO.setGatewayIndustry(gatewayIndustryPO.getEnumStr());

                    complaintHistoryService.addComplaintHistory(complaintHistoryPO, userId);

                    assert callRecordTypeEnum != null;
                    if (callRecordTypeEnum.equals(CallRecordTypeEnum.CALL_OUT_JOB)) {
                        MyThreadUtils.sleepMilliseconds(1000);
                    }
                } catch (Exception e) {
                    log.error("读取数据报错", e);
                }

            }
            log.info("准备写入错误条数={}", faultList.size());
            failCount = faultList.size();
            comExceptionHandler.write(0, BatchConstant.COMPLAINT_HISTORY_IMPORT_HEADER, BatchConstant.SheetName.COMPLAINT_HISTORY_SHEETNAME, faultList);
        }
    }

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        executionContext = stepExecution.getExecutionContext();
    }

    @AfterStep
    public void afterStep(StepExecution stepExecution) {
        log.info("failCount={}", failCount);
        executionContext.putInt(CONFLICT_COUNT, failCount);
        log.info("ComplaintHistoryImportWriter#afterStep={} failCount={}", executionContext, failCount);
    }

}
