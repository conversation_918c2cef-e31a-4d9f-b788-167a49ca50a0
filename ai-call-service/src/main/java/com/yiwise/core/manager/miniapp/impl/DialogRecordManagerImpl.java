package com.yiwise.core.manager.miniapp.impl;


import com.yiwise.base.common.audio.AudioHandleUtils;
import com.yiwise.base.common.audio.WaveHeader;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.base.common.utils.file.MyFileUtils;
import com.yiwise.base.common.utils.string.MyStringUtils;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.base.common.utils.collection.MyCollectionUtils;
import com.yiwise.core.dal.mongo.knowledgebase.KnowledgeBasePO;
import com.yiwise.core.helper.objectstorage.AddOssPrefixSerializer;
import com.yiwise.core.manager.miniapp.DialogRecordManager;
import com.yiwise.core.manager.miniapp.TextAudioManager;
import com.yiwise.core.model.bo.record.*;
import com.yiwise.core.model.dialogflow.dto.RecordCountDTO;
import com.yiwise.core.model.dialogflow.dto.RecordUploadDTO;
import com.yiwise.core.model.dialogflow.entity.*;
import com.yiwise.core.model.dialogflow.tree.HierarchyInfo;
import com.yiwise.core.model.enums.dialogflow.*;
import com.yiwise.core.model.miniapp.dto.*;
import com.yiwise.core.model.miniapp.vo.*;
import com.yiwise.core.model.nlp.NlpAudioPO;
import com.yiwise.core.model.vo.dialogrecord.RobotKnowledgeModifyVO;
import com.yiwise.core.service.OssKeyCenter;
import com.yiwise.core.service.TempFilePathKeyCenter;
import com.yiwise.core.service.dialogflow.DialogFlowConfigService;
import com.yiwise.core.service.dialogflow.DialogFlowRobotKnowledgeService;
import com.yiwise.core.service.dialogflow.DialogFlowService;
import com.yiwise.core.service.dialogflow.DialogFlowStepService;
import com.yiwise.core.service.dialogflow.impl.DialogFlowUploadServiceImpl;
import com.yiwise.core.service.miniapp.platform.impl.AudioHandleService;
import com.yiwise.core.service.miniapp.platform.impl.DialogRecordService;
import com.yiwise.core.service.nlp.NlpAudioService;
import com.yiwise.middleware.objectstorage.common.ObjectStorageHelper;
import com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.yiwise.core.config.ApplicationConstant.WAV_TO_PCM_HEAD_LEN;


/**
 * 话术录音管理
 *
 * <AUTHOR>
 * @date 2018-12-06 11:12
 */
@Component
public class DialogRecordManagerImpl implements DialogRecordManager {

    @Resource
    private DialogFlowService dialogFlowService;
    @Resource
    private DialogFlowStepService dialogFlowStepService;
    @Resource
    private DialogFlowConfigService dialogFlowConfigService;
    @Resource
    private DialogFlowRobotKnowledgeService robotKnowledgeService;
    @Resource
    private NlpAudioService nlpAudioService;
    @Resource
    private TextAudioManager textAudioManager;
    @Resource
    private ObjectStorageHelper objectStorageHelper;
    @Lazy
    @Resource
    private DialogRecordService dialogRecordService;
    @Resource
    private AudioHandleService audioHandleService;


    private final static String HANG_UP_RECORD_TITLE = "特殊回答配置";
    private final static String SERVICE_TRIGGER_HANG_UP_HIERARCHY = HierarchyInfo.getHierarchyStr(0, 0, 0);
    private final static String NEGATIVE_TRIGGER_HANG_UP_HIERARCHY = HierarchyInfo.getHierarchyStr(0, 1, 0);

    private final static Logger logger = LoggerFactory.getLogger(com.yiwise.core.manager.miniapp.DialogRecordManager.class);

    @Override
    public List<RecordNodeDTO> getDialogFlowRecordNodeList(Long dialogFlowId) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        if (dialogFlowId == null) {
            return recordNodeDTOS;
        }

        // 获取所有 DialogFlowStep
        List<DialogFlowStepPO> dialogFlowSteps = dialogFlowStepService.findDialogFlowSteps(dialogFlowId);
        dialogFlowSteps.forEach(dialogFlowStep -> recordNodeDTOS.addAll(getDialogFlowStepRecordNodeList(dialogFlowStep)));

        // 获取所有挂机话术
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
        recordNodeDTOS.addAll(getHangUpRecordNodeList(dialogFlowConfig));

        // 获取所有知识库
        com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum dialogVoiceType = dialogFlowService.selectDialogFlowVoiceType(dialogFlowId);
        List<RobotKnowledgePO> robotKnowledges = robotKnowledgeService.findRobotKnowledge(dialogFlowId);
        robotKnowledges.forEach(robotKnowledge -> recordNodeDTOS.addAll(getRobotKnowledgeRecordNodeList(robotKnowledge, dialogVoiceType)));
        return recordNodeDTOS;
    }


    @Override
    public DialogFlowRecordBO getDialogFlowRecordItem(Long dialogFlowId) {

        DialogFlowRecordBO dialogFlowRecordBO = new DialogFlowRecordBO();
        if (dialogFlowId == null) {
            return dialogFlowRecordBO;
        }
        DialogFlowInfoPO dialogFlowInfoPO = dialogFlowService.selectByKey(dialogFlowId);
        dialogFlowRecordBO.setDialogFlowId(dialogFlowId);
        dialogFlowRecordBO.setName(dialogFlowInfoPO.getName());

        // 获取所有 DialogFlowStep
        List<DialogFlowStepPO> dialogFlowSteps = dialogFlowStepService.findDialogFlowSteps(dialogFlowId);
        dialogFlowSteps.forEach(dialogFlowStep -> resolveDialogFlowStepRecordItemList(dialogFlowStep, dialogFlowRecordBO));

        // 获取所有挂机话术
        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId);
        DialogFlowHangUpRecordBO hangUpRecordItemList = getHangUpRecordItemList(dialogFlowConfig, null);
        dialogFlowRecordBO.setDialogFlowConfig(hangUpRecordItemList);

        // 获取所有知识库
        List<RobotKnowledgePO> robotKnowledges = robotKnowledgeService.findRobotKnowledge(dialogFlowId);
        robotKnowledges.forEach(robotKnowledge -> dialogFlowRecordBO.addRobotKnowledge(getRobotKnowledgeRecordItemList(robotKnowledge)));

        return dialogFlowRecordBO;
    }

    @Override
    public List<RecordNodeDTO> getDialogFlowStepRecordNodeList(DialogFlowStepPO dialogFlowStep) {
        com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum dialogVoiceTypeEnum=dialogFlowService.selectDialogFlowVoiceType(dialogFlowStep.getDialogFlowId());
        return getDialogFlowStepRecordNodeList(dialogFlowStep, dialogVoiceTypeEnum);
    }

    @Override
    public List<RecordNodeDTO> getDialogFlowStepRecordNodeList(DialogFlowStepPO dialogFlowStep, com.yiwise.middleware.tts.enums.DialogVoiceTypeEnum dialogVoiceTypeEnum) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        if (dialogFlowStep == null) {
            return recordNodeDTOS;
        }

        // 获取基本信息
        RecordTypeEnum type = dialogFlowStep.getType().toRecordTypeEnum();
        Integer version = dialogFlowStep.getVersionId();
        RecordParentMetaDTO parentMeta = getDialogFlowStepMetaInfo(dialogFlowStep);

        // 遍历录音流程树状结构
        Map<String, TextAudioContentPO> dialogStepTreeMap = dialogFlowStepService.getDialogStepTreeInfo(dialogFlowStep);
        //获取类型
        for (String hierarchy : dialogStepTreeMap.keySet()) {
            // 解析录音文本节点
            TextAudioContentPO audio = dialogStepTreeMap.get(hierarchy);
            List<RecordContentExtendDTO> recordContentExtendDTOS = textAudioManager.textAudioParse(audio,dialogVoiceTypeEnum);
            recordContentExtendDTOS.forEach(item -> item.setHierarchy(hierarchy));

            // 组合录音节点数据
            for (RecordContentExtendDTO extend : recordContentExtendDTOS) {
                RecordMetaDTO recordMeta = new RecordMetaDTO(version, extend.getHierarchy(), extend.getPart());
                RecordNodeDTO recordNodeDTO = new RecordNodeDTO(type, parentMeta, recordMeta, extend, extend.getStatus());
                recordNodeDTO.setOriginTextAudio(audio);
                recordNodeDTOS.add(recordNodeDTO);
            }
        }

        return recordNodeDTOS;
    }

    /**
     * 一个step的所有录音文本
     * @param dialogFlowStep
     * @return
     */
    @Override
    public void resolveDialogFlowStepRecordItemList(DialogFlowStepPO dialogFlowStep, DialogFlowRecordBO dialogFlowRecordBO) {
        // 遍历录音流程树状结构
        List<RecordItemBO> recordContents = new ArrayList<>();
        List<DialogFlowNodePO> dialogStepNodeList = dialogFlowStepService.getDialogStepNodeList(dialogFlowStep);
        //获取类型
        DialogVoiceTypeEnum dialogVoiceTypeEnum=dialogFlowService.selectDialogFlowVoiceType(dialogFlowStep.getDialogFlowId());
        for (DialogFlowNodePO nodePO : dialogStepNodeList) {
            // 每个node可能有多个录音文本
            List<TextAudioContentPO> textAudioContentList = nodePO.getTextAudioContentList();
            for (TextAudioContentPO textAudioContentPO : textAudioContentList) {
                // 每个文本可能根据变量分割成多个录音
                List<RecordItemBO> recordItemBOS = textAudioManager.recordItemParse(textAudioContentPO, dialogVoiceTypeEnum);
                recordItemBOS.forEach(item -> item.setTitle(nodePO.getName()));
                recordContents.addAll(recordItemBOS);
            }
        }
        DialogFlowStepRecordBO dialogFlowStepRecordBO = new DialogFlowStepRecordBO();
        dialogFlowStepRecordBO.setTitle(dialogFlowStep.getName());
        dialogFlowStepRecordBO.setDialogFlowNodes(recordContents);
        if (DialogFlowStepTypeEnum.MAIN_DIALOG_FLOW.equals(dialogFlowStep.getType())) {
            dialogFlowRecordBO.addDialogFlowStep(dialogFlowStepRecordBO);
        } else {
            dialogFlowRecordBO.addKnowledgeStep(dialogFlowStepRecordBO);
        }
    }

    @Override
    public List<RecordNodeDTO> getHangUpRecordNodeList(DialogFlowConfigurationPO dialogFlowConfig) {
        return getHangUpRecordNodeList(dialogFlowConfig, null);
    }

    @Override
    public List<RecordNodeDTO> getHangUpRecordNodeList(DialogFlowConfigurationPO dialogFlowConfig, RecordTypeEnum recordType) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        if (dialogFlowConfig == null) {
            return recordNodeDTOS;
        }
        //获取类型
        DialogVoiceTypeEnum dialogVoiceTypeEnum=dialogFlowService.selectDialogFlowVoiceType(dialogFlowConfig.getDialogFlowId());
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_BUSINESS.equals(recordType)) {
            // 基本信息
            RecordParentMetaDTO businessParentMetaDTO = new RecordParentMetaDTO();
            businessParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
            businessParentMetaDTO.setParentTitle("触发多个业务问题");

            List<DialogFlowBusinessQAConfigRulePO> dialogFlowBusinessQAConfigRuleList = dialogFlowConfig.getDialogFlowBusinessQAConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowBusinessQAConfigRuleList)) {
                int idx = 0;
                for (DialogFlowBusinessQAConfigRulePO dialogFlowBusinessQAConfigRulePO : dialogFlowBusinessQAConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowBusinessQAConfigRulePO.getAnswerType())) {
                        int finalIdx = idx;
                        textAudioManager.textAudioParse(dialogFlowBusinessQAConfigRulePO.getTextAudioContent(),dialogVoiceTypeEnum).forEach(extend -> {
                            RecordMetaDTO recordMeta = new RecordMetaDTO(null, HierarchyInfo.getHierarchyStr(0, finalIdx, 0), extend.getPart());
                            RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.CONFIG_MULTI_BUSINESS, businessParentMetaDTO, recordMeta, extend, extend.getStatus());
                            recordNodeDTO.setOriginTextAudio(dialogFlowBusinessQAConfigRulePO.getTextAudioContent());
                            recordNodeDTOS.add(recordNodeDTO);
                        });
                        idx++;
                    }
                }
            }
        }
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_REJECT.equals(recordType)) {
            RecordParentMetaDTO declineParentMetaDTO = new RecordParentMetaDTO();
            declineParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
            declineParentMetaDTO.setParentTitle("触发多次拒绝");

            List<DialogFlowDeclineConfigRulePO> dialogFlowDeclineConfigRuleList = dialogFlowConfig.getDialogFlowDeclineConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowDeclineConfigRuleList)) {
                int idx = 0;
                for (DialogFlowDeclineConfigRulePO dialogFlowDeclineConfigRulePO : dialogFlowDeclineConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowDeclineConfigRulePO.getAnswerType())) {
                        int finalIdx = idx;
                        textAudioManager.textAudioParse(dialogFlowDeclineConfigRulePO.getTextAudioContent(),dialogVoiceTypeEnum).forEach(extend -> {
                            RecordMetaDTO recordMeta = new RecordMetaDTO(null, HierarchyInfo.getHierarchyStr(0, finalIdx, 0), extend.getPart());
                            RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.CONFIG_MULTI_REJECT, declineParentMetaDTO, recordMeta, extend, extend.getStatus());
                            recordNodeDTOS.add(recordNodeDTO);
                        });
                        idx++;
                    }
                }
            }
        }
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_USER_SAY.equals(recordType)) {
            RecordParentMetaDTO userSayParentMetaDTO = new RecordParentMetaDTO();
            userSayParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
            userSayParentMetaDTO.setParentTitle("客户单句时长");

            List<DialogFlowUserSayConfigRulePO> dialogFlowUserSayConfigRuleList = dialogFlowConfig.getDialogFlowUserSayConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowUserSayConfigRuleList)) {
                int idx = 0;
                for (DialogFlowUserSayConfigRulePO dialogFlowUserSayConfigRulePO : dialogFlowUserSayConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowUserSayConfigRulePO.getAnswerType())) {
                        int finalIdx = idx;
                        textAudioManager.textAudioParse(dialogFlowUserSayConfigRulePO.getTextAudioContent(),dialogVoiceTypeEnum).forEach(extend -> {
                            RecordMetaDTO recordMeta = new RecordMetaDTO(null, HierarchyInfo.getHierarchyStr(0, finalIdx, 0), extend.getPart());
                            RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.CONFIG_MULTI_USER_SAY, userSayParentMetaDTO, recordMeta, extend, extend.getStatus());
                            recordNodeDTOS.add(recordNodeDTO);
                        });
                        idx++;
                    }
                }
            }
        }
        if(recordType == null || RecordTypeEnum.CALL_TIMEOUT.equals(recordType)) {
            RecordParentMetaDTO userSayParentMetaDTO = new RecordParentMetaDTO();
            userSayParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
            userSayParentMetaDTO.setParentTitle("通话时长限制");
            if (BooleanUtils.isTrue(dialogFlowConfig.getEnableCallTimeout())) {
                int finalIdx = 0;
                textAudioManager.textAudioParse(dialogFlowConfig.getCallTimeoutHangupAnswer(),dialogVoiceTypeEnum).forEach(extend -> {
                    RecordMetaDTO recordMeta = new RecordMetaDTO(null, HierarchyInfo.getHierarchyStr(0, finalIdx, 0), extend.getPart());
                    RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.CALL_TIMEOUT, userSayParentMetaDTO, recordMeta, extend, extend.getStatus());
                    recordNodeDTOS.add(recordNodeDTO);
                });
            }
        }
        if(recordType == null || RecordTypeEnum.MUTE_TIMEOUT.equals(recordType)) {
            RecordParentMetaDTO userSayParentMetaDTO = new RecordParentMetaDTO();
            userSayParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
            userSayParentMetaDTO.setParentTitle("静音时长限制");
            if (BooleanUtils.isTrue(dialogFlowConfig.getEnableContinuousMuteTimeout())) {
                int finalIdx = 0;
                textAudioManager.textAudioParse(dialogFlowConfig.getContinuousMuteHangupAnswer(),dialogVoiceTypeEnum).forEach(extend -> {
                    RecordMetaDTO recordMeta = new RecordMetaDTO(null, HierarchyInfo.getHierarchyStr(0, finalIdx, 0), extend.getPart());
                    RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.MUTE_TIMEOUT, userSayParentMetaDTO, recordMeta, extend, extend.getStatus());
                    recordNodeDTOS.add(recordNodeDTO);
                });
            }
        }
        return recordNodeDTOS;
    }

    public DialogFlowHangUpRecordBO getHangUpRecordItemList(DialogFlowConfigurationPO dialogFlowConfig, RecordTypeEnum recordType) {
        DialogFlowHangUpRecordBO dialogFlowHangUpRecordBO = new DialogFlowHangUpRecordBO();
        if (dialogFlowConfig == null) {
            return dialogFlowHangUpRecordBO;
        }
        //获取类型
        DialogVoiceTypeEnum dialogVoiceTypeEnum=dialogFlowService.selectDialogFlowVoiceType(dialogFlowConfig.getDialogFlowId());
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_BUSINESS.equals(recordType)) {
            // 基本信息

            List<DialogFlowBusinessQAConfigRulePO> dialogFlowBusinessQAConfigRuleList = dialogFlowConfig.getDialogFlowBusinessQAConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowBusinessQAConfigRuleList)) {
                List<RecordItemBO> recordNodeDTOS = new ArrayList<>();
                for (DialogFlowBusinessQAConfigRulePO dialogFlowBusinessQAConfigRulePO : dialogFlowBusinessQAConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowBusinessQAConfigRulePO.getAnswerType())) {
                        List<RecordItemBO> recordItemBOS = textAudioManager.recordItemParse(dialogFlowBusinessQAConfigRulePO.getTextAudioContent(), dialogVoiceTypeEnum);
//                        recordItemBOS.forEach(item -> item.setTitle("触发多个业务问题"));
                        recordNodeDTOS.addAll(recordItemBOS);
                    }
                }
                dialogFlowHangUpRecordBO.setDialogFlowBusinessQAConfigs(recordNodeDTOS);
            }
        }
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_REJECT.equals(recordType)) {

            List<DialogFlowDeclineConfigRulePO> dialogFlowDeclineConfigRuleList = dialogFlowConfig.getDialogFlowDeclineConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowDeclineConfigRuleList)) {
                List<RecordItemBO> recordNodeDTOS = new ArrayList<>();
                for (DialogFlowDeclineConfigRulePO dialogFlowDeclineConfigRulePO : dialogFlowDeclineConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowDeclineConfigRulePO.getAnswerType())) {
                        List<RecordItemBO> recordItemBOS = textAudioManager.recordItemParse(dialogFlowDeclineConfigRulePO.getTextAudioContent(), dialogVoiceTypeEnum);
//                        recordItemBOS.forEach(item -> item.setTitle("触发多次拒绝"));
                        recordNodeDTOS.addAll(recordItemBOS);
                    }
                }
                dialogFlowHangUpRecordBO.setDialogFlowDeclineConfigs(recordNodeDTOS);
            }
        }
        if(recordType == null || RecordTypeEnum.CONFIG_MULTI_USER_SAY.equals(recordType)) {

            List<DialogFlowUserSayConfigRulePO> dialogFlowUserSayConfigRuleList = dialogFlowConfig.getDialogFlowUserSayConfigRuleList();
            if (CollectionUtils.isNotEmpty(dialogFlowUserSayConfigRuleList)) {
                List<RecordItemBO> recordNodeDTOS = new ArrayList<>();
                for (DialogFlowUserSayConfigRulePO dialogFlowUserSayConfigRulePO : dialogFlowUserSayConfigRuleList) {
                    if (RobotKnowledgeAnswerTypeEnum.DIRECT_ANSWER.equals(dialogFlowUserSayConfigRulePO.getAnswerType())) {
                        List<RecordItemBO> recordItemBOS = textAudioManager.recordItemParse(dialogFlowUserSayConfigRulePO.getTextAudioContent(), dialogVoiceTypeEnum);
//                        recordItemBOS.forEach(item -> item.setTitle("客户单句时长"));
                        recordNodeDTOS.addAll(recordItemBOS);
                    }
                }
                dialogFlowHangUpRecordBO.setDialogFlowUserSayConfigs(recordNodeDTOS);
            }
        }
        return dialogFlowHangUpRecordBO;
    }

    @Override
    public List<RecordNodeDTO> getRobotKnowledgeRecordNodeList(RobotKnowledgePO robotKnowledge) {
        DialogVoiceTypeEnum dialogVoiceTypeEnum=dialogFlowService.selectDialogFlowVoiceType(robotKnowledge.getDialogFlowId());
        return getRobotKnowledgeRecordNodeList(robotKnowledge, dialogVoiceTypeEnum);
    }

    @Override
    public List<RecordNodeDTO> getRobotKnowledgeRecordNodeList(RobotKnowledgePO robotKnowledge, DialogVoiceTypeEnum dialogVoiceTypeEnum) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        if (robotKnowledge == null) {
            return recordNodeDTOS;
        }

        // 基本信息
        RecordParentMetaDTO parentMeta = getRobotKnowledgeMetaInfo(robotKnowledge);

        // 解析录音节点
        List<? extends TextAudioContentPO> audios = robotKnowledge.getRobotKnowledgeAnswers();
        return resolveKnowledgeTextAudioContent(audios, parentMeta, dialogVoiceTypeEnum);
    }

    public RobotKnowledgeRecordBO getRobotKnowledgeRecordItemList(RobotKnowledgePO robotKnowledge) {
        RobotKnowledgeRecordBO robotKnowledgeRecordBO = new RobotKnowledgeRecordBO();
        if (robotKnowledge == null) {
            return robotKnowledgeRecordBO;
        }


        // 解析录音节点
        List<? extends TextAudioContentPO> audios = robotKnowledge.getRobotKnowledgeAnswers();
        List<RecordItemBO> recordItemBOS = resolveKnowledgeTextAudioContentItem(audios);
//        recordItemBOS.forEach(item -> item.setTitle(robotKnowledge.getTitle()));
        robotKnowledgeRecordBO.setTitle(robotKnowledge.getTitle());
        robotKnowledgeRecordBO.setAnswers(recordItemBOS);

        return robotKnowledgeRecordBO;
    }

    @Override
    public List<RecordNodeDTO> getKnowledgeBaseRecordNodeList(KnowledgeBasePO robotKnowledge) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        if (robotKnowledge == null) {
            return recordNodeDTOS;
        }

        // 基本信息
        RecordParentMetaDTO parentMeta = new RecordParentMetaDTO();
        parentMeta.setParentId(robotKnowledge.getKnowledgeBaseId());
        parentMeta.setParentTitle(robotKnowledge.getTitle());

        // 解析录音节点
        List<RobotKnowledgeTextAudioContentPO> audios = robotKnowledge.getRobotKnowledgeAnswers();
        return resolveKnowledgeTextAudioContent(audios, parentMeta, DialogVoiceTypeEnum.MAN_MADE);
    }

    private List<RecordNodeDTO> resolveKnowledgeTextAudioContent(List<? extends TextAudioContentPO> audios, RecordParentMetaDTO parentMeta, DialogVoiceTypeEnum dialogVoiceType) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();
        for (int idx = 0; idx < audios.size(); idx++) {
            TextAudioContentPO audio = audios.get(idx);
            List<RecordContentExtendDTO> recordContents = textAudioManager.textAudioParse(audio, dialogVoiceType);
            for (RecordContentExtendDTO extend : recordContents) {
                String hierarchy = HierarchyInfo.getHierarchyStr(0, idx, 0);
                RecordMetaDTO recordMeta = new RecordMetaDTO(null, hierarchy, extend.getPart());
                RecordNodeDTO recordNodeDTO = new RecordNodeDTO(RecordTypeEnum.ROBOT_KNOWLEDGE, parentMeta, recordMeta, extend, extend.getStatus());
                recordNodeDTO.setOriginTextAudio(audio);
                recordNodeDTOS.add(recordNodeDTO);
            }
        }
        return recordNodeDTOS;
    }

    private List<RecordItemBO> resolveKnowledgeTextAudioContentItem(List<? extends TextAudioContentPO> audios) {
        List<RecordItemBO> recordNodeDTOS = new ArrayList<>();
        for (int idx = 0; idx < audios.size(); idx++) {
            TextAudioContentPO audio = audios.get(idx);
            List<RecordItemBO> recordItemBOS = textAudioManager.recordItemParse(audio, DialogVoiceTypeEnum.MAN_MADE);
            recordNodeDTOS.addAll(recordItemBOS);
        }
        return recordNodeDTOS;
    }

    @Override
    public RecordCountDTO statDialogFlowRecordCount(DialogFlowInfoPO dialogFlowInfo) {
        RecordCountDTO recordCountDTO = new RecordCountDTO();

        // 话术Id
        Long dialogFlowId = dialogFlowInfo.getId();

        // 统计话术流程数量
        List<DialogFlowStepPO> dialogFlowStepList = dialogFlowStepService.findDialogFlowSteps(dialogFlowId);
        dialogFlowStepList.forEach(dialogFlowStep -> {
            recordCountDTO.incTotalRecordCount(dialogFlowStep.getTotalNodeCount());
            recordCountDTO.incHasRecordCount(dialogFlowStep.getRecordNodeCount());
        });

        // 统计话术配置数量
        List<RecordNodeDTO> dialogConfigNodeList = getHangUpRecordNodeList(dialogFlowConfigService.getDialogFlowConfiguration(dialogFlowId));
        RecordCountDTO dialogConfigCount = statDialogRecordNum(dialogConfigNodeList);
        recordCountDTO.incTotalRecordCount(dialogConfigCount.getTotalRecordCount());
        recordCountDTO.incHasRecordCount(dialogConfigCount.getHasRecordCount());

        // 统计知识库数量
        List<RecordNodeDTO> robotKnowledgeNodeList = new ArrayList<>();
        robotKnowledgeService.findRobotKnowledge(dialogFlowId).forEach(robotKnowledge -> robotKnowledgeNodeList.addAll(getRobotKnowledgeRecordNodeList(robotKnowledge)));
        RecordCountDTO robotKnowledgeCount = statDialogRecordNum(robotKnowledgeNodeList);
        recordCountDTO.incTotalRecordCount(robotKnowledgeCount.getTotalRecordCount());
        recordCountDTO.incHasRecordCount(robotKnowledgeCount.getHasRecordCount());

        return recordCountDTO;
    }

    @Override
    public Map<Long, RecordCountDTO> statDialogFlowListRecordCountMap(List<DialogFlowInfoPO> list) {
        List<Long> ids = new ArrayList<>();
        list.forEach(dialogFlowInfoPO -> ids.add(dialogFlowInfoPO.getId()));

        Map<Long, RecordCountDTO> map = new HashMap<>();
        list.forEach(dialogFlowInfoPO -> map.put(dialogFlowInfoPO.getId(), new RecordCountDTO()));
        //in 查询话术流程
        List<DialogFlowStepPO> dialogFlowStepList = dialogFlowStepService.findDialogFlowStepsByIds(ids);
        dialogFlowStepList.forEach(dialogFlowStep -> {
            RecordCountDTO recordCountDTO = map.get(dialogFlowStep.getDialogFlowId());
            recordCountDTO.incTotalRecordCount(dialogFlowStep.getTotalNodeCount());
            recordCountDTO.incHasRecordCount(dialogFlowStep.getRecordNodeCount());
            map.put(dialogFlowStep.getDialogFlowId(), recordCountDTO);
        });

        // 统计话术配置数量
        List<DialogFlowConfigurationPO> dialogFlowConfigurationList = dialogFlowConfigService.getDialogFlowConfigurationByIds(ids);
        dialogFlowConfigurationList.forEach(dialogConfig -> {
            RecordCountDTO recordCountDTO = map.get(dialogConfig.getDialogFlowId());
            List<RecordNodeDTO> dialogConfigNodeList = getHangUpRecordNodeList(dialogConfig);
            RecordCountDTO dialogConfigCount = statDialogRecordNum(dialogConfigNodeList);
            recordCountDTO.incTotalRecordCount(dialogConfigCount.getTotalRecordCount());
            recordCountDTO.incHasRecordCount(dialogConfigCount.getHasRecordCount());
            map.put(dialogConfig.getDialogFlowId(), recordCountDTO);
        });

        // 统计知识库数量
        List<RobotKnowledgePO> knowledgeList = robotKnowledgeService.findRobotKnowledgeByDialogFlowIds(ids);
        knowledgeList.forEach(robotKnowledgePO -> {
            RecordCountDTO recordCountDTO = map.get(robotKnowledgePO.getDialogFlowId());
            List<RecordNodeDTO> robotKnowledgeNodeList = getRobotKnowledgeRecordNodeList(robotKnowledgePO);
            RecordCountDTO robotKnowledgeCount = statDialogRecordNum(robotKnowledgeNodeList);
            recordCountDTO.incTotalRecordCount(robotKnowledgeCount.getTotalRecordCount());
            recordCountDTO.incHasRecordCount(robotKnowledgeCount.getHasRecordCount());
            map.put(robotKnowledgePO.getDialogFlowId(), recordCountDTO);
        });
        return map;
    }

    @Override
    public Map<Long, RecordCountDTO> statNlpDialogFlowListRecordCountMap(List<DialogFlowInfoPO> list) {
        Map<Long, RecordCountDTO> dialog2count = MyCollectionUtils.listToConvertMap(list, DialogFlowInfoPO::getId, ignore -> new RecordCountDTO());
        List<Long> dialogFlowIdList = list.stream().map(DialogFlowInfoPO::getId).collect(Collectors.toList());
        List<DialogFlowInfoPO> dialogFlowList = dialogFlowService.findAllByDialogFlowIds(new HashSet<>(dialogFlowIdList));
        Map<Long, DialogFlowInfoPO> dialogMap = new HashMap<>();
        dialogFlowList.stream()
                .filter(item -> {
                    return DialogVoiceTypeEnum.MAN_MADE.equals(item.getVoiceType());
                }).forEach(item -> {
                    dialogMap.put(item.getId(), item);
                });

        List<NlpAudioPO> audioList = nlpAudioService.queryRecordByDialogFlowIds(dialogMap.keySet());

        Map<Long, List<NlpAudioPO>> audioListMap = MyCollectionUtils.listToMapList(audioList, NlpAudioPO::getDialogFlowId);

        audioListMap.forEach((dialogFlowId, audios) -> {
            DialogFlowInfoPO dialogFlow = dialogMap.get(dialogFlowId);
            dialog2count.put(dialogFlowId, nlpAudioService.analysisAudioStats(dialogFlow, audios));
        });

        return dialog2count;
    }

    @Override
    public RecordCountDTO statDialogRecordNum(List<RecordNodeDTO> recordNodeDTOS) {
        RecordCountDTO recordCountDTO = new RecordCountDTO();
        if (CollectionUtils.isEmpty(recordNodeDTOS)) {
            return recordCountDTO;
        }

        int totalRecordCount = recordNodeDTOS.size();
        List<RecordNodeDTO> hasRecordNodeDTOS = recordNodeDTOS.stream().
                filter(item -> Boolean.TRUE.equals(item.getStatus())).
                collect(Collectors.toList());
        int hasRecordCount = hasRecordNodeDTOS.size();

        recordCountDTO.setTotalRecordCount(totalRecordCount);
        recordCountDTO.setHasRecordCount(hasRecordCount);

        return recordCountDTO;
    }

    @Override
    public List<MiniappRecordDialogItemVO> unionDialogDetail(List<RecordNodeDTO> recordNodeDTOS) {
        List<MiniappRecordDialogItemVO> recordDialogItemVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(recordNodeDTOS)) {
            return recordDialogItemVOS;
        }

        // 按类别分类
        Map<RecordTypeEnum, List<RecordNodeDTO>> typeGroupResult = recordNodeDTOS.stream().collect(Collectors.groupingBy(RecordNodeDTO::getRecordType));
        typeGroupResult.forEach((type, typeNodes) -> {
            // 基于每个 parentId 进行分类统计
            Map<RecordParentMetaDTO, List<RecordNodeDTO>> parentGroupResult = typeNodes.stream().collect(Collectors.groupingBy(RecordNodeDTO::getRecordParentMeta));
            List<RecordParentMetaDTO> recordParentMetaDTOS;
            if (RecordTypeEnum.DIALOG_FLOW_STEP.equals(type) || RecordTypeEnum.KNOWLEDGE_STEP.equals(type)) {
                recordParentMetaDTOS = parentGroupResult.keySet().stream().sorted(Comparator.comparing(RecordParentMetaDTO::getOrder)).collect(Collectors.toList());
            } else {
                recordParentMetaDTOS = new ArrayList<>(parentGroupResult.keySet());
            }
            recordParentMetaDTOS.forEach(parentMeta -> {
                List<RecordNodeDTO> nodes = parentGroupResult.get(parentMeta);
                RecordCountDTO recordCount = statDialogRecordNum(nodes);
                RecordNodeDTO recordNode = nodes.stream().findFirst().orElse(new RecordNodeDTO());
                MiniappRecordDialogItemVO recordDialogItemVO = new MiniappRecordDialogItemVO();
                recordDialogItemVO.setType(type);
                recordDialogItemVO.setId(parentMeta.getParentId());
                recordDialogItemVO.setTitle(recordNode.getRecordParentMeta().getParentTitle());
                recordDialogItemVO.setNoneRecordCount(recordCount.getNoneRecordCount());
                recordDialogItemVO.setTotalRecordCount(recordCount.getTotalRecordCount());
                recordDialogItemVO.setLabel(recordNode.getLabel());
                recordDialogItemVOS.add(recordDialogItemVO);
            });
        });

        return recordDialogItemVOS;
    }

    @Override
    public List<MiniappRecordDialogNoneRecordVO> unionNoneRecordNodeList(List<RecordNodeDTO> recordNodeDTOS) {
        List<MiniappRecordDialogNoneRecordVO> dialogNoneRecordVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(recordNodeDTOS)) {
            return dialogNoneRecordVOS;
        }

        // 过滤出未录音列表
        List<RecordNodeDTO> noneRecordNodeDTOS = recordNodeDTOS.stream()
                .filter(recordNode -> Boolean.FALSE.equals(recordNode.getStatus()))
                .sorted(Comparator.comparing(RecordNodeDTO::getRecordType))
                .collect(Collectors.toList());

        // 设置未录音信息
        noneRecordNodeDTOS.forEach(noneRecordNode -> {
            // 获取元数据信息
            RecordParentMetaDTO parentMeta = noneRecordNode.getRecordParentMeta();
            RecordMetaDTO recordMeta = noneRecordNode.getRecordMeta();
            // 设置信息
            MiniappRecordDialogNoneRecordVO dialogNoneRecordVO = new MiniappRecordDialogNoneRecordVO();
            dialogNoneRecordVO.setId(parentMeta.getParentId());
            dialogNoneRecordVO.setVersion(recordMeta.getVersion());
            dialogNoneRecordVO.setType(noneRecordNode.getRecordType());
            dialogNoneRecordVO.setHierarchy(recordMeta.getHierarchy());
            dialogNoneRecordVO.setPart(recordMeta.getPart());
            dialogNoneRecordVOS.add(dialogNoneRecordVO);
        });

        return dialogNoneRecordVOS;
    }

    @Override
    public List<MiniappRecordDialogItemDetailVO> unionRecordItemList(List<RecordNodeDTO> recordNodeDTOS) {
        return unionRecordItemList(recordNodeDTOS, null);
    }

    @Override
    public List<MiniappRecordDialogItemDetailVO> unionRecordItemList(List<RecordNodeDTO> recordNodeDTOS, DialogFlowStepPO dialogFlowStepPO) {
        List<MiniappRecordDialogItemDetailVO> recordItemList = new ArrayList<>();
        if (CollectionUtils.isEmpty(recordNodeDTOS)) {
            return recordItemList;
        }

        Map<String, String> hierarchy2NodeName = dialogFlowStepService.getDialogStepTreeNodeName(dialogFlowStepPO);

        recordNodeDTOS.forEach(recordNode -> {
            // 获取数据信息
            RecordParentMetaDTO parentMeta = recordNode.getRecordParentMeta();
            RecordMetaDTO recordMeta = recordNode.getRecordMeta();
            RecordContentDTO recordContent = recordNode.getRecordContent();
            // 设置信息
            MiniappRecordDialogItemDetailVO recordItem = new MiniappRecordDialogItemDetailVO();
            recordItem.setId(parentMeta.getParentId());
            recordItem.setVersion(recordMeta.getVersion());
            recordItem.setType(recordNode.getRecordType());
            recordItem.setStatus(recordNode.getStatus());
            recordItem.setHierarchy(recordMeta.getHierarchy());
            recordItem.setPart(recordMeta.getPart());
            recordItem.setText(recordContent.getAudioText());
            recordItem.setAudioUrl(AddOssPrefixSerializer.getAddOssPrefixUrl(recordContent.getAudioUrl()));
            recordItem.setNodeTitle(hierarchy2NodeName.get(recordMeta.getHierarchy()));
            recordItem.setLabel(recordNode.getLabel());
            recordItemList.add(recordItem);
        });

        return recordItemList;
    }

    @Override
    public MiniappRecordDialogRecordDetailVO unionRecordDetail(RecordNodeDTO recordNode) {
        MiniappRecordDialogRecordDetailVO recordDetail = new MiniappRecordDialogRecordDetailVO();
        if (recordNode == null) {
            return recordDetail;
        }

        // 获取数据信息
        RecordParentMetaDTO parentMeta = recordNode.getRecordParentMeta();
        RecordMetaDTO recordMeta = recordNode.getRecordMeta();
        RecordContentDTO recordContent = recordNode.getRecordContent();
        // 设置信息
        recordDetail.setParentId(parentMeta.getParentId());
        recordDetail.setVersion(recordMeta.getVersion());
        recordDetail.setHierarchy(recordMeta.getHierarchy());
        recordDetail.setPart(recordMeta.getPart());
        recordDetail.setTitle(parentMeta.getParentTitle());
        recordDetail.setText(recordContent.getAudioText());
        recordDetail.setAudioUrl(objectStorageHelper.getURL(recordContent.getAudioUrl()));

        return recordDetail;
    }


    @Override
    public Boolean isDialogFlowResetAll(Long dialogFlowId) {
        DialogFlowInfoPO dialogFlowInfo = dialogFlowService.getDialogFlow(dialogFlowId);
        DialogFlowStatusEnum status = dialogFlowInfo.getStatus();
        return DialogFlowStatusEnum.DRAFT.equals(status) || DialogFlowStatusEnum.REJECTED.equals(status);
    }

    @Override
    public List<RecordNodeDTO> queryRecordNodeDTOList(List<RecordNodeDTO> recordNodeDTOS, List<MiniappRecordNodeRequestVO> qs) {
        List<RecordNodeDTO> queryRecordNodeDTOS = new ArrayList<>();
        qs.forEach(q -> queryRecordNodeDTOS.addAll(queryRecordNodeDTOList(recordNodeDTOS, q)));
        return queryRecordNodeDTOS;
    }

    @Override
    public List<RecordNodeDTO> queryRecordNodeDTOList(List<RecordNodeDTO> recordNodeDTOS, MiniappRecordNodeRequestVO q) {
        // 组合查询条件
        Predicate<RecordNodeDTO> typePre = (RecordNodeDTO n) ->
                n.getRecordType() == null || n.getRecordType().equals(q.getType());
        Predicate<RecordNodeDTO> verPre = (RecordNodeDTO n) ->
                n.getRecordMeta().getVersion() == null || n.getRecordMeta().getVersion().equals(q.getVersion());
        Predicate<RecordNodeDTO> hierPre = (RecordNodeDTO n) ->
                n.getRecordMeta().getHierarchy() == null || n.getRecordMeta().getHierarchy().equals(q.getHierarchy());
        Predicate<RecordNodeDTO> partPre = (RecordNodeDTO n) ->
                n.getRecordMeta().getPart() == null || n.getRecordMeta().getPart().equals(q.getPart());
        Predicate<RecordNodeDTO> fullPre = typePre.and(verPre).and(hierPre).and(partPre);

        return recordNodeDTOS.stream().filter(fullPre).collect(Collectors.toList());
    }

    @Override
    public void resetRecordNodeList(List<RecordNodeDTO> recordNodeDTOS, Long resetUserId) {
        skipAutoUploadSameTextAudio.set(true);
        try {
            // 按类别分类
            Map<RecordTypeEnum, List<RecordNodeDTO>> typeGroupResult = recordNodeDTOS.stream()
                    .collect(Collectors.groupingBy(RecordNodeDTO::getRecordType));
            typeGroupResult.forEach((type, typeNodes) -> {
                // 基于每个 parentId 进行分类统计
                Map<String, List<RecordNodeDTO>> parentGroupResult = typeNodes.stream()
                        .collect(Collectors.groupingBy(x -> x.getRecordParentMeta().getParentId()));
                parentGroupResult.forEach((parentId, nodes) -> {
                    if (RecordTypeEnum.DIALOG_FLOW_STEP.equals(type)) {
                        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(parentId);
                        resetDialogStepRecord(dialogFlowStep, nodes, resetUserId);
                    } else if ((RecordTypeEnum.isHangUp(type))) {
                        DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigService.getDialogFlowConfiguration(Long.valueOf(parentId));
                        resetHangUpRecord(dialogFlowConfig, nodes, resetUserId);
                    } else if (RecordTypeEnum.ROBOT_KNOWLEDGE.equals(type)) {
                        RobotKnowledgePO robotKnowledge = robotKnowledgeService.getRobotKnowledge(parentId);
                        resetRobotKnowledgeRecord(robotKnowledge, nodes, resetUserId);
                    } else if (RecordTypeEnum.KNOWLEDGE_STEP.equals(type)) {
                        DialogFlowStepPO dialogFlowStep = dialogFlowStepService.getDialogFlowStep(parentId);
                        resetDialogStepRecord(dialogFlowStep, nodes, resetUserId);
                    }
                });
            });
        } finally {
            skipAutoUploadSameTextAudio.remove();
        }
    }

    private void resetDialogStepRecord(DialogFlowStepPO dialogFlowStep, List<RecordNodeDTO> recordNodeDTOS, Long resetUserId) {
        // 获取对话流程树状结构信息，读取指定的录音节点
        Map<String, TextAudioContentPO> stepTreeInfoMap = dialogFlowStepService.getDialogStepTreeInfo(dialogFlowStep);
        int resetNum = 0;
        for (RecordNodeDTO recordNode : recordNodeDTOS) {
            RecordMetaDTO recordMeta = recordNode.getRecordMeta();
            TextAudioContentPO audio = stepTreeInfoMap.get(recordMeta.getHierarchy());
            if (textAudioManager.isBlankAudioText(audio)) {
                continue;
            }
            resetNum += textAudioManager.resetAudioUrl(audio, recordMeta.getPart());
        }
        // 保存话术流程更新
        dialogFlowStepService.modifyDialogFlowStep(dialogFlowStep, resetUserId);
        if (resetNum > 0) {
            // 更新录音修改时间(必要)
            updateRecordingTime(dialogFlowService.getDialogFlow(dialogFlowStep.getDialogFlowId()));
        }
    }

    private void resetHangUpRecord(DialogFlowConfigurationPO dialogFlowConfig, List<RecordNodeDTO> recordNodeDTOS, Long resetUserId) {
        int resetNum = 0;
        for (RecordNodeDTO recordNode : recordNodeDTOS) {
            TextAudioContentPO audio = getHangUpTextAudio(dialogFlowConfig, recordNode);
            if (textAudioManager.isBlankAudioText(audio)) {
                continue;
            }
            resetNum += textAudioManager.resetAudioUrl(audio, recordNode.getRecordMeta().getPart());
        }
        // 保存话术配置更新
        dialogFlowConfigService.setDialogFlowConfiguration(dialogFlowConfig, resetUserId);
        if (resetNum > 0) {
            // 更新录音修改时间(必要)
            updateRecordingTime(dialogFlowService.getDialogFlow(dialogFlowConfig.getDialogFlowId()));
        }
    }

    private void resetRobotKnowledgeRecord(RobotKnowledgePO robotKnowledge, List<RecordNodeDTO> recordNodeDTOS, Long resetUserId) {
        Map<String, List<RecordNodeDTO>> hierarchyGroupResult = recordNodeDTOS.stream()
                .collect(Collectors.groupingBy(x -> x.getRecordMeta().getHierarchy()));
        int resetNum = 0;
        List<String> hierarchyList = new ArrayList<>(hierarchyGroupResult.keySet());
        for (String hierarchy : hierarchyList) {
            List<RecordNodeDTO> nodeDTOS = hierarchyGroupResult.get(hierarchy);
            List<? extends TextAudioContentPO> audioList = robotKnowledge.getRobotKnowledgeAnswers();
            TextAudioContentPO audio = audioList.get(HierarchyInfo.toHierarchy(hierarchy).getIndex());
            if (textAudioManager.isBlankAudioText(audio)) {
                continue;
            }
            for (RecordNodeDTO node : nodeDTOS) {
                RecordMetaDTO recordMeta = node.getRecordMeta();
                resetNum += textAudioManager.resetAudioUrl(audio, recordMeta.getPart());
            }
        }
        // 保存知识库更新
        robotKnowledge.setRecorded(false);
        RobotKnowledgeModifyVO robotKnowledgeModifyVO = new RobotKnowledgeModifyVO();
        BeanUtils.copyProperties(robotKnowledge, robotKnowledgeModifyVO);
        robotKnowledgeService.modifyRobotKnowledge(robotKnowledgeModifyVO, resetUserId);
        if (resetNum > 0) {
            // 更新录音修改时间(必要)
            updateRecordingTime(dialogFlowService.getDialogFlow(robotKnowledge.getDialogFlowId()));
        }
    }


    @Override
    public void uploadDialogStepRecord(DialogFlowStepPO dialogFlowStep, RecordNodeDTO recordNodeDTO, RecordUploadDTO recordUploadDTO, Long uploadUserId) {
        Map<String, TextAudioContentPO> stepTreeMap = dialogFlowStepService.getDialogStepTreeInfo(dialogFlowStep);
        // 获取录音内容
        RecordMetaDTO recordMeta = recordNodeDTO.getRecordMeta();
        TextAudioContentPO audio = stepTreeMap.get(recordMeta.getHierarchy());
        if (textAudioManager.isBlankAudioText(audio)) {
            return;
        }
        // 更新数据库的录音地址
        textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), recordUploadDTO.getDbRecordUrl());
        // 更新录音流程信息
        dialogFlowStepService.modifyDialogFlowStep(dialogFlowStep, uploadUserId);
        // 更新录音修改时间(必要)
        updateRecordingTime(dialogFlowService.getDialogFlow(dialogFlowStep.getDialogFlowId()));
    }

    @Override
    public void uploadHangUpRecord(DialogFlowConfigurationPO dialogFlowConfig, RecordNodeDTO recordNodeDTO, RecordUploadDTO recordUploadDTO, Long uploadUserId) {
        TextAudioContentPO audio = getHangUpTextAudio(dialogFlowConfig, recordNodeDTO);
        if (textAudioManager.isBlankAudioText(audio)) {
            return;
        }
        // 更新数据库的录音地址
        RecordMetaDTO recordMeta = recordNodeDTO.getRecordMeta();
        textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), recordUploadDTO.getDbRecordUrl());
        // 更新录音配置信息
        dialogFlowConfigService.setDialogFlowConfiguration(dialogFlowConfig, uploadUserId);
        // 更新录音修改时间
        updateRecordingTime(dialogFlowService.getDialogFlow(dialogFlowConfig.getDialogFlowId()));
    }

    @Override
    public void uploadRobotKnowledgeRecord(RobotKnowledgePO robotKnowledge, RecordNodeDTO recordNodeDTO, RecordUploadDTO recordUploadDTO, Long uploadUserId) {
        // 录音节点元数据
        RecordMetaDTO recordMeta = recordNodeDTO.getRecordMeta();
        // 获取录音节点
        List<? extends TextAudioContentPO> answers = robotKnowledge.getRobotKnowledgeAnswers();
        int hierarchyIndex = HierarchyInfo.toHierarchy(recordMeta.getHierarchy()).getIndex();
        TextAudioContentPO audio = answers.get(hierarchyIndex);
        if (textAudioManager.isBlankAudioText(audio)) {
            return;
        }
        // 更新知识库录音信息
        textAudioManager.updateAudioUrl(audio, recordMeta.getPart(), recordUploadDTO.getDbRecordUrl());
        RobotKnowledgeModifyVO robotKnowledgeModifyVO = new RobotKnowledgeModifyVO();
        BeanUtils.copyProperties(robotKnowledge, robotKnowledgeModifyVO);
        robotKnowledgeService.modifyRobotKnowledge(robotKnowledgeModifyVO, uploadUserId);
        // 更新录音修改时间
        updateRecordingTime(dialogFlowService.getDialogFlow(robotKnowledge.getDialogFlowId()));
    }


    @Override
    public RecordUploadDTO uploadRecord(Long dialogFlowId, String id, MultipartFile file, RecordTypeEnum recordType) {
        // 返回的信息
        RecordUploadDTO recordUpload = new RecordUploadDTO();

        // 保存为临时文件
        String basePath = TempFilePathKeyCenter.getDialogRecordTempFilePath(recordType);
        File basePathFile = new File(basePath);
        if (!basePathFile.exists()) {
            basePathFile.mkdirs();
        }

        String originalFilename = file.getOriginalFilename();
        File localTmpFile = new File(basePath + file.getOriginalFilename());
        try {
            file.transferTo(localTmpFile);
        } catch (IOException exp) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "上传的录音文件保存失败");
        }

        // 录音格式转换
        String wavFilePath = null;
        File wavFile;
        //检查格式是否是mp3，如果是mp3转成wav。
        if (null != file.getOriginalFilename() && file.getOriginalFilename().endsWith(".mp3")) {
            try {
                wavFilePath = AudioHandleUtils.mp3ConvertWav(basePath + originalFilename);
                audioHandleService.validWaveFormat(new File(wavFilePath));
                wavFile = AudioHandleUtils.autoCutAudioInfo(new File(wavFilePath), WAV_TO_PCM_HEAD_LEN);
            } catch (ComException comException) {
                throw comException;
            } catch (Exception e) {
                logger.error("[LogHub_Warn]话术录音处理失败, 话术 id: {}, {} id: {}, 录音文件: {}, Exception Message: {}",
                        dialogFlowId, recordType.toHumpStr(), dialogFlowId, wavFilePath, e.getMessage());
                throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
            }

        } else {
            // 效验声道
            audioHandleService.validWaveFormat(localTmpFile);
            wavFile = AudioHandleUtils.autoCutAudioInfo(localTmpFile, WAV_TO_PCM_HEAD_LEN);
            wavFilePath = wavFile.getAbsolutePath();
        }

        // 获取文件名
        String wavFileName;
        try {
            wavFileName = DialogFlowUploadServiceImpl.getFileName(wavFilePath);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取录音文件名失败, 话术 id: {}, {} id: {}, 录音文件: {}, Exception Message: {}",
                    dialogFlowId, recordType.toHumpStr(), id, wavFilePath, e.getMessage());
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
        }

        // 上传录音到阿里OSS对象
        String ossFileKey = OssKeyCenter.getDialogRecordOssFileKey(dialogFlowId, id, wavFileName, recordType);
        if (ossFileKey == null) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音文件RecordType类型错误");
        }
        String aliRecordUrl = objectStorageHelper.upload(ossFileKey, wavFile);

        recordUpload.setDbRecordUrl(ossFileKey);
        recordUpload.setAliOssFullUrl(aliRecordUrl);

        // 删除本地录音文件
        if (!MyFileUtils.deleteFileByPath(wavFile.getPath())) {
            logger.error("服务器录音文件删除失败");
        }

        return recordUpload;
    }

    @Override
    public RecordUploadDTO uploadRecord(Long customVariableRecordingId, String name, String content, MultipartFile file) {
        // 返回的信息
        RecordUploadDTO recordUpload = new RecordUploadDTO();

        // 保存为临时文件
        String basePath = TempFilePathKeyCenter.getCustomVariableTempFilePath();
        File basePathFile = new File(basePath);
        if (!basePathFile.exists()) {
            basePathFile.mkdirs();
        }

        String originalFilename = file.getOriginalFilename();
        File localTmpFile = new File(basePath + originalFilename);
        try {
            file.transferTo(localTmpFile);
        } catch (IOException exp) {
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "上传的录音文件保存失败");
        }

        // 效验声道
        audioHandleService.validWaveFormat(localTmpFile);

        // 录音格式转换
        String wavFilePath = null;
        File wavFile;
        try {
            wavFilePath = AudioHandleUtils.mp3ConvertWav(basePath + originalFilename);
            wavFile = new File(wavFilePath);
            wavFile = AudioHandleUtils.autoCutAudioInfo(wavFile, WAV_TO_PCM_HEAD_LEN);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]百家姓录音处理失败, 自定义变量录音Id: {}, 录音内容: {}，录音文件: {}, Exception Message: {}",
                    customVariableRecordingId, content, wavFilePath, e.getMessage());
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
        }

        // 获取文件名
        String wavFileName;
        try {
            wavFileName = DialogFlowUploadServiceImpl.getFileName(wavFilePath);
        } catch (Exception e) {
            logger.error("[LogHub_Warn]获取录音文件名失败, 自定义变量录音Id: {}, 录音内容: {}，录音文件: {}, Exception Message: {}",
                    customVariableRecordingId, content, wavFilePath, e.getMessage());
            throw new ComException(ComErrorCode.UNKNOWN_ERROR, "录音处理失败");
        }

        // 上传录音到阿里OSS对象
        String ossFileKey = OssKeyCenter.getCustomVariableOssFileKey(customVariableRecordingId, name, content, wavFileName);
        String aliRecordUrl = objectStorageHelper.upload(ossFileKey, wavFile);

        recordUpload.setDbRecordUrl(ossFileKey);
        recordUpload.setAliOssFullUrl(aliRecordUrl);

        // 删除本地录音文件
        if (!MyFileUtils.deleteFileByPath(wavFile.getPath())) {
            logger.error("服务器录音文件删除失败");
        }

        return recordUpload;
    }

    @Override
    public void updateRecordingTime(DialogFlowInfoPO dialogFlowInfo) {
        dialogFlowInfo.setLastRecordingTime(LocalDateTime.now());
        dialogFlowService.updateNotNull(dialogFlowInfo);
    }


    @Override
    public RecordParentMetaDTO getDialogFlowStepMetaInfo(DialogFlowStepPO dialogFlowStep) {
        RecordParentMetaDTO recordParentMetaDTO = new RecordParentMetaDTO();
        if (dialogFlowStep == null) {
            return recordParentMetaDTO;
        }
        recordParentMetaDTO.setParentId(dialogFlowStep.getId());
        recordParentMetaDTO.setParentTitle(dialogFlowStep.getName());
        recordParentMetaDTO.setOrder(dialogFlowStep.getDefinedOrder());
        recordParentMetaDTO.setLabel(dialogFlowStep.getLabel());
        return recordParentMetaDTO;
    }

    @Override
    public RecordParentMetaDTO getHangUpMetaInfo(DialogFlowConfigurationPO dialogFlowConfig) {
        RecordParentMetaDTO recordParentMetaDTO = new RecordParentMetaDTO();
        if (dialogFlowConfig == null) {
            return recordParentMetaDTO;
        }
        recordParentMetaDTO.setParentId(String.valueOf(dialogFlowConfig.getDialogFlowId()));
        recordParentMetaDTO.setParentTitle(HANG_UP_RECORD_TITLE);
        return recordParentMetaDTO;
    }

    @Override
    public RecordParentMetaDTO getRobotKnowledgeMetaInfo(RobotKnowledgePO robotKnowledge) {
        RecordParentMetaDTO recordParentMetaDTO = new RecordParentMetaDTO();
        if (robotKnowledge == null) {
            return recordParentMetaDTO;
        }
        recordParentMetaDTO.setParentId(robotKnowledge.getId());
        recordParentMetaDTO.setParentTitle(robotKnowledge.getTitle());
        recordParentMetaDTO.setLabel(robotKnowledge.getLabel());
        return recordParentMetaDTO;
    }

    @Override
    public TextAudioContentPO getHangUpTextAudio(DialogFlowConfigurationPO dialogFlowConfig, RecordNodeDTO recordNodeDTO) {
        Integer index = HierarchyInfo.toHierarchy(recordNodeDTO.getRecordMeta().getHierarchy()).getIndex();
        if(RecordTypeEnum.CONFIG_MULTI_BUSINESS.equals(recordNodeDTO.getRecordType())) {
            List<DialogFlowBusinessQAConfigRulePO> dialogFlowBusinessQAConfigRuleList = dialogFlowConfig.getDialogFlowBusinessQAConfigRuleList();
            if(CollectionUtils.isNotEmpty(dialogFlowBusinessQAConfigRuleList) && dialogFlowBusinessQAConfigRuleList.size() > index) {
                return dialogFlowBusinessQAConfigRuleList.get(index).getTextAudioContent();
            }
        }else if(RecordTypeEnum.CONFIG_MULTI_REJECT.equals(recordNodeDTO.getRecordType())) {
            List<DialogFlowDeclineConfigRulePO> dialogFlowDeclineConfigRuleList = dialogFlowConfig.getDialogFlowDeclineConfigRuleList();
            if(CollectionUtils.isNotEmpty(dialogFlowDeclineConfigRuleList) && dialogFlowDeclineConfigRuleList.size() > index) {
                return dialogFlowDeclineConfigRuleList.get(index).getTextAudioContent();
            }
        }else if(RecordTypeEnum.CONFIG_MULTI_USER_SAY.equals(recordNodeDTO.getRecordType())) {
            List<DialogFlowUserSayConfigRulePO> dialogFlowUserSayConfigRuleList = dialogFlowConfig.getDialogFlowUserSayConfigRuleList();
            if(CollectionUtils.isNotEmpty(dialogFlowUserSayConfigRuleList) && dialogFlowUserSayConfigRuleList.size() > index) {
                return dialogFlowUserSayConfigRuleList.get(index).getTextAudioContent();
            }
        }else if(RecordTypeEnum.CALL_TIMEOUT.equals(recordNodeDTO.getRecordType())) {
            if (BooleanUtils.isTrue(dialogFlowConfig.getEnableCallTimeout())) {
                return dialogFlowConfig.getCallTimeoutHangupAnswer();
            }
        }else if(RecordTypeEnum.MUTE_TIMEOUT.equals(recordNodeDTO.getRecordType())) {
            if (BooleanUtils.isTrue(dialogFlowConfig.getEnableContinuousMuteTimeout())) {
                return dialogFlowConfig.getContinuousMuteHangupAnswer();
            }
        }
        return null;
    }

    @Override
    public RecordCountDTO calculateDialogFlowRecordStats(Long dialogFlowId) {
        List<RecordCountDTO> resultList = calculateDialogFlowRecordStats(Collections.singletonList(dialogFlowId));
        if (CollectionUtils.isEmpty(resultList)) {
            return new RecordCountDTO();
        }
        return resultList.get(0);
    }

    @Override
    public List<RecordCountDTO> calculateDialogFlowRecordStats(List<Long> dialogFlowIdList) {
        List<RecordCountDTO> result = new ArrayList<>();

        List<DialogFlowInfoPO> dialogFlowList = dialogFlowService.findAllByDialogFlowIds(new HashSet<>(dialogFlowIdList));
        List<DialogFlowStepPO> dialogFlowSteps = dialogFlowStepService.findDialogFlowStepsByIds(dialogFlowIdList);
        List<DialogFlowConfigurationPO> dialogFlowConfigList = dialogFlowConfigService.getDialogFlowConfigurationByIds(dialogFlowIdList);
        List<RobotKnowledgePO> robotKnowledgeList = robotKnowledgeService.findRobotKnowledgeByDialogFlowIds(dialogFlowIdList);

        Map<Long, List<DialogFlowStepPO>> stepMapList = MyCollectionUtils.listToMapList(dialogFlowSteps, DialogFlowStepPO::getDialogFlowId);
        Map<Long, DialogFlowConfigurationPO> dialogFlowConfigMap = MyCollectionUtils.listToMap(dialogFlowConfigList, DialogFlowConfigurationPO::getDialogFlowId);
        Map<Long, List<RobotKnowledgePO>> knowledgeMapList = MyCollectionUtils.listToMapList(robotKnowledgeList, RobotKnowledgePO::getDialogFlowId);

        return dialogFlowList
                .stream()
                .map(dialogFlow -> {
                    List<DialogFlowStepPO> stepList = stepMapList.getOrDefault(dialogFlow.getId(), Collections.emptyList());
                    DialogFlowConfigurationPO dialogFlowConfig = dialogFlowConfigMap.get(dialogFlow.getId());
                    List<RobotKnowledgePO> knowledgeList = knowledgeMapList.getOrDefault(dialogFlow.getId(), Collections.emptyList());

                    //获取话术的基础信息
                    DialogVoiceTypeEnum voiceType = dialogFlow.getVoiceType();
                    RecordCountDTO recordCountDTO = new RecordCountDTO();
                    List<RecordNodeDTO> allRecordList = getDialogFlowRecordNodeList(stepList, dialogFlowConfig, voiceType, knowledgeList);
                    DialogFlowHumanInterventionVO humanIntervention = dialogRecordService.getHumanInterventionRecords(dialogFlowConfig);
                    List<String> allTextContentList = allRecordList.stream()
                            .map(item -> {
                                if (Objects.isNull(item.getRecordContent())) {
                                    return null;
                                }
                                return item.getRecordContent().getAudioText();
                            })
                            .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                            .map(textAudioManager::removePartPrefixText)
                            .collect(Collectors.toList());
                    if (Objects.nonNull(humanIntervention)) {
                        allTextContentList.add(humanIntervention.getReminderText());
                    }
                    Set<String> distinctContentSet = new HashSet<>(allTextContentList);
                    List<String> hasAudioList = allRecordList.stream()
                            .map(item -> {
                                if (Objects.nonNull(item.getRecordContent()) && BooleanUtils.isTrue(item.getStatus())) {
                                    return item.getRecordContent().getAudioText();
                                }
                                return null;
                            })
                            .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                            .map(textAudioManager::removePartPrefixText)
                            .collect(Collectors.toList());
                    if (Objects.nonNull(humanIntervention) && org.apache.commons.lang3.StringUtils.isNotBlank(humanIntervention.getReminderUrl())) {
                        hasAudioList.add(humanIntervention.getReminderText());
                    }
                    recordCountDTO.setTotalRecordCount(allTextContentList.size());
                    recordCountDTO.setTotalRecordWord(allTextContentList.stream().mapToInt(MyStringUtils::countValidChar).sum());
                    recordCountDTO.setDistinctRecordCount(distinctContentSet.size());
                    recordCountDTO.setDistinctWordCount(distinctContentSet.stream().mapToInt(MyStringUtils::countValidChar).sum());
                    recordCountDTO.setHasRecordCount(hasAudioList.size());
                    recordCountDTO.setHasRecordWord(hasAudioList.stream().mapToInt(MyStringUtils::countValidChar).sum());
                    recordCountDTO.setDialogFlowId(dialogFlow.getId());
                    Set<String> distinctHasContentSet = new HashSet<>(hasAudioList);
                    recordCountDTO.setDistinctHasRecordCount(distinctHasContentSet.size());
                    recordCountDTO.setDistinctHasWordCount(distinctHasContentSet.stream().mapToInt(MyStringUtils::countValidChar).sum());

                    return recordCountDTO;
                })
                .collect(Collectors.toList());

    }

    private List<RecordNodeDTO> getDialogFlowRecordNodeList(List<DialogFlowStepPO> dialogFlowSteps,
                                                            DialogFlowConfigurationPO dialogFlowConfig,
                                                            DialogVoiceTypeEnum dialogVoiceType,
                                                            List<RobotKnowledgePO> robotKnowledges) {
        List<RecordNodeDTO> recordNodeDTOS = new ArrayList<>();

        // 获取所有 DialogFlowStep
        if (CollectionUtils.isNotEmpty(dialogFlowSteps)) {
            dialogFlowSteps.forEach(dialogFlowStep -> recordNodeDTOS.addAll(getDialogFlowStepRecordNodeList(dialogFlowStep)));
        }

        // 获取所有挂机话术
        if (Objects.nonNull(dialogFlowConfig)) {
            recordNodeDTOS.addAll(getHangUpRecordNodeList(dialogFlowConfig));
        }

        // 获取所有知识库
        if (CollectionUtils.isNotEmpty(robotKnowledges)) {
            robotKnowledges.forEach(robotKnowledge -> recordNodeDTOS.addAll(getRobotKnowledgeRecordNodeList(robotKnowledge, dialogVoiceType)));
        }
        return recordNodeDTOS;
    }

}

