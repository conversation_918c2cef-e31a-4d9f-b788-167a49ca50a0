package com.yiwise.core;

import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.service.platform.OrganizationService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 组织service测试
 * @create 2018/11/13
 **/
public class OrganizationServiceTest extends AbstractContextTest {

    @Resource
    OrganizationService organizationService;

    @Test
    public void selectSubordinate() {
        UserPO userPO = new UserPO();
        userPO.setTenantId(1L);
        System.out.println(organizationService.selectSubordinate(userPO));
    }

    @Test
    public void selectDeprecatedSubordinate() {
        Long tenantId = 1L;
        List<Long> organizationIdList = Arrays.asList(1L, 68L, 85L, 116L, 71L, 72L, 117L);
        System.out.println(organizationService.selectSubordinate(tenantId, organizationIdList));
    }

}
