package com.yiwise.ope.api.controller.robotcalljob;

import com.yiwise.base.model.annotation.auth.InnerOnly;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.helper.NewRobotCallJobActiveTimeHelper;
import com.yiwise.core.model.bo.robotcalljob.timeduration.ActiveTimeBO;
import com.yiwise.core.model.vo.ope.RobotCallJobTimeVO;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/apiOpe/robotCallJob")
public class RobotCallJobTimeController {

	@InnerOnly
	@PostMapping("dateIsValid")
		public ResultObject<Boolean> dateIsValid(@RequestBody RobotCallJobTimeVO request) {
		return ResultObject.success(NewRobotCallJobActiveTimeHelper.dateIsValid(request.getRobotCallJob(), request.getLocalDate()));
	}

	@InnerOnly
	@PostMapping("getNextValidDate")
	public ResultObject<LocalDate> getNextValidDate(@RequestBody RobotCallJobTimeVO request) {
		return ResultObject.success(NewRobotCallJobActiveTimeHelper.getNextValidDate(request.getRobotCallJob(), request.getLocalDate()).orElse(null));
	}

	@InnerOnly
	@PostMapping("isInActiveTimeDuration")
	public ResultObject<Boolean> isInActiveTimeDuration(@RequestBody RobotCallJobTimeVO request) {
		return ResultObject.success(NewRobotCallJobActiveTimeHelper.isInActiveTimeDuration(request.getRobotCallJob(), request.getLocalDateTime()));
	}

	@InnerOnly
	@PostMapping("getNextTime")
	public ResultObject<ActiveTimeBO> getNextTime(@RequestBody RobotCallJobTimeVO request) {
		return ResultObject.success(NewRobotCallJobActiveTimeHelper.getNextTime(request.getRobotCallJob(), request.getLocalDateTime()).orElse(null));
	}

	@InnerOnly
	@PostMapping("getNearestActiveTime")
	public ResultObject<ActiveTimeBO> getNearestActiveTime(@RequestBody RobotCallJobTimeVO request) {
		return ResultObject.success(NewRobotCallJobActiveTimeHelper.getNearestActiveTime(request.getRobotCallJob(), request.getLocalDateTime()).orElse(null));
	}
}
