package com.yiwise.ope.api.controller.distributor;


import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.DistributorPO;
import com.yiwise.core.dal.entity.OperationLogPO;
import com.yiwise.core.dal.entity.UserPO;
import com.yiwise.core.dal.mongo.DistributorConfigModelMongoPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DistributorListVO;
import com.yiwise.core.model.vo.boss.DistributorQueryVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.model.vo.boss.DistributorSimpleVO;
import com.yiwise.core.model.vo.ope.*;
import com.yiwise.core.service.engine.OperationLogService;
import com.yiwise.core.service.ope.platform.DialogFlowDistributorRelationService;
import com.yiwise.core.service.ope.platform.DistributorService;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.validate.boss.distributor.UpdateBossDistributorValidate;
import com.yiwise.lcs.api.dto.SmsPlatformChannelDTO;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> wang
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/apiOpe/distributor")
public class DistributorController {

    @Resource
    private DistributorService distributorService;
    @Resource
    private DialogFlowDistributorRelationService dialogFlowDistributorRelationService;
    @Resource
    private UserService userService;
    @Resource
    private OperationLogService operationLogService;


    // @formatter:off
    /**
     * @api {Get} /apiOpe/distributor/listDistributor 获取一级代理商列表
     * @apiName listDistributor
     * @apiGroup distributor
     * @apiParam {Integer} pageNum                       #页数
     * @apiParam {Integer} pageSize                      #每页数据量
     * @apiParam {String} name                           #搜索渠道商
     * @apiParam {String} contactPerson                  #搜索联系人
     * @apiParam {String} contactPhone                   #搜索联系电话
     * @apiParam {String} customerManager                #搜索客户经理
     * @apiParam {Long} customerManagerId                #搜索客户经理id
     * @apiParam {UserStatusEnum} status                 #NOT_ENABLED(0, "未启用"), ENABLED(1, "启用"), CLOSED(2, "关闭");
     * @apiParam {Long} dialogFlowId                     #话术id
     * @apiParam {Long} distributorIdSet                 #具体选择的代理商id
     * @apiParam {String} equalMatchLinkman                        # 需要精确匹配的联系人
     *
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 12,
     *         "pages": 1,
     *         "content": [
     *               {
     *                 "id": 61,
     *                 "parentId": 0,
     *                 "name": "杭州零级代理商",
     *                 "contactPerson": "test",
     *                 "contactPhone": "1231231231",
     *                 "address": "123",
     *                 "robotLimit": null,
     *                 "dialogLimit": null,
     *                 "customerManager": "test",
     *                 "customerManagerId": 33,
     *                 "comment": "伟大的共产主义接班人",
     *                 "chargingMethod": null,
     *                 "marketScreen": null,
     *                 "startDate": "2018-06-05",
     *                 "endDate": "2021-07-01",
     *                 "totalRobotDisplay": 37,
     *                 "soldRobotDisplay": 0,
     *                 "remainingRobotDisplay": 37,
     *                 "totalDialogFlow": 1000,
     *                 "soldDialogFlow": 0,
     *                 "remainingDialogFlow": 1000,
     *                 "billingMethod": "YEARLY",
     *                 "managerPhone": null,
     *                 "status": "ENABLED"
     *             }
     *        ]
     *     },
     *     "requestId": "RAAEOBDK",
     *     "resultMsg": "获取成功!",
     *     "errorStackTrace": null
     * }
     *     */
    // @formatter:on
    @GetMapping(value = "/listDistributor")
    public ResultObject listDistributor(DistributorQueryVO distributorQuery) {
        distributorQuery.setDistributorId(0L);
        distributorQuery.setCurrentLoginUserId(SecurityUtils.getUserId());
        distributorQuery.setSystemEnum(SystemEnum.OPE);
        PageResultObject<DistributorListVO> pageResultObject = null;
        if (distributorQuery.getDialogFlowId() != null) {
            Set<Long> distributorIdSet = dialogFlowDistributorRelationService.selectByDialogFlowId(distributorQuery.getDialogFlowId());
            if (CollectionUtils.isEmpty(distributorIdSet)) {
                pageResultObject = PageResultObject.of(new ArrayList<>());
                return ResultObject.success(pageResultObject, "获取成功!");
            }
            distributorQuery.setDistributorIdSetByDialogFlow(distributorIdSet);
        }
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(distributorQuery.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(distributorQuery.getCurrentLoginUserId());
            distributorQuery.setFilterUserIdSet(userId);
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据
        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(distributorQuery.getCurrentLoginUserId())) {
            distributorQuery.setFilterUserIdSet(userService.getOpeChildUserIdByParentUserId(distributorQuery.getCurrentLoginUserId()));
        }


        pageResultObject = distributorService.listBossDistributor(distributorQuery);
        return ResultObject.success(pageResultObject, "获取成功!");
    }

    // @formatter:off
    /**
     * @api {Get} /apiOpe/distributor/exportListDistributor 获取一级代理商列表
     * @apiName exportListDistributor
     * @apiGroup distributor
     *
     * @apiParam {Integer} pageNum                       #页数
     * @apiParam {Integer} pageSize                      #每页数据量
     * @apiParam {String} name                           #搜索渠道商
     * @apiParam {String} contactPerson                  #搜索联系人
     * @apiParam {String} contactPhone                   #搜索联系电话
     * @apiParam {String} customerManager                #搜索客户经理
     * @apiParam {Long} customerManagerId                #搜索客户经理id
     * @apiParam {UserStatusEnum} status                 #NOT_ENABLED(0, "未启用"), ENABLED(1, "启用"), CLOSED(2, "关闭");
     * @apiParam {Long} dialogFlowId                     #话术id
     * @apiParam {Long} distributorIdSet                 #具体选择的代理商id
     * @apiParam {String} equalMatchLinkman                        # 需要精确匹配的联系人
     *
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 20,
     *         "totalElements": 12,
     *         "pages": 1,
     *         "content": [
     *               {
     *                 "id": 61,
     *                 "parentId": 0,
     *                 "name": "杭州零级代理商",
     *                 "contactPerson": "test",
     *                 "contactPhone": "1231231231",
     *                 "address": "123",
     *                 "robotLimit": null,
     *                 "dialogLimit": null,
     *                 "customerManager": "test",
     *                 "customerManagerId": 33,
     *                 "comment": "伟大的共产主义接班人",
     *                 "chargingMethod": null,
     *                 "marketScreen": null,
     *                 "startDate": "2018-06-05",
     *                 "endDate": "2021-07-01",
     *                 "totalRobotDisplay": 37,
     *                 "soldRobotDisplay": 0,
     *                 "remainingRobotDisplay": 37,
     *                 "totalDialogFlow": 1000,
     *                 "soldDialogFlow": 0,
     *                 "remainingDialogFlow": 1000,
     *                 "billingMethod": "YEARLY",
     *                 "managerPhone": null,
     *                 "status": "ENABLED"
     *             }
     *        ]
     *     },
     *     "requestId": "RAAEOBDK",
     *     "resultMsg": "获取成功!",
     *     "errorStackTrace": null
     * }
     *     */
    // @formatter:on
    @GetMapping(value = "/exportListDistributor")
    public ResultObject exportListDistributor(DistributorQueryVO distributorQuery) {
        distributorQuery.setDistributorId(0L);
        distributorQuery.setCurrentLoginUserId(SecurityUtils.getUserId());
        distributorQuery.setSystemEnum(SystemEnum.OPE);

        if (distributorQuery.getDialogFlowId() != null) {
            Set<Long> distributorIdSet = dialogFlowDistributorRelationService.selectByDialogFlowId(distributorQuery.getDialogFlowId());
            if (CollectionUtils.isEmpty(distributorIdSet)) {
                throw new ComException(ComErrorCode.NOT_EXIST,"创建导出任务失败，没有符合条件的客户");
            }
            distributorQuery.setDistributorIdSetByDialogFlow(distributorIdSet);
        }
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(distributorQuery.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(distributorQuery.getCurrentLoginUserId());
            distributorQuery.setFilterUserIdSet(userId);
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据
        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(distributorQuery.getCurrentLoginUserId())) {
            distributorQuery.setFilterUserIdSet(userService.getOpeChildUserIdByParentUserId(distributorQuery.getCurrentLoginUserId()));
        }
        JobStartResultVO result = distributorService.exportListBossDistributor(distributorQuery);
        return ResultObject.success(result, "获取成功!");
    }


    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/createDistributor 新建一级代理商
     * @apiName createDistributor
     * @apiGroup distributor
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"name":"杭州一知智能科技有限公司",                           # 代理商名字，必填
     * 	"address":"浙江省杭州市",                                   # 代理商地址，必填
     * 	"contactPerson":"范范",                                     # 联系人，必填
     * 	"contactPhone":"13812345678",                               # 联系电话，必填
     * 	"customerManager":"封君",                                   # 客户经理
     * 	"customerManagerId":123,                                   # 客户经理id
     * 	"deploymentUserId":123,                                   # 实施负责人
     * 	"comment":"伟大的共产主义接班人",                            # 备注
     * 	"status" : "ENABLED",                                       # 是否启用,ENABLED(1, "启用"),CLOSED(2, "关闭");必填
     * 	"managerPhone":"12312312312312",                            # 经理电话
     * 	"rightToSplit":false,                                       # 能否创建下级分销商 true/false
     * 	"status": ENABLED                                           # 是否启用     NOT_ENABLED(0, "未启用"), ENABLED(1, "启用"), CLOSED(2, "关闭");
     * 	"deploymentUserIdList":[]                                   # 实施负责人ID list
     * 	"prestoreFare":540,                                        # 预存金额，必填
     * 	"givedDialogCount":5,                                        # AI话术赠送，必填
     * 	"robotPrice":5,                                        # 机器人价格，必填
     * 	"robotUnit":"YEARLY",                                        # 机器人价格单位，必填 PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"dialogFlowPrice":5,                                        # 话术价格，必填
     * 	"dialogFlowUnit":"PER",                                        # 话术价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"multiConcurrencyPrice":5,                                        # 一线多并发价格，必填
     * 	"multiConcurrencyUnit":"YEARLY",                                        # 一线多并发价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"agentPrice":5,                                        # 人工座席价格，必填
     * 	"agentUnit":"YEARLY",                                        # 人工座席价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"aiAssistantPrice":5,                                        # AI助理价格，必填
     * 	"aiAssistantUnit":"YEARLY",                                        # AI助理价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"ttsComposePrice":5,                                        # tts价格，必填
     * 	"ttsComposeUnit":"YEARLY",                                        # tts价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"ivrNavigationPrice":5,                                        # ivr导航价格，必填
     * 	"ivrNavigationUnit":"YEARLY",                                        # ivr导航价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"qcPrice":5,                                        # 质检价格，必填
     * 	"qcUnit":"HOUR",                                        # 质检价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     * 	"defaultPrice":0,                                        # 是否是 默认价格修改  0  否 1 是',
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "成功创建！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/createDistributor")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR,
            contentExpression = " '用户[' + #currentUser.name + '] 添加新的代理商['+ #distributorVO.name +'] ' "
    )
    public ResultObject createDistributor(@RequestBody @Validated DistributorOpeVO distributorVO) {

        //创建代理商
        Long createdUserId = SecurityUtils.getUserId();
        distributorVO.calculate(distributorVO);
        //ope端设置客户经理名称
        if(org.apache.commons.lang3.StringUtils.isBlank(distributorVO.getCustomerManager())) {
            if(distributorVO.getCustomerManagerId() != null) {
                UserPO userPO = userService.getUserById(distributorVO.getCustomerManagerId());
                if(userPO != null) {
                    distributorVO.setCustomerManager(userPO.getName());
                }
            }
        }
        return ResultObject.success(distributorService.createDistributorLogic(distributorVO,createdUserId));
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/updateDistributor  更新指定代理商信息
     * @apiName updateDistributor
     * @apiGroup distributor
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"id":61,                                    # 代理商ID, 必填
     * 	"address":"123",                            # 代理商地址，必填
     * 	"name":"杭州零级代理商",                     # 代理商名字，必填
     * 	"contactPerson":"test",                     # 联系人，必填
     * 	"contactPhone":"1231231231",                # 联系电话，必填
     * 	"customerManager":"test",                   # 客户经理
     * 	"customerManagerId":123,                   # 客户经理id
     * 	"deploymentUserId":123,                     # 实施负责人用户id
     * 	"managerPhone":"12312312312312",            # 经理电话
     * 	"billingMethod":1,                          # 计费方式 1.YEARLY, 按年计费， 2.MONTHLY, 按月计费, 必填
     * 	"comment":"伟大的共产主义接班人",            # 备注
     * 	"rightToSplit":false                        # 能否创建下级分销商 true/false
     * 	"deploymentUserIdList":[]                                   # 实施负责人ID list
     * 	 	"robotPrice":5,                                        # 机器人价格，必填
     *      "robotUnit":"YEARLY",                                        # 机器人价格单位，必填 PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *    	"dialogFlowPrice":5,                                        # 话术价格，必填
     *    	"dialogFlowUnit":"PER",                                        # 话术价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"multiConcurrencyPrice":5,                                        # 一线多并发价格，必填
     *    	"multiConcurrencyUnit":"YEARLY",                                        # 一线多并发价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *    	"agentPrice":5,                                        # 人工座席价格，必填
     *    	"agentUnit":"YEARLY",                                        # 人工座席价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"aiAssistantPrice":5,                                        # AI助理价格，必填
     *     	"aiAssistantUnit":"YEARLY",                                        # AI助理价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"ttsComposePrice":5,                                        # tts价格，必填
     *     	"ttsComposeUnit":"YEARLY",                                        # tts价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"ivrNavigationPrice":5,                                        # ivr导航价格，必填
     *     	"ivrNavigationUnit":"YEARLY",                                        # ivr导航价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"qcPrice":5,                                        # 质检价格，必填
     *     	"qcUnit":"HOUR",                                        # 质检价格单位，必填PER(0, "每套"),HOUR(1, "每小时"),MONTHLY(2, "每月"),YEARLY(3, "每年")
     *     	"defaultPrice":0,
     * }
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "更新成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/updateDistributor")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorPO.distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 更新代理商['+ #distributorPO.name +']的信息 ' "
    )
    public ResultObject updateDistributor(@RequestBody @Validated(UpdateBossDistributorValidate.class) DistributorOpeUpdateVO distributorPO) {
        distributorPO.setUpdateUserId(SecurityUtils.getUserId());
        distributorPO.setCreateUserId(SecurityUtils.getUserId());
        distributorPO.calculate(distributorPO);
        distributorService.opeUpdate(distributorPO);
        return ResultObject.success(null,"更新成功！");
    }

        // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/updateDistributorIsvConfig  更新指定代理商信息
     * @apiName updateDistributorIsvConfig
     * @apiGroup distributor
     *
     * @apiParamExample {json} Request Example
     * {
     * 	"distributorId":61,                             # 代理商ID, 必填
     * 	"supportIsvConfig":false                        # 能否设置isv账号 true/false
     * }
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "更新成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/updateDistributorIsvConfig")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorPO.distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 更新代理商['+ #distributorPO.name +']的信息 ' "
    )
    public ResultObject updateDistributorIsvConfig(@RequestBody DistributorIsvConfigVO distributorVO) {
        DistributorPO distributorPO = new DistributorPO();
        distributorPO.setDistributorId(distributorVO.getDistributorId());
        distributorPO.setUpdateUserId(SecurityUtils.getUserId());
        distributorPO.setSupportIsvConfig(distributorVO.getSupportIsvConfig());
        distributorService.update(distributorPO);
        return ResultObject.success(null,"更新成功！");
    }

    // @formatter:off
    /**
     * @api {Get} /apiOpe/distributor/queryDistributor 获取在新建二级代理商时一级代理商列表下拉框
     * @apiName queryDistributor
     * @apiGroup distributor
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *    "code": 200,
     *    "data": [
     *        {
     *            "id": 69,
     *            "parentId": 8,
     *            "name": "西藏二级代理商1",
     *            "contactPerson": "王总",
     *            "contactPhone": "16644444474",
     *            "address": "深圳市",
     *            "robotLimit": null,
     *            "dialogLimit": null,
     *            "customerManager": "客户经理",
     *            "comment": "备注22",
     *            "chargingMethod": null,
     *            "marketScreen": null,
     *            "startDate": "2018-08-02",
     *            "endDate": "2019-08-02",
     *            "totalRobot": 112,
     *            "soldRobot": 0,
     *            "remainingRobot": 112,
     *            "totalDialogFlow": 101,
     *            "soldDialogFlow": 0,
     *            "remainingDialogFlow": 101,
     *            "billingMethod": "MONTHLY",
     *            "managerPhone": null,
     *            "rightToSplit": true,
     *            "status": "CLOSED",
     *            "parentName": "一级代理商"
     *        }
     *     "requestId": "RAAEOBDK",
     *     "resultMsg": "获取成功!",
     *     "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @GetMapping(value = "/queryDistributor")
    public ResultObject queryDistributor() {
        List<DistributorListVO> distributorList = distributorService.querySuperDistributor();
        return ResultObject.success(distributorList, "获取成功!");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/getDistributorInfo    编辑代理商时带出的原有信息
     * @apiName getDistributorInfo
     * @apiGroup distributor
     * @apiParam {Long} distributor                      #代理商ID
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *          {
     *            "id": 60,
     *            "parentId": 0,
     *            "name": "杭州一知智能科技有限公司",
     *            "contactPerson": "范范",
     *            "contactPhone": "13812345691",
     *            "address": "浙江省杭州市",
     *            "robotLimit": null,
     *            "dialogLimit": null,
     *            "customerManager": "封君",
     *            "comment": "伟大的共产主义接班人",
     *            "chargingMethod": null,
     *            "marketScreen": null,
     *            "startDate": "2018-06-05",
     *            "endDate": "2021-07-01",
     *            "totalRobotDisplay": 36,
     *            "soldRobotDisplay": 0,
     *            "remainingRobotDisplay": 36,
     *            "totalDialogFlow": 999,
     *            "soldDialogFlow": 0,
     *            "remainingDialogFlow": 0,
     *            "billingMethod": "YEARLY",
     *            "managerPhone": null
     *        }
     *     ],
     *    "requestId": "AXGAPZJS",
     *    "resultMsg": "执行成功",
     *    "errorStackTrace": null
     *}
     */
    // @formatter:on
    @GetMapping("/getDistributorInfo")
    public ResultObject getDistributorInfo(@RequestParam(value = "distributorId") Long distributorId) {
        DistributorListVO distributorListVO = distributorService.getDistributorInfo(distributorId);
        return ResultObject.success(distributorListVO);
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/extend  扩容可用话术和机器人
     * @apiName extentDistributor
     * @apiGroup distributor-distributorDialogFlow
     *
     * @apiParam {Long} distributorId                     #代理商ID
     * @apiParam {Integer} robot                #机器人
     * @apiParam {Integer} dialogFlow           #话术
     * @apiParamExample {x-www-form-urlencoded} Request Example
     * {
     * 	"id":1,
     * 	"robot":3
     * 	"dialogFlow":2
     * }
     *
     * @apiSuccessExample {json} Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/extend")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 给代理商[distributorId='+ #distributorId +']进行了扩容操作 ' "
    )
    public ResultObject extendDistributor(@RequestParam(value = "robot", defaultValue = "0", required = false) BigDecimal robot,
                                         @RequestParam(value = "dialogFlow", defaultValue = "0", required = false) Integer dialogFlow,
                                         @RequestParam(value = "distributorId") Long distributorId) {
        Long userId = SecurityUtils.getUserId();
        distributorService.extendDistributor(distributorId, robot, dialogFlow, userId);

        return ResultObject.success(null,"扩容成功！");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/updateStatus     经销商，二级代理商更改账号的状态（启用、禁用）
     * @apiName updateStatus
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId              # 代理商的id.必填
     * @apiParam {boolean} flag                    # 更改状态的标志 ，true: 启用，false:禁用；默认启用true
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateStatus")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 更新了代理商的状态 [distributorId='+ #distributorId +'] ' "
    )
    public ResultObject updateStatus(@RequestParam @NotNull(message = "经销商的id不能为空") Long distributorId,
                                    @RequestParam(required = false, defaultValue = "true") boolean flag) {
        Long updateUser = SecurityUtils.getUserId();
        distributorService.updateStatus(updateUser, distributorId, flag);
        return ResultObject.success(null,"启用/禁用成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/updateChangeAiCycleStatus     经销商，二级代理商更改能否调整AI 服务周期的状态（启用、禁用）
     * @apiName updateChangeAiCycleStatus
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId              # 代理商的id.必填
     * @apiParam {boolean} flag                    # 更改状态的标志 ，true: 启用，false:禁用；默认启用true
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateChangeAiCycleStatus")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 更新了代理商的调整AI 服务周期的状态 [distributorId='+ #distributorId +'] ' "
    )
    public ResultObject updateChangeAiCycleStatus(@RequestParam @NotNull(message = "经销商的id不能为空") Long distributorId,
                                    @RequestParam(required = false, defaultValue = "true") boolean flag) {
        Long updateUser = SecurityUtils.getUserId();
        distributorService.updateChangeAiCycleStatus(updateUser, distributorId, flag);
        return ResultObject.success(null,"启用/禁用AI 服务周期的状态");
    }
// @formatter:off
    /**
     * @api {post} /apiOpe/distributor/updateExamineSubDistributorCustomer     审核分销商新增客户（启用、禁用）
     * @apiName updateExamineSubDistributorCustomer
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId              # 代理商的id.必填
     * @apiParam {boolean} flag                    # 更改状态的标志 ，true: 启用，false:禁用；默认启用true
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": "启用/禁用成功",
     *     "requestId": "PANBDOGU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateExamineSubDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 更新了审核分销商新增客户的状态 [distributorId='+ #distributorId +'] ' "
    )
    public ResultObject updateExamineSubDistributorCustomer(@RequestParam @NotNull(message = "经销商的id不能为空") Long distributorId,
                                    @RequestParam(required = false, defaultValue = "true") boolean flag) {
        Long updateUser = SecurityUtils.getUserId();
        distributorService.updateExamineSubDistributorCustomer(updateUser, distributorId, flag);
        return ResultObject.success(null,"启用/禁用成功");
    }
    // @formatter:off
    /**
     * @api {Get} /apiOpe/distributor/queryDistributorForNewCustomer 获取新建一级代理商客户时一级代理商列表下拉框
     * @apiName queryDistributorForNewCustomer
     * @apiGroup distributor
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *    "code": 200,
     *    "data": [
     *        {
     *            "id": 69,
     *            "parentId": 8,
     *            "name": "西藏二级代理商1",
     *            "contactPerson": "王总",
     *            "contactPhone": "16644444474",
     *            "address": "深圳市",
     *            "robotLimit": null,
     *            "dialogLimit": null,
     *            "customerManager": "客户经理",
     *            "comment": "备注22",
     *            "chargingMethod": null,
     *            "marketScreen": null,
     *            "startDate": "2018-08-02",
     *            "endDate": "2019-08-02",
     *            "totalRobot": 112,
     *            "soldRobot": 0,
     *            "remainingRobot": 112,
     *            "totalDialogFlow": 101,
     *            "soldDialogFlow": 0,
     *            "remainingDialogFlow": 101,
     *            "billingMethod": "MONTHLY",
     *            "managerPhone": null,
     *            "rightToSplit": true,
     *            "status": "CLOSED",
     *            "parentName": "一级代理商"
     *        }
     *     "requestId": "RAAEOBDK",
     *     "resultMsg": "获取成功!",
     *     "errorStackTrace": null
     * }
     *
     */
    // @formatter:on
    @GetMapping(value = "/queryDistributorForNewCustomer")
    public ResultObject queryDistributorForNewCustomer() {
        List<DistributorListVO> distributorList = distributorService.queryCustomerSuperDistributor();
        return ResultObject.success(distributorList, "获取成功!");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributor/resetPassword     ope系统代理商,二级代理商重置密码
     * @apiName resetPassword
     * @apiGroup distributor
     *
     * @apiParam {Long} distributorId                                    # 经销商的id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "重置密码成功",
     *     "requestId": "DVMEQOMU",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/resetPassword")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.DISTRIBUTOR, refIdExpression = "#distributorId",
            contentExpression = " '用户[' + #currentUser.name + '] 重置了代理商 [distributorId='+ #distributorId +'] 的账户密码' "
    )
    public ResultObject resetPassword(@RequestParam Long distributorId) {
        Long updateUser = SecurityUtils.getUserId();
        String defaultPassword = userService.resetDistributorPassword(distributorId, updateUser);
        return ResultObject.success(defaultPassword);
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/searchIdAndNamePairList     搜索代理商名称和id键值对
     * @apiName searchIdAndNamePairList
     * @apiGroup distributor
     *
     * @apiParam {String} company                                    # 代理商类型 DISTRIBUTOR 一级代理商，SUB_DISTRIBUTOR 二级经销商
     * @apiParam {String} searchName                                 # 搜索代理商名称
     * @apiParam {String} searchLinkman                              # 搜索代理商联系人
     * @apiParam {String} searchPhoneNumber                          # 搜索联系电话
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "distributorId": 117,
     *       "name": "不可以发展分销商",
     *       "contactPerson" :"这是联系人"
     *     }
     *   ],
     *   "requestId": "PASFYKDK",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchIdAndNamePairList")
    public ResultObject searchIdAndNamePairList(@RequestParam CompanyEnum company,
                                               @RequestParam(required = false) String searchName,
                                               @RequestParam(required = false) String searchLinkman,
                                               @RequestParam(required = false) String searchPhoneNumber) {
        if (StringUtils.isEmpty(searchLinkman)
                && StringUtils.isEmpty(searchName)
                && StringUtils.isEmpty(searchPhoneNumber)) {

            throw new ComException(ComErrorCode.VALIDATE_ERROR, "搜索字段不能为空");
        }
        return ResultObject.success(distributorService.searchDistributorByCompanyType(company, searchName, searchLinkman, searchPhoneNumber, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/searchDistributorByName     根据名称搜索一级代理商
     * @apiName searchDistributorByName
     * @apiGroup distributor
     *
     * @apiParam {String} keyWords                          # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "distributorId": 117,
     *       "name": "不可以发展分销商",
     *       "contactPerson" :"这是联系人"
     *     }
     *   ],
     *   "requestId": "PASFYKDK",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDistributorByName")
    public ResultObject searchDistributorByName(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(distributorService.searchDistributorByCompanyType(CompanyEnum.DISTRIBUTOR, keyWords, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/searchSubDistributorByName     根据名称搜索二级代理商
     * @apiName searchSubDistributorByName
     * @apiGroup distributor
     *
     * @apiParam {String} keyWords                          # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "distributorId": 117,
     *       "name": "不可以发展分销商",
     *       "contactPerson" :"这是联系人"
     *     }
     *   ],
     *   "requestId": "PASFYKDK",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchSubDistributorByName")
    public ResultObject searchSubDistributorByName(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(distributorService.searchDistributorByCompanyType(CompanyEnum.SUB_DISTRIBUTOR, keyWords, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/searchDistributorByLinkman     根据联系人搜索一级代理商
     * @apiName searchDistributorByLinkman
     * @apiGroup distributor
     *
     * @apiParam {String} keyWords                          # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "distributorId": 117,
     *       "name": "不可以发展分销商",
     *       "contactPerson" :"这是联系人"
     *     }
     *   ],
     *   "requestId": "PASFYKDK",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDistributorByLinkman")
    public ResultObject searchDistributorByLinkman(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(distributorService.searchDistributorByCompanyType(CompanyEnum.DISTRIBUTOR, null, keyWords, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributor/searchSubDistributorByLinkman     根据联系人搜索二级代理商
     * @apiName searchSubDistributorByLinkman
     * @apiGroup distributor
     *
     * @apiParam {String} keyWords                          # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "distributorId": 117,
     *       "name": "不可以发展分销商",
     *       "contactPerson" :"这是联系人"
     *     }
     *   ],
     *   "requestId": "PASFYKDK",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchSubDistributorByLinkman")
    public ResultObject searchSubDistributorByLinkman(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(distributorService.searchDistributorByCompanyType(CompanyEnum.SUB_DISTRIBUTOR, null, keyWords, null, 1, 20));
    }

    /**
     * @api {get} /apiOpe/distributor/updateDistributorAuthenticationStatus    修改代理商资质认证状态
     */
    @PostMapping("updateDistributorAuthenticationStatus")
    public ResultObject searchSubDistributorByLinkman(@RequestBody DistributorPO distributorPO) {
        distributorService.updateNotNull(distributorPO);
        OperationLogPO operationLogPO = new OperationLogPO();
        operationLogPO.setModule(SystemEnum.OPE);
        operationLogPO.setLogType(OperationLogLogTypeEnum.AUTHENTICATION_DISTRIBUTOR_CONFIG);
        operationLogPO.setRefId(distributorPO.getDistributorId());
        operationLogPO.setOperationType(OperationLogOperationTypeEnum.DEFAULT);
        operationLogPO.setCreateUserId(SecurityUtils.getUserId());
        operationLogPO.setCreateTime(LocalDateTime.now());
        operationLogPO.setTenantId(SecurityUtils.getTenantId());
        operationLogPO.setDistributorId(SecurityUtils.getDistributorId());
        operationLogPO.setToBeOptionDistributorId(distributorPO.getDistributorId());
        String ip = SecurityUtils.getRemoteIpAddress();
        operationLogPO.setIpAddress(ip);
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        String requestUri = request.getRequestURI();
        operationLogPO.setApiUrl(requestUri);
        String requestId = MDC.get("MDC_LOG_ID");
        operationLogPO.setRequestId(requestId);
        String description = (EnabledStatusEnum.ENABLE.equals(distributorPO.getCustomerAuthentication())?"开启":"关闭") + "客户资质认证";
        operationLogPO.setDescription(description);
        operationLogService.addToMongo(operationLogPO);
        return ResultObject.success();
    }

    /**
     * 保存代理商配置模板
     * @param distributorConfigModelVO
     * @return
     */
    @PostMapping("/saveDistributorModel")
    public ResultObject saveDistributorModel(@RequestBody DistributorConfigModelVO distributorConfigModelVO) {
        distributorService.saveDistributorModel(distributorConfigModelVO);
        return ResultObject.success();
    }

    /**
     * 修改代理商是否开启在线充值
     */
    @PostMapping("updateEnableRechargeOnline")
    public ResultObject updateEnableRechargeOnline(@RequestBody UpdateDistributorEnableRechargeOnlineVO requestVO) {
        requestVO.setUserId(SecurityUtils.getUserId());
        distributorService.updateEnableRechargeOnline(requestVO);
        return ResultObject.success();
    }

    /**
     * 代理商集团相关配置--集团黑名单设置
     */
    @PostMapping("/updateMainBrandConfig")
    public ResultObject updateMainBrandConfig(@RequestBody DistributorMainBrandConfigVO requestVO) {
        requestVO.setUpdateUserId(SecurityUtils.getUserId());
        distributorService.updateMainBrandConfig(requestVO);
        return ResultObject.success(null, "配置成功");
    }

    /**
     * 判断所选租户是否属于同一个代理商
     * @param req
     * @return
     */
    @PostMapping("/isSameDistributor")
    public ResultObject<DistributorTenantSimpleInfoVO> isSameDistributor(@RequestBody DistributorConfigModelQueryVO req) {
        if (Objects.nonNull(req.getQuery())) {
            req.getQuery().setCurrentLoginUserId(SecurityUtils.getUserId());
            req.getQuery().setIsOpeDistributor(true);
        }
        DistributorTenantSimpleInfoVO sameDistributor = distributorService.isSameDistributor(req);
        return ResultObject.success(sameDistributor);
    }

    /**
     * 批量修改操作
     * @param req
     * @return
     */
    @PostMapping("/batchEditDistributor")
    public ResultObject batchEditDistributor(@RequestBody DistributorConfigModelVO req) {
        if (Objects.nonNull(req.getQuery())) {
            req.getQuery().setCurrentLoginUserId(SecurityUtils.getUserId());
            req.getQuery().setIsOpeDistributor(true);
        }
        JobStartResultVO jobStartResultVO = distributorService.exportFailedTenantId(req, SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResultObject.success(jobStartResultVO);
    }

    /**
     * 获取代理商配置的短信模板
     * @param distributorId
     * @return
     */
    @GetMapping("/getDistributorSmsConfig")
    public ResultObject<List<SmsPlatformChannelDTO>> getDistributorSmsConfig(@RequestParam("distributorId") Long distributorId) {
        List<SmsPlatformChannelDTO> distributorSmsConfig = distributorService.getDistributorSmsConfig(distributorId);
        return ResultObject.success(distributorSmsConfig);
    }

    /**
     * 获取代理商配置模板
     * @return
     */
    @GetMapping("/getDistributorModel")
    public ResultObject<DistributorConfigModelVO> getDistributorModel(@RequestParam("distributorId") Long distributorId) {
        DistributorConfigModelVO modelConfig = distributorService.getModelConfigVO(distributorId);
        return ResultObject.success(modelConfig);
    }

    /**
     * 获取所有一级代理商
     */
    @PostMapping("/getDistributorList")
    public ResultObject<List<DistributorSimpleVO>> getDistributorList(@RequestParam(value = "searchWords", required = false) String searchWords) {
        List<DistributorSimpleVO> distributorList = distributorService.getDistributorList(searchWords);
        return ResultObject.success(distributorList);
    }

}
