package com.yiwise.ope.api.controller.tenant;

import com.yiwise.base.model.annotation.auth.NoLogin;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.base.model.exception.ComErrorCode;
import com.yiwise.base.model.exception.ComException;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.TenantAutoSetPO;
import com.yiwise.core.dal.entity.TenantCallInterceptPO;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.bo.sms.TenantSmsPriceBO;
import com.yiwise.core.model.dto.stats.TenantSimpleInfoDTO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.ope.TenantAutoSetVO;
import com.yiwise.core.model.vo.ope.TenantShowAccountFareVO;
import com.yiwise.core.model.vo.privacynumber.vo.PrivacyNumberConfigVO;
import com.yiwise.core.model.vo.stats.TenantStatsQuery;
import com.yiwise.core.model.vo.tenant.*;
import com.yiwise.core.model.vo.wechat.TenantUpdateSendAddFriendRequestVO;
import com.yiwise.core.service.engine.TenantAutoSetService;
import com.yiwise.core.service.engine.TenantCallInterceptService;
import com.yiwise.core.service.ope.platform.TenantService;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 租户管理
 * <AUTHOR>
 * @Date 2018/11/29
 **/
@Validated
@RestController
@RequestMapping("/apiOpe/tenant")
public class TenantController {
    private static final String wei_xin_start="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=";
    @Resource
    private TenantService tenantService;
    @Resource
    private TenantCallInterceptService tenantCallInterceptService;
    @Resource
    private TenantAutoSetService tenantAutoSetService;

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/setEnableConCurrency    设置是否启用一线多并发
     * @apiName setEnableConCurrency
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                    # 客户id，必填
     * @apiParam {boolean} enableConCurrency                           # 一线多并发状态 必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "设置成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("setEnableConCurrency")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否开启一线多并发的状态，更新的状态为' + #enableConCurrency "
    )
    public ResultObject setEnableConCurrency(@RequestParam @NotNull(message = "客户id不能为空") Long tenantId,
                                            @RequestParam @NotNull(message = "客户id不能为空") Boolean enableConCurrency) {
        tenantService.setEnableConCurrency(tenantId, enableConCurrency);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/setEnableCompressAudio    设置是否压缩音频
     * @apiName setEnableCompressAudio
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                    # 客户id，必填
     * @apiParam {boolean} enableCompressAudio                         # 压缩音频状态
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "设置成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("setEnableCompressAudio")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否压缩音频的状态，更新的状态为' + #enableCompressAudio "
    )
    public ResultObject setEnableCompressAudio(@RequestParam @NotNull(message = "客户id不能为空") Long tenantId, @RequestParam @NotNull(message = "是否压缩音频状态不能为空") Boolean enableCompressAudio) {
        tenantService.setEnableCompressAudio(tenantId, enableCompressAudio);
        return ResultObject.success(null, "设置成功");
    }

     // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/setUseYiwiseAsr    设置是否使用一知ASR
     * @apiName setUseYiwiseAsr
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                    # 客户id，必填
     * @apiParam {boolean} useYiwiseAsr                                # 使用一知ASR
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "设置成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("setUseYiwiseAsr")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否使用一知ASR，更新的状态为' + #useYiwiseAsr "
    )
    public ResultObject setUseYiwiseAsr(@RequestParam @NotNull(message = "客户id不能为空") Long tenantId,
                                       @RequestParam @NotNull(message = "是否一知ASR状态不能为空") Boolean useYiwiseAsr) {
        tenantService.setUseYiwiseAsr(tenantId, useYiwiseAsr);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/setEnableStatsMsgPush    设置是否推送即时统计信息
     * @apiName setEnableStatsMsgPush
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                    # 客户id，必填
     * @apiParam {boolean} enableStatsMsgPush                          # 是否推送即时统计信息
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "设置成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("setEnableStatsMsgPush")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否推送即时统计信息，更新的状态为' + #enableStatsMsgPush "
    )
    public ResultObject setEnableStatsMsgPush(@RequestParam @NotNull(message = "客户id不能为空") Long tenantId,
                                       @RequestParam @NotNull(message = "是否一知ASR状态不能为空") Boolean enableStatsMsgPush) {
        tenantService.setEnableStatsMsgPush(tenantId, enableStatsMsgPush);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/deleteTenant    删除客户
     * @apiName deleteTenant
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                    # 客户id，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "删除成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/deleteTenant")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 删除了客户[tenantId='+ #tenantId +']' "
    )
    public ResultObject deleteTenant(Long tenantId) {
        Long updateUserId = SecurityUtils.getUserId();
        tenantService.deleteByTenantId(tenantId, updateUserId);
        return ResultObject.success("删除成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setEnableCSSeat 设置是否启用人工外呼
     * @apiName setEnableCSSeat
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableCSSeat # 人工外呼设置状态 必填
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableCSSeat")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否启用人工外呼，更新的状态为' + #enableCSSeat "
    )
    public ResultObject setEnableCSSeat(@RequestParam("tenantId") Long tenantId,
                                       @RequestParam("enableCSSeat") Boolean enableCSSeat) {
        tenantService.setEnableCSSeat(tenantId, enableCSSeat);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setCsSeatCount 设置人工坐席数量
     * @apiName setCsSeatCount
     * @apiGroup tenant
     * @apiParam {Long} tenantId        # 客户id，必填
     * @apiParam {Integer} csSeatCount  # 人工坐席数量 必填
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setCsSeatCount")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']人工坐席数量为' + #csSeatCount "
    )
    public ResultObject setCsSeatCount(@RequestParam("tenantId") Long tenantId,
                                      @RequestParam("csSeatCount") Integer csSeatCount) {
        TenantPO tenantPO = new TenantPO();
        tenantPO.setTenantId(tenantId);
        tenantPO.setCsSeatCount(csSeatCount);
        tenantService.setCsSeatCount(tenantPO);
        return ResultObject.success(null, "设置成功");
    }
    /**
     * @api {post} /apiOpe/tenant/setImplemantStatus 设置实施阶段
     * @apiName setImplemantStatus
     * @apiGroup tenant
     * @apiParam {Long} tenantId        # 客户id，必填
     * @apiParam {string} tenantImplementStatus  # 状态 inventory(0, "盘点"),dialog_join(1, "话术对接"),dialog_finalize(2, "话术定稿"),dialog_config(3, "话术配置"),phone_number_config(4, "线路配置"),in_test(5, "内部测试"),formal_ai(6, "上线外呼"),recover(7, "复盘");
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setImplemantStatus")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']实施阶段为' + #tenantImplementStatus "
    )
    public ResultObject setImplemantStatus(@RequestParam("tenantId") Long tenantId,
                                      @RequestParam("tenantImplementStatus") TenantImplementStatusEnum tenantImplementStatus) {
        TenantPO tenantPO = new TenantPO();
        tenantPO.setTenantId(tenantId);
        tenantPO.setTenantImplementStatus(tenantImplementStatus);
        tenantService.updateNotNull(tenantPO);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setEnableAiAssistant 设置是否启用AI助理
     * @apiName setEnableAiAssistant
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableAiAssistant # AI助理 必填
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableAiAssistant")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否启用AI助理，更新的状态为' + #enableAiAssistant "
    )
    public ResultObject setEnableAiAssistant(@RequestParam("tenantId") Long tenantId,
                                            @RequestParam("enableAiAssistant") Boolean enableAiAssistant) {
        tenantService.setEnableAiAssistant(tenantId, enableAiAssistant);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setEnableCsTransfer 设置是否启用人工介入
     * @apiName setEnableCsTransfer
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableCsTransfer # 人工介入-web设置状态 必填
     * @apiParam {boolean} enableCsTransferTel # 人工介入-tel设置状态 必填
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableCsTransfer")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了客户['+ #tenantId +']是否启用人工介入，更新人工介入-web设置状态' + #enableCsTransfer + '-tel设置状态' + #enableCsTransfeTel"
    )
    public ResultObject setEnableCsTransfer(@RequestParam("tenantId") Long tenantId,
                                           @RequestParam("enableCsTransfer") Boolean enableCsTransfer,
                                           @RequestParam("enableCsTransferTel") Boolean enableCsTransfeTel) {
        tenantService.setEnableCsTransfer(tenantId, enableCsTransfer, enableCsTransfeTel);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/enableCallIn    更新客户是否启用呼入
     * @apiName enableCallIn
     * @apiGroup tenant
     *
     * @apiParam {Long}    tenantId                                   # 客户id，必填
     * @apiParam {Boolean} status                                     # 是否启用状态，true为启用，false为禁用，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "操作专攻",
     *     "requestId": null,
     *     "resultMsg": "操作人成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/enableCallIn")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']是否启用呼入的状态，更新的状态为' + #status  "
    )
    public ResultObject enableCallIn(@NotNull(message = "客户id不能为空") Long tenantId,
                                    @NotNull(message = "启用状态不能为空") Boolean status) {
        Long updateUserId = SecurityUtils.getUserId();
        tenantService.enableCallIn(tenantId, status, updateUserId);
        return ResultObject.success("操作成功");
    }

     /**
     * @api {post} /apiOpe/tenant/setEnableVoiceQualityControl 设置是否启用语音质检
     * @apiName setEnableVoiceQualityControl
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableVoiceQualityControl # 语音质检，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableVoiceQualityControl")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']是否启用语音质检，更新的状态为' + #enableVoiceQualityControl  "
    )
    public ResultObject setEnableVoiceQualityControl(@RequestParam("tenantId") Long tenantId,
                                                    @RequestParam("enableVoiceQualityControl") Boolean enableVoiceQualityControl){
        tenantService.setEnableVoiceQualityControl(tenantId, enableVoiceQualityControl, SecurityUtils.getUserId());
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setQcCostUnit 设置语音质检费用
     * @apiName setQcCostUnit
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {Long} qcCostUnit # 语音质检，必填
     * @apiParam {Long} qcTextCostUnit # 语音质检，必填
     * @apiParam {Boolean} enableQcTagThreshold # 语音质检，必填
     * @apiParam {Double} qcTagThreshold # 语音质检，必填
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setQcCostUnit")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']语音质检费用为' + #qcCostUnit  "
    )
    public ResultObject setQcCostUnit(@RequestParam("tenantId") Long tenantId,
                                     @RequestParam("qcCostUnit") Long qcCostUnit,
                                     @RequestParam("qcTextCostUnit") Long qcTextCostUnit,
                                     @RequestParam("enableQcTagThreshold") Boolean enableQcTagThreshold,
                                     @RequestParam("qcTagThreshold") Double qcTagThreshold
                                     ){
        Long userId = SecurityUtils.getUserId();
        tenantService.setQcCostUnit(tenantId, qcCostUnit, qcTextCostUnit, enableQcTagThreshold, qcTagThreshold, userId);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchAllTenant    搜索所有客户
     * @apiName searchAllTenant
     * @apiGroup tenant
     *
     * @apiParam {String}   keyWords                                   # 关键词搜索，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *   "code": 200,
     *   "data": [
     *     {
     *       "tenantId": 1,
     *       "name": "邹静在找的大boss",
     *       "linkman":"这是联系人"
     *     }
     *   ],
     *   "requestId": "KDDQSRWE",
     *   "resultMsg": "执行成功",
     *   "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/searchAllTenant")
    public ResultObject searchAllTenant(String keyWords,
                                       @RequestParam(required = false, defaultValue = "20") Integer count,
                                       @RequestParam(required = false) TenantPayTypeEnum tenantPayType,
                                       @RequestParam(required = false) Integer customerTrackType,
                                       @RequestParam(required = false) Integer customerTrackSubType) {
        return ResultObject.success(tenantService.searchAllTenant(keyWords, count, tenantPayType, customerTrackType, customerTrackSubType));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchTenantForSelectList    搜索客户名称
     * @apiName searchTenantForSelectList
     * @apiGroup tenant
     *
     * @apiParam {String}    company                                   # 客户类型 DIRECT_CUSTOMER 直销客户，DISTRIBUTOR_CUSTOMER 一级代理商客户， SUB_DISTRIBUTOR_CUSTOMER 二级代理商客户
     * @apiParam {String}    searchCompanyName                         # 搜索客户名称
     * @apiParam {String}    searchLinkman                             # 搜索客户联系人
     * @apiParam {String}    searchPhoneNumber                         # 联系电话
     * @apiParam {String}    searchSubAccountPhoneNumber                     # 子账号电话
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchTenantForSelectList")
    public ResultObject searchTenantForSelectList(@RequestParam CompanyEnum company,
                                                 @RequestParam(required = false) String searchCompanyName,
                                                 @RequestParam(required = false) String searchLinkman,
                                                 @RequestParam(required = false) String searchPhoneNumber,
                                                 @RequestParam(required = false) String searchSubAccountPhoneNumber) {
        if (StringUtils.isBlank(searchCompanyName)
                && StringUtils.isBlank(searchLinkman)
                && StringUtils.isBlank(searchPhoneNumber)
                && StringUtils.isBlank(searchSubAccountPhoneNumber)) {
            throw new ComException(ComErrorCode.VALIDATE_ERROR, "搜索字段不能为空");
        }
        return ResultObject.success(tenantService.searchTenantForSelectList(company, searchCompanyName, searchLinkman, searchPhoneNumber, searchSubAccountPhoneNumber, 1, 20));
    }


    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchDirectTenantByName    根据名称搜索直销客户
     * @apiName searchDirectTenantByName
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDirectTenantByName")
    public ResultObject searchDirectTenantByName(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.DIRECT_CUSTOMER, keyWords, null, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchDistributorTenantByName    根据名称搜索代理商客户
     * @apiName searchDistributorTenantByName
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *              "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDistributorTenantByName")
    public ResultObject searchDistributorTenantByName(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.DISTRIBUTOR_CUSTOMER, keyWords, null, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchSubDistributorTenantByName    根据名称搜索二级代理商客户
     * @apiName searchSubDistributorTenantByName
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchSubDistributorTenantByName")
    public ResultObject searchSubDistributorTenantByName(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.SUB_DISTRIBUTOR_CUSTOMER, keyWords, null, null, null, 1, 20));
    }


    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchDirectTenantByLinkman    根据联系人搜索直销客户
     * @apiName searchDirectTenantByLinkman
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDirectTenantByLinkman")
    public ResultObject searchDirectTenantByLinkman(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.DIRECT_CUSTOMER, null, keyWords, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchDistributorTenantByLinkman    根据联系人搜索代理商客户
     * @apiName searchDistributorTenantByLinkman
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchDistributorTenantByLinkman")
    public ResultObject searchDistributorTenantByLinkman(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.DISTRIBUTOR_CUSTOMER, null, keyWords, null, null, 1, 20));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/searchSubDistributorTenantByLinkman    根据联系人搜索二级代理商客户
     * @apiName searchSubDistributorTenantByLinkman
     * @apiGroup tenant
     *
     * @apiParam {String}    keyWords                                   # 搜索关键字
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": [
     *         {
     *             "tenantId": 109,
     *             "name": "众纳测试openapi用户",
     *             "linkman":"这是联系人"
     *         }
     *     ],
     *     "requestId": "NOEZUMVX",
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("searchSubDistributorTenantByLinkman")
    public ResultObject searchSubDistributorTenantByLinkman(@RequestParam(defaultValue = "") String keyWords) {
        return ResultObject.success(tenantService.searchTenantForSelectList(CompanyEnum.SUB_DISTRIBUTOR_CUSTOMER, null, keyWords, null, null, 1, 20));
    }

// @formatter:off
    /**
     * @api {post} /apiOpe/tenant/setEnabledViewIntent 设置是否启用查看未识别意图列表
     * @apiName setEnabledViewIntent
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enabledViewIntent # 是否启用  false：关闭 true: 开启
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": "DCQVIXOV",
     *     "resultMsg": "设置成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("setEnabledViewIntent")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']是否启用查看未识别意图列表，更新的状态为' + #enabledViewIntent  "
    )
    public ResultObject setEnabledViewIntent(@RequestParam("tenantId") Long tenantId,
                                            @RequestParam("enabledViewIntent") Boolean enabledViewIntent) {
        tenantService.setEnabledViewIntent(tenantId, enabledViewIntent);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setEnableIvr 设置是否启用ivr
     * @apiName setEnableIvr
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableIvr # 是否启用ivr，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableIvr")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']是否启用ivr，更新的状态为' + #enableIvr  "
    )
    public ResultObject setEnableIvr(@RequestParam("tenantId") Long tenantId, @RequestParam("enableIvr") Boolean enableIvr){
        tenantService.setEnableIvr(tenantId, enableIvr);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * 设置是否启用独立设置短信单价
     */
    @PostMapping("setEnableSmsPrice")
    public ResultObject setEnableSmsPrice(@RequestBody TenantSmsPriceBO tenantSmsPriceBO) {
        tenantService.setEnableSmsPrice(tenantSmsPriceBO, SecurityUtils.getUserId());
        return ResultObject.success();
    }


    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/setEnableTaskWaitingTimeout 设置是否启用自定义任务超时时长
     * @apiName setEnableTaskWaitingTimeout
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {boolean} enableTaskWaitingTimeout # 是否启用任务超时时长，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("setEnableTaskWaitingTimeout")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']是否启用自定义任务超时时长，更新的状态为' + #enableTaskWaitingTimeout  "
    )
    public ResultObject setEnableTaskWaitingTimeout(@RequestParam("tenantId") Long tenantId, @RequestParam("enableTaskWaitingTimeout") Boolean enableTaskWaitingTimeout){
        tenantService.updateTenantEnableTaskWaitingTimeout(tenantId, enableTaskWaitingTimeout);
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/setDialInterval 设置可拨打时段
     * @apiName setDialInterval
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {LocalTime} dialIntervalStart # 时段开始时间，必填
     * @apiParam {LocalTime} dialIntervalEnd # 时段结束时间，必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("setDialInterval")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']可拨打时段start为' + #dialIntervalStart + 'end为' +  #dialIntervalEnd "
    )
    public ResultObject setDialInterval(@RequestParam("tenantId") Long tenantId,
                                       @RequestParam("dialIntervalStart") LocalTime dialIntervalStart,
                                       @RequestParam("dialIntervalEnd") LocalTime dialIntervalEnd){
        tenantService.setDialInterval(tenantId, dialIntervalStart, dialIntervalEnd);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {get} /apiOpe/tenant/interceptInfo 呼叫拦截详情
     * @apiName interceptInfo
     * @apiGroup tenant
     * @apiParam {Long} tenantId
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": {
     *     "callOutRestrict":"YES", #外呼上限 YES-是,  NO - 否
     *     "callOutDays":1,         #外呼时间内天数
     *     "callOutCount":1,        #外呼次数上限
     *     "phoneNumberAbnormal":"YES", #号码异常检测 YES-是,  NO - 否
     *     "notExistDays":1,        #空号天数内
     *     "notServiceDays":1,      #停机天数内
     *     "phoneNumberLocation":"YES",  #归属限制 YES-是,  NO - 否
     * },
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @GetMapping("/interceptInfo")
    @ResponseBody
    public ResultObject<List<TenantCallInterceptPO>> getTenantInterceptInfo(@RequestParam Long tenantId){
        List<TenantCallInterceptPO> intercepts = tenantCallInterceptService.selectSystemInterceptsByTenantId(tenantId);
        return ResultObject.success(intercepts);
    }

    /**
     * /apiOpe/tenant/updateIntercept 新建或更新呼叫拦截详情
     */
    // @formatter:on
    @PostMapping("/updateIntercept")
    @ResponseBody
    public ResultObject<Void> updateTenantIntercept(@RequestBody TenantCallInterceptPO tenantCallInterceptPO){
        tenantCallInterceptPO.setUpdateUserId(SecurityUtils.getUserId());
        tenantCallInterceptPO.setSource(TenantCallInterceptSourceEnum.SYSTEM);
        tenantCallInterceptService.updateTenantIntercept(tenantCallInterceptPO);
        return ResultObject.success(null, "编辑成功");
    }

    /**
     * /apiOpe/tenant/deleteTenantIntercept 删除呼叫拦截规则
     */
    @GetMapping("/deleteTenantIntercept")
    public ResultObject<Void> deleteTenantIntercept(@NotNull(message = "拦截规则id不能为空") @RequestParam Long tenantCallInterceptId){
        tenantCallInterceptService.deleteTenantCallIntercept(SecurityUtils.getTenantId(), tenantCallInterceptId);
        return ResultObject.success(null, "删除成功");
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/getTenantAllLineDetail    首页获取tenant整体设置信息
     * @apiName getTenantAllLineDetail
     * @apiGroup tenant
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping(value = "/getTenantAllLineDetail")
    public ResultObject getTenantAllLineDetail(@RequestParam("tenantId") Long tenantId) {
        TenantAutoSetVO tenantAutoSetVO=new TenantAutoSetVO();
        TenantAutoSetPO tenantAutoSetPO= tenantAutoSetService.selectAllLineByTenant(tenantId);
        if(Objects.nonNull(tenantAutoSetPO)){
            BeanUtils.copyProperties(tenantAutoSetPO,tenantAutoSetVO);
        }
        return ResultObject.success(tenantAutoSetVO,"操作成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/createOrUpdateTenantAllLine    新建或者修改 tenant 整体的信息
     * @apiName tenantAutoSet
     * @apiGroup createOrUpdateTenantAllLine
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *          "autoWarnMessageReceiver":[1,2,3,3]       # 预警消息推送到群后的接收人user_id
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/createOrUpdateTenantAllLine")
    public ResultObject createOrUpdateTenantAllLine(@RequestBody TenantAutoSetPO tenantAutoSet) {
        Long userid= SecurityUtils.getUserId();
        tenantAutoSet.setUpdateUserId(userid);
        if(StringUtils.isNotBlank(tenantAutoSet.getWeiXinUrl())&&!tenantAutoSet.getWeiXinUrl().startsWith(wei_xin_start)){
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR,"设置的微信群地址不对");
        }
        if(Objects.isNull(tenantAutoSet.getTenantAutoSetId())){
            tenantAutoSetService.saveNotNull(tenantAutoSet);
        }else {
            tenantAutoSetService.updateNotNull(tenantAutoSet);
        }
        return ResultObject.success("操作成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/getTenantAllAccountDetail    首页获取tenant 账户余额信息
     * @apiName getTenantAllAccountDetail
     * @apiGroup tenant
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping(value = "/getTenantAllAccountDetail")
    public ResultObject getTenantAllAccountDetail(@RequestParam("tenantId") Long tenantId) {
        TenantAutoSetVO tenantAutoSetVO=new TenantAutoSetVO();
        TenantAutoSetPO tenantAutoSetPO= tenantAutoSetService.selectAllAccountByTenant(tenantId);
        if(Objects.nonNull(tenantAutoSetPO)){
            BeanUtils.copyProperties(tenantAutoSetPO,tenantAutoSetVO);
        }
        return ResultObject.success(tenantAutoSetVO,"操作成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/createOrUpdateTenantAllAccount   新建或者修改 tenant 账户余额的信息
     * @apiName tenantAutoSet
     * @apiGroup createOrUpdateTenantAllAccount
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/createOrUpdateTenantAllAccount")
    public ResultObject createOrUpdateTenantAllAccount(@RequestBody TenantAutoSetPO tenantAutoSet) {
        Long userid= SecurityUtils.getUserId();
        tenantAutoSet.setUpdateUserId(userid);
        if(StringUtils.isNotBlank(tenantAutoSet.getWeiXinUrl())&&!tenantAutoSet.getWeiXinUrl().startsWith(wei_xin_start)){
            return ResultObject.fail(ComErrorCode.VALIDATE_ERROR,"设置的微信群地址不对");
        }
        if(Objects.nonNull(tenantAutoSet.getAutoWarnGateFareOpe())){
            tenantAutoSet.setAutoWarnGateFareOpe(tenantAutoSet.getAutoWarnGateFareOpe()*1000);
        }
        if(Objects.isNull(tenantAutoSet.getTenantAutoSetId())){
            tenantAutoSetService.saveNotNull(tenantAutoSet);
        }else {
            tenantAutoSetService.updateNotNull(tenantAutoSet);
        }
        return ResultObject.success("操作成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/createOrUpdateTenantMessage    新建或者修改状态 短信设置
     * @apiName tenant
     * @apiGroup createOrUpdateTenantMessage
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping(value = "/createOrUpdateTenantMessage")
    public ResultObject createOrUpdateTenantMessage(@RequestBody TenantAutoSetPO tenantAutoSet) {
        Long userid= SecurityUtils.getUserId();
        tenantAutoSet.setUpdateUserId(userid);
        if(Objects.isNull(tenantAutoSet.getTenantAutoSetId())){
            tenantAutoSetService.saveNotNull(tenantAutoSet);
        }else {
            tenantAutoSetService.updateNotNull(tenantAutoSet);
        }
        return ResultObject.success("操作成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/tenant/getAutoSetMessageDetail    首页获取短信的具体信息
     * @apiName getAutoSetMessageDetail
     * @apiGroup tenant
     * @apiParamExample {json} Request Example
     * {
     *           tenantId         # id
     *          "type":"Line"                         # Line(0, "线路"),MESSAGE(1, "短信"),QC(2, "质检");
     *          "enabledAutoPayAccount":1                         # 是否自动续费
     *          "enabledAutoWarn":1                        # 是否余额预警
     *          "tenantPhoneNumberId":11
     *          "autoPayGateFare":50                        # 自动续费阈值
     *          "autoWarnGateFare":50                         # 自动预警阈值
     *          "autoPayFare":50                         # 续费金额
     *          "autoWarnMessageUsers":[1,2,3,3]                        # 预警消息推送 userid
     *          "autoWarnWeixinUsers":[1,2,3,3]                        # 微信消息推送 userid
     *
     * }


     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": "GVEGQUJS",
     *     "resultMsg": "新建成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping(value = "/getAutoSetMessageDetail")
    public ResultObject getAutoSetMessageDetail(@RequestParam("tenantId") Long tenantId) {
        TenantAutoSetPO tenantAutoSetPO= tenantAutoSetService.selectMessageByTenant(tenantId);
        return ResultObject.success(tenantAutoSetPO,"操作成功");
    }


    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/setHotWordCount 设置热词数量
     * @apiName setHotWordCount
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {int} hotWordCount # 热词数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("setHotWordCount")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']热词数量为' + #hotWordCount "
    )
    public ResultObject setHotWordCount(@RequestParam("tenantId") Long tenantId,
                                       @RequestParam("hotWordCount") Integer hotWordCount){
        tenantService.setHotWordCount(tenantId, hotWordCount, SecurityUtils.getUserId());
        return ResultObject.success(null, "设置成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/tenant/setAsrSelfLearningModelCount 设置自学习模型数量
     * @apiName setAsrSelfLearningModelCount
     * @apiGroup tenant
     *
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {int} asrSelfLearningModelCount # 自学习模型数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("setAsrSelfLearningModelCount")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 变更了客户[tenantId='+ #tenantId +']自学习模型数量为' + #asrSelfLearningModelCount "
    )
    public ResultObject setAsrSelfLearningModelCount(@RequestParam("tenantId") Long tenantId,
                                       @RequestParam("asrSelfLearningModelCount") Integer asrSelfLearningModelCount){
        tenantService.setAsrSelfLearningModelCount(tenantId, asrSelfLearningModelCount, SecurityUtils.getUserId());
        return ResultObject.success(null, "设置成功");
    }
    /**
     * @api {post} /apiOpe/tenant/setTenantAccountStatus 设置客户类型
     * @apiName setTenantAccountStatus
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {string} tenantAccountStatus # 客户类型 S A B C
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setTenantAccountStatus")
    public ResultObject setTenantAccountStatus(@RequestParam("tenantId") Long tenantId,
                                           @RequestParam("tenantAccountStatus") TenantAccountStatusEnum tenantAccountStatus) {
        tenantService.setTenantAccountStatus(tenantId, tenantAccountStatus,SecurityUtils.getUserId());
        return ResultObject.success(null, "设置成功");
    }

    /**
     * /apiOpe/tenant/updateSendAddFriendStatus 设置发送微信加好友信息类型
     */
    @PostMapping("updateSendAddFriendStatus")
    public ResultObject<Void> updateSendAddFriendStatus(@RequestBody TenantUpdateSendAddFriendRequestVO request) {
        tenantService.updateSendAddFriendStatus(request, SecurityUtils.getUserId());
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/selectByPayType 根据付费类型搜索tenant
     * @apiName selectByPayType
     * @apiGroup tenant
     * @apiParam {string} tenantPayType # 付费类型SUBSCRIBE(0, "订阅制付费"), PIECE(1, "按接通付费"), MINUTE(2, "按分钟付费"),
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("selectByPayType")
    public ResultObject selectByPayType(
                                           @RequestParam("tenantPayType") @NotNull TenantPayTypeEnum tenantPayType,
                                           @RequestParam(value = "tenantName" ,required = false) String tenantName,
                                           @RequestParam(required = false, defaultValue = "1")Integer pageNum,
                                           @RequestParam(required = false, defaultValue = "20")Integer pageSize
                                           ) {
        return ResultObject.success(tenantService.selectByPayType(tenantPayType,tenantName,pageNum,pageSize), "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setTenantSkipPhoneValidation 设置tenant在批量导入用户时是否进行电话号码格式校验
     * @apiName setTenantSkipPhoneValidation
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {Boolean} skipPhoneValidation # 是否进行电话号码格式校验
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setTenantSkipPhoneValidation")
    public ResultObject setTenantSkipPhoneValidation(
                                                    @RequestParam("tenantId") Long tenantId,
                                                    @RequestParam("skipPhoneValidation") Boolean skipPhoneValidation
                                                    ) {
        tenantService.setTenantSkipPhoneValidation(tenantId, skipPhoneValidation);
        return ResultObject.success(null, "设置成功");
    }


    /**
     * @api {post} /apiOpe/tenant/setEnableAiEngine 设置是否开始aiEngine
     * @apiName setEnableAiEngine
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {Boolean} enableAiEngine # 是否开启ai引擎
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableAiEngine")
    public ResultObject setEnableAiEngine(@RequestParam("tenantId") Long tenantId,
                                         @RequestParam("enableAiEngine") Boolean enableAiEngine) {
        tenantService.setEnableAiEngine(tenantId, enableAiEngine);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * @api {post} /apiOpe/tenant/setEnableYiwiseBot 设置是否开启yiwiseBot
     * @apiName setEnableYiwiseBot
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {Boolean} enableYiwiseBot # 是否开启yiwiseBot
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableYiwiseBot")
    public ResultObject setEnableYiwiseBot(@RequestParam("tenantId") Long tenantId,
                                         @RequestParam("enableYiwiseBot") Boolean enableYiwiseBot) {
        tenantService.setEnableYiwiseBot(tenantId, enableYiwiseBot);
        return ResultObject.success(null, "设置成功");
    }

    @PostMapping("setYiwiseBotViewAuth")
    public ResultObject<Void> setYiwiseBotViewAuth(@RequestParam("tenantId") Long tenantId,
                                                   @RequestParam("enableCustomerViewBot") Boolean enableCustomerViewBot,
                                                   @RequestParam("enableOpeRoleViewBot") Boolean enableOpeRoleViewBot) {
        tenantService.setYiwiseBotViewAuth(tenantId, enableCustomerViewBot, enableOpeRoleViewBot);
        return ResultObject.success(null);
    }

    /**
     * 设置客户隐私号报价
     */
    @PostMapping("setEnablePrivacyNumber")
	public ResultObject setEnablePrivacyNumber(@RequestBody PrivacyNumberConfigVO config) {
        config.setUpdateUserId(SecurityUtils.getUserId());
		tenantService.setEnablePrivacyNumber(config);
		return ResultObject.success(null, "设置成功");
	}

    /**
     * @api {post} /apiOpe/tenant/setEnableStandardDialogFlow 设置是否开始aiEngine
     * @apiName setEnableStandardDialogFlow
     * @apiGroup tenant
     * @apiParam {Long} tenantId # 客户id，必填
     * @apiParam {Boolean} enableAiEngine # 是否开启ai引擎
     * @apiSuccessExample Response 200 Example
     * {
     * "code": 200,
     * "data": null,
     * "requestId": null,
     * "resultMsg": "设置成功",
     * "errorStackTrace": null
     * }
     */
    @PostMapping("setEnableStandardDialogFlow")
    public ResultObject setEnableStandardDialogFlow(@RequestParam("tenantId") Long tenantId,
                                                   @RequestParam("enableStandardDialogFlow") Boolean enableStandardDialogFlow) {
        tenantService.setEnableStandardDialogFlow(tenantId, enableStandardDialogFlow);
        return ResultObject.success(null, "设置成功");
    }

    @GetMapping("updateCallOutBlackList")
    public ResultObject updateCallOutBlackList(@RequestParam("tenantId") Long tenantId, @RequestParam(value = "callOutBlackListIds", required = false) Set<Long> callOutBlackListIds) {
        tenantService.updateCallOutBlackList(tenantId, callOutBlackListIds);
        return ResultObject.success(null, "设置成功");
    }

    @PostMapping("setSmsChannel")
    public ResultObject setTenantSmsChannel(@RequestBody TenantSmsChannelVO tenantSmsChannelVO) {
        tenantService.setTenantSmsChannel(tenantSmsChannelVO);
        return ResultObject.success("设置成功");
    }

    @GetMapping("setOverdraft")
    public ResultObject setOverdraft(@RequestParam("tenantId") Long tenantId,
                                    @RequestParam(value = "enableOverdraft", required = false) boolean enableOverdraft,
                                    @RequestParam(value = "creditLine", required = false) Long creditLine,
                                    @RequestParam OverdraftDurationEnum overdraftDuration) {
        return ResultObject.success(tenantService.setOverdraft(tenantId, enableOverdraft, creditLine, overdraftDuration), "设置成功");
    }

    @GetMapping("updateEnableLoginSecond")
    public ResultObject updateEnableLoginSecond(@RequestParam("tenantId") Long tenantId, @RequestParam(value = "enableLoginSecond") Boolean enableLoginSecond) {
        tenantService.updateEnableLoginSecond(tenantId, enableLoginSecond);
        return ResultObject.success(null, "设置成功");
    }


    @PostMapping("/getTenantSimpleInfoList")
    public ResultObject getTenantSimpleInfoList(@RequestBody TenantStatsQuery condition){
        if(Objects.nonNull(condition)&&condition.getCsmUserId()!=null){
            List<TenantSimpleInfoDTO> tenantSimpleInfoDTOS = tenantService.getTenantSimpleInfoList(condition);
            return ResultObject.success(tenantSimpleInfoDTOS,"获取成功");
        }
        return ResultObject.success(null,"获取成功");
    }

    @GetMapping("updateEnableRechargeOnline")
    public ResultObject updateEnableRechargeOnline(@RequestParam("tenantId") Long tenantId, @RequestParam(value = "enableRechargeOnline") Boolean enableRechargeOnline) {
        tenantService.updateEnableRechargeOnline(tenantId, enableRechargeOnline);
        return ResultObject.success(null, "设置成功");
    }

    /**
     * 设置短信的显示单价
     */
    @PostMapping("setSmsShowPrice")
    public ResultObject setSmsShowPrice(@RequestBody TenantSmsShowPriceVO smsShowPriceVO) {
        tenantService.setTenantSmsShowPrice(smsShowPriceVO);
        return ResultObject.success("设置成功");
    }

    /**
     * 查看短信设置的单价 返回单价（元）
     */
    @GetMapping("getSmsShowPrice")
    public ResultObject<TenantSmsShowPriceVO> getSmsShowPrice(@RequestParam Long tenantId) {
        Long priceLi = tenantService.getTenantSmsShowPrice(tenantId);
        return ResultObject.success(TenantSmsShowPriceVO.builder()
                .price(Objects.nonNull(priceLi) ? BigDecimal.valueOf(priceLi).divide(BigDecimal.valueOf(1000)).doubleValue() : null)
                .status(Objects.nonNull(priceLi) ? CommonStatusEnum.START_UP : CommonStatusEnum.BLOCK_UP)
                .build());
    }

	/**
	 * 回调转发服务查询tenant的mainBrandId
	 */
	@NoLogin
    @GetMapping("getMainBrandId")
	public ResultObject<Long> getMainBrandId(@RequestParam Long tenantId) {
		TenantPO tenant = tenantService.selectByKey(tenantId);
		if (tenant == null) {
			return ResultObject.success(null);
		}
		return ResultObject.success(tenant.getNewMainBrandId());
	}

	/**
	 * 租户改为使用新版计费服务
	 */
    @GetMapping("usingNewBillingService")
	public ResultObject<Void> usingNewBillingService(@RequestParam Long tenantId) {
		tenantService.usingNewBillingService(tenantId);
		return ResultObject.success(null, "升级成功");
	}

	/**
	 * 更新客户AICC展示账户余额的选项
	 * /apiOpe/tenant/updateShowAccountFare
	 */
    @PostMapping("updateShowAccountFare")
	public ResultObject<Void> updateShowAccountFare(@RequestBody TenantShowAccountFareVO tenantShowAccountFare) {
		tenantService.updateShowAccountFare(tenantShowAccountFare);
		return ResultObject.success(null, "更新成功");
	}

    /**
     * 短信模板免审核设置
     * @param tenantSkipSmsTemplateReviewVO
     * @return
     */
    @PostMapping("setSkipSmsTemplateReview")
    public ResultObject setSkipSmsTemplateReview(@RequestBody TenantSkipSmsTemplateReviewVO tenantSkipSmsTemplateReviewVO) {
        tenantSkipSmsTemplateReviewVO.setCurrentUserId(SecurityUtils.getUserId());
        tenantService.setTenantSkipSmsTemplateReview(tenantSkipSmsTemplateReviewVO);
        return ResultObject.success();
    }

    /**
     * 客户性别识别功能开关
     *
     * @param tenantId
     * @param enableGenderRecognition
     * @return
     */
    @GetMapping("updateEnableGenderRecognition")
    public ResultObject<Void> updateEnableGenderRecognition(@RequestParam("tenantId") Long tenantId,
                                                     @RequestParam("enableGenderRecognition") Boolean enableGenderRecognition){
        tenantService.updateEnableGenderRecognition(tenantId,enableGenderRecognition);
        return ResultObject.success(null,"更新成功");
    }

    /**
     * 客户外呼版本控制
     * @return
     */
    @PostMapping("updateEnableTenantCallOutVersion")
    public ResultObject<Void> updateEnableTenantCallOutVersion(@RequestBody CallOutVersionVO callOutVersionVO){
        tenantService.updateEnableTenantCallOutVersion(callOutVersionVO.getTenantId(),callOutVersionVO.getNewCallOut(),callOutVersionVO.getEnable());
        return ResultObject.success(null,"更新成功");
    }

    /**
     * 客户视频外呼开关
     * @return
     */
    @PostMapping("updateEnableVideoCallOut")
    public ResultObject<Void> updateEnableVideoCallOut(@RequestBody VideoCallOutEnableVO callOutEnableVO){
        tenantService.updateEnableVideoCallOut(callOutEnableVO.getTenantId(),callOutEnableVO.getEnable());
        return ResultObject.success(null,"更新成功");
    }

    /**
     * 更新视频外呼配置
     * @param videoConfigVO
     * @return
     */
    @PostMapping("updateVideoConfig")
    public ResultObject<Void> updateVideoConfig(@RequestBody VideoConfigVO videoConfigVO){
        videoConfigVO.setUserId(SecurityUtils.getUserId());
        tenantService.updateVideoConfig(videoConfigVO);
        return ResultObject.success(null,"更新成功");
    }

    /**
     * 获取视频外呼配置
     * @param tenantId
     * @return
     */
    @GetMapping("getVideoConfig")
    public ResultObject<VideoConfigVO> getVideoConfig(@RequestParam("tenantId")Long tenantId){
        VideoConfigVO videoConfigVO = tenantService.getVideoConfig(tenantId);
        return ResultObject.success(videoConfigVO);
    }

    /**
     * 更新机型识别配置
     * @param identifyConfigVO
     * @return
     */
    @PostMapping("updateModelIdentify")
    public ResultObject<Void> updateModelIdentify(@RequestBody IdentifyConfigVO identifyConfigVO){
        tenantService.updateModelIdentify(identifyConfigVO);
        return ResultObject.success(null,"更新成功");
    }


    /**
     * 获取机型识别配置
     * @param tenantId
     * @return
     */
    @PostMapping("getModelIdentify")
    public ResultObject<IdentifyConfigVO> getModelIdentify(@RequestParam("tenantId")Long tenantId){
        IdentifyConfigVO identifyConfigVO = tenantService.getModelIdentify(tenantId);
        return ResultObject.success(identifyConfigVO);
    }

    /**
     * 更新客户自定义黑名单配置
     * @param customBlackConfigVO
     * @return
     */
    @PostMapping("/updateCustomBlackConfig")
    public ResultObject updateCustomBlackConfig(@RequestBody CustomBlackConfigVO customBlackConfigVO) {
        tenantService.updateCustomBlackConfig(customBlackConfigVO);
        return ResultObject.success();
    }

    /**
     * 更新租户通话时长限制
     * @param tenantId 租户 id
     * @param taskTalkingTimeoutMinutes 时长限制, 单位分钟
     * @return
     */
    @PostMapping("updateTaskTalkingTimeout")
    public ResultObject<Object> updateTaskTalkingTimeout(@NotNull Long tenantId,
                                                 @NotNull Integer taskTalkingTimeoutMinutes) {
        tenantService.updateTaskTalkingTimeout(tenantId, taskTalkingTimeoutMinutes);
        return ResultObject.success();
    }
}
