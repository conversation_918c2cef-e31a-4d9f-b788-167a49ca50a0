package com.yiwise.ope.api.controller.customer;

import cn.hutool.core.util.StrUtil;
import com.yiwise.base.model.bean.PageResultObject;
import com.yiwise.base.model.bean.ResultObject;
import com.yiwise.core.aop.annotation.SimpleOperationLog;
import com.yiwise.core.dal.entity.TenantPO;
import com.yiwise.core.model.enums.*;
import com.yiwise.core.model.vo.batch.JobStartResultVO;
import com.yiwise.core.model.vo.boss.DirectCustomerQueryVO;
import com.yiwise.core.model.vo.boss.DistributorCustomerQueryVO;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerFunctionInfo;
import com.yiwise.core.model.vo.distributorcustomer.DistributorCustomerOpenFunctionQueryVO;
import com.yiwise.core.model.vo.ope.CustomerInsertVO;
import com.yiwise.core.model.vo.ope.CustomerUpdateVO;
import com.yiwise.core.service.ope.platform.*;
import com.yiwise.core.service.platform.UserService;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerInsertValidate;
import com.yiwise.core.validate.ope.distributorCustomer.DistributorCustomerOpenFunctionValidate;
import com.yiwise.springboot.config.interceptor.token.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;

/**
 * @ClassName DistributorCustomerController
 * @Description 经销商客户
 * <AUTHOR>
 * @Date 2018 8 20 13:11
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/apiOpe/distributorCustomer")
public class DistributorCustomerController {

    @Resource
    private DistributorCustomerService distributorCustomerService;
    @Resource
    private DirectCustomerService directCustomerService;
    @Resource
    private UserService userService;
    @Resource
    private TenantService tenantService;

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/addDistributorCustomer    添加经销商客户
     * @apiName addDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantName" : "阿里巴巴集团",                  # 客户名，必填
     *     "linkman" : "马云",                             # 联系人 ，必填
     *     "phoneNumber" : "***********",                  # 联系方式 ，必填
     *     "password":"password",                           # 密码
     *     "dialogFlowIds" : [1,4],                        # 话术id的集合
     *     "aiConcurrencyLevel" : 10,                      # AI座席数,必填
     *     "startTime" : "2018-08-20",                     # 服务开始时间,必填
     *     "endTime" : "2022-08-17",                       # 服务结束时间,必填
     *     "accountType":"FORMAL" ,                        # 账号类型
     *     "distributorId":8,                              # 经销商的id,必填
     *     "accountManager":"王思聪",                      # 客户经理
     *     "password":"jfkdslfsd",                         # 密码
     *     "comment" : "此客户有极大的意向长期合作！",     # 备注
     *     "status": "ENABLED"  ,                          # 是否启用     NOT_ENABLED(0, "未启用"), ENABLED(1, "启用");
     *     "accountManagePhone" : "***********",           # 客户经理电话
     *     "deploymentUserId" :  666                       # 实施负责人的id
     * }
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "添加经销商客户成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/addDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT,
            contentExpression = "'用户[' + #currentUser.name + '] 添加了新的经销商客户['+ #customerInsertVO.companyName +']' "
    )
    public ResultObject addDistributorCustomer(@Validated(value = DistributorCustomerInsertValidate.class) @RequestBody CustomerInsertVO customerInsertVO) {
        Long userId = SecurityUtils.getUserId();
        customerInsertVO.setCreateUserId(userId);
        customerInsertVO.setCreateUserId(userId);
	    // 厘->分
	    customerInsertVO.processPrice();
        String defaultPassword = distributorCustomerService.addDistributorCustomer(customerInsertVO);
        return ResultObject.success(defaultPassword);
    }


    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/listDistributorCustomer  经销商客户列表
     * @apiName listDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客戶名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     * @apiParam {Long} subDistributorId                           # 代理商ID
     * @apiParam {Enum} accountType                                # 账号类型的枚举  FORMAL(0,"正式") ON_TRIAL(1,"试用");
     * @apiParam {String} subAccountPhone                          # 子账号的手机号
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     * @apiParam {Long} deploymentUserId                           # 实施负责人的id，如果传0的话代表未知，不传查全部
     * @apiParam {Long} tenantIdSet                                # 选择的具体客户id
     * @apiParam {String} equalMatchLinkman                        # 需要精确匹配的联系人
     * @apiParam {String} enableConcurrency                        # 非必填，是否过滤开启一线多并发，true：启用，false：禁用
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                           # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountManagePhone":"***********",                     # 客户经理电话
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                             # 是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/listDistributorCustomer")
    public ResultObject listDistributorCustomer(@RequestBody DirectCustomerQueryVO query) {

        if (query.getTenantId() != null) {
            boolean numeric = StrUtil.isNumeric(query.getTenantId().trim());
            if (!numeric) {
                return ResultObject.success(new ArrayList<>());
            }
        }

        query.setCurrentLoginUserId(SecurityUtils.getUserId());
        query.setIsOpeDistributor(true);
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(query.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(userId));
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据

        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(query.getCurrentLoginUserId())) {
            Set<Long> filterUserIdSet = userService.getOpeChildUserIdByParentUserId(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(filterUserIdSet));
        }

        PageResultObject page = directCustomerService.listAllCustomer(query, false);
        return ResultObject.success(page);
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/listDistributorCustomerForQiyu  经销商客户列表(计费统计-代理商客户ai坐席列表)
     * @apiName listDistributorCustomerForQiyu
     * @apiGroup distributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {Long} distributorId                           # 代理商ID
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                           # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountManagePhone":"***********",                     # 客户经理电话
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                             # 是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/listDistributorCustomerForQiyu")
    public ResultObject listDistributorCustomerForQiyu(@RequestBody DistributorCustomerQueryVO query) {
        PageResultObject page = distributorCustomerService.listDistributorTotalCustomerForQiyu(query);
        return ResultObject.success(page);
    }

    /**
     * 代理商ai坐席明细
     *
     * @param query 参数
     * @return 结果
     */
    @GetMapping("/listDistributorCustomerForQiyuDetail")
    public ResultObject listDistributorCustomerForQiyuDetail(DistributorCustomerQueryVO query) {

        return ResultObject.success(distributorCustomerService.listDistributorDetailCustomerForQiyu(query));
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/exportListDistributorCustomer  导出经销商客户列表
     * @apiName exportListDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {List}    selectTenantIdList                      # 选择的客户列表，全选就不传
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客戶名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     * @apiParam {Long} subDistributorId                           # 代理商ID
     * @apiParam {Enum} accountType                                # 账号类型的枚举  FORMAL(0,"正式") ON_TRIAL(1,"试用");
     * @apiParam {String} subAccountPhone                          # 子账号的手机号
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     * @apiParam {Long} deploymentUserId                           # 实施负责人的id，如果传0的话代表未知，不传查全部
     * @apiParam {Long} tenantIdSet                                # 选择的具体客户id
     * @apiParam {String} equalMatchLinkman                        # 需要精确匹配的联系人
     * @apiParam {String} enableConcurrency                        # 非必填，是否过滤开启一线多并发，true：启用，false：禁用
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                           # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountManagePhone":"***********",                     # 客户经理电话
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                             # 是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/exportListDistributorCustomer")
    public ResultObject exportListDistributorCustomer(DistributorCustomerQueryVO query) {
        query.setCurrentLoginUserId(SecurityUtils.getUserId());
        query.setIsOpeDistributor(true);
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(query.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(userId));
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据
        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(query.getCurrentLoginUserId())) {
            Set<Long> filterUserIdSet = userService.getOpeChildUserIdByParentUserId(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(filterUserIdSet));
        }
        //选择的tenantId
        if(!CollectionUtils.isEmpty(query.getSelectTenantIdList())){
            if(CollectionUtils.isEmpty(query.getTenantIdSet())){
                query.setTenantIdSet(new HashSet<>(query.getSelectTenantIdList()));
            }else {
                List<Long> idList = new ArrayList<>();
                for (Long id : query.getTenantIdSet()){
                    if(query.getSelectTenantIdList().contains(id)){
                        idList.add(id);
                    }
                }
                query.setTenantIdSet(new HashSet<>(idList));
            }
        }
        JobStartResultVO page = distributorCustomerService.exportListAllDistributorCustomer(query);
        return ResultObject.success(page);
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/exportListDistributorCustomerForQiyu  导出经销商客户列表 (计费统计 代理商客户ai坐席导出)
     * @apiName exportListDistributorCustomerForQiyu
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {Long} distributorId                           # 代理商ID
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     *
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                           # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountManagePhone":"***********",                     # 客户经理电话
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                             # 是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/exportListDistributorCustomerForQiyu")
    public ResultObject exportListDistributorCustomerForQiyu(DistributorCustomerQueryVO query) {
        query.setCurrentLoginUserId(SecurityUtils.getUserId());
        JobStartResultVO page = distributorCustomerService.exportListAllDistributorCustomerForQiyu(query);
        return ResultObject.success(page);
    }


    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/updateDistributorCustomer 更新经销商客户信息
     * @apiName updateDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParamExample {json} Request Example
     * {
     *     "tenantId":9,                                        # 客户id,必填
     *     "distributorId" : 1,                                 # 经销商的id,必填
     *     "tenantName" : "杭州一知智能科技有限公司",           # 客户名
     *     "linkman" : "达康书记",                              # 联系人
     *     "phoneNumber" : "***********",                       # 联系方式
     *     "accountManagePhone" : "***********",                # 客户经理电话
     *     "status": "ENABLED"                                  # 是否启用     NOT_ENABLED(0, "未启用"), ENABLED(1, "启用");
     *     "deploymentUserId" : 6666                            # 实施负责人的id
     * }
     *
     * @apiSuccessExample Response 200 Example
     *{
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "更新成功！",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/updateDistributorCustomer")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantPO.tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 更新了经销商客户['+ #tenantPO.companyName +']的信息' "
    )
    public ResultObject updateDistributorCustomer(@RequestBody CustomerUpdateVO customerUpdateVO) {
        Long userId = SecurityUtils.getUserId();
        customerUpdateVO.setUpdateUserId(userId);
	    // 厘->分
	    customerUpdateVO.processPrice();
        if (customerUpdateVO.getMaFareDouble() != null) {
            customerUpdateVO.setMaFare(Double.valueOf(customerUpdateVO.getMaFareDouble() * 10).longValue());
        }
        if (customerUpdateVO.getMaComFare() != null) {
            customerUpdateVO.setMaComFare(customerUpdateVO.getMaComFare() * 10);
        }
	    if (customerUpdateVO.getExtensionFare() != null) {
		    customerUpdateVO.setExtensionFare(customerUpdateVO.getExtensionFare() * 10);
	    }
        if (!userService.checkOpeUserHasViewDistributorCustomerPhoneNumberAuth(userId)) {
            //没有查看权限不允许修改联系人电话
            TenantPO tenantPO = tenantService.selectByKey(customerUpdateVO.getTenantId());
            Assert.notNull(tenantPO, "代理商客户不存在");
            customerUpdateVO.setPhoneNumber(tenantPO.getPhoneNumber());
        }
        distributorCustomerService.updateOpeDistributorCustomer(customerUpdateVO);
        return ResultObject.success(null, "更新成功！");
    }


    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/delay 代理商客户延期
     * @apiName delay
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newEndTime                        # 要延期的时间,必填
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {string} tenantAiccPartType                               # 类型 crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本") FUN_MULTI_CONCURRENCY(8, "一线多并发"),
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/delay")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 对经销商客户[tenantId='+ #tenantId +']进行了延期' "
    )
    public ResultObject delay(@RequestParam Long id, @RequestParam LocalDate newEndTime, @RequestParam Long tenantId,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        distributorCustomerService.opeAndBossDistributorCustomerDelay(id, newEndTime, tenantId, SecurityUtils.getUserId(),tenantAiccPartType);
        return ResultObject.success(null, "延期成功!");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateDelayCost 代理商客户延期费用 结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateDelayCost
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Double} ObjectCount                                     # 开通数量，比如开通多少 AI 并发数 非必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {LocalDate} newEndTime                               # 要延期的时间,必填
     * @apiParam {Long} robotId                               # 机器人的id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "代理商客户功能开通消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDelayCost")
    public ResultObject calculateDelayCost(
            @RequestParam(required = false) Double ObjectCount,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "新的服务结束日期不能为空") @RequestParam LocalDate newEndTime,
            @NotNull(message = "机器人id不能为空") @RequestParam Long robotId
            ) {
        Double result=distributorCustomerService.calculateDelayCost(ObjectCount,distributorId,tenantId,newEndTime,robotId);
        return ResultObject.success(result, "代理商客户延期消费计算成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/adjustingServiceCycle    经销商客户调整服务周期
     * @apiName adjustingServiceCycle
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 扣除坐席并发的数量
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/adjustingServiceCycle")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 对经销商客户[tenantId='+ #tenantId +']调整了服务周期' "
    )
    public ResultObject adjustingServiceCycle(@RequestParam Long id,
                                             @RequestParam(required = false) LocalDate newStartTime,
                                             @RequestParam(required = false) LocalDate newEndTime,
                                             @RequestParam Long tenantId,
                                             @RequestParam(required = true) Long reduceAiAmount,
                                             @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {

        directCustomerService.adjustingServiceCycle(id, newStartTime, newEndTime, tenantId, SecurityUtils.getUserId(),
                reduceAiAmount,  tenantAiccPartType);
        return ResultObject.success(null, "调整服务周期成功!");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateAdjustingServiceCycleCost    经销商客户调整服务周期消费   结果为正 就是退钱给客户，为负数则需要消耗
     * @apiName calculateAdjustingServiceCycleCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} id                                     # 机器坐席的id，必填
     * @apiParam {LocalDate} newStartTime                      # 新的开始服务时间
     * @apiParam {LocalDate} newEndTime                        # 新的结束服务时间
     * @apiParam {Long} tenantId                               # 租户id，必填
     * @apiParam {Long} reduceAiAmount                         # 扣除坐席并发的数量
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": "延期成功!",
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAdjustingServiceCycleCost")
    public ResultObject calculateAdjustingServiceCycleCost(@RequestParam Long robotId,
                                             @RequestParam(required = false) LocalDate newStartTime,
                                             @RequestParam(required = false) LocalDate newEndTime,
                                             @RequestParam Long tenantId,
                                             @RequestParam Long distributorId,
                                             @RequestParam(required = true) Long reduceAiAmount) {
        Double cost= directCustomerService.calculateAdjustingServiceCycleCost(robotId,tenantId,distributorId,SecurityUtils.getUserId(), newStartTime, newEndTime, reduceAiAmount);
        return ResultObject.success(cost, "调整服务周期消费计算成功!");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/dilatation 代理商客户扩容
     * @apiName dilatation
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {string} tenantAiccPartType                               # 类型 crm_out_call_platform(0,"智能外呼"),crm_call_in_recp(1,"智能呼入"),crm_qc_platform(2,"智能质检"),cs_console(3,"客服工作台"),voice_cs_console(4,"客服工作台-语音"),text_cs_console(5,"客服工作台-文本"),yi_brain_voice(6, "Yi-Brain-语音"),yi_brain_text(7, "Yi-Brain-文本") FUN_MULTI_CONCURRENCY(8, "一线多并发"),
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/dilatation")
    @SimpleOperationLog(
            module = SystemEnum.OPE, logType = OperationLogLogTypeEnum.TENANT, refIdExpression = "#tenantId",
            contentExpression = "'用户[' + #currentUser.name + '] 对经销商客户[tenantId='+ #tenantId +']进行了扩容' "
    )
    public ResultObject dilatation(@RequestParam Long tenantId,
                                  @RequestParam Integer count,
                                  @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                  @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime
                                    ,@RequestParam(required = false) TenantAiccPartEnum tenantAiccPartType) {
        directCustomerService.dilatation(tenantId, count, startTime, endTime, SecurityUtils.getUserId(),tenantAiccPartType);
        return ResultObject.success(null, "扩容成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateDilatationCost 代理商客户扩容消费   返回正数表示退回  负数表示消耗
     * @apiName calculateDilatationCost
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Long} tenantId                               # 客户id，必填
     * @apiParam {Integer} count                               # 要扩容的数量，必填
     * @apiParam {LocalDate} startTime                         # 开始服务的时间，必填
     * @apiParam {LocalDate} endTime                           # 结束服务的时间，必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     *
     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": null,
     *     "requestId": null,
     *     "resultMsg": "扩容成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateDilatationCost")
    public ResultObject calculateDilatationCost(@RequestParam Long tenantId,
                                  @RequestParam Integer count,
                                  @NotNull(message = "开始服务时间不能为空") @RequestParam LocalDate startTime,
                                  @NotNull(message = "结束服务时间不能为空") @RequestParam LocalDate endTime,
                                  @RequestParam Long distributorId,
                                   @RequestParam(required = false) TenantAiccPartEnum tenantAiccPartEnum) {
        Double cost=directCustomerService.calculateDilatationCost(tenantId, count, startTime, endTime, distributorId,tenantAiccPartEnum);
        return ResultObject.success(cost, "扩容成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/listSubDistributorCustomer  二级经销商客户列表
     * @apiName listSubDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客戶名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     * @apiParam {Long} subDistributorId                           # 代理商ID
     * @apiParam {Enum} accountType                                # 账号类型的枚举  FORMAL(0,"正式") ON_TRIAL(1,"试用");
     * @apiParam {String} subAccountPhone                          # 子账号的手机号
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     * @apiParam {Long} deploymentUserId                           # 实施负责人的id，如果传0的话代表未知，不传查全部
     * @apiParam {Long} tenantIdSet                                # 选择的具体客户id
     * @apiParam {String} enableConcurrency                       # 非必填，是否过滤启用一线多并发，true： 启用， false： 禁用

     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                          # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                              #是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/listSubDistributorCustomer")
    public ResultObject listSubDistributorCustomer(@RequestBody DirectCustomerQueryVO query) {
        if (query.getTenantId() != null) {
            boolean numeric = StrUtil.isNumeric(query.getTenantId().trim());
            if (!numeric) {
                return ResultObject.success(PageResultObject.of(new ArrayList<>()));
            }
        }
        query.setCurrentLoginUserId(SecurityUtils.getUserId());
        query.setIsOpeDistributor(false);
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(query.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(userId));
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据
        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(query.getCurrentLoginUserId())) {
            Set<Long> filterUserIdSet = userService.getOpeChildUserIdByParentUserId(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(filterUserIdSet));
        }
        PageResultObject page = directCustomerService.listAllCustomer(query, false);
        return ResultObject.success(page);
    }

    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/exportListSubDistributorCustomer  二级经销商客户列表
     * @apiName exportListSubDistributorCustomer
     * @apiGroup customer-distributorCustomer
     *
     * @apiParam {List}    selectTenantIdList                      # 选择的客户列表，全选就不传
     * @apiParam {Integer} pageNum                                 # 第几页
     * @apiParam {Integer} pageSize                                # 页面大小
     * @apiParam {String} companyName                              # 搜索 客戶名
     * @apiParam {String} linkMan                                  # 搜索 联系人
     * @apiParam {String} phoneNumber                              # 搜索 手机号
     * @apiParam {boolean} expire                                  # 即将过期筛选条件的关键字，默认false。
     * @apiParam {Long} subDistributorId                           # 代理商ID
     * @apiParam {Enum} accountType                                # 账号类型的枚举  FORMAL(0,"正式") ON_TRIAL(1,"试用");
     * @apiParam {String} subAccountPhone                          # 子账号的手机号
     * @apiParam {String} startDate                                # 开始时间
     * @apiParam {String} endDate                                  # 结束时间
     * @apiParam {Long} deploymentUserId                           # 实施负责人的id，如果传0的话代表未知，不传查全部
     * @apiParam {Long} tenantIdSet                                # 选择的具体客户id
     * @apiParam {String} enableConcurrency                       # 非必填，是否过滤启用一线多并发，true： 启用， false： 禁用

     * @apiSuccessExample Response 200 Example
     * {
     *     "code": 200,
     *     "data": {
     *         "number": 1,
     *         "pageSize": 3,
     *         "totalElements": 3,
     *         "pages": 1,
     *         "content": [
     *             {
     *                 "tenantId": 7,                                          # 客户id
     *                 "tenantName": "阿里巴巴集团",                          # 客户名称
     *                 "linkman": "马云",                                      # 联系人
     *                 "phoneNumber": "***********",                           # 联系方式
     *                 "accountManager": null,                                 # 客户经理
     *                 "accountType": "FORMAL",                                # 账户类型
     *                 "comment": null,                                        # 备注
     *                 "dialogFlowId": 1,                                      # 话术id
     *                 "dialogFlowName": null,
     *                 "aiConcurrencyLevel": 10000,                            # AI 并发数
     *                 "bindingId": null,                                      # 绑定线路的id
     *                 "bindingName": null,
     *                 "telephoneFare": null,                                  # 账户余额
     *                 "startTime": "2018-08-20",                              # 服务开始时间
     *                 "endTime": "2022-08-17",                                # 服务结束时间
     *                 "distributorId": 1,                                     # 经销商的id
     *                 "name": "123",                                          # 代理商的名称
     *                 "deploymentUser" :"tom"                                 # 实施负责人的名字
     *                 "forbiddenCount" : 1                                    # 禁用的总次数
     *                 "enabledViewIntent": false   # 是否可以查看未识别意向列表 false ：关闭  ；true: 开启
     *                 "robotList": [                                          # 机器坐席的集合
     *                     {
     *                         "robotId": 17,                                  # 机器坐席的id
     *                         "count": 10000,　　　　　　　　　             　# 机器坐席的数量
     *                         "tenantId": 7,                                  # 对应的客户的id
     *                         "comment": null,                                # 备注
     *                         "startTime": "2018-08-20",                      # 开始服务时间
     *                         "endTime": "2022-08-17" ,                       # 结束服务时间
     *                         "expirationTime": 60                            # 该客户机器坐席剩余服务时间
     *                     }
     *                 ],
     *                 "userStatusEnum": "ENABLED"                              #是否启用
     *                 "enableCsSeat" : true,       # 人工坐席开启标志
     *                 "csSeatCount"  : 1 ,         # 人工坐席数量
     *
     *             }
     *     "requestId": null,
     *     "resultMsg": "执行成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @GetMapping("/exportListSubDistributorCustomer")
    public ResultObject exportListSubDistributorCustomer(DistributorCustomerQueryVO query) {
        query.setCurrentLoginUserId(SecurityUtils.getUserId());
        query.setIsOpeDistributor(false);
        query.setSubDistributor(true);
        //客户权限为我自己，只能查看自己的数据
        if (userService.checkOpeUserIsOnlyReadSelfTenantData(query.getCurrentLoginUserId())) {
            Set<Long> userId = new HashSet<>();
            userId.add(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(userId));
        }
        //客户权限为所在组及下级组，可查看组织内下级所有客户数据
        if (userService.checkOpeCustomerIsOnlyReadOwningGroupsAndSubbordinateGroupsTenantData(query.getCurrentLoginUserId())) {
            Set<Long> filterUserIdSet = userService.getOpeChildUserIdByParentUserId(query.getCurrentLoginUserId());
            query.setFilterDistributorCustomerManagerIdList(new ArrayList<>(filterUserIdSet));
        }
        //选择的tenantId
        if(!CollectionUtils.isEmpty(query.getSelectTenantIdList())){
            if(CollectionUtils.isEmpty(query.getTenantIdSet())){
                query.setTenantIdSet(new HashSet<>(query.getSelectTenantIdList()));
            }else {
                List<Long> idList = new ArrayList<>();
                for (Long id : query.getTenantIdSet()){
                    if(query.getSelectTenantIdList().contains(id)){
                        idList.add(id);
                    }
                }
                query.setTenantIdSet(new HashSet<>(idList));
            }
        }
        JobStartResultVO resultVO = distributorCustomerService.exportListAllSubDistributorCustomer(query);
        return ResultObject.success(resultVO);
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateAiCost 新增代理商客户 开通AI坐席消耗费用
     * @apiName calculateAiCost
     * @apiGroup distributorCustomer
     *
     * @apiParam {Double} robotCount                                     # AI 坐席数必填
     * @apiParam {LocalDate} startTime                      # 服务开始时间必填
     * @apiParam {LocalDate} endTime                        # 服务结束时间必填
     * @apiParam {Long} distributorId                               # 代理商id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "开通功能消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAiCost")
    public ResultObject calculateAiCost(
            @NotNull(message = "AI 座席数不能为空")@RequestParam Double robotCount,
            @NotNull(message = "服务开始时间不能为空")@RequestParam LocalDate startTime,
            @NotNull(message = "服务结束时间不能为空")@RequestParam LocalDate endTime,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId) {
        Double result=distributorCustomerService.calculateAiCost(robotCount,distributorId,startTime, endTime,true);
        return ResultObject.success(result, "开通功能消耗费用计算成功");
    }
    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateAgentAndAiAssistantCost 计算 开通人工坐席/AI 助理的费用 改动之前接口比较费事所以重新写个
     * @apiName calculateAgentAndAiAssistantCost
     * @apiGroup distributorCustomer
     *
     * @apiParam {Double} count                                     # 新增数量 必填
     * @apiParam {LocalDate} startTime                      # 服务开始时间必填
     * @apiParam {LocalDate} endTime                        # 服务结束时间必填
     * @apiParam {Long} distributorId                               # 代理商id,必填
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "开通功能消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateAgentAndAiAssistantCost")
    public ResultObject calculateAiCost(
            @NotNull(message = "新增数量不能为空")@RequestParam Double count,
            @NotNull(message = "服务开始时间不能为空")@RequestParam LocalDate startTime,
            @NotNull(message = "服务结束时间不能为空")@RequestParam LocalDate endTime,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        Double result=distributorCustomerService.calculateAgentAndAiAssistantCost(count,distributorId,startTime, endTime,rechargePrestoreStreamFunEnum);
        return ResultObject.success(result, "开通功能+"+rechargePrestoreStreamFunEnum.getDesc()+"消耗费用计算成功");
    }

    // @formatter:off
    /**
     * @api {post} /apiOpe/distributorCustomer/calculateFunctionCost 代理商客户功能开通消耗费用
     * @apiName calculateFunctionCost
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Double} ObjectCount                                     # 开通数量，比如开通多少 AI 并发数 非必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {RechargePrestoreStreamFunEnum} rechargePrestoreStreamFunEnum                      # FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": 2400,
     *     "requestId": null,
     *     "resultMsg": "代理商客户功能开通消耗费用计算成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/calculateFunctionCost")
    @Deprecated
    public ResultObject calculateFunctionCost(
            @RequestParam(required = false) Double ObjectCount,
            @NotNull(message = "代理商id不能为空") @RequestParam Long distributorId,
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        Double result=distributorCustomerService.calculateFunctionCost(ObjectCount,distributorId,tenantId, rechargePrestoreStreamFunEnum,true,null,null);
        return ResultObject.success(result, "代理商客户功能开通消耗费用计算成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/getFunctionInfo 代理商客户开通功能 窗口信息查询
     * @apiName getFunctionInfo
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {RechargePrestoreStreamFunEnum} rechargePrestoreStreamFunEnum                      # FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *
     *     },
     *     "requestId": null,
     *     "resultMsg": "代理商客户开通功能 窗口信息查询成功",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/getFunctionInfo")
    public ResultObject getFunctionInfo(
            @NotNull(message = "客户id不能为空") @RequestParam Long tenantId,
            @NotNull(message = "消费类型不能为空") @RequestParam RechargePrestoreStreamFunEnum rechargePrestoreStreamFunEnum) {
        List<DistributorCustomerFunctionInfo> result=distributorCustomerService.getFunctionInfo(tenantId, rechargePrestoreStreamFunEnum);
        return ResultObject.success(result, "代理商客户开通功能 窗口信息查询成功");
    }
    // @formatter:off
    /**
     * @api {get} /apiOpe/distributorCustomer/openFunction 代理商客户开通功能
     * @apiName openFunction
     * @apiGroup distributorCustomer
     *
     *
     * @apiParam {Double} ObjectCount                                     # 开通数量，比如开通多少 AI 并发数 非必填
     * @apiParam {Long} distributorId                                     # 代理商id,必填
     * @apiParam {Long} tenantId                               # 租户id,必填
     * @apiParam {RechargePrestoreStreamFunEnum} rechargePrestoreStreamFunEnum                      # FUN_AI_ASSISTANT, # 消耗类型,必填 DEFAULT(0,""),FUN_AI(1, "AI坐席"),FUN_AI_ASSISTANT(2, "AI助理"),FUN_TTS_COMPOSE(3, "TTS全句合成"),FUN_MULTI_CONCURRENCY(4, "一线多并发"),FUN_AGENT(5, "人工座席"),FUN_IVR_NAVIGATION(6, "IVR导航"),FUN_QC(7, "智能质检"),FUN_DIALOG_FLOW(8, "AI话术"),
     *
     *
     * @apiSuccessExample {json} Response 200 Example
     *{
     *     "code": 200,
     *     "data": {
     *
     *     },
     *     "requestId": null,
     *     "resultMsg": "",
     *     "errorStackTrace": null
     * }
     */
    // @formatter:on
    @PostMapping("/openFunction")
    public ResultObject openFunction(@Validated(value = DistributorCustomerOpenFunctionValidate.class) @RequestBody DistributorCustomerOpenFunctionQueryVO distributorCustomerOpenFunctionQueryVO) {
        Long userId=SecurityUtils.getUserId();
        distributorCustomerService.openFunction(distributorCustomerOpenFunctionQueryVO.getDistributorId(),distributorCustomerOpenFunctionQueryVO.getTenantId(), distributorCustomerOpenFunctionQueryVO.getObjectCount(), distributorCustomerOpenFunctionQueryVO.getRechargePrestoreStreamFunEnum(),userId,distributorCustomerOpenFunctionQueryVO.getStartTime(),distributorCustomerOpenFunctionQueryVO.getEndTime());
        return ResultObject.success("代理商客户功能开通成功");
    }
}
